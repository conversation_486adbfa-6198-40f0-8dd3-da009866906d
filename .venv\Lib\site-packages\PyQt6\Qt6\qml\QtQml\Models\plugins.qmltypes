import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qqmlmodelsmodule_p.h"
        name: "QAbstractItemModel"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQml.Models/AbstractItemModel 6.5"]
        isCreatable: false
        exportMetaObjectRevisions: [1541]
        Enum {
            name: "LayoutChangeHint"
            values: [
                "NoLayoutChangeHint",
                "VerticalSortHint",
                "HorizontalSortHint"
            ]
        }
        Enum {
            name: "CheckIndexOption"
            isScoped: true
            values: [
                "NoOption",
                "IndexIsValid",
                "DoNotUseParent",
                "ParentIsInvalid"
            ]
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
            Parameter { name: "roles"; type: "int"; isList: true }
        }
        Signal {
            name: "dataChanged"
            isCloned: true
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
        }
        Signal {
            name: "headerDataChanged"
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutChanged"
            isCloned: true
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
        }
        Signal { name: "layoutChanged"; isCloned: true }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutAboutToBeChanged"
            isCloned: true
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
        }
        Signal { name: "layoutAboutToBeChanged"; isCloned: true }
        Signal {
            name: "rowsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal { name: "modelAboutToBeReset" }
        Signal { name: "modelReset" }
        Signal {
            name: "rowsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "rowsMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Signal {
            name: "columnsMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Method { name: "submit"; type: "bool" }
        Method { name: "revert" }
        Method { name: "resetInternalData" }
        Method {
            name: "hasIndex"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "hasIndex"
            type: "bool"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "parent"
            type: "QModelIndex"
            isMethodConstant: true
            Parameter { name: "child"; type: "QModelIndex" }
        }
        Method {
            name: "sibling"
            type: "QModelIndex"
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "idx"; type: "QModelIndex" }
        }
        Method {
            name: "rowCount"
            type: "int"
            isMethodConstant: true
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "rowCount"; type: "int"; isCloned: true; isMethodConstant: true }
        Method {
            name: "columnCount"
            type: "int"
            isMethodConstant: true
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "columnCount"; type: "int"; isCloned: true; isMethodConstant: true }
        Method {
            name: "hasChildren"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "hasChildren"; type: "bool"; isCloned: true; isMethodConstant: true }
        Method {
            name: "data"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "data"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "setData"
            type: "bool"
            isCloned: true
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
        }
        Method {
            name: "insertRows"
            revision: 1540
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "insertRows"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "insertColumns"
            revision: 1540
            type: "bool"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "insertColumns"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "column"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "removeRows"
            revision: 1540
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "removeRows"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "removeColumns"
            revision: 1540
            type: "bool"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "removeColumns"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "column"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "moveRows"
            revision: 1540
            type: "bool"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceRow"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationChild"; type: "int" }
        }
        Method {
            name: "moveColumns"
            revision: 1540
            type: "bool"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceColumn"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationChild"; type: "int" }
        }
        Method {
            name: "insertRow"
            revision: 1540
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "insertRow"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "insertColumn"
            revision: 1540
            type: "bool"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "insertColumn"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "removeRow"
            revision: 1540
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "removeRow"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "removeColumn"
            revision: 1540
            type: "bool"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "removeColumn"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "moveRow"
            revision: 1540
            type: "bool"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceRow"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationChild"; type: "int" }
        }
        Method {
            name: "moveColumn"
            revision: 1540
            type: "bool"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceColumn"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationChild"; type: "int" }
        }
        Method {
            name: "fetchMore"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "canFetchMore"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "flags"
            type: "Qt::ItemFlags"
            isMethodConstant: true
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "sort"
            revision: 1540
            Parameter { name: "column"; type: "int" }
            Parameter { name: "order"; type: "Qt::SortOrder" }
        }
        Method {
            name: "sort"
            revision: 1540
            isCloned: true
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            isMethodConstant: true
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qqmlmodelsmodule_p.h"
        name: "QAbstractListModel"
        accessSemantics: "reference"
        prototype: "QAbstractItemModel"
        exports: ["QtQml.Models/AbstractListModel 6.5"]
        isCreatable: false
        exportMetaObjectRevisions: [1541]
    }
    Component {
        file: "private/qqmlmodelindexvaluetype_p.h"
        name: "QItemSelection"
        accessSemantics: "sequence"
        valueType: "QItemSelectionRange"
    }
    Component {
        file: "private/qqmlmodelsmodule_p.h"
        name: "QItemSelectionModel"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQml.Models/ItemSelectionModel 2.2",
            "QtQml.Models/ItemSelectionModel 6.0"
        ]
        exportMetaObjectRevisions: [514, 1536]
        Enum {
            name: "SelectionFlags"
            alias: "SelectionFlag"
            isFlag: true
            values: [
                "NoUpdate",
                "Clear",
                "Select",
                "Deselect",
                "Toggle",
                "Current",
                "Rows",
                "Columns",
                "SelectCurrent",
                "ToggleCurrent",
                "ClearAndSelect"
            ]
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            bindable: "bindableModel"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 0
        }
        Property {
            name: "hasSelection"
            type: "bool"
            read: "hasSelection"
            notify: "selectionChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "currentIndex"
            type: "QModelIndex"
            read: "currentIndex"
            notify: "currentChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "selection"
            type: "QItemSelection"
            read: "selection"
            notify: "selectionChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "selectedIndexes"
            type: "QModelIndexList"
            read: "selectedIndexes"
            notify: "selectionChanged"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "selectionChanged"
            Parameter { name: "selected"; type: "QItemSelection" }
            Parameter { name: "deselected"; type: "QItemSelection" }
        }
        Signal {
            name: "currentChanged"
            Parameter { name: "current"; type: "QModelIndex" }
            Parameter { name: "previous"; type: "QModelIndex" }
        }
        Signal {
            name: "currentRowChanged"
            Parameter { name: "current"; type: "QModelIndex" }
            Parameter { name: "previous"; type: "QModelIndex" }
        }
        Signal {
            name: "currentColumnChanged"
            Parameter { name: "current"; type: "QModelIndex" }
            Parameter { name: "previous"; type: "QModelIndex" }
        }
        Signal {
            name: "modelChanged"
            Parameter { name: "model"; type: "QAbstractItemModel"; isPointer: true }
        }
        Method {
            name: "setCurrentIndex"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "command"; type: "QItemSelectionModel::SelectionFlags" }
        }
        Method {
            name: "select"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "command"; type: "QItemSelectionModel::SelectionFlags" }
        }
        Method {
            name: "select"
            Parameter { name: "selection"; type: "QItemSelection" }
            Parameter { name: "command"; type: "QItemSelectionModel::SelectionFlags" }
        }
        Method { name: "clear" }
        Method { name: "reset" }
        Method { name: "clearSelection" }
        Method { name: "clearCurrentIndex" }
        Method {
            name: "isSelected"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "isRowSelected"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "isRowSelected"
            type: "bool"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "isColumnSelected"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "isColumnSelected"
            type: "bool"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "rowIntersectsSelection"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "rowIntersectsSelection"
            type: "bool"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "columnIntersectsSelection"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "columnIntersectsSelection"
            type: "bool"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "selectedRows"
            type: "QModelIndexList"
            isMethodConstant: true
            Parameter { name: "column"; type: "int" }
        }
        Method { name: "selectedRows"; type: "QModelIndexList"; isCloned: true; isMethodConstant: true }
        Method {
            name: "selectedColumns"
            type: "QModelIndexList"
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "selectedColumns"
            type: "QModelIndexList"
            isCloned: true
            isMethodConstant: true
        }
    }
    Component {
        file: "private/qqmlmodelindexvaluetype_p.h"
        name: "QModelIndexList"
        accessSemantics: "sequence"
        valueType: "QModelIndex"
    }
    Component {
        file: "private/qqmlmodelindexvaluetype_p.h"
        name: "std::vector<QModelIndex>"
        accessSemantics: "sequence"
        valueType: "QModelIndex"
    }
    Component {
        file: "private/qqmlabstractdelegatecomponent_p.h"
        name: "QQmlAbstractDelegateComponent"
        accessSemantics: "reference"
        prototype: "QQmlComponent"
        exports: [
            "QtQml.Models/AbstractDelegateComponent 2.0",
            "QtQml.Models/AbstractDelegateComponent 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Signal { name: "delegateChanged" }
    }
    Component { file: "private/qqmlchangeset_p.h"; name: "QQmlChangeSet"; accessSemantics: "value" }
    Component {
        file: "private/qqmldelegatecomponent_p.h"
        name: "QQmlDelegateChoice"
        accessSemantics: "reference"
        defaultProperty: "delegate"
        prototype: "QObject"
        exports: ["QtQml.Models/DelegateChoice 6.9"]
        exportMetaObjectRevisions: [1545]
        Property {
            name: "roleValue"
            type: "QVariant"
            read: "roleValue"
            write: "setRoleValue"
            notify: "roleValueChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "row"
            type: "int"
            read: "row"
            write: "setRow"
            notify: "rowChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "index"
            type: "int"
            read: "row"
            write: "setRow"
            notify: "indexChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "column"
            type: "int"
            read: "column"
            write: "setColumn"
            notify: "columnChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 4
            isFinal: true
        }
        Signal { name: "roleValueChanged" }
        Signal { name: "rowChanged" }
        Signal { name: "indexChanged" }
        Signal { name: "columnChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "changed" }
    }
    Component {
        file: "private/qqmldelegatecomponent_p.h"
        name: "QQmlDelegateChooser"
        accessSemantics: "reference"
        defaultProperty: "choices"
        prototype: "QQmlAbstractDelegateComponent"
        exports: ["QtQml.Models/DelegateChooser 6.9"]
        exportMetaObjectRevisions: [1545]
        Property {
            name: "role"
            type: "QString"
            read: "role"
            write: "setRole"
            notify: "roleChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "choices"
            type: "QQmlDelegateChoice"
            isList: true
            read: "choices"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Signal { name: "roleChanged" }
    }
    Component {
        file: "private/qqmldelegatemodel_p.h"
        name: "QQmlDelegateModel"
        accessSemantics: "reference"
        defaultProperty: "delegate"
        prototype: "QQmlInstanceModel"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQml.Models/DelegateModel 2.1",
            "QtQml.Models/DelegateModel 2.15",
            "QtQml.Models/DelegateModel 6.0"
        ]
        exportMetaObjectRevisions: [513, 527, 1536]
        attachedType: "QQmlDelegateModelAttached"
        Property { name: "model"; type: "QVariant"; read: "model"; write: "setModel"; index: 0 }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 1
        }
        Property {
            name: "filterOnGroup"
            type: "QString"
            read: "filterGroup"
            write: "setFilterGroup"
            reset: "resetFilterGroup"
            notify: "filterGroupChanged"
            index: 2
        }
        Property {
            name: "items"
            type: "QQmlDelegateModelGroup"
            isPointer: true
            read: "items"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "persistedItems"
            type: "QQmlDelegateModelGroup"
            isPointer: true
            read: "persistedItems"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "groups"
            type: "QQmlDelegateModelGroup"
            isList: true
            read: "groups"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "parts"
            type: "QObject"
            isPointer: true
            read: "parts"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "rootIndex"
            type: "QVariant"
            read: "rootIndex"
            write: "setRootIndex"
            notify: "rootIndexChanged"
            index: 7
        }
        Signal { name: "filterGroupChanged" }
        Signal { name: "defaultGroupsChanged" }
        Signal { name: "rootIndexChanged" }
        Signal { name: "delegateChanged" }
        Method {
            name: "_q_itemsChanged"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "roles"; type: "int"; isList: true }
        }
        Method {
            name: "_q_itemsInserted"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "_q_itemsRemoved"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "_q_itemsMoved"
            Parameter { name: "from"; type: "int" }
            Parameter { name: "to"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method { name: "_q_modelAboutToBeReset" }
        Method {
            name: "_q_rowsInserted"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_columnsInserted"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_columnsRemoved"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_columnsMoved"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_rowsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "begin"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "_q_rowsRemoved"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_rowsMoved"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_dataChanged"
            Parameter { type: "QModelIndex" }
            Parameter { type: "QModelIndex" }
            Parameter { type: "int"; isList: true }
        }
        Method {
            name: "_q_layoutChanged"
            Parameter { type: "QPersistentModelIndex"; isList: true }
            Parameter { type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Method {
            name: "modelIndex"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "idx"; type: "int" }
        }
        Method { name: "parentModelIndex"; type: "QVariant"; isMethodConstant: true }
    }
    Component {
        file: "private/qqmldelegatemodel_p.h"
        name: "QQmlDelegateModelAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "model"
            type: "QQmlDelegateModel"
            isPointer: true
            read: "model"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "groups"
            type: "QStringList"
            read: "groups"
            write: "setGroups"
            notify: "groupsChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "isUnresolved"
            type: "bool"
            read: "isUnresolved"
            notify: "unresolvedChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "inPersistedItems"
            type: "bool"
            read: "inPersistedItems"
            write: "setInPersistedItems"
            notify: "groupsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "inItems"
            type: "bool"
            read: "inItems"
            write: "setInItems"
            notify: "groupsChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "persistedItemsIndex"
            type: "int"
            read: "persistedItemsIndex"
            notify: "groupsChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "itemsIndex"
            type: "int"
            read: "itemsIndex"
            notify: "groupsChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Signal { name: "groupsChanged" }
        Signal { name: "unresolvedChanged" }
    }
    Component {
        file: "private/qqmldelegatemodel_p.h"
        name: "QQmlDelegateModelGroup"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQml.Models/DelegateModelGroup 2.1",
            "QtQml.Models/DelegateModelGroup 6.0"
        ]
        exportMetaObjectRevisions: [513, 1536]
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 1
        }
        Property {
            name: "includeByDefault"
            type: "bool"
            read: "defaultInclude"
            write: "setDefaultInclude"
            notify: "defaultIncludeChanged"
            index: 2
        }
        Signal { name: "countChanged" }
        Signal { name: "nameChanged" }
        Signal { name: "defaultIncludeChanged" }
        Signal {
            name: "changed"
            Parameter { name: "removed"; type: "QJSValue" }
            Parameter { name: "inserted"; type: "QJSValue" }
        }
        Method { name: "insert"; isJavaScriptFunction: true }
        Method { name: "create"; isJavaScriptFunction: true }
        Method { name: "resolve"; isJavaScriptFunction: true }
        Method { name: "remove"; isJavaScriptFunction: true }
        Method { name: "addGroups"; isJavaScriptFunction: true }
        Method { name: "removeGroups"; isJavaScriptFunction: true }
        Method { name: "setGroups"; isJavaScriptFunction: true }
        Method { name: "move"; isJavaScriptFunction: true }
        Method {
            name: "get"
            type: "QJSValue"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qqmlobjectmodel_p.h"
        name: "QQmlInstanceModel"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "countChanged" }
        Signal {
            name: "modelUpdated"
            Parameter { name: "changeSet"; type: "QQmlChangeSet" }
            Parameter { name: "reset"; type: "bool" }
        }
        Signal {
            name: "createdItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "initItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "destroyingItem"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "itemPooled"
            revision: 527
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "itemReused"
            revision: 527
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qqmlinstantiator_p.h"
        name: "QQmlInstantiator"
        accessSemantics: "reference"
        defaultProperty: "delegate"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQml.Models/Instantiator 2.1",
            "QtQml.Models/Instantiator 6.0"
        ]
        exportMetaObjectRevisions: [513, 1536]
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 0
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "isAsync"
            write: "setAsync"
            notify: "asynchronousChanged"
            index: 1
        }
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 2
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 4
        }
        Property {
            name: "object"
            type: "QObject"
            isPointer: true
            read: "object"
            notify: "objectChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "modelChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "countChanged" }
        Signal { name: "objectChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "asynchronousChanged" }
        Signal {
            name: "objectAdded"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "objectRemoved"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "_q_createdItem"
            Parameter { type: "int" }
            Parameter { type: "QObject"; isPointer: true }
        }
        Method {
            name: "_q_modelUpdated"
            Parameter { type: "QQmlChangeSet" }
            Parameter { type: "bool" }
        }
        Method {
            name: "objectAt"
            type: "QObject"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qqmlmodelindexvaluetype_p.h"
        name: "QItemSelectionRange"
        accessSemantics: "value"
        extension: "QQmlItemSelectionRangeValueType"
    }
    Component {
        file: "private/qqmlmodelindexvaluetype_p.h"
        name: "QQmlItemSelectionRangeValueType"
        accessSemantics: "value"
        Property { name: "top"; type: "int"; read: "top"; index: 0; isReadonly: true; isFinal: true }
        Property { name: "left"; type: "int"; read: "left"; index: 1; isReadonly: true; isFinal: true }
        Property { name: "bottom"; type: "int"; read: "bottom"; index: 2; isReadonly: true; isFinal: true }
        Property { name: "right"; type: "int"; read: "right"; index: 3; isReadonly: true; isFinal: true }
        Property { name: "width"; type: "int"; read: "width"; index: 4; isReadonly: true; isFinal: true }
        Property { name: "height"; type: "int"; read: "height"; index: 5; isReadonly: true; isFinal: true }
        Property {
            name: "topLeft"
            type: "QPersistentModelIndex"
            read: "topLeft"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "bottomRight"
            type: "QPersistentModelIndex"
            read: "bottomRight"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "parent"
            type: "QModelIndex"
            read: "parent"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property { name: "valid"; type: "bool"; read: "isValid"; index: 9; isReadonly: true; isFinal: true }
        Property { name: "empty"; type: "bool"; read: "isEmpty"; index: 10; isReadonly: true; isFinal: true }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            index: 11
            isReadonly: true
            isFinal: true
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "contains"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "contains"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parentIndex"; type: "QModelIndex" }
        }
        Method {
            name: "intersects"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "other"; type: "QItemSelectionRange" }
        }
        Method {
            name: "intersected"
            type: "QItemSelectionRange"
            isMethodConstant: true
            Parameter { name: "other"; type: "QItemSelectionRange" }
        }
    }
    Component {
        file: "private/qqmllistmodel_p.h"
        name: "QQmlListElement"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQml.Models/ListElement 2.0",
            "QtQml.Models/ListElement 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qqmllistmodel_p.h"
        name: "QQmlListModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        exports: [
            "QtQml.Models/ListModel 2.0",
            "QtQml.Models/ListModel 2.14",
            "QtQml.Models/ListModel 6.0",
            "QtQml.Models/ListModel 6.4"
        ]
        hasCustomParser: true
        exportMetaObjectRevisions: [512, 526, 1536, 1540]
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "dynamicRoles"
            type: "bool"
            read: "dynamicRoles"
            write: "setDynamicRoles"
            index: 1
        }
        Property {
            name: "agent"
            revision: 526
            type: "QObject"
            isPointer: true
            read: "agent"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Signal { name: "countChanged" }
        Method { name: "clear" }
        Method { name: "remove"; isJavaScriptFunction: true }
        Method { name: "append"; isJavaScriptFunction: true }
        Method { name: "insert"; isJavaScriptFunction: true }
        Method {
            name: "get"
            type: "QJSValue"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "set"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "value"; type: "QJSValue" }
        }
        Method {
            name: "setProperty"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "property"; type: "QString" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "move"
            Parameter { name: "from"; type: "int" }
            Parameter { name: "to"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method { name: "sync" }
    }
    Component {
        file: "private/qqmllistmodelworkeragent_p.h"
        name: "QQmlListModelWorkerAgent"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "count"; type: "int"; read: "count"; index: 0; isReadonly: true; isFinal: true }
        Property {
            name: "engine"
            type: "QQmlV4ExecutionEnginePtr"
            read: "engine"
            write: "setEngine"
            notify: "engineChanged"
            index: 1
            isFinal: true
        }
        Signal {
            name: "engineChanged"
            Parameter { name: "engine"; type: "QQmlV4ExecutionEnginePtr" }
        }
        Method { name: "addref" }
        Method { name: "release" }
        Method { name: "clear" }
        Method { name: "remove"; isJavaScriptFunction: true }
        Method { name: "append"; isJavaScriptFunction: true }
        Method { name: "insert"; isJavaScriptFunction: true }
        Method {
            name: "get"
            type: "QJSValue"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "set"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "value"; type: "QJSValue" }
        }
        Method {
            name: "setProperty"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "property"; type: "QString" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "move"
            Parameter { name: "from"; type: "int" }
            Parameter { name: "to"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method { name: "sync" }
    }
    Component {
        file: "private/qqmlmodelindexvaluetype_p.h"
        name: "QModelIndex"
        accessSemantics: "value"
        extension: "QQmlModelIndexValueType"
    }
    Component {
        file: "private/qqmlmodelindexvaluetype_p.h"
        name: "QQmlModelIndexValueType"
        accessSemantics: "value"
        Property {
            name: "row"
            type: "int"
            read: "row"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "column"
            type: "int"
            read: "column"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "parent"
            type: "QModelIndex"
            read: "parent"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "valid"
            type: "bool"
            read: "isValid"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "internalId"
            type: "qulonglong"
            read: "internalId"
            index: 5
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "data"
            revision: 1543
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "role"; type: "int" }
        }
        Method { name: "data"; revision: 1543; type: "QVariant"; isCloned: true; isMethodConstant: true }
    }
    Component {
        file: "private/qqmlobjectmodel_p.h"
        name: "QQmlObjectModel"
        accessSemantics: "reference"
        defaultProperty: "children"
        prototype: "QQmlInstanceModel"
        exports: [
            "QtQml.Models/ObjectModel 2.1",
            "QtQml.Models/ObjectModel 2.3",
            "QtQml.Models/ObjectModel 2.15",
            "QtQml.Models/ObjectModel 6.0"
        ]
        exportMetaObjectRevisions: [513, 515, 527, 1536]
        attachedType: "QQmlObjectModelAttached"
        Property {
            name: "children"
            type: "QObject"
            isList: true
            read: "children"
            notify: "childrenChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "childrenChanged" }
        Method { name: "clear"; revision: 515 }
        Method {
            name: "_q_createJSWrapper"
            type: "qulonglong"
            Parameter { type: "QQmlV4ExecutionEnginePtr" }
        }
        Method {
            name: "get"
            revision: 515
            type: "QObject"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "append"
            revision: 515
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "insert"
            revision: 515
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "move"
            revision: 515
            Parameter { name: "from"; type: "int" }
            Parameter { name: "to"; type: "int" }
            Parameter { name: "n"; type: "int" }
        }
        Method {
            name: "move"
            revision: 515
            isCloned: true
            Parameter { name: "from"; type: "int" }
            Parameter { name: "to"; type: "int" }
        }
        Method {
            name: "remove"
            revision: 515
            Parameter { name: "index"; type: "int" }
            Parameter { name: "n"; type: "int" }
        }
        Method {
            name: "remove"
            revision: 515
            isCloned: true
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qqmlobjectmodel_p.h"
        name: "QQmlObjectModelAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "index"
            type: "int"
            read: "index"
            notify: "indexChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Signal { name: "indexChanged" }
    }
    Component {
        file: "private/qqmlmodelindexvaluetype_p.h"
        name: "QPersistentModelIndex"
        accessSemantics: "value"
        extension: "QQmlPersistentModelIndexValueType"
    }
    Component {
        file: "private/qqmlmodelindexvaluetype_p.h"
        name: "QQmlPersistentModelIndexValueType"
        accessSemantics: "value"
        Property { name: "row"; type: "int"; read: "row"; index: 0; isReadonly: true; isFinal: true }
        Property { name: "column"; type: "int"; read: "column"; index: 1; isReadonly: true; isFinal: true }
        Property {
            name: "parent"
            type: "QModelIndex"
            read: "parent"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property { name: "valid"; type: "bool"; read: "isValid"; index: 3; isReadonly: true; isFinal: true }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "internalId"
            type: "qulonglong"
            read: "internalId"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "data"
            revision: 1543
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "role"; type: "int" }
        }
        Method { name: "data"; revision: 1543; type: "QVariant"; isCloned: true; isMethodConstant: true }
    }
    Component {
        file: "private/qquickpackage_p.h"
        name: "QQuickPackage"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QObject"
        exports: ["QtQml.Models/Package 2.0", "QtQml.Models/Package 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        attachedType: "QQuickPackageAttached"
        Property { name: "data"; type: "QObject"; isList: true; read: "data"; index: 0; isReadonly: true }
    }
    Component {
        file: "private/qquickpackage_p.h"
        name: "QQuickPackageAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 0; isFinal: true }
    }
}
