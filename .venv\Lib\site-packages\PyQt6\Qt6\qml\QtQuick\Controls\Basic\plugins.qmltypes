import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickbasicstyle_p.h"
        name: "QQuickBasicStyle"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.Controls.Basic/Basic 2.1",
            "QtQuick.Controls.Basic/Basic 6.0"
        ]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [513, 1536]
        Property {
            name: "backgroundColor"
            type: "QColor"
            read: "backgroundColor"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "overlayModalColor"
            type: "QColor"
            read: "overlayModalColor"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "overlayDimColor"
            type: "QColor"
            read: "overlayDimColor"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textColor"
            type: "QColor"
            read: "textColor"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textDarkColor"
            type: "QColor"
            read: "textDarkColor"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textLightColor"
            type: "QColor"
            read: "textLightColor"
            index: 5
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textLinkColor"
            type: "QColor"
            read: "textLinkColor"
            index: 6
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textSelectionColor"
            type: "QColor"
            read: "textSelectionColor"
            index: 7
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textDisabledColor"
            type: "QColor"
            read: "textDisabledColor"
            index: 8
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textDisabledLightColor"
            type: "QColor"
            read: "textDisabledLightColor"
            index: 9
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textPlaceholderColor"
            type: "QColor"
            read: "textPlaceholderColor"
            index: 10
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "focusColor"
            type: "QColor"
            read: "focusColor"
            index: 11
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "focusLightColor"
            type: "QColor"
            read: "focusLightColor"
            index: 12
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "focusPressedColor"
            type: "QColor"
            read: "focusPressedColor"
            index: 13
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "buttonColor"
            type: "QColor"
            read: "buttonColor"
            index: 14
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "buttonPressedColor"
            type: "QColor"
            read: "buttonPressedColor"
            index: 15
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "buttonCheckedColor"
            type: "QColor"
            read: "buttonCheckedColor"
            index: 16
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "buttonCheckedPressedColor"
            type: "QColor"
            read: "buttonCheckedPressedColor"
            index: 17
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "buttonCheckedFocusColor"
            type: "QColor"
            read: "buttonCheckedFocusColor"
            index: 18
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "toolButtonColor"
            type: "QColor"
            read: "toolButtonColor"
            index: 19
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "tabButtonColor"
            type: "QColor"
            read: "tabButtonColor"
            index: 20
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "tabButtonPressedColor"
            type: "QColor"
            read: "tabButtonPressedColor"
            index: 21
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "tabButtonCheckedPressedColor"
            type: "QColor"
            read: "tabButtonCheckedPressedColor"
            index: 22
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "delegateColor"
            type: "QColor"
            read: "delegateColor"
            index: 23
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "delegatePressedColor"
            type: "QColor"
            read: "delegatePressedColor"
            index: 24
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "delegateFocusColor"
            type: "QColor"
            read: "delegateFocusColor"
            index: 25
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "indicatorPressedColor"
            type: "QColor"
            read: "indicatorPressedColor"
            index: 26
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "indicatorDisabledColor"
            type: "QColor"
            read: "indicatorDisabledColor"
            index: 27
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "indicatorFrameColor"
            type: "QColor"
            read: "indicatorFrameColor"
            index: 28
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "indicatorFramePressedColor"
            type: "QColor"
            read: "indicatorFramePressedColor"
            index: 29
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "indicatorFrameDisabledColor"
            type: "QColor"
            read: "indicatorFrameDisabledColor"
            index: 30
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "frameDarkColor"
            type: "QColor"
            read: "frameDarkColor"
            index: 31
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "frameLightColor"
            type: "QColor"
            read: "frameLightColor"
            index: 32
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "scrollBarColor"
            type: "QColor"
            read: "scrollBarColor"
            index: 33
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "scrollBarPressedColor"
            type: "QColor"
            read: "scrollBarPressedColor"
            index: 34
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "progressBarColor"
            type: "QColor"
            read: "progressBarColor"
            index: 35
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "pageIndicatorColor"
            type: "QColor"
            read: "pageIndicatorColor"
            index: 36
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "separatorColor"
            type: "QColor"
            read: "separatorColor"
            index: 37
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "disabledDarkColor"
            type: "QColor"
            read: "disabledDarkColor"
            index: 38
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "disabledLightColor"
            type: "QColor"
            read: "disabledLightColor"
            index: 39
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qtquickcontrols2basicforeign_p.h"
        name: "QQuickContextMenu"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtQuick.Controls.Basic/ContextMenu 6.9"]
        isCreatable: false
        exportMetaObjectRevisions: [1545]
        attachedType: "QQuickContextMenu"
        Property {
            name: "menu"
            type: "QQuickMenu"
            isPointer: true
            read: "menu"
            write: "setMenu"
            notify: "menuChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "menuChanged" }
        Signal {
            name: "requested"
            Parameter { name: "position"; type: "QPointF" }
        }
    }
    Component {
        file: "private/qtquickcontrols2basicforeign_p.h"
        name: "QQuickOverlay"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Controls.Basic/Overlay 2.3",
            "QtQuick.Controls.Basic/Overlay 2.4",
            "QtQuick.Controls.Basic/Overlay 2.7",
            "QtQuick.Controls.Basic/Overlay 2.11",
            "QtQuick.Controls.Basic/Overlay 6.0",
            "QtQuick.Controls.Basic/Overlay 6.3",
            "QtQuick.Controls.Basic/Overlay 6.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [515, 516, 519, 523, 1536, 1539, 1543]
        attachedType: "QQuickOverlayAttached"
        Property {
            name: "modal"
            type: "QQmlComponent"
            isPointer: true
            read: "modal"
            write: "setModal"
            notify: "modalChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "modeless"
            type: "QQmlComponent"
            isPointer: true
            read: "modeless"
            write: "setModeless"
            notify: "modelessChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "modalChanged" }
        Signal { name: "modelessChanged" }
        Signal { name: "pressed" }
        Signal { name: "released" }
    }
    Component {
        file: "private/qtquickcontrols2basicforeign_p.h"
        name: "QQuickSplitHandleAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.Controls.Basic/SplitHandle 2.13",
            "QtQuick.Controls.Basic/SplitHandle 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [525, 1536]
        attachedType: "QQuickSplitHandleAttached"
        Property {
            name: "hovered"
            type: "bool"
            read: "isHovered"
            notify: "hoveredChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            notify: "pressedChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Signal { name: "hoveredChanged" }
        Signal { name: "pressedChanged" }
    }
}
