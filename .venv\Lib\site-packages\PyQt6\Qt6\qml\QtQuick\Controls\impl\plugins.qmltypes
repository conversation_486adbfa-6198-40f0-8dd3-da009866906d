import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qpa/qplatformtheme.h"
        name: "QPlatformTheme"
        accessSemantics: "value"
        Enum {
            name: "ThemeHint"
            values: [
                "CursorFlashTime",
                "KeyboardInputInterval",
                "MouseDoubleClickInterval",
                "StartDragDistance",
                "StartDragTime",
                "KeyboardAutoRepeatRate",
                "PasswordMaskDelay",
                "StartDragVelocity",
                "TextCursorWidth",
                "DropShadow",
                "MaximumScrollBarDragDistance",
                "ToolButtonStyle",
                "ToolBarIconSize",
                "ItemViewActivateItemOnSingleClick",
                "SystemIconThemeName",
                "SystemIconFallbackThemeName",
                "IconThemeSearchPaths",
                "StyleNames",
                "WindowAutoPlacement",
                "DialogButtonBoxLayout",
                "DialogButtonBoxButtonsHaveIcons",
                "UseFullScreenForPopupMenu",
                "KeyboardScheme",
                "UiEffects",
                "SpellCheckUnderlineStyle",
                "TabFocusBehavior",
                "IconPixmapSizes",
                "PasswordMaskCharacter",
                "DialogSnapToDefaultButton",
                "ContextMenuOnMouseRelease",
                "MousePressAndHoldInterval",
                "MouseDoubleClickDistance",
                "WheelScrollLines",
                "TouchDoubleTapDistance",
                "ShowShortcutsInContextMenus",
                "IconFallbackSearchPaths",
                "MouseQuickSelectionThreshold",
                "InteractiveResizeAcrossScreens",
                "ShowDirectoriesFirst",
                "PreselectFirstFileInDirectory",
                "ButtonPressKeys",
                "SetFocusOnTouchRelease",
                "FlickStartDistance",
                "FlickMaximumVelocity",
                "FlickDeceleration",
                "MenuBarFocusOnAltPressRelease",
                "MouseCursorTheme",
                "MouseCursorSize",
                "UnderlineShortcut",
                "ShowIconsInMenus",
                "PreferFileIconFromTheme"
            ]
        }
        Enum {
            name: "DialogType"
            values: [
                "FileDialog",
                "ColorDialog",
                "FontDialog",
                "MessageDialog"
            ]
        }
        Enum {
            name: "Palette"
            values: [
                "SystemPalette",
                "ToolTipPalette",
                "ToolButtonPalette",
                "ButtonPalette",
                "CheckBoxPalette",
                "RadioButtonPalette",
                "HeaderPalette",
                "ComboBoxPalette",
                "ItemViewPalette",
                "MessageBoxLabelPelette",
                "MessageBoxLabelPalette",
                "TabBarPalette",
                "LabelPalette",
                "GroupBoxPalette",
                "MenuPalette",
                "MenuBarPalette",
                "TextEditPalette",
                "TextLineEditPalette",
                "NPalettes"
            ]
        }
        Enum {
            name: "Font"
            values: [
                "SystemFont",
                "MenuFont",
                "MenuBarFont",
                "MenuItemFont",
                "MessageBoxFont",
                "LabelFont",
                "TipLabelFont",
                "StatusBarFont",
                "TitleBarFont",
                "MdiSubWindowTitleFont",
                "DockWidgetTitleFont",
                "PushButtonFont",
                "CheckBoxFont",
                "RadioButtonFont",
                "ToolButtonFont",
                "ItemViewFont",
                "ListViewFont",
                "HeaderViewFont",
                "ListBoxFont",
                "ComboMenuItemFont",
                "ComboLineEditFont",
                "SmallFont",
                "MiniFont",
                "FixedFont",
                "GroupBoxTitleFont",
                "TabButtonFont",
                "EditorFont",
                "NFonts"
            ]
        }
        Enum {
            name: "StandardPixmap"
            values: [
                "TitleBarMenuButton",
                "TitleBarMinButton",
                "TitleBarMaxButton",
                "TitleBarCloseButton",
                "TitleBarNormalButton",
                "TitleBarShadeButton",
                "TitleBarUnshadeButton",
                "TitleBarContextHelpButton",
                "DockWidgetCloseButton",
                "MessageBoxInformation",
                "MessageBoxWarning",
                "MessageBoxCritical",
                "MessageBoxQuestion",
                "DesktopIcon",
                "TrashIcon",
                "ComputerIcon",
                "DriveFDIcon",
                "DriveHDIcon",
                "DriveCDIcon",
                "DriveDVDIcon",
                "DriveNetIcon",
                "DirOpenIcon",
                "DirClosedIcon",
                "DirLinkIcon",
                "DirLinkOpenIcon",
                "FileIcon",
                "FileLinkIcon",
                "ToolBarHorizontalExtensionButton",
                "ToolBarVerticalExtensionButton",
                "FileDialogStart",
                "FileDialogEnd",
                "FileDialogToParent",
                "FileDialogNewFolder",
                "FileDialogDetailedView",
                "FileDialogInfoView",
                "FileDialogContentsView",
                "FileDialogListView",
                "FileDialogBack",
                "DirIcon",
                "DialogOkButton",
                "DialogCancelButton",
                "DialogHelpButton",
                "DialogOpenButton",
                "DialogSaveButton",
                "DialogCloseButton",
                "DialogApplyButton",
                "DialogResetButton",
                "DialogDiscardButton",
                "DialogYesButton",
                "DialogNoButton",
                "ArrowUp",
                "ArrowDown",
                "ArrowLeft",
                "ArrowRight",
                "ArrowBack",
                "ArrowForward",
                "DirHomeIcon",
                "CommandLink",
                "VistaShield",
                "BrowserReload",
                "BrowserStop",
                "MediaPlay",
                "MediaStop",
                "MediaPause",
                "MediaSkipForward",
                "MediaSkipBackward",
                "MediaSeekForward",
                "MediaSeekBackward",
                "MediaVolume",
                "MediaVolumeMuted",
                "LineEditClearButton",
                "DialogYesToAllButton",
                "DialogNoToAllButton",
                "DialogSaveAllButton",
                "DialogAbortButton",
                "DialogRetryButton",
                "DialogIgnoreButton",
                "RestoreDefaultsButton",
                "TabCloseButton",
                "NStandardPixmap",
                "CustomBase"
            ]
        }
        Enum {
            name: "KeyboardSchemes"
            values: [
                "WindowsKeyboardScheme",
                "MacKeyboardScheme",
                "X11KeyboardScheme",
                "KdeKeyboardScheme",
                "GnomeKeyboardScheme",
                "CdeKeyboardScheme"
            ]
        }
        Enum {
            name: "UiEffect"
            values: [
                "GeneralUiEffect",
                "AnimateMenuUiEffect",
                "FadeMenuUiEffect",
                "AnimateComboUiEffect",
                "AnimateTooltipUiEffect",
                "FadeTooltipUiEffect",
                "AnimateToolBoxUiEffect",
                "HoverEffect"
            ]
        }
    }
    Component {
        file: "private/qquickimageselector_p.h"
        name: "QQuickAnimatedImageSelector"
        accessSemantics: "reference"
        prototype: "QQuickImageSelector"
        exports: [
            "QtQuick.Controls.impl/AnimatedImageSelector 2.3",
            "QtQuick.Controls.impl/AnimatedImageSelector 6.0"
        ]
        exportMetaObjectRevisions: [515, 1536]
    }
    Component {
        file: "private/qquickchecklabel_p.h"
        name: "QQuickCheckLabel"
        accessSemantics: "reference"
        prototype: "QQuickText"
        exports: [
            "QtQuick.Controls.impl/CheckLabel 2.3",
            "QtQuick.Controls.impl/CheckLabel 2.4",
            "QtQuick.Controls.impl/CheckLabel 2.6",
            "QtQuick.Controls.impl/CheckLabel 2.7",
            "QtQuick.Controls.impl/CheckLabel 2.9",
            "QtQuick.Controls.impl/CheckLabel 2.10",
            "QtQuick.Controls.impl/CheckLabel 2.11",
            "QtQuick.Controls.impl/CheckLabel 6.0",
            "QtQuick.Controls.impl/CheckLabel 6.2",
            "QtQuick.Controls.impl/CheckLabel 6.3",
            "QtQuick.Controls.impl/CheckLabel 6.7"
        ]
        exportMetaObjectRevisions: [
            515,
            516,
            518,
            519,
            521,
            522,
            523,
            1536,
            1538,
            1539,
            1543
        ]
    }
    Component {
        file: "private/qquickclippedtext_p.h"
        name: "QQuickClippedText"
        accessSemantics: "reference"
        prototype: "QQuickText"
        exports: [
            "QtQuick.Controls.impl/ClippedText 2.2",
            "QtQuick.Controls.impl/ClippedText 2.3",
            "QtQuick.Controls.impl/ClippedText 2.4",
            "QtQuick.Controls.impl/ClippedText 2.6",
            "QtQuick.Controls.impl/ClippedText 2.7",
            "QtQuick.Controls.impl/ClippedText 2.9",
            "QtQuick.Controls.impl/ClippedText 2.10",
            "QtQuick.Controls.impl/ClippedText 2.11",
            "QtQuick.Controls.impl/ClippedText 6.0",
            "QtQuick.Controls.impl/ClippedText 6.2",
            "QtQuick.Controls.impl/ClippedText 6.3",
            "QtQuick.Controls.impl/ClippedText 6.7"
        ]
        exportMetaObjectRevisions: [
            514,
            515,
            516,
            518,
            519,
            521,
            522,
            523,
            1536,
            1538,
            1539,
            1543
        ]
        Property { name: "clipX"; type: "double"; read: "clipX"; write: "setClipX"; index: 0; isFinal: true }
        Property { name: "clipY"; type: "double"; read: "clipY"; write: "setClipY"; index: 1; isFinal: true }
        Property {
            name: "clipWidth"
            type: "double"
            read: "clipWidth"
            write: "setClipWidth"
            index: 2
            isFinal: true
        }
        Property {
            name: "clipHeight"
            type: "double"
            read: "clipHeight"
            write: "setClipHeight"
            index: 3
            isFinal: true
        }
    }
    Component {
        file: "private/qquickcolor_p.h"
        name: "QQuickColor"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.Controls.impl/Color 2.3",
            "QtQuick.Controls.impl/Color 6.0"
        ]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [515, 1536]
        Method {
            name: "transparent"
            type: "QColor"
            isMethodConstant: true
            Parameter { name: "color"; type: "QColor" }
            Parameter { name: "opacity"; type: "double" }
        }
        Method {
            name: "blend"
            type: "QColor"
            isMethodConstant: true
            Parameter { name: "a"; type: "QColor" }
            Parameter { name: "b"; type: "QColor" }
            Parameter { name: "factor"; type: "double" }
        }
    }
    Component {
        file: "private/qquickcolorimage_p.h"
        name: "QQuickColorImage"
        accessSemantics: "reference"
        prototype: "QQuickImage"
        exports: [
            "QtQuick.Controls.impl/ColorImage 2.3",
            "QtQuick.Controls.impl/ColorImage 2.4",
            "QtQuick.Controls.impl/ColorImage 2.5",
            "QtQuick.Controls.impl/ColorImage 2.7",
            "QtQuick.Controls.impl/ColorImage 2.11",
            "QtQuick.Controls.impl/ColorImage 2.14",
            "QtQuick.Controls.impl/ColorImage 2.15",
            "QtQuick.Controls.impl/ColorImage 6.0",
            "QtQuick.Controls.impl/ColorImage 6.2",
            "QtQuick.Controls.impl/ColorImage 6.3",
            "QtQuick.Controls.impl/ColorImage 6.7",
            "QtQuick.Controls.impl/ColorImage 6.8"
        ]
        exportMetaObjectRevisions: [
            515,
            516,
            517,
            519,
            523,
            526,
            527,
            1536,
            1538,
            1539,
            1543,
            1544
        ]
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            reset: "resetColor"
            notify: "colorChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "defaultColor"
            type: "QColor"
            read: "defaultColor"
            write: "setDefaultColor"
            reset: "resetDefaultColor"
            notify: "defaultColorChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "colorChanged" }
        Signal { name: "defaultColorChanged" }
    }
    Component {
        file: "private/qquickiconimage_p.h"
        name: "QQuickIconImage"
        accessSemantics: "reference"
        prototype: "QQuickImage"
        exports: [
            "QtQuick.Controls.impl/IconImage 2.3",
            "QtQuick.Controls.impl/IconImage 2.4",
            "QtQuick.Controls.impl/IconImage 2.5",
            "QtQuick.Controls.impl/IconImage 2.7",
            "QtQuick.Controls.impl/IconImage 2.11",
            "QtQuick.Controls.impl/IconImage 2.14",
            "QtQuick.Controls.impl/IconImage 2.15",
            "QtQuick.Controls.impl/IconImage 6.0",
            "QtQuick.Controls.impl/IconImage 6.2",
            "QtQuick.Controls.impl/IconImage 6.3",
            "QtQuick.Controls.impl/IconImage 6.7",
            "QtQuick.Controls.impl/IconImage 6.8"
        ]
        exportMetaObjectRevisions: [
            515,
            516,
            517,
            519,
            523,
            526,
            527,
            1536,
            1538,
            1539,
            1543,
            1544
        ]
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "nameChanged" }
        Signal { name: "colorChanged" }
    }
    Component {
        file: "private/qquickiconlabel_p.h"
        name: "QQuickIconLabel"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Controls.impl/IconLabel 2.3",
            "QtQuick.Controls.impl/IconLabel 2.4",
            "QtQuick.Controls.impl/IconLabel 2.7",
            "QtQuick.Controls.impl/IconLabel 2.11",
            "QtQuick.Controls.impl/IconLabel 6.0",
            "QtQuick.Controls.impl/IconLabel 6.3",
            "QtQuick.Controls.impl/IconLabel 6.7"
        ]
        exportMetaObjectRevisions: [515, 516, 519, 523, 1536, 1539, 1543]
        Enum {
            name: "Display"
            values: [
                "IconOnly",
                "TextOnly",
                "TextBesideIcon",
                "TextUnderIcon"
            ]
        }
        Property { name: "icon"; type: "QQuickIcon"; read: "icon"; write: "setIcon"; index: 0; isFinal: true }
        Property { name: "text"; type: "QString"; read: "text"; write: "setText"; index: 1; isFinal: true }
        Property { name: "font"; type: "QFont"; read: "font"; write: "setFont"; index: 2; isFinal: true }
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 3; isFinal: true }
        Property {
            name: "display"
            type: "Display"
            read: "display"
            write: "setDisplay"
            index: 4
            isFinal: true
        }
        Property {
            name: "spacing"
            type: "double"
            read: "spacing"
            write: "setSpacing"
            index: 5
            isFinal: true
        }
        Property {
            name: "mirrored"
            type: "bool"
            read: "isMirrored"
            write: "setMirrored"
            index: 6
            isFinal: true
        }
        Property {
            name: "alignment"
            type: "Qt::Alignment"
            read: "alignment"
            write: "setAlignment"
            index: 7
            isFinal: true
        }
        Property {
            name: "topPadding"
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            reset: "resetTopPadding"
            index: 8
            isFinal: true
        }
        Property {
            name: "leftPadding"
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            reset: "resetLeftPadding"
            index: 9
            isFinal: true
        }
        Property {
            name: "rightPadding"
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            reset: "resetRightPadding"
            index: 10
            isFinal: true
        }
        Property {
            name: "bottomPadding"
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            reset: "resetBottomPadding"
            index: 11
            isFinal: true
        }
    }
    Component {
        file: "private/qquickimageselector_p.h"
        name: "QQuickImageSelector"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus", "QQmlPropertyValueInterceptor"]
        exports: [
            "QtQuick.Controls.impl/ImageSelector 2.3",
            "QtQuick.Controls.impl/ImageSelector 6.0"
        ]
        exportMetaObjectRevisions: [515, 1536]
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            notify: "sourceChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 1; isFinal: true }
        Property { name: "path"; type: "QString"; read: "path"; write: "setPath"; index: 2; isFinal: true }
        Property {
            name: "states"
            type: "QVariantList"
            read: "states"
            write: "setStates"
            index: 3
            isFinal: true
        }
        Property {
            name: "separator"
            type: "QString"
            read: "separator"
            write: "setSeparator"
            index: 4
            isFinal: true
        }
        Property { name: "cache"; type: "bool"; read: "cache"; write: "setCache"; index: 5; isFinal: true }
        Signal { name: "sourceChanged" }
    }
    Component {
        file: "private/qquickitemgroup_p.h"
        name: "QQuickItemGroup"
        accessSemantics: "reference"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick.Controls.impl/ItemGroup 2.2",
            "QtQuick.Controls.impl/ItemGroup 2.4",
            "QtQuick.Controls.impl/ItemGroup 2.7",
            "QtQuick.Controls.impl/ItemGroup 2.11",
            "QtQuick.Controls.impl/ItemGroup 6.0",
            "QtQuick.Controls.impl/ItemGroup 6.2",
            "QtQuick.Controls.impl/ItemGroup 6.3",
            "QtQuick.Controls.impl/ItemGroup 6.7"
        ]
        exportMetaObjectRevisions: [
            514,
            516,
            519,
            523,
            1536,
            1538,
            1539,
            1543
        ]
    }
    Component {
        file: "private/qquickmnemoniclabel_p.h"
        name: "QQuickMnemonicLabel"
        accessSemantics: "reference"
        prototype: "QQuickText"
        exports: [
            "QtQuick.Controls.impl/MnemonicLabel 2.3",
            "QtQuick.Controls.impl/MnemonicLabel 2.4",
            "QtQuick.Controls.impl/MnemonicLabel 2.6",
            "QtQuick.Controls.impl/MnemonicLabel 2.7",
            "QtQuick.Controls.impl/MnemonicLabel 2.9",
            "QtQuick.Controls.impl/MnemonicLabel 2.10",
            "QtQuick.Controls.impl/MnemonicLabel 2.11",
            "QtQuick.Controls.impl/MnemonicLabel 6.0",
            "QtQuick.Controls.impl/MnemonicLabel 6.2",
            "QtQuick.Controls.impl/MnemonicLabel 6.3",
            "QtQuick.Controls.impl/MnemonicLabel 6.7"
        ]
        exportMetaObjectRevisions: [
            515,
            516,
            518,
            519,
            521,
            522,
            523,
            1536,
            1538,
            1539,
            1543
        ]
        Property { name: "text"; type: "QString"; read: "text"; write: "setText"; index: 0; isFinal: true }
        Property {
            name: "mnemonicVisible"
            type: "bool"
            read: "isMnemonicVisible"
            write: "setMnemonicVisible"
            index: 1
            isFinal: true
        }
    }
    Component {
        file: "private/qquickninepatchimage_p.h"
        name: "QQuickNinePatchImage"
        accessSemantics: "reference"
        prototype: "QQuickImage"
        exports: [
            "QtQuick.Controls.impl/NinePatchImage 2.3",
            "QtQuick.Controls.impl/NinePatchImage 2.4",
            "QtQuick.Controls.impl/NinePatchImage 2.5",
            "QtQuick.Controls.impl/NinePatchImage 2.7",
            "QtQuick.Controls.impl/NinePatchImage 2.11",
            "QtQuick.Controls.impl/NinePatchImage 2.14",
            "QtQuick.Controls.impl/NinePatchImage 2.15",
            "QtQuick.Controls.impl/NinePatchImage 6.0",
            "QtQuick.Controls.impl/NinePatchImage 6.2",
            "QtQuick.Controls.impl/NinePatchImage 6.3",
            "QtQuick.Controls.impl/NinePatchImage 6.7",
            "QtQuick.Controls.impl/NinePatchImage 6.8"
        ]
        exportMetaObjectRevisions: [
            515,
            516,
            517,
            519,
            523,
            526,
            527,
            1536,
            1538,
            1539,
            1543,
            1544
        ]
        Property {
            name: "topPadding"
            type: "double"
            read: "topPadding"
            notify: "topPaddingChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "leftPadding"
            type: "double"
            read: "leftPadding"
            notify: "leftPaddingChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "rightPadding"
            type: "double"
            read: "rightPadding"
            notify: "rightPaddingChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "bottomPadding"
            type: "double"
            read: "bottomPadding"
            notify: "bottomPaddingChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "topInset"
            type: "double"
            read: "topInset"
            notify: "topInsetChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "leftInset"
            type: "double"
            read: "leftInset"
            notify: "leftInsetChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "rightInset"
            type: "double"
            read: "rightInset"
            notify: "rightInsetChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "bottomInset"
            type: "double"
            read: "bottomInset"
            notify: "bottomInsetChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Signal { name: "topPaddingChanged" }
        Signal { name: "leftPaddingChanged" }
        Signal { name: "rightPaddingChanged" }
        Signal { name: "bottomPaddingChanged" }
        Signal { name: "topInsetChanged" }
        Signal { name: "leftInsetChanged" }
        Signal { name: "rightInsetChanged" }
        Signal { name: "bottomInsetChanged" }
    }
    Component {
        file: "private/qquickimageselector_p.h"
        name: "QQuickNinePatchImageSelector"
        accessSemantics: "reference"
        prototype: "QQuickImageSelector"
        exports: [
            "QtQuick.Controls.impl/NinePatchImageSelector 2.3",
            "QtQuick.Controls.impl/NinePatchImageSelector 6.0"
        ]
        exportMetaObjectRevisions: [515, 1536]
    }
    Component {
        file: "private/qquickpaddedrectangle_p.h"
        name: "QQuickPaddedRectangle"
        accessSemantics: "reference"
        prototype: "QQuickRectangle"
        exports: [
            "QtQuick.Controls.impl/PaddedRectangle 2.0",
            "QtQuick.Controls.impl/PaddedRectangle 2.1",
            "QtQuick.Controls.impl/PaddedRectangle 2.4",
            "QtQuick.Controls.impl/PaddedRectangle 2.7",
            "QtQuick.Controls.impl/PaddedRectangle 2.11",
            "QtQuick.Controls.impl/PaddedRectangle 6.0",
            "QtQuick.Controls.impl/PaddedRectangle 6.3",
            "QtQuick.Controls.impl/PaddedRectangle 6.7"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539, 1543]
        Property {
            name: "padding"
            type: "double"
            read: "padding"
            write: "setPadding"
            reset: "resetPadding"
            notify: "paddingChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "topPadding"
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            reset: "resetTopPadding"
            notify: "topPaddingChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "leftPadding"
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            reset: "resetLeftPadding"
            notify: "leftPaddingChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "rightPadding"
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            reset: "resetRightPadding"
            notify: "rightPaddingChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "bottomPadding"
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            reset: "resetBottomPadding"
            notify: "bottomPaddingChanged"
            index: 4
            isFinal: true
        }
        Signal { name: "paddingChanged" }
        Signal { name: "topPaddingChanged" }
        Signal { name: "leftPaddingChanged" }
        Signal { name: "rightPaddingChanged" }
        Signal { name: "bottomPaddingChanged" }
    }
    Component {
        file: "private/qquickplaceholdertext_p.h"
        name: "QQuickPlaceholderText"
        accessSemantics: "reference"
        prototype: "QQuickText"
        exports: [
            "QtQuick.Controls.impl/PlaceholderText 2.2",
            "QtQuick.Controls.impl/PlaceholderText 2.3",
            "QtQuick.Controls.impl/PlaceholderText 2.4",
            "QtQuick.Controls.impl/PlaceholderText 2.6",
            "QtQuick.Controls.impl/PlaceholderText 2.7",
            "QtQuick.Controls.impl/PlaceholderText 2.9",
            "QtQuick.Controls.impl/PlaceholderText 2.10",
            "QtQuick.Controls.impl/PlaceholderText 2.11",
            "QtQuick.Controls.impl/PlaceholderText 6.0",
            "QtQuick.Controls.impl/PlaceholderText 6.2",
            "QtQuick.Controls.impl/PlaceholderText 6.3",
            "QtQuick.Controls.impl/PlaceholderText 6.7"
        ]
        exportMetaObjectRevisions: [
            514,
            515,
            516,
            518,
            519,
            521,
            522,
            523,
            1536,
            1538,
            1539,
            1543
        ]
        Method { name: "updateAlignment" }
    }
    Component {
        file: "private/qquickplatformtheme_p.h"
        name: "QQuickPlatformTheme"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QPlatformTheme"
        extensionIsNamespace: true
        exports: ["QtQuick.Controls.impl/PlatformTheme 6.3"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [1539]
        Method {
            name: "themeHint"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "themeHint"; type: "QPlatformTheme::ThemeHint" }
        }
    }
    Component {
        file: "private/qquickchecklabel_p.h"
        name: "QQuickText"
        accessSemantics: "reference"
        prototype: "QQuickImplicitSizeItem"
        interfaces: ["QQuickTextInterface"]
        Enum {
            name: "HAlignment"
            values: [
                "AlignLeft",
                "AlignRight",
                "AlignHCenter",
                "AlignJustify"
            ]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Enum {
            name: "TextStyle"
            values: ["Normal", "Outline", "Raised", "Sunken"]
        }
        Enum {
            name: "TextFormat"
            values: [
                "PlainText",
                "RichText",
                "MarkdownText",
                "AutoText",
                "StyledText"
            ]
        }
        Enum {
            name: "TextElideMode"
            values: ["ElideLeft", "ElideRight", "ElideMiddle", "ElideNone"]
        }
        Enum {
            name: "WrapMode"
            values: [
                "NoWrap",
                "WordWrap",
                "WrapAnywhere",
                "WrapAtWordBoundaryOrAnywhere",
                "Wrap"
            ]
        }
        Enum {
            name: "RenderType"
            values: ["QtRendering", "NativeRendering", "CurveRendering"]
        }
        Enum {
            name: "RenderTypeQuality"
            values: [
                "DefaultRenderTypeQuality",
                "LowRenderTypeQuality",
                "NormalRenderTypeQuality",
                "HighRenderTypeQuality",
                "VeryHighRenderTypeQuality"
            ]
        }
        Enum {
            name: "LineHeightMode"
            values: ["ProportionalHeight", "FixedHeight"]
        }
        Enum {
            name: "FontSizeMode"
            values: ["FixedSize", "HorizontalFit", "VerticalFit", "Fit"]
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 1
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 2
        }
        Property {
            name: "linkColor"
            type: "QColor"
            read: "linkColor"
            write: "setLinkColor"
            notify: "linkColorChanged"
            index: 3
        }
        Property {
            name: "style"
            type: "TextStyle"
            read: "style"
            write: "setStyle"
            notify: "styleChanged"
            index: 4
        }
        Property {
            name: "styleColor"
            type: "QColor"
            read: "styleColor"
            write: "setStyleColor"
            notify: "styleColorChanged"
            index: 5
        }
        Property {
            name: "horizontalAlignment"
            type: "HAlignment"
            read: "hAlign"
            write: "setHAlign"
            reset: "resetHAlign"
            notify: "horizontalAlignmentChanged"
            index: 6
        }
        Property {
            name: "effectiveHorizontalAlignment"
            type: "HAlignment"
            read: "effectiveHAlign"
            notify: "effectiveHorizontalAlignmentChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "verticalAlignment"
            type: "VAlignment"
            read: "vAlign"
            write: "setVAlign"
            notify: "verticalAlignmentChanged"
            index: 8
        }
        Property {
            name: "wrapMode"
            type: "WrapMode"
            read: "wrapMode"
            write: "setWrapMode"
            notify: "wrapModeChanged"
            index: 9
        }
        Property {
            name: "lineCount"
            type: "int"
            read: "lineCount"
            notify: "lineCountChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "truncated"
            type: "bool"
            read: "truncated"
            notify: "truncatedChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "maximumLineCount"
            type: "int"
            read: "maximumLineCount"
            write: "setMaximumLineCount"
            reset: "resetMaximumLineCount"
            notify: "maximumLineCountChanged"
            index: 12
        }
        Property {
            name: "textFormat"
            type: "TextFormat"
            read: "textFormat"
            write: "setTextFormat"
            notify: "textFormatChanged"
            index: 13
        }
        Property {
            name: "elide"
            type: "TextElideMode"
            read: "elideMode"
            write: "setElideMode"
            notify: "elideModeChanged"
            index: 14
        }
        Property {
            name: "contentWidth"
            type: "double"
            read: "contentWidth"
            notify: "contentWidthChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "contentHeight"
            type: "double"
            read: "contentHeight"
            notify: "contentHeightChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "paintedWidth"
            type: "double"
            read: "contentWidth"
            notify: "contentWidthChanged"
            index: 17
            isReadonly: true
        }
        Property {
            name: "paintedHeight"
            type: "double"
            read: "contentHeight"
            notify: "contentHeightChanged"
            index: 18
            isReadonly: true
        }
        Property {
            name: "lineHeight"
            type: "double"
            read: "lineHeight"
            write: "setLineHeight"
            notify: "lineHeightChanged"
            index: 19
        }
        Property {
            name: "lineHeightMode"
            type: "LineHeightMode"
            read: "lineHeightMode"
            write: "setLineHeightMode"
            notify: "lineHeightModeChanged"
            index: 20
        }
        Property {
            name: "baseUrl"
            type: "QUrl"
            read: "baseUrl"
            write: "setBaseUrl"
            reset: "resetBaseUrl"
            notify: "baseUrlChanged"
            index: 21
        }
        Property {
            name: "minimumPixelSize"
            type: "int"
            read: "minimumPixelSize"
            write: "setMinimumPixelSize"
            notify: "minimumPixelSizeChanged"
            index: 22
        }
        Property {
            name: "minimumPointSize"
            type: "int"
            read: "minimumPointSize"
            write: "setMinimumPointSize"
            notify: "minimumPointSizeChanged"
            index: 23
        }
        Property {
            name: "fontSizeMode"
            type: "FontSizeMode"
            read: "fontSizeMode"
            write: "setFontSizeMode"
            notify: "fontSizeModeChanged"
            index: 24
        }
        Property {
            name: "renderType"
            type: "RenderType"
            read: "renderType"
            write: "setRenderType"
            notify: "renderTypeChanged"
            index: 25
        }
        Property {
            name: "hoveredLink"
            revision: 514
            type: "QString"
            read: "hoveredLink"
            notify: "linkHovered"
            index: 26
            isReadonly: true
        }
        Property {
            name: "renderTypeQuality"
            revision: 1536
            type: "int"
            read: "renderTypeQuality"
            write: "setRenderTypeQuality"
            notify: "renderTypeQualityChanged"
            index: 27
        }
        Property {
            name: "padding"
            revision: 518
            type: "double"
            read: "padding"
            write: "setPadding"
            reset: "resetPadding"
            notify: "paddingChanged"
            index: 28
        }
        Property {
            name: "topPadding"
            revision: 518
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            reset: "resetTopPadding"
            notify: "topPaddingChanged"
            index: 29
        }
        Property {
            name: "leftPadding"
            revision: 518
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            reset: "resetLeftPadding"
            notify: "leftPaddingChanged"
            index: 30
        }
        Property {
            name: "rightPadding"
            revision: 518
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            reset: "resetRightPadding"
            notify: "rightPaddingChanged"
            index: 31
        }
        Property {
            name: "bottomPadding"
            revision: 518
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            reset: "resetBottomPadding"
            notify: "bottomPaddingChanged"
            index: 32
        }
        Property {
            name: "fontInfo"
            revision: 521
            type: "QJSValue"
            read: "fontInfo"
            notify: "fontInfoChanged"
            index: 33
            isReadonly: true
        }
        Property {
            name: "advance"
            revision: 522
            type: "QSizeF"
            read: "advance"
            notify: "contentSizeChanged"
            index: 34
            isReadonly: true
        }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "QString" }
        }
        Signal {
            name: "linkActivated"
            Parameter { name: "link"; type: "QString" }
        }
        Signal {
            name: "linkHovered"
            revision: 514
            Parameter { name: "link"; type: "QString" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal { name: "colorChanged" }
        Signal { name: "linkColorChanged" }
        Signal {
            name: "styleChanged"
            Parameter { name: "style"; type: "QQuickText::TextStyle" }
        }
        Signal { name: "styleColorChanged" }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickText::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickText::VAlignment" }
        }
        Signal { name: "wrapModeChanged" }
        Signal { name: "lineCountChanged" }
        Signal { name: "truncatedChanged" }
        Signal { name: "maximumLineCountChanged" }
        Signal {
            name: "textFormatChanged"
            Parameter { name: "textFormat"; type: "QQuickText::TextFormat" }
        }
        Signal {
            name: "elideModeChanged"
            Parameter { name: "mode"; type: "QQuickText::TextElideMode" }
        }
        Signal { name: "contentSizeChanged" }
        Signal {
            name: "contentWidthChanged"
            Parameter { name: "contentWidth"; type: "double" }
        }
        Signal {
            name: "contentHeightChanged"
            Parameter { name: "contentHeight"; type: "double" }
        }
        Signal {
            name: "lineHeightChanged"
            Parameter { name: "lineHeight"; type: "double" }
        }
        Signal {
            name: "lineHeightModeChanged"
            Parameter { name: "mode"; type: "LineHeightMode" }
        }
        Signal { name: "fontSizeModeChanged" }
        Signal { name: "minimumPixelSizeChanged" }
        Signal { name: "minimumPointSizeChanged" }
        Signal { name: "effectiveHorizontalAlignmentChanged" }
        Signal {
            name: "lineLaidOut"
            Parameter { name: "line"; type: "QQuickTextLine"; isPointer: true }
        }
        Signal { name: "baseUrlChanged" }
        Signal { name: "renderTypeChanged" }
        Signal { name: "paddingChanged"; revision: 518 }
        Signal { name: "topPaddingChanged"; revision: 518 }
        Signal { name: "leftPaddingChanged"; revision: 518 }
        Signal { name: "rightPaddingChanged"; revision: 518 }
        Signal { name: "bottomPaddingChanged"; revision: 518 }
        Signal { name: "fontInfoChanged"; revision: 521 }
        Signal { name: "renderTypeQualityChanged"; revision: 1536 }
        Method { name: "q_updateLayout" }
        Method { name: "triggerPreprocess" }
        Method {
            name: "loadResource"
            revision: 1543
            type: "QVariant"
            Parameter { name: "type"; type: "int" }
            Parameter { name: "source"; type: "QUrl" }
        }
        Method { name: "resourceRequestFinished" }
        Method { name: "imageDownloadFinished" }
        Method { name: "forceLayout"; revision: 521 }
        Method {
            name: "linkAt"
            revision: 515
            type: "QString"
            isMethodConstant: true
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        file: "private/qquicktumblerview_p.h"
        name: "QQuickTumblerView"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Controls.impl/TumblerView 2.1",
            "QtQuick.Controls.impl/TumblerView 2.4",
            "QtQuick.Controls.impl/TumblerView 2.7",
            "QtQuick.Controls.impl/TumblerView 2.11",
            "QtQuick.Controls.impl/TumblerView 6.0",
            "QtQuick.Controls.impl/TumblerView 6.3",
            "QtQuick.Controls.impl/TumblerView 6.7"
        ]
        exportMetaObjectRevisions: [513, 516, 519, 523, 1536, 1539, 1543]
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 0
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 1
        }
        Property {
            name: "path"
            type: "QQuickPath"
            isPointer: true
            read: "path"
            write: "setPath"
            notify: "pathChanged"
            index: 2
        }
        Signal { name: "modelChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "pathChanged" }
    }
}
