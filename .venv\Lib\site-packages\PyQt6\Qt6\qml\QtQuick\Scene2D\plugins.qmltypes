import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qscene2d_p.h"
        name: "Qt3DRender::Quick::QScene2D"
        accessSemantics: "reference"
        defaultProperty: "item"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DRender::Render::Quick::QQuick3DScene2D"
        exports: [
            "QtQuick.Scene2D/Scene2D 2.9",
            "QtQuick.Scene2D/Scene2D 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Enum {
            name: "RenderPolicy"
            values: ["Continuous", "SingleShot"]
        }
        Property {
            name: "output"
            type: "Qt3DRender::QRenderTargetOutput"
            isPointer: true
            read: "output"
            write: "setOutput"
            notify: "outputChanged"
            index: 0
        }
        Property {
            name: "renderPolicy"
            type: "RenderPolicy"
            read: "renderPolicy"
            write: "setRenderPolicy"
            notify: "renderPolicyChanged"
            index: 1
        }
        Property {
            name: "item"
            type: "QQuickItem"
            isPointer: true
            read: "item"
            write: "setItem"
            notify: "itemChanged"
            index: 2
        }
        Property {
            name: "mouseEnabled"
            type: "bool"
            read: "isMouseEnabled"
            write: "setMouseEnabled"
            notify: "mouseEnabledChanged"
            index: 3
        }
        Signal {
            name: "outputChanged"
            Parameter { name: "output"; type: "Qt3DRender::QRenderTargetOutput"; isPointer: true }
        }
        Signal {
            name: "renderPolicyChanged"
            Parameter { name: "policy"; type: "QScene2D::RenderPolicy" }
        }
        Signal {
            name: "itemChanged"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "mouseEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setOutput"
            Parameter { name: "output"; type: "Qt3DRender::QRenderTargetOutput"; isPointer: true }
        }
        Method {
            name: "setRenderPolicy"
            Parameter { name: "policy"; type: "QScene2D::RenderPolicy" }
        }
        Method {
            name: "setItem"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "setMouseEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dscene2d_p.h"
        name: "Qt3DRender::Render::Quick::QQuick3DScene2D"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "entities"
            type: "Qt3DCore::QEntity"
            isList: true
            read: "entities"
            index: 0
            isReadonly: true
        }
    }
}
