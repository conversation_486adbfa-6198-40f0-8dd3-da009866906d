# The PEP 484 type hints stub file for the QtQml module.
#
# Generated by SIP 6.10.0
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import collections, re, typing, enum

try:
    from warnings import deprecated
except ImportError:
    pass

import PyQt6.sip

from PyQt6 import QtCore
from PyQt6 import QtNetwork

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[collections.abc.Callable[..., Any], QtCore.pyqtBoundSignal]


class QJSEngine(QtCore.QObject):

    class ObjectOwnership(enum.Enum):
        CppOwnership = ... # type: QJSEngine.ObjectOwnership
        JavaScriptOwnership = ... # type: QJSEngine.ObjectOwnership

    class Extension(enum.Flag):
        TranslationExtension = ... # type: QJSEngine.Extension
        ConsoleExtension = ... # type: QJSEngine.Extension
        GarbageCollectionExtension = ... # type: QJSEngine.Extension
        AllExtensions = ... # type: QJSEngine.Extension

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject]) -> None: ...

    def toPrimitiveValue(self, value: typing.Any) -> 'QJSPrimitiveValue': ...
    def toManagedValue(self, value: typing.Any) -> 'QJSManagedValue': ...
    def toScriptValue(self, value: typing.Any) -> 'QJSValue': ...
    def newSymbol(self, name: typing.Optional[str]) -> 'QJSValue': ...
    def registerModule(self, moduleName: typing.Optional[str], value: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]) -> bool: ...
    uiLanguageChanged: typing.ClassVar[QtCore.pyqtSignal]
    def catchError(self) -> 'QJSValue': ...
    def hasError(self) -> bool: ...
    @staticmethod
    def objectOwnership(a0: typing.Optional[QtCore.QObject]) -> 'QJSEngine.ObjectOwnership': ...
    @staticmethod
    def setObjectOwnership(a0: typing.Optional[QtCore.QObject], a1: 'QJSEngine.ObjectOwnership') -> None: ...
    def setUiLanguage(self, language: typing.Optional[str]) -> None: ...
    def uiLanguage(self) -> str: ...
    def isInterrupted(self) -> bool: ...
    def setInterrupted(self, interrupted: bool) -> None: ...
    @typing.overload
    def throwError(self, message: typing.Optional[str]) -> None: ...
    @typing.overload
    def throwError(self, error: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]) -> None: ...
    @typing.overload
    def throwError(self, errorType: 'QJSValue.ErrorType', message: typing.Optional[str] = ...) -> None: ...
    def newErrorObject(self, errorType: 'QJSValue.ErrorType', message: typing.Optional[str] = ...) -> 'QJSValue': ...
    def importModule(self, fileName: typing.Optional[str]) -> 'QJSValue': ...
    def newQMetaObject(self, metaObject: typing.Optional[QtCore.QMetaObject]) -> 'QJSValue': ...
    def installExtensions(self, extensions: 'QJSEngine.Extension', object: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]] = ...) -> None: ...
    def collectGarbage(self) -> None: ...
    def newQObject(self, object: typing.Optional[QtCore.QObject]) -> 'QJSValue': ...
    def newArray(self, length: int = ...) -> 'QJSValue': ...
    def newObject(self) -> 'QJSValue': ...
    def evaluate(self, program: typing.Optional[str], fileName: typing.Optional[str] = ..., lineNumber: int = ..., exceptionStackTrace: list[str] = ...) -> 'QJSValue': ...
    def globalObject(self) -> 'QJSValue': ...


class QJSManagedValue(PyQt6.sip.simplewrapper):

    class Type(enum.Enum):
        Undefined = ... # type: QJSManagedValue.Type
        Boolean = ... # type: QJSManagedValue.Type
        Number = ... # type: QJSManagedValue.Type
        String = ... # type: QJSManagedValue.Type
        Object = ... # type: QJSManagedValue.Type
        Symbol = ... # type: QJSManagedValue.Type
        Function = ... # type: QJSManagedValue.Type

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, value: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]], engine: typing.Optional[QJSEngine]) -> None: ...
    @typing.overload
    def __init__(self, value: 'QJSPrimitiveValue', engine: typing.Optional[QJSEngine]) -> None: ...
    @typing.overload
    def __init__(self, string: typing.Optional[str], engine: typing.Optional[QJSEngine]) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any, engine: typing.Optional[QJSEngine]) -> None: ...

    def callAsConstructor(self, arguments: collections.abc.Iterable[typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]] = ...) -> 'QJSValue': ...
    def callWithInstance(self, instance: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]], arguments: collections.abc.Iterable[typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]] = ...) -> 'QJSValue': ...
    def call(self, arguments: collections.abc.Iterable[typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]] = ...) -> 'QJSValue': ...
    @typing.overload
    def deleteProperty(self, name: typing.Optional[str]) -> bool: ...
    @typing.overload
    def deleteProperty(self, arrayIndex: int) -> bool: ...
    @typing.overload
    def setProperty(self, name: typing.Optional[str], value: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]) -> None: ...
    @typing.overload
    def setProperty(self, arrayIndex: int, value: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]) -> None: ...
    @typing.overload
    def property(self, name: typing.Optional[str]) -> 'QJSValue': ...
    @typing.overload
    def property(self, arrayIndex: int) -> 'QJSValue': ...
    @typing.overload
    def hasOwnProperty(self, name: typing.Optional[str]) -> bool: ...
    @typing.overload
    def hasOwnProperty(self, arrayIndex: int) -> bool: ...
    @typing.overload
    def hasProperty(self, name: typing.Optional[str]) -> bool: ...
    @typing.overload
    def hasProperty(self, arrayIndex: int) -> bool: ...
    def toDateTime(self) -> QtCore.QDateTime: ...
    def toQMetaObject(self) -> typing.Optional[QtCore.QMetaObject]: ...
    def toQObject(self) -> typing.Optional[QtCore.QObject]: ...
    def toUrl(self) -> QtCore.QUrl: ...
    def toRegularExpression(self) -> QtCore.QRegularExpression: ...
    def toInteger(self) -> int: ...
    def toVariant(self) -> typing.Any: ...
    def toJSValue(self) -> 'QJSValue': ...
    def toPrimitive(self) -> 'QJSPrimitiveValue': ...
    def toBoolean(self) -> bool: ...
    def toNumber(self) -> float: ...
    def toString(self) -> str: ...
    def isError(self) -> bool: ...
    def isDate(self) -> bool: ...
    def isQMetaObject(self) -> bool: ...
    def isQObject(self) -> bool: ...
    def isVariant(self) -> bool: ...
    def isUrl(self) -> bool: ...
    def isArray(self) -> bool: ...
    def isRegularExpression(self) -> bool: ...
    def isNull(self) -> bool: ...
    def isInteger(self) -> bool: ...
    def isFunction(self) -> bool: ...
    def isSymbol(self) -> bool: ...
    def isObject(self) -> bool: ...
    def isString(self) -> bool: ...
    def isNumber(self) -> bool: ...
    def isBoolean(self) -> bool: ...
    def isUndefined(self) -> bool: ...
    def type(self) -> 'QJSManagedValue.Type': ...
    def setPrototype(self, prototype: 'QJSManagedValue') -> None: ...
    def prototype(self) -> 'QJSManagedValue': ...
    def engine(self) -> typing.Optional[QJSEngine]: ...
    def strictlyEquals(self, other: 'QJSManagedValue') -> bool: ...
    def equals(self, other: 'QJSManagedValue') -> bool: ...


class QJSPrimitiveUndefined(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QJSPrimitiveUndefined') -> None: ...


class QJSPrimitiveNull(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QJSPrimitiveNull') -> None: ...


class QJSPrimitiveValue(PyQt6.sip.simplewrapper):

    class Type(enum.Enum):
        Undefined = ... # type: QJSPrimitiveValue.Type
        Null = ... # type: QJSPrimitiveValue.Type
        Boolean = ... # type: QJSPrimitiveValue.Type
        Integer = ... # type: QJSPrimitiveValue.Type
        Double = ... # type: QJSPrimitiveValue.Type
        String = ... # type: QJSPrimitiveValue.Type

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, undefined: QJSPrimitiveUndefined) -> None: ...
    @typing.overload
    def __init__(self, null: QJSPrimitiveNull) -> None: ...
    @typing.overload
    def __init__(self, value: bool) -> None: ...
    @typing.overload
    def __init__(self, value: int) -> None: ...
    @typing.overload
    def __init__(self, value: float) -> None: ...
    @typing.overload
    def __init__(self, string: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QJSPrimitiveValue') -> None: ...

    def __add__(self, rhs: 'QJSPrimitiveValue') -> 'QJSPrimitiveValue': ...
    def __sub__(self, rhs: 'QJSPrimitiveValue') -> 'QJSPrimitiveValue': ...
    def __mul__(self, rhs: 'QJSPrimitiveValue') -> 'QJSPrimitiveValue': ...
    def __truediv__(self, rhs: 'QJSPrimitiveValue') -> 'QJSPrimitiveValue': ...
    def __mod__(self, rhs: 'QJSPrimitiveValue') -> 'QJSPrimitiveValue': ...
    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __lt__(self, rhs: 'QJSPrimitiveValue') -> bool: ...
    def __gt__(self, rhs: 'QJSPrimitiveValue') -> bool: ...
    def __le__(self, rhs: 'QJSPrimitiveValue') -> bool: ...
    def __ge__(self, rhs: 'QJSPrimitiveValue') -> bool: ...
    def data(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def metaType(self) -> QtCore.QMetaType: ...
    def __neg__(self) -> 'QJSPrimitiveValue': ...
    def __pos__(self) -> 'QJSPrimitiveValue': ...
    def equals(self, other: 'QJSPrimitiveValue') -> bool: ...
    def strictlyEquals(self, other: 'QJSPrimitiveValue') -> bool: ...
    def toString(self) -> str: ...
    def toDouble(self) -> float: ...
    def toInteger(self) -> int: ...
    def toBoolean(self) -> bool: ...
    def type(self) -> 'QJSPrimitiveValue.Type': ...


class QJSValue(PyQt6.sip.simplewrapper):

    class ErrorType(enum.Enum):
        GenericError = ... # type: QJSValue.ErrorType
        EvalError = ... # type: QJSValue.ErrorType
        RangeError = ... # type: QJSValue.ErrorType
        ReferenceError = ... # type: QJSValue.ErrorType
        SyntaxError = ... # type: QJSValue.ErrorType
        TypeError = ... # type: QJSValue.ErrorType
        URIError = ... # type: QJSValue.ErrorType

    class ObjectConversionBehavior(enum.Enum):
        ConvertJSObjects = ... # type: QJSValue.ObjectConversionBehavior
        RetainJSObjects = ... # type: QJSValue.ObjectConversionBehavior

    class SpecialValue(enum.Enum):
        NullValue = ... # type: QJSValue.SpecialValue
        UndefinedValue = ... # type: QJSValue.SpecialValue

    @typing.overload
    def __init__(self, value: 'QJSValue.SpecialValue' = ...) -> None: ...
    @typing.overload
    def __init__(self, other: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]) -> None: ...

    def errorType(self) -> 'QJSValue.ErrorType': ...
    def callAsConstructor(self, args: collections.abc.Iterable[typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]] = ...) -> 'QJSValue': ...
    def callWithInstance(self, instance: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]], args: collections.abc.Iterable[typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]] = ...) -> 'QJSValue': ...
    def call(self, args: collections.abc.Iterable[typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]] = ...) -> 'QJSValue': ...
    def isCallable(self) -> bool: ...
    def deleteProperty(self, name: typing.Optional[str]) -> bool: ...
    def hasOwnProperty(self, name: typing.Optional[str]) -> bool: ...
    def hasProperty(self, name: typing.Optional[str]) -> bool: ...
    @typing.overload
    def setProperty(self, name: typing.Optional[str], value: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]) -> None: ...
    @typing.overload
    def setProperty(self, arrayIndex: int, value: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]) -> None: ...
    @typing.overload
    def property(self, name: typing.Optional[str]) -> 'QJSValue': ...
    @typing.overload
    def property(self, arrayIndex: int) -> 'QJSValue': ...
    def setPrototype(self, prototype: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]) -> None: ...
    def prototype(self) -> 'QJSValue': ...
    def strictlyEquals(self, other: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]) -> bool: ...
    def equals(self, other: typing.Union['QJSValue', 'QJSValue.SpecialValue', bool, int, float, typing.Optional[str]]) -> bool: ...
    def toDateTime(self) -> QtCore.QDateTime: ...
    def toQObject(self) -> typing.Optional[QtCore.QObject]: ...
    def toPrimitive(self) -> QJSPrimitiveValue: ...
    @typing.overload
    def toVariant(self) -> typing.Any: ...
    @typing.overload
    def toVariant(self, behavior: 'QJSValue.ObjectConversionBehavior') -> typing.Any: ...
    def toBool(self) -> bool: ...
    def toUInt(self) -> int: ...
    def toInt(self) -> int: ...
    def toNumber(self) -> float: ...
    def toString(self) -> str: ...
    def isUrl(self) -> bool: ...
    def isError(self) -> bool: ...
    def isArray(self) -> bool: ...
    def isRegExp(self) -> bool: ...
    def isDate(self) -> bool: ...
    def isObject(self) -> bool: ...
    def isQObject(self) -> bool: ...
    def isVariant(self) -> bool: ...
    def isUndefined(self) -> bool: ...
    def isString(self) -> bool: ...
    def isNull(self) -> bool: ...
    def isNumber(self) -> bool: ...
    def isBool(self) -> bool: ...


class QJSValueIterator(PyQt6.sip.simplewrapper):

    def __init__(self, value: typing.Union[QJSValue, QJSValue.SpecialValue, bool, int, float, typing.Optional[str]]) -> None: ...

    def value(self) -> QJSValue: ...
    def name(self) -> str: ...
    def next(self) -> bool: ...
    def hasNext(self) -> bool: ...


class QQmlAbstractUrlInterceptor(PyQt6.sip.simplewrapper):

    class DataType(enum.Enum):
        QmlFile = ... # type: QQmlAbstractUrlInterceptor.DataType
        JavaScriptFile = ... # type: QQmlAbstractUrlInterceptor.DataType
        QmldirFile = ... # type: QQmlAbstractUrlInterceptor.DataType
        UrlString = ... # type: QQmlAbstractUrlInterceptor.DataType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQmlAbstractUrlInterceptor') -> None: ...

    def intercept(self, path: QtCore.QUrl, type: 'QQmlAbstractUrlInterceptor.DataType') -> QtCore.QUrl: ...


class QQmlEngine(QJSEngine):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def markCurrentFunctionAsTranslationBinding(self) -> None: ...
    offlineStoragePathChanged: typing.ClassVar[QtCore.pyqtSignal]
    def clearSingletons(self) -> None: ...
    def urlInterceptors(self) -> list[QQmlAbstractUrlInterceptor]: ...
    def interceptUrl(self, url: QtCore.QUrl, type: QQmlAbstractUrlInterceptor.DataType) -> QtCore.QUrl: ...
    def removeUrlInterceptor(self, urlInterceptor: typing.Optional[QQmlAbstractUrlInterceptor]) -> None: ...
    def addUrlInterceptor(self, urlInterceptor: typing.Optional[QQmlAbstractUrlInterceptor]) -> None: ...
    @typing.overload
    def singletonInstance(self, qmlTypeId: int) -> QtCore.QObject: ...
    @typing.overload
    def singletonInstance(self, moduleName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], typeName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> QtCore.QObject: ...
    def offlineStorageDatabaseFilePath(self, databaseName: typing.Optional[str]) -> str: ...
    exit: typing.ClassVar[QtCore.pyqtSignal]
    warnings: typing.ClassVar[QtCore.pyqtSignal]
    quit: typing.ClassVar[QtCore.pyqtSignal]
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    def retranslate(self) -> None: ...
    @staticmethod
    def setContextForObject(a0: typing.Optional[QtCore.QObject], a1: typing.Optional['QQmlContext']) -> None: ...
    @staticmethod
    def contextForObject(a0: typing.Optional[QtCore.QObject]) -> typing.Optional['QQmlContext']: ...
    def setOutputWarningsToStandardError(self, a0: bool) -> None: ...
    def outputWarningsToStandardError(self) -> bool: ...
    def setBaseUrl(self, a0: QtCore.QUrl) -> None: ...
    def baseUrl(self) -> QtCore.QUrl: ...
    def offlineStoragePath(self) -> str: ...
    def setOfflineStoragePath(self, dir: typing.Optional[str]) -> None: ...
    def incubationController(self) -> typing.Optional['QQmlIncubationController']: ...
    def setIncubationController(self, a0: typing.Optional['QQmlIncubationController']) -> None: ...
    def removeImageProvider(self, id: typing.Optional[str]) -> None: ...
    def imageProvider(self, id: typing.Optional[str]) -> typing.Optional['QQmlImageProviderBase']: ...
    def addImageProvider(self, id: typing.Optional[str], a1: typing.Optional['QQmlImageProviderBase']) -> None: ...
    def networkAccessManager(self) -> typing.Optional[QtNetwork.QNetworkAccessManager]: ...
    def networkAccessManagerFactory(self) -> typing.Optional['QQmlNetworkAccessManagerFactory']: ...
    def setNetworkAccessManagerFactory(self, a0: typing.Optional['QQmlNetworkAccessManagerFactory']) -> None: ...
    def importPlugin(self, filePath: typing.Optional[str], uri: typing.Optional[str], errors: typing.Optional[collections.abc.Iterable['QQmlError']]) -> bool: ...
    def addPluginPath(self, dir: typing.Optional[str]) -> None: ...
    def setPluginPathList(self, paths: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def pluginPathList(self) -> list[str]: ...
    def addImportPath(self, dir: typing.Optional[str]) -> None: ...
    def setImportPathList(self, paths: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def importPathList(self) -> list[str]: ...
    def trimComponentCache(self) -> None: ...
    def clearComponentCache(self) -> None: ...
    def rootContext(self) -> typing.Optional['QQmlContext']: ...


class QQmlApplicationEngine(QQmlEngine):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, url: QtCore.QUrl, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, filePath: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, uri: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], typeName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    objectCreationFailed: typing.ClassVar[QtCore.pyqtSignal]
    objectCreated: typing.ClassVar[QtCore.pyqtSignal]
    def loadFromModule(self, uri: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], typeName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> None: ...
    def setInitialProperties(self, initialProperties: dict[typing.Optional[str], typing.Any]) -> None: ...
    def setExtraFileSelectors(self, extraFileSelectors: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def loadData(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], url: QtCore.QUrl = ...) -> None: ...
    @typing.overload
    def load(self, url: QtCore.QUrl) -> None: ...
    @typing.overload
    def load(self, filePath: typing.Optional[str]) -> None: ...
    def rootObjects(self) -> list[QtCore.QObject]: ...


class QQmlComponent(QtCore.QObject):

    class Status(enum.Enum):
        Null = ... # type: QQmlComponent.Status
        Ready = ... # type: QQmlComponent.Status
        Loading = ... # type: QQmlComponent.Status
        Error = ... # type: QQmlComponent.Status

    class CompilationMode(enum.Enum):
        PreferSynchronous = ... # type: QQmlComponent.CompilationMode
        Asynchronous = ... # type: QQmlComponent.CompilationMode

    @typing.overload
    def __init__(self, a0: typing.Optional[QQmlEngine], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QQmlEngine], fileName: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QQmlEngine], fileName: typing.Optional[str], mode: 'QQmlComponent.CompilationMode', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QQmlEngine], url: QtCore.QUrl, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QQmlEngine], url: QtCore.QUrl, mode: 'QQmlComponent.CompilationMode', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, engine: typing.Optional[QQmlEngine], uri: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], typeName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, engine: typing.Optional[QQmlEngine], uri: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], typeName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], mode: 'QQmlComponent.CompilationMode', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setInitialProperties(self, component: typing.Optional[QtCore.QObject], properties: dict[typing.Optional[str], typing.Any]) -> None: ...
    def engine(self) -> typing.Optional[QQmlEngine]: ...
    progressChanged: typing.ClassVar[QtCore.pyqtSignal]
    statusChanged: typing.ClassVar[QtCore.pyqtSignal]
    def loadFromModule(self, uri: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], typeName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], mode: 'QQmlComponent.CompilationMode' = ...) -> None: ...
    def setData(self, a0: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], baseUrl: QtCore.QUrl) -> None: ...
    @typing.overload
    def loadUrl(self, url: QtCore.QUrl) -> None: ...
    @typing.overload
    def loadUrl(self, url: QtCore.QUrl, mode: 'QQmlComponent.CompilationMode') -> None: ...
    def creationContext(self) -> typing.Optional['QQmlContext']: ...
    def completeCreate(self) -> None: ...
    def beginCreate(self, a0: typing.Optional['QQmlContext']) -> typing.Optional[QtCore.QObject]: ...
    def createWithInitialProperties(self, initialProperties: dict[typing.Optional[str], typing.Any], context: typing.Optional['QQmlContext'] = ...) -> typing.Optional[QtCore.QObject]: ...
    @typing.overload
    def create(self, context: typing.Optional['QQmlContext'] = ...) -> typing.Optional[QtCore.QObject]: ...
    @typing.overload
    def create(self, a0: 'QQmlIncubator', context: typing.Optional['QQmlContext'] = ..., forContext: typing.Optional['QQmlContext'] = ...) -> None: ...
    def url(self) -> QtCore.QUrl: ...
    def progress(self) -> float: ...
    def errors(self) -> list['QQmlError']: ...
    def isLoading(self) -> bool: ...
    def isError(self) -> bool: ...
    def isReady(self) -> bool: ...
    def isNull(self) -> bool: ...
    def isBound(self) -> bool: ...
    def status(self) -> 'QQmlComponent.Status': ...


class QQmlContext(QtCore.QObject):

    class PropertyPair(PyQt6.sip.simplewrapper):

        name = ... # type: typing.Optional[str]
        value = ... # type: typing.Any

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QQmlContext.PropertyPair') -> None: ...

    @typing.overload
    def __init__(self, engine: typing.Optional[QQmlEngine], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parentContext: typing.Optional['QQmlContext'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def objectForName(self, a0: typing.Optional[str]) -> typing.Optional[QtCore.QObject]: ...
    def setContextProperties(self, properties: collections.abc.Iterable['QQmlContext.PropertyPair']) -> None: ...
    def baseUrl(self) -> QtCore.QUrl: ...
    def setBaseUrl(self, a0: QtCore.QUrl) -> None: ...
    def resolvedUrl(self, a0: QtCore.QUrl) -> QtCore.QUrl: ...
    def nameForObject(self, a0: typing.Optional[QtCore.QObject]) -> str: ...
    @typing.overload
    def setContextProperty(self, a0: typing.Optional[str], a1: typing.Optional[QtCore.QObject]) -> None: ...
    @typing.overload
    def setContextProperty(self, a0: typing.Optional[str], a1: typing.Any) -> None: ...
    def contextProperty(self, a0: typing.Optional[str]) -> typing.Any: ...
    def setContextObject(self, a0: typing.Optional[QtCore.QObject]) -> None: ...
    def contextObject(self) -> typing.Optional[QtCore.QObject]: ...
    def parentContext(self) -> typing.Optional['QQmlContext']: ...
    def engine(self) -> typing.Optional[QQmlEngine]: ...
    def isValid(self) -> bool: ...


class QQmlImageProviderBase(QtCore.QObject):

    class Flag(enum.Flag):
        ForceAsynchronousImageLoading = ... # type: QQmlImageProviderBase.Flag

    class ImageType(enum.Enum):
        Image = ... # type: QQmlImageProviderBase.ImageType
        Pixmap = ... # type: QQmlImageProviderBase.ImageType
        Texture = ... # type: QQmlImageProviderBase.ImageType
        ImageResponse = ... # type: QQmlImageProviderBase.ImageType

    def flags(self) -> 'QQmlImageProviderBase.Flag': ...
    def imageType(self) -> 'QQmlImageProviderBase.ImageType': ...


class QQmlError(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQmlError') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def swap(self, other: 'QQmlError') -> None: ...
    def setMessageType(self, messageType: QtCore.QtMsgType) -> None: ...
    def messageType(self) -> QtCore.QtMsgType: ...
    def setObject(self, a0: typing.Optional[QtCore.QObject]) -> None: ...
    def object(self) -> typing.Optional[QtCore.QObject]: ...
    def toString(self) -> str: ...
    def setColumn(self, a0: int) -> None: ...
    def column(self) -> int: ...
    def setLine(self, a0: int) -> None: ...
    def line(self) -> int: ...
    def setDescription(self, a0: typing.Optional[str]) -> None: ...
    def description(self) -> str: ...
    def setUrl(self, a0: QtCore.QUrl) -> None: ...
    def url(self) -> QtCore.QUrl: ...
    def isValid(self) -> bool: ...


class QQmlExpression(QtCore.QObject):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QQmlContext], a1: typing.Optional[QtCore.QObject], a2: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQmlScriptString', context: typing.Optional[QQmlContext] = ..., scope: typing.Optional[QtCore.QObject] = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    valueChanged: typing.ClassVar[QtCore.pyqtSignal]
    def evaluate(self) -> typing.Tuple[typing.Any, typing.Optional[bool]]: ...
    def error(self) -> QQmlError: ...
    def clearError(self) -> None: ...
    def hasError(self) -> bool: ...
    def scopeObject(self) -> typing.Optional[QtCore.QObject]: ...
    def setSourceLocation(self, fileName: typing.Optional[str], line: int, column: int = ...) -> None: ...
    def columnNumber(self) -> int: ...
    def lineNumber(self) -> int: ...
    def sourceFile(self) -> str: ...
    def setNotifyOnValueChanged(self, a0: bool) -> None: ...
    def notifyOnValueChanged(self) -> bool: ...
    def setExpression(self, a0: typing.Optional[str]) -> None: ...
    def expression(self) -> str: ...
    def context(self) -> typing.Optional[QQmlContext]: ...
    def engine(self) -> typing.Optional[QQmlEngine]: ...


class QQmlExtensionPlugin(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def unregisterTypes(self) -> None: ...
    def baseUrl(self) -> QtCore.QUrl: ...
    def registerTypes(self, uri: typing.Optional[str]) -> None: ...


class QQmlEngineExtensionPlugin(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def initializeEngine(self, engine: typing.Optional[QQmlEngine], uri: typing.Optional[str]) -> None: ...


class QQmlFileSelector(QtCore.QObject):

    def __init__(self, engine: typing.Optional[QQmlEngine], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def selector(self) -> typing.Optional[QtCore.QFileSelector]: ...
    def setExtraSelectors(self, strings: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def setSelector(self, selector: typing.Optional[QtCore.QFileSelector]) -> None: ...


class QQmlIncubator(PyQt6.sip.simplewrapper):

    class Status(enum.Enum):
        Null = ... # type: QQmlIncubator.Status
        Ready = ... # type: QQmlIncubator.Status
        Loading = ... # type: QQmlIncubator.Status
        Error = ... # type: QQmlIncubator.Status

    class IncubationMode(enum.Enum):
        Asynchronous = ... # type: QQmlIncubator.IncubationMode
        AsynchronousIfNested = ... # type: QQmlIncubator.IncubationMode
        Synchronous = ... # type: QQmlIncubator.IncubationMode

    def __init__(self, mode: 'QQmlIncubator.IncubationMode' = ...) -> None: ...

    def setInitialState(self, a0: typing.Optional[QtCore.QObject]) -> None: ...
    def statusChanged(self, a0: 'QQmlIncubator.Status') -> None: ...
    def setInitialProperties(self, initialProperties: dict[typing.Optional[str], typing.Any]) -> None: ...
    def object(self) -> typing.Optional[QtCore.QObject]: ...
    def status(self) -> 'QQmlIncubator.Status': ...
    def incubationMode(self) -> 'QQmlIncubator.IncubationMode': ...
    def errors(self) -> list[QQmlError]: ...
    def isLoading(self) -> bool: ...
    def isError(self) -> bool: ...
    def isReady(self) -> bool: ...
    def isNull(self) -> bool: ...
    def forceCompletion(self) -> None: ...
    def clear(self) -> None: ...


class QQmlIncubationController(PyQt6.sip.simplewrapper):

    def __init__(self) -> None: ...

    def incubatingObjectCountChanged(self, a0: int) -> None: ...
    def incubateFor(self, msecs: int) -> None: ...
    def incubatingObjectCount(self) -> int: ...
    def engine(self) -> typing.Optional[QQmlEngine]: ...


class QQmlListReference(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QtCore.QObject], property: typing.Optional[str], engine: typing.Optional[QQmlEngine] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQmlListReference') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any, engine: typing.Optional[QQmlEngine] = ...) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def removeLast(self) -> bool: ...
    def replace(self, a0: int, a1: typing.Optional[QtCore.QObject]) -> bool: ...
    def canRemoveLast(self) -> bool: ...
    def canReplace(self) -> bool: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def clear(self) -> bool: ...
    def at(self, a0: int) -> typing.Optional[QtCore.QObject]: ...
    def append(self, a0: typing.Optional[QtCore.QObject]) -> bool: ...
    def isReadable(self) -> bool: ...
    def isManipulable(self) -> bool: ...
    def canCount(self) -> bool: ...
    def canClear(self) -> bool: ...
    def canAt(self) -> bool: ...
    def canAppend(self) -> bool: ...
    def listElementType(self) -> typing.Optional[QtCore.QMetaObject]: ...
    def object(self) -> typing.Optional[QtCore.QObject]: ...
    def isValid(self) -> bool: ...


class QQmlNetworkAccessManagerFactory(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQmlNetworkAccessManagerFactory') -> None: ...

    def create(self, parent: typing.Optional[QtCore.QObject]) -> typing.Optional[QtNetwork.QNetworkAccessManager]: ...


class QQmlParserStatus(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQmlParserStatus') -> None: ...

    def componentComplete(self) -> None: ...
    def classBegin(self) -> None: ...


class QQmlProperty(PyQt6.sip.simplewrapper):

    class Type(enum.Enum):
        Invalid = ... # type: QQmlProperty.Type
        Property = ... # type: QQmlProperty.Type
        SignalProperty = ... # type: QQmlProperty.Type

    class PropertyTypeCategory(enum.Enum):
        InvalidCategory = ... # type: QQmlProperty.PropertyTypeCategory
        List = ... # type: QQmlProperty.PropertyTypeCategory
        Object = ... # type: QQmlProperty.PropertyTypeCategory
        Normal = ... # type: QQmlProperty.PropertyTypeCategory

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QtCore.QObject]) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QtCore.QObject], a1: typing.Optional[QQmlContext]) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QtCore.QObject], a1: typing.Optional[QQmlEngine]) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QtCore.QObject], a1: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QtCore.QObject], a1: typing.Optional[str], a2: typing.Optional[QQmlContext]) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QtCore.QObject], a1: typing.Optional[str], a2: typing.Optional[QQmlEngine]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQmlProperty') -> None: ...

    def __ne__(self, other: object): ...
    def swap(self, other: 'QQmlProperty') -> None: ...
    def method(self) -> QtCore.QMetaMethod: ...
    def property(self) -> QtCore.QMetaProperty: ...
    def index(self) -> int: ...
    def object(self) -> typing.Optional[QtCore.QObject]: ...
    def isResettable(self) -> bool: ...
    def isDesignable(self) -> bool: ...
    def isWritable(self) -> bool: ...
    @typing.overload
    def connectNotifySignal(self, slot: PYQT_SLOT) -> bool: ...
    @typing.overload
    def connectNotifySignal(self, dest: typing.Optional[QtCore.QObject], method: int) -> bool: ...
    def needsNotifySignal(self) -> bool: ...
    def hasNotifySignal(self) -> bool: ...
    def reset(self) -> bool: ...
    @typing.overload
    def write(self, a0: typing.Any) -> bool: ...
    @typing.overload
    @staticmethod
    def write(a0: typing.Optional[QtCore.QObject], a1: typing.Optional[str], a2: typing.Any) -> bool: ...
    @typing.overload
    @staticmethod
    def write(a0: typing.Optional[QtCore.QObject], a1: typing.Optional[str], a2: typing.Any, a3: typing.Optional[QQmlContext]) -> bool: ...
    @typing.overload
    @staticmethod
    def write(a0: typing.Optional[QtCore.QObject], a1: typing.Optional[str], a2: typing.Any, a3: typing.Optional[QQmlEngine]) -> bool: ...
    @typing.overload
    def read(self) -> typing.Any: ...
    @typing.overload
    @staticmethod
    def read(a0: typing.Optional[QtCore.QObject], a1: typing.Optional[str]) -> typing.Any: ...
    @typing.overload
    @staticmethod
    def read(a0: typing.Optional[QtCore.QObject], a1: typing.Optional[str], a2: typing.Optional[QQmlContext]) -> typing.Any: ...
    @typing.overload
    @staticmethod
    def read(a0: typing.Optional[QtCore.QObject], a1: typing.Optional[str], a2: typing.Optional[QQmlEngine]) -> typing.Any: ...
    def name(self) -> str: ...
    def propertyMetaType(self) -> QtCore.QMetaType: ...
    def propertyTypeName(self) -> typing.Optional[str]: ...
    def propertyTypeCategory(self) -> 'QQmlProperty.PropertyTypeCategory': ...
    def propertyType(self) -> int: ...
    def isBindable(self) -> bool: ...
    def isSignalProperty(self) -> bool: ...
    def isProperty(self) -> bool: ...
    def isValid(self) -> bool: ...
    def type(self) -> 'QQmlProperty.Type': ...
    def __eq__(self, other: object): ...
    def __hash__(self) -> int: ...


class QQmlPropertyMap(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def updateValue(self, key: typing.Optional[str], input: typing.Any) -> typing.Any: ...
    valueChanged: typing.ClassVar[QtCore.pyqtSignal]
    def __getitem__(self, key: typing.Optional[str]) -> typing.Any: ...
    def contains(self, key: typing.Optional[str]) -> bool: ...
    def isEmpty(self) -> bool: ...
    def __len__(self) -> int: ...
    def size(self) -> int: ...
    def count(self) -> int: ...
    def keys(self) -> list[str]: ...
    def clear(self, key: typing.Optional[str]) -> None: ...
    def freeze(self) -> None: ...
    @typing.overload
    def insert(self, values: dict[typing.Optional[str], typing.Any]) -> None: ...
    @typing.overload
    def insert(self, key: typing.Optional[str], value: typing.Any) -> None: ...
    def value(self, key: typing.Optional[str]) -> typing.Any: ...


class QQmlPropertyValueSource(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQmlPropertyValueSource') -> None: ...

    def setTarget(self, a0: QQmlProperty) -> None: ...


class QQmlScriptString(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQmlScriptString') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def booleanLiteral(self) -> typing.Tuple[bool, typing.Optional[bool]]: ...
    def numberLiteral(self) -> typing.Tuple[float, typing.Optional[bool]]: ...
    def stringLiteral(self) -> str: ...
    def isNullLiteral(self) -> bool: ...
    def isUndefinedLiteral(self) -> bool: ...
    def isEmpty(self) -> bool: ...


def qmlRegisterUncreatableType(a0: type, uri: typing.Optional[str], major: int, minor: int, reason: typing.Optional[str], qmlName: typing.Optional[str] = ...) -> int: ...
def qmlRegisterAnonymousType(a0: type, uri: typing.Optional[str], major: int) -> int: ...
def qmlRegisterSingletonInstance(uri: typing.Optional[str], major: int, minor: int, typeName: typing.Optional[str], cppObject: typing.Optional[QtCore.QObject]) -> int: ...
def qmlRegisterRevision(a0: type, uri: typing.Optional[str], major: int, minor: int, attachedProperties: type = ...) -> int: ...
def qmlAttachedPropertiesObject(a0: type, object: typing.Optional[QtCore.QObject], create: bool = ...) -> typing.Optional[QtCore.QObject]: ...
def qjsEngine(a0: typing.Optional[QtCore.QObject]) -> typing.Optional[QJSEngine]: ...
def qmlEngine(a0: typing.Optional[QtCore.QObject]) -> typing.Optional[QQmlEngine]: ...
def qmlContext(a0: typing.Optional[QtCore.QObject]) -> typing.Optional[QQmlContext]: ...
def qmlTypeId(uri: typing.Optional[str], versionMajor: int, versionMinor: int, qmlName: typing.Optional[str]) -> int: ...
@typing.overload
def qmlRegisterType(url: QtCore.QUrl, uri: typing.Optional[str], versionMajor: int, versionMinor: int, qmlName: typing.Optional[str]) -> int: ...
@typing.overload
def qmlRegisterType(a0: type, uri: typing.Optional[str], major: int, minor: int, name: typing.Optional[str] = ..., attachedProperties: type = ...) -> int: ...
@typing.overload
def qmlRegisterSingletonType(url: QtCore.QUrl, uri: typing.Optional[str], versionMajor: int, versionMinor: int, qmlName: typing.Optional[str]) -> int: ...
@typing.overload
def qmlRegisterSingletonType(a0: type, uri: typing.Optional[str], major: int, minor: int, factory: collections.abc.Callable[[QQmlEngine, QJSEngine], typing.Any], name: typing.Optional[str] = ...) -> int: ...
def qmlRegisterModule(uri: typing.Optional[str], versionMajor: int, versionMinor: int) -> None: ...
def qmlProtectModule(uri: typing.Optional[str], majVersion: int) -> bool: ...
def qmlRegisterUncreatableMetaObject(staticMetaObject: QtCore.QMetaObject, uri: typing.Optional[str], versionMajor: int, versionMinor: int, qmlName: typing.Optional[str], reason: typing.Optional[str]) -> int: ...
def qmlRegisterTypeNotAvailable(uri: typing.Optional[str], versionMajor: int, versionMinor: int, qmlName: typing.Optional[str], message: typing.Optional[str]) -> int: ...
def qmlClearTypeRegistrations() -> None: ...
