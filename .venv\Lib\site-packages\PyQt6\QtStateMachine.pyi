# The PEP 484 type hints stub file for the QtStateMachine module.
#
# Generated by SIP 6.10.0
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICENSE included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import collections, re, typing, enum

try:
    from warnings import deprecated
except ImportError:
    pass

import PyQt6.sip

from PyQt6 import QtCore
from PyQt6 import QtGui

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[collections.abc.Callable[..., Any], QtCore.pyqtBoundSignal]


class QAbstractState(QtCore.QObject):

    def __init__(self, parent: typing.Optional['QState'] = ...) -> None: ...

    def event(self, e: typing.Optional[QtCore.QEvent]) -> bool: ...
    def onExit(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    def onEntry(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    exited: typing.ClassVar[QtCore.pyqtSignal]
    entered: typing.ClassVar[QtCore.pyqtSignal]
    activeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def active(self) -> bool: ...
    def machine(self) -> typing.Optional['QStateMachine']: ...
    def parentState(self) -> typing.Optional['QState']: ...


class QAbstractTransition(QtCore.QObject):

    class TransitionType(enum.Enum):
        ExternalTransition = ... # type: QAbstractTransition.TransitionType
        InternalTransition = ... # type: QAbstractTransition.TransitionType

    def __init__(self, sourceState: typing.Optional['QState'] = ...) -> None: ...

    def event(self, e: typing.Optional[QtCore.QEvent]) -> bool: ...
    def onTransition(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    def eventTest(self, event: typing.Optional[QtCore.QEvent]) -> bool: ...
    targetStatesChanged: typing.ClassVar[QtCore.pyqtSignal]
    targetStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    triggered: typing.ClassVar[QtCore.pyqtSignal]
    def animations(self) -> list[QtCore.QAbstractAnimation]: ...
    def removeAnimation(self, animation: typing.Optional[QtCore.QAbstractAnimation]) -> None: ...
    def addAnimation(self, animation: typing.Optional[QtCore.QAbstractAnimation]) -> None: ...
    def machine(self) -> typing.Optional['QStateMachine']: ...
    def setTransitionType(self, type: 'QAbstractTransition.TransitionType') -> None: ...
    def transitionType(self) -> 'QAbstractTransition.TransitionType': ...
    def setTargetStates(self, targets: collections.abc.Iterable[QAbstractState]) -> None: ...
    def targetStates(self) -> list[QAbstractState]: ...
    def setTargetState(self, target: typing.Optional[QAbstractState]) -> None: ...
    def targetState(self) -> typing.Optional[QAbstractState]: ...
    def sourceState(self) -> typing.Optional['QState']: ...


class QEventTransition(QAbstractTransition):

    @typing.overload
    def __init__(self, sourceState: typing.Optional['QState'] = ...) -> None: ...
    @typing.overload
    def __init__(self, object: typing.Optional[QtCore.QObject], type: QtCore.QEvent.Type, sourceState: typing.Optional['QState'] = ...) -> None: ...

    def event(self, e: typing.Optional[QtCore.QEvent]) -> bool: ...
    def onTransition(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    def eventTest(self, event: typing.Optional[QtCore.QEvent]) -> bool: ...
    def setEventType(self, type: QtCore.QEvent.Type) -> None: ...
    def eventType(self) -> QtCore.QEvent.Type: ...
    def setEventSource(self, object: typing.Optional[QtCore.QObject]) -> None: ...
    def eventSource(self) -> typing.Optional[QtCore.QObject]: ...


class QFinalState(QAbstractState):

    def __init__(self, parent: typing.Optional['QState'] = ...) -> None: ...

    def event(self, e: typing.Optional[QtCore.QEvent]) -> bool: ...
    def onExit(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    def onEntry(self, event: typing.Optional[QtCore.QEvent]) -> None: ...


class QHistoryState(QAbstractState):

    class HistoryType(enum.Enum):
        ShallowHistory = ... # type: QHistoryState.HistoryType
        DeepHistory = ... # type: QHistoryState.HistoryType

    @typing.overload
    def __init__(self, parent: typing.Optional['QState'] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: 'QHistoryState.HistoryType', parent: typing.Optional['QState'] = ...) -> None: ...

    def event(self, e: typing.Optional[QtCore.QEvent]) -> bool: ...
    def onExit(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    def onEntry(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    historyTypeChanged: typing.ClassVar[QtCore.pyqtSignal]
    defaultStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    defaultTransitionChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setHistoryType(self, type: 'QHistoryState.HistoryType') -> None: ...
    def historyType(self) -> 'QHistoryState.HistoryType': ...
    def setDefaultState(self, state: typing.Optional[QAbstractState]) -> None: ...
    def defaultState(self) -> typing.Optional[QAbstractState]: ...
    def setDefaultTransition(self, transition: typing.Optional[QAbstractTransition]) -> None: ...
    def defaultTransition(self) -> typing.Optional[QAbstractTransition]: ...


class QKeyEventTransition(QEventTransition):

    @typing.overload
    def __init__(self, sourceState: typing.Optional['QState'] = ...) -> None: ...
    @typing.overload
    def __init__(self, object: typing.Optional[QtCore.QObject], type: QtCore.QEvent.Type, key: int, sourceState: typing.Optional['QState'] = ...) -> None: ...

    def eventTest(self, event: typing.Optional[QtCore.QEvent]) -> bool: ...
    def onTransition(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    def setModifierMask(self, modifiers: QtCore.Qt.KeyboardModifier) -> None: ...
    def modifierMask(self) -> QtCore.Qt.KeyboardModifier: ...
    def setKey(self, key: int) -> None: ...
    def key(self) -> int: ...


class QMouseEventTransition(QEventTransition):

    @typing.overload
    def __init__(self, sourceState: typing.Optional['QState'] = ...) -> None: ...
    @typing.overload
    def __init__(self, object: typing.Optional[QtCore.QObject], type: QtCore.QEvent.Type, button: QtCore.Qt.MouseButton, sourceState: typing.Optional['QState'] = ...) -> None: ...

    def eventTest(self, event: typing.Optional[QtCore.QEvent]) -> bool: ...
    def onTransition(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    def setHitTestPath(self, path: QtGui.QPainterPath) -> None: ...
    def hitTestPath(self) -> QtGui.QPainterPath: ...
    def setModifierMask(self, modifiers: QtCore.Qt.KeyboardModifier) -> None: ...
    def modifierMask(self) -> QtCore.Qt.KeyboardModifier: ...
    def setButton(self, button: QtCore.Qt.MouseButton) -> None: ...
    def button(self) -> QtCore.Qt.MouseButton: ...


class QSignalTransition(QAbstractTransition):

    @typing.overload
    def __init__(self, sourceState: typing.Optional['QState'] = ...) -> None: ...
    @typing.overload
    def __init__(self, signal: pyqtBoundSignal, sourceState: typing.Optional['QState'] = ...) -> None: ...

    signalChanged: typing.ClassVar[QtCore.pyqtSignal]
    senderObjectChanged: typing.ClassVar[QtCore.pyqtSignal]
    def event(self, e: typing.Optional[QtCore.QEvent]) -> bool: ...
    def onTransition(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    def eventTest(self, event: typing.Optional[QtCore.QEvent]) -> bool: ...
    def setSignal(self, signal: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def signal(self) -> QtCore.QByteArray: ...
    def setSenderObject(self, sender: typing.Optional[QtCore.QObject]) -> None: ...
    def senderObject(self) -> typing.Optional[QtCore.QObject]: ...


class QState(QAbstractState):

    class RestorePolicy(enum.Enum):
        DontRestoreProperties = ... # type: QState.RestorePolicy
        RestoreProperties = ... # type: QState.RestorePolicy

    class ChildMode(enum.Enum):
        ExclusiveStates = ... # type: QState.ChildMode
        ParallelStates = ... # type: QState.ChildMode

    @typing.overload
    def __init__(self, parent: typing.Optional['QState'] = ...) -> None: ...
    @typing.overload
    def __init__(self, childMode: 'QState.ChildMode', parent: typing.Optional['QState'] = ...) -> None: ...

    def event(self, e: typing.Optional[QtCore.QEvent]) -> bool: ...
    def onExit(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    def onEntry(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    errorStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    initialStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    childModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    propertiesAssigned: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def assignProperty(self, object: typing.Optional[QtCore.QObject], name: typing.Optional[str], value: typing.Any) -> None: ...
    def setChildMode(self, mode: 'QState.ChildMode') -> None: ...
    def childMode(self) -> 'QState.ChildMode': ...
    def setInitialState(self, state: typing.Optional[QAbstractState]) -> None: ...
    def initialState(self) -> typing.Optional[QAbstractState]: ...
    def transitions(self) -> list[QAbstractTransition]: ...
    def removeTransition(self, transition: typing.Optional[QAbstractTransition]) -> None: ...
    @typing.overload
    def addTransition(self, transition: typing.Optional[QAbstractTransition]) -> None: ...
    @typing.overload
    def addTransition(self, signal: pyqtBoundSignal, target: typing.Optional[QAbstractState]) -> typing.Optional[QSignalTransition]: ...
    @typing.overload
    def addTransition(self, target: typing.Optional[QAbstractState]) -> typing.Optional[QAbstractTransition]: ...
    def setErrorState(self, state: typing.Optional[QAbstractState]) -> None: ...
    def errorState(self) -> typing.Optional[QAbstractState]: ...


class QStateMachine(QState):

    class Error(enum.Enum):
        NoError = ... # type: QStateMachine.Error
        NoInitialStateError = ... # type: QStateMachine.Error
        NoDefaultStateInHistoryStateError = ... # type: QStateMachine.Error
        NoCommonAncestorForTransitionError = ... # type: QStateMachine.Error
        StateMachineChildModeSetToParallelError = ... # type: QStateMachine.Error

    class EventPriority(enum.Enum):
        NormalPriority = ... # type: QStateMachine.EventPriority
        HighPriority = ... # type: QStateMachine.EventPriority

    class SignalEvent(QtCore.QEvent):

        def arguments(self) -> list[typing.Any]: ...
        def signalIndex(self) -> int: ...
        def sender(self) -> typing.Optional[QtCore.QObject]: ...

    class WrappedEvent(QtCore.QEvent):

        def event(self) -> typing.Optional[QtCore.QEvent]: ...
        def object(self) -> typing.Optional[QtCore.QObject]: ...

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, childMode: QState.ChildMode, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def event(self, e: typing.Optional[QtCore.QEvent]) -> bool: ...
    def onExit(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    def onEntry(self, event: typing.Optional[QtCore.QEvent]) -> None: ...
    runningChanged: typing.ClassVar[QtCore.pyqtSignal]
    stopped: typing.ClassVar[QtCore.pyqtSignal]
    started: typing.ClassVar[QtCore.pyqtSignal]
    def setRunning(self, running: bool) -> None: ...
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def eventFilter(self, watched: typing.Optional[QtCore.QObject], event: typing.Optional[QtCore.QEvent]) -> bool: ...
    def configuration(self) -> set[QAbstractState]: ...
    def cancelDelayedEvent(self, id: int) -> bool: ...
    def postDelayedEvent(self, event: typing.Optional[QtCore.QEvent], delay: int) -> int: ...
    def postEvent(self, event: typing.Optional[QtCore.QEvent], priority: 'QStateMachine.EventPriority' = ...) -> None: ...
    def setGlobalRestorePolicy(self, restorePolicy: QState.RestorePolicy) -> None: ...
    def globalRestorePolicy(self) -> QState.RestorePolicy: ...
    def removeDefaultAnimation(self, animation: typing.Optional[QtCore.QAbstractAnimation]) -> None: ...
    def defaultAnimations(self) -> list[QtCore.QAbstractAnimation]: ...
    def addDefaultAnimation(self, animation: typing.Optional[QtCore.QAbstractAnimation]) -> None: ...
    def setAnimated(self, enabled: bool) -> None: ...
    def isAnimated(self) -> bool: ...
    def isRunning(self) -> bool: ...
    def clearError(self) -> None: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QStateMachine.Error': ...
    def removeState(self, state: typing.Optional[QAbstractState]) -> None: ...
    def addState(self, state: typing.Optional[QAbstractState]) -> None: ...
