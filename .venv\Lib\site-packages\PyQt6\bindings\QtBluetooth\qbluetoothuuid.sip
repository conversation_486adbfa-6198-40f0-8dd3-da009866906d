// qbluetoothuuid.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QBluetoothUuid : public QUuid
{
%TypeHeaderCode
#include <qbluetoothuuid.h>
%End

public:
    enum class ProtocolUuid
    {
        Sdp,
        Udp,
        Rfcomm,
        Tcp,
        TcsBin,
        TcsAt,
        Att,
        Obex,
        Ip,
        Ftp,
        Http,
        Wsp,
        Bnep,
        Upnp,
        Hidp,
        HardcopyControlChannel,
        HardcopyDataChannel,
        HardcopyNotification,
        Avctp,
        Avdtp,
        Cmtp,
        UdiCPlain,
        McapControlChannel,
        McapDataChannel,
        L2cap,
    };

    enum class ServiceClassUuid
    {
        ServiceDiscoveryServer,
        BrowseGroupDescriptor,
        PublicBrowseGroup,
        SerialPort,
        LANAccessUsingPPP,
        DialupNetworking,
        IrMCSync,
        ObexObjectPush,
        OBEXFileTransfer,
        IrMCSyncCommand,
        Headset,
        AudioSource,
        AudioSink,
        AV_RemoteControlTarget,
        AdvancedAudioDistribution,
        AV_RemoteControl,
        AV_RemoteControlController,
        HeadsetAG,
        PANU,
        NAP,
        GN,
        DirectPrinting,
        ReferencePrinting,
        BasicImage,
        ImagingResponder,
        ImagingAutomaticArchive,
        ImagingReferenceObjects,
        Handsfree,
        HandsfreeAudioGateway,
        DirectPrintingReferenceObjectsService,
        ReflectedUI,
        BasicPrinting,
        PrintingStatus,
        HumanInterfaceDeviceService,
        HardcopyCableReplacement,
        HCRPrint,
        HCRScan,
        SIMAccess,
        PhonebookAccessPCE,
        PhonebookAccessPSE,
        PhonebookAccess,
        HeadsetHS,
        MessageAccessServer,
        MessageNotificationServer,
        MessageAccessProfile,
        GNSS,
        GNSSServer,
        Display3D,
        Glasses3D,
        Synchronization3D,
        MPSProfile,
        MPSService,
        PnPInformation,
        GenericNetworking,
        GenericFileTransfer,
        GenericAudio,
        GenericTelephony,
        VideoSource,
        VideoSink,
        VideoDistribution,
        HDP,
        HDPSource,
        HDPSink,
        GenericAccess,
        GenericAttribute,
        ImmediateAlert,
        LinkLoss,
        TxPower,
        CurrentTimeService,
        ReferenceTimeUpdateService,
        NextDSTChangeService,
        Glucose,
        HealthThermometer,
        DeviceInformation,
        HeartRate,
        PhoneAlertStatusService,
        BatteryService,
        BloodPressure,
        AlertNotificationService,
        HumanInterfaceDevice,
        ScanParameters,
        RunningSpeedAndCadence,
        CyclingSpeedAndCadence,
        CyclingPower,
        LocationAndNavigation,
        EnvironmentalSensing,
        BodyComposition,
        UserData,
        WeightScale,
        BondManagement,
        ContinuousGlucoseMonitoring,
    };

    enum class CharacteristicType
    {
        DeviceName,
        Appearance,
        PeripheralPrivacyFlag,
        ReconnectionAddress,
        PeripheralPreferredConnectionParameters,
        ServiceChanged,
        AlertLevel,
        TxPowerLevel,
        DateTime,
        DayOfWeek,
        DayDateTime,
        ExactTime256,
        DSTOffset,
        TimeZone,
        LocalTimeInformation,
        TimeWithDST,
        TimeAccuracy,
        TimeSource,
        ReferenceTimeInformation,
        TimeUpdateControlPoint,
        TimeUpdateState,
        GlucoseMeasurement,
        BatteryLevel,
        TemperatureMeasurement,
        TemperatureType,
        IntermediateTemperature,
        MeasurementInterval,
        BootKeyboardInputReport,
        SystemID,
        ModelNumberString,
        SerialNumberString,
        FirmwareRevisionString,
        HardwareRevisionString,
        SoftwareRevisionString,
        ManufacturerNameString,
        IEEE1107320601RegulatoryCertificationDataList,
        CurrentTime,
        MagneticDeclination,
        ScanRefresh,
        BootKeyboardOutputReport,
        BootMouseInputReport,
        GlucoseMeasurementContext,
        BloodPressureMeasurement,
        IntermediateCuffPressure,
        HeartRateMeasurement,
        BodySensorLocation,
        HeartRateControlPoint,
        AlertStatus,
        RingerControlPoint,
        RingerSetting,
        AlertCategoryIDBitMask,
        AlertCategoryID,
        AlertNotificationControlPoint,
        UnreadAlertStatus,
        NewAlert,
        SupportedNewAlertCategory,
        SupportedUnreadAlertCategory,
        BloodPressureFeature,
        HIDInformation,
        ReportMap,
        HIDControlPoint,
        Report,
        ProtocolMode,
        ScanIntervalWindow,
        PnPID,
        GlucoseFeature,
        RecordAccessControlPoint,
        RSCMeasurement,
        RSCFeature,
        SCControlPoint,
        CSCMeasurement,
        CSCFeature,
        SensorLocation,
        CyclingPowerMeasurement,
        CyclingPowerVector,
        CyclingPowerFeature,
        CyclingPowerControlPoint,
        LocationAndSpeed,
        Navigation,
        PositionQuality,
        LNFeature,
        LNControlPoint,
        Elevation,
        Pressure,
        Temperature,
        Humidity,
        TrueWindSpeed,
        TrueWindDirection,
        ApparentWindSpeed,
        ApparentWindDirection,
        GustFactor,
        PollenConcentration,
        UVIndex,
        Irradiance,
        Rainfall,
        WindChill,
        HeatIndex,
        DewPoint,
        DescriptorValueChanged,
        AerobicHeartRateLowerLimit,
        AerobicThreshold,
        Age,
        AnaerobicHeartRateLowerLimit,
        AnaerobicHeartRateUpperLimit,
        AnaerobicThreshold,
        AerobicHeartRateUpperLimit,
        DateOfBirth,
        DateOfThresholdAssessment,
        EmailAddress,
        FatBurnHeartRateLowerLimit,
        FatBurnHeartRateUpperLimit,
        FirstName,
        FiveZoneHeartRateLimits,
        Gender,
        HeartRateMax,
        Height,
        HipCircumference,
        LastName,
        MaximumRecommendedHeartRate,
        RestingHeartRate,
        SportTypeForAerobicAnaerobicThresholds,
        ThreeZoneHeartRateLimits,
        TwoZoneHeartRateLimits,
        VO2Max,
        WaistCircumference,
        Weight,
        DatabaseChangeIncrement,
        UserIndex,
        BodyCompositionFeature,
        BodyCompositionMeasurement,
        WeightMeasurement,
        WeightScaleFeature,
        UserControlPoint,
        MagneticFluxDensity2D,
        MagneticFluxDensity3D,
        Language,
        BarometricPressureTrend,
    };

    enum class DescriptorType
    {
        UnknownDescriptorType,
        CharacteristicExtendedProperties,
        CharacteristicUserDescription,
        ClientCharacteristicConfiguration,
        ServerCharacteristicConfiguration,
        CharacteristicPresentationFormat,
        CharacteristicAggregateFormat,
        ValidRange,
        ExternalReportReference,
        ReportReference,
        EnvironmentalSensingConfiguration,
        EnvironmentalSensingMeasurement,
        EnvironmentalSensingTriggerSetting,
    };

    QBluetoothUuid();
    QBluetoothUuid(QBluetoothUuid::ProtocolUuid uuid);
    QBluetoothUuid(QBluetoothUuid::ServiceClassUuid uuid);
    QBluetoothUuid(QBluetoothUuid::CharacteristicType uuid);
    QBluetoothUuid(QBluetoothUuid::DescriptorType uuid);
    explicit QBluetoothUuid(quint32 uuid);
%If (Qt_6_6_0 -)
    QBluetoothUuid(quint128 uuid, QSysInfo::Endian order = QSysInfo::BigEndian);
%End
%If (- Qt_6_6_0)
    explicit QBluetoothUuid(quint128 uuid);
%End
    explicit QBluetoothUuid(const QString &uuid);
    QBluetoothUuid(const QBluetoothUuid &uuid);
    QBluetoothUuid(const QUuid &uuid);
    ~QBluetoothUuid();
    int minimumSize() const;
    quint16 toUInt16(bool *ok = 0) const;
    quint32 toUInt32(bool *ok = 0) const;
    quint128 toUInt128() const;
    static QString serviceClassToString(QBluetoothUuid::ServiceClassUuid uuid);
    static QString protocolToString(QBluetoothUuid::ProtocolUuid uuid);
    static QString characteristicToString(QBluetoothUuid::CharacteristicType uuid);
    static QString descriptorToString(QBluetoothUuid::DescriptorType uuid);
%If (Qt_6_7_0 -)
    Py_hash_t __hash__() const;
%MethodCode
        // The tp_hash slot (from QUuid) should be inherited.  Is this a SIP bug?
        sipRes = qHash(*sipCpp);
%End

%End
};

%End
%If (Qt_6_2_0 -)
QDataStream &operator<<(QDataStream &s, const QBluetoothUuid &uuid) /ReleaseGIL/;
%End
%If (Qt_6_2_0 -)
QDataStream &operator>>(QDataStream &s, QBluetoothUuid &uuid /Constrained/) /ReleaseGIL/;
%End
%If (Qt_6_2_0 -)
bool operator==(const QBluetoothUuid &a, const QBluetoothUuid &b);
%End
%If (Qt_6_2_0 -)
bool operator!=(const QBluetoothUuid &a, const QBluetoothUuid &b);
%End
