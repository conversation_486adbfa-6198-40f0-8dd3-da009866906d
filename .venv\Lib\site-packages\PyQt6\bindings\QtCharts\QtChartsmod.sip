// QtChartsmod.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtCharts, keyword_arguments="Optional", use_limited_api=True)

%Import QtGui/QtGuimod.sip
%Import QtWidgets/QtWidgetsmod.sip

%Timeline {QtCharts_6_0_0 QtCharts_6_1_0 QtCharts_6_2_0 QtCharts_6_3_0 QtCharts_6_4_0 QtCharts_6_5_0 QtCharts_6_6_0 QtCharts_6_7_0 QtCharts_6_8_0 QtCharts_6_9_0}

%Copying
Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6-Charts.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype PyQt6.sip.simplewrapper

int PYQT_CHART_VERSION;
const char *PYQT_CHART_VERSION_STR;

%ModuleCode
static int PYQT_CHART_VERSION = 0x060900;
static const char *PYQT_CHART_VERSION_STR = "6.9.0";
%End

%Include qabstractaxis.sip
%Include qabstractbarseries.sip
%Include qabstractseries.sip
%Include qarealegendmarker.sip
%Include qareaseries.sip
%Include qbarcategoryaxis.sip
%Include qbarlegendmarker.sip
%Include qbarseries.sip
%Include qbarset.sip
%Include qboxplotlegendmarker.sip
%Include qboxplotseries.sip
%Include qboxset.sip
%Include qcandlesticklegendmarker.sip
%Include qcandlestickmodelmapper.sip
%Include qcandlestickseries.sip
%Include qcandlestickset.sip
%Include qcategoryaxis.sip
%Include qchart.sip
%Include qchartview.sip
%Include qcoloraxis.sip
%Include qdatetimeaxis.sip
%Include qhbarmodelmapper.sip
%Include qhboxplotmodelmapper.sip
%Include qhcandlestickmodelmapper.sip
%Include qhorizontalbarseries.sip
%Include qhorizontalpercentbarseries.sip
%Include qhorizontalstackedbarseries.sip
%Include qhpiemodelmapper.sip
%Include qhxymodelmapper.sip
%Include qlegend.sip
%Include qlegendmarker.sip
%Include qlineseries.sip
%Include qlogvalueaxis.sip
%Include qpercentbarseries.sip
%Include qpielegendmarker.sip
%Include qpieseries.sip
%Include qpieslice.sip
%Include qpolarchart.sip
%Include qscatterseries.sip
%Include qsplineseries.sip
%Include qstackedbarseries.sip
%Include qvalueaxis.sip
%Include qvbarmodelmapper.sip
%Include qvboxplotmodelmapper.sip
%Include qvcandlestickmodelmapper.sip
%Include qvpiemodelmapper.sip
%Include qvxymodelmapper.sip
%Include qxylegendmarker.sip
%Include qxyseries.sip
%Include qhash.sip
