// qabstractaxis.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractAxis : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qabstractaxis.h>
%End

public:
    enum AxisType /BaseType=Flag/
    {
        AxisTypeNoAxis,
        AxisTypeValue,
        AxisTypeBarCategory,
        AxisTypeCategory,
        AxisTypeDateTime,
        AxisTypeLogValue,
%If (QtCharts_6_2_0 -)
        AxisTypeColor,
%End
    };

    typedef QFlags<QAbstractAxis::AxisType> AxisTypes;
    virtual ~QAbstractAxis();
    virtual QAbstractAxis::AxisType type() const = 0;
    bool isVisible() const;
    void setVisible(bool visible = true);
    bool isLineVisible() const;
    void setLineVisible(bool visible = true);
    void setLinePen(const QPen &pen);
    QPen linePen() const;
    void setLinePenColor(QColor color);
    QColor linePenColor() const;
    bool isGridLineVisible() const;
    void setGridLineVisible(bool visible = true);
    void setGridLinePen(const QPen &pen);
    QPen gridLinePen() const;
    bool labelsVisible() const;
    void setLabelsVisible(bool visible = true);
    void setLabelsBrush(const QBrush &brush);
    QBrush labelsBrush() const;
    void setLabelsFont(const QFont &font);
    QFont labelsFont() const;
    void setLabelsAngle(int angle);
    int labelsAngle() const;
    void setLabelsColor(QColor color);
    QColor labelsColor() const;
    bool shadesVisible() const;
    void setShadesVisible(bool visible = true);
    void setShadesPen(const QPen &pen);
    QPen shadesPen() const;
    void setShadesBrush(const QBrush &brush);
    QBrush shadesBrush() const;
    void setShadesColor(QColor color);
    QColor shadesColor() const;
    void setShadesBorderColor(QColor color);
    QColor shadesBorderColor() const;
    void setMin(const QVariant &min);
    void setMax(const QVariant &max);
    void setRange(const QVariant &min, const QVariant &max);
    void show();
    void hide();
    Qt::Orientation orientation() const;

signals:
    void visibleChanged(bool visible);
    void lineVisibleChanged(bool visible);
    void labelsVisibleChanged(bool visible);
    void gridVisibleChanged(bool visible);
    void colorChanged(QColor color);
    void labelsColorChanged(QColor color);
    void shadesVisibleChanged(bool visible);
    void shadesColorChanged(QColor color);
    void shadesBorderColorChanged(QColor color);

public:
    bool isTitleVisible() const;
    void setTitleVisible(bool visible = true);
    void setTitleBrush(const QBrush &brush);
    QBrush titleBrush() const;
    void setTitleFont(const QFont &font);
    QFont titleFont() const;
    void setTitleText(const QString &title);
    QString titleText() const;
    Qt::Alignment alignment() const;

signals:
    void linePenChanged(const QPen &pen);
    void labelsBrushChanged(const QBrush &brush);
    void labelsFontChanged(const QFont &pen);
    void labelsAngleChanged(int angle);
    void gridLinePenChanged(const QPen &pen);
    void titleTextChanged(const QString &title);
    void titleBrushChanged(const QBrush &brush);
    void titleVisibleChanged(bool visible);
    void titleFontChanged(const QFont &font);
    void shadesPenChanged(const QPen &pen);
    void shadesBrushChanged(const QBrush &brush);

public:
    bool isMinorGridLineVisible() const;
    void setMinorGridLineVisible(bool visible = true);
    void setMinorGridLinePen(const QPen &pen);
    QPen minorGridLinePen() const;
    void setGridLineColor(const QColor &color);
    QColor gridLineColor();
    void setMinorGridLineColor(const QColor &color);
    QColor minorGridLineColor();
    void setReverse(bool reverse = true);
    bool isReverse() const;

signals:
    void minorGridVisibleChanged(bool visible);
    void minorGridLinePenChanged(const QPen &pen);
    void gridLineColorChanged(const QColor &color);
    void minorGridLineColorChanged(const QColor &color);
    void reverseChanged(bool reverse);

public:
    void setLabelsEditable(bool editable = true);
    bool labelsEditable() const;

signals:
    void labelsEditableChanged(bool editable);

public:
%If (QtCharts_6_2_0 -)
    bool labelsTruncated() const;
%End
%If (QtCharts_6_2_0 -)
    void setTruncateLabels(bool truncateLabels = true);
%End
%If (QtCharts_6_2_0 -)
    bool truncateLabels() const;
%End

signals:
%If (QtCharts_6_2_0 -)
    void labelsTruncatedChanged(bool labelsTruncated);
%End
%If (QtCharts_6_2_0 -)
    void truncateLabelsChanged(bool truncateLabels);
%End
};
