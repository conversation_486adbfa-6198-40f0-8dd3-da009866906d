// qabstractbarseries.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractBarSeries : public QAbstractSeries /NoDefaultCtors/
{
%TypeHeaderCode
#include <qabstractbarseries.h>
%End

public:
    virtual ~QAbstractBarSeries();
    void setBarWidth(qreal width);
    qreal barWidth() const;
    bool append(QBarSet *set /Transfer/);
    bool remove(QBarSet *set /TransferBack/);
    bool append(const QList<QBarSet *> &sets /Transfer/);
    bool insert(int index, QBarSet *set /Transfer/);
    int count() const /__len__/;
    QList<QBarSet *> barSets() const;
    void clear();
    void setLabelsVisible(bool visible = true);
    bool isLabelsVisible() const;
    bool take(QBarSet *set);

signals:
    void clicked(int index, QBarSet *barset);
    void hovered(bool status, int index, QBarSet *barset);
    void countChanged();
    void labelsVisibleChanged();
    void barsetsAdded(const QList<QBarSet *> &sets);
    void barsetsRemoved(const QList<QBarSet *> &sets);

public:
    enum LabelsPosition
    {
        LabelsCenter,
        LabelsInsideEnd,
        LabelsInsideBase,
        LabelsOutsideEnd,
    };

    void setLabelsFormat(const QString &format);
    QString labelsFormat() const;
    void setLabelsPosition(QAbstractBarSeries::LabelsPosition position);
    QAbstractBarSeries::LabelsPosition labelsPosition() const;

signals:
    void labelsFormatChanged(const QString &format);
    void labelsPositionChanged(QAbstractBarSeries::LabelsPosition position);
    void pressed(int index, QBarSet *barset);
    void released(int index, QBarSet *barset);
    void doubleClicked(int index, QBarSet *barset);

public:
    void setLabelsAngle(qreal angle);
    qreal labelsAngle() const;

signals:
    void labelsAngleChanged(qreal angle);

public:
    void setLabelsPrecision(int precision);
    int labelsPrecision() const;

signals:
    void labelsPrecisionChanged(int precision);
};
