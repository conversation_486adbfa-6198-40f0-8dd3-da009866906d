// qabstractseries.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractSeries : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qabstractseries.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QAbstractAxis, &sipType_QAbstractAxis, 19, 1},
        {sipName_QAbstractSeries, &sipType_QAbstractSeries, 25, 2},
        {sipName_QLegendMarker, &sipType_QLegendMarker, 40, 3},
        {sipName_QBarSet, &sipType_QBarSet, -1, 4},
        {sipName_QBoxSet, &sipType_QBoxSet, -1, 5},
        {sipName_QCandlestickModelMapper, &sipType_QCandlestickModelMapper, 46, 6},
        {sipName_QCandlestickSet, &sipType_QCandlestickSet, -1, 7},
        {sipName_QChart, &sipType_QChart, 48, 8},
        {sipName_QChartView, &sipType_QChartView, -1, 9},
        {sipName_QHBarModelMapper, &sipType_QHBarModelMapper, -1, 10},
        {sipName_QHBoxPlotModelMapper, &sipType_QHBoxPlotModelMapper, -1, 11},
        {sipName_QHPieModelMapper, &sipType_QHPieModelMapper, -1, 12},
        {sipName_QHXYModelMapper, &sipType_QHXYModelMapper, -1, 13},
        {sipName_QLegend, &sipType_QLegend, -1, 14},
        {sipName_QPieSlice, &sipType_QPieSlice, -1, 15},
        {sipName_QVBarModelMapper, &sipType_QVBarModelMapper, -1, 16},
        {sipName_QVBoxPlotModelMapper, &sipType_QVBoxPlotModelMapper, -1, 17},
        {sipName_QVPieModelMapper, &sipType_QVPieModelMapper, -1, 18},
        {sipName_QVXYModelMapper, &sipType_QVXYModelMapper, -1, -1},
        {sipName_QBarCategoryAxis, &sipType_QBarCategoryAxis, -1, 20},
        {sipName_QValueAxis, &sipType_QValueAxis, 24, 21},
    #if QT_VERSION >= 0x060200
        {sipName_QColorAxis, &sipType_QColorAxis, -1, 22},
    #else
        {0, 0, -1, 22},
    #endif
        {sipName_QDateTimeAxis, &sipType_QDateTimeAxis, -1, 23},
        {sipName_QLogValueAxis, &sipType_QLogValueAxis, -1, -1},
        {sipName_QCategoryAxis, &sipType_QCategoryAxis, -1, -1},
        {sipName_QAbstractBarSeries, &sipType_QAbstractBarSeries, 31, 26},
        {sipName_QAreaSeries, &sipType_QAreaSeries, -1, 27},
        {sipName_QBoxPlotSeries, &sipType_QBoxPlotSeries, -1, 28},
        {sipName_QCandlestickSeries, &sipType_QCandlestickSeries, -1, 29},
        {sipName_QXYSeries, &sipType_QXYSeries, 37, 30},
        {sipName_QPieSeries, &sipType_QPieSeries, -1, -1},
        {sipName_QBarSeries, &sipType_QBarSeries, -1, 32},
        {sipName_QHorizontalBarSeries, &sipType_QHorizontalBarSeries, -1, 33},
        {sipName_QHorizontalPercentBarSeries, &sipType_QHorizontalPercentBarSeries, -1, 34},
        {sipName_QHorizontalStackedBarSeries, &sipType_QHorizontalStackedBarSeries, -1, 35},
        {sipName_QPercentBarSeries, &sipType_QPercentBarSeries, -1, 36},
        {sipName_QStackedBarSeries, &sipType_QStackedBarSeries, -1, -1},
        {sipName_QLineSeries, &sipType_QLineSeries, 39, 38},
        {sipName_QScatterSeries, &sipType_QScatterSeries, -1, -1},
        {sipName_QSplineSeries, &sipType_QSplineSeries, -1, -1},
        {sipName_QAreaLegendMarker, &sipType_QAreaLegendMarker, -1, 41},
        {sipName_QBarLegendMarker, &sipType_QBarLegendMarker, -1, 42},
        {sipName_QBoxPlotLegendMarker, &sipType_QBoxPlotLegendMarker, -1, 43},
        {sipName_QCandlestickLegendMarker, &sipType_QCandlestickLegendMarker, -1, 44},
        {sipName_QPieLegendMarker, &sipType_QPieLegendMarker, -1, 45},
        {sipName_QXYLegendMarker, &sipType_QXYLegendMarker, -1, -1},
        {sipName_QHCandlestickModelMapper, &sipType_QHCandlestickModelMapper, -1, 47},
        {sipName_QVCandlestickModelMapper, &sipType_QVCandlestickModelMapper, -1, -1},
        {sipName_QPolarChart, &sipType_QPolarChart, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum SeriesType
    {
        SeriesTypeLine,
        SeriesTypeArea,
        SeriesTypeBar,
        SeriesTypeStackedBar,
        SeriesTypePercentBar,
        SeriesTypePie,
        SeriesTypeScatter,
        SeriesTypeSpline,
        SeriesTypeHorizontalBar,
        SeriesTypeHorizontalStackedBar,
        SeriesTypeHorizontalPercentBar,
        SeriesTypeBoxPlot,
        SeriesTypeCandlestick,
    };

    virtual ~QAbstractSeries();
    virtual QAbstractSeries::SeriesType type() const = 0;
    void setName(const QString &name);
    QString name() const;
    void setVisible(bool visible = true);
    bool isVisible() const;
    QChart *chart() const;
    void show();
    void hide();

signals:
    void nameChanged();
    void visibleChanged();

public:
    qreal opacity() const;
    void setOpacity(qreal opacity);
    bool attachAxis(QAbstractAxis *axis);
    bool detachAxis(QAbstractAxis *axis);
    QList<QAbstractAxis *> attachedAxes();

signals:
    void opacityChanged();

public:
    void setUseOpenGL(bool enable = true);
    bool useOpenGL() const;

signals:
    void useOpenGLChanged();
};
