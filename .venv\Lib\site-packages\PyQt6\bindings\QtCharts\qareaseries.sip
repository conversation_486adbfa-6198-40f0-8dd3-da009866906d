// qareaseries.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAreaSeries : public QAbstractSeries
{
%TypeHeaderCode
#include <qareaseries.h>
%End

public:
    explicit QAreaSeries(QObject *parent /TransferThis/ = 0);
    QAreaSeries(QLineSeries *upperSeries, QLineSeries *lowerSeries = 0);
    virtual ~QAreaSeries();
    virtual QAbstractSeries::SeriesType type() const;
    void setUpperSeries(QLineSeries *series);
    QLineSeries *upperSeries() const;
    void setLowerSeries(QLineSeries *series);
    QLineSeries *lowerSeries() const;
    void setPen(const QPen &pen);
    QPen pen() const;
    void setBrush(const QBrush &brush);
    QBrush brush() const;
    void setPointsVisible(bool visible = true);
    bool pointsVisible() const;
    void setColor(const QColor &color);
    QColor color() const;
    void setBorderColor(const QColor &color);
    QColor borderColor() const;

signals:
    void borderColorChanged(QColor color);
    void colorChanged(QColor color);
    void clicked(const QPointF &point);
    void selected();
    void hovered(const QPointF &point, bool state);

public:
    void setPointLabelsFormat(const QString &format);
    QString pointLabelsFormat() const;
    void setPointLabelsVisible(bool visible = true);
    bool pointLabelsVisible() const;
    void setPointLabelsFont(const QFont &font);
    QFont pointLabelsFont() const;
    void setPointLabelsColor(const QColor &color);
    QColor pointLabelsColor() const;

signals:
    void pointLabelsFormatChanged(const QString &format);
    void pointLabelsVisibilityChanged(bool visible);
    void pointLabelsFontChanged(const QFont &font);
    void pointLabelsColorChanged(const QColor &color);
    void pressed(const QPointF &point);
    void released(const QPointF &point);
    void doubleClicked(const QPointF &point);

public:
    void setPointLabelsClipping(bool enable = true);
    bool pointLabelsClipping() const;

signals:
    void pointLabelsClippingChanged(bool clipping);
};
