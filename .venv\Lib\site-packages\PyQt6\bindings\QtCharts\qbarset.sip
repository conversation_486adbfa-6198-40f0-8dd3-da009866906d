// qbarset.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QBarSet : public QObject
{
%TypeHeaderCode
#include <qbarset.h>
%End

public:
    QBarSet(const QString name, QObject *parent /TransferThis/ = 0);
    virtual ~QBarSet();
    void append(const QList<qreal> &values);
    void append(const qreal value);
    QBarSet &operator<<(const qreal &value);
    void insert(const int index, const qreal value);
    void replace(const int index, const qreal value);
    int count() const /__len__/;
    qreal sum() const;
    void setPen(const QPen &pen);
    QPen pen() const;
    void setBrush(const QBrush &brush);
    QBrush brush() const;
    void setLabelBrush(const QBrush &brush);
    QBrush labelBrush() const;
    void setLabelFont(const QFont &font);
    QFont labelFont() const;
    void setLabel(const QString label);
    QString label() const;
    void remove(const int index, const int count = 1);
    qreal at(const int index) const;
    qreal operator[](const int index) const;
%MethodCode
        Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());
        
        if (idx < 0)
            sipIsErr = 1;
        else
            sipRes = sipCpp->operator[]((int)idx);
%End

    QColor color();
    void setColor(QColor color);
    QColor borderColor();
    void setBorderColor(QColor color);
    QColor labelColor();
    void setLabelColor(QColor color);

signals:
    void penChanged();
    void brushChanged();
    void labelChanged();
    void labelBrushChanged();
    void labelFontChanged();
    void valuesAdded(int index, int count);
    void valuesRemoved(int index, int count);
    void valueChanged(int index);
    void clicked(int index);
    void hovered(bool status, int index);
    void colorChanged(QColor color);
    void borderColorChanged(QColor color);
    void labelColorChanged(QColor color);
    void pressed(int index);
    void released(int index);
    void doubleClicked(int index);

public:
%If (QtCharts_6_2_0 -)
    QColor selectedColor() const;
%End
%If (QtCharts_6_2_0 -)
    void setSelectedColor(const QColor &color);
%End
%If (QtCharts_6_2_0 -)
    bool isBarSelected(int index) const;
%End
%If (QtCharts_6_2_0 -)
    void selectBar(int index);
%End
%If (QtCharts_6_2_0 -)
    void deselectBar(int index);
%End
%If (QtCharts_6_2_0 -)
    void setBarSelected(int index, bool selected);
%End
%If (QtCharts_6_2_0 -)
    void selectAllBars();
%End
%If (QtCharts_6_2_0 -)
    void deselectAllBars();
%End
%If (QtCharts_6_2_0 -)
    void selectBars(const QList<int> &indexes);
%End
%If (QtCharts_6_2_0 -)
    void deselectBars(const QList<int> &indexes);
%End
%If (QtCharts_6_2_0 -)
    void toggleSelection(const QList<int> &indexes);
%End
%If (QtCharts_6_2_0 -)
    QList<int> selectedBars() const;
%End

signals:
%If (QtCharts_6_2_0 -)
    void selectedColorChanged(const QColor &color);
%End
%If (QtCharts_6_2_0 -)
    void selectedBarsChanged(const QList<int> &indexes);
%End
};
