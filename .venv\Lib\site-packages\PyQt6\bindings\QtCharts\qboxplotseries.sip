// qboxplotseries.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QBoxPlotSeries : public QAbstractSeries
{
%TypeHeaderCode
#include <qboxplotseries.h>
%End

public:
    explicit QBoxPlotSeries(QObject *parent /TransferThis/ = 0);
    virtual ~QBoxPlotSeries();
    bool append(QBoxSet *box /Transfer/);
    bool append(const QList<QBoxSet *> &boxes /Transfer/);
    bool remove(QBoxSet *box);
    bool take(QBoxSet *box);
    bool insert(int index, QBoxSet *box /Transfer/);
    int count() const /__len__/;
    QList<QBoxSet *> boxSets() const;
    void clear();
    virtual QAbstractSeries::SeriesType type() const;
    void setBoxOutlineVisible(bool visible);
    bool boxOutlineVisible();
    void setBoxWidth(qreal width);
    qreal boxWidth();
    void setBrush(const QBrush &brush);
    QBrush brush() const;
    void setPen(const QPen &pen);
    QPen pen() const;

signals:
    void clicked(QBoxSet *boxset);
    void hovered(bool status, QBoxSet *boxset);
    void countChanged();
    void penChanged();
    void brushChanged();
    void boxOutlineVisibilityChanged();
    void boxWidthChanged();
    void boxsetsAdded(const QList<QBoxSet *> &sets);
    void boxsetsRemoved(const QList<QBoxSet *> &sets);
    void pressed(QBoxSet *boxset);
    void released(QBoxSet *boxset);
    void doubleClicked(QBoxSet *boxset);
};
