// qcategoryaxis.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCategoryAxis : public QValueAxis
{
%TypeHeaderCode
#include <qcategoryaxis.h>
%End

public:
    explicit QCategoryAxis(QObject *parent /TransferThis/ = 0);
    virtual ~QCategoryAxis();
    virtual QAbstractAxis::AxisType type() const;
    void append(const QString &label, qreal categoryEndValue);
    void remove(const QString &label);
    void replaceLabel(const QString &oldLabel, const QString &newLabel);
    qreal startValue(const QString &categoryLabel = QString()) const;
    void setStartValue(qreal min);
    qreal endValue(const QString &categoryLabel) const;
    QStringList categoriesLabels();
    int count() const /__len__/;

signals:
    void categoriesChanged();

public:
    enum AxisLabelsPosition
    {
        AxisLabelsPositionCenter,
        AxisLabelsPositionOnValue,
    };

    QCategoryAxis::AxisLabelsPosition labelsPosition() const;
    void setLabelsPosition(QCategoryAxis::AxisLabelsPosition position);

signals:
    void labelsPositionChanged(QCategoryAxis::AxisLabelsPosition position);
};
