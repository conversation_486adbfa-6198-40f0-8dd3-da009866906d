// qchart.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QChart : public QGraphicsWidget
{
%TypeHeaderCode
#include <qchart.h>
%End

public:
    enum ChartTheme
    {
        ChartThemeLight,
        ChartThemeBlueCerulean,
        ChartThemeDark,
        ChartThemeBrownSand,
        ChartThemeBlueNcs,
        ChartThemeHighContrast,
        ChartThemeBlueIcy,
        ChartThemeQt,
    };

    enum AnimationOption /BaseType=Flag/
    {
        NoAnimation,
        GridAxisAnimations,
        SeriesAnimations,
        AllAnimations,
    };

    typedef QFlags<QChart::AnimationOption> AnimationOptions;
    QChart(QGraphicsItem *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QChart();
    void addSeries(QAbstractSeries *series /Transfer/);
    void removeSeries(QAbstractSeries *series /TransferBack/);
    void removeAllSeries();
    QList<QAbstractSeries *> series() const;
    void setTheme(QChart::ChartTheme theme);
    QChart::ChartTheme theme() const;
    void setTitle(const QString &title);
    QString title() const;
    void setTitleFont(const QFont &font);
    QFont titleFont() const;
    void setTitleBrush(const QBrush &brush);
    QBrush titleBrush() const;
    void setBackgroundBrush(const QBrush &brush);
    QBrush backgroundBrush() const;
    void setBackgroundPen(const QPen &pen);
    QPen backgroundPen() const;
    void setBackgroundVisible(bool visible = true);
    bool isBackgroundVisible() const;
    void setAnimationOptions(QChart::AnimationOptions options);
    QChart::AnimationOptions animationOptions() const;
    void zoomIn();
    void zoomIn(const QRectF &rect);
    void zoomOut();
    void zoom(qreal factor);
    QLegend *legend() const;
    void createDefaultAxes();
    void setDropShadowEnabled(bool enabled = true);
    bool isDropShadowEnabled() const;
    void scroll(qreal dx, qreal dy);
    QRectF plotArea() const;
    void addAxis(QAbstractAxis *axis /Transfer/, Qt::Alignment alignment);
    void removeAxis(QAbstractAxis *axis /TransferBack/);
    QList<QAbstractAxis *> axes(Qt::Orientations orientation = Qt::Horizontal | Qt::Vertical, QAbstractSeries *series = 0) const;
    void setMargins(const QMargins &margins);
    QMargins margins() const;
    QPointF mapToValue(const QPointF &position, QAbstractSeries *series = 0);
    QPointF mapToPosition(const QPointF &value, QAbstractSeries *series = 0);

    enum ChartType
    {
        ChartTypeUndefined,
        ChartTypeCartesian,
        ChartTypePolar,
    };

    void setBackgroundRoundness(qreal diameter);
    qreal backgroundRoundness() const;
    void zoomReset();
    bool isZoomed();
    void setPlotArea(const QRectF &rect);
    void setPlotAreaBackgroundBrush(const QBrush &brush);
    QBrush plotAreaBackgroundBrush() const;
    void setPlotAreaBackgroundPen(const QPen &pen);
    QPen plotAreaBackgroundPen() const;
    void setPlotAreaBackgroundVisible(bool visible = true);
    bool isPlotAreaBackgroundVisible() const;
    QChart::ChartType chartType() const;
    void setLocalizeNumbers(bool localize);
    bool localizeNumbers() const;
    void setLocale(const QLocale &locale);
    QLocale locale() const;

signals:
    void plotAreaChanged(const QRectF &plotArea);

public:
    void setAnimationDuration(int msecs);
    int animationDuration() const;
    void setAnimationEasingCurve(const QEasingCurve &curve);
    QEasingCurve animationEasingCurve() const;
};
