// qchartview.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QChartView : public QGraphicsView
{
%TypeHeaderCode
#include <qchartview.h>
%End

public:
    enum RubberBand /BaseType=Flag/
    {
        NoRubberBand,
        VerticalRubberBand,
        HorizontalRubberBand,
        RectangleRubberBand,
%If (QtCharts_6_2_0 -)
        ClickThroughRubberBand,
%End
    };

    typedef QFlags<QChartView::RubberBand> RubberBands;
    explicit QChartView(QWidget *parent /TransferThis/ = 0);
    QChartView(QChart *chart /Transfer/, QWidget *parent /TransferThis/ = 0);
    virtual ~QChartView();
    void setRubberBand(const QChartView::RubberBands &rubberBands);
    QChartView::RubberBands rubberBand() const;
    void setChart(QChart *chart /Transfer/);
%MethodCode
        // The ownership of any existing chart must be passed back to Python.
        QChart *c = sipCpp->chart();
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->setChart(a0);
        Py_END_ALLOW_THREADS
        
        if (c)
        {
            PyObject *co = sipGetPyObject(c, sipType_QChart);
        
            if (co)
                sipTransferBack(co);
        }
%End

    QChart *chart() const;

protected:
    virtual void resizeEvent(QResizeEvent *event);
    virtual void mousePressEvent(QMouseEvent *event);
    virtual void mouseMoveEvent(QMouseEvent *event);
    virtual void mouseReleaseEvent(QMouseEvent *event);
    virtual void wheelEvent(QWheelEvent *event);
};
