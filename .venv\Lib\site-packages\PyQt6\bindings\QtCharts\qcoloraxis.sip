// qcoloraxis.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtCharts_6_2_0 -)

class QColorAxis : public QAbstractAxis
{
%TypeHeaderCode
#include <qcoloraxis.h>
%End

public:
    explicit QColorAxis(QObject *parent /TransferThis/ = 0);
    virtual ~QColorAxis();
    virtual QAbstractAxis::AxisType type() const;
    void setMin(qreal min);
    qreal min() const;
    void setMax(qreal max);
    qreal max() const;
    void setRange(qreal min, qreal max);
    void setTickCount(int count);
    int tickCount() const;
    void setSize(const qreal size);
    qreal size() const;
    void setGradient(const QLinearGradient &gradient);
    QLinearGradient gradient() const;
    void setAutoRange(bool autoRange);
    bool autoRange() const;

signals:
    void minChanged(qreal min);
    void maxChanged(qreal max);
    void rangeChanged(qreal min, qreal max);
    void tickCountChanged(int tickCount);
    void gradientChanged(const QLinearGradient &gradient);
    void sizeChanged(const qreal size);
    void autoRangeChanged(bool autoRange);
};

%End
