// qlegend.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLegend : public QGraphicsWidget
{
%TypeHeaderCode
#include <qlegend.h>
%End

public:
    virtual ~QLegend();
    virtual void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0);
    void setBrush(const QBrush &brush);
    QBrush brush() const;
    void setPen(const QPen &pen);
    QPen pen() const;
    void setAlignment(Qt::Alignment alignment);
    Qt::Alignment alignment() const;
    void detachFromChart();
    void attachToChart();
    bool isAttachedToChart();
    void setBackgroundVisible(bool visible = true);
    bool isBackgroundVisible() const;
    void setColor(QColor color);
    QColor color();
    void setBorderColor(QColor color);
    QColor borderColor();
    void setFont(const QFont &font);
    QFont font() const;
    void setLabelBrush(const QBrush &brush);
    QBrush labelBrush() const;
    void setLabelColor(QColor color);
    QColor labelColor() const;
    QList<QLegendMarker *> markers(QAbstractSeries *series = 0) const;

signals:
    void backgroundVisibleChanged(bool visible);
    void colorChanged(QColor color);
    void borderColorChanged(QColor color);
    void fontChanged(QFont font);
    void labelColorChanged(QColor color);

protected:
    virtual void hideEvent(QHideEvent *event);
    virtual void showEvent(QShowEvent *event);

public:
    bool reverseMarkers();
    void setReverseMarkers(bool reverseMarkers = true);

signals:
    void reverseMarkersChanged(bool reverseMarkers);

public:
    bool showToolTips() const;
    void setShowToolTips(bool show);

signals:
    void showToolTipsChanged(bool showToolTips);

public:
    enum MarkerShape
    {
        MarkerShapeDefault,
        MarkerShapeRectangle,
        MarkerShapeCircle,
        MarkerShapeFromSeries,
%If (QtCharts_6_2_0 -)
        MarkerShapeRotatedRectangle,
%End
%If (QtCharts_6_2_0 -)
        MarkerShapeTriangle,
%End
%If (QtCharts_6_2_0 -)
        MarkerShapeStar,
%End
%If (QtCharts_6_2_0 -)
        MarkerShapePentagon,
%End
    };

    QLegend::MarkerShape markerShape() const;
    void setMarkerShape(QLegend::MarkerShape shape);

signals:
    void markerShapeChanged(QLegend::MarkerShape shape);

public:
%If (QtCharts_6_2_0 -)
    bool isInteractive() const;
%End
%If (QtCharts_6_2_0 -)
    void setInteractive(bool interactive);
%End

signals:
%If (QtCharts_6_2_0 -)
    void attachedToChartChanged(bool attachedToChart);
%End
%If (QtCharts_6_2_0 -)
    void interactiveChanged(bool interactive);
%End

private:
    explicit QLegend(QChart *chart);
};
