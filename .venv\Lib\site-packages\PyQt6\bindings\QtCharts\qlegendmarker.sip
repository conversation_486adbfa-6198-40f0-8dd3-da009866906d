// qlegendmarker.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLegendMarker : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qlegendmarker.h>
%End

public:
    enum LegendMarkerType
    {
        LegendMarkerTypeArea,
        LegendMarkerTypeBar,
        LegendMarkerTypePie,
        LegendMarkerTypeXY,
        LegendMarkerTypeBoxPlot,
        LegendMarkerTypeCandlestick,
    };

    virtual ~QLegendMarker();
    virtual QLegendMarker::LegendMarkerType type() = 0;
    QString label() const;
    void setLabel(const QString &label);
    QBrush labelBrush() const;
    void setLabelBrush(const QBrush &brush);
    QFont font() const;
    void setFont(const QFont &font);
    QPen pen() const;
    void setPen(const QPen &pen);
    QBrush brush() const;
    void setBrush(const QBrush &brush);
    bool isVisible() const;
    void setVisible(bool visible);
    virtual QAbstractSeries *series() = 0;

signals:
    void clicked();
    void hovered(bool status);
    void labelChanged();
    void labelBrushChanged();
    void fontChanged();
    void penChanged();
    void brushChanged();
    void visibleChanged();

public:
    QLegend::MarkerShape shape() const;
    void setShape(QLegend::MarkerShape shape);

signals:
    void shapeChanged();
};
