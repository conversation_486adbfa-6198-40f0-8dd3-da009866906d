// qpieseries.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPieSeries : public QAbstractSeries
{
%TypeHeaderCode
#include <qpieseries.h>
%End

public:
    explicit QPieSeries(QObject *parent /TransferThis/ = 0);
    virtual ~QPieSeries();
    virtual QAbstractSeries::SeriesType type() const;
    bool append(QPieSlice *slice /Transfer/);
    bool append(const QList<QPieSlice *> &slices /Transfer/);
    QPieSeries &operator<<(QPieSlice *slice /Transfer/);
    QPieSlice *append(const QString &label, qreal value);
    bool insert(int index, QPieSlice *slice /Transfer/);
    bool remove(QPieSlice *slice);
    void clear();
    QList<QPieSlice *> slices() const;
    int count() const /__len__/;
    bool isEmpty() const;
    qreal sum() const;
    void setHorizontalPosition(qreal relativePosition);
    qreal horizontalPosition() const;
    void setVerticalPosition(qreal relativePosition);
    qreal verticalPosition() const;
    void setPieSize(qreal relativeSize);
    qreal pieSize() const;
    void setPieStartAngle(qreal startAngle);
    qreal pieStartAngle() const;
    void setPieEndAngle(qreal endAngle);
    qreal pieEndAngle() const;
    void setLabelsVisible(bool visible = true);

signals:
    void added(const QList<QPieSlice *> &slices);
    void removed(const QList<QPieSlice *> &slices);
    void clicked(QPieSlice *slice);
    void hovered(QPieSlice *slice, bool state);
    void countChanged();
    void sumChanged();

public:
    bool take(QPieSlice *slice);
    void setHoleSize(qreal holeSize);
    qreal holeSize() const;
    void setLabelsPosition(QPieSlice::LabelPosition position);

signals:
    void pressed(QPieSlice *slice);
    void released(QPieSlice *slice);
    void doubleClicked(QPieSlice *slice);
};
