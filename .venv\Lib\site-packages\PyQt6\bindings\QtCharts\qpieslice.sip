// qpieslice.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPieSlice : public QObject
{
%TypeHeaderCode
#include <qpieslice.h>
%End

public:
    explicit QPieSlice(QObject *parent /TransferThis/ = 0);
    QPieSlice(QString label, qreal value, QObject *parent /TransferThis/ = 0);
    virtual ~QPieSlice();
    void setLabel(QString label);
    QString label() const;
    void setValue(qreal value);
    qreal value() const;
    void setLabelVisible(bool visible = true);
    bool isLabelVisible() const;
    void setExploded(bool exploded = true);
    bool isExploded() const;
    void setPen(const QPen &pen);
    QPen pen() const;
    QColor borderColor();
    void setBorderColor(QColor color);
    int borderWidth();
    void setBorderWidth(int width);
    void setBrush(const QBrush &brush);
    QBrush brush() const;
    QColor color();
    void setColor(QColor color);
    void setLabelBrush(const QBrush &brush);
    QBrush labelBrush() const;
    QColor labelColor();
    void setLabelColor(QColor color);
    void setLabelFont(const QFont &font);
    QFont labelFont() const;
    void setLabelArmLengthFactor(qreal factor);
    qreal labelArmLengthFactor() const;
    void setExplodeDistanceFactor(qreal factor);
    qreal explodeDistanceFactor() const;
    qreal percentage() const;
    qreal startAngle() const;
    qreal angleSpan() const;
    QPieSeries *series() const;

    enum LabelPosition
    {
        LabelOutside,
        LabelInsideHorizontal,
        LabelInsideTangential,
        LabelInsideNormal,
    };

    QPieSlice::LabelPosition labelPosition();
    void setLabelPosition(QPieSlice::LabelPosition position);

signals:
    void labelChanged();
    void valueChanged();
    void labelVisibleChanged();
    void penChanged();
    void brushChanged();
    void labelBrushChanged();
    void labelFontChanged();
    void percentageChanged();
    void startAngleChanged();
    void angleSpanChanged();
    void colorChanged();
    void borderColorChanged();
    void borderWidthChanged();
    void labelColorChanged();
    void clicked();
    void hovered(bool state);
    void pressed();
    void released();
    void doubleClicked();
};
