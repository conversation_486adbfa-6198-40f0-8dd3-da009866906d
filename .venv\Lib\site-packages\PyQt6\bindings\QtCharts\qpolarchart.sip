// qpolarchart.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPolarChart : public QChart
{
%TypeHeaderCode
#include <qpolarchart.h>
%End

public:
    enum PolarOrientation /BaseType=Flag/
    {
        PolarOrientationRadial,
        PolarOrientationAngular,
    };

    typedef QFlags<QPolarChart::PolarOrientation> PolarOrientations;
    QPolarChart(QGraphicsItem *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QPolarChart();
    void addAxis(QAbstractAxis *axis /Transfer/, QPolarChart::PolarOrientation polarOrientation);
    QList<QAbstractAxis *> axes(QPolarChart::PolarOrientations polarOrientation = QPolarChart::PolarOrientations(QPolarChart::PolarOrientationRadial | QPolarChart::PolarOrientationAngular), QAbstractSeries *series = 0) const;
    static QPolarChart::PolarOrientation axisPolarOrientation(QAbstractAxis *axis);
};
