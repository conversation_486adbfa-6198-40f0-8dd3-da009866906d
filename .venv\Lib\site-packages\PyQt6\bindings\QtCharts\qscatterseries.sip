// qscatterseries.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QScatterSeries : public QXYSeries
{
%TypeHeaderCode
#include <qscatterseries.h>
%End

public:
    enum MarkerShape
    {
        Marker<PERSON><PERSON><PERSON><PERSON>ircle,
        <PERSON>er<PERSON>ha<PERSON>Rectangle,
%If (QtCharts_6_2_0 -)
        MarkerShapeRotatedRectangle,
%End
%If (QtCharts_6_2_0 -)
        MarkerShapeTriangle,
%End
%If (QtCharts_6_2_0 -)
        MarkerShapeStar,
%End
%If (QtCharts_6_2_0 -)
        MarkerShapePentagon,
%End
    };

    explicit QScatterSeries(QObject *parent /TransferThis/ = 0);
    virtual ~QScatterSeries();
    virtual QAbstractSeries::SeriesType type() const;
    QScatterSeries::MarkerShape markerShape() const;
    void setMarkerShape(QScatterSeries::MarkerShape shape);
    qreal markerSize() const;
    void setMarkerSize(qreal size);
    virtual void setPen(const QPen &pen);
    QBrush brush() const;
    virtual void setBrush(const QBrush &brush);
    virtual void setColor(const QColor &color);
    virtual QColor color() const;
    void setBorderColor(const QColor &color);
    QColor borderColor() const;

signals:
    void colorChanged(QColor color);
    void borderColorChanged(QColor color);
    void markerShapeChanged(QScatterSeries::MarkerShape shape);
    void markerSizeChanged(qreal size);
};
