// qvalueaxis.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QValueAxis : public QAbstractAxis
{
%TypeHeaderCode
#include <qvalueaxis.h>
%End

public:
    explicit QValueAxis(QObject *parent /TransferThis/ = 0);
    virtual ~QValueAxis();
    virtual QAbstractAxis::AxisType type() const;
    void setMin(qreal min);
    qreal min() const;
    void setMax(qreal max);
    qreal max() const;
    void setRange(qreal min, qreal max);
    void setTickCount(int count);
    int tickCount() const;
    void setLabelFormat(const QString &format);
    QString labelFormat() const;

public slots:
    void applyNiceNumbers();

signals:
    void minChanged(qreal min);
    void maxChanged(qreal max);
    void rangeChanged(qreal min, qreal max);
    void tickCountChanged(int tickCount);
    void labelFormatChanged(const QString &format);

public:
    void setMinorTickCount(int count);
    int minorTickCount() const;

signals:
    void minorTickCountChanged(int tickCount);

public:
    enum TickType
    {
        TicksDynamic,
        TicksFixed,
    };

    void setTickAnchor(qreal anchor);
    qreal tickAnchor() const;
    void setTickInterval(qreal insterval);
    qreal tickInterval() const;
    void setTickType(QValueAxis::TickType type);
    QValueAxis::TickType tickType() const;

signals:
    void tickIntervalChanged(qreal interval);
    void tickAnchorChanged(qreal anchor);
    void tickTypeChanged(QValueAxis::TickType type);
};
