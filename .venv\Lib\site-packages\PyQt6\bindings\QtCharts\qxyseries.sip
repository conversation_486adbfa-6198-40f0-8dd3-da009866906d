// qxyseries.sip generated by MetaSIP
//
// This file is part of the QtCharts Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6-Charts.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QXYSeries : public QAbstractSeries /NoDefaultCtors/
{
%TypeHeaderCode
#include <qxyseries.h>
%End

public:
    virtual ~QXYSeries();
    void append(qreal x, qreal y);
    void append(const QPointF &point);
    void append(const QList<QPointF> &points);
    void replace(qreal oldX, qreal oldY, qreal newX, qreal newY);
    void replace(const QPointF &oldPoint, const QPointF &newPoint);
    void replace(const QList<QPointF> &points);
    void replace(int index, qreal newX, qreal newY);
    void replace(int index, const QPointF &newPoint);
    void remove(qreal x, qreal y);
    void remove(const QPointF &point);
    void remove(int index);
    void insert(int index, const QPointF &point);
    void clear();
    int count() const /__len__/;
    QList<QPointF> points() const;
    QXYSeries &operator<<(const QPointF &point);
    QXYSeries &operator<<(const QList<QPointF> &points);
    virtual void setPen(const QPen &pen);
    QPen pen() const;
    virtual void setBrush(const QBrush &brush);
    QBrush brush() const;
    virtual void setColor(const QColor &color);
    virtual QColor color() const;
    void setPointsVisible(bool visible = true);
    bool pointsVisible() const;
    const QPointF &at(int index) const;

signals:
    void clicked(const QPointF &point);
    void colorChanged(QColor color);
    void pointReplaced(int index);
    void pointRemoved(int index);
    void pointAdded(int index);
    void pointsReplaced();
    void hovered(const QPointF &point, bool state);

public:
    void setPointLabelsFormat(const QString &format);
    QString pointLabelsFormat() const;
    void setPointLabelsVisible(bool visible = true);
    bool pointLabelsVisible() const;
    void setPointLabelsFont(const QFont &font);
    QFont pointLabelsFont() const;
    void setPointLabelsColor(const QColor &color);
    QColor pointLabelsColor() const;

signals:
    void pointLabelsFormatChanged(const QString &format);
    void pointLabelsVisibilityChanged(bool visible);
    void pointLabelsFontChanged(const QFont &font);
    void pointLabelsColorChanged(const QColor &color);
    void pressed(const QPointF &point);
    void released(const QPointF &point);
    void doubleClicked(const QPointF &point);

public:
    void removePoints(int index, int count);
    void setPointLabelsClipping(bool enable = true);
    bool pointLabelsClipping() const;

signals:
    void pointLabelsClippingChanged(bool clipping);
    void pointsRemoved(int index, int count);
    void penChanged(const QPen &pen);

public:
%If (QtCharts_6_2_0 -)

    enum class PointConfiguration
    {
        Color,
        Size,
        Visibility,
        LabelVisibility,
%If (QtCharts_6_5_0 -)
        LabelFormat,
%End
    };

%End
%If (QtCharts_6_2_0 -)
    void setSelectedColor(const QColor &color);
%End
%If (QtCharts_6_2_0 -)
    QColor selectedColor() const;
%End
%If (QtCharts_6_2_0 -)
    bool isPointSelected(int index);
%End
%If (QtCharts_6_2_0 -)
    void selectPoint(int index);
%End
%If (QtCharts_6_2_0 -)
    void deselectPoint(int index);
%End
%If (QtCharts_6_2_0 -)
    void setPointSelected(int index, bool selected);
%End
%If (QtCharts_6_2_0 -)
    void selectAllPoints();
%End
%If (QtCharts_6_2_0 -)
    void deselectAllPoints();
%End
%If (QtCharts_6_2_0 -)
    void selectPoints(const QList<int> &indexes);
%End
%If (QtCharts_6_2_0 -)
    void deselectPoints(const QList<int> &indexes);
%End
%If (QtCharts_6_2_0 -)
    void toggleSelection(const QList<int> &indexes);
%End
%If (QtCharts_6_2_0 -)
    QList<int> selectedPoints() const;
%End
%If (QtCharts_6_2_0 -)
    void setLightMarker(const QImage &lightMarker);
%End
%If (QtCharts_6_2_0 -)
    const QImage &lightMarker() const;
%End
%If (QtCharts_6_2_0 -)
    void setSelectedLightMarker(const QImage &selectedLightMarker);
%End
%If (QtCharts_6_2_0 -)
    const QImage &selectedLightMarker() const;
%End
%If (QtCharts_6_2_0 -)
    void setMarkerSize(qreal size);
%End
%If (QtCharts_6_2_0 -)
    qreal markerSize() const;
%End
%If (QtCharts_6_2_0 -)
    void setBestFitLineVisible(bool visible = true);
%End
%If (QtCharts_6_2_0 -)
    bool bestFitLineVisible() const;
%End
%If (QtCharts_6_2_0 -)
    std::pair<qreal, qreal> bestFitLineEquation(bool &ok) const;
%End
%If (QtCharts_6_2_0 -)
    void setBestFitLinePen(const QPen &pen);
%End
%If (QtCharts_6_2_0 -)
    QPen bestFitLinePen() const;
%End
%If (QtCharts_6_2_0 -)
    void setBestFitLineColor(const QColor &color);
%End
%If (QtCharts_6_2_0 -)
    QColor bestFitLineColor() const;
%End
%If (QtCharts_6_2_0 -)
    void clearPointConfiguration(const int index, const QXYSeries::PointConfiguration key);
%End
%If (QtCharts_6_2_0 -)
    void clearPointConfiguration(const int index);
%End
%If (QtCharts_6_2_0 -)
    void clearPointsConfiguration(const QXYSeries::PointConfiguration key);
%End
%If (QtCharts_6_2_0 -)
    void clearPointsConfiguration();
%End
%If (QtCharts_6_2_0 -)
    void setPointConfiguration(const int index, const QXYSeries::PointConfiguration key, const QVariant &value);
%End
%If (QtCharts_6_2_0 -)
    void setPointConfiguration(const int index, const QHash<QXYSeries::PointConfiguration, QVariant> &configuration);
%End
%If (QtCharts_6_2_0 -)
    void setPointsConfiguration(const QHash<int, QHash<QXYSeries::PointConfiguration, QVariant>> &pointsConfiguration);
%End
%If (QtCharts_6_2_0 -)
    QHash<QXYSeries::PointConfiguration, QVariant> pointConfiguration(const int index) const;
%End
%If (QtCharts_6_8_0 -)
    typedef QHash<int, QHash<QXYSeries::PointConfiguration, QVariant>> PointsConfigurationHash;
%End
%If (QtCharts_6_8_0 -)
    QXYSeries::PointsConfigurationHash pointsConfiguration() const;
%End
%If (QtCharts_6_2_0 - QtCharts_6_8_0)
    QHash<int, QHash<QXYSeries::PointConfiguration, QVariant>> pointsConfiguration() const;
%End
%If (QtCharts_6_2_0 -)
    void sizeBy(const QList<qreal> &sourceData, const qreal minSize, const qreal maxSize);
%End
%If (QtCharts_6_2_0 -)
    void colorBy(const QList<qreal> &sourceData, const QLinearGradient &gradient = QLinearGradient());
%End

signals:
%If (QtCharts_6_2_0 -)
    void selectedColorChanged(const QColor &color);
%End
%If (QtCharts_6_2_0 -)
    void selectedPointsChanged();
%End
%If (QtCharts_6_2_0 -)
    void lightMarkerChanged(const QImage &lightMarker);
%End
%If (QtCharts_6_2_0 -)
    void selectedLightMarkerChanged(const QImage &selectedLightMarker);
%End
%If (QtCharts_6_2_0 -)
    void bestFitLineVisibilityChanged(bool visible);
%End
%If (QtCharts_6_2_0 -)
    void bestFitLinePenChanged(const QPen &pen);
%End
%If (QtCharts_6_2_0 -)
    void bestFitLineColorChanged(const QColor &color);
%End
%If (QtCharts_6_8_0 -)
    void pointsConfigurationChanged(const QXYSeries::PointsConfigurationHash &configuration);
%End
%If (QtCharts_6_2_0 - QtCharts_6_8_0)
    void pointsConfigurationChanged(const QHash<int, QHash<QXYSeries::PointConfiguration, QVariant>> &configuration);
%End
%If (QtCharts_6_2_0 -)
    void markerSizeChanged(qreal size);
%End
};
