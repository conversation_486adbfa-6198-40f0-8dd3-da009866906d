// QtCoremod.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtCore, call_super_init=True, default_VirtualErrorHandler=PyQt6, keyword_arguments="Optional", use_limited_api=True, py_ssize_t_clean=True)

%Timeline {Qt_6_0_0 Qt_6_1_0 Qt_6_2_0 Qt_6_3_0 Qt_6_4_0 Qt_6_5_0 Qt_6_6_0 Qt_6_7_0 Qt_6_8_0 Qt_6_9_0}

%Platforms {Android iOS Linux macOS WebAssembly Windows}

%Feature PyQt_Accessibility
%Feature PyQt_SessionManager
%Feature PyQt_SSL
%Feature PyQt_qreal_double
%Feature PyQt_PrintDialog
%Feature PyQt_Printer
%Feature PyQt_PrintPreviewWidget
%Feature PyQt_PrintPreviewDialog
%Feature PyQt_RawFont
%Feature PyQt_OpenGL
%Feature PyQt_OpenGL_ES2
%Feature PyQt_Process
%Feature PyQt_WebChannel
%Feature PyQt_DTLS
%Feature PyQt_Permissions
%Feature PyQt_Vulkan

%Copying
Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%Plugin PyQt6

%DefaultEncoding "ASCII"

%Include(name=pyqt-internal.sip5, optional=True)
%Include(name=pyqt-gpl.sip5, optional=True)
%Include(name=pyqt-commercial.sip5, optional=True)

%DefaultSupertype PyQt6.sip.simplewrapper

int PYQT_VERSION;
const char *PYQT_VERSION_STR;

%ModuleCode
static int PYQT_VERSION = 0x060900;
static const char *PYQT_VERSION_STR = "6.9.0";
%End

%Include qglobal.sip
%Include qtenvironmentvariables.sip
%Include qtversion.sip
%Include qnamespace.sip
%Include qabstractanimation.sip
%Include qabstracteventdispatcher.sip
%Include qabstractitemmodel.sip
%Include qabstractnativeeventfilter.sip
%Include qabstractproxymodel.sip
%Include qanimationgroup.sip
%Include qbasictimer.sip
%Include qbitarray.sip
%Include qbuffer.sip
%Include qbytearray.sip
%Include qbytearrayalgorithms.sip
%Include qbytearraymatcher.sip
%Include qcalendar.sip
%Include qcborcommon.sip
%Include qcborstreamreader.sip
%Include qcborstreamwriter.sip
%Include qchar.sip
%Include qcollator.sip
%Include qcommandlineoption.sip
%Include qcommandlineparser.sip
%Include qconcatenatetablesproxymodel.sip
%Include qcoreapplication.sip
%Include qcoreevent.sip
%Include qcryptographichash.sip
%Include qdatastream.sip
%Include qdatetime.sip
%Include qdeadlinetimer.sip
%Include qdir.sip
%Include qdiriterator.sip
%Include qeasingcurve.sip
%Include qelapsedtimer.sip
%Include qeventloop.sip
%Include qfile.sip
%Include qfiledevice.sip
%Include qfileinfo.sip
%Include qfileselector.sip
%Include qfilesystemwatcher.sip
%Include qidentityproxymodel.sip
%Include qiodevice.sip
%Include qiodevicebase.sip
%Include qitemselectionmodel.sip
%Include qjsondocument.sip
%Include qjsonparseerror.sip
%Include qjsonvalue.sip
%Include qlibrary.sip
%Include qlibraryinfo.sip
%Include qline.sip
%Include qlocale.sip
%Include qlockfile.sip
%Include qlogging.sip
%Include qloggingcategory.sip
%Include qmargins.sip
%Include qmessageauthenticationcode.sip
%Include qmetaobject.sip
%Include qmetatype.sip
%Include qmimedata.sip
%Include qmimedatabase.sip
%Include qmimetype.sip
%Include qmutex.sip
%Include qnumeric.sip
%Include qobject.sip
%Include qobjectcleanuphandler.sip
%Include qobjectdefs.sip
%Include qoperatingsystemversion.sip
%Include qparallelanimationgroup.sip
%Include qpauseanimation.sip
%Include qpermissions.sip
%Include qpropertyanimation.sip
%Include qpluginloader.sip
%Include qpoint.sip
%Include qprocess.sip
%Include qrandom.sip
%Include qreadwritelock.sip
%Include qrect.sip
%Include qregularexpression.sip
%Include qresource.sip
%Include qrunnable.sip
%Include qsavefile.sip
%Include qsemaphore.sip
%Include qsequentialanimationgroup.sip
%Include qsettings.sip
%Include qsharedmemory.sip
%Include qsignalmapper.sip
%Include qsize.sip
%Include qsocketnotifier.sip
%Include qsortfilterproxymodel.sip
%Include qstandardpaths.sip
%Include qstorageinfo.sip
%Include qstringconverter_base.sip
%Include qstringconverter.sip
%Include qstringlistmodel.sip
%Include qsysinfo.sip
%Include qsystemsemaphore.sip
%Include qtemporarydir.sip
%Include qtemporaryfile.sip
%Include qtextboundaryfinder.sip
%Include qtextstream.sip
%Include qthread.sip
%Include qthreadpool.sip
%Include qtimeline.sip
%Include qtimer.sip
%Include qtimezone.sip
%Include qtipccommon.sip
%Include qtranslator.sip
%Include qtransposeproxymodel.sip
%Include qtyperevision.sip
%Include qtypes.sip
%Include qurl.sip
%Include qurlquery.sip
%Include quuid.sip
%Include qvariant.sip
%Include qvariantanimation.sip
%Include qversionnumber.sip
%Include qwaitcondition.sip
%Include qxmlstream.sip
%Include qyieldcpu.sip
%Include qanystringview.sip
%Include qbytearraylist.sip
%Include qbytearrayview.sip
%Include qflags.sip
%Include qjsonarray.sip
%Include qjsonobject.sip
%Include qmutexlocker.sip
%Include qpycore_qhash.sip
%Include qpycore_qlist.sip
%Include qpycore_qmap.sip
%Include qpycore_qset.sip
%Include qpycore_std_chrono_duration.sip
%Include qpycore_std_optional.sip
%Include qpycore_std_pair.sip
%Include qpycore_virtual_error_handler.sip
%Include qstring.sip
%Include qstringlist.sip
%Include qstringview.sip
%Include qwineventnotifier.sip
