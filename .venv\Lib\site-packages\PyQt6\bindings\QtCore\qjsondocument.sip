// qjsondocument.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (- Qt_6_9_0)

struct QJsonParseError
{
%TypeHeaderCode
#include <qjsondocument.h>
%End

    enum ParseError
    {
        NoError,
        UnterminatedObject,
        MissingNameSeparator,
        UnterminatedArray,
        MissingValueSeparator,
        IllegalValue,
        TerminationByNumber,
        IllegalNumber,
        IllegalEscapeSequence,
        IllegalUTF8String,
        UnterminatedString,
        MissingObject,
        DeepNesting,
        DocumentTooLarge,
        GarbageAtEnd,
    };

    QString errorString() const;
    int offset;
    QJsonParseError::ParseError error;
};

%End

class QJsonDocument
{
%TypeHeaderCode
#include <qjsondocument.h>
%End

public:
    QJsonDocument();
    explicit QJsonDocument(const QJsonObject &object);
    explicit QJsonDocument(const QJsonArray &array);
    QJsonDocument(const QJsonDocument &other);
    ~QJsonDocument();
    static QJsonDocument fromVariant(const QVariant &variant);
    QVariant toVariant() const;

    enum JsonFormat
    {
        Indented,
        Compact,
    };

    static QJsonDocument fromJson(const QByteArray &json, QJsonParseError *error = 0);
    QByteArray toJson(QJsonDocument::JsonFormat format = QJsonDocument::Indented) const;
    bool isEmpty() const;
    bool isArray() const;
    bool isObject() const;
    QJsonObject object() const;
    QJsonArray array() const;
    void setObject(const QJsonObject &object);
    void setArray(const QJsonArray &array);
%If (- Qt_6_8_0)
    bool operator==(const QJsonDocument &other) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QJsonDocument &other) const;
%End
    bool isNull() const;
    void swap(QJsonDocument &other /Constrained/);
    const QJsonValue operator[](qsizetype i) const;
    const QJsonValue operator[](const QString &key) const;
};

QDataStream &operator<<(QDataStream &, const QJsonDocument & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QJsonDocument & /Constrained/) /ReleaseGIL/;
%If (Qt_6_8_0 -)
bool operator!=(const QJsonDocument &lhs, const QJsonDocument &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QJsonDocument &lhs, const QJsonDocument &rhs);
%End
