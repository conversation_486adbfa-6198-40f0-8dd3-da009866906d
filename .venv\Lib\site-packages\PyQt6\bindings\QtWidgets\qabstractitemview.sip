// qabstractitemview.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractItemView : public QAbstractScrollArea
{
%TypeHeaderCode
#include <qabstractitemview.h>
%End

public:
    enum DragDropMode
    {
        NoDragDrop,
        DragOnly,
        DropOnly,
        DragDrop,
        InternalMove,
    };

    enum EditTrigger /BaseType=Flag/
    {
        NoEditTriggers,
        CurrentChanged,
        DoubleClicked,
        SelectedClicked,
        EditKeyPressed,
        AnyKeyPressed,
        AllEditTriggers,
    };

    typedef QFlags<QAbstractItemView::EditTrigger> EditTriggers;

    enum ScrollHint
    {
        EnsureVisible,
        PositionAtTop,
        PositionAtBottom,
        PositionAtCenter,
    };

    enum ScrollMode
    {
        ScrollPerItem,
        ScrollPerPixel,
    };

    enum SelectionBehavior
    {
        SelectItems,
        SelectRows,
        SelectColumns,
    };

    enum SelectionMode
    {
        NoSelection,
        SingleSelection,
        MultiSelection,
        ExtendedSelection,
        ContiguousSelection,
    };

    explicit QAbstractItemView(QWidget *parent /TransferThis/ = 0);
    virtual ~QAbstractItemView();
    virtual void setModel(QAbstractItemModel *model /KeepReference/);
    QAbstractItemModel *model() const;
    virtual void setSelectionModel(QItemSelectionModel *selectionModel /KeepReference/);
    QItemSelectionModel *selectionModel() const;
    void setItemDelegate(QAbstractItemDelegate *delegate /KeepReference/);
    QAbstractItemDelegate *itemDelegate() const;
    void setSelectionMode(QAbstractItemView::SelectionMode mode);
    QAbstractItemView::SelectionMode selectionMode() const;
    void setSelectionBehavior(QAbstractItemView::SelectionBehavior behavior);
    QAbstractItemView::SelectionBehavior selectionBehavior() const;
    QModelIndex currentIndex() const;
    QModelIndex rootIndex() const;
    void setEditTriggers(QAbstractItemView::EditTriggers triggers);
    QAbstractItemView::EditTriggers editTriggers() const;
    void setAutoScroll(bool enable);
    bool hasAutoScroll() const;
    void setTabKeyNavigation(bool enable);
    bool tabKeyNavigation() const;
    void setDropIndicatorShown(bool enable);
    bool showDropIndicator() const;
    void setDragEnabled(bool enable);
    bool dragEnabled() const;
    void setAlternatingRowColors(bool enable);
    bool alternatingRowColors() const;
    void setIconSize(const QSize &size);
    QSize iconSize() const;
    void setTextElideMode(Qt::TextElideMode mode);
    Qt::TextElideMode textElideMode() const;
    virtual void keyboardSearch(const QString &search);
    virtual QRect visualRect(const QModelIndex &index) const = 0;
    virtual void scrollTo(const QModelIndex &index, QAbstractItemView::ScrollHint hint = QAbstractItemView::EnsureVisible) = 0;
    virtual QModelIndex indexAt(const QPoint &p) const = 0;
    QSize sizeHintForIndex(const QModelIndex &index) const;
    virtual int sizeHintForRow(int row) const;
    virtual int sizeHintForColumn(int column) const;
    void openPersistentEditor(const QModelIndex &index);
    void closePersistentEditor(const QModelIndex &index);
    void setIndexWidget(const QModelIndex &index, QWidget *widget /Transfer/);
%MethodCode
        // We have to break the association with any existing widget.
        QWidget *w = sipCpp->indexWidget(*a0);
        
        if (w)
        {
            PyObject *wo = sipGetPyObject(w, sipType_QWidget);
        
            if (wo)
                sipTransferTo(wo, 0);
        }
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->setIndexWidget(*a0, a1);
        Py_END_ALLOW_THREADS
%End

    QWidget *indexWidget(const QModelIndex &index) const;

public slots:
    virtual void reset();
    virtual void setRootIndex(const QModelIndex &index);
    virtual void selectAll();
    void edit(const QModelIndex &index);
    void clearSelection();
    void setCurrentIndex(const QModelIndex &index);
    void scrollToTop();
    void scrollToBottom();
    void update();
    void update(const QModelIndex &index);

protected slots:
    virtual void dataChanged(const QModelIndex &topLeft, const QModelIndex &bottomRight, const QList<int> &roles = QList<int>());
    virtual void rowsInserted(const QModelIndex &parent, int start, int end);
    virtual void rowsAboutToBeRemoved(const QModelIndex &parent, int start, int end);
    virtual void selectionChanged(const QItemSelection &selected, const QItemSelection &deselected);
    virtual void currentChanged(const QModelIndex &current, const QModelIndex &previous);
    virtual void updateEditorData();
    virtual void updateEditorGeometries();
    virtual void updateGeometries();
    virtual void verticalScrollbarAction(int action);
    virtual void horizontalScrollbarAction(int action);
    virtual void verticalScrollbarValueChanged(int value);
    virtual void horizontalScrollbarValueChanged(int value);
    virtual void closeEditor(QWidget *editor, QAbstractItemDelegate::EndEditHint hint);
    virtual void commitData(QWidget *editor);
    virtual void editorDestroyed(QObject *editor);

signals:
    void pressed(const QModelIndex &index);
    void clicked(const QModelIndex &index);
    void doubleClicked(const QModelIndex &index);
    void activated(const QModelIndex &index);
    void entered(const QModelIndex &index);
    void viewportEntered();
    void iconSizeChanged(const QSize &size);

protected:
    enum CursorAction
    {
        MoveUp,
        MoveDown,
        MoveLeft,
        MoveRight,
        MoveHome,
        MoveEnd,
        MovePageUp,
        MovePageDown,
        MoveNext,
        MovePrevious,
    };

    virtual QModelIndex moveCursor(QAbstractItemView::CursorAction cursorAction, Qt::KeyboardModifiers modifiers) = 0;
    virtual int horizontalOffset() const = 0;
    virtual int verticalOffset() const = 0;
    virtual bool isIndexHidden(const QModelIndex &index) const = 0;
    virtual void setSelection(const QRect &rect, QItemSelectionModel::SelectionFlags command) = 0;
    virtual QRegion visualRegionForSelection(const QItemSelection &selection) const = 0;
    virtual QModelIndexList selectedIndexes() const;
    virtual bool edit(const QModelIndex &index, QAbstractItemView::EditTrigger trigger, QEvent *event);
    virtual QItemSelectionModel::SelectionFlags selectionCommand(const QModelIndex &index, const QEvent *event = 0) const;
    virtual void startDrag(Qt::DropActions supportedActions);

    enum State
    {
        NoState,
        DraggingState,
        DragSelectingState,
        EditingState,
        ExpandingState,
        CollapsingState,
        AnimatingState,
    };

    QAbstractItemView::State state() const;
    void setState(QAbstractItemView::State state);
    void scheduleDelayedItemsLayout();
    void executeDelayedItemsLayout();
    void scrollDirtyRegion(int dx, int dy);
    void setDirtyRegion(const QRegion &region);
    QPoint dirtyRegionOffset() const;
    virtual bool event(QEvent *event);
    virtual bool viewportEvent(QEvent *e);
    virtual void mousePressEvent(QMouseEvent *e);
    virtual void mouseMoveEvent(QMouseEvent *e);
    virtual void mouseReleaseEvent(QMouseEvent *e);
    virtual void mouseDoubleClickEvent(QMouseEvent *e);
    virtual void dragEnterEvent(QDragEnterEvent *e);
    virtual void dragMoveEvent(QDragMoveEvent *e);
    virtual void dragLeaveEvent(QDragLeaveEvent *e);
    virtual void dropEvent(QDropEvent *e);
    virtual void focusInEvent(QFocusEvent *e);
    virtual void focusOutEvent(QFocusEvent *e);
    virtual void keyPressEvent(QKeyEvent *e);
    virtual void resizeEvent(QResizeEvent *e);
    virtual void timerEvent(QTimerEvent *e);

    enum DropIndicatorPosition
    {
        OnItem,
        AboveItem,
        BelowItem,
        OnViewport,
    };

    QAbstractItemView::DropIndicatorPosition dropIndicatorPosition() const;

public:
    void setVerticalScrollMode(QAbstractItemView::ScrollMode mode);
    QAbstractItemView::ScrollMode verticalScrollMode() const;
    void setHorizontalScrollMode(QAbstractItemView::ScrollMode mode);
    QAbstractItemView::ScrollMode horizontalScrollMode() const;
    void setDragDropOverwriteMode(bool overwrite);
    bool dragDropOverwriteMode() const;
    void setDragDropMode(QAbstractItemView::DragDropMode behavior);
    QAbstractItemView::DragDropMode dragDropMode() const;
    void setItemDelegateForRow(int row, QAbstractItemDelegate *delegate /KeepReference/);
    QAbstractItemDelegate *itemDelegateForRow(int row) const;
    void setItemDelegateForColumn(int column, QAbstractItemDelegate *delegate /KeepReference/);
    QAbstractItemDelegate *itemDelegateForColumn(int column) const;
    virtual QAbstractItemDelegate *itemDelegateForIndex(const QModelIndex &index) const;
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery query) const;
    void setAutoScrollMargin(int margin);
    int autoScrollMargin() const;

protected:
    virtual bool focusNextPrevChild(bool next);
    virtual void inputMethodEvent(QInputMethodEvent *event);
    virtual QSize viewportSizeHint() const;
    virtual bool eventFilter(QObject *object, QEvent *event);

public:
    void setDefaultDropAction(Qt::DropAction dropAction);
    Qt::DropAction defaultDropAction() const;
    void resetVerticalScrollMode();
    void resetHorizontalScrollMode();
    bool isPersistentEditorOpen(const QModelIndex &index) const;

protected:
    virtual void initViewItemOption(QStyleOptionViewItem *option) const;

public:
%If (Qt_6_9_0 -)
    int updateThreshold() const;
%End
%If (Qt_6_9_0 -)
    void setUpdateThreshold(int threshold);
%End
};
