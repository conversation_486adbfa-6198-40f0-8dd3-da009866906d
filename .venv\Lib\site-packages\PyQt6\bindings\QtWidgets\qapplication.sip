// qapplication.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


typedef QList<QWidget *> QWidgetList;

class QApplication : public QGuiApplication
{
%TypeHeaderCode
#include <qapplication.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QWidget, &sipType_QWidget, 18, 1},
        {sipName_QAbstractItemDelegate, &sipType_QAbstractItemDelegate, 89, 2},
        {sipName_QApplication, &sipType_QApplication, -1, 3},
        {sipName_QLayout, &sipType_QLayout, 91, 4},
        {sipName_QButtonGroup, &sipType_QButtonGroup, -1, 5},
        {sipName_QStyle, &sipType_QStyle, 97, 6},
        {sipName_QCompleter, &sipType_QCompleter, -1, 7},
        {sipName_QDataWidgetMapper, &sipType_QDataWidgetMapper, -1, 8},
        {sipName_QGesture, &sipType_QGesture, 99, 9},
        {sipName_QGraphicsAnchor, &sipType_QGraphicsAnchor, -1, 10},
        {sipName_QGraphicsEffect, &sipType_QGraphicsEffect, 104, 11},
        {sipName_QGraphicsObject, &sipType_QGraphicsObject, 108, 12},
        {sipName_QGraphicsTransform, &sipType_QGraphicsTransform, 111, 13},
        {sipName_QGraphicsScene, &sipType_QGraphicsScene, -1, 14},
        {sipName_QPlainTextDocumentLayout, &sipType_QPlainTextDocumentLayout, -1, 15},
        {sipName_QScroller, &sipType_QScroller, -1, 16},
        {sipName_QSystemTrayIcon, &sipType_QSystemTrayIcon, -1, 17},
        {sipName_QWidgetAction, &sipType_QWidgetAction, -1, -1},
        {sipName_QAbstractButton, &sipType_QAbstractButton, 45, 19},
        {sipName_QFrame, &sipType_QFrame, 50, 20},
        {sipName_QAbstractSlider, &sipType_QAbstractSlider, 72, 21},
        {sipName_QAbstractSpinBox, &sipType_QAbstractSpinBox, 75, 22},
        {sipName_QCalendarWidget, &sipType_QCalendarWidget, -1, 23},
        {sipName_QDialog, &sipType_QDialog, 80, 24},
        {sipName_QComboBox, &sipType_QComboBox, 88, 25},
        {sipName_QDialogButtonBox, &sipType_QDialogButtonBox, -1, 26},
        {sipName_QDockWidget, &sipType_QDockWidget, -1, 27},
        {sipName_QFocusFrame, &sipType_QFocusFrame, -1, 28},
        {sipName_QGroupBox, &sipType_QGroupBox, -1, 29},
        {sipName_QKeySequenceEdit, &sipType_QKeySequenceEdit, -1, 30},
        {sipName_QLineEdit, &sipType_QLineEdit, -1, 31},
        {sipName_QMainWindow, &sipType_QMainWindow, -1, 32},
        {sipName_QMdiSubWindow, &sipType_QMdiSubWindow, -1, 33},
        {sipName_QMenu, &sipType_QMenu, -1, 34},
        {sipName_QMenuBar, &sipType_QMenuBar, -1, 35},
        {sipName_QProgressBar, &sipType_QProgressBar, -1, 36},
        {sipName_QRubberBand, &sipType_QRubberBand, -1, 37},
        {sipName_QSizeGrip, &sipType_QSizeGrip, -1, 38},
        {sipName_QSplashScreen, &sipType_QSplashScreen, -1, 39},
        {sipName_QSplitterHandle, &sipType_QSplitterHandle, -1, 40},
        {sipName_QStatusBar, &sipType_QStatusBar, -1, 41},
        {sipName_QTabBar, &sipType_QTabBar, -1, 42},
        {sipName_QTabWidget, &sipType_QTabWidget, -1, 43},
        {sipName_QToolBar, &sipType_QToolBar, -1, 44},
        {sipName_QWizardPage, &sipType_QWizardPage, -1, -1},
        {sipName_QCheckBox, &sipType_QCheckBox, -1, 46},
        {sipName_QPushButton, &sipType_QPushButton, 49, 47},
        {sipName_QRadioButton, &sipType_QRadioButton, -1, 48},
        {sipName_QToolButton, &sipType_QToolButton, -1, -1},
        {sipName_QCommandLinkButton, &sipType_QCommandLinkButton, -1, -1},
        {sipName_QAbstractScrollArea, &sipType_QAbstractScrollArea, 56, 51},
        {sipName_QLCDNumber, &sipType_QLCDNumber, -1, 52},
        {sipName_QLabel, &sipType_QLabel, -1, 53},
        {sipName_QSplitter, &sipType_QSplitter, -1, 54},
        {sipName_QStackedWidget, &sipType_QStackedWidget, -1, 55},
        {sipName_QToolBox, &sipType_QToolBox, -1, -1},
        {sipName_QAbstractItemView, &sipType_QAbstractItemView, 62, 57},
        {sipName_QGraphicsView, &sipType_QGraphicsView, -1, 58},
        {sipName_QMdiArea, &sipType_QMdiArea, -1, 59},
        {sipName_QPlainTextEdit, &sipType_QPlainTextEdit, -1, 60},
        {sipName_QScrollArea, &sipType_QScrollArea, -1, 61},
        {sipName_QTextEdit, &sipType_QTextEdit, 71, -1},
        {sipName_QColumnView, &sipType_QColumnView, -1, 63},
        {sipName_QHeaderView, &sipType_QHeaderView, -1, 64},
        {sipName_QListView, &sipType_QListView, 67, 65},
        {sipName_QTableView, &sipType_QTableView, 69, 66},
        {sipName_QTreeView, &sipType_QTreeView, 70, -1},
        {sipName_QListWidget, &sipType_QListWidget, -1, 68},
        {sipName_QUndoView, &sipType_QUndoView, -1, -1},
        {sipName_QTableWidget, &sipType_QTableWidget, -1, -1},
        {sipName_QTreeWidget, &sipType_QTreeWidget, -1, -1},
        {sipName_QTextBrowser, &sipType_QTextBrowser, -1, -1},
        {sipName_QDial, &sipType_QDial, -1, 73},
        {sipName_QScrollBar, &sipType_QScrollBar, -1, 74},
        {sipName_QSlider, &sipType_QSlider, -1, -1},
        {sipName_QDateTimeEdit, &sipType_QDateTimeEdit, 78, 76},
        {sipName_QDoubleSpinBox, &sipType_QDoubleSpinBox, -1, 77},
        {sipName_QSpinBox, &sipType_QSpinBox, -1, -1},
        {sipName_QDateEdit, &sipType_QDateEdit, -1, 79},
        {sipName_QTimeEdit, &sipType_QTimeEdit, -1, -1},
        {sipName_QColorDialog, &sipType_QColorDialog, -1, 81},
        {sipName_QErrorMessage, &sipType_QErrorMessage, -1, 82},
        {sipName_QFileDialog, &sipType_QFileDialog, -1, 83},
        {sipName_QFontDialog, &sipType_QFontDialog, -1, 84},
        {sipName_QInputDialog, &sipType_QInputDialog, -1, 85},
        {sipName_QMessageBox, &sipType_QMessageBox, -1, 86},
        {sipName_QProgressDialog, &sipType_QProgressDialog, -1, 87},
        {sipName_QWizard, &sipType_QWizard, -1, -1},
        {sipName_QFontComboBox, &sipType_QFontComboBox, -1, -1},
        {sipName_QItemDelegate, &sipType_QItemDelegate, -1, 90},
        {sipName_QStyledItemDelegate, &sipType_QStyledItemDelegate, -1, -1},
        {sipName_QBoxLayout, &sipType_QBoxLayout, 95, 92},
        {sipName_QFormLayout, &sipType_QFormLayout, -1, 93},
        {sipName_QGridLayout, &sipType_QGridLayout, -1, 94},
        {sipName_QStackedLayout, &sipType_QStackedLayout, -1, -1},
        {sipName_QHBoxLayout, &sipType_QHBoxLayout, -1, 96},
        {sipName_QVBoxLayout, &sipType_QVBoxLayout, -1, -1},
        {sipName_QCommonStyle, &sipType_QCommonStyle, 98, -1},
        {sipName_QProxyStyle, &sipType_QProxyStyle, -1, -1},
        {sipName_QPanGesture, &sipType_QPanGesture, -1, 100},
        {sipName_QPinchGesture, &sipType_QPinchGesture, -1, 101},
        {sipName_QSwipeGesture, &sipType_QSwipeGesture, -1, 102},
        {sipName_QTapAndHoldGesture, &sipType_QTapAndHoldGesture, -1, 103},
        {sipName_QTapGesture, &sipType_QTapGesture, -1, -1},
        {sipName_QGraphicsBlurEffect, &sipType_QGraphicsBlurEffect, -1, 105},
        {sipName_QGraphicsColorizeEffect, &sipType_QGraphicsColorizeEffect, -1, 106},
        {sipName_QGraphicsDropShadowEffect, &sipType_QGraphicsDropShadowEffect, -1, 107},
        {sipName_QGraphicsOpacityEffect, &sipType_QGraphicsOpacityEffect, -1, -1},
        {sipName_QGraphicsWidget, &sipType_QGraphicsWidget, 110, 109},
        {sipName_QGraphicsTextItem, &sipType_QGraphicsTextItem, -1, -1},
        {sipName_QGraphicsProxyWidget, &sipType_QGraphicsProxyWidget, -1, -1},
        {sipName_QGraphicsRotation, &sipType_QGraphicsRotation, -1, 112},
        {sipName_QGraphicsScale, &sipType_QGraphicsScale, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QApplication(SIP_PYLIST argv /TypeHint="List[str]"/) /PostHook=__pyQtQAppHook__/ [(int &argc, char **argv, int = QCoreApplication::ApplicationFlags)];
%MethodCode
        // The Python interface is a list of argument strings that is modified.
        
        int argc;
        char **argv;
        
        // Convert the list.
        if ((argv = pyqt6_qtwidgets_from_argv_list(a0, argc)) == NULL)
            sipIsErr = 1;
        else
        {
            // Create it now the arguments are right.
            static int nargc;
            nargc = argc;
        
            Py_BEGIN_ALLOW_THREADS
            sipCpp = new sipQApplication(nargc, argv, QCoreApplication::ApplicationFlags);
            Py_END_ALLOW_THREADS
        
            // Now modify the original list.
            pyqt6_qtwidgets_update_argv_list(a0, argc, argv);
        }
%End

    virtual ~QApplication() /ReleaseGIL/;
%MethodCode
        pyqt6_qtwidgets_cleanup_qobjects();
%End

    static QStyle *style();
    static void setStyle(QStyle * /Transfer/);
    static QStyle *setStyle(const QString &);
    static QPalette palette();
    static QPalette palette(const QWidget *);
    static QPalette palette(const char *className);
    static void setPalette(const QPalette &, const char *className = 0);
    static QFont font();
    static QFont font(const QWidget *);
    static QFont font(const char *className);
    static void setFont(const QFont &, const char *className = 0);
    static QWidgetList allWidgets();
    static QWidgetList topLevelWidgets();
    static QWidget *activePopupWidget();
    static QWidget *activeModalWidget();
    static QWidget *focusWidget();
    static QWidget *activeWindow();
    static void setActiveWindow(QWidget *act);
    static QWidget *widgetAt(const QPoint &p);
    static QWidget *widgetAt(int x, int y);
    static QWidget *topLevelAt(const QPoint &p);
    static QWidget *topLevelAt(int x, int y);
    static void beep();
    static void alert(QWidget *widget, int msecs = 0) /ReleaseGIL/;
    static void setCursorFlashTime(int);
    static int cursorFlashTime();
    static void setDoubleClickInterval(int);
    static int doubleClickInterval();
    static void setKeyboardInputInterval(int);
    static int keyboardInputInterval();
    static void setWheelScrollLines(int);
    static int wheelScrollLines();
    static void setStartDragTime(int ms);
    static int startDragTime();
    static void setStartDragDistance(int l);
    static int startDragDistance();
    static bool isEffectEnabled(Qt::UIEffect);
    static void setEffectEnabled(Qt::UIEffect, bool enabled = true);
    static int exec() /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,ReleaseGIL/;
    virtual bool notify(QObject *, QEvent *) /ReleaseGIL/;
    bool autoSipEnabled() const;
    QString styleSheet() const;

signals:
    void focusChanged(QWidget *old, QWidget *now);

public slots:
    static void aboutQt();
    static void closeAllWindows();
    void setAutoSipEnabled(const bool enabled);
    void setStyleSheet(const QString &sheet);

protected:
    virtual bool event(QEvent *);
};

%ModuleHeaderCode
// Imports from QtCore.
typedef void (*pyqt6_qtwidgets_cleanup_qobjects_t)();
extern pyqt6_qtwidgets_cleanup_qobjects_t pyqt6_qtwidgets_cleanup_qobjects;

typedef char **(*pyqt6_qtwidgets_from_argv_list_t)(PyObject *, int &);
extern pyqt6_qtwidgets_from_argv_list_t pyqt6_qtwidgets_from_argv_list;

typedef sipErrorState (*pyqt6_qtwidgets_get_connection_parts_t)(PyObject *, QObject *, const char *, bool, QObject **, QByteArray &);
extern pyqt6_qtwidgets_get_connection_parts_t pyqt6_qtwidgets_get_connection_parts;

typedef sipErrorState (*pyqt6_qtwidgets_get_signal_signature_t)(PyObject *, QObject *, QByteArray &);
extern pyqt6_qtwidgets_get_signal_signature_t pyqt6_qtwidgets_get_signal_signature;

typedef void (*pyqt6_qtwidgets_update_argv_list_t)(PyObject *, int, char **);
extern pyqt6_qtwidgets_update_argv_list_t pyqt6_qtwidgets_update_argv_list;

// This is needed for Qt v5.0.0.
#if defined(B0)
#undef B0
#endif
%End

%ModuleCode
#include "qpywidgets_api.h"

// Imports from QtCore.
pyqt6_qtwidgets_cleanup_qobjects_t pyqt6_qtwidgets_cleanup_qobjects;
pyqt6_qtwidgets_from_argv_list_t pyqt6_qtwidgets_from_argv_list;
pyqt6_qtwidgets_get_connection_parts_t pyqt6_qtwidgets_get_connection_parts;
pyqt6_qtwidgets_get_signal_signature_t pyqt6_qtwidgets_get_signal_signature;
pyqt6_qtwidgets_update_argv_list_t pyqt6_qtwidgets_update_argv_list;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtwidgets_cleanup_qobjects = (pyqt6_qtwidgets_cleanup_qobjects_t)sipImportSymbol("pyqt6_cleanup_qobjects");
Q_ASSERT(pyqt6_qtwidgets_cleanup_qobjects);

pyqt6_qtwidgets_from_argv_list = (pyqt6_qtwidgets_from_argv_list_t)sipImportSymbol("pyqt6_from_argv_list");
Q_ASSERT(pyqt6_qtwidgets_from_argv_list);

pyqt6_qtwidgets_get_connection_parts = (pyqt6_qtwidgets_get_connection_parts_t)sipImportSymbol("pyqt6_get_connection_parts");
Q_ASSERT(pyqt6_qtwidgets_get_connection_parts);

pyqt6_qtwidgets_get_signal_signature = (pyqt6_qtwidgets_get_signal_signature_t)sipImportSymbol("pyqt6_get_signal_signature");
Q_ASSERT(pyqt6_qtwidgets_get_signal_signature);

pyqt6_qtwidgets_update_argv_list = (pyqt6_qtwidgets_update_argv_list_t)sipImportSymbol("pyqt6_update_argv_list");
Q_ASSERT(pyqt6_qtwidgets_update_argv_list);

qpywidgets_post_init();
%End
