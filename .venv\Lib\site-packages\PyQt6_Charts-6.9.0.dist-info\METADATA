Metadata-Version: 2.1
Name: PyQt6-Charts
Version: 6.9.0
Requires-Python: >=3.9
Summary: Python bindings for the Qt Charts library
Description-Content-Type: text/markdown
Project-Url: homepage, https://www.riverbankcomputing.com/software/pyqtchart/
Requires-Dist: PyQt6-sip (>=13.8, <14)
Requires-Dist: PyQt6-Charts-Qt6 (>=6.9.0, <6.10.0)
Requires-Dist: PyQt6 (>=6.2.0)
License: GPL v3
Author-Email: Riverbank Computing Limited <<EMAIL>>

# PyQt6-Charts - Python Bindings for the Qt Charts Library

PyQt6-Charts is a set of Python bindings for The Qt Company's Qt Charts
library.  The bindings sit on top of PyQt6 and are implemented as a single
module.


## Author

PyQt6-Charts is copyright (c) Riverbank Computing Limited.  Its homepage is
https://www.riverbankcomputing.com/software/pyqtchart/.

Support may be obtained from the PyQt mailing list at
https://www.riverbankcomputing.com/mailman/listinfo/pyqt/.


## License

PyQt6-Charts is released under the GPL v3 license and under a commercial
license that allows for the development of proprietary applications.


## Documentation

The documentation for the latest release can be found
[here](https://www.riverbankcomputing.com/static/Docs/PyQt6/).


## Installation

The GPL version of PyQt6-Charts can be installed from PyPI:

    pip install PyQt6-Charts

`pip` will also build and install the bindings from the sdist package but Qt's
`qmake` tool must be on `PATH`.

The `sip-install` tool will also install the bindings from the sdist package
but will allow you to configure many aspects of the installation.
