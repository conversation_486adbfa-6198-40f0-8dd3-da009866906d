# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.Qt3DExtras, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.Qt3DExtras`

import PySide6.Qt3DExtras
import PySide6.QtCore
import PySide6.QtGui
import PySide6.Qt3DCore
import PySide6.Qt3DRender

import typing
import collections
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QIntList: ...


class Qt3DExtras(Shiboken.Object):

    class QAbstractCameraController(PySide6.Qt3DCore.Qt3DCore.QEntity):

        accelerationChanged      : typing.ClassVar[Signal] = ... # accelerationChanged(float)
        cameraChanged            : typing.ClassVar[Signal] = ... # cameraChanged()
        decelerationChanged      : typing.ClassVar[Signal] = ... # decelerationChanged(float)
        linearSpeedChanged       : typing.ClassVar[Signal] = ... # linearSpeedChanged()
        lookSpeedChanged         : typing.ClassVar[Signal] = ... # lookSpeedChanged()

        class InputState(Shiboken.Object):

            @typing.overload
            def __init__(self, /) -> None: ...
            @typing.overload
            def __init__(self, InputState: PySide6.Qt3DExtras.Qt3DExtras.QAbstractCameraController.InputState, /) -> None: ...

            def __copy__(self, /) -> typing.Self: ...


        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, camera: PySide6.Qt3DRender.Qt3DRender.QCamera | None = ..., linearSpeed: float | None = ..., lookSpeed: float | None = ..., acceleration: float | None = ..., deceleration: float | None = ...) -> None: ...

        def acceleration(self, /) -> float: ...
        def camera(self, /) -> PySide6.Qt3DRender.Qt3DRender.QCamera: ...
        def deceleration(self, /) -> float: ...
        def linearSpeed(self, /) -> float: ...
        def lookSpeed(self, /) -> float: ...
        def setAcceleration(self, acceleration: float, /) -> None: ...
        def setCamera(self, camera: PySide6.Qt3DRender.Qt3DRender.QCamera, /) -> None: ...
        def setDeceleration(self, deceleration: float, /) -> None: ...
        def setLinearSpeed(self, linearSpeed: float, /) -> None: ...
        def setLookSpeed(self, lookSpeed: float, /) -> None: ...

    class QAbstractSpriteSheet(PySide6.Qt3DCore.Qt3DCore.QNode):

        currentIndexChanged      : typing.ClassVar[Signal] = ... # currentIndexChanged(int)
        textureChanged           : typing.ClassVar[Signal] = ... # textureChanged(Qt3DRender::QAbstractTexture*)
        textureTransformChanged  : typing.ClassVar[Signal] = ... # textureTransformChanged(QMatrix3x3)
        def currentIndex(self, /) -> int: ...
        def setCurrentIndex(self, currentIndex: int, /) -> None: ...
        def setTexture(self, texture: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, /) -> None: ...
        def texture(self, /) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def textureTransform(self, /) -> PySide6.QtGui.QMatrix3x3: ...

    class QConeGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        bottomRadiusChanged      : typing.ClassVar[Signal] = ... # bottomRadiusChanged(float)
        hasBottomEndcapChanged   : typing.ClassVar[Signal] = ... # hasBottomEndcapChanged(bool)
        hasTopEndcapChanged      : typing.ClassVar[Signal] = ... # hasTopEndcapChanged(bool)
        lengthChanged            : typing.ClassVar[Signal] = ... # lengthChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)
        topRadiusChanged         : typing.ClassVar[Signal] = ... # topRadiusChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, hasTopEndcap: bool | None = ..., hasBottomEndcap: bool | None = ..., rings: int | None = ..., slices: int | None = ..., topRadius: float | None = ..., bottomRadius: float | None = ..., length: float | None = ..., positionAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., normalAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., texCoordAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., indexAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ...) -> None: ...

        def bottomRadius(self, /) -> float: ...
        def hasBottomEndcap(self, /) -> bool: ...
        def hasTopEndcap(self, /) -> bool: ...
        def indexAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def length(self, /) -> float: ...
        def normalAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def rings(self, /) -> int: ...
        def setBottomRadius(self, bottomRadius: float, /) -> None: ...
        def setHasBottomEndcap(self, hasBottomEndcap: bool, /) -> None: ...
        def setHasTopEndcap(self, hasTopEndcap: bool, /) -> None: ...
        def setLength(self, length: float, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def setTopRadius(self, topRadius: float, /) -> None: ...
        def slices(self, /) -> int: ...
        def texCoordAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def topRadius(self, /) -> float: ...
        def updateIndices(self, /) -> None: ...
        def updateVertices(self, /) -> None: ...

    class QConeGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        bottomRadiusChanged      : typing.ClassVar[Signal] = ... # bottomRadiusChanged(float)
        hasBottomEndcapChanged   : typing.ClassVar[Signal] = ... # hasBottomEndcapChanged(bool)
        hasTopEndcapChanged      : typing.ClassVar[Signal] = ... # hasTopEndcapChanged(bool)
        lengthChanged            : typing.ClassVar[Signal] = ... # lengthChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)
        topRadiusChanged         : typing.ClassVar[Signal] = ... # topRadiusChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., hasTopEndcap: bool | None = ..., hasBottomEndcap: bool | None = ..., topRadius: float | None = ..., bottomRadius: float | None = ..., length: float | None = ...) -> None: ...

        def bottomRadius(self, /) -> float: ...
        def hasBottomEndcap(self, /) -> bool: ...
        def hasTopEndcap(self, /) -> bool: ...
        def length(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setBottomRadius(self, bottomRadius: float, /) -> None: ...
        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setHasBottomEndcap(self, hasBottomEndcap: bool, /) -> None: ...
        def setHasTopEndcap(self, hasTopEndcap: bool, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setInstanceCount(self, instanceCount: int, /) -> None: ...
        def setLength(self, length: float, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def setTopRadius(self, topRadius: float, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def slices(self, /) -> int: ...
        def topRadius(self, /) -> float: ...

    class QConeMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        bottomRadiusChanged      : typing.ClassVar[Signal] = ... # bottomRadiusChanged(float)
        hasBottomEndcapChanged   : typing.ClassVar[Signal] = ... # hasBottomEndcapChanged(bool)
        hasTopEndcapChanged      : typing.ClassVar[Signal] = ... # hasTopEndcapChanged(bool)
        lengthChanged            : typing.ClassVar[Signal] = ... # lengthChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)
        topRadiusChanged         : typing.ClassVar[Signal] = ... # topRadiusChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., hasTopEndcap: bool | None = ..., hasBottomEndcap: bool | None = ..., topRadius: float | None = ..., bottomRadius: float | None = ..., length: float | None = ...) -> None: ...

        def bottomRadius(self, /) -> float: ...
        def hasBottomEndcap(self, /) -> bool: ...
        def hasTopEndcap(self, /) -> bool: ...
        def length(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setBottomRadius(self, bottomRadius: float, /) -> None: ...
        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setHasBottomEndcap(self, hasBottomEndcap: bool, /) -> None: ...
        def setHasTopEndcap(self, hasTopEndcap: bool, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setInstanceCount(self, instanceCount: int, /) -> None: ...
        def setLength(self, length: float, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def setTopRadius(self, topRadius: float, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def slices(self, /) -> int: ...
        def topRadius(self, /) -> float: ...

    class QCuboidGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        xExtentChanged           : typing.ClassVar[Signal] = ... # xExtentChanged(float)
        xyMeshResolutionChanged  : typing.ClassVar[Signal] = ... # xyMeshResolutionChanged(QSize)
        xzMeshResolutionChanged  : typing.ClassVar[Signal] = ... # xzMeshResolutionChanged(QSize)
        yExtentChanged           : typing.ClassVar[Signal] = ... # yExtentChanged(float)
        yzMeshResolutionChanged  : typing.ClassVar[Signal] = ... # yzMeshResolutionChanged(QSize)
        zExtentChanged           : typing.ClassVar[Signal] = ... # zExtentChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, xExtent: float | None = ..., yExtent: float | None = ..., zExtent: float | None = ..., xyMeshResolution: PySide6.QtCore.QSize | None = ..., yzMeshResolution: PySide6.QtCore.QSize | None = ..., xzMeshResolution: PySide6.QtCore.QSize | None = ..., positionAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., normalAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., texCoordAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., tangentAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., indexAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ...) -> None: ...

        def indexAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def normalAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def setXExtent(self, xExtent: float, /) -> None: ...
        def setXYMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setXZMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setYExtent(self, yExtent: float, /) -> None: ...
        def setYZMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setZExtent(self, zExtent: float, /) -> None: ...
        def tangentAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def texCoordAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def updateIndices(self, /) -> None: ...
        def updateVertices(self, /) -> None: ...
        def xExtent(self, /) -> float: ...
        def xyMeshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def xzMeshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def yExtent(self, /) -> float: ...
        def yzMeshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def zExtent(self, /) -> float: ...

    class QCuboidGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        xExtentChanged           : typing.ClassVar[Signal] = ... # xExtentChanged(float)
        xyMeshResolutionChanged  : typing.ClassVar[Signal] = ... # xyMeshResolutionChanged(QSize)
        xzMeshResolutionChanged  : typing.ClassVar[Signal] = ... # xzMeshResolutionChanged(QSize)
        yExtentChanged           : typing.ClassVar[Signal] = ... # yExtentChanged(float)
        yzMeshResolutionChanged  : typing.ClassVar[Signal] = ... # yzMeshResolutionChanged(QSize)
        zExtentChanged           : typing.ClassVar[Signal] = ... # zExtentChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, xExtent: float | None = ..., yExtent: float | None = ..., zExtent: float | None = ..., yzMeshResolution: PySide6.QtCore.QSize | None = ..., xzMeshResolution: PySide6.QtCore.QSize | None = ..., xyMeshResolution: PySide6.QtCore.QSize | None = ...) -> None: ...

        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setInstanceCount(self, instanceCount: int, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def setXExtent(self, xExtent: float, /) -> None: ...
        def setXYMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setXZMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setYExtent(self, yExtent: float, /) -> None: ...
        def setYZMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setZExtent(self, zExtent: float, /) -> None: ...
        def xExtent(self, /) -> float: ...
        def xyMeshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def xzMeshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def yExtent(self, /) -> float: ...
        def yzMeshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def zExtent(self, /) -> float: ...

    class QCuboidMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        xExtentChanged           : typing.ClassVar[Signal] = ... # xExtentChanged(float)
        xyMeshResolutionChanged  : typing.ClassVar[Signal] = ... # xyMeshResolutionChanged(QSize)
        xzMeshResolutionChanged  : typing.ClassVar[Signal] = ... # xzMeshResolutionChanged(QSize)
        yExtentChanged           : typing.ClassVar[Signal] = ... # yExtentChanged(float)
        yzMeshResolutionChanged  : typing.ClassVar[Signal] = ... # yzMeshResolutionChanged(QSize)
        zExtentChanged           : typing.ClassVar[Signal] = ... # zExtentChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, xExtent: float | None = ..., yExtent: float | None = ..., zExtent: float | None = ..., yzMeshResolution: PySide6.QtCore.QSize | None = ..., xzMeshResolution: PySide6.QtCore.QSize | None = ..., xyMeshResolution: PySide6.QtCore.QSize | None = ...) -> None: ...

        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setInstanceCount(self, instanceCount: int, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def setXExtent(self, xExtent: float, /) -> None: ...
        def setXYMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setXZMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setYExtent(self, yExtent: float, /) -> None: ...
        def setYZMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setZExtent(self, zExtent: float, /) -> None: ...
        def xExtent(self, /) -> float: ...
        def xyMeshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def xzMeshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def yExtent(self, /) -> float: ...
        def yzMeshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def zExtent(self, /) -> float: ...

    class QCylinderGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        lengthChanged            : typing.ClassVar[Signal] = ... # lengthChanged(float)
        radiusChanged            : typing.ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., radius: float | None = ..., length: float | None = ..., positionAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., normalAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., texCoordAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., indexAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ...) -> None: ...

        def indexAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def length(self, /) -> float: ...
        def normalAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def radius(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setLength(self, length: float, /) -> None: ...
        def setRadius(self, radius: float, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def slices(self, /) -> int: ...
        def texCoordAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def updateIndices(self, /) -> None: ...
        def updateVertices(self, /) -> None: ...

    class QCylinderGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        lengthChanged            : typing.ClassVar[Signal] = ... # lengthChanged(float)
        radiusChanged            : typing.ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., radius: float | None = ..., length: float | None = ...) -> None: ...

        def length(self, /) -> float: ...
        def radius(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setInstanceCount(self, instanceCount: int, /) -> None: ...
        def setLength(self, length: float, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType, /) -> None: ...
        def setRadius(self, radius: float, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def slices(self, /) -> int: ...

    class QCylinderMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        lengthChanged            : typing.ClassVar[Signal] = ... # lengthChanged(float)
        radiusChanged            : typing.ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., radius: float | None = ..., length: float | None = ...) -> None: ...

        def length(self, /) -> float: ...
        def radius(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setInstanceCount(self, instanceCount: int, /) -> None: ...
        def setLength(self, length: float, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType, /) -> None: ...
        def setRadius(self, radius: float, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def slices(self, /) -> int: ...

    class QDiffuseMapMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : typing.ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : typing.ClassVar[Signal] = ... # diffuseChanged(Qt3DRender::QAbstractTexture*)
        shininessChanged         : typing.ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : typing.ClassVar[Signal] = ... # specularChanged(QColor)
        textureScaleChanged      : typing.ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, ambient: PySide6.QtGui.QColor | None = ..., specular: PySide6.QtGui.QColor | None = ..., shininess: float | None = ..., diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture | None = ..., textureScale: float | None = ...) -> None: ...

        def ambient(self, /) -> PySide6.QtGui.QColor: ...
        def diffuse(self, /) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def setAmbient(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setDiffuse(self, diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, /) -> None: ...
        def setShininess(self, shininess: float, /) -> None: ...
        def setSpecular(self, specular: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setTextureScale(self, textureScale: float, /) -> None: ...
        def shininess(self, /) -> float: ...
        def specular(self, /) -> PySide6.QtGui.QColor: ...
        def textureScale(self, /) -> float: ...

    class QDiffuseSpecularMapMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : typing.ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : typing.ClassVar[Signal] = ... # diffuseChanged(Qt3DRender::QAbstractTexture*)
        shininessChanged         : typing.ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : typing.ClassVar[Signal] = ... # specularChanged(Qt3DRender::QAbstractTexture*)
        textureScaleChanged      : typing.ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, ambient: PySide6.QtGui.QColor | None = ..., shininess: float | None = ..., specular: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture | None = ..., diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture | None = ..., textureScale: float | None = ...) -> None: ...

        def ambient(self, /) -> PySide6.QtGui.QColor: ...
        def diffuse(self, /) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def setAmbient(self, ambient: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setDiffuse(self, diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, /) -> None: ...
        def setShininess(self, shininess: float, /) -> None: ...
        def setSpecular(self, specular: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, /) -> None: ...
        def setTextureScale(self, textureScale: float, /) -> None: ...
        def shininess(self, /) -> float: ...
        def specular(self, /) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def textureScale(self, /) -> float: ...

    class QDiffuseSpecularMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        alphaBlendingEnabledChanged: typing.ClassVar[Signal] = ... # alphaBlendingEnabledChanged(bool)
        ambientChanged           : typing.ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : typing.ClassVar[Signal] = ... # diffuseChanged(QVariant)
        normalChanged            : typing.ClassVar[Signal] = ... # normalChanged(QVariant)
        shininessChanged         : typing.ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : typing.ClassVar[Signal] = ... # specularChanged(QVariant)
        textureScaleChanged      : typing.ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, ambient: PySide6.QtGui.QColor | None = ..., diffuse: typing.Optional[typing.Any] = ..., specular: typing.Optional[typing.Any] = ..., shininess: float | None = ..., normal: typing.Optional[typing.Any] = ..., textureScale: float | None = ..., alphaBlending: bool | None = ...) -> None: ...

        def ambient(self, /) -> PySide6.QtGui.QColor: ...
        def diffuse(self, /) -> typing.Any: ...
        def isAlphaBlendingEnabled(self, /) -> bool: ...
        def normal(self, /) -> typing.Any: ...
        def setAlphaBlendingEnabled(self, enabled: bool, /) -> None: ...
        def setAmbient(self, ambient: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setDiffuse(self, diffuse: typing.Any, /) -> None: ...
        def setNormal(self, normal: typing.Any, /) -> None: ...
        def setShininess(self, shininess: float, /) -> None: ...
        def setSpecular(self, specular: typing.Any, /) -> None: ...
        def setTextureScale(self, textureScale: float, /) -> None: ...
        def shininess(self, /) -> float: ...
        def specular(self, /) -> typing.Any: ...
        def textureScale(self, /) -> float: ...

    class QExtrudedTextGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        depthChanged             : typing.ClassVar[Signal] = ... # depthChanged(float)
        fontChanged              : typing.ClassVar[Signal] = ... # fontChanged(QFont)
        textChanged              : typing.ClassVar[Signal] = ... # textChanged(QString)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, text: str | None = ..., font: PySide6.QtGui.QFont | None = ..., extrusionLength: float | None = ..., positionAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., normalAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., indexAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ...) -> None: ...

        def extrusionLength(self, /) -> float: ...
        def font(self, /) -> PySide6.QtGui.QFont: ...
        def indexAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def normalAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def setDepth(self, extrusionLength: float, /) -> None: ...
        def setFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
        def setText(self, text: str, /) -> None: ...
        def text(self, /) -> str: ...

    class QExtrudedTextMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        depthChanged             : typing.ClassVar[Signal] = ... # depthChanged(float)
        fontChanged              : typing.ClassVar[Signal] = ... # fontChanged(QFont)
        textChanged              : typing.ClassVar[Signal] = ... # textChanged(QString)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, text: str | None = ..., font: PySide6.QtGui.QFont | None = ..., depth: float | None = ...) -> None: ...

        def depth(self, /) -> float: ...
        def font(self, /) -> PySide6.QtGui.QFont: ...
        def setDepth(self, depth: float, /) -> None: ...
        def setFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
        def setText(self, text: str, /) -> None: ...
        def text(self, /) -> str: ...

    class QFirstPersonCameraController(PySide6.Qt3DExtras.Qt3DExtras.QAbstractCameraController):

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ...) -> None: ...


    class QForwardRenderer(PySide6.Qt3DRender.Qt3DRender.QTechniqueFilter):

        buffersToClearChanged    : typing.ClassVar[Signal] = ... # buffersToClearChanged(Qt3DRender::QClearBuffers::BufferType)
        cameraChanged            : typing.ClassVar[Signal] = ... # cameraChanged(Qt3DCore::QEntity*)
        clearColorChanged        : typing.ClassVar[Signal] = ... # clearColorChanged(QColor)
        externalRenderTargetSizeChanged: typing.ClassVar[Signal] = ... # externalRenderTargetSizeChanged(QSize)
        frustumCullingEnabledChanged: typing.ClassVar[Signal] = ... # frustumCullingEnabledChanged(bool)
        gammaChanged             : typing.ClassVar[Signal] = ... # gammaChanged(float)
        showDebugOverlayChanged  : typing.ClassVar[Signal] = ... # showDebugOverlayChanged(bool)
        surfaceChanged           : typing.ClassVar[Signal] = ... # surfaceChanged(QObject*)
        viewportRectChanged      : typing.ClassVar[Signal] = ... # viewportRectChanged(QRectF)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, surface: PySide6.QtCore.QObject | None = ..., window: PySide6.QtCore.QObject | None = ..., viewportRect: PySide6.QtCore.QRectF | None = ..., clearColor: PySide6.QtGui.QColor | None = ..., buffersToClear: PySide6.Qt3DRender.Qt3DRender.QClearBuffers.BufferType | None = ..., camera: PySide6.Qt3DCore.Qt3DCore.QEntity | None = ..., externalRenderTargetSize: PySide6.QtCore.QSize | None = ..., frustumCulling: bool | None = ..., gamma: float | None = ..., showDebugOverlay: bool | None = ...) -> None: ...

        def buffersToClear(self, /) -> PySide6.Qt3DRender.Qt3DRender.QClearBuffers.BufferType: ...
        def camera(self, /) -> PySide6.Qt3DCore.Qt3DCore.QEntity: ...
        def clearColor(self, /) -> PySide6.QtGui.QColor: ...
        def externalRenderTargetSize(self, /) -> PySide6.QtCore.QSize: ...
        def gamma(self, /) -> float: ...
        def isFrustumCullingEnabled(self, /) -> bool: ...
        def setBuffersToClear(self, arg__1: PySide6.Qt3DRender.Qt3DRender.QClearBuffers.BufferType, /) -> None: ...
        def setCamera(self, camera: PySide6.Qt3DCore.Qt3DCore.QEntity, /) -> None: ...
        def setClearColor(self, clearColor: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setExternalRenderTargetSize(self, size: PySide6.QtCore.QSize, /) -> None: ...
        def setFrustumCullingEnabled(self, enabled: bool, /) -> None: ...
        def setGamma(self, gamma: float, /) -> None: ...
        def setShowDebugOverlay(self, showDebugOverlay: bool, /) -> None: ...
        def setSurface(self, surface: PySide6.QtCore.QObject, /) -> None: ...
        def setViewportRect(self, viewportRect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
        def showDebugOverlay(self, /) -> bool: ...
        def surface(self, /) -> PySide6.QtCore.QObject: ...
        def viewportRect(self, /) -> PySide6.QtCore.QRectF: ...

    class QGoochMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        alphaChanged             : typing.ClassVar[Signal] = ... # alphaChanged(float)
        betaChanged              : typing.ClassVar[Signal] = ... # betaChanged(float)
        coolChanged              : typing.ClassVar[Signal] = ... # coolChanged(QColor)
        diffuseChanged           : typing.ClassVar[Signal] = ... # diffuseChanged(QColor)
        shininessChanged         : typing.ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : typing.ClassVar[Signal] = ... # specularChanged(QColor)
        warmChanged              : typing.ClassVar[Signal] = ... # warmChanged(QColor)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, diffuse: PySide6.QtGui.QColor | None = ..., specular: PySide6.QtGui.QColor | None = ..., cool: PySide6.QtGui.QColor | None = ..., warm: PySide6.QtGui.QColor | None = ..., alpha: float | None = ..., beta: float | None = ..., shininess: float | None = ...) -> None: ...

        def alpha(self, /) -> float: ...
        def beta(self, /) -> float: ...
        def cool(self, /) -> PySide6.QtGui.QColor: ...
        def diffuse(self, /) -> PySide6.QtGui.QColor: ...
        def setAlpha(self, alpha: float, /) -> None: ...
        def setBeta(self, beta: float, /) -> None: ...
        def setCool(self, cool: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setDiffuse(self, diffuse: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setShininess(self, shininess: float, /) -> None: ...
        def setSpecular(self, specular: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setWarm(self, warm: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def shininess(self, /) -> float: ...
        def specular(self, /) -> PySide6.QtGui.QColor: ...
        def warm(self, /) -> PySide6.QtGui.QColor: ...

    class QMetalRoughMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientOcclusionChanged  : typing.ClassVar[Signal] = ... # ambientOcclusionChanged(QVariant)
        baseColorChanged         : typing.ClassVar[Signal] = ... # baseColorChanged(QVariant)
        metalnessChanged         : typing.ClassVar[Signal] = ... # metalnessChanged(QVariant)
        normalChanged            : typing.ClassVar[Signal] = ... # normalChanged(QVariant)
        roughnessChanged         : typing.ClassVar[Signal] = ... # roughnessChanged(QVariant)
        textureScaleChanged      : typing.ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, baseColor: typing.Optional[typing.Any] = ..., metalness: typing.Optional[typing.Any] = ..., roughness: typing.Optional[typing.Any] = ..., ambientOcclusion: typing.Optional[typing.Any] = ..., normal: typing.Optional[typing.Any] = ..., textureScale: float | None = ...) -> None: ...

        def ambientOcclusion(self, /) -> typing.Any: ...
        def baseColor(self, /) -> typing.Any: ...
        def metalness(self, /) -> typing.Any: ...
        def normal(self, /) -> typing.Any: ...
        def roughness(self, /) -> typing.Any: ...
        def setAmbientOcclusion(self, ambientOcclusion: typing.Any, /) -> None: ...
        def setBaseColor(self, baseColor: typing.Any, /) -> None: ...
        def setMetalness(self, metalness: typing.Any, /) -> None: ...
        def setNormal(self, normal: typing.Any, /) -> None: ...
        def setRoughness(self, roughness: typing.Any, /) -> None: ...
        def setTextureScale(self, textureScale: float, /) -> None: ...
        def textureScale(self, /) -> float: ...

    class QMorphPhongMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : typing.ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : typing.ClassVar[Signal] = ... # diffuseChanged(QColor)
        interpolatorChanged      : typing.ClassVar[Signal] = ... # interpolatorChanged(float)
        shininessChanged         : typing.ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : typing.ClassVar[Signal] = ... # specularChanged(QColor)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, ambient: PySide6.QtGui.QColor | None = ..., diffuse: PySide6.QtGui.QColor | None = ..., specular: PySide6.QtGui.QColor | None = ..., shininess: float | None = ..., interpolator: float | None = ...) -> None: ...

        def ambient(self, /) -> PySide6.QtGui.QColor: ...
        def diffuse(self, /) -> PySide6.QtGui.QColor: ...
        def interpolator(self, /) -> float: ...
        def setAmbient(self, ambient: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setDiffuse(self, diffuse: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setInterpolator(self, interpolator: float, /) -> None: ...
        def setShininess(self, shininess: float, /) -> None: ...
        def setSpecular(self, specular: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def shininess(self, /) -> float: ...
        def specular(self, /) -> PySide6.QtGui.QColor: ...

    class QNormalDiffuseMapAlphaMaterial(PySide6.Qt3DExtras.Qt3DExtras.QNormalDiffuseMapMaterial):

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ...) -> None: ...


    class QNormalDiffuseMapMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : typing.ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : typing.ClassVar[Signal] = ... # diffuseChanged(Qt3DRender::QAbstractTexture*)
        normalChanged            : typing.ClassVar[Signal] = ... # normalChanged(Qt3DRender::QAbstractTexture*)
        shininessChanged         : typing.ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : typing.ClassVar[Signal] = ... # specularChanged(QColor)
        textureScaleChanged      : typing.ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, ambient: PySide6.QtGui.QColor | None = ..., specular: PySide6.QtGui.QColor | None = ..., diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture | None = ..., normal: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture | None = ..., shininess: float | None = ..., textureScale: float | None = ...) -> None: ...

        def ambient(self, /) -> PySide6.QtGui.QColor: ...
        def diffuse(self, /) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def normal(self, /) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def setAmbient(self, ambient: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setDiffuse(self, diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, /) -> None: ...
        def setNormal(self, normal: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, /) -> None: ...
        def setShininess(self, shininess: float, /) -> None: ...
        def setSpecular(self, specular: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setTextureScale(self, textureScale: float, /) -> None: ...
        def shininess(self, /) -> float: ...
        def specular(self, /) -> PySide6.QtGui.QColor: ...
        def textureScale(self, /) -> float: ...

    class QNormalDiffuseSpecularMapMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : typing.ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : typing.ClassVar[Signal] = ... # diffuseChanged(Qt3DRender::QAbstractTexture*)
        normalChanged            : typing.ClassVar[Signal] = ... # normalChanged(Qt3DRender::QAbstractTexture*)
        shininessChanged         : typing.ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : typing.ClassVar[Signal] = ... # specularChanged(Qt3DRender::QAbstractTexture*)
        textureScaleChanged      : typing.ClassVar[Signal] = ... # textureScaleChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, ambient: PySide6.QtGui.QColor | None = ..., diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture | None = ..., normal: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture | None = ..., specular: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture | None = ..., shininess: float | None = ..., textureScale: float | None = ...) -> None: ...

        def ambient(self, /) -> PySide6.QtGui.QColor: ...
        def diffuse(self, /) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def normal(self, /) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def setAmbient(self, ambient: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setDiffuse(self, diffuse: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, /) -> None: ...
        def setNormal(self, normal: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, /) -> None: ...
        def setShininess(self, shininess: float, /) -> None: ...
        def setSpecular(self, specular: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, /) -> None: ...
        def setTextureScale(self, textureScale: float, /) -> None: ...
        def shininess(self, /) -> float: ...
        def specular(self, /) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def textureScale(self, /) -> float: ...

    class QOrbitCameraController(PySide6.Qt3DExtras.Qt3DExtras.QAbstractCameraController):

        inversePanChanged        : typing.ClassVar[Signal] = ... # inversePanChanged(bool)
        inverseTiltChanged       : typing.ClassVar[Signal] = ... # inverseTiltChanged(bool)
        inverseXTranslateChanged : typing.ClassVar[Signal] = ... # inverseXTranslateChanged(bool)
        inverseYTranslateChanged : typing.ClassVar[Signal] = ... # inverseYTranslateChanged(bool)
        upVectorChanged          : typing.ClassVar[Signal] = ... # upVectorChanged(QVector3D)
        zoomInLimitChanged       : typing.ClassVar[Signal] = ... # zoomInLimitChanged()
        zoomTranslateViewCenterChanged: typing.ClassVar[Signal] = ... # zoomTranslateViewCenterChanged(bool)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, zoomInLimit: float | None = ..., upVector: PySide6.QtGui.QVector3D | None = ..., inverseXTranslate: bool | None = ..., inverseYTranslate: bool | None = ..., inversePan: bool | None = ..., inverseTilt: bool | None = ..., zoomTranslateViewCenter: bool | None = ...) -> None: ...

        def inversePan(self, /) -> bool: ...
        def inverseTilt(self, /) -> bool: ...
        def inverseXTranslate(self, /) -> bool: ...
        def inverseYTranslate(self, /) -> bool: ...
        def setInversePan(self, isInverse: bool, /) -> None: ...
        def setInverseTilt(self, isInverse: bool, /) -> None: ...
        def setInverseXTranslate(self, isInverse: bool, /) -> None: ...
        def setInverseYTranslate(self, isInverse: bool, /) -> None: ...
        def setUpVector(self, upVector: PySide6.QtGui.QVector3D, /) -> None: ...
        def setZoomInLimit(self, zoomInLimit: float, /) -> None: ...
        def setZoomTranslateViewCenter(self, isTranslate: bool, /) -> None: ...
        def upVector(self, /) -> PySide6.QtGui.QVector3D: ...
        def zoomInLimit(self, /) -> float: ...
        def zoomTranslateViewCenter(self, /) -> bool: ...

    class QPerVertexColorMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ...) -> None: ...


    class QPhongAlphaMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        alphaChanged             : typing.ClassVar[Signal] = ... # alphaChanged(float)
        ambientChanged           : typing.ClassVar[Signal] = ... # ambientChanged(QColor)
        blendFunctionArgChanged  : typing.ClassVar[Signal] = ... # blendFunctionArgChanged(Qt3DRender::QBlendEquation::BlendFunction)
        destinationAlphaArgChanged: typing.ClassVar[Signal] = ... # destinationAlphaArgChanged(Qt3DRender::QBlendEquationArguments::Blending)
        destinationRgbArgChanged : typing.ClassVar[Signal] = ... # destinationRgbArgChanged(Qt3DRender::QBlendEquationArguments::Blending)
        diffuseChanged           : typing.ClassVar[Signal] = ... # diffuseChanged(QColor)
        shininessChanged         : typing.ClassVar[Signal] = ... # shininessChanged(float)
        sourceAlphaArgChanged    : typing.ClassVar[Signal] = ... # sourceAlphaArgChanged(Qt3DRender::QBlendEquationArguments::Blending)
        sourceRgbArgChanged      : typing.ClassVar[Signal] = ... # sourceRgbArgChanged(Qt3DRender::QBlendEquationArguments::Blending)
        specularChanged          : typing.ClassVar[Signal] = ... # specularChanged(QColor)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, ambient: PySide6.QtGui.QColor | None = ..., diffuse: PySide6.QtGui.QColor | None = ..., specular: PySide6.QtGui.QColor | None = ..., shininess: float | None = ..., alpha: float | None = ..., sourceRgbArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending | None = ..., destinationRgbArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending | None = ..., sourceAlphaArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending | None = ..., destinationAlphaArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending | None = ..., blendFunctionArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquation.BlendFunction | None = ...) -> None: ...

        def alpha(self, /) -> float: ...
        def ambient(self, /) -> PySide6.QtGui.QColor: ...
        def blendFunctionArg(self, /) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquation.BlendFunction: ...
        def destinationAlphaArg(self, /) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def destinationRgbArg(self, /) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def diffuse(self, /) -> PySide6.QtGui.QColor: ...
        def setAlpha(self, alpha: float, /) -> None: ...
        def setAmbient(self, ambient: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setBlendFunctionArg(self, blendFunctionArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquation.BlendFunction, /) -> None: ...
        def setDestinationAlphaArg(self, destinationAlphaArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending, /) -> None: ...
        def setDestinationRgbArg(self, destinationRgbArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending, /) -> None: ...
        def setDiffuse(self, diffuse: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setShininess(self, shininess: float, /) -> None: ...
        def setSourceAlphaArg(self, sourceAlphaArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending, /) -> None: ...
        def setSourceRgbArg(self, sourceRgbArg: PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending, /) -> None: ...
        def setSpecular(self, specular: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def shininess(self, /) -> float: ...
        def sourceAlphaArg(self, /) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def sourceRgbArg(self, /) -> PySide6.Qt3DRender.Qt3DRender.QBlendEquationArguments.Blending: ...
        def specular(self, /) -> PySide6.QtGui.QColor: ...

    class QPhongMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        ambientChanged           : typing.ClassVar[Signal] = ... # ambientChanged(QColor)
        diffuseChanged           : typing.ClassVar[Signal] = ... # diffuseChanged(QColor)
        shininessChanged         : typing.ClassVar[Signal] = ... # shininessChanged(float)
        specularChanged          : typing.ClassVar[Signal] = ... # specularChanged(QColor)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, ambient: PySide6.QtGui.QColor | None = ..., diffuse: PySide6.QtGui.QColor | None = ..., specular: PySide6.QtGui.QColor | None = ..., shininess: float | None = ...) -> None: ...

        def ambient(self, /) -> PySide6.QtGui.QColor: ...
        def diffuse(self, /) -> PySide6.QtGui.QColor: ...
        def setAmbient(self, ambient: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setDiffuse(self, diffuse: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setShininess(self, shininess: float, /) -> None: ...
        def setSpecular(self, specular: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def shininess(self, /) -> float: ...
        def specular(self, /) -> PySide6.QtGui.QColor: ...

    class QPlaneGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        heightChanged            : typing.ClassVar[Signal] = ... # heightChanged(float)
        mirroredChanged          : typing.ClassVar[Signal] = ... # mirroredChanged(bool)
        resolutionChanged        : typing.ClassVar[Signal] = ... # resolutionChanged(QSize)
        widthChanged             : typing.ClassVar[Signal] = ... # widthChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, width: float | None = ..., height: float | None = ..., resolution: PySide6.QtCore.QSize | None = ..., mirrored: bool | None = ..., positionAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., normalAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., texCoordAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., tangentAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., indexAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ...) -> None: ...

        def height(self, /) -> float: ...
        def indexAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def mirrored(self, /) -> bool: ...
        def normalAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def resolution(self, /) -> PySide6.QtCore.QSize: ...
        def setHeight(self, height: float, /) -> None: ...
        def setMirrored(self, mirrored: bool, /) -> None: ...
        def setResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setWidth(self, width: float, /) -> None: ...
        def tangentAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def texCoordAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def updateIndices(self, /) -> None: ...
        def updateVertices(self, /) -> None: ...
        def width(self, /) -> float: ...

    class QPlaneGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        heightChanged            : typing.ClassVar[Signal] = ... # heightChanged(float)
        meshResolutionChanged    : typing.ClassVar[Signal] = ... # meshResolutionChanged(QSize)
        mirroredChanged          : typing.ClassVar[Signal] = ... # mirroredChanged(bool)
        widthChanged             : typing.ClassVar[Signal] = ... # widthChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, width: float | None = ..., height: float | None = ..., meshResolution: PySide6.QtCore.QSize | None = ..., mirrored: bool | None = ...) -> None: ...

        def height(self, /) -> float: ...
        def meshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def mirrored(self, /) -> bool: ...
        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setHeight(self, height: float, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setInstanceCount(self, instanceCount: int, /) -> None: ...
        def setMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setMirrored(self, mirrored: bool, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def setWidth(self, width: float, /) -> None: ...
        def width(self, /) -> float: ...

    class QPlaneMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        heightChanged            : typing.ClassVar[Signal] = ... # heightChanged(float)
        meshResolutionChanged    : typing.ClassVar[Signal] = ... # meshResolutionChanged(QSize)
        mirroredChanged          : typing.ClassVar[Signal] = ... # mirroredChanged(bool)
        widthChanged             : typing.ClassVar[Signal] = ... # widthChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, width: float | None = ..., height: float | None = ..., meshResolution: PySide6.QtCore.QSize | None = ..., mirrored: bool | None = ...) -> None: ...

        def height(self, /) -> float: ...
        def meshResolution(self, /) -> PySide6.QtCore.QSize: ...
        def mirrored(self, /) -> bool: ...
        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setHeight(self, height: float, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setInstanceCount(self, instanceCount: int, /) -> None: ...
        def setMeshResolution(self, resolution: PySide6.QtCore.QSize, /) -> None: ...
        def setMirrored(self, mirrored: bool, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def setWidth(self, width: float, /) -> None: ...
        def width(self, /) -> float: ...

    class QSkyboxEntity(PySide6.Qt3DCore.Qt3DCore.QEntity):

        baseNameChanged          : typing.ClassVar[Signal] = ... # baseNameChanged(QString)
        extensionChanged         : typing.ClassVar[Signal] = ... # extensionChanged(QString)
        gammaCorrectEnabledChanged: typing.ClassVar[Signal] = ... # gammaCorrectEnabledChanged(bool)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, baseName: str | None = ..., extension: str | None = ..., gammaCorrect: bool | None = ...) -> None: ...

        def baseName(self, /) -> str: ...
        def extension(self, /) -> str: ...
        def isGammaCorrectEnabled(self, /) -> bool: ...
        def setBaseName(self, path: str, /) -> None: ...
        def setExtension(self, extension: str, /) -> None: ...
        def setGammaCorrectEnabled(self, enabled: bool, /) -> None: ...

    class QSphereGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        generateTangentsChanged  : typing.ClassVar[Signal] = ... # generateTangentsChanged(bool)
        radiusChanged            : typing.ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., radius: float | None = ..., generateTangents: bool | None = ..., positionAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., normalAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., texCoordAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., tangentAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., indexAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ...) -> None: ...

        def generateTangents(self, /) -> bool: ...
        def indexAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def normalAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def radius(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setGenerateTangents(self, gen: bool, /) -> None: ...
        def setRadius(self, radius: float, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def slices(self, /) -> int: ...
        def tangentAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def texCoordAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def updateIndices(self, /) -> None: ...
        def updateVertices(self, /) -> None: ...

    class QSphereGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        generateTangentsChanged  : typing.ClassVar[Signal] = ... # generateTangentsChanged(bool)
        radiusChanged            : typing.ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., radius: float | None = ..., generateTangents: bool | None = ...) -> None: ...

        def generateTangents(self, /) -> bool: ...
        def radius(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGenerateTangents(self, gen: bool, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType, /) -> None: ...
        def setRadius(self, radius: float, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def slices(self, /) -> int: ...

    class QSphereMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        generateTangentsChanged  : typing.ClassVar[Signal] = ... # generateTangentsChanged(bool)
        radiusChanged            : typing.ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., radius: float | None = ..., generateTangents: bool | None = ...) -> None: ...

        def generateTangents(self, /) -> bool: ...
        def radius(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGenerateTangents(self, gen: bool, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType, /) -> None: ...
        def setRadius(self, radius: float, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def slices(self, /) -> int: ...

    class QSpriteGrid(PySide6.Qt3DExtras.Qt3DExtras.QAbstractSpriteSheet):

        columnsChanged           : typing.ClassVar[Signal] = ... # columnsChanged(int)
        rowsChanged              : typing.ClassVar[Signal] = ... # rowsChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rows: int | None = ..., columns: int | None = ...) -> None: ...

        def columns(self, /) -> int: ...
        def rows(self, /) -> int: ...
        def setColumns(self, columns: int, /) -> None: ...
        def setRows(self, rows: int, /) -> None: ...

    class QSpriteSheet(PySide6.Qt3DExtras.Qt3DExtras.QAbstractSpriteSheet):

        spritesChanged           : typing.ClassVar[Signal] = ... # spritesChanged(QList<QSpriteSheetItem*>)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, sprites: collections.abc.Sequence[PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem] | None = ...) -> None: ...

        @typing.overload
        def addSprite(self, sprite: PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem, /) -> None: ...
        @typing.overload
        def addSprite(self, x: int, y: int, width: int, height: int, /) -> PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem: ...
        def removeSprite(self, sprite: PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem, /) -> None: ...
        def setSprites(self, sprites: collections.abc.Sequence[PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem], /) -> None: ...
        def sprites(self, /) -> typing.List[PySide6.Qt3DExtras.Qt3DExtras.QSpriteSheetItem]: ...

    class QSpriteSheetItem(PySide6.Qt3DCore.Qt3DCore.QNode):

        heightChanged            : typing.ClassVar[Signal] = ... # heightChanged(int)
        widthChanged             : typing.ClassVar[Signal] = ... # widthChanged(int)
        xChanged                 : typing.ClassVar[Signal] = ... # xChanged(int)
        yChanged                 : typing.ClassVar[Signal] = ... # yChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, x: int | None = ..., y: int | None = ..., width: int | None = ..., height: int | None = ...) -> None: ...

        def height(self, /) -> int: ...
        def setHeight(self, height: int, /) -> None: ...
        def setWidth(self, width: int, /) -> None: ...
        def setX(self, x: int, /) -> None: ...
        def setY(self, y: int, /) -> None: ...
        def width(self, /) -> int: ...
        def x(self, /) -> int: ...
        def y(self, /) -> int: ...

    class QText2DEntity(PySide6.Qt3DCore.Qt3DCore.QEntity):

        colorChanged             : typing.ClassVar[Signal] = ... # colorChanged(QColor)
        fontChanged              : typing.ClassVar[Signal] = ... # fontChanged(QFont)
        heightChanged            : typing.ClassVar[Signal] = ... # heightChanged(float)
        textChanged              : typing.ClassVar[Signal] = ... # textChanged(QString)
        widthChanged             : typing.ClassVar[Signal] = ... # widthChanged(float)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, font: PySide6.QtGui.QFont | None = ..., text: str | None = ..., color: PySide6.QtGui.QColor | None = ..., width: float | None = ..., height: float | None = ..., alignment: PySide6.QtCore.Qt.AlignmentFlag | None = ...) -> None: ...

        def alignment(self, /) -> PySide6.QtCore.Qt.AlignmentFlag: ...
        def color(self, /) -> PySide6.QtGui.QColor: ...
        def font(self, /) -> PySide6.QtGui.QFont: ...
        def height(self, /) -> float: ...
        def setAlignment(self, alignment: PySide6.QtCore.Qt.AlignmentFlag, /) -> None: ...
        def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
        def setFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
        def setHeight(self, height: float, /) -> None: ...
        def setText(self, text: str, /) -> None: ...
        def setWidth(self, width: float, /) -> None: ...
        def text(self, /) -> str: ...
        def width(self, /) -> float: ...

    class QTextureMaterial(PySide6.Qt3DRender.Qt3DRender.QMaterial):

        alphaBlendingEnabledChanged: typing.ClassVar[Signal] = ... # alphaBlendingEnabledChanged(bool)
        textureChanged           : typing.ClassVar[Signal] = ... # textureChanged(Qt3DRender::QAbstractTexture*)
        textureOffsetChanged     : typing.ClassVar[Signal] = ... # textureOffsetChanged(QVector2D)
        textureTransformChanged  : typing.ClassVar[Signal] = ... # textureTransformChanged(QMatrix3x3)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, texture: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture | None = ..., textureOffset: PySide6.QtGui.QVector2D | None = ..., textureTransform: PySide6.QtGui.QMatrix3x3 | None = ..., alphaBlending: bool | None = ...) -> None: ...

        def isAlphaBlendingEnabled(self, /) -> bool: ...
        def setAlphaBlendingEnabled(self, enabled: bool, /) -> None: ...
        def setTexture(self, texture: PySide6.Qt3DRender.Qt3DRender.QAbstractTexture, /) -> None: ...
        def setTextureOffset(self, textureOffset: PySide6.QtGui.QVector2D, /) -> None: ...
        def setTextureTransform(self, matrix: PySide6.QtGui.QMatrix3x3, /) -> None: ...
        def texture(self, /) -> PySide6.Qt3DRender.Qt3DRender.QAbstractTexture: ...
        def textureOffset(self, /) -> PySide6.QtGui.QVector2D: ...
        def textureTransform(self, /) -> PySide6.QtGui.QMatrix3x3: ...

    class QTorusGeometry(PySide6.Qt3DCore.Qt3DCore.QGeometry):

        minorRadiusChanged       : typing.ClassVar[Signal] = ... # minorRadiusChanged(float)
        radiusChanged            : typing.ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., radius: float | None = ..., minorRadius: float | None = ..., positionAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., normalAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., texCoordAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ..., indexAttribute: PySide6.Qt3DCore.Qt3DCore.QAttribute | None = ...) -> None: ...

        def indexAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def minorRadius(self, /) -> float: ...
        def normalAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def positionAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def radius(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setMinorRadius(self, minorRadius: float, /) -> None: ...
        def setRadius(self, radius: float, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def slices(self, /) -> int: ...
        def texCoordAttribute(self, /) -> PySide6.Qt3DCore.Qt3DCore.QAttribute: ...
        def updateIndices(self, /) -> None: ...
        def updateVertices(self, /) -> None: ...

    class QTorusGeometryView(PySide6.Qt3DCore.Qt3DCore.QGeometryView):

        minorRadiusChanged       : typing.ClassVar[Signal] = ... # minorRadiusChanged(float)
        radiusChanged            : typing.ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., radius: float | None = ..., minorRadius: float | None = ...) -> None: ...

        def minorRadius(self, /) -> float: ...
        def radius(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setInstanceCount(self, instanceCount: int, /) -> None: ...
        def setMinorRadius(self, minorRadius: float, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DCore.Qt3DCore.QGeometryView.PrimitiveType, /) -> None: ...
        def setRadius(self, radius: float, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def slices(self, /) -> int: ...

    class QTorusMesh(PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer):

        minorRadiusChanged       : typing.ClassVar[Signal] = ... # minorRadiusChanged(float)
        radiusChanged            : typing.ClassVar[Signal] = ... # radiusChanged(float)
        ringsChanged             : typing.ClassVar[Signal] = ... # ringsChanged(int)
        slicesChanged            : typing.ClassVar[Signal] = ... # slicesChanged(int)

        def __init__(self, /, parent: PySide6.Qt3DCore.Qt3DCore.QNode | None = ..., *, rings: int | None = ..., slices: int | None = ..., radius: float | None = ..., minorRadius: float | None = ...) -> None: ...

        def minorRadius(self, /) -> float: ...
        def radius(self, /) -> float: ...
        def rings(self, /) -> int: ...
        def setFirstInstance(self, firstInstance: int, /) -> None: ...
        def setGeometry(self, geometry: PySide6.Qt3DCore.Qt3DCore.QGeometry, /) -> None: ...
        def setIndexOffset(self, indexOffset: int, /) -> None: ...
        def setInstanceCount(self, instanceCount: int, /) -> None: ...
        def setMinorRadius(self, minorRadius: float, /) -> None: ...
        def setPrimitiveRestartEnabled(self, enabled: bool, /) -> None: ...
        def setPrimitiveType(self, primitiveType: PySide6.Qt3DRender.Qt3DRender.QGeometryRenderer.PrimitiveType, /) -> None: ...
        def setRadius(self, radius: float, /) -> None: ...
        def setRestartIndexValue(self, index: int, /) -> None: ...
        def setRings(self, rings: int, /) -> None: ...
        def setSlices(self, slices: int, /) -> None: ...
        def setVertexCount(self, vertexCount: int, /) -> None: ...
        def slices(self, /) -> int: ...

    class Qt3DWindow(PySide6.QtGui.QWindow):

        def __init__(self, /, screen: PySide6.QtGui.QScreen | None = ..., arg__2: PySide6.Qt3DRender.Qt3DRender.API = ...) -> None: ...

        def activeFrameGraph(self, /) -> PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode: ...
        def camera(self, /) -> PySide6.Qt3DRender.Qt3DRender.QCamera: ...
        def defaultFrameGraph(self, /) -> PySide6.Qt3DExtras.Qt3DExtras.QForwardRenderer: ...
        def event(self, e: PySide6.QtCore.QEvent, /) -> bool: ...
        @typing.overload
        def registerAspect(self, aspect: PySide6.Qt3DCore.Qt3DCore.QAbstractAspect, /) -> None: ...
        @typing.overload
        def registerAspect(self, name: str, /) -> None: ...
        def renderSettings(self, /) -> PySide6.Qt3DRender.Qt3DRender.QRenderSettings: ...
        def resizeEvent(self, arg__1: PySide6.QtGui.QResizeEvent, /) -> None: ...
        def setActiveFrameGraph(self, activeFrameGraph: PySide6.Qt3DRender.Qt3DRender.QFrameGraphNode, /) -> None: ...
        def setRootEntity(self, root: PySide6.Qt3DCore.Qt3DCore.QEntity, /) -> None: ...
        def showEvent(self, e: PySide6.QtGui.QShowEvent, /) -> None: ...


    @staticmethod
    def setupWindowSurface(window: PySide6.QtGui.QWindow, arg__2: PySide6.Qt3DRender.Qt3DRender.API, /) -> None: ...


# eof
