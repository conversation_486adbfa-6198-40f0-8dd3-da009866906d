# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtAxContainer, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtAxContainer`

import PySide6.QtAxContainer
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtWidgets

import enum
import typing
import collections
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QAxBase(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def __lshift__(self, s: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, s: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    @staticmethod
    def argumentsToList(var1: typing.Any, var2: typing.Any, var3: typing.Any, var4: typing.Any, var5: typing.Any, var6: typing.Any, var7: typing.Any, var8: typing.Any, /) -> typing.List[typing.Any]: ...
    def asVariant(self, /) -> typing.Any: ...
    def axBaseMetaObject(self, /) -> PySide6.QtCore.QMetaObject: ...
    def classContext(self, /) -> int: ...
    def className(self, /) -> bytes | bytearray | memoryview: ...
    def clear(self, /) -> None: ...
    def control(self, /) -> str: ...
    def disableClassInfo(self, /) -> None: ...
    def disableEventSink(self, /) -> None: ...
    def disableMetaObject(self, /) -> None: ...
    @typing.overload
    def dynamicCall(self, name: bytes | bytearray | memoryview, vars: collections.abc.Sequence[typing.Any], /) -> typing.Any: ...
    @typing.overload
    def dynamicCall(self, name: bytes | bytearray | memoryview, /, v1: typing.Any = ..., v2: typing.Any = ..., v3: typing.Any = ..., v4: typing.Any = ..., v5: typing.Any = ..., v6: typing.Any = ..., v7: typing.Any = ..., v8: typing.Any = ...) -> typing.Any: ...
    def generateDocumentation(self, /) -> str: ...
    def indexOfVerb(self, verb: str, /) -> int: ...
    def initializeFrom(self, that: PySide6.QtAxContainer.QAxBase, /) -> None: ...
    def internalRelease(self, /) -> None: ...
    def isNull(self, /) -> bool: ...
    def propertyBag(self, /) -> typing.Dict[str, typing.Any]: ...
    def propertyWritable(self, arg__1: bytes | bytearray | memoryview, /) -> bool: ...
    def qObject(self, /) -> PySide6.QtCore.QObject: ...
    @typing.overload
    def querySubObject(self, name: bytes | bytearray | memoryview, vars: collections.abc.Sequence[typing.Any], /) -> PySide6.QtAxContainer.QAxObject: ...
    @typing.overload
    def querySubObject(self, name: bytes | bytearray | memoryview, /, v1: typing.Any = ..., v2: typing.Any = ..., v3: typing.Any = ..., v4: typing.Any = ..., v5: typing.Any = ..., v6: typing.Any = ..., v7: typing.Any = ..., v8: typing.Any = ...) -> PySide6.QtAxContainer.QAxObject: ...
    def setClassContext(self, classContext: int, /) -> None: ...
    def setControl(self, arg__1: str, /) -> bool: ...
    def setPropertyBag(self, arg__1: typing.Dict[str, typing.Any], /) -> None: ...
    def setPropertyWritable(self, arg__1: bytes | bytearray | memoryview, arg__2: bool, /) -> None: ...
    def verbs(self, /) -> typing.List[str]: ...


class QAxBaseObject(PySide6.QtCore.QObject, PySide6.QtAxContainer.QAxObjectInterface):

    exception                : typing.ClassVar[Signal] = ... # exception(int,QString,QString,QString)
    propertyChanged          : typing.ClassVar[Signal] = ... # propertyChanged(QString)
    signal                   : typing.ClassVar[Signal] = ... # signal(QString,int,void*)


class QAxBaseWidget(PySide6.QtWidgets.QWidget, PySide6.QtAxContainer.QAxObjectInterface):

    exception                : typing.ClassVar[Signal] = ... # exception(int,QString,QString,QString)
    propertyChanged          : typing.ClassVar[Signal] = ... # propertyChanged(QString)
    signal                   : typing.ClassVar[Signal] = ... # signal(QString,int,void*)


class QAxObject(PySide6.QtAxContainer.QAxBaseObject, PySide6.QtAxContainer.QAxBase):

    @typing.overload
    def __init__(self, c: str, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def classContext(self, /) -> int: ...
    def clear(self, /) -> None: ...
    def control(self, /) -> str: ...
    def doVerb(self, verb: str, /) -> bool: ...
    def resetControl(self, /) -> None: ...
    def setClassContext(self, classContext: int, /) -> None: ...
    def setControl(self, c: str, /) -> bool: ...


class QAxObjectInterface(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def classContext(self, /) -> int: ...
    def control(self, /) -> str: ...
    def resetControl(self, /) -> None: ...
    def setClassContext(self, classContext: int, /) -> None: ...
    def setControl(self, c: str, /) -> bool: ...


class QAxScript(PySide6.QtCore.QObject):

    entered                  : typing.ClassVar[Signal] = ... # entered()
    error                    : typing.ClassVar[Signal] = ... # error(int,QString,int,QString)
    finished                 : typing.ClassVar[Signal] = ... # finished(); finished(QVariant); finished(int,QString,QString,QString)
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(int)

    class FunctionFlags(enum.Enum):

        FunctionNames             = ...  # 0x0
        FunctionSignatures        = ...  # 0x1


    def __init__(self, name: str, manager: PySide6.QtAxContainer.QAxScriptManager, /) -> None: ...

    @typing.overload
    def call(self, function: str, arguments: collections.abc.Sequence[typing.Any], /) -> typing.Any: ...
    @typing.overload
    def call(self, function: str, /, v1: typing.Any = ..., v2: typing.Any = ..., v3: typing.Any = ..., v4: typing.Any = ..., v5: typing.Any = ..., v6: typing.Any = ..., v7: typing.Any = ..., v8: typing.Any = ...) -> typing.Any: ...
    def functions(self, /, arg__1: PySide6.QtAxContainer.QAxScript.FunctionFlags = ...) -> typing.List[str]: ...
    def load(self, code: str, /, language: str = ...) -> bool: ...
    def scriptCode(self, /) -> str: ...
    def scriptEngine(self, /) -> PySide6.QtAxContainer.QAxScriptEngine: ...
    def scriptName(self, /) -> str: ...


class QAxScriptEngine(PySide6.QtAxContainer.QAxObject):

    class State(enum.Enum):

        Uninitialized             = ...  # 0x0
        Started                   = ...  # 0x1
        Connected                 = ...  # 0x2
        Disconnected              = ...  # 0x3
        Closed                    = ...  # 0x4
        Initialized               = ...  # 0x5


    def __init__(self, language: str, script: PySide6.QtAxContainer.QAxScript, /) -> None: ...

    def addItem(self, name: str, /) -> None: ...
    def hasIntrospection(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def scriptLanguage(self, /) -> str: ...
    def setState(self, st: PySide6.QtAxContainer.QAxScriptEngine.State, /) -> None: ...
    def state(self, /) -> PySide6.QtAxContainer.QAxScriptEngine.State: ...


class QAxScriptManager(PySide6.QtCore.QObject):

    error                    : typing.ClassVar[Signal] = ... # error(QAxScript*,int,QString,int,QString)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def addObject(self, object: PySide6.QtAxContainer.QAxBase, /) -> None: ...
    @typing.overload
    def call(self, function: str, arguments: collections.abc.Sequence[typing.Any], /) -> typing.Any: ...
    @typing.overload
    def call(self, function: str, /, v1: typing.Any = ..., v2: typing.Any = ..., v3: typing.Any = ..., v4: typing.Any = ..., v5: typing.Any = ..., v6: typing.Any = ..., v7: typing.Any = ..., v8: typing.Any = ...) -> typing.Any: ...
    def functions(self, /, arg__1: PySide6.QtAxContainer.QAxScript.FunctionFlags = ...) -> typing.List[str]: ...
    @typing.overload
    def load(self, file: str, name: str, /) -> PySide6.QtAxContainer.QAxScript: ...
    @typing.overload
    def load(self, code: str, name: str, language: str, /) -> PySide6.QtAxContainer.QAxScript: ...
    @staticmethod
    def registerEngine(name: str, extension: str, /, code: str = ...) -> bool: ...
    def script(self, name: str, /) -> PySide6.QtAxContainer.QAxScript: ...
    @staticmethod
    def scriptFileFilter() -> str: ...
    def scriptNames(self, /) -> typing.List[str]: ...


class QAxSelect(PySide6.QtWidgets.QDialog):

    class SandboxingLevel(enum.Enum):

        SandboxingNone            = ...  # 0x0
        SandboxingProcess         = ...  # 0x1
        SandboxingLowIntegrity    = ...  # 0x2
        SandboxingAppContainer    = ...  # 0x3


    def __init__(self, /, parent: PySide6.QtWidgets.QWidget | None = ..., flags: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def clsid(self, /) -> str: ...
    def sandboxingLevel(self, /) -> PySide6.QtAxContainer.QAxSelect.SandboxingLevel: ...


class QAxWidget(PySide6.QtAxContainer.QAxBaseWidget, PySide6.QtAxContainer.QAxBase):

    @typing.overload
    def __init__(self, c: str, /, parent: PySide6.QtWidgets.QWidget | None = ..., f: PySide6.QtCore.Qt.WindowType = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtWidgets.QWidget | None = ..., f: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def changeEvent(self, e: PySide6.QtCore.QEvent, /) -> None: ...
    def classContext(self, /) -> int: ...
    def clear(self, /) -> None: ...
    def control(self, /) -> str: ...
    @typing.overload
    def createHostWindow(self, arg__1: bool, /) -> bool: ...
    @typing.overload
    def createHostWindow(self, arg__1: bool, arg__2: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> bool: ...
    def doVerb(self, verb: str, /) -> bool: ...
    def minimumSizeHint(self, /) -> PySide6.QtCore.QSize: ...
    def resetControl(self, /) -> None: ...
    def resizeEvent(self, arg__1: PySide6.QtGui.QResizeEvent, /) -> None: ...
    def setClassContext(self, classContext: int, /) -> None: ...
    def setControl(self, arg__1: str, /) -> bool: ...
    def sizeHint(self, /) -> PySide6.QtCore.QSize: ...
    def translateKeyEvent(self, message: int, keycode: int, /) -> bool: ...


class QIntList: ...


# eof
