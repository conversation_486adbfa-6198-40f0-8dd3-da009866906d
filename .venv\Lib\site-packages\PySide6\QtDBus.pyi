# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtDBus, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtDBus`

import PySide6.QtDBus
import PySide6.QtCore

import enum
import typing
import collections
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QDBus(Shiboken.Object):

    class CallMode(enum.Enum):

        NoBlock                   = ...  # 0x0
        Block                     = ...  # 0x1
        BlockWithGui              = ...  # 0x2
        AutoDetect                = ...  # 0x3


class QDBusAbstractAdaptor(PySide6.QtCore.QObject):

    def __init__(self, parent: PySide6.QtCore.QObject, /) -> None: ...

    def autoRelaySignals(self, /) -> bool: ...
    def setAutoRelaySignals(self, enable: bool, /) -> None: ...


class QDBusAbstractInterface(PySide6.QtDBus.QDBusAbstractInterfaceBase):

    def __init__(self, service: str, path: str, interface: bytes | bytearray | memoryview, connection: PySide6.QtDBus.QDBusConnection, parent: PySide6.QtCore.QObject, /) -> None: ...

    def asyncCall(self, method: str, /) -> PySide6.QtDBus.QDBusPendingCall: ...
    def asyncCallWithArgumentList(self, method: str, args: collections.abc.Sequence[typing.Any], /) -> PySide6.QtDBus.QDBusPendingCall: ...
    @typing.overload
    def call(self, mode: PySide6.QtDBus.QDBus.CallMode, method: str, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: PySide6.QtDBus.QDBus.CallMode, arg__2: str, arg__3: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: PySide6.QtDBus.QDBus.CallMode, arg__2: str, arg__3: typing.Any, arg__4: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: PySide6.QtDBus.QDBus.CallMode, arg__2: str, arg__3: typing.Any, arg__4: typing.Any, arg__5: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: PySide6.QtDBus.QDBus.CallMode, arg__2: str, arg__3: typing.Any, arg__4: typing.Any, arg__5: typing.Any, arg__6: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: PySide6.QtDBus.QDBus.CallMode, arg__2: str, arg__3: typing.Any, arg__4: typing.Any, arg__5: typing.Any, arg__6: typing.Any, arg__7: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: PySide6.QtDBus.QDBus.CallMode, arg__2: str, arg__3: typing.Any, arg__4: typing.Any, arg__5: typing.Any, arg__6: typing.Any, arg__7: typing.Any, arg__8: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: PySide6.QtDBus.QDBus.CallMode, arg__2: str, arg__3: typing.Any, arg__4: typing.Any, arg__5: typing.Any, arg__6: typing.Any, arg__7: typing.Any, arg__8: typing.Any, arg__9: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: PySide6.QtDBus.QDBus.CallMode, arg__2: str, arg__3: typing.Any, arg__4: typing.Any, arg__5: typing.Any, arg__6: typing.Any, arg__7: typing.Any, arg__8: typing.Any, arg__9: typing.Any, arg__10: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, method: str, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: str, arg__2: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: str, arg__2: typing.Any, arg__3: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: str, arg__2: typing.Any, arg__3: typing.Any, arg__4: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def call(self, arg__1: str, arg__2: typing.Any, arg__3: typing.Any, arg__4: typing.Any, arg__5: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    def callWithArgumentList(self, mode: PySide6.QtDBus.QDBus.CallMode, method: str, args: collections.abc.Sequence[typing.Any], /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def callWithCallback(self, method: str, args: collections.abc.Sequence[typing.Any], receiver: PySide6.QtCore.QObject, member: bytes | bytearray | memoryview, /) -> bool: ...
    @typing.overload
    def callWithCallback(self, method: str, args: collections.abc.Sequence[typing.Any], receiver: PySide6.QtCore.QObject, member: bytes | bytearray | memoryview, errorSlot: bytes | bytearray | memoryview, /) -> bool: ...
    def connectNotify(self, signal: PySide6.QtCore.QMetaMethod, /) -> None: ...
    def connection(self, /) -> PySide6.QtDBus.QDBusConnection: ...
    def disconnectNotify(self, signal: PySide6.QtCore.QMetaMethod, /) -> None: ...
    def interface(self, /) -> str: ...
    def internalConstCall(self, mode: PySide6.QtDBus.QDBus.CallMode, method: str, /, args: collections.abc.Sequence[typing.Any] = ...) -> PySide6.QtDBus.QDBusMessage: ...
    def internalPropGet(self, propname: bytes | bytearray | memoryview, /) -> typing.Any: ...
    def internalPropSet(self, propname: bytes | bytearray | memoryview, value: typing.Any, /) -> None: ...
    def isInteractiveAuthorizationAllowed(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def lastError(self, /) -> PySide6.QtDBus.QDBusError: ...
    def path(self, /) -> str: ...
    def service(self, /) -> str: ...
    def setInteractiveAuthorizationAllowed(self, enable: bool, /) -> None: ...
    def setTimeout(self, timeout: int, /) -> None: ...
    def timeout(self, /) -> int: ...


class QDBusAbstractInterfaceBase(PySide6.QtCore.QObject): ...


class QDBusArgument(Shiboken.Object):

    class ElementType(enum.Enum):

        UnknownType               = ...  # -1
        BasicType                 = ...  # 0x0
        VariantType               = ...  # 0x1
        ArrayType                 = ...  # 0x2
        StructureType             = ...  # 0x3
        MapType                   = ...  # 0x4
        MapEntryType              = ...  # 0x5


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtDBus.QDBusArgument, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    @typing.overload
    def __lshift__(self, arg: PySide6.QtDBus.QDBusObjectPath, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, arg: PySide6.QtDBus.QDBusSignature, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, arg: PySide6.QtDBus.QDBusUnixFileDescriptor, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, arg: PySide6.QtDBus.QDBusVariant, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, date: PySide6.QtCore.QDate, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, dt: PySide6.QtCore.QDateTime, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, line: PySide6.QtCore.QLine, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, pt: PySide6.QtCore.QPoint, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, rect: PySide6.QtCore.QRect, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, size: PySide6.QtCore.QSize, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, time: PySide6.QtCore.QTime, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, arg: str, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, list: collections.abc.Sequence[typing.Any], /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, arg: collections.abc.Sequence[str], /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, line: PySide6.QtCore.QLineF | PySide6.QtCore.QLine, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, pt: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, size: PySide6.QtCore.QSizeF | PySide6.QtCore.QSize, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, map: typing.Dict[str, typing.Any], /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, arg: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, arg: bool, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, arg: int, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __lshift__(self, arg: float, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, arg: PySide6.QtDBus.QDBusObjectPath, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, arg: PySide6.QtDBus.QDBusSignature, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, arg: PySide6.QtDBus.QDBusUnixFileDescriptor, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, arg: PySide6.QtDBus.QDBusVariant, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, date: PySide6.QtCore.QDate, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, dt: PySide6.QtCore.QDateTime, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, line: PySide6.QtCore.QLine, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, pt: PySide6.QtCore.QPoint, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, rect: PySide6.QtCore.QRect, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, size: PySide6.QtCore.QSize, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, time: PySide6.QtCore.QTime, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, arg: str, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, arg: collections.abc.Sequence[str], /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, line: PySide6.QtCore.QLineF | PySide6.QtCore.QLine, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, pt: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, size: PySide6.QtCore.QSizeF | PySide6.QtCore.QSize, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, arg: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, arg: bool, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, arg: int, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, arg: float, /) -> PySide6.QtDBus.QDBusArgument: ...
    @typing.overload
    def __rshift__(self, v: typing.Any, /) -> PySide6.QtDBus.QDBusArgument: ...
    def appendVariant(self, v: typing.Any, /) -> None: ...
    def asVariant(self, /) -> typing.Any: ...
    def atEnd(self, /) -> bool: ...
    @typing.overload
    def beginArray(self, /) -> None: ...
    @typing.overload
    def beginArray(self, elementMetaType: PySide6.QtCore.QMetaType | PySide6.QtCore.QMetaType.Type, /) -> None: ...
    @typing.overload
    def beginArray(self, elementMetaTypeId: int, /) -> None: ...
    @typing.overload
    def beginMap(self, /) -> None: ...
    @typing.overload
    def beginMap(self, keyMetaType: PySide6.QtCore.QMetaType | PySide6.QtCore.QMetaType.Type, valueMetaType: PySide6.QtCore.QMetaType | PySide6.QtCore.QMetaType.Type, /) -> None: ...
    @typing.overload
    def beginMap(self, keyMetaTypeId: int, valueMetaTypeId: int, /) -> None: ...
    def beginMapEntry(self, /) -> None: ...
    def beginStructure(self, /) -> None: ...
    def currentSignature(self, /) -> str: ...
    def currentType(self, /) -> PySide6.QtDBus.QDBusArgument.ElementType: ...
    def endArray(self, /) -> None: ...
    def endMap(self, /) -> None: ...
    def endMapEntry(self, /) -> None: ...
    def endStructure(self, /) -> None: ...
    def swap(self, other: PySide6.QtDBus.QDBusArgument, /) -> None: ...


class QDBusConnection(Shiboken.Object):

    class BusType(enum.Enum):

        SessionBus                = ...  # 0x0
        SystemBus                 = ...  # 0x1
        ActivationBus             = ...  # 0x2

    class ConnectionCapability(enum.Flag):

        UnixFileDescriptorPassing = ...  # 0x1

    class RegisterOption(enum.Flag):

        ExportAdaptors            = ...  # 0x1
        ExportScriptableSlots     = ...  # 0x10
        ExportScriptableSignals   = ...  # 0x20
        ExportScriptableProperties = ...  # 0x40
        ExportScriptableInvokables = ...  # 0x80
        ExportScriptableContents  = ...  # 0xf0
        ExportNonScriptableSlots  = ...  # 0x100
        ExportAllSlots            = ...  # 0x110
        ExportNonScriptableSignals = ...  # 0x200
        ExportAllSignal           = ...  # 0x220
        ExportAllSignals          = ...  # 0x220
        ExportNonScriptableProperties = ...  # 0x400
        ExportAllProperties       = ...  # 0x440
        ExportNonScriptableInvokables = ...  # 0x800
        ExportAllInvokables       = ...  # 0x880
        ExportNonScriptableContents = ...  # 0xf00
        ExportAllContents         = ...  # 0xff0
        ExportChildObjects        = ...  # 0x1000

    class UnregisterMode(enum.Enum):

        UnregisterNode            = ...  # 0x0
        UnregisterTree            = ...  # 0x1

    class VirtualObjectRegisterOption(enum.Flag):

        SingleNode                = ...  # 0x0
        SubPath                   = ...  # 0x1


    @typing.overload
    def __init__(self, other: PySide6.QtDBus.QDBusConnection, /) -> None: ...
    @typing.overload
    def __init__(self, name: str, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def asyncCall(self, message: PySide6.QtDBus.QDBusMessage, /, timeout: int = ...) -> PySide6.QtDBus.QDBusPendingCall: ...
    def baseService(self, /) -> str: ...
    def call(self, message: PySide6.QtDBus.QDBusMessage, /, mode: PySide6.QtDBus.QDBus.CallMode = ..., timeout: int = ...) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def callWithCallback(self, message: PySide6.QtDBus.QDBusMessage, receiver: PySide6.QtCore.QObject, returnMethod: bytes | bytearray | memoryview, errorMethod: bytes | bytearray | memoryview, /, timeout: int = ...) -> bool: ...
    @typing.overload
    def callWithCallback(self, message: PySide6.QtDBus.QDBusMessage, receiver: PySide6.QtCore.QObject, slot: bytes | bytearray | memoryview, /, timeout: int = ...) -> bool: ...
    @typing.overload
    def connect(self, service: str, path: str, interface: str, name: str, receiver: PySide6.QtCore.QObject, slot: bytes | bytearray | memoryview, /) -> bool: ...
    @typing.overload
    def connect(self, service: str, path: str, interface: str, name: str, signature: str, receiver: PySide6.QtCore.QObject, slot: bytes | bytearray | memoryview, /) -> bool: ...
    @typing.overload
    def connect(self, service: str, path: str, interface: str, name: str, argumentMatch: collections.abc.Sequence[str], signature: str, receiver: PySide6.QtCore.QObject, slot: bytes | bytearray | memoryview, /) -> bool: ...
    @typing.overload
    @staticmethod
    def connectToBus(type: PySide6.QtDBus.QDBusConnection.BusType, name: str, /) -> PySide6.QtDBus.QDBusConnection: ...
    @typing.overload
    @staticmethod
    def connectToBus(address: str, name: str, /) -> PySide6.QtDBus.QDBusConnection: ...
    @staticmethod
    def connectToPeer(address: str, name: str, /) -> PySide6.QtDBus.QDBusConnection: ...
    def connectionCapabilities(self, /) -> PySide6.QtDBus.QDBusConnection.ConnectionCapability: ...
    @typing.overload
    def disconnect(self, service: str, path: str, interface: str, name: str, receiver: PySide6.QtCore.QObject, slot: bytes | bytearray | memoryview, /) -> bool: ...
    @typing.overload
    def disconnect(self, service: str, path: str, interface: str, name: str, signature: str, receiver: PySide6.QtCore.QObject, slot: bytes | bytearray | memoryview, /) -> bool: ...
    @typing.overload
    def disconnect(self, service: str, path: str, interface: str, name: str, argumentMatch: collections.abc.Sequence[str], signature: str, receiver: PySide6.QtCore.QObject, slot: bytes | bytearray | memoryview, /) -> bool: ...
    @staticmethod
    def disconnectFromBus(name: str, /) -> None: ...
    @staticmethod
    def disconnectFromPeer(name: str, /) -> None: ...
    def interface(self, /) -> PySide6.QtDBus.QDBusConnectionInterface: ...
    def internalPointer(self, /) -> int: ...
    def isConnected(self, /) -> bool: ...
    def lastError(self, /) -> PySide6.QtDBus.QDBusError: ...
    @staticmethod
    def localMachineId() -> PySide6.QtCore.QByteArray: ...
    def name(self, /) -> str: ...
    def objectRegisteredAt(self, path: str, /) -> PySide6.QtCore.QObject: ...
    @typing.overload
    def registerObject(self, path: str, object: PySide6.QtCore.QObject, /, options: PySide6.QtDBus.QDBusConnection.RegisterOption = ...) -> bool: ...
    @typing.overload
    def registerObject(self, path: str, interface: str, object: PySide6.QtCore.QObject, /, options: PySide6.QtDBus.QDBusConnection.RegisterOption = ...) -> bool: ...
    def registerService(self, serviceName: str, /) -> bool: ...
    def registerVirtualObject(self, path: str, object: PySide6.QtDBus.QDBusVirtualObject, /, options: PySide6.QtDBus.QDBusConnection.VirtualObjectRegisterOption = ...) -> bool: ...
    def send(self, message: PySide6.QtDBus.QDBusMessage, /) -> bool: ...
    @staticmethod
    def sessionBus() -> PySide6.QtDBus.QDBusConnection: ...
    def swap(self, other: PySide6.QtDBus.QDBusConnection, /) -> None: ...
    @staticmethod
    def systemBus() -> PySide6.QtDBus.QDBusConnection: ...
    def unregisterObject(self, path: str, /, mode: PySide6.QtDBus.QDBusConnection.UnregisterMode = ...) -> None: ...
    def unregisterService(self, serviceName: str, /) -> bool: ...


class QDBusConnectionInterface(PySide6.QtDBus.QDBusAbstractInterface):

    NameAcquired             : typing.ClassVar[Signal] = ... # NameAcquired(QString)
    NameLost                 : typing.ClassVar[Signal] = ... # NameLost(QString)
    NameOwnerChanged         : typing.ClassVar[Signal] = ... # NameOwnerChanged(QString,QString,QString)
    callWithCallbackFailed   : typing.ClassVar[Signal] = ... # callWithCallbackFailed(QDBusError,QDBusMessage)
    serviceOwnerChanged      : typing.ClassVar[Signal] = ... # serviceOwnerChanged(QString,QString,QString)
    serviceRegistered        : typing.ClassVar[Signal] = ... # serviceRegistered(QString)
    serviceUnregistered      : typing.ClassVar[Signal] = ... # serviceUnregistered(QString)

    class RegisterServiceReply(enum.Enum):

        ServiceNotRegistered      = ...  # 0x0
        ServiceRegistered         = ...  # 0x1
        ServiceQueued             = ...  # 0x2

    class ServiceQueueOptions(enum.Enum):

        DontQueueService          = ...  # 0x0
        QueueService              = ...  # 0x1
        ReplaceExistingService    = ...  # 0x2

    class ServiceReplacementOptions(enum.Enum):

        DontAllowReplacement      = ...  # 0x0
        AllowReplacement          = ...  # 0x1


    def activatableServiceNames(self, /) -> PySide6.QtDBus.QDBusReply: ...
    def connectNotify(self, arg__1: PySide6.QtCore.QMetaMethod, /) -> None: ...
    def disconnectNotify(self, arg__1: PySide6.QtCore.QMetaMethod, /) -> None: ...
    def isServiceRegistered(self, arg__1: str, /) -> PySide6.QtDBus.QDBusReply: ...
    def registerService(self, arg__1: str, arg__2: PySide6.QtDBus.QDBusConnectionInterface.ServiceQueueOptions, arg__3: PySide6.QtDBus.QDBusConnectionInterface.ServiceReplacementOptions, /) -> PySide6.QtDBus.QDBusReply: ...
    def registeredServiceNames(self, /) -> PySide6.QtDBus.QDBusReply: ...
    def serviceOwner(self, arg__1: str, /) -> PySide6.QtDBus.QDBusReply: ...
    def servicePid(self, arg__1: str, /) -> PySide6.QtDBus.QDBusReply: ...
    def serviceUid(self, arg__1: str, /) -> PySide6.QtDBus.QDBusReply: ...
    def startService(self, arg__1: str, /) -> PySide6.QtDBus.QDBusReply: ...
    def unregisterService(self, arg__1: str, /) -> PySide6.QtDBus.QDBusReply: ...


class QDBusContext(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QDBusContext: PySide6.QtDBus.QDBusContext, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def calledFromDBus(self, /) -> bool: ...
    def connection(self, /) -> PySide6.QtDBus.QDBusConnection: ...
    def isDelayedReply(self, /) -> bool: ...
    def message(self, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def sendErrorReply(self, type: PySide6.QtDBus.QDBusError.ErrorType, /, msg: str = ...) -> None: ...
    @typing.overload
    def sendErrorReply(self, name: str, /, msg: str = ...) -> None: ...
    def setDelayedReply(self, enable: bool, /) -> None: ...


class QDBusError(Shiboken.Object):

    class ErrorType(enum.Enum):

        NoError                   = ...  # 0x0
        Other                     = ...  # 0x1
        Failed                    = ...  # 0x2
        NoMemory                  = ...  # 0x3
        ServiceUnknown            = ...  # 0x4
        NoReply                   = ...  # 0x5
        BadAddress                = ...  # 0x6
        NotSupported              = ...  # 0x7
        LimitsExceeded            = ...  # 0x8
        AccessDenied              = ...  # 0x9
        NoServer                  = ...  # 0xa
        Timeout                   = ...  # 0xb
        NoNetwork                 = ...  # 0xc
        AddressInUse              = ...  # 0xd
        Disconnected              = ...  # 0xe
        InvalidArgs               = ...  # 0xf
        UnknownMethod             = ...  # 0x10
        TimedOut                  = ...  # 0x11
        InvalidSignature          = ...  # 0x12
        UnknownInterface          = ...  # 0x13
        UnknownObject             = ...  # 0x14
        UnknownProperty           = ...  # 0x15
        PropertyReadOnly          = ...  # 0x16
        InternalError             = ...  # 0x17
        InvalidService            = ...  # 0x18
        InvalidObjectPath         = ...  # 0x19
        InvalidInterface          = ...  # 0x1a
        InvalidMember             = ...  # 0x1b
        LastErrorType             = ...  # 0x1b


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, error: PySide6.QtDBus.QDBusError.ErrorType, message: str, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtDBus.QDBusError, /) -> None: ...
    @typing.overload
    def __init__(self, msg: PySide6.QtDBus.QDBusMessage, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __repr__(self, /) -> str: ...
    @staticmethod
    def errorString(error: PySide6.QtDBus.QDBusError.ErrorType, /) -> str: ...
    def isValid(self, /) -> bool: ...
    def message(self, /) -> str: ...
    def name(self, /) -> str: ...
    def swap(self, other: PySide6.QtDBus.QDBusError | PySide6.QtDBus.QDBusMessage, /) -> None: ...
    def type(self, /) -> PySide6.QtDBus.QDBusError.ErrorType: ...


class QDBusInterface(PySide6.QtDBus.QDBusAbstractInterface):

    def __init__(self, service: str, path: str, /, interface: str = ..., connection: PySide6.QtDBus.QDBusConnection = ..., parent: PySide6.QtCore.QObject | None = ...) -> None: ...


class QDBusMessage(Shiboken.Object):

    class MessageType(enum.Enum):

        InvalidMessage            = ...  # 0x0
        MethodCallMessage         = ...  # 0x1
        ReplyMessage              = ...  # 0x2
        ErrorMessage              = ...  # 0x3
        SignalMessage             = ...  # 0x4


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtDBus.QDBusMessage, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __lshift__(self, arg: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    def __repr__(self, /) -> str: ...
    def arguments(self, /) -> typing.List[typing.Any]: ...
    def autoStartService(self, /) -> bool: ...
    @typing.overload
    @staticmethod
    def createError(type: PySide6.QtDBus.QDBusError.ErrorType, msg: str, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    @staticmethod
    def createError(name: str, msg: str, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    @staticmethod
    def createError(err: PySide6.QtDBus.QDBusError | PySide6.QtDBus.QDBusMessage, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def createErrorReply(self, type: PySide6.QtDBus.QDBusError.ErrorType, msg: str, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def createErrorReply(self, name: str, msg: str, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def createErrorReply(self, err: PySide6.QtDBus.QDBusError | PySide6.QtDBus.QDBusMessage, /) -> PySide6.QtDBus.QDBusMessage: ...
    @staticmethod
    def createMethodCall(destination: str, path: str, interface: str, method: str, /) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def createReply(self, /, arguments: collections.abc.Sequence[typing.Any] = ...) -> PySide6.QtDBus.QDBusMessage: ...
    @typing.overload
    def createReply(self, argument: typing.Any, /) -> PySide6.QtDBus.QDBusMessage: ...
    @staticmethod
    def createSignal(path: str, interface: str, name: str, /) -> PySide6.QtDBus.QDBusMessage: ...
    @staticmethod
    def createTargetedSignal(service: str, path: str, interface: str, name: str, /) -> PySide6.QtDBus.QDBusMessage: ...
    def errorMessage(self, /) -> str: ...
    def errorName(self, /) -> str: ...
    def interface(self, /) -> str: ...
    def isDelayedReply(self, /) -> bool: ...
    def isInteractiveAuthorizationAllowed(self, /) -> bool: ...
    def isReplyRequired(self, /) -> bool: ...
    def member(self, /) -> str: ...
    def path(self, /) -> str: ...
    def service(self, /) -> str: ...
    def setArguments(self, arguments: collections.abc.Sequence[typing.Any], /) -> None: ...
    def setAutoStartService(self, enable: bool, /) -> None: ...
    def setDelayedReply(self, enable: bool, /) -> None: ...
    def setInteractiveAuthorizationAllowed(self, enable: bool, /) -> None: ...
    def signature(self, /) -> str: ...
    def swap(self, other: PySide6.QtDBus.QDBusMessage, /) -> None: ...
    def type(self, /) -> PySide6.QtDBus.QDBusMessage.MessageType: ...


class QDBusObjectPath(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QDBusObjectPath: PySide6.QtDBus.QDBusObjectPath, /) -> None: ...
    @typing.overload
    def __init__(self, path: str, /) -> None: ...
    @typing.overload
    def __init__(self, path: bytes | bytearray | memoryview, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtDBus.QDBusObjectPath, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __lt__(self, rhs: PySide6.QtDBus.QDBusObjectPath, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtDBus.QDBusObjectPath, /) -> bool: ...
    def path(self, /) -> str: ...
    def setPath(self, path: str, /) -> None: ...
    def swap(self, other: PySide6.QtDBus.QDBusObjectPath, /) -> None: ...


class QDBusPendingCall(Shiboken.Object):

    def __init__(self, other: PySide6.QtDBus.QDBusPendingCall, /) -> None: ...

    def error(self, /) -> PySide6.QtDBus.QDBusError: ...
    @staticmethod
    def fromCompletedCall(message: PySide6.QtDBus.QDBusMessage, /) -> PySide6.QtDBus.QDBusPendingCall: ...
    @staticmethod
    def fromError(error: PySide6.QtDBus.QDBusError | PySide6.QtDBus.QDBusMessage, /) -> PySide6.QtDBus.QDBusPendingCall: ...
    def isError(self, /) -> bool: ...
    def isFinished(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def reply(self, /) -> PySide6.QtDBus.QDBusMessage: ...
    def swap(self, other: PySide6.QtDBus.QDBusPendingCall, /) -> None: ...
    def waitForFinished(self, /) -> None: ...


class QDBusPendingCallWatcher(PySide6.QtCore.QObject, PySide6.QtDBus.QDBusPendingCall):

    finished                 : typing.ClassVar[Signal] = ... # finished(); finished(QDBusPendingCallWatcher*)

    def __init__(self, call: PySide6.QtDBus.QDBusPendingCall, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def waitForFinished(self, /) -> None: ...


class QDBusReply(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, reply: PySide6.QtDBus.QDBusMessage, /) -> None: ...

    def error(self, /) -> PySide6.QtDBus.QDBusError: ...
    def isValid(self, /) -> bool: ...
    def value(self, /) -> typing.Any: ...


class QDBusServer(PySide6.QtCore.QObject):

    newConnection            : typing.ClassVar[Signal] = ... # newConnection(QDBusConnection)

    @typing.overload
    def __init__(self, address: str, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def address(self, /) -> str: ...
    def isAnonymousAuthenticationAllowed(self, /) -> bool: ...
    def isConnected(self, /) -> bool: ...
    def lastError(self, /) -> PySide6.QtDBus.QDBusError: ...
    def setAnonymousAuthenticationAllowed(self, value: bool, /) -> None: ...


class QDBusServiceWatcher(PySide6.QtCore.QObject):

    serviceOwnerChanged      : typing.ClassVar[Signal] = ... # serviceOwnerChanged(QString,QString,QString)
    serviceRegistered        : typing.ClassVar[Signal] = ... # serviceRegistered(QString)
    serviceUnregistered      : typing.ClassVar[Signal] = ... # serviceUnregistered(QString)

    class WatchModeFlag(enum.Flag):

        WatchForRegistration      = ...  # 0x1
        WatchForUnregistration    = ...  # 0x2
        WatchForOwnerChange       = ...  # 0x3


    @typing.overload
    def __init__(self, service: str, connection: PySide6.QtDBus.QDBusConnection, /, watchMode: PySide6.QtDBus.QDBusServiceWatcher.WatchModeFlag = ..., parent: PySide6.QtCore.QObject | None = ..., *, watchedServices: collections.abc.Sequence[str] | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, watchedServices: collections.abc.Sequence[str] | None = ..., watchMode: PySide6.QtDBus.QDBusServiceWatcher.WatchModeFlag | None = ...) -> None: ...

    def addWatchedService(self, newService: str, /) -> None: ...
    def connection(self, /) -> PySide6.QtDBus.QDBusConnection: ...
    def removeWatchedService(self, service: str, /) -> bool: ...
    def setConnection(self, connection: PySide6.QtDBus.QDBusConnection, /) -> None: ...
    def setWatchMode(self, mode: PySide6.QtDBus.QDBusServiceWatcher.WatchModeFlag, /) -> None: ...
    def setWatchedServices(self, services: collections.abc.Sequence[str], /) -> None: ...
    def watchMode(self, /) -> PySide6.QtDBus.QDBusServiceWatcher.WatchModeFlag: ...
    def watchedServices(self, /) -> typing.List[str]: ...


class QDBusSignature(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, signature: str, /) -> None: ...
    @typing.overload
    def __init__(self, signature: bytes | bytearray | memoryview, /) -> None: ...

    def __eq__(self, rhs: PySide6.QtDBus.QDBusSignature, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __lt__(self, rhs: PySide6.QtDBus.QDBusSignature, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtDBus.QDBusSignature, /) -> bool: ...
    def setSignature(self, signature: str, /) -> None: ...
    def signature(self, /) -> str: ...
    def swap(self, other: PySide6.QtDBus.QDBusSignature, /) -> None: ...


class QDBusUnixFileDescriptor(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtDBus.QDBusUnixFileDescriptor, /) -> None: ...
    @typing.overload
    def __init__(self, fileDescriptor: int, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def fileDescriptor(self, /) -> int: ...
    def giveFileDescriptor(self, fileDescriptor: int, /) -> None: ...
    @staticmethod
    def isSupported() -> bool: ...
    def isValid(self, /) -> bool: ...
    def setFileDescriptor(self, fileDescriptor: int, /) -> None: ...
    def swap(self, other: PySide6.QtDBus.QDBusUnixFileDescriptor, /) -> None: ...
    def takeFileDescriptor(self, /) -> int: ...


class QDBusVariant(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QDBusVariant: PySide6.QtDBus.QDBusVariant, /) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, v2: PySide6.QtDBus.QDBusVariant, /) -> bool: ...
    def setVariant(self, variant: typing.Any, /) -> None: ...
    def swap(self, other: PySide6.QtDBus.QDBusVariant, /) -> None: ...
    def variant(self, /) -> typing.Any: ...


class QDBusVirtualObject(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def handleMessage(self, message: PySide6.QtDBus.QDBusMessage, connection: PySide6.QtDBus.QDBusConnection, /) -> bool: ...
    def introspect(self, path: str, /) -> str: ...


class QIntList: ...


# eof
