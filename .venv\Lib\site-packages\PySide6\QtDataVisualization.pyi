# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtDataVisualization, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtDataVisualization`

import PySide6.QtDataVisualization
import PySide6.QtCore
import PySide6.QtGui

import enum
import typing
import collections
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class Q3DBars(PySide6.QtDataVisualization.QAbstract3DGraph):

    barSeriesMarginChanged   : typing.ClassVar[Signal] = ... # barSeriesMarginChanged(QSizeF)
    barSpacingChanged        : typing.ClassVar[Signal] = ... # barSpacingChanged(QSizeF)
    barSpacingRelativeChanged: typing.ClassVar[Signal] = ... # barSpacingRelativeChanged(bool)
    barThicknessChanged      : typing.ClassVar[Signal] = ... # barThicknessChanged(float)
    columnAxisChanged        : typing.ClassVar[Signal] = ... # columnAxisChanged(QCategory3DAxis*)
    floorLevelChanged        : typing.ClassVar[Signal] = ... # floorLevelChanged(float)
    multiSeriesUniformChanged: typing.ClassVar[Signal] = ... # multiSeriesUniformChanged(bool)
    primarySeriesChanged     : typing.ClassVar[Signal] = ... # primarySeriesChanged(QBar3DSeries*)
    rowAxisChanged           : typing.ClassVar[Signal] = ... # rowAxisChanged(QCategory3DAxis*)
    selectedSeriesChanged    : typing.ClassVar[Signal] = ... # selectedSeriesChanged(QBar3DSeries*)
    valueAxisChanged         : typing.ClassVar[Signal] = ... # valueAxisChanged(QValue3DAxis*)

    def __init__(self, /, format: PySide6.QtGui.QSurfaceFormat | PySide6.QtGui.QSurfaceFormat.FormatOption | None = ..., parent: PySide6.QtGui.QWindow | None = ..., *, multiSeriesUniform: bool | None = ..., barThickness: float | None = ..., barSpacing: PySide6.QtCore.QSizeF | None = ..., barSpacingRelative: bool | None = ..., barSeriesMargin: PySide6.QtCore.QSizeF | None = ..., rowAxis: PySide6.QtDataVisualization.QCategory3DAxis | None = ..., columnAxis: PySide6.QtDataVisualization.QCategory3DAxis | None = ..., valueAxis: PySide6.QtDataVisualization.QValue3DAxis | None = ..., primarySeries: PySide6.QtDataVisualization.QBar3DSeries | None = ..., selectedSeries: PySide6.QtDataVisualization.QBar3DSeries | None = ..., floorLevel: float | None = ...) -> None: ...

    def addAxis(self, axis: PySide6.QtDataVisualization.QAbstract3DAxis, /) -> None: ...
    def addSeries(self, series: PySide6.QtDataVisualization.QBar3DSeries, /) -> None: ...
    def axes(self, /) -> typing.List[PySide6.QtDataVisualization.QAbstract3DAxis]: ...
    def barSeriesMargin(self, /) -> PySide6.QtCore.QSizeF: ...
    def barSpacing(self, /) -> PySide6.QtCore.QSizeF: ...
    def barThickness(self, /) -> float: ...
    def columnAxis(self, /) -> PySide6.QtDataVisualization.QCategory3DAxis: ...
    def floorLevel(self, /) -> float: ...
    def insertSeries(self, index: int, series: PySide6.QtDataVisualization.QBar3DSeries, /) -> None: ...
    def isBarSpacingRelative(self, /) -> bool: ...
    def isMultiSeriesUniform(self, /) -> bool: ...
    def primarySeries(self, /) -> PySide6.QtDataVisualization.QBar3DSeries: ...
    def releaseAxis(self, axis: PySide6.QtDataVisualization.QAbstract3DAxis, /) -> None: ...
    def removeSeries(self, series: PySide6.QtDataVisualization.QBar3DSeries, /) -> None: ...
    def rowAxis(self, /) -> PySide6.QtDataVisualization.QCategory3DAxis: ...
    def selectedSeries(self, /) -> PySide6.QtDataVisualization.QBar3DSeries: ...
    def seriesList(self, /) -> typing.List[PySide6.QtDataVisualization.QBar3DSeries]: ...
    def setBarSeriesMargin(self, margin: PySide6.QtCore.QSizeF | PySide6.QtCore.QSize, /) -> None: ...
    def setBarSpacing(self, spacing: PySide6.QtCore.QSizeF | PySide6.QtCore.QSize, /) -> None: ...
    def setBarSpacingRelative(self, relative: bool, /) -> None: ...
    def setBarThickness(self, thicknessRatio: float, /) -> None: ...
    def setColumnAxis(self, axis: PySide6.QtDataVisualization.QCategory3DAxis, /) -> None: ...
    def setFloorLevel(self, level: float, /) -> None: ...
    def setMultiSeriesUniform(self, uniform: bool, /) -> None: ...
    def setPrimarySeries(self, series: PySide6.QtDataVisualization.QBar3DSeries, /) -> None: ...
    def setRowAxis(self, axis: PySide6.QtDataVisualization.QCategory3DAxis, /) -> None: ...
    def setValueAxis(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...
    def valueAxis(self, /) -> PySide6.QtDataVisualization.QValue3DAxis: ...


class Q3DCamera(PySide6.QtDataVisualization.Q3DObject):

    cameraPresetChanged      : typing.ClassVar[Signal] = ... # cameraPresetChanged(Q3DCamera::CameraPreset)
    maxZoomLevelChanged      : typing.ClassVar[Signal] = ... # maxZoomLevelChanged(float)
    minZoomLevelChanged      : typing.ClassVar[Signal] = ... # minZoomLevelChanged(float)
    targetChanged            : typing.ClassVar[Signal] = ... # targetChanged(QVector3D)
    wrapXRotationChanged     : typing.ClassVar[Signal] = ... # wrapXRotationChanged(bool)
    wrapYRotationChanged     : typing.ClassVar[Signal] = ... # wrapYRotationChanged(bool)
    xRotationChanged         : typing.ClassVar[Signal] = ... # xRotationChanged(float)
    yRotationChanged         : typing.ClassVar[Signal] = ... # yRotationChanged(float)
    zoomLevelChanged         : typing.ClassVar[Signal] = ... # zoomLevelChanged(float)

    class CameraPreset(enum.Enum):

        CameraPresetNone          = ...  # -1
        CameraPresetFrontLow      = ...  # 0x0
        CameraPresetFront         = ...  # 0x1
        CameraPresetFrontHigh     = ...  # 0x2
        CameraPresetLeftLow       = ...  # 0x3
        CameraPresetLeft          = ...  # 0x4
        CameraPresetLeftHigh      = ...  # 0x5
        CameraPresetRightLow      = ...  # 0x6
        CameraPresetRight         = ...  # 0x7
        CameraPresetRightHigh     = ...  # 0x8
        CameraPresetBehindLow     = ...  # 0x9
        CameraPresetBehind        = ...  # 0xa
        CameraPresetBehindHigh    = ...  # 0xb
        CameraPresetIsometricLeft = ...  # 0xc
        CameraPresetIsometricLeftHigh = ...  # 0xd
        CameraPresetIsometricRight = ...  # 0xe
        CameraPresetIsometricRightHigh = ...  # 0xf
        CameraPresetDirectlyAbove = ...  # 0x10
        CameraPresetDirectlyAboveCW45 = ...  # 0x11
        CameraPresetDirectlyAboveCCW45 = ...  # 0x12
        CameraPresetFrontBelow    = ...  # 0x13
        CameraPresetLeftBelow     = ...  # 0x14
        CameraPresetRightBelow    = ...  # 0x15
        CameraPresetBehindBelow   = ...  # 0x16
        CameraPresetDirectlyBelow = ...  # 0x17


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, xRotation: float | None = ..., yRotation: float | None = ..., zoomLevel: float | None = ..., cameraPreset: PySide6.QtDataVisualization.Q3DCamera.CameraPreset | None = ..., wrapXRotation: bool | None = ..., wrapYRotation: bool | None = ..., target: PySide6.QtGui.QVector3D | None = ..., minZoomLevel: float | None = ..., maxZoomLevel: float | None = ...) -> None: ...

    def cameraPreset(self, /) -> PySide6.QtDataVisualization.Q3DCamera.CameraPreset: ...
    def copyValuesFrom(self, source: PySide6.QtDataVisualization.Q3DObject, /) -> None: ...
    def maxZoomLevel(self, /) -> float: ...
    def minZoomLevel(self, /) -> float: ...
    def setCameraPosition(self, horizontal: float, vertical: float, /, zoom: float = ...) -> None: ...
    def setCameraPreset(self, preset: PySide6.QtDataVisualization.Q3DCamera.CameraPreset, /) -> None: ...
    def setMaxZoomLevel(self, zoomLevel: float, /) -> None: ...
    def setMinZoomLevel(self, zoomLevel: float, /) -> None: ...
    def setTarget(self, target: PySide6.QtGui.QVector3D, /) -> None: ...
    def setWrapXRotation(self, isEnabled: bool, /) -> None: ...
    def setWrapYRotation(self, isEnabled: bool, /) -> None: ...
    def setXRotation(self, rotation: float, /) -> None: ...
    def setYRotation(self, rotation: float, /) -> None: ...
    def setZoomLevel(self, zoomLevel: float, /) -> None: ...
    def target(self, /) -> PySide6.QtGui.QVector3D: ...
    def wrapXRotation(self, /) -> bool: ...
    def wrapYRotation(self, /) -> bool: ...
    def xRotation(self, /) -> float: ...
    def yRotation(self, /) -> float: ...
    def zoomLevel(self, /) -> float: ...


class Q3DInputHandler(PySide6.QtDataVisualization.QAbstract3DInputHandler):

    rotationEnabledChanged   : typing.ClassVar[Signal] = ... # rotationEnabledChanged(bool)
    selectionEnabledChanged  : typing.ClassVar[Signal] = ... # selectionEnabledChanged(bool)
    zoomAtTargetEnabledChanged: typing.ClassVar[Signal] = ... # zoomAtTargetEnabledChanged(bool)
    zoomEnabledChanged       : typing.ClassVar[Signal] = ... # zoomEnabledChanged(bool)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, rotationEnabled: bool | None = ..., zoomEnabled: bool | None = ..., selectionEnabled: bool | None = ..., zoomAtTargetEnabled: bool | None = ...) -> None: ...

    def isRotationEnabled(self, /) -> bool: ...
    def isSelectionEnabled(self, /) -> bool: ...
    def isZoomAtTargetEnabled(self, /) -> bool: ...
    def isZoomEnabled(self, /) -> bool: ...
    def mouseMoveEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint, /) -> None: ...
    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint, /) -> None: ...
    def mouseReleaseEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint, /) -> None: ...
    def setRotationEnabled(self, enable: bool, /) -> None: ...
    def setSelectionEnabled(self, enable: bool, /) -> None: ...
    def setZoomAtTargetEnabled(self, enable: bool, /) -> None: ...
    def setZoomEnabled(self, enable: bool, /) -> None: ...
    def wheelEvent(self, event: PySide6.QtGui.QWheelEvent, /) -> None: ...


class Q3DLight(PySide6.QtDataVisualization.Q3DObject):

    autoPositionChanged      : typing.ClassVar[Signal] = ... # autoPositionChanged(bool)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, autoPosition: bool | None = ...) -> None: ...

    def isAutoPosition(self, /) -> bool: ...
    def setAutoPosition(self, enabled: bool, /) -> None: ...


class Q3DObject(PySide6.QtCore.QObject):

    positionChanged          : typing.ClassVar[Signal] = ... # positionChanged(QVector3D)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, parentScene: PySide6.QtDataVisualization.Q3DScene | None = ..., position: PySide6.QtGui.QVector3D | None = ...) -> None: ...

    def copyValuesFrom(self, source: PySide6.QtDataVisualization.Q3DObject, /) -> None: ...
    def isDirty(self, /) -> bool: ...
    def parentScene(self, /) -> PySide6.QtDataVisualization.Q3DScene: ...
    def position(self, /) -> PySide6.QtGui.QVector3D: ...
    def setDirty(self, dirty: bool, /) -> None: ...
    def setPosition(self, position: PySide6.QtGui.QVector3D, /) -> None: ...


class Q3DScatter(PySide6.QtDataVisualization.QAbstract3DGraph):

    axisXChanged             : typing.ClassVar[Signal] = ... # axisXChanged(QValue3DAxis*)
    axisYChanged             : typing.ClassVar[Signal] = ... # axisYChanged(QValue3DAxis*)
    axisZChanged             : typing.ClassVar[Signal] = ... # axisZChanged(QValue3DAxis*)
    selectedSeriesChanged    : typing.ClassVar[Signal] = ... # selectedSeriesChanged(QScatter3DSeries*)

    def __init__(self, /, format: PySide6.QtGui.QSurfaceFormat | PySide6.QtGui.QSurfaceFormat.FormatOption | None = ..., parent: PySide6.QtGui.QWindow | None = ..., *, axisX: PySide6.QtDataVisualization.QValue3DAxis | None = ..., axisY: PySide6.QtDataVisualization.QValue3DAxis | None = ..., axisZ: PySide6.QtDataVisualization.QValue3DAxis | None = ..., selectedSeries: PySide6.QtDataVisualization.QScatter3DSeries | None = ...) -> None: ...

    def addAxis(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...
    def addSeries(self, series: PySide6.QtDataVisualization.QScatter3DSeries, /) -> None: ...
    def axes(self, /) -> typing.List[PySide6.QtDataVisualization.QValue3DAxis]: ...
    def axisX(self, /) -> PySide6.QtDataVisualization.QValue3DAxis: ...
    def axisY(self, /) -> PySide6.QtDataVisualization.QValue3DAxis: ...
    def axisZ(self, /) -> PySide6.QtDataVisualization.QValue3DAxis: ...
    def releaseAxis(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...
    def removeSeries(self, series: PySide6.QtDataVisualization.QScatter3DSeries, /) -> None: ...
    def selectedSeries(self, /) -> PySide6.QtDataVisualization.QScatter3DSeries: ...
    def seriesList(self, /) -> typing.List[PySide6.QtDataVisualization.QScatter3DSeries]: ...
    def setAxisX(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...
    def setAxisY(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...
    def setAxisZ(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...


class Q3DScene(PySide6.QtCore.QObject):

    activeCameraChanged      : typing.ClassVar[Signal] = ... # activeCameraChanged(Q3DCamera*)
    activeLightChanged       : typing.ClassVar[Signal] = ... # activeLightChanged(Q3DLight*)
    devicePixelRatioChanged  : typing.ClassVar[Signal] = ... # devicePixelRatioChanged(float)
    graphPositionQueryChanged: typing.ClassVar[Signal] = ... # graphPositionQueryChanged(QPoint)
    primarySubViewportChanged: typing.ClassVar[Signal] = ... # primarySubViewportChanged(QRect)
    secondarySubViewportChanged: typing.ClassVar[Signal] = ... # secondarySubViewportChanged(QRect)
    secondarySubviewOnTopChanged: typing.ClassVar[Signal] = ... # secondarySubviewOnTopChanged(bool)
    selectionQueryPositionChanged: typing.ClassVar[Signal] = ... # selectionQueryPositionChanged(QPoint)
    slicingActiveChanged     : typing.ClassVar[Signal] = ... # slicingActiveChanged(bool)
    viewportChanged          : typing.ClassVar[Signal] = ... # viewportChanged(QRect)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, viewport: PySide6.QtCore.QRect | None = ..., primarySubViewport: PySide6.QtCore.QRect | None = ..., secondarySubViewport: PySide6.QtCore.QRect | None = ..., selectionQueryPosition: PySide6.QtCore.QPoint | None = ..., secondarySubviewOnTop: bool | None = ..., slicingActive: bool | None = ..., activeCamera: PySide6.QtDataVisualization.Q3DCamera | None = ..., activeLight: PySide6.QtDataVisualization.Q3DLight | None = ..., devicePixelRatio: float | None = ..., graphPositionQuery: PySide6.QtCore.QPoint | None = ...) -> None: ...

    def activeCamera(self, /) -> PySide6.QtDataVisualization.Q3DCamera: ...
    def activeLight(self, /) -> PySide6.QtDataVisualization.Q3DLight: ...
    def devicePixelRatio(self, /) -> float: ...
    def graphPositionQuery(self, /) -> PySide6.QtCore.QPoint: ...
    @staticmethod
    def invalidSelectionPoint() -> PySide6.QtCore.QPoint: ...
    def isPointInPrimarySubView(self, point: PySide6.QtCore.QPoint, /) -> bool: ...
    def isPointInSecondarySubView(self, point: PySide6.QtCore.QPoint, /) -> bool: ...
    def isSecondarySubviewOnTop(self, /) -> bool: ...
    def isSlicingActive(self, /) -> bool: ...
    def primarySubViewport(self, /) -> PySide6.QtCore.QRect: ...
    def secondarySubViewport(self, /) -> PySide6.QtCore.QRect: ...
    def selectionQueryPosition(self, /) -> PySide6.QtCore.QPoint: ...
    def setActiveCamera(self, camera: PySide6.QtDataVisualization.Q3DCamera, /) -> None: ...
    def setActiveLight(self, light: PySide6.QtDataVisualization.Q3DLight, /) -> None: ...
    def setDevicePixelRatio(self, pixelRatio: float, /) -> None: ...
    def setGraphPositionQuery(self, point: PySide6.QtCore.QPoint, /) -> None: ...
    def setPrimarySubViewport(self, primarySubViewport: PySide6.QtCore.QRect, /) -> None: ...
    def setSecondarySubViewport(self, secondarySubViewport: PySide6.QtCore.QRect, /) -> None: ...
    def setSecondarySubviewOnTop(self, isSecondaryOnTop: bool, /) -> None: ...
    def setSelectionQueryPosition(self, point: PySide6.QtCore.QPoint, /) -> None: ...
    def setSlicingActive(self, isSlicing: bool, /) -> None: ...
    def viewport(self, /) -> PySide6.QtCore.QRect: ...


class Q3DSurface(PySide6.QtDataVisualization.QAbstract3DGraph):

    axisXChanged             : typing.ClassVar[Signal] = ... # axisXChanged(QValue3DAxis*)
    axisYChanged             : typing.ClassVar[Signal] = ... # axisYChanged(QValue3DAxis*)
    axisZChanged             : typing.ClassVar[Signal] = ... # axisZChanged(QValue3DAxis*)
    flipHorizontalGridChanged: typing.ClassVar[Signal] = ... # flipHorizontalGridChanged(bool)
    selectedSeriesChanged    : typing.ClassVar[Signal] = ... # selectedSeriesChanged(QSurface3DSeries*)

    def __init__(self, /, format: PySide6.QtGui.QSurfaceFormat | PySide6.QtGui.QSurfaceFormat.FormatOption | None = ..., parent: PySide6.QtGui.QWindow | None = ..., *, axisX: PySide6.QtDataVisualization.QValue3DAxis | None = ..., axisY: PySide6.QtDataVisualization.QValue3DAxis | None = ..., axisZ: PySide6.QtDataVisualization.QValue3DAxis | None = ..., selectedSeries: PySide6.QtDataVisualization.QSurface3DSeries | None = ..., flipHorizontalGrid: bool | None = ...) -> None: ...

    def addAxis(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...
    def addSeries(self, series: PySide6.QtDataVisualization.QSurface3DSeries, /) -> None: ...
    def axes(self, /) -> typing.List[PySide6.QtDataVisualization.QValue3DAxis]: ...
    def axisX(self, /) -> PySide6.QtDataVisualization.QValue3DAxis: ...
    def axisY(self, /) -> PySide6.QtDataVisualization.QValue3DAxis: ...
    def axisZ(self, /) -> PySide6.QtDataVisualization.QValue3DAxis: ...
    def flipHorizontalGrid(self, /) -> bool: ...
    def releaseAxis(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...
    def removeSeries(self, series: PySide6.QtDataVisualization.QSurface3DSeries, /) -> None: ...
    def selectedSeries(self, /) -> PySide6.QtDataVisualization.QSurface3DSeries: ...
    def seriesList(self, /) -> typing.List[PySide6.QtDataVisualization.QSurface3DSeries]: ...
    def setAxisX(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...
    def setAxisY(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...
    def setAxisZ(self, axis: PySide6.QtDataVisualization.QValue3DAxis, /) -> None: ...
    def setFlipHorizontalGrid(self, flip: bool, /) -> None: ...


class Q3DTheme(PySide6.QtCore.QObject):

    ambientLightStrengthChanged: typing.ClassVar[Signal] = ... # ambientLightStrengthChanged(float)
    backgroundColorChanged   : typing.ClassVar[Signal] = ... # backgroundColorChanged(QColor)
    backgroundEnabledChanged : typing.ClassVar[Signal] = ... # backgroundEnabledChanged(bool)
    baseColorsChanged        : typing.ClassVar[Signal] = ... # baseColorsChanged(QList<QColor>)
    baseGradientsChanged     : typing.ClassVar[Signal] = ... # baseGradientsChanged(QList<QLinearGradient>)
    colorStyleChanged        : typing.ClassVar[Signal] = ... # colorStyleChanged(Q3DTheme::ColorStyle)
    fontChanged              : typing.ClassVar[Signal] = ... # fontChanged(QFont)
    gridEnabledChanged       : typing.ClassVar[Signal] = ... # gridEnabledChanged(bool)
    gridLineColorChanged     : typing.ClassVar[Signal] = ... # gridLineColorChanged(QColor)
    highlightLightStrengthChanged: typing.ClassVar[Signal] = ... # highlightLightStrengthChanged(float)
    labelBackgroundColorChanged: typing.ClassVar[Signal] = ... # labelBackgroundColorChanged(QColor)
    labelBackgroundEnabledChanged: typing.ClassVar[Signal] = ... # labelBackgroundEnabledChanged(bool)
    labelBorderEnabledChanged: typing.ClassVar[Signal] = ... # labelBorderEnabledChanged(bool)
    labelTextColorChanged    : typing.ClassVar[Signal] = ... # labelTextColorChanged(QColor)
    lightColorChanged        : typing.ClassVar[Signal] = ... # lightColorChanged(QColor)
    lightStrengthChanged     : typing.ClassVar[Signal] = ... # lightStrengthChanged(float)
    multiHighlightColorChanged: typing.ClassVar[Signal] = ... # multiHighlightColorChanged(QColor)
    multiHighlightGradientChanged: typing.ClassVar[Signal] = ... # multiHighlightGradientChanged(QLinearGradient)
    singleHighlightColorChanged: typing.ClassVar[Signal] = ... # singleHighlightColorChanged(QColor)
    singleHighlightGradientChanged: typing.ClassVar[Signal] = ... # singleHighlightGradientChanged(QLinearGradient)
    typeChanged              : typing.ClassVar[Signal] = ... # typeChanged(Q3DTheme::Theme)
    windowColorChanged       : typing.ClassVar[Signal] = ... # windowColorChanged(QColor)

    class ColorStyle(enum.Enum):

        ColorStyleUniform         = ...  # 0x0
        ColorStyleObjectGradient  = ...  # 0x1
        ColorStyleRangeGradient   = ...  # 0x2

    class Theme(enum.Enum):

        ThemeQt                   = ...  # 0x0
        ThemePrimaryColors        = ...  # 0x1
        ThemeDigia                = ...  # 0x2
        ThemeStoneMoss            = ...  # 0x3
        ThemeArmyBlue             = ...  # 0x4
        ThemeRetro                = ...  # 0x5
        ThemeEbony                = ...  # 0x6
        ThemeIsabelle             = ...  # 0x7
        ThemeUserDefined          = ...  # 0x8


    @typing.overload
    def __init__(self, themeType: PySide6.QtDataVisualization.Q3DTheme.Theme, /, parent: PySide6.QtCore.QObject | None = ..., *, type: PySide6.QtDataVisualization.Q3DTheme.Theme | None = ..., baseColors: collections.abc.Sequence[PySide6.QtGui.QColor] | None = ..., backgroundColor: PySide6.QtGui.QColor | None = ..., windowColor: PySide6.QtGui.QColor | None = ..., labelTextColor: PySide6.QtGui.QColor | None = ..., labelBackgroundColor: PySide6.QtGui.QColor | None = ..., gridLineColor: PySide6.QtGui.QColor | None = ..., singleHighlightColor: PySide6.QtGui.QColor | None = ..., multiHighlightColor: PySide6.QtGui.QColor | None = ..., lightColor: PySide6.QtGui.QColor | None = ..., baseGradients: collections.abc.Sequence[PySide6.QtGui.QLinearGradient] | None = ..., singleHighlightGradient: PySide6.QtGui.QLinearGradient | None = ..., multiHighlightGradient: PySide6.QtGui.QLinearGradient | None = ..., lightStrength: float | None = ..., ambientLightStrength: float | None = ..., highlightLightStrength: float | None = ..., labelBorderEnabled: bool | None = ..., font: PySide6.QtGui.QFont | None = ..., backgroundEnabled: bool | None = ..., gridEnabled: bool | None = ..., labelBackgroundEnabled: bool | None = ..., colorStyle: PySide6.QtDataVisualization.Q3DTheme.ColorStyle | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, type: PySide6.QtDataVisualization.Q3DTheme.Theme | None = ..., baseColors: collections.abc.Sequence[PySide6.QtGui.QColor] | None = ..., backgroundColor: PySide6.QtGui.QColor | None = ..., windowColor: PySide6.QtGui.QColor | None = ..., labelTextColor: PySide6.QtGui.QColor | None = ..., labelBackgroundColor: PySide6.QtGui.QColor | None = ..., gridLineColor: PySide6.QtGui.QColor | None = ..., singleHighlightColor: PySide6.QtGui.QColor | None = ..., multiHighlightColor: PySide6.QtGui.QColor | None = ..., lightColor: PySide6.QtGui.QColor | None = ..., baseGradients: collections.abc.Sequence[PySide6.QtGui.QLinearGradient] | None = ..., singleHighlightGradient: PySide6.QtGui.QLinearGradient | None = ..., multiHighlightGradient: PySide6.QtGui.QLinearGradient | None = ..., lightStrength: float | None = ..., ambientLightStrength: float | None = ..., highlightLightStrength: float | None = ..., labelBorderEnabled: bool | None = ..., font: PySide6.QtGui.QFont | None = ..., backgroundEnabled: bool | None = ..., gridEnabled: bool | None = ..., labelBackgroundEnabled: bool | None = ..., colorStyle: PySide6.QtDataVisualization.Q3DTheme.ColorStyle | None = ...) -> None: ...

    def ambientLightStrength(self, /) -> float: ...
    def backgroundColor(self, /) -> PySide6.QtGui.QColor: ...
    def baseColors(self, /) -> typing.List[PySide6.QtGui.QColor]: ...
    def baseGradients(self, /) -> typing.List[PySide6.QtGui.QLinearGradient]: ...
    def colorStyle(self, /) -> PySide6.QtDataVisualization.Q3DTheme.ColorStyle: ...
    def font(self, /) -> PySide6.QtGui.QFont: ...
    def gridLineColor(self, /) -> PySide6.QtGui.QColor: ...
    def highlightLightStrength(self, /) -> float: ...
    def isBackgroundEnabled(self, /) -> bool: ...
    def isGridEnabled(self, /) -> bool: ...
    def isLabelBackgroundEnabled(self, /) -> bool: ...
    def isLabelBorderEnabled(self, /) -> bool: ...
    def labelBackgroundColor(self, /) -> PySide6.QtGui.QColor: ...
    def labelTextColor(self, /) -> PySide6.QtGui.QColor: ...
    def lightColor(self, /) -> PySide6.QtGui.QColor: ...
    def lightStrength(self, /) -> float: ...
    def multiHighlightColor(self, /) -> PySide6.QtGui.QColor: ...
    def multiHighlightGradient(self, /) -> PySide6.QtGui.QLinearGradient: ...
    def setAmbientLightStrength(self, strength: float, /) -> None: ...
    def setBackgroundColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setBackgroundEnabled(self, enabled: bool, /) -> None: ...
    def setBaseColors(self, colors: collections.abc.Sequence[PySide6.QtGui.QColor], /) -> None: ...
    def setBaseGradients(self, gradients: collections.abc.Sequence[PySide6.QtGui.QLinearGradient], /) -> None: ...
    def setColorStyle(self, style: PySide6.QtDataVisualization.Q3DTheme.ColorStyle, /) -> None: ...
    def setFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def setGridEnabled(self, enabled: bool, /) -> None: ...
    def setGridLineColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setHighlightLightStrength(self, strength: float, /) -> None: ...
    def setLabelBackgroundColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLabelBackgroundEnabled(self, enabled: bool, /) -> None: ...
    def setLabelBorderEnabled(self, enabled: bool, /) -> None: ...
    def setLabelTextColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLightColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLightStrength(self, strength: float, /) -> None: ...
    def setMultiHighlightColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setMultiHighlightGradient(self, gradient: PySide6.QtGui.QLinearGradient, /) -> None: ...
    def setSingleHighlightColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setSingleHighlightGradient(self, gradient: PySide6.QtGui.QLinearGradient, /) -> None: ...
    def setType(self, themeType: PySide6.QtDataVisualization.Q3DTheme.Theme, /) -> None: ...
    def setWindowColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def singleHighlightColor(self, /) -> PySide6.QtGui.QColor: ...
    def singleHighlightGradient(self, /) -> PySide6.QtGui.QLinearGradient: ...
    def type(self, /) -> PySide6.QtDataVisualization.Q3DTheme.Theme: ...
    def windowColor(self, /) -> PySide6.QtGui.QColor: ...


class QAbstract3DAxis(PySide6.QtCore.QObject):

    autoAdjustRangeChanged   : typing.ClassVar[Signal] = ... # autoAdjustRangeChanged(bool)
    labelAutoRotationChanged : typing.ClassVar[Signal] = ... # labelAutoRotationChanged(float)
    labelsChanged            : typing.ClassVar[Signal] = ... # labelsChanged()
    maxChanged               : typing.ClassVar[Signal] = ... # maxChanged(float)
    minChanged               : typing.ClassVar[Signal] = ... # minChanged(float)
    orientationChanged       : typing.ClassVar[Signal] = ... # orientationChanged(QAbstract3DAxis::AxisOrientation)
    rangeChanged             : typing.ClassVar[Signal] = ... # rangeChanged(float,float)
    titleChanged             : typing.ClassVar[Signal] = ... # titleChanged(QString)
    titleFixedChanged        : typing.ClassVar[Signal] = ... # titleFixedChanged(bool)
    titleVisibilityChanged   : typing.ClassVar[Signal] = ... # titleVisibilityChanged(bool)

    class AxisOrientation(enum.Enum):

        AxisOrientationNone       = ...  # 0x0
        AxisOrientationX          = ...  # 0x1
        AxisOrientationY          = ...  # 0x2
        AxisOrientationZ          = ...  # 0x4

    class AxisType(enum.Enum):

        AxisTypeNone              = ...  # 0x0
        AxisTypeCategory          = ...  # 0x1
        AxisTypeValue             = ...  # 0x2


    def isAutoAdjustRange(self, /) -> bool: ...
    def isTitleFixed(self, /) -> bool: ...
    def isTitleVisible(self, /) -> bool: ...
    def labelAutoRotation(self, /) -> float: ...
    def labels(self, /) -> typing.List[str]: ...
    def max(self, /) -> float: ...
    def min(self, /) -> float: ...
    def orientation(self, /) -> PySide6.QtDataVisualization.QAbstract3DAxis.AxisOrientation: ...
    def setAutoAdjustRange(self, autoAdjust: bool, /) -> None: ...
    def setLabelAutoRotation(self, angle: float, /) -> None: ...
    def setLabels(self, labels: collections.abc.Sequence[str], /) -> None: ...
    def setMax(self, max: float, /) -> None: ...
    def setMin(self, min: float, /) -> None: ...
    def setRange(self, min: float, max: float, /) -> None: ...
    def setTitle(self, title: str, /) -> None: ...
    def setTitleFixed(self, fixed: bool, /) -> None: ...
    def setTitleVisible(self, visible: bool, /) -> None: ...
    def title(self, /) -> str: ...
    def type(self, /) -> PySide6.QtDataVisualization.QAbstract3DAxis.AxisType: ...


class QAbstract3DGraph(PySide6.QtGui.QWindow):

    activeInputHandlerChanged: typing.ClassVar[Signal] = ... # activeInputHandlerChanged(QAbstract3DInputHandler*)
    activeThemeChanged       : typing.ClassVar[Signal] = ... # activeThemeChanged(Q3DTheme*)
    aspectRatioChanged       : typing.ClassVar[Signal] = ... # aspectRatioChanged(double)
    currentFpsChanged        : typing.ClassVar[Signal] = ... # currentFpsChanged(double)
    horizontalAspectRatioChanged: typing.ClassVar[Signal] = ... # horizontalAspectRatioChanged(double)
    localeChanged            : typing.ClassVar[Signal] = ... # localeChanged(QLocale)
    marginChanged            : typing.ClassVar[Signal] = ... # marginChanged(double)
    measureFpsChanged        : typing.ClassVar[Signal] = ... # measureFpsChanged(bool)
    optimizationHintsChanged : typing.ClassVar[Signal] = ... # optimizationHintsChanged(QAbstract3DGraph::OptimizationHints)
    orthoProjectionChanged   : typing.ClassVar[Signal] = ... # orthoProjectionChanged(bool)
    polarChanged             : typing.ClassVar[Signal] = ... # polarChanged(bool)
    queriedGraphPositionChanged: typing.ClassVar[Signal] = ... # queriedGraphPositionChanged(QVector3D)
    radialLabelOffsetChanged : typing.ClassVar[Signal] = ... # radialLabelOffsetChanged(float)
    reflectionChanged        : typing.ClassVar[Signal] = ... # reflectionChanged(bool)
    reflectivityChanged      : typing.ClassVar[Signal] = ... # reflectivityChanged(double)
    selectedElementChanged   : typing.ClassVar[Signal] = ... # selectedElementChanged(QAbstract3DGraph::ElementType)
    selectionModeChanged     : typing.ClassVar[Signal] = ... # selectionModeChanged(QAbstract3DGraph::SelectionFlags)
    shadowQualityChanged     : typing.ClassVar[Signal] = ... # shadowQualityChanged(QAbstract3DGraph::ShadowQuality)

    class ElementType(enum.Enum):

        ElementNone               = ...  # 0x0
        ElementSeries             = ...  # 0x1
        ElementAxisXLabel         = ...  # 0x2
        ElementAxisYLabel         = ...  # 0x3
        ElementAxisZLabel         = ...  # 0x4
        ElementCustomItem         = ...  # 0x5

    class OptimizationHint(enum.Flag):

        OptimizationDefault       = ...  # 0x0
        OptimizationStatic        = ...  # 0x1

    class SelectionFlag(enum.Flag):

        SelectionNone             = ...  # 0x0
        SelectionItem             = ...  # 0x1
        SelectionRow              = ...  # 0x2
        SelectionItemAndRow       = ...  # 0x3
        SelectionColumn           = ...  # 0x4
        SelectionItemAndColumn    = ...  # 0x5
        SelectionRowAndColumn     = ...  # 0x6
        SelectionItemRowAndColumn = ...  # 0x7
        SelectionSlice            = ...  # 0x8
        SelectionMultiSeries      = ...  # 0x10

    class ShadowQuality(enum.Enum):

        ShadowQualityNone         = ...  # 0x0
        ShadowQualityLow          = ...  # 0x1
        ShadowQualityMedium       = ...  # 0x2
        ShadowQualityHigh         = ...  # 0x3
        ShadowQualitySoftLow      = ...  # 0x4
        ShadowQualitySoftMedium   = ...  # 0x5
        ShadowQualitySoftHigh     = ...  # 0x6


    def activeInputHandler(self, /) -> PySide6.QtDataVisualization.QAbstract3DInputHandler: ...
    def activeTheme(self, /) -> PySide6.QtDataVisualization.Q3DTheme: ...
    def addCustomItem(self, item: PySide6.QtDataVisualization.QCustom3DItem, /) -> int: ...
    def addInputHandler(self, inputHandler: PySide6.QtDataVisualization.QAbstract3DInputHandler, /) -> None: ...
    def addTheme(self, theme: PySide6.QtDataVisualization.Q3DTheme, /) -> None: ...
    def aspectRatio(self, /) -> float: ...
    def clearSelection(self, /) -> None: ...
    def currentFps(self, /) -> float: ...
    def customItems(self, /) -> typing.List[PySide6.QtDataVisualization.QCustom3DItem]: ...
    def event(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def exposeEvent(self, event: PySide6.QtGui.QExposeEvent, /) -> None: ...
    def hasContext(self, /) -> bool: ...
    def hasSeries(self, series: PySide6.QtDataVisualization.QAbstract3DSeries, /) -> bool: ...
    def horizontalAspectRatio(self, /) -> float: ...
    def inputHandlers(self, /) -> typing.List[PySide6.QtDataVisualization.QAbstract3DInputHandler]: ...
    def isOrthoProjection(self, /) -> bool: ...
    def isPolar(self, /) -> bool: ...
    def isReflection(self, /) -> bool: ...
    def locale(self, /) -> PySide6.QtCore.QLocale: ...
    def margin(self, /) -> float: ...
    def measureFps(self, /) -> bool: ...
    def mouseDoubleClickEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseMoveEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseReleaseEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def optimizationHints(self, /) -> PySide6.QtDataVisualization.QAbstract3DGraph.OptimizationHint: ...
    def queriedGraphPosition(self, /) -> PySide6.QtGui.QVector3D: ...
    def radialLabelOffset(self, /) -> float: ...
    def reflectivity(self, /) -> float: ...
    def releaseCustomItem(self, item: PySide6.QtDataVisualization.QCustom3DItem, /) -> None: ...
    def releaseInputHandler(self, inputHandler: PySide6.QtDataVisualization.QAbstract3DInputHandler, /) -> None: ...
    def releaseTheme(self, theme: PySide6.QtDataVisualization.Q3DTheme, /) -> None: ...
    def removeCustomItem(self, item: PySide6.QtDataVisualization.QCustom3DItem, /) -> None: ...
    def removeCustomItemAt(self, position: PySide6.QtGui.QVector3D, /) -> None: ...
    def removeCustomItems(self, /) -> None: ...
    def renderToImage(self, /, msaaSamples: int | None = ..., imageSize: PySide6.QtCore.QSize = ...) -> PySide6.QtGui.QImage: ...
    def resizeEvent(self, event: PySide6.QtGui.QResizeEvent, /) -> None: ...
    def scene(self, /) -> PySide6.QtDataVisualization.Q3DScene: ...
    def selectedAxis(self, /) -> PySide6.QtDataVisualization.QAbstract3DAxis: ...
    def selectedCustomItem(self, /) -> PySide6.QtDataVisualization.QCustom3DItem: ...
    def selectedCustomItemIndex(self, /) -> int: ...
    def selectedElement(self, /) -> PySide6.QtDataVisualization.QAbstract3DGraph.ElementType: ...
    def selectedLabelIndex(self, /) -> int: ...
    def selectionMode(self, /) -> PySide6.QtDataVisualization.QAbstract3DGraph.SelectionFlag: ...
    def setActiveInputHandler(self, inputHandler: PySide6.QtDataVisualization.QAbstract3DInputHandler, /) -> None: ...
    def setActiveTheme(self, theme: PySide6.QtDataVisualization.Q3DTheme, /) -> None: ...
    def setAspectRatio(self, ratio: float, /) -> None: ...
    def setHorizontalAspectRatio(self, ratio: float, /) -> None: ...
    def setLocale(self, locale: PySide6.QtCore.QLocale | PySide6.QtCore.QLocale.Language, /) -> None: ...
    def setMargin(self, margin: float, /) -> None: ...
    def setMeasureFps(self, enable: bool, /) -> None: ...
    def setOptimizationHints(self, hints: PySide6.QtDataVisualization.QAbstract3DGraph.OptimizationHint, /) -> None: ...
    def setOrthoProjection(self, enable: bool, /) -> None: ...
    def setPolar(self, enable: bool, /) -> None: ...
    def setRadialLabelOffset(self, offset: float, /) -> None: ...
    def setReflection(self, enable: bool, /) -> None: ...
    def setReflectivity(self, reflectivity: float, /) -> None: ...
    def setSelectionMode(self, mode: PySide6.QtDataVisualization.QAbstract3DGraph.SelectionFlag, /) -> None: ...
    def setShadowQuality(self, quality: PySide6.QtDataVisualization.QAbstract3DGraph.ShadowQuality, /) -> None: ...
    def shadowQuality(self, /) -> PySide6.QtDataVisualization.QAbstract3DGraph.ShadowQuality: ...
    def shadowsSupported(self, /) -> bool: ...
    def themes(self, /) -> typing.List[PySide6.QtDataVisualization.Q3DTheme]: ...
    def touchEvent(self, event: PySide6.QtGui.QTouchEvent, /) -> None: ...
    def wheelEvent(self, event: PySide6.QtGui.QWheelEvent, /) -> None: ...


class QAbstract3DInputHandler(PySide6.QtCore.QObject):

    inputViewChanged         : typing.ClassVar[Signal] = ... # inputViewChanged(QAbstract3DInputHandler::InputView)
    positionChanged          : typing.ClassVar[Signal] = ... # positionChanged(QPoint)
    sceneChanged             : typing.ClassVar[Signal] = ... # sceneChanged(Q3DScene*)

    class InputView(enum.Enum):

        InputViewNone             = ...  # 0x0
        InputViewOnPrimary        = ...  # 0x1
        InputViewOnSecondary      = ...  # 0x2


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, inputView: PySide6.QtDataVisualization.QAbstract3DInputHandler.InputView | None = ..., inputPosition: PySide6.QtCore.QPoint | None = ..., scene: PySide6.QtDataVisualization.Q3DScene | None = ...) -> None: ...

    def inputPosition(self, /) -> PySide6.QtCore.QPoint: ...
    def inputView(self, /) -> PySide6.QtDataVisualization.QAbstract3DInputHandler.InputView: ...
    def mouseDoubleClickEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseMoveEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint, /) -> None: ...
    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint, /) -> None: ...
    def mouseReleaseEvent(self, event: PySide6.QtGui.QMouseEvent, mousePos: PySide6.QtCore.QPoint, /) -> None: ...
    def prevDistance(self, /) -> int: ...
    def previousInputPos(self, /) -> PySide6.QtCore.QPoint: ...
    def scene(self, /) -> PySide6.QtDataVisualization.Q3DScene: ...
    def setInputPosition(self, position: PySide6.QtCore.QPoint, /) -> None: ...
    def setInputView(self, inputView: PySide6.QtDataVisualization.QAbstract3DInputHandler.InputView, /) -> None: ...
    def setPrevDistance(self, distance: int, /) -> None: ...
    def setPreviousInputPos(self, position: PySide6.QtCore.QPoint, /) -> None: ...
    def setScene(self, scene: PySide6.QtDataVisualization.Q3DScene, /) -> None: ...
    def touchEvent(self, event: PySide6.QtGui.QTouchEvent, /) -> None: ...
    def wheelEvent(self, event: PySide6.QtGui.QWheelEvent, /) -> None: ...


class QAbstract3DSeries(PySide6.QtCore.QObject):

    baseColorChanged         : typing.ClassVar[Signal] = ... # baseColorChanged(QColor)
    baseGradientChanged      : typing.ClassVar[Signal] = ... # baseGradientChanged(QLinearGradient)
    colorStyleChanged        : typing.ClassVar[Signal] = ... # colorStyleChanged(Q3DTheme::ColorStyle)
    itemLabelChanged         : typing.ClassVar[Signal] = ... # itemLabelChanged(QString)
    itemLabelFormatChanged   : typing.ClassVar[Signal] = ... # itemLabelFormatChanged(QString)
    itemLabelVisibilityChanged: typing.ClassVar[Signal] = ... # itemLabelVisibilityChanged(bool)
    meshChanged              : typing.ClassVar[Signal] = ... # meshChanged(QAbstract3DSeries::Mesh)
    meshRotationChanged      : typing.ClassVar[Signal] = ... # meshRotationChanged(QQuaternion)
    meshSmoothChanged        : typing.ClassVar[Signal] = ... # meshSmoothChanged(bool)
    multiHighlightColorChanged: typing.ClassVar[Signal] = ... # multiHighlightColorChanged(QColor)
    multiHighlightGradientChanged: typing.ClassVar[Signal] = ... # multiHighlightGradientChanged(QLinearGradient)
    nameChanged              : typing.ClassVar[Signal] = ... # nameChanged(QString)
    singleHighlightColorChanged: typing.ClassVar[Signal] = ... # singleHighlightColorChanged(QColor)
    singleHighlightGradientChanged: typing.ClassVar[Signal] = ... # singleHighlightGradientChanged(QLinearGradient)
    userDefinedMeshChanged   : typing.ClassVar[Signal] = ... # userDefinedMeshChanged(QString)
    visibilityChanged        : typing.ClassVar[Signal] = ... # visibilityChanged(bool)

    class Mesh(enum.Enum):

        MeshUserDefined           = ...  # 0x0
        MeshBar                   = ...  # 0x1
        MeshCube                  = ...  # 0x2
        MeshPyramid               = ...  # 0x3
        MeshCone                  = ...  # 0x4
        MeshCylinder              = ...  # 0x5
        MeshBevelBar              = ...  # 0x6
        MeshBevelCube             = ...  # 0x7
        MeshSphere                = ...  # 0x8
        MeshMinimal               = ...  # 0x9
        MeshArrow                 = ...  # 0xa
        MeshPoint                 = ...  # 0xb

    class SeriesType(enum.Enum):

        SeriesTypeNone            = ...  # 0x0
        SeriesTypeBar             = ...  # 0x1
        SeriesTypeScatter         = ...  # 0x2
        SeriesTypeSurface         = ...  # 0x4


    def baseColor(self, /) -> PySide6.QtGui.QColor: ...
    def baseGradient(self, /) -> PySide6.QtGui.QLinearGradient: ...
    def colorStyle(self, /) -> PySide6.QtDataVisualization.Q3DTheme.ColorStyle: ...
    def isItemLabelVisible(self, /) -> bool: ...
    def isMeshSmooth(self, /) -> bool: ...
    def isVisible(self, /) -> bool: ...
    def itemLabel(self, /) -> str: ...
    def itemLabelFormat(self, /) -> str: ...
    def mesh(self, /) -> PySide6.QtDataVisualization.QAbstract3DSeries.Mesh: ...
    def meshRotation(self, /) -> PySide6.QtGui.QQuaternion: ...
    def multiHighlightColor(self, /) -> PySide6.QtGui.QColor: ...
    def multiHighlightGradient(self, /) -> PySide6.QtGui.QLinearGradient: ...
    def name(self, /) -> str: ...
    def setBaseColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setBaseGradient(self, gradient: PySide6.QtGui.QLinearGradient, /) -> None: ...
    def setColorStyle(self, style: PySide6.QtDataVisualization.Q3DTheme.ColorStyle, /) -> None: ...
    def setItemLabelFormat(self, format: str, /) -> None: ...
    def setItemLabelVisible(self, visible: bool, /) -> None: ...
    def setMesh(self, mesh: PySide6.QtDataVisualization.QAbstract3DSeries.Mesh, /) -> None: ...
    def setMeshAxisAndAngle(self, axis: PySide6.QtGui.QVector3D, angle: float, /) -> None: ...
    def setMeshRotation(self, rotation: PySide6.QtGui.QQuaternion, /) -> None: ...
    def setMeshSmooth(self, enable: bool, /) -> None: ...
    def setMultiHighlightColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setMultiHighlightGradient(self, gradient: PySide6.QtGui.QLinearGradient, /) -> None: ...
    def setName(self, name: str, /) -> None: ...
    def setSingleHighlightColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setSingleHighlightGradient(self, gradient: PySide6.QtGui.QLinearGradient, /) -> None: ...
    def setUserDefinedMesh(self, fileName: str, /) -> None: ...
    def setVisible(self, visible: bool, /) -> None: ...
    def singleHighlightColor(self, /) -> PySide6.QtGui.QColor: ...
    def singleHighlightGradient(self, /) -> PySide6.QtGui.QLinearGradient: ...
    def type(self, /) -> PySide6.QtDataVisualization.QAbstract3DSeries.SeriesType: ...
    def userDefinedMesh(self, /) -> str: ...


class QAbstractDataProxy(PySide6.QtCore.QObject):

    class DataType(enum.Enum):

        DataTypeNone              = ...  # 0x0
        DataTypeBar               = ...  # 0x1
        DataTypeScatter           = ...  # 0x2
        DataTypeSurface           = ...  # 0x4


    def type(self, /) -> PySide6.QtDataVisualization.QAbstractDataProxy.DataType: ...


class QBar3DSeries(PySide6.QtDataVisualization.QAbstract3DSeries):

    dataProxyChanged         : typing.ClassVar[Signal] = ... # dataProxyChanged(QBarDataProxy*)
    meshAngleChanged         : typing.ClassVar[Signal] = ... # meshAngleChanged(float)
    rowColorsChanged         : typing.ClassVar[Signal] = ... # rowColorsChanged(QList<QColor>)
    selectedBarChanged       : typing.ClassVar[Signal] = ... # selectedBarChanged(QPoint)

    @typing.overload
    def __init__(self, dataProxy: PySide6.QtDataVisualization.QBarDataProxy, /, parent: PySide6.QtCore.QObject | None = ..., *, selectedBar: PySide6.QtCore.QPoint | None = ..., meshAngle: float | None = ..., rowColors: collections.abc.Sequence[PySide6.QtGui.QColor] | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, dataProxy: PySide6.QtDataVisualization.QBarDataProxy | None = ..., selectedBar: PySide6.QtCore.QPoint | None = ..., meshAngle: float | None = ..., rowColors: collections.abc.Sequence[PySide6.QtGui.QColor] | None = ...) -> None: ...

    def dataProxy(self, /) -> PySide6.QtDataVisualization.QBarDataProxy: ...
    @staticmethod
    def invalidSelectionPosition() -> PySide6.QtCore.QPoint: ...
    def meshAngle(self, /) -> float: ...
    def rowColors(self, /) -> typing.List[PySide6.QtGui.QColor]: ...
    def selectedBar(self, /) -> PySide6.QtCore.QPoint: ...
    def setDataProxy(self, proxy: PySide6.QtDataVisualization.QBarDataProxy, /) -> None: ...
    def setMeshAngle(self, angle: float, /) -> None: ...
    def setRowColors(self, colors: collections.abc.Sequence[PySide6.QtGui.QColor], /) -> None: ...
    def setSelectedBar(self, position: PySide6.QtCore.QPoint, /) -> None: ...


class QBarDataItem(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtDataVisualization.QBarDataItem, /) -> None: ...
    @typing.overload
    def __init__(self, value: float, /) -> None: ...
    @typing.overload
    def __init__(self, value: float, angle: float, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def createExtraData(self, /) -> None: ...
    def rotation(self, /) -> float: ...
    def setRotation(self, angle: float, /) -> None: ...
    def setValue(self, val: float, /) -> None: ...
    def value(self, /) -> float: ...


class QBarDataProxy(PySide6.QtDataVisualization.QAbstractDataProxy):

    arrayReset               : typing.ClassVar[Signal] = ... # arrayReset()
    columnLabelsChanged      : typing.ClassVar[Signal] = ... # columnLabelsChanged()
    itemChanged              : typing.ClassVar[Signal] = ... # itemChanged(int,int)
    rowCountChanged          : typing.ClassVar[Signal] = ... # rowCountChanged(int)
    rowLabelsChanged         : typing.ClassVar[Signal] = ... # rowLabelsChanged()
    rowsAdded                : typing.ClassVar[Signal] = ... # rowsAdded(int,int)
    rowsChanged              : typing.ClassVar[Signal] = ... # rowsChanged(int,int)
    rowsInserted             : typing.ClassVar[Signal] = ... # rowsInserted(int,int)
    rowsRemoved              : typing.ClassVar[Signal] = ... # rowsRemoved(int,int)
    seriesChanged            : typing.ClassVar[Signal] = ... # seriesChanged(QBar3DSeries*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, rowCount: int | None = ..., rowLabels: collections.abc.Sequence[str] | None = ..., columnLabels: collections.abc.Sequence[str] | None = ..., series: PySide6.QtDataVisualization.QBar3DSeries | None = ...) -> None: ...

    @typing.overload
    def addRow(self, arg__1: collections.abc.Sequence[PySide6.QtDataVisualization.QBarDataItem], /) -> int: ...
    @typing.overload
    def addRow(self, arg__1: collections.abc.Sequence[PySide6.QtDataVisualization.QBarDataItem], arg__2: str, /) -> int: ...
    @typing.overload
    def addRows(self, rows: typing.List[typing.List[PySide6.QtDataVisualization.QBarDataItem]], /) -> int: ...
    @typing.overload
    def addRows(self, rows: typing.List[typing.List[PySide6.QtDataVisualization.QBarDataItem]], labels: collections.abc.Sequence[str], /) -> int: ...
    def array(self, /) -> typing.List[typing.List[PySide6.QtDataVisualization.QBarDataItem]]: ...
    def columnLabels(self, /) -> typing.List[str]: ...
    @typing.overload
    def insertRow(self, arg__1: int, arg__2: collections.abc.Sequence[PySide6.QtDataVisualization.QBarDataItem], /) -> None: ...
    @typing.overload
    def insertRow(self, arg__1: int, arg__2: collections.abc.Sequence[PySide6.QtDataVisualization.QBarDataItem], arg__3: str, /) -> None: ...
    @typing.overload
    def insertRows(self, rowIndex: int, rows: typing.List[typing.List[PySide6.QtDataVisualization.QBarDataItem]], /) -> None: ...
    @typing.overload
    def insertRows(self, rowIndex: int, rows: typing.List[typing.List[PySide6.QtDataVisualization.QBarDataItem]], labels: collections.abc.Sequence[str], /) -> None: ...
    @typing.overload
    def itemAt(self, position: PySide6.QtCore.QPoint, /) -> PySide6.QtDataVisualization.QBarDataItem: ...
    @typing.overload
    def itemAt(self, rowIndex: int, columnIndex: int, /) -> PySide6.QtDataVisualization.QBarDataItem: ...
    def removeRows(self, rowIndex: int, removeCount: int, /, removeLabels: bool = ...) -> None: ...
    @typing.overload
    def resetArray(self, /) -> None: ...
    @typing.overload
    def resetArray(self, arg__1: typing.List[typing.List[PySide6.QtDataVisualization.QBarDataItem]], /) -> None: ...
    @typing.overload
    def resetArray(self, arg__1: typing.List[typing.List[PySide6.QtDataVisualization.QBarDataItem]], arg__2: collections.abc.Sequence[str], arg__3: collections.abc.Sequence[str], /) -> None: ...
    def rowAt(self, rowIndex: int, /) -> typing.List[PySide6.QtDataVisualization.QBarDataItem]: ...
    def rowCount(self, /) -> int: ...
    def rowLabels(self, /) -> typing.List[str]: ...
    def series(self, /) -> PySide6.QtDataVisualization.QBar3DSeries: ...
    def setColumnLabels(self, labels: collections.abc.Sequence[str], /) -> None: ...
    @typing.overload
    def setItem(self, position: PySide6.QtCore.QPoint, item: PySide6.QtDataVisualization.QBarDataItem | float, /) -> None: ...
    @typing.overload
    def setItem(self, rowIndex: int, columnIndex: int, item: PySide6.QtDataVisualization.QBarDataItem | float, /) -> None: ...
    @typing.overload
    def setRow(self, arg__1: int, arg__2: collections.abc.Sequence[PySide6.QtDataVisualization.QBarDataItem], /) -> None: ...
    @typing.overload
    def setRow(self, arg__1: int, arg__2: collections.abc.Sequence[PySide6.QtDataVisualization.QBarDataItem], arg__3: str, /) -> None: ...
    def setRowLabels(self, labels: collections.abc.Sequence[str], /) -> None: ...
    @typing.overload
    def setRows(self, rowIndex: int, rows: typing.List[typing.List[PySide6.QtDataVisualization.QBarDataItem]], /) -> None: ...
    @typing.overload
    def setRows(self, rowIndex: int, rows: typing.List[typing.List[PySide6.QtDataVisualization.QBarDataItem]], labels: collections.abc.Sequence[str], /) -> None: ...


class QCategory3DAxis(PySide6.QtDataVisualization.QAbstract3DAxis):

    labelsChanged            : typing.ClassVar[Signal] = ... # labelsChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, labels: collections.abc.Sequence[str] | None = ...) -> None: ...

    def labels(self, /) -> typing.List[str]: ...
    def setLabels(self, labels: collections.abc.Sequence[str], /) -> None: ...


class QCustom3DItem(PySide6.QtCore.QObject):

    meshFileChanged          : typing.ClassVar[Signal] = ... # meshFileChanged(QString)
    positionAbsoluteChanged  : typing.ClassVar[Signal] = ... # positionAbsoluteChanged(bool)
    positionChanged          : typing.ClassVar[Signal] = ... # positionChanged(QVector3D)
    rotationChanged          : typing.ClassVar[Signal] = ... # rotationChanged(QQuaternion)
    scalingAbsoluteChanged   : typing.ClassVar[Signal] = ... # scalingAbsoluteChanged(bool)
    scalingChanged           : typing.ClassVar[Signal] = ... # scalingChanged(QVector3D)
    shadowCastingChanged     : typing.ClassVar[Signal] = ... # shadowCastingChanged(bool)
    textureFileChanged       : typing.ClassVar[Signal] = ... # textureFileChanged(QString)
    visibleChanged           : typing.ClassVar[Signal] = ... # visibleChanged(bool)

    @typing.overload
    def __init__(self, meshFile: str, position: PySide6.QtGui.QVector3D, scaling: PySide6.QtGui.QVector3D, rotation: PySide6.QtGui.QQuaternion, texture: PySide6.QtGui.QImage, /, parent: PySide6.QtCore.QObject | None = ..., *, textureFile: str | None = ..., positionAbsolute: bool | None = ..., visible: bool | None = ..., shadowCasting: bool | None = ..., scalingAbsolute: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, meshFile: str | None = ..., textureFile: str | None = ..., position: PySide6.QtGui.QVector3D | None = ..., positionAbsolute: bool | None = ..., scaling: PySide6.QtGui.QVector3D | None = ..., rotation: PySide6.QtGui.QQuaternion | None = ..., visible: bool | None = ..., shadowCasting: bool | None = ..., scalingAbsolute: bool | None = ...) -> None: ...

    def isPositionAbsolute(self, /) -> bool: ...
    def isScalingAbsolute(self, /) -> bool: ...
    def isShadowCasting(self, /) -> bool: ...
    def isVisible(self, /) -> bool: ...
    def meshFile(self, /) -> str: ...
    def position(self, /) -> PySide6.QtGui.QVector3D: ...
    def rotation(self, /) -> PySide6.QtGui.QQuaternion: ...
    def scaling(self, /) -> PySide6.QtGui.QVector3D: ...
    def setMeshFile(self, meshFile: str, /) -> None: ...
    def setPosition(self, position: PySide6.QtGui.QVector3D, /) -> None: ...
    def setPositionAbsolute(self, positionAbsolute: bool, /) -> None: ...
    def setRotation(self, rotation: PySide6.QtGui.QQuaternion, /) -> None: ...
    def setRotationAxisAndAngle(self, axis: PySide6.QtGui.QVector3D, angle: float, /) -> None: ...
    def setScaling(self, scaling: PySide6.QtGui.QVector3D, /) -> None: ...
    def setScalingAbsolute(self, scalingAbsolute: bool, /) -> None: ...
    def setShadowCasting(self, enabled: bool, /) -> None: ...
    def setTextureFile(self, textureFile: str, /) -> None: ...
    def setTextureImage(self, textureImage: PySide6.QtGui.QImage, /) -> None: ...
    def setVisible(self, visible: bool, /) -> None: ...
    def textureFile(self, /) -> str: ...


class QCustom3DLabel(PySide6.QtDataVisualization.QCustom3DItem):

    backgroundColorChanged   : typing.ClassVar[Signal] = ... # backgroundColorChanged(QColor)
    backgroundEnabledChanged : typing.ClassVar[Signal] = ... # backgroundEnabledChanged(bool)
    borderEnabledChanged     : typing.ClassVar[Signal] = ... # borderEnabledChanged(bool)
    facingCameraChanged      : typing.ClassVar[Signal] = ... # facingCameraChanged(bool)
    fontChanged              : typing.ClassVar[Signal] = ... # fontChanged(QFont)
    textChanged              : typing.ClassVar[Signal] = ... # textChanged(QString)
    textColorChanged         : typing.ClassVar[Signal] = ... # textColorChanged(QColor)

    @typing.overload
    def __init__(self, text: str, font: PySide6.QtGui.QFont, position: PySide6.QtGui.QVector3D, scaling: PySide6.QtGui.QVector3D, rotation: PySide6.QtGui.QQuaternion, /, parent: PySide6.QtCore.QObject | None = ..., *, textColor: PySide6.QtGui.QColor | None = ..., backgroundColor: PySide6.QtGui.QColor | None = ..., borderEnabled: bool | None = ..., backgroundEnabled: bool | None = ..., facingCamera: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, text: str | None = ..., font: PySide6.QtGui.QFont | None = ..., textColor: PySide6.QtGui.QColor | None = ..., backgroundColor: PySide6.QtGui.QColor | None = ..., borderEnabled: bool | None = ..., backgroundEnabled: bool | None = ..., facingCamera: bool | None = ...) -> None: ...

    def backgroundColor(self, /) -> PySide6.QtGui.QColor: ...
    def font(self, /) -> PySide6.QtGui.QFont: ...
    def isBackgroundEnabled(self, /) -> bool: ...
    def isBorderEnabled(self, /) -> bool: ...
    def isFacingCamera(self, /) -> bool: ...
    def setBackgroundColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setBackgroundEnabled(self, enabled: bool, /) -> None: ...
    def setBorderEnabled(self, enabled: bool, /) -> None: ...
    def setFacingCamera(self, enabled: bool, /) -> None: ...
    def setFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def setText(self, text: str, /) -> None: ...
    def setTextColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def text(self, /) -> str: ...
    def textColor(self, /) -> PySide6.QtGui.QColor: ...


class QCustom3DVolume(PySide6.QtDataVisualization.QCustom3DItem):

    alphaMultiplierChanged   : typing.ClassVar[Signal] = ... # alphaMultiplierChanged(float)
    colorTableChanged        : typing.ClassVar[Signal] = ... # colorTableChanged()
    drawSliceFramesChanged   : typing.ClassVar[Signal] = ... # drawSliceFramesChanged(bool)
    drawSlicesChanged        : typing.ClassVar[Signal] = ... # drawSlicesChanged(bool)
    preserveOpacityChanged   : typing.ClassVar[Signal] = ... # preserveOpacityChanged(bool)
    sliceFrameColorChanged   : typing.ClassVar[Signal] = ... # sliceFrameColorChanged(QColor)
    sliceFrameGapsChanged    : typing.ClassVar[Signal] = ... # sliceFrameGapsChanged(QVector3D)
    sliceFrameThicknessesChanged: typing.ClassVar[Signal] = ... # sliceFrameThicknessesChanged(QVector3D)
    sliceFrameWidthsChanged  : typing.ClassVar[Signal] = ... # sliceFrameWidthsChanged(QVector3D)
    sliceIndexXChanged       : typing.ClassVar[Signal] = ... # sliceIndexXChanged(int)
    sliceIndexYChanged       : typing.ClassVar[Signal] = ... # sliceIndexYChanged(int)
    sliceIndexZChanged       : typing.ClassVar[Signal] = ... # sliceIndexZChanged(int)
    textureDataChanged       : typing.ClassVar[Signal] = ... # textureDataChanged(QList<uchar>*)
    textureDepthChanged      : typing.ClassVar[Signal] = ... # textureDepthChanged(int)
    textureFormatChanged     : typing.ClassVar[Signal] = ... # textureFormatChanged(QImage::Format)
    textureHeightChanged     : typing.ClassVar[Signal] = ... # textureHeightChanged(int)
    textureWidthChanged      : typing.ClassVar[Signal] = ... # textureWidthChanged(int)
    useHighDefShaderChanged  : typing.ClassVar[Signal] = ... # useHighDefShaderChanged(bool)

    @typing.overload
    def __init__(self, position: PySide6.QtGui.QVector3D, scaling: PySide6.QtGui.QVector3D, rotation: PySide6.QtGui.QQuaternion, textureWidth: int, textureHeight: int, textureDepth: int, textureData: collections.abc.Sequence[int], textureFormat: PySide6.QtGui.QImage.Format, colorTable: collections.abc.Sequence[int], /, parent: PySide6.QtCore.QObject | None = ..., *, sliceIndexX: int | None = ..., sliceIndexY: int | None = ..., sliceIndexZ: int | None = ..., alphaMultiplier: float | None = ..., preserveOpacity: bool | None = ..., useHighDefShader: bool | None = ..., drawSlices: bool | None = ..., drawSliceFrames: bool | None = ..., sliceFrameColor: PySide6.QtGui.QColor | None = ..., sliceFrameWidths: PySide6.QtGui.QVector3D | None = ..., sliceFrameGaps: PySide6.QtGui.QVector3D | None = ..., sliceFrameThicknesses: PySide6.QtGui.QVector3D | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, textureWidth: int | None = ..., textureHeight: int | None = ..., textureDepth: int | None = ..., sliceIndexX: int | None = ..., sliceIndexY: int | None = ..., sliceIndexZ: int | None = ..., colorTable: collections.abc.Sequence[int] | None = ..., textureData: collections.abc.Sequence[int] | None = ..., alphaMultiplier: float | None = ..., preserveOpacity: bool | None = ..., useHighDefShader: bool | None = ..., drawSlices: bool | None = ..., drawSliceFrames: bool | None = ..., sliceFrameColor: PySide6.QtGui.QColor | None = ..., sliceFrameWidths: PySide6.QtGui.QVector3D | None = ..., sliceFrameGaps: PySide6.QtGui.QVector3D | None = ..., sliceFrameThicknesses: PySide6.QtGui.QVector3D | None = ...) -> None: ...

    def alphaMultiplier(self, /) -> float: ...
    def colorTable(self, /) -> typing.List[int]: ...
    def createTextureData(self, images: collections.abc.Sequence[PySide6.QtGui.QImage], /) -> typing.List[int]: ...
    def drawSliceFrames(self, /) -> bool: ...
    def drawSlices(self, /) -> bool: ...
    def preserveOpacity(self, /) -> bool: ...
    def renderSlice(self, axis: PySide6.QtCore.Qt.Axis, index: int, /) -> PySide6.QtGui.QImage: ...
    def setAlphaMultiplier(self, mult: float, /) -> None: ...
    def setColorTable(self, colors: collections.abc.Sequence[int], /) -> None: ...
    def setDrawSliceFrames(self, enable: bool, /) -> None: ...
    def setDrawSlices(self, enable: bool, /) -> None: ...
    def setPreserveOpacity(self, enable: bool, /) -> None: ...
    def setSliceFrameColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setSliceFrameGaps(self, values: PySide6.QtGui.QVector3D, /) -> None: ...
    def setSliceFrameThicknesses(self, values: PySide6.QtGui.QVector3D, /) -> None: ...
    def setSliceFrameWidths(self, values: PySide6.QtGui.QVector3D, /) -> None: ...
    def setSliceIndexX(self, value: int, /) -> None: ...
    def setSliceIndexY(self, value: int, /) -> None: ...
    def setSliceIndexZ(self, value: int, /) -> None: ...
    def setSliceIndices(self, x: int, y: int, z: int, /) -> None: ...
    @typing.overload
    def setSubTextureData(self, axis: PySide6.QtCore.Qt.Axis, index: int, image: PySide6.QtGui.QImage, /) -> None: ...
    @typing.overload
    def setSubTextureData(self, axis: PySide6.QtCore.Qt.Axis, index: int, data: bytes | bytearray | memoryview, /) -> None: ...
    def setTextureData(self, arg__1: collections.abc.Sequence[int], /) -> None: ...
    def setTextureDepth(self, value: int, /) -> None: ...
    def setTextureDimensions(self, width: int, height: int, depth: int, /) -> None: ...
    def setTextureFormat(self, format: PySide6.QtGui.QImage.Format, /) -> None: ...
    def setTextureHeight(self, value: int, /) -> None: ...
    def setTextureWidth(self, value: int, /) -> None: ...
    def setUseHighDefShader(self, enable: bool, /) -> None: ...
    def sliceFrameColor(self, /) -> PySide6.QtGui.QColor: ...
    def sliceFrameGaps(self, /) -> PySide6.QtGui.QVector3D: ...
    def sliceFrameThicknesses(self, /) -> PySide6.QtGui.QVector3D: ...
    def sliceFrameWidths(self, /) -> PySide6.QtGui.QVector3D: ...
    def sliceIndexX(self, /) -> int: ...
    def sliceIndexY(self, /) -> int: ...
    def sliceIndexZ(self, /) -> int: ...
    def textureData(self, /) -> typing.List[int]: ...
    def textureDataWidth(self, /) -> int: ...
    def textureDepth(self, /) -> int: ...
    def textureFormat(self, /) -> PySide6.QtGui.QImage.Format: ...
    def textureHeight(self, /) -> int: ...
    def textureWidth(self, /) -> int: ...
    def useHighDefShader(self, /) -> bool: ...


class QHeightMapSurfaceDataProxy(PySide6.QtDataVisualization.QSurfaceDataProxy):

    autoScaleYChanged        : typing.ClassVar[Signal] = ... # autoScaleYChanged(bool)
    heightMapChanged         : typing.ClassVar[Signal] = ... # heightMapChanged(QImage)
    heightMapFileChanged     : typing.ClassVar[Signal] = ... # heightMapFileChanged(QString)
    maxXValueChanged         : typing.ClassVar[Signal] = ... # maxXValueChanged(float)
    maxYValueChanged         : typing.ClassVar[Signal] = ... # maxYValueChanged(float)
    maxZValueChanged         : typing.ClassVar[Signal] = ... # maxZValueChanged(float)
    minXValueChanged         : typing.ClassVar[Signal] = ... # minXValueChanged(float)
    minYValueChanged         : typing.ClassVar[Signal] = ... # minYValueChanged(float)
    minZValueChanged         : typing.ClassVar[Signal] = ... # minZValueChanged(float)

    @typing.overload
    def __init__(self, image: PySide6.QtGui.QImage, /, parent: PySide6.QtCore.QObject | None = ..., *, heightMap: PySide6.QtGui.QImage | None = ..., heightMapFile: str | None = ..., minXValue: float | None = ..., maxXValue: float | None = ..., minZValue: float | None = ..., maxZValue: float | None = ..., minYValue: float | None = ..., maxYValue: float | None = ..., autoScaleY: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, filename: str, /, parent: PySide6.QtCore.QObject | None = ..., *, heightMap: PySide6.QtGui.QImage | None = ..., heightMapFile: str | None = ..., minXValue: float | None = ..., maxXValue: float | None = ..., minZValue: float | None = ..., maxZValue: float | None = ..., minYValue: float | None = ..., maxYValue: float | None = ..., autoScaleY: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, heightMap: PySide6.QtGui.QImage | None = ..., heightMapFile: str | None = ..., minXValue: float | None = ..., maxXValue: float | None = ..., minZValue: float | None = ..., maxZValue: float | None = ..., minYValue: float | None = ..., maxYValue: float | None = ..., autoScaleY: bool | None = ...) -> None: ...

    def autoScaleY(self, /) -> bool: ...
    def heightMap(self, /) -> PySide6.QtGui.QImage: ...
    def heightMapFile(self, /) -> str: ...
    def maxXValue(self, /) -> float: ...
    def maxYValue(self, /) -> float: ...
    def maxZValue(self, /) -> float: ...
    def minXValue(self, /) -> float: ...
    def minYValue(self, /) -> float: ...
    def minZValue(self, /) -> float: ...
    def setAutoScaleY(self, enabled: bool, /) -> None: ...
    def setHeightMap(self, image: PySide6.QtGui.QImage, /) -> None: ...
    def setHeightMapFile(self, filename: str, /) -> None: ...
    def setMaxXValue(self, max: float, /) -> None: ...
    def setMaxYValue(self, max: float, /) -> None: ...
    def setMaxZValue(self, max: float, /) -> None: ...
    def setMinXValue(self, min: float, /) -> None: ...
    def setMinYValue(self, min: float, /) -> None: ...
    def setMinZValue(self, min: float, /) -> None: ...
    def setValueRanges(self, minX: float, maxX: float, minZ: float, maxZ: float, /) -> None: ...


class QIntList: ...


class QItemModelBarDataProxy(PySide6.QtDataVisualization.QBarDataProxy):

    autoColumnCategoriesChanged: typing.ClassVar[Signal] = ... # autoColumnCategoriesChanged(bool)
    autoRowCategoriesChanged : typing.ClassVar[Signal] = ... # autoRowCategoriesChanged(bool)
    columnCategoriesChanged  : typing.ClassVar[Signal] = ... # columnCategoriesChanged()
    columnRoleChanged        : typing.ClassVar[Signal] = ... # columnRoleChanged(QString)
    columnRolePatternChanged : typing.ClassVar[Signal] = ... # columnRolePatternChanged(QRegularExpression)
    columnRoleReplaceChanged : typing.ClassVar[Signal] = ... # columnRoleReplaceChanged(QString)
    itemModelChanged         : typing.ClassVar[Signal] = ... # itemModelChanged(const QAbstractItemModel*)
    multiMatchBehaviorChanged: typing.ClassVar[Signal] = ... # multiMatchBehaviorChanged(MultiMatchBehavior)
    rotationRoleChanged      : typing.ClassVar[Signal] = ... # rotationRoleChanged(QString)
    rotationRolePatternChanged: typing.ClassVar[Signal] = ... # rotationRolePatternChanged(QRegularExpression)
    rotationRoleReplaceChanged: typing.ClassVar[Signal] = ... # rotationRoleReplaceChanged(QString)
    rowCategoriesChanged     : typing.ClassVar[Signal] = ... # rowCategoriesChanged()
    rowRoleChanged           : typing.ClassVar[Signal] = ... # rowRoleChanged(QString)
    rowRolePatternChanged    : typing.ClassVar[Signal] = ... # rowRolePatternChanged(QRegularExpression)
    rowRoleReplaceChanged    : typing.ClassVar[Signal] = ... # rowRoleReplaceChanged(QString)
    useModelCategoriesChanged: typing.ClassVar[Signal] = ... # useModelCategoriesChanged(bool)
    valueRoleChanged         : typing.ClassVar[Signal] = ... # valueRoleChanged(QString)
    valueRolePatternChanged  : typing.ClassVar[Signal] = ... # valueRolePatternChanged(QRegularExpression)
    valueRoleReplaceChanged  : typing.ClassVar[Signal] = ... # valueRoleReplaceChanged(QString)

    class MultiMatchBehavior(enum.Enum):

        MMBFirst                  = ...  # 0x0
        MMBLast                   = ...  # 0x1
        MMBAverage                = ...  # 0x2
        MMBCumulative             = ...  # 0x3


    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, valueRole: str, rotationRole: str, rowCategories: collections.abc.Sequence[str], columnCategories: collections.abc.Sequence[str], /, parent: PySide6.QtCore.QObject | None = ..., *, useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., valueRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., valueRoleReplace: str | None = ..., rotationRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, valueRole: str, rotationRole: str, /, parent: PySide6.QtCore.QObject | None = ..., *, rowCategories: collections.abc.Sequence[str] | None = ..., columnCategories: collections.abc.Sequence[str] | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., valueRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., valueRoleReplace: str | None = ..., rotationRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, valueRole: str, rowCategories: collections.abc.Sequence[str], columnCategories: collections.abc.Sequence[str], /, parent: PySide6.QtCore.QObject | None = ..., *, rotationRole: str | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., valueRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., valueRoleReplace: str | None = ..., rotationRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, valueRole: str, /, parent: PySide6.QtCore.QObject | None = ..., *, rotationRole: str | None = ..., rowCategories: collections.abc.Sequence[str] | None = ..., columnCategories: collections.abc.Sequence[str] | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., valueRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., valueRoleReplace: str | None = ..., rotationRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, valueRole: str, /, parent: PySide6.QtCore.QObject | None = ..., *, rowRole: str | None = ..., columnRole: str | None = ..., rotationRole: str | None = ..., rowCategories: collections.abc.Sequence[str] | None = ..., columnCategories: collections.abc.Sequence[str] | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., valueRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., valueRoleReplace: str | None = ..., rotationRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, /, parent: PySide6.QtCore.QObject | None = ..., *, rowRole: str | None = ..., columnRole: str | None = ..., valueRole: str | None = ..., rotationRole: str | None = ..., rowCategories: collections.abc.Sequence[str] | None = ..., columnCategories: collections.abc.Sequence[str] | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., valueRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., valueRoleReplace: str | None = ..., rotationRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, itemModel: PySide6.QtCore.QAbstractItemModel | None = ..., rowRole: str | None = ..., columnRole: str | None = ..., valueRole: str | None = ..., rotationRole: str | None = ..., rowCategories: collections.abc.Sequence[str] | None = ..., columnCategories: collections.abc.Sequence[str] | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., valueRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., valueRoleReplace: str | None = ..., rotationRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior | None = ...) -> None: ...

    def autoColumnCategories(self, /) -> bool: ...
    def autoRowCategories(self, /) -> bool: ...
    def columnCategories(self, /) -> typing.List[str]: ...
    def columnCategoryIndex(self, category: str, /) -> int: ...
    def columnRole(self, /) -> str: ...
    def columnRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def columnRoleReplace(self, /) -> str: ...
    def itemModel(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def multiMatchBehavior(self, /) -> PySide6.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior: ...
    def remap(self, rowRole: str, columnRole: str, valueRole: str, rotationRole: str, rowCategories: collections.abc.Sequence[str], columnCategories: collections.abc.Sequence[str], /) -> None: ...
    def rotationRole(self, /) -> str: ...
    def rotationRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def rotationRoleReplace(self, /) -> str: ...
    def rowCategories(self, /) -> typing.List[str]: ...
    def rowCategoryIndex(self, category: str, /) -> int: ...
    def rowRole(self, /) -> str: ...
    def rowRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def rowRoleReplace(self, /) -> str: ...
    def setAutoColumnCategories(self, enable: bool, /) -> None: ...
    def setAutoRowCategories(self, enable: bool, /) -> None: ...
    def setColumnCategories(self, categories: collections.abc.Sequence[str], /) -> None: ...
    def setColumnRole(self, role: str, /) -> None: ...
    def setColumnRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setColumnRoleReplace(self, replace: str, /) -> None: ...
    def setItemModel(self, itemModel: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setMultiMatchBehavior(self, behavior: PySide6.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior, /) -> None: ...
    def setRotationRole(self, role: str, /) -> None: ...
    def setRotationRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setRotationRoleReplace(self, replace: str, /) -> None: ...
    def setRowCategories(self, categories: collections.abc.Sequence[str], /) -> None: ...
    def setRowRole(self, role: str, /) -> None: ...
    def setRowRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setRowRoleReplace(self, replace: str, /) -> None: ...
    def setUseModelCategories(self, enable: bool, /) -> None: ...
    def setValueRole(self, role: str, /) -> None: ...
    def setValueRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setValueRoleReplace(self, replace: str, /) -> None: ...
    def useModelCategories(self, /) -> bool: ...
    def valueRole(self, /) -> str: ...
    def valueRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def valueRoleReplace(self, /) -> str: ...


class QItemModelScatterDataProxy(PySide6.QtDataVisualization.QScatterDataProxy):

    itemModelChanged         : typing.ClassVar[Signal] = ... # itemModelChanged(const QAbstractItemModel*)
    rotationRoleChanged      : typing.ClassVar[Signal] = ... # rotationRoleChanged(QString)
    rotationRolePatternChanged: typing.ClassVar[Signal] = ... # rotationRolePatternChanged(QRegularExpression)
    rotationRoleReplaceChanged: typing.ClassVar[Signal] = ... # rotationRoleReplaceChanged(QString)
    xPosRoleChanged          : typing.ClassVar[Signal] = ... # xPosRoleChanged(QString)
    xPosRolePatternChanged   : typing.ClassVar[Signal] = ... # xPosRolePatternChanged(QRegularExpression)
    xPosRoleReplaceChanged   : typing.ClassVar[Signal] = ... # xPosRoleReplaceChanged(QString)
    yPosRoleChanged          : typing.ClassVar[Signal] = ... # yPosRoleChanged(QString)
    yPosRolePatternChanged   : typing.ClassVar[Signal] = ... # yPosRolePatternChanged(QRegularExpression)
    yPosRoleReplaceChanged   : typing.ClassVar[Signal] = ... # yPosRoleReplaceChanged(QString)
    zPosRoleChanged          : typing.ClassVar[Signal] = ... # zPosRoleChanged(QString)
    zPosRolePatternChanged   : typing.ClassVar[Signal] = ... # zPosRolePatternChanged(QRegularExpression)
    zPosRoleReplaceChanged   : typing.ClassVar[Signal] = ... # zPosRoleReplaceChanged(QString)

    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, xPosRole: str, yPosRole: str, zPosRole: str, rotationRole: str, /, parent: PySide6.QtCore.QObject | None = ..., *, xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., rotationRoleReplace: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, xPosRole: str, yPosRole: str, zPosRole: str, /, parent: PySide6.QtCore.QObject | None = ..., *, rotationRole: str | None = ..., xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., rotationRoleReplace: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, /, parent: PySide6.QtCore.QObject | None = ..., *, xPosRole: str | None = ..., yPosRole: str | None = ..., zPosRole: str | None = ..., rotationRole: str | None = ..., xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., rotationRoleReplace: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, itemModel: PySide6.QtCore.QAbstractItemModel | None = ..., xPosRole: str | None = ..., yPosRole: str | None = ..., zPosRole: str | None = ..., rotationRole: str | None = ..., xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rotationRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., rotationRoleReplace: str | None = ...) -> None: ...

    def itemModel(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def remap(self, xPosRole: str, yPosRole: str, zPosRole: str, rotationRole: str, /) -> None: ...
    def rotationRole(self, /) -> str: ...
    def rotationRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def rotationRoleReplace(self, /) -> str: ...
    def setItemModel(self, itemModel: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setRotationRole(self, role: str, /) -> None: ...
    def setRotationRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setRotationRoleReplace(self, replace: str, /) -> None: ...
    def setXPosRole(self, role: str, /) -> None: ...
    def setXPosRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setXPosRoleReplace(self, replace: str, /) -> None: ...
    def setYPosRole(self, role: str, /) -> None: ...
    def setYPosRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setYPosRoleReplace(self, replace: str, /) -> None: ...
    def setZPosRole(self, role: str, /) -> None: ...
    def setZPosRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setZPosRoleReplace(self, replace: str, /) -> None: ...
    def xPosRole(self, /) -> str: ...
    def xPosRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def xPosRoleReplace(self, /) -> str: ...
    def yPosRole(self, /) -> str: ...
    def yPosRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def yPosRoleReplace(self, /) -> str: ...
    def zPosRole(self, /) -> str: ...
    def zPosRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def zPosRoleReplace(self, /) -> str: ...


class QItemModelSurfaceDataProxy(PySide6.QtDataVisualization.QSurfaceDataProxy):

    autoColumnCategoriesChanged: typing.ClassVar[Signal] = ... # autoColumnCategoriesChanged(bool)
    autoRowCategoriesChanged : typing.ClassVar[Signal] = ... # autoRowCategoriesChanged(bool)
    columnCategoriesChanged  : typing.ClassVar[Signal] = ... # columnCategoriesChanged()
    columnRoleChanged        : typing.ClassVar[Signal] = ... # columnRoleChanged(QString)
    columnRolePatternChanged : typing.ClassVar[Signal] = ... # columnRolePatternChanged(QRegularExpression)
    columnRoleReplaceChanged : typing.ClassVar[Signal] = ... # columnRoleReplaceChanged(QString)
    itemModelChanged         : typing.ClassVar[Signal] = ... # itemModelChanged(const QAbstractItemModel*)
    multiMatchBehaviorChanged: typing.ClassVar[Signal] = ... # multiMatchBehaviorChanged(MultiMatchBehavior)
    rowCategoriesChanged     : typing.ClassVar[Signal] = ... # rowCategoriesChanged()
    rowRoleChanged           : typing.ClassVar[Signal] = ... # rowRoleChanged(QString)
    rowRolePatternChanged    : typing.ClassVar[Signal] = ... # rowRolePatternChanged(QRegularExpression)
    rowRoleReplaceChanged    : typing.ClassVar[Signal] = ... # rowRoleReplaceChanged(QString)
    useModelCategoriesChanged: typing.ClassVar[Signal] = ... # useModelCategoriesChanged(bool)
    xPosRoleChanged          : typing.ClassVar[Signal] = ... # xPosRoleChanged(QString)
    xPosRolePatternChanged   : typing.ClassVar[Signal] = ... # xPosRolePatternChanged(QRegularExpression)
    xPosRoleReplaceChanged   : typing.ClassVar[Signal] = ... # xPosRoleReplaceChanged(QString)
    yPosRoleChanged          : typing.ClassVar[Signal] = ... # yPosRoleChanged(QString)
    yPosRolePatternChanged   : typing.ClassVar[Signal] = ... # yPosRolePatternChanged(QRegularExpression)
    yPosRoleReplaceChanged   : typing.ClassVar[Signal] = ... # yPosRoleReplaceChanged(QString)
    zPosRoleChanged          : typing.ClassVar[Signal] = ... # zPosRoleChanged(QString)
    zPosRolePatternChanged   : typing.ClassVar[Signal] = ... # zPosRolePatternChanged(QRegularExpression)
    zPosRoleReplaceChanged   : typing.ClassVar[Signal] = ... # zPosRoleReplaceChanged(QString)

    class MultiMatchBehavior(enum.Enum):

        MMBFirst                  = ...  # 0x0
        MMBLast                   = ...  # 0x1
        MMBAverage                = ...  # 0x2
        MMBCumulativeY            = ...  # 0x3


    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, xPosRole: str, yPosRole: str, zPosRole: str, rowCategories: collections.abc.Sequence[str], columnCategories: collections.abc.Sequence[str], /, parent: PySide6.QtCore.QObject | None = ..., *, useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, xPosRole: str, yPosRole: str, zPosRole: str, /, parent: PySide6.QtCore.QObject | None = ..., *, rowCategories: collections.abc.Sequence[str] | None = ..., columnCategories: collections.abc.Sequence[str] | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, yPosRole: str, rowCategories: collections.abc.Sequence[str], columnCategories: collections.abc.Sequence[str], /, parent: PySide6.QtCore.QObject | None = ..., *, xPosRole: str | None = ..., zPosRole: str | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, rowRole: str, columnRole: str, yPosRole: str, /, parent: PySide6.QtCore.QObject | None = ..., *, xPosRole: str | None = ..., zPosRole: str | None = ..., rowCategories: collections.abc.Sequence[str] | None = ..., columnCategories: collections.abc.Sequence[str] | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, yPosRole: str, /, parent: PySide6.QtCore.QObject | None = ..., *, rowRole: str | None = ..., columnRole: str | None = ..., xPosRole: str | None = ..., zPosRole: str | None = ..., rowCategories: collections.abc.Sequence[str] | None = ..., columnCategories: collections.abc.Sequence[str] | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, itemModel: PySide6.QtCore.QAbstractItemModel, /, parent: PySide6.QtCore.QObject | None = ..., *, rowRole: str | None = ..., columnRole: str | None = ..., xPosRole: str | None = ..., yPosRole: str | None = ..., zPosRole: str | None = ..., rowCategories: collections.abc.Sequence[str] | None = ..., columnCategories: collections.abc.Sequence[str] | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, itemModel: PySide6.QtCore.QAbstractItemModel | None = ..., rowRole: str | None = ..., columnRole: str | None = ..., xPosRole: str | None = ..., yPosRole: str | None = ..., zPosRole: str | None = ..., rowCategories: collections.abc.Sequence[str] | None = ..., columnCategories: collections.abc.Sequence[str] | None = ..., useModelCategories: bool | None = ..., autoRowCategories: bool | None = ..., autoColumnCategories: bool | None = ..., rowRolePattern: PySide6.QtCore.QRegularExpression | None = ..., columnRolePattern: PySide6.QtCore.QRegularExpression | None = ..., xPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., yPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., zPosRolePattern: PySide6.QtCore.QRegularExpression | None = ..., rowRoleReplace: str | None = ..., columnRoleReplace: str | None = ..., xPosRoleReplace: str | None = ..., yPosRoleReplace: str | None = ..., zPosRoleReplace: str | None = ..., multiMatchBehavior: PySide6.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior | None = ...) -> None: ...

    def autoColumnCategories(self, /) -> bool: ...
    def autoRowCategories(self, /) -> bool: ...
    def columnCategories(self, /) -> typing.List[str]: ...
    def columnCategoryIndex(self, category: str, /) -> int: ...
    def columnRole(self, /) -> str: ...
    def columnRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def columnRoleReplace(self, /) -> str: ...
    def itemModel(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def multiMatchBehavior(self, /) -> PySide6.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior: ...
    def remap(self, rowRole: str, columnRole: str, xPosRole: str, yPosRole: str, zPosRole: str, rowCategories: collections.abc.Sequence[str], columnCategories: collections.abc.Sequence[str], /) -> None: ...
    def rowCategories(self, /) -> typing.List[str]: ...
    def rowCategoryIndex(self, category: str, /) -> int: ...
    def rowRole(self, /) -> str: ...
    def rowRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def rowRoleReplace(self, /) -> str: ...
    def setAutoColumnCategories(self, enable: bool, /) -> None: ...
    def setAutoRowCategories(self, enable: bool, /) -> None: ...
    def setColumnCategories(self, categories: collections.abc.Sequence[str], /) -> None: ...
    def setColumnRole(self, role: str, /) -> None: ...
    def setColumnRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setColumnRoleReplace(self, replace: str, /) -> None: ...
    def setItemModel(self, itemModel: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setMultiMatchBehavior(self, behavior: PySide6.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior, /) -> None: ...
    def setRowCategories(self, categories: collections.abc.Sequence[str], /) -> None: ...
    def setRowRole(self, role: str, /) -> None: ...
    def setRowRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setRowRoleReplace(self, replace: str, /) -> None: ...
    def setUseModelCategories(self, enable: bool, /) -> None: ...
    def setXPosRole(self, role: str, /) -> None: ...
    def setXPosRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setXPosRoleReplace(self, replace: str, /) -> None: ...
    def setYPosRole(self, role: str, /) -> None: ...
    def setYPosRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setYPosRoleReplace(self, replace: str, /) -> None: ...
    def setZPosRole(self, role: str, /) -> None: ...
    def setZPosRolePattern(self, pattern: PySide6.QtCore.QRegularExpression | str, /) -> None: ...
    def setZPosRoleReplace(self, replace: str, /) -> None: ...
    def useModelCategories(self, /) -> bool: ...
    def xPosRole(self, /) -> str: ...
    def xPosRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def xPosRoleReplace(self, /) -> str: ...
    def yPosRole(self, /) -> str: ...
    def yPosRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def yPosRoleReplace(self, /) -> str: ...
    def zPosRole(self, /) -> str: ...
    def zPosRolePattern(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def zPosRoleReplace(self, /) -> str: ...


class QLogValue3DAxisFormatter(PySide6.QtDataVisualization.QValue3DAxisFormatter):

    autoSubGridChanged       : typing.ClassVar[Signal] = ... # autoSubGridChanged(bool)
    baseChanged              : typing.ClassVar[Signal] = ... # baseChanged(double)
    showEdgeLabelsChanged    : typing.ClassVar[Signal] = ... # showEdgeLabelsChanged(bool)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, base: float | None = ..., autoSubGrid: bool | None = ..., showEdgeLabels: bool | None = ...) -> None: ...

    def autoSubGrid(self, /) -> bool: ...
    def base(self, /) -> float: ...
    def createNewInstance(self, /) -> PySide6.QtDataVisualization.QValue3DAxisFormatter: ...
    def populateCopy(self, copy: PySide6.QtDataVisualization.QValue3DAxisFormatter, /) -> None: ...
    def positionAt(self, value: float, /) -> float: ...
    def recalculate(self, /) -> None: ...
    def setAutoSubGrid(self, enabled: bool, /) -> None: ...
    def setBase(self, base: float, /) -> None: ...
    def setShowEdgeLabels(self, enabled: bool, /) -> None: ...
    def showEdgeLabels(self, /) -> bool: ...
    def valueAt(self, position: float, /) -> float: ...


class QScatter3DSeries(PySide6.QtDataVisualization.QAbstract3DSeries):

    dataProxyChanged         : typing.ClassVar[Signal] = ... # dataProxyChanged(QScatterDataProxy*)
    itemSizeChanged          : typing.ClassVar[Signal] = ... # itemSizeChanged(float)
    selectedItemChanged      : typing.ClassVar[Signal] = ... # selectedItemChanged(int)

    @typing.overload
    def __init__(self, dataProxy: PySide6.QtDataVisualization.QScatterDataProxy, /, parent: PySide6.QtCore.QObject | None = ..., *, selectedItem: int | None = ..., itemSize: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, dataProxy: PySide6.QtDataVisualization.QScatterDataProxy | None = ..., selectedItem: int | None = ..., itemSize: float | None = ...) -> None: ...

    def dataProxy(self, /) -> PySide6.QtDataVisualization.QScatterDataProxy: ...
    @staticmethod
    def invalidSelectionIndex() -> int: ...
    def itemSize(self, /) -> float: ...
    def selectedItem(self, /) -> int: ...
    def setDataProxy(self, proxy: PySide6.QtDataVisualization.QScatterDataProxy, /) -> None: ...
    def setItemSize(self, size: float, /) -> None: ...
    def setSelectedItem(self, index: int, /) -> None: ...


class QScatterDataItem(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtDataVisualization.QScatterDataItem, /) -> None: ...
    @typing.overload
    def __init__(self, position: PySide6.QtGui.QVector3D, /) -> None: ...
    @typing.overload
    def __init__(self, position: PySide6.QtGui.QVector3D, rotation: PySide6.QtGui.QQuaternion, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def createExtraData(self, /) -> None: ...
    def position(self, /) -> PySide6.QtGui.QVector3D: ...
    def rotation(self, /) -> PySide6.QtGui.QQuaternion: ...
    def setPosition(self, pos: PySide6.QtGui.QVector3D, /) -> None: ...
    def setRotation(self, rot: PySide6.QtGui.QQuaternion, /) -> None: ...
    def setX(self, value: float, /) -> None: ...
    def setY(self, value: float, /) -> None: ...
    def setZ(self, value: float, /) -> None: ...
    def x(self, /) -> float: ...
    def y(self, /) -> float: ...
    def z(self, /) -> float: ...


class QScatterDataProxy(PySide6.QtDataVisualization.QAbstractDataProxy):

    arrayReset               : typing.ClassVar[Signal] = ... # arrayReset()
    itemCountChanged         : typing.ClassVar[Signal] = ... # itemCountChanged(int)
    itemsAdded               : typing.ClassVar[Signal] = ... # itemsAdded(int,int)
    itemsChanged             : typing.ClassVar[Signal] = ... # itemsChanged(int,int)
    itemsInserted            : typing.ClassVar[Signal] = ... # itemsInserted(int,int)
    itemsRemoved             : typing.ClassVar[Signal] = ... # itemsRemoved(int,int)
    seriesChanged            : typing.ClassVar[Signal] = ... # seriesChanged(QScatter3DSeries*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, itemCount: int | None = ..., series: PySide6.QtDataVisualization.QScatter3DSeries | None = ...) -> None: ...

    def addItem(self, item: PySide6.QtDataVisualization.QScatterDataItem | PySide6.QtGui.QVector3D, /) -> int: ...
    def addItems(self, items: collections.abc.Sequence[PySide6.QtDataVisualization.QScatterDataItem], /) -> int: ...
    def array(self, /) -> typing.List[PySide6.QtDataVisualization.QScatterDataItem]: ...
    def insertItem(self, index: int, item: PySide6.QtDataVisualization.QScatterDataItem | PySide6.QtGui.QVector3D, /) -> None: ...
    def insertItems(self, index: int, items: collections.abc.Sequence[PySide6.QtDataVisualization.QScatterDataItem], /) -> None: ...
    def itemAt(self, index: int, /) -> PySide6.QtDataVisualization.QScatterDataItem: ...
    def itemCount(self, /) -> int: ...
    def removeItems(self, index: int, removeCount: int, /) -> None: ...
    def resetArray(self, arg__1: collections.abc.Sequence[PySide6.QtDataVisualization.QScatterDataItem], /) -> None: ...
    def series(self, /) -> PySide6.QtDataVisualization.QScatter3DSeries: ...
    def setItem(self, index: int, item: PySide6.QtDataVisualization.QScatterDataItem | PySide6.QtGui.QVector3D, /) -> None: ...
    def setItems(self, index: int, items: collections.abc.Sequence[PySide6.QtDataVisualization.QScatterDataItem], /) -> None: ...


class QSurface3DSeries(PySide6.QtDataVisualization.QAbstract3DSeries):

    dataProxyChanged         : typing.ClassVar[Signal] = ... # dataProxyChanged(QSurfaceDataProxy*)
    drawModeChanged          : typing.ClassVar[Signal] = ... # drawModeChanged(QSurface3DSeries::DrawFlags)
    flatShadingEnabledChanged: typing.ClassVar[Signal] = ... # flatShadingEnabledChanged(bool)
    flatShadingSupportedChanged: typing.ClassVar[Signal] = ... # flatShadingSupportedChanged(bool)
    selectedPointChanged     : typing.ClassVar[Signal] = ... # selectedPointChanged(QPoint)
    textureChanged           : typing.ClassVar[Signal] = ... # textureChanged(QImage)
    textureFileChanged       : typing.ClassVar[Signal] = ... # textureFileChanged(QString)
    wireframeColorChanged    : typing.ClassVar[Signal] = ... # wireframeColorChanged(QColor)

    class DrawFlag(enum.Flag):

        DrawWireframe             = ...  # 0x1
        DrawSurface               = ...  # 0x2
        DrawSurfaceAndWireframe   = ...  # 0x3


    @typing.overload
    def __init__(self, dataProxy: PySide6.QtDataVisualization.QSurfaceDataProxy, /, parent: PySide6.QtCore.QObject | None = ..., *, selectedPoint: PySide6.QtCore.QPoint | None = ..., flatShadingEnabled: bool | None = ..., flatShadingSupported: bool | None = ..., drawMode: PySide6.QtDataVisualization.QSurface3DSeries.DrawFlag | None = ..., texture: PySide6.QtGui.QImage | None = ..., textureFile: str | None = ..., wireframeColor: PySide6.QtGui.QColor | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, dataProxy: PySide6.QtDataVisualization.QSurfaceDataProxy | None = ..., selectedPoint: PySide6.QtCore.QPoint | None = ..., flatShadingEnabled: bool | None = ..., flatShadingSupported: bool | None = ..., drawMode: PySide6.QtDataVisualization.QSurface3DSeries.DrawFlag | None = ..., texture: PySide6.QtGui.QImage | None = ..., textureFile: str | None = ..., wireframeColor: PySide6.QtGui.QColor | None = ...) -> None: ...

    def dataProxy(self, /) -> PySide6.QtDataVisualization.QSurfaceDataProxy: ...
    def drawMode(self, /) -> PySide6.QtDataVisualization.QSurface3DSeries.DrawFlag: ...
    @staticmethod
    def invalidSelectionPosition() -> PySide6.QtCore.QPoint: ...
    def isFlatShadingEnabled(self, /) -> bool: ...
    def isFlatShadingSupported(self, /) -> bool: ...
    def selectedPoint(self, /) -> PySide6.QtCore.QPoint: ...
    def setDataProxy(self, proxy: PySide6.QtDataVisualization.QSurfaceDataProxy, /) -> None: ...
    def setDrawMode(self, mode: PySide6.QtDataVisualization.QSurface3DSeries.DrawFlag, /) -> None: ...
    def setFlatShadingEnabled(self, enabled: bool, /) -> None: ...
    def setSelectedPoint(self, position: PySide6.QtCore.QPoint, /) -> None: ...
    def setTexture(self, texture: PySide6.QtGui.QImage, /) -> None: ...
    def setTextureFile(self, filename: str, /) -> None: ...
    def setWireframeColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def texture(self, /) -> PySide6.QtGui.QImage: ...
    def textureFile(self, /) -> str: ...
    def wireframeColor(self, /) -> PySide6.QtGui.QColor: ...


class QSurfaceDataItem(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtDataVisualization.QSurfaceDataItem, /) -> None: ...
    @typing.overload
    def __init__(self, position: PySide6.QtGui.QVector3D, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def createExtraData(self, /) -> None: ...
    def position(self, /) -> PySide6.QtGui.QVector3D: ...
    def setPosition(self, pos: PySide6.QtGui.QVector3D, /) -> None: ...
    def setX(self, value: float, /) -> None: ...
    def setY(self, value: float, /) -> None: ...
    def setZ(self, value: float, /) -> None: ...
    def x(self, /) -> float: ...
    def y(self, /) -> float: ...
    def z(self, /) -> float: ...


class QSurfaceDataProxy(PySide6.QtDataVisualization.QAbstractDataProxy):

    arrayReset               : typing.ClassVar[Signal] = ... # arrayReset()
    columnCountChanged       : typing.ClassVar[Signal] = ... # columnCountChanged(int)
    itemChanged              : typing.ClassVar[Signal] = ... # itemChanged(int,int)
    rowCountChanged          : typing.ClassVar[Signal] = ... # rowCountChanged(int)
    rowsAdded                : typing.ClassVar[Signal] = ... # rowsAdded(int,int)
    rowsChanged              : typing.ClassVar[Signal] = ... # rowsChanged(int,int)
    rowsInserted             : typing.ClassVar[Signal] = ... # rowsInserted(int,int)
    rowsRemoved              : typing.ClassVar[Signal] = ... # rowsRemoved(int,int)
    seriesChanged            : typing.ClassVar[Signal] = ... # seriesChanged(QSurface3DSeries*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, rowCount: int | None = ..., columnCount: int | None = ..., series: PySide6.QtDataVisualization.QSurface3DSeries | None = ...) -> None: ...

    def addRow(self, arg__1: collections.abc.Sequence[PySide6.QtDataVisualization.QSurfaceDataItem], /) -> int: ...
    def addRows(self, rows: typing.List[typing.List[PySide6.QtDataVisualization.QSurfaceDataItem]], /) -> int: ...
    def array(self, /) -> typing.List[typing.List[PySide6.QtDataVisualization.QSurfaceDataItem]]: ...
    def columnCount(self, /) -> int: ...
    def insertRow(self, arg__1: int, arg__2: collections.abc.Sequence[PySide6.QtDataVisualization.QSurfaceDataItem], /) -> None: ...
    def insertRows(self, rowIndex: int, rows: typing.List[typing.List[PySide6.QtDataVisualization.QSurfaceDataItem]], /) -> None: ...
    @typing.overload
    def itemAt(self, position: PySide6.QtCore.QPoint, /) -> PySide6.QtDataVisualization.QSurfaceDataItem: ...
    @typing.overload
    def itemAt(self, rowIndex: int, columnIndex: int, /) -> PySide6.QtDataVisualization.QSurfaceDataItem: ...
    def removeRows(self, rowIndex: int, removeCount: int, /) -> None: ...
    def resetArray(self, arg__1: typing.List[typing.List[PySide6.QtDataVisualization.QSurfaceDataItem]], /) -> None: ...
    def resetArrayNp(self, x: float, deltaX: float, z: float, deltaZ: float, data: collections.abc.Sequence[typing.Any], /) -> None: ...
    def rowCount(self, /) -> int: ...
    def series(self, /) -> PySide6.QtDataVisualization.QSurface3DSeries: ...
    @typing.overload
    def setItem(self, position: PySide6.QtCore.QPoint, item: PySide6.QtDataVisualization.QSurfaceDataItem | PySide6.QtGui.QVector3D, /) -> None: ...
    @typing.overload
    def setItem(self, rowIndex: int, columnIndex: int, item: PySide6.QtDataVisualization.QSurfaceDataItem | PySide6.QtGui.QVector3D, /) -> None: ...
    def setRow(self, arg__1: int, arg__2: collections.abc.Sequence[PySide6.QtDataVisualization.QSurfaceDataItem], /) -> None: ...
    def setRows(self, rowIndex: int, rows: typing.List[typing.List[PySide6.QtDataVisualization.QSurfaceDataItem]], /) -> None: ...


class QTouch3DInputHandler(PySide6.QtDataVisualization.Q3DInputHandler):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def touchEvent(self, event: PySide6.QtGui.QTouchEvent, /) -> None: ...


class QValue3DAxis(PySide6.QtDataVisualization.QAbstract3DAxis):

    formatterChanged         : typing.ClassVar[Signal] = ... # formatterChanged(QValue3DAxisFormatter*)
    labelFormatChanged       : typing.ClassVar[Signal] = ... # labelFormatChanged(QString)
    reversedChanged          : typing.ClassVar[Signal] = ... # reversedChanged(bool)
    segmentCountChanged      : typing.ClassVar[Signal] = ... # segmentCountChanged(int)
    subSegmentCountChanged   : typing.ClassVar[Signal] = ... # subSegmentCountChanged(int)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, segmentCount: int | None = ..., subSegmentCount: int | None = ..., labelFormat: str | None = ..., formatter: PySide6.QtDataVisualization.QValue3DAxisFormatter | None = ..., reversed: bool | None = ...) -> None: ...

    def formatter(self, /) -> PySide6.QtDataVisualization.QValue3DAxisFormatter: ...
    def labelFormat(self, /) -> str: ...
    def reversed(self, /) -> bool: ...
    def segmentCount(self, /) -> int: ...
    def setFormatter(self, formatter: PySide6.QtDataVisualization.QValue3DAxisFormatter, /) -> None: ...
    def setLabelFormat(self, format: str, /) -> None: ...
    def setReversed(self, enable: bool, /) -> None: ...
    def setSegmentCount(self, count: int, /) -> None: ...
    def setSubSegmentCount(self, count: int, /) -> None: ...
    def subSegmentCount(self, /) -> int: ...


class QValue3DAxisFormatter(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def allowNegatives(self, /) -> bool: ...
    def allowZero(self, /) -> bool: ...
    def axis(self, /) -> PySide6.QtDataVisualization.QValue3DAxis: ...
    def createNewInstance(self, /) -> PySide6.QtDataVisualization.QValue3DAxisFormatter: ...
    def gridPositions(self, /) -> typing.List[float]: ...
    def labelPositions(self, /) -> typing.List[float]: ...
    def labelStrings(self, /) -> typing.List[str]: ...
    def locale(self, /) -> PySide6.QtCore.QLocale: ...
    def markDirty(self, /, labelsChange: bool = ...) -> None: ...
    def populateCopy(self, copy: PySide6.QtDataVisualization.QValue3DAxisFormatter, /) -> None: ...
    def positionAt(self, value: float, /) -> float: ...
    def recalculate(self, /) -> None: ...
    def setAllowNegatives(self, allow: bool, /) -> None: ...
    def setAllowZero(self, allow: bool, /) -> None: ...
    def setGridPositions(self, grid_positions: collections.abc.Sequence[float], /) -> None: ...
    def setLabelPositions(self, label_positions: collections.abc.Sequence[float], /) -> None: ...
    def setLabelStrings(self, label_strings: collections.abc.Sequence[str], /) -> None: ...
    def setLocale(self, locale: PySide6.QtCore.QLocale | PySide6.QtCore.QLocale.Language, /) -> None: ...
    def stringForValue(self, value: float, format: str, /) -> str: ...
    def subGridPositions(self, /) -> typing.List[float]: ...
    def valueAt(self, position: float, /) -> float: ...


def qDefaultSurfaceFormat(antialias: bool, /) -> PySide6.QtGui.QSurfaceFormat: ...


# eof
