# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtGraphsWidgets, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtGraphsWidgets`

import PySide6.QtGraphsWidgets
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtQuick
import PySide6.QtQuickWidgets
import PySide6.QtGraphs

import typing
from PySide6.QtCore import Signal


class Q3DBarsWidgetItem(PySide6.QtGraphsWidgets.Q3DGraphsWidgetItem):

    barSeriesMarginChanged   : typing.ClassVar[Signal] = ... # barSeriesMarginChanged(QSizeF)
    barSpacingChanged        : typing.ClassVar[Signal] = ... # barSpacingChanged(QSizeF)
    barSpacingRelativeChanged: typing.ClassVar[Signal] = ... # barSpacingRelativeChanged(bool)
    barThicknessChanged      : typing.ClassVar[Signal] = ... # barThicknessChanged(float)
    columnAxisChanged        : typing.ClassVar[Signal] = ... # columnAxisChanged(QCategory3DAxis*)
    floorLevelChanged        : typing.ClassVar[Signal] = ... # floorLevelChanged(float)
    multiSeriesUniformChanged: typing.ClassVar[Signal] = ... # multiSeriesUniformChanged(bool)
    primarySeriesChanged     : typing.ClassVar[Signal] = ... # primarySeriesChanged(QBar3DSeries*)
    rowAxisChanged           : typing.ClassVar[Signal] = ... # rowAxisChanged(QCategory3DAxis*)
    selectedSeriesChanged    : typing.ClassVar[Signal] = ... # selectedSeriesChanged(QBar3DSeries*)
    valueAxisChanged         : typing.ClassVar[Signal] = ... # valueAxisChanged(QValue3DAxis*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, multiSeriesUniform: bool | None = ..., barThickness: float | None = ..., barSpacing: PySide6.QtCore.QSizeF | None = ..., barSpacingRelative: bool | None = ..., barSeriesMargin: PySide6.QtCore.QSizeF | None = ..., rowAxis: PySide6.QtGraphs.QCategory3DAxis | None = ..., columnAxis: PySide6.QtGraphs.QCategory3DAxis | None = ..., valueAxis: PySide6.QtGraphs.QValue3DAxis | None = ..., primarySeries: PySide6.QtGraphs.QBar3DSeries | None = ..., selectedSeries: PySide6.QtGraphs.QBar3DSeries | None = ..., floorLevel: float | None = ...) -> None: ...

    def addAxis(self, axis: PySide6.QtGraphs.QAbstract3DAxis, /) -> None: ...
    def addSeries(self, series: PySide6.QtGraphs.QBar3DSeries, /) -> None: ...
    def axes(self, /) -> typing.List[PySide6.QtGraphs.QAbstract3DAxis]: ...
    def barSeriesMargin(self, /) -> PySide6.QtCore.QSizeF: ...
    def barSpacing(self, /) -> PySide6.QtCore.QSizeF: ...
    def barThickness(self, /) -> float: ...
    def columnAxis(self, /) -> PySide6.QtGraphs.QCategory3DAxis: ...
    def event(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def floorLevel(self, /) -> float: ...
    def insertSeries(self, index: int, series: PySide6.QtGraphs.QBar3DSeries, /) -> None: ...
    def isBarSpacingRelative(self, /) -> bool: ...
    def isMultiSeriesUniform(self, /) -> bool: ...
    def primarySeries(self, /) -> PySide6.QtGraphs.QBar3DSeries: ...
    def releaseAxis(self, axis: PySide6.QtGraphs.QAbstract3DAxis, /) -> None: ...
    def removeSeries(self, series: PySide6.QtGraphs.QBar3DSeries, /) -> None: ...
    def rowAxis(self, /) -> PySide6.QtGraphs.QCategory3DAxis: ...
    def selectedSeries(self, /) -> PySide6.QtGraphs.QBar3DSeries: ...
    def seriesList(self, /) -> typing.List[PySide6.QtGraphs.QBar3DSeries]: ...
    def setBarSeriesMargin(self, margin: PySide6.QtCore.QSizeF | PySide6.QtCore.QSize, /) -> None: ...
    def setBarSpacing(self, spacing: PySide6.QtCore.QSizeF | PySide6.QtCore.QSize, /) -> None: ...
    def setBarSpacingRelative(self, relative: bool, /) -> None: ...
    def setBarThickness(self, thicknessRatio: float, /) -> None: ...
    def setColumnAxis(self, axis: PySide6.QtGraphs.QCategory3DAxis, /) -> None: ...
    def setFloorLevel(self, level: float, /) -> None: ...
    def setMultiSeriesUniform(self, uniform: bool, /) -> None: ...
    def setPrimarySeries(self, series: PySide6.QtGraphs.QBar3DSeries, /) -> None: ...
    def setRowAxis(self, axis: PySide6.QtGraphs.QCategory3DAxis, /) -> None: ...
    def setValueAxis(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...
    def valueAxis(self, /) -> PySide6.QtGraphs.QValue3DAxis: ...


class Q3DGraphsWidgetItem(PySide6.QtCore.QObject):

    activeThemeChanged       : typing.ClassVar[Signal] = ... # activeThemeChanged(QGraphsTheme*)
    ambientLightStrengthChanged: typing.ClassVar[Signal] = ... # ambientLightStrengthChanged()
    aspectRatioChanged       : typing.ClassVar[Signal] = ... # aspectRatioChanged(double)
    cameraPresetChanged      : typing.ClassVar[Signal] = ... # cameraPresetChanged(QtGraphs3D::CameraPreset)
    cameraTargetPositionChanged: typing.ClassVar[Signal] = ... # cameraTargetPositionChanged(QVector3D)
    cameraXRotationChanged   : typing.ClassVar[Signal] = ... # cameraXRotationChanged(float)
    cameraYRotationChanged   : typing.ClassVar[Signal] = ... # cameraYRotationChanged(float)
    cameraZoomLevelChanged   : typing.ClassVar[Signal] = ... # cameraZoomLevelChanged(float)
    currentFpsChanged        : typing.ClassVar[Signal] = ... # currentFpsChanged(int)
    doubleTapped             : typing.ClassVar[Signal] = ... # doubleTapped(QEventPoint,Qt::MouseButton)
    dragged                  : typing.ClassVar[Signal] = ... # dragged(QVector2D)
    gridLineTypeChanged      : typing.ClassVar[Signal] = ... # gridLineTypeChanged()
    horizontalAspectRatioChanged: typing.ClassVar[Signal] = ... # horizontalAspectRatioChanged(double)
    labelMarginChanged       : typing.ClassVar[Signal] = ... # labelMarginChanged(float)
    lightColorChanged        : typing.ClassVar[Signal] = ... # lightColorChanged()
    lightStrengthChanged     : typing.ClassVar[Signal] = ... # lightStrengthChanged()
    localeChanged            : typing.ClassVar[Signal] = ... # localeChanged(QLocale)
    longPressed              : typing.ClassVar[Signal] = ... # longPressed()
    marginChanged            : typing.ClassVar[Signal] = ... # marginChanged(double)
    maxCameraXRotationChanged: typing.ClassVar[Signal] = ... # maxCameraXRotationChanged(float)
    maxCameraYRotationChanged: typing.ClassVar[Signal] = ... # maxCameraYRotationChanged(float)
    maxCameraZoomLevelChanged: typing.ClassVar[Signal] = ... # maxCameraZoomLevelChanged(float)
    measureFpsChanged        : typing.ClassVar[Signal] = ... # measureFpsChanged(bool)
    minCameraXRotationChanged: typing.ClassVar[Signal] = ... # minCameraXRotationChanged(float)
    minCameraYRotationChanged: typing.ClassVar[Signal] = ... # minCameraYRotationChanged(float)
    minCameraZoomLevelChanged: typing.ClassVar[Signal] = ... # minCameraZoomLevelChanged(float)
    mouseMove                : typing.ClassVar[Signal] = ... # mouseMove(QPoint)
    msaaSamplesChanged       : typing.ClassVar[Signal] = ... # msaaSamplesChanged(int)
    optimizationHintChanged  : typing.ClassVar[Signal] = ... # optimizationHintChanged(QtGraphs3D::OptimizationHint)
    orthoProjectionChanged   : typing.ClassVar[Signal] = ... # orthoProjectionChanged(bool)
    pinch                    : typing.ClassVar[Signal] = ... # pinch(double)
    polarChanged             : typing.ClassVar[Signal] = ... # polarChanged(bool)
    queriedGraphPositionChanged: typing.ClassVar[Signal] = ... # queriedGraphPositionChanged(QVector3D)
    radialLabelOffsetChanged : typing.ClassVar[Signal] = ... # radialLabelOffsetChanged(float)
    rotationEnabledChanged   : typing.ClassVar[Signal] = ... # rotationEnabledChanged(bool)
    selectedElementChanged   : typing.ClassVar[Signal] = ... # selectedElementChanged(QtGraphs3D::ElementType)
    selectionEnabledChanged  : typing.ClassVar[Signal] = ... # selectionEnabledChanged(bool)
    selectionModeChanged     : typing.ClassVar[Signal] = ... # selectionModeChanged(QtGraphs3D::SelectionFlags)
    shadowQualityChanged     : typing.ClassVar[Signal] = ... # shadowQualityChanged(QtGraphs3D::ShadowQuality)
    shadowStrengthChanged    : typing.ClassVar[Signal] = ... # shadowStrengthChanged()
    tapped                   : typing.ClassVar[Signal] = ... # tapped(QEventPoint,Qt::MouseButton)
    transparencyTechniqueChanged: typing.ClassVar[Signal] = ... # transparencyTechniqueChanged(QtGraphs3D::TransparencyTechnique)
    wheel                    : typing.ClassVar[Signal] = ... # wheel(QWheelEvent*)
    wrapCameraXRotationChanged: typing.ClassVar[Signal] = ... # wrapCameraXRotationChanged(bool)
    wrapCameraYRotationChanged: typing.ClassVar[Signal] = ... # wrapCameraYRotationChanged(bool)
    zoomAtTargetEnabledChanged: typing.ClassVar[Signal] = ... # zoomAtTargetEnabledChanged(bool)
    zoomEnabledChanged       : typing.ClassVar[Signal] = ... # zoomEnabledChanged(bool)
    def activeTheme(self, /) -> PySide6.QtGraphs.QGraphsTheme: ...
    def addCustomItem(self, item: PySide6.QtGraphs.QCustom3DItem, /) -> int: ...
    def addTheme(self, theme: PySide6.QtGraphs.QGraphsTheme, /) -> None: ...
    def ambientLightStrength(self, /) -> float: ...
    def aspectRatio(self, /) -> float: ...
    def cameraPreset(self, /) -> PySide6.QtGraphs.QtGraphs3D.CameraPreset: ...
    def cameraTargetPosition(self, /) -> PySide6.QtGui.QVector3D: ...
    def cameraXRotation(self, /) -> float: ...
    def cameraYRotation(self, /) -> float: ...
    def cameraZoomLevel(self, /) -> float: ...
    def clearSelection(self, /) -> None: ...
    def currentFps(self, /) -> int: ...
    def customItems(self, /) -> typing.List[PySide6.QtGraphs.QCustom3DItem]: ...
    def doPicking(self, point: PySide6.QtCore.QPoint, /) -> None: ...
    def doRayPicking(self, origin: PySide6.QtGui.QVector3D, direction: PySide6.QtGui.QVector3D, /) -> None: ...
    def event(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def eventFilter(self, obj: PySide6.QtCore.QObject, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def gridLineType(self, /) -> PySide6.QtGraphs.QtGraphs3D.GridLineType: ...
    def hasSeries(self, series: PySide6.QtGraphs.QAbstract3DSeries, /) -> bool: ...
    def horizontalAspectRatio(self, /) -> float: ...
    def isOrthoProjection(self, /) -> bool: ...
    def isPolar(self, /) -> bool: ...
    def isRotationEnabled(self, /) -> bool: ...
    def isSelectionEnabled(self, /) -> bool: ...
    def isZoomAtTargetEnabled(self, /) -> bool: ...
    def isZoomEnabled(self, /) -> bool: ...
    def labelMargin(self, /) -> float: ...
    def lightColor(self, /) -> PySide6.QtGui.QColor: ...
    def lightStrength(self, /) -> float: ...
    def locale(self, /) -> PySide6.QtCore.QLocale: ...
    def margin(self, /) -> float: ...
    def maxCameraXRotation(self, /) -> float: ...
    def maxCameraYRotation(self, /) -> float: ...
    def maxCameraZoomLevel(self, /) -> float: ...
    def measureFps(self, /) -> bool: ...
    def minCameraXRotation(self, /) -> float: ...
    def minCameraYRotation(self, /) -> float: ...
    def minCameraZoomLevel(self, /) -> float: ...
    def msaaSamples(self, /) -> int: ...
    def optimizationHint(self, /) -> PySide6.QtGraphs.QtGraphs3D.OptimizationHint: ...
    def queriedGraphPosition(self, /) -> PySide6.QtGui.QVector3D: ...
    def radialLabelOffset(self, /) -> float: ...
    def releaseCustomItem(self, item: PySide6.QtGraphs.QCustom3DItem, /) -> None: ...
    def releaseTheme(self, theme: PySide6.QtGraphs.QGraphsTheme, /) -> None: ...
    def removeCustomItem(self, item: PySide6.QtGraphs.QCustom3DItem, /) -> None: ...
    def removeCustomItemAt(self, position: PySide6.QtGui.QVector3D, /) -> None: ...
    def removeCustomItems(self, /) -> None: ...
    def renderToImage(self, /, imageSize: PySide6.QtCore.QSize = ...) -> typing.Tuple[PySide6.QtQuick.QQuickItemGrabResult]: ...
    def scene(self, /) -> PySide6.QtGraphs.Q3DScene: ...
    def selectedAxis(self, /) -> PySide6.QtGraphs.QAbstract3DAxis: ...
    def selectedCustomItem(self, /) -> PySide6.QtGraphs.QCustom3DItem: ...
    def selectedCustomItemIndex(self, /) -> int: ...
    def selectedElement(self, /) -> PySide6.QtGraphs.QtGraphs3D.ElementType: ...
    def selectedLabelIndex(self, /) -> int: ...
    def selectionMode(self, /) -> PySide6.QtGraphs.QtGraphs3D.SelectionFlag: ...
    def setActiveTheme(self, activeTheme: PySide6.QtGraphs.QGraphsTheme, /) -> None: ...
    def setAmbientLightStrength(self, newAmbientLightStrength: float, /) -> None: ...
    def setAspectRatio(self, ratio: float, /) -> None: ...
    def setCameraPosition(self, horizontal: float, vertical: float, /, zoom: float = ...) -> None: ...
    def setCameraPreset(self, preset: PySide6.QtGraphs.QtGraphs3D.CameraPreset, /) -> None: ...
    def setCameraTargetPosition(self, target: PySide6.QtGui.QVector3D, /) -> None: ...
    def setCameraXRotation(self, rotation: float, /) -> None: ...
    def setCameraYRotation(self, rotation: float, /) -> None: ...
    def setCameraZoomLevel(self, level: float, /) -> None: ...
    def setDefaultInputHandler(self, /) -> None: ...
    def setDragButton(self, button: PySide6.QtCore.Qt.MouseButton, /) -> None: ...
    def setGridLineType(self, gridLineType: PySide6.QtGraphs.QtGraphs3D.GridLineType, /) -> None: ...
    def setHorizontalAspectRatio(self, ratio: float, /) -> None: ...
    def setLabelMargin(self, margin: float, /) -> None: ...
    def setLightColor(self, newLightColor: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLightStrength(self, newLightStrength: float, /) -> None: ...
    def setLocale(self, locale: PySide6.QtCore.QLocale | PySide6.QtCore.QLocale.Language, /) -> None: ...
    def setMargin(self, margin: float, /) -> None: ...
    def setMaxCameraXRotation(self, rotation: float, /) -> None: ...
    def setMaxCameraYRotation(self, rotation: float, /) -> None: ...
    def setMaxCameraZoomLevel(self, level: float, /) -> None: ...
    def setMeasureFps(self, enable: bool, /) -> None: ...
    def setMinCameraXRotation(self, rotation: float, /) -> None: ...
    def setMinCameraYRotation(self, rotation: float, /) -> None: ...
    def setMinCameraZoomLevel(self, level: float, /) -> None: ...
    def setMsaaSamples(self, samples: int, /) -> None: ...
    def setOptimizationHint(self, hint: PySide6.QtGraphs.QtGraphs3D.OptimizationHint, /) -> None: ...
    def setOrthoProjection(self, enable: bool, /) -> None: ...
    def setPolar(self, enable: bool, /) -> None: ...
    def setRadialLabelOffset(self, offset: float, /) -> None: ...
    def setRotationEnabled(self, enable: bool, /) -> None: ...
    def setSelectionEnabled(self, enable: bool, /) -> None: ...
    def setSelectionMode(self, selectionMode: PySide6.QtGraphs.QtGraphs3D.SelectionFlag, /) -> None: ...
    def setShadowQuality(self, shadowQuality: PySide6.QtGraphs.QtGraphs3D.ShadowQuality, /) -> None: ...
    def setShadowStrength(self, newShadowStrength: float, /) -> None: ...
    def setTransparencyTechnique(self, technique: PySide6.QtGraphs.QtGraphs3D.TransparencyTechnique, /) -> None: ...
    def setWidget(self, widget: PySide6.QtQuickWidgets.QQuickWidget, /) -> None: ...
    def setWrapCameraXRotation(self, wrap: bool, /) -> None: ...
    def setWrapCameraYRotation(self, wrap: bool, /) -> None: ...
    def setZoomAtTargetEnabled(self, enable: bool, /) -> None: ...
    def setZoomEnabled(self, enable: bool, /) -> None: ...
    def shadowQuality(self, /) -> PySide6.QtGraphs.QtGraphs3D.ShadowQuality: ...
    def shadowStrength(self, /) -> float: ...
    def themes(self, /) -> typing.List[PySide6.QtGraphs.QGraphsTheme]: ...
    def transparencyTechnique(self, /) -> PySide6.QtGraphs.QtGraphs3D.TransparencyTechnique: ...
    def unsetDefaultDragHandler(self, /) -> None: ...
    def unsetDefaultInputHandler(self, /) -> None: ...
    def unsetDefaultPinchHandler(self, /) -> None: ...
    def unsetDefaultTapHandler(self, /) -> None: ...
    def unsetDefaultWheelHandler(self, /) -> None: ...
    def widget(self, /) -> PySide6.QtQuickWidgets.QQuickWidget: ...
    def wrapCameraXRotation(self, /) -> bool: ...
    def wrapCameraYRotation(self, /) -> bool: ...


class Q3DScatterWidgetItem(PySide6.QtGraphsWidgets.Q3DGraphsWidgetItem):

    axisXChanged             : typing.ClassVar[Signal] = ... # axisXChanged(QValue3DAxis*)
    axisYChanged             : typing.ClassVar[Signal] = ... # axisYChanged(QValue3DAxis*)
    axisZChanged             : typing.ClassVar[Signal] = ... # axisZChanged(QValue3DAxis*)
    selectedSeriesChanged    : typing.ClassVar[Signal] = ... # selectedSeriesChanged(QScatter3DSeries*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, axisX: PySide6.QtGraphs.QValue3DAxis | None = ..., axisY: PySide6.QtGraphs.QValue3DAxis | None = ..., axisZ: PySide6.QtGraphs.QValue3DAxis | None = ..., selectedSeries: PySide6.QtGraphs.QScatter3DSeries | None = ...) -> None: ...

    def addAxis(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...
    def addSeries(self, series: PySide6.QtGraphs.QScatter3DSeries, /) -> None: ...
    def axes(self, /) -> typing.List[PySide6.QtGraphs.QValue3DAxis]: ...
    def axisX(self, /) -> PySide6.QtGraphs.QValue3DAxis: ...
    def axisY(self, /) -> PySide6.QtGraphs.QValue3DAxis: ...
    def axisZ(self, /) -> PySide6.QtGraphs.QValue3DAxis: ...
    def event(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def releaseAxis(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...
    def removeSeries(self, series: PySide6.QtGraphs.QScatter3DSeries, /) -> None: ...
    def selectedSeries(self, /) -> PySide6.QtGraphs.QScatter3DSeries: ...
    def seriesList(self, /) -> typing.List[PySide6.QtGraphs.QScatter3DSeries]: ...
    def setAxisX(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...
    def setAxisY(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...
    def setAxisZ(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...


class Q3DSurfaceWidgetItem(PySide6.QtGraphsWidgets.Q3DGraphsWidgetItem):

    axisXChanged             : typing.ClassVar[Signal] = ... # axisXChanged(QValue3DAxis*)
    axisYChanged             : typing.ClassVar[Signal] = ... # axisYChanged(QValue3DAxis*)
    axisZChanged             : typing.ClassVar[Signal] = ... # axisZChanged(QValue3DAxis*)
    flipHorizontalGridChanged: typing.ClassVar[Signal] = ... # flipHorizontalGridChanged(bool)
    selectedSeriesChanged    : typing.ClassVar[Signal] = ... # selectedSeriesChanged(QSurface3DSeries*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, axisX: PySide6.QtGraphs.QValue3DAxis | None = ..., axisY: PySide6.QtGraphs.QValue3DAxis | None = ..., axisZ: PySide6.QtGraphs.QValue3DAxis | None = ..., selectedSeries: PySide6.QtGraphs.QSurface3DSeries | None = ..., flipHorizontalGrid: bool | None = ...) -> None: ...

    def addAxis(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...
    def addSeries(self, series: PySide6.QtGraphs.QSurface3DSeries, /) -> None: ...
    def axes(self, /) -> typing.List[PySide6.QtGraphs.QValue3DAxis]: ...
    def axisX(self, /) -> PySide6.QtGraphs.QValue3DAxis: ...
    def axisY(self, /) -> PySide6.QtGraphs.QValue3DAxis: ...
    def axisZ(self, /) -> PySide6.QtGraphs.QValue3DAxis: ...
    def event(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def flipHorizontalGrid(self, /) -> bool: ...
    def releaseAxis(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...
    def removeSeries(self, series: PySide6.QtGraphs.QSurface3DSeries, /) -> None: ...
    def selectedSeries(self, /) -> PySide6.QtGraphs.QSurface3DSeries: ...
    def seriesList(self, /) -> typing.List[PySide6.QtGraphs.QSurface3DSeries]: ...
    def setAxisX(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...
    def setAxisY(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...
    def setAxisZ(self, axis: PySide6.QtGraphs.QValue3DAxis, /) -> None: ...
    def setFlipHorizontalGrid(self, flip: bool, /) -> None: ...


class QIntList: ...


# eof
