# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtMultimediaWidgets, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtMultimediaWidgets`

import PySide6.QtMultimediaWidgets
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtWidgets
import PySide6.QtMultimedia

import typing
from PySide6.QtCore import Signal


class QGraphicsVideoItem(PySide6.QtWidgets.QGraphicsObject):

    nativeSizeChanged        : typing.ClassVar[Signal] = ... # nativeSizeChanged(QSizeF)

    def __init__(self, /, parent: PySide6.QtWidgets.QGraphicsItem | None = ..., *, aspectRatioMode: PySide6.QtCore.Qt.AspectRatioMode | None = ..., offset: PySide6.QtCore.QPointF | None = ..., size: PySide6.QtCore.QSizeF | None = ..., nativeSize: PySide6.QtCore.QSizeF | None = ..., videoSink: PySide6.QtMultimedia.QVideoSink | None = ...) -> None: ...

    def aspectRatioMode(self, /) -> PySide6.QtCore.Qt.AspectRatioMode: ...
    def boundingRect(self, /) -> PySide6.QtCore.QRectF: ...
    def itemChange(self, change: PySide6.QtWidgets.QGraphicsItem.GraphicsItemChange, value: typing.Any, /) -> typing.Any: ...
    def nativeSize(self, /) -> PySide6.QtCore.QSizeF: ...
    def offset(self, /) -> PySide6.QtCore.QPointF: ...
    def paint(self, painter: PySide6.QtGui.QPainter, option: PySide6.QtWidgets.QStyleOptionGraphicsItem, /, widget: PySide6.QtWidgets.QWidget | None = ...) -> None: ...
    def setAspectRatioMode(self, mode: PySide6.QtCore.Qt.AspectRatioMode, /) -> None: ...
    def setOffset(self, offset: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> None: ...
    def setSize(self, size: PySide6.QtCore.QSizeF | PySide6.QtCore.QSize, /) -> None: ...
    def size(self, /) -> PySide6.QtCore.QSizeF: ...
    def timerEvent(self, event: PySide6.QtCore.QTimerEvent, /) -> None: ...
    def type(self, /) -> int: ...
    def videoSink(self, /) -> PySide6.QtMultimedia.QVideoSink: ...


class QIntList: ...


class QVideoWidget(PySide6.QtWidgets.QWidget):

    aspectRatioModeChanged   : typing.ClassVar[Signal] = ... # aspectRatioModeChanged(Qt::AspectRatioMode)
    fullScreenChanged        : typing.ClassVar[Signal] = ... # fullScreenChanged(bool)

    def __init__(self, /, parent: PySide6.QtWidgets.QWidget | None = ..., *, fullScreen: bool | None = ..., aspectRatioMode: PySide6.QtCore.Qt.AspectRatioMode | None = ...) -> None: ...

    def aspectRatioMode(self, /) -> PySide6.QtCore.Qt.AspectRatioMode: ...
    def event(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def hideEvent(self, event: PySide6.QtGui.QHideEvent, /) -> None: ...
    def moveEvent(self, event: PySide6.QtGui.QMoveEvent, /) -> None: ...
    def resizeEvent(self, event: PySide6.QtGui.QResizeEvent, /) -> None: ...
    def setAspectRatioMode(self, mode: PySide6.QtCore.Qt.AspectRatioMode, /) -> None: ...
    def setFullScreen(self, fullScreen: bool, /) -> None: ...
    def showEvent(self, event: PySide6.QtGui.QShowEvent, /) -> None: ...
    def sizeHint(self, /) -> PySide6.QtCore.QSize: ...
    def videoSink(self, /) -> PySide6.QtMultimedia.QVideoSink: ...


# eof
