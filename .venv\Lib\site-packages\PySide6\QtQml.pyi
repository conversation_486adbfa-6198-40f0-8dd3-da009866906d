# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtQml, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtQml`

import PySide6.QtQml
import PySide6.QtCore
import PySide6.QtNetwork

import os
import enum
import typing
import collections
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class ListProperty(PySide6.QtCore.Property):

    def __init__(self, type: type, /, append: collections.abc.Callable[..., typing.Any] | None = ..., at: collections.abc.Callable[..., typing.Any] | None = ..., clear: collections.abc.Callable[..., typing.Any] | None = ..., count: collections.abc.Callable[..., typing.Any] | None = ...) -> None: ...


class QIntList: ...


class QJSEngine(PySide6.QtCore.QObject):

    uiLanguageChanged        : typing.ClassVar[Signal] = ... # uiLanguageChanged()

    class Extension(enum.Flag):

        AllExtensions             = ...  # -1
        TranslationExtension      = ...  # 0x1
        ConsoleExtension          = ...  # 0x2
        GarbageCollectionExtension = ...  # 0x4

    class ObjectOwnership(enum.Enum):

        CppOwnership              = ...  # 0x0
        JavaScriptOwnership       = ...  # 0x1


    @typing.overload
    def __init__(self, parent: PySide6.QtCore.QObject, /, *, uiLanguage: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, uiLanguage: str | None = ...) -> None: ...

    def catchError(self, /) -> PySide6.QtQml.QJSValue: ...
    def collectGarbage(self, /) -> None: ...
    def evaluate(self, program: str, /, fileName: str = ..., lineNumber: int = ...) -> typing.Tuple[PySide6.QtQml.QJSValue, typing.List[str]]: ...
    def globalObject(self, /) -> PySide6.QtQml.QJSValue: ...
    def hasError(self, /) -> bool: ...
    def importModule(self, fileName: str, /) -> PySide6.QtQml.QJSValue: ...
    def installExtensions(self, extensions: PySide6.QtQml.QJSEngine.Extension, /, object: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int = ...) -> None: ...
    def isInterrupted(self, /) -> bool: ...
    def newArray(self, /, length: int | None = ...) -> PySide6.QtQml.QJSValue: ...
    def newErrorObject(self, errorType: PySide6.QtQml.QJSValue.ErrorType, /, message: str = ...) -> PySide6.QtQml.QJSValue: ...
    def newObject(self, /) -> PySide6.QtQml.QJSValue: ...
    def newQMetaObject(self, metaObject: PySide6.QtCore.QMetaObject, /) -> PySide6.QtQml.QJSValue: ...
    def newQObject(self, object: PySide6.QtCore.QObject, /) -> PySide6.QtQml.QJSValue: ...
    def newSymbol(self, name: str, /) -> PySide6.QtQml.QJSValue: ...
    @staticmethod
    def objectOwnership(arg__1: PySide6.QtCore.QObject, /) -> PySide6.QtQml.QJSEngine.ObjectOwnership: ...
    def registerModule(self, moduleName: str, value: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /) -> bool: ...
    def setInterrupted(self, interrupted: bool, /) -> None: ...
    @staticmethod
    def setObjectOwnership(arg__1: PySide6.QtCore.QObject, arg__2: PySide6.QtQml.QJSEngine.ObjectOwnership, /) -> None: ...
    def setUiLanguage(self, language: str, /) -> None: ...
    @typing.overload
    def throwError(self, errorType: PySide6.QtQml.QJSValue.ErrorType, /, message: str = ...) -> None: ...
    @typing.overload
    def throwError(self, message: str, /) -> None: ...
    @typing.overload
    def throwError(self, error: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /) -> None: ...
    def toScriptValue(self, value: typing.Any, /) -> PySide6.QtQml.QJSValue: ...
    def uiLanguage(self, /) -> str: ...


class QJSManagedValue(Shiboken.Object):

    class Type(enum.Enum):

        Undefined                 = ...  # 0x0
        Boolean                   = ...  # 0x1
        Number                    = ...  # 0x2
        String                    = ...  # 0x3
        Object                    = ...  # 0x4
        Symbol                    = ...  # 0x5
        Function                  = ...  # 0x6


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, string: str, engine: PySide6.QtQml.QJSEngine, /) -> None: ...
    @typing.overload
    def __init__(self, value: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, engine: PySide6.QtQml.QJSEngine, /) -> None: ...
    @typing.overload
    def __init__(self, value: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, engine: PySide6.QtQml.QJSEngine, /) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any, engine: PySide6.QtQml.QJSEngine, /) -> None: ...

    def call(self, /, arguments: collections.abc.Sequence[PySide6.QtQml.QJSValue] = ...) -> PySide6.QtQml.QJSValue: ...
    def callAsConstructor(self, /, arguments: collections.abc.Sequence[PySide6.QtQml.QJSValue] = ...) -> PySide6.QtQml.QJSValue: ...
    def callWithInstance(self, instance: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /, arguments: collections.abc.Sequence[PySide6.QtQml.QJSValue] = ...) -> PySide6.QtQml.QJSValue: ...
    @typing.overload
    def deleteProperty(self, name: str, /) -> bool: ...
    @typing.overload
    def deleteProperty(self, arrayIndex: int, /) -> bool: ...
    def engine(self, /) -> PySide6.QtQml.QJSEngine: ...
    def equals(self, other: PySide6.QtQml.QJSManagedValue, /) -> bool: ...
    @typing.overload
    def hasOwnProperty(self, name: str, /) -> bool: ...
    @typing.overload
    def hasOwnProperty(self, arrayIndex: int, /) -> bool: ...
    @typing.overload
    def hasProperty(self, name: str, /) -> bool: ...
    @typing.overload
    def hasProperty(self, arrayIndex: int, /) -> bool: ...
    def isArray(self, /) -> bool: ...
    def isBoolean(self, /) -> bool: ...
    def isDate(self, /) -> bool: ...
    def isError(self, /) -> bool: ...
    def isFunction(self, /) -> bool: ...
    def isInteger(self, /) -> bool: ...
    def isJsMetaType(self, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def isNumber(self, /) -> bool: ...
    def isObject(self, /) -> bool: ...
    def isQMetaObject(self, /) -> bool: ...
    def isQObject(self, /) -> bool: ...
    def isRegularExpression(self, /) -> bool: ...
    def isString(self, /) -> bool: ...
    def isSymbol(self, /) -> bool: ...
    def isUndefined(self, /) -> bool: ...
    def isUrl(self, /) -> bool: ...
    def isVariant(self, /) -> bool: ...
    def jsMetaInstantiate(self, /, values: collections.abc.Sequence[PySide6.QtQml.QJSValue] = ...) -> PySide6.QtQml.QJSManagedValue: ...
    def jsMetaMembers(self, /) -> typing.List[str]: ...
    def jsMetaType(self, /) -> PySide6.QtQml.QJSManagedValue: ...
    @typing.overload
    def property(self, name: str, /) -> PySide6.QtQml.QJSValue: ...
    @typing.overload
    def property(self, arrayIndex: int, /) -> PySide6.QtQml.QJSValue: ...
    def prototype(self, /) -> PySide6.QtQml.QJSManagedValue: ...
    @typing.overload
    def setProperty(self, name: str, value: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /) -> None: ...
    @typing.overload
    def setProperty(self, arrayIndex: int, value: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /) -> None: ...
    def setPrototype(self, prototype: PySide6.QtQml.QJSManagedValue, /) -> None: ...
    def strictlyEquals(self, other: PySide6.QtQml.QJSManagedValue, /) -> bool: ...
    def toBoolean(self, /) -> bool: ...
    def toDateTime(self, /) -> PySide6.QtCore.QDateTime: ...
    def toInteger(self, /) -> int: ...
    def toJSValue(self, /) -> PySide6.QtQml.QJSValue: ...
    def toNumber(self, /) -> float: ...
    def toPrimitive(self, /) -> PySide6.QtQml.QJSPrimitiveValue: ...
    def toQMetaObject(self, /) -> PySide6.QtCore.QMetaObject: ...
    def toQObject(self, /) -> PySide6.QtCore.QObject: ...
    def toRegularExpression(self, /) -> PySide6.QtCore.QRegularExpression: ...
    def toString(self, /) -> str: ...
    def toUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def toVariant(self, /) -> typing.Any: ...
    def type(self, /) -> PySide6.QtQml.QJSManagedValue.Type: ...


class QJSPrimitiveValue(Shiboken.Object):

    class Type(enum.Enum):

        Undefined                 = ...  # 0x0
        Null                      = ...  # 0x1
        Boolean                   = ...  # 0x2
        Integer                   = ...  # 0x3
        Double                    = ...  # 0x4
        String                    = ...  # 0x5


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QJSPrimitiveValue: PySide6.QtQml.QJSPrimitiveValue, /) -> None: ...
    @typing.overload
    def __init__(self, string: str, /) -> None: ...
    @typing.overload
    def __init__(self, type: PySide6.QtCore.QMetaType | PySide6.QtCore.QMetaType.Type, /) -> None: ...
    @typing.overload
    def __init__(self, type: PySide6.QtCore.QMetaType | PySide6.QtCore.QMetaType.Type, value: int, /) -> None: ...
    @typing.overload
    def __init__(self, value: bool, /) -> None: ...
    @typing.overload
    def __init__(self, value: int, /) -> None: ...
    @typing.overload
    def __init__(self, value: float, /) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any, /) -> None: ...

    def __add__(self, rhs: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> PySide6.QtQml.QJSPrimitiveValue: ...
    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> bool: ...
    def __ge__(self, rhs: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> bool: ...
    def __gt__(self, rhs: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> bool: ...
    def __iadd__(self, arg__1: int, /) -> PySide6.QtQml.QJSPrimitiveValue: ...  # type: ignore[misc]
    def __isub__(self, arg__1: int, /) -> PySide6.QtQml.QJSPrimitiveValue: ...  # type: ignore[misc]
    def __le__(self, rhs: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> bool: ...
    def __lt__(self, rhs: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> bool: ...
    def __mod__(self, rhs: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> PySide6.QtQml.QJSPrimitiveValue: ...
    def __mul__(self, rhs: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> PySide6.QtQml.QJSPrimitiveValue: ...
    def __ne__(self, rhs: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> bool: ...
    def __neg__(self, /) -> PySide6.QtQml.QJSPrimitiveValue: ...
    def __pos__(self, /) -> PySide6.QtQml.QJSPrimitiveValue: ...
    def __sub__(self, rhs: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> PySide6.QtQml.QJSPrimitiveValue: ...
    def constData(self, /) -> int: ...
    def data(self, /) -> int: ...
    def equals(self, other: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> bool: ...
    def metaType(self, /) -> PySide6.QtCore.QMetaType: ...
    def strictlyEquals(self, other: PySide6.QtQml.QJSPrimitiveValue | str | bool | float | int, /) -> bool: ...
    def toBoolean(self, /) -> bool: ...
    def toDouble(self, /) -> float: ...
    def toInteger(self, /) -> int: ...
    def toString(self, /) -> str: ...
    def toVariant(self, /) -> typing.Any: ...
    def type(self, /) -> PySide6.QtQml.QJSPrimitiveValue.Type: ...


class QJSValue(Shiboken.Object):

    class ErrorType(enum.Enum):

        NoError                   = ...  # 0x0
        GenericError              = ...  # 0x1
        EvalError                 = ...  # 0x2
        RangeError                = ...  # 0x3
        ReferenceError            = ...  # 0x4
        SyntaxError               = ...  # 0x5
        TypeError                 = ...  # 0x6
        URIError                  = ...  # 0x7

    class ObjectConversionBehavior(enum.Enum):

        ConvertJSObjects          = ...  # 0x0
        RetainJSObjects           = ...  # 0x1

    class SpecialValue(enum.Enum):

        NullValue                 = ...  # 0x0
        UndefinedValue            = ...  # 0x1


    @typing.overload
    def __init__(self, other: PySide6.QtQml.QJSValue, /) -> None: ...
    @typing.overload
    def __init__(self, /, value: PySide6.QtQml.QJSValue.SpecialValue = ...) -> None: ...
    @typing.overload
    def __init__(self, value: str, /) -> None: ...
    @typing.overload
    def __init__(self, str: bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def __init__(self, value: bool, /) -> None: ...
    @typing.overload
    def __init__(self, value: int, /) -> None: ...
    @typing.overload
    def __init__(self, value: float, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __lshift__(self, arg__1: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, arg__1: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def call(self, /, args: collections.abc.Sequence[PySide6.QtQml.QJSValue] = ...) -> PySide6.QtQml.QJSValue: ...
    def callAsConstructor(self, /, args: collections.abc.Sequence[PySide6.QtQml.QJSValue] = ...) -> PySide6.QtQml.QJSValue: ...
    def callWithInstance(self, instance: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /, args: collections.abc.Sequence[PySide6.QtQml.QJSValue] = ...) -> PySide6.QtQml.QJSValue: ...
    def deleteProperty(self, name: str, /) -> bool: ...
    def equals(self, other: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /) -> bool: ...
    def errorType(self, /) -> PySide6.QtQml.QJSValue.ErrorType: ...
    def hasOwnProperty(self, name: str, /) -> bool: ...
    def hasProperty(self, name: str, /) -> bool: ...
    def isArray(self, /) -> bool: ...
    def isBool(self, /) -> bool: ...
    def isCallable(self, /) -> bool: ...
    def isDate(self, /) -> bool: ...
    def isError(self, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def isNumber(self, /) -> bool: ...
    def isObject(self, /) -> bool: ...
    def isQMetaObject(self, /) -> bool: ...
    def isQObject(self, /) -> bool: ...
    def isRegExp(self, /) -> bool: ...
    def isString(self, /) -> bool: ...
    def isUndefined(self, /) -> bool: ...
    def isUrl(self, /) -> bool: ...
    def isVariant(self, /) -> bool: ...
    @typing.overload
    def property(self, name: str, /) -> PySide6.QtQml.QJSValue: ...
    @typing.overload
    def property(self, arrayIndex: int, /) -> PySide6.QtQml.QJSValue: ...
    def prototype(self, /) -> PySide6.QtQml.QJSValue: ...
    @typing.overload
    def setProperty(self, name: str, value: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /) -> None: ...
    @typing.overload
    def setProperty(self, arrayIndex: int, value: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /) -> None: ...
    def setPrototype(self, prototype: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /) -> None: ...
    def strictlyEquals(self, other: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /) -> bool: ...
    def toBool(self, /) -> bool: ...
    def toDateTime(self, /) -> PySide6.QtCore.QDateTime: ...
    def toInt(self, /) -> int: ...
    def toNumber(self, /) -> float: ...
    def toPrimitive(self, /) -> PySide6.QtQml.QJSPrimitiveValue: ...
    def toQMetaObject(self, /) -> PySide6.QtCore.QMetaObject: ...
    def toQObject(self, /) -> PySide6.QtCore.QObject: ...
    def toString(self, /) -> str: ...
    def toUInt(self, /) -> int: ...
    @typing.overload
    def toVariant(self, /) -> typing.Any: ...
    @typing.overload
    def toVariant(self, behavior: PySide6.QtQml.QJSValue.ObjectConversionBehavior, /) -> typing.Any: ...


class QJSValueIterator(Shiboken.Object):

    def __init__(self, value: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /) -> None: ...

    def hasNext(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def next(self, /) -> bool: ...
    def value(self, /) -> PySide6.QtQml.QJSValue: ...


class QPyQmlParserStatus(PySide6.QtCore.QObject, PySide6.QtQml.QQmlParserStatus):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...


class QPyQmlPropertyValueSource(PySide6.QtCore.QObject, PySide6.QtQml.QQmlPropertyValueSource):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...


class QQmlAbstractUrlInterceptor(Shiboken.Object):

    class DataType(enum.Enum):

        QmlFile                   = ...  # 0x0
        JavaScriptFile            = ...  # 0x1
        QmldirFile                = ...  # 0x2
        UrlString                 = ...  # 0x1000


    def __init__(self, /) -> None: ...

    def intercept(self, path: PySide6.QtCore.QUrl | str, type: PySide6.QtQml.QQmlAbstractUrlInterceptor.DataType, /) -> PySide6.QtCore.QUrl: ...


class QQmlApplicationEngine(PySide6.QtQml.QQmlEngine):

    objectCreated            : typing.ClassVar[Signal] = ... # objectCreated(QObject*,QUrl)
    objectCreationFailed     : typing.ClassVar[Signal] = ... # objectCreationFailed(QUrl)

    @typing.overload
    def __init__(self, uri: str, typeName: str, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, filePath: str, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, url: PySide6.QtCore.QUrl | str, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    @typing.overload
    def load(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    @typing.overload
    def load(self, filePath: str | bytes | os.PathLike[str], /) -> None: ...
    def loadData(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, url: PySide6.QtCore.QUrl | str = ...) -> None: ...
    def loadFromModule(self, uri: str, typeName: str, /) -> None: ...
    def rootObjects(self, /) -> typing.List[PySide6.QtCore.QObject]: ...
    def setExtraFileSelectors(self, extraFileSelectors: collections.abc.Sequence[str], /) -> None: ...
    def setInitialProperties(self, initialProperties: typing.Dict[str, typing.Any], /) -> None: ...


class QQmlComponent(PySide6.QtCore.QObject):

    progressChanged          : typing.ClassVar[Signal] = ... # progressChanged(double)
    statusChanged            : typing.ClassVar[Signal] = ... # statusChanged(QQmlComponent::Status)

    class CompilationMode(enum.Enum):

        PreferSynchronous         = ...  # 0x0
        Asynchronous              = ...  # 0x1

    class Status(enum.Enum):

        Null                      = ...  # 0x0
        Ready                     = ...  # 0x1
        Loading                   = ...  # 0x2
        Error                     = ...  # 0x3


    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlEngine, url: PySide6.QtCore.QUrl, mode: PySide6.QtQml.QQmlComponent.CompilationMode, /, parent: PySide6.QtCore.QObject | None = ..., *, progress: float | None = ..., status: PySide6.QtQml.QQmlComponent.Status | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlEngine, url: PySide6.QtCore.QUrl, /, parent: PySide6.QtCore.QObject | None = ..., *, progress: float | None = ..., status: PySide6.QtQml.QQmlComponent.Status | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlEngine, fileName: str, mode: PySide6.QtQml.QQmlComponent.CompilationMode, /, parent: PySide6.QtCore.QObject | None = ..., *, progress: float | None = ..., status: PySide6.QtQml.QQmlComponent.Status | None = ..., url: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, engine: PySide6.QtQml.QQmlEngine, uri: str, typeName: str, mode: PySide6.QtQml.QQmlComponent.CompilationMode, /, parent: PySide6.QtCore.QObject | None = ..., *, progress: float | None = ..., status: PySide6.QtQml.QQmlComponent.Status | None = ..., url: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, engine: PySide6.QtQml.QQmlEngine, uri: str, typeName: str, /, parent: PySide6.QtCore.QObject | None = ..., *, progress: float | None = ..., status: PySide6.QtQml.QQmlComponent.Status | None = ..., url: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlEngine, fileName: str, /, parent: PySide6.QtCore.QObject | None = ..., *, progress: float | None = ..., status: PySide6.QtQml.QQmlComponent.Status | None = ..., url: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlEngine, /, parent: PySide6.QtCore.QObject | None = ..., *, progress: float | None = ..., status: PySide6.QtQml.QQmlComponent.Status | None = ..., url: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, progress: float | None = ..., status: PySide6.QtQml.QQmlComponent.Status | None = ..., url: PySide6.QtCore.QUrl | None = ...) -> None: ...

    def beginCreate(self, arg__1: PySide6.QtQml.QQmlContext, /) -> PySide6.QtCore.QObject: ...
    def completeCreate(self, /) -> None: ...
    @typing.overload
    def create(self, arg__1: PySide6.QtQml.QQmlIncubator, /, context: PySide6.QtQml.QQmlContext | None = ..., forContext: PySide6.QtQml.QQmlContext | None = ...) -> None: ...
    @typing.overload
    def create(self, /, context: PySide6.QtQml.QQmlContext | None = ...) -> PySide6.QtCore.QObject: ...
    def createObject(self, /, parent: PySide6.QtCore.QObject | None = ..., properties: typing.Dict[str, typing.Any] = ...) -> PySide6.QtCore.QObject: ...
    def createWithInitialProperties(self, initialProperties: typing.Dict[str, typing.Any], /, context: PySide6.QtQml.QQmlContext | None = ...) -> PySide6.QtCore.QObject: ...
    def creationContext(self, /) -> PySide6.QtQml.QQmlContext: ...
    def engine(self, /) -> PySide6.QtQml.QQmlEngine: ...
    def errorString(self, /) -> str: ...
    def errors(self, /) -> typing.List[PySide6.QtQml.QQmlError]: ...
    def isBound(self, /) -> bool: ...
    def isError(self, /) -> bool: ...
    def isLoading(self, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def isReady(self, /) -> bool: ...
    def loadFromModule(self, uri: str, typeName: str, /, mode: PySide6.QtQml.QQmlComponent.CompilationMode = ...) -> None: ...
    @typing.overload
    def loadUrl(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    @typing.overload
    def loadUrl(self, url: PySide6.QtCore.QUrl | str, mode: PySide6.QtQml.QQmlComponent.CompilationMode, /) -> None: ...
    def progress(self, /) -> float: ...
    def setData(self, arg__1: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, baseUrl: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setInitialProperties(self, component: PySide6.QtCore.QObject, properties: typing.Dict[str, typing.Any], /) -> None: ...
    def status(self, /) -> PySide6.QtQml.QQmlComponent.Status: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QQmlContext(PySide6.QtCore.QObject):

    class PropertyPair(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, PropertyPair: PySide6.QtQml.QQmlContext.PropertyPair, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...


    @typing.overload
    def __init__(self, parent: PySide6.QtQml.QQmlEngine, /, objParent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: PySide6.QtQml.QQmlContext, /, objParent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def baseUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def contextObject(self, /) -> PySide6.QtCore.QObject: ...
    def contextProperty(self, arg__1: str, /) -> typing.Any: ...
    def engine(self, /) -> PySide6.QtQml.QQmlEngine: ...
    def importedScript(self, name: str, /) -> PySide6.QtQml.QJSValue: ...
    def isValid(self, /) -> bool: ...
    def nameForObject(self, arg__1: PySide6.QtCore.QObject, /) -> str: ...
    def objectForName(self, arg__1: str, /) -> PySide6.QtCore.QObject: ...
    def parentContext(self, /) -> PySide6.QtQml.QQmlContext: ...
    def resolvedUrl(self, arg__1: PySide6.QtCore.QUrl | str, /) -> PySide6.QtCore.QUrl: ...
    def setBaseUrl(self, arg__1: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setContextObject(self, arg__1: PySide6.QtCore.QObject, /) -> None: ...
    def setContextProperties(self, properties: collections.abc.Sequence[PySide6.QtQml.QQmlContext.PropertyPair], /) -> None: ...
    @typing.overload
    def setContextProperty(self, arg__1: str, arg__2: PySide6.QtCore.QObject, /) -> None: ...
    @typing.overload
    def setContextProperty(self, arg__1: str, arg__2: typing.Any, /) -> None: ...


class QQmlDebuggingEnabler(Shiboken.Object):

    class StartMode(enum.Enum):

        DoNotWaitForClient        = ...  # 0x0
        WaitForClient             = ...  # 0x1


    def __init__(self, /, printWarning: bool = ...) -> None: ...

    @staticmethod
    def connectToLocalDebugger(socketFileName: str, /, mode: PySide6.QtQml.QQmlDebuggingEnabler.StartMode = ...) -> bool: ...
    @staticmethod
    def debuggerServices() -> typing.List[str]: ...
    @staticmethod
    def enableDebugging(printWarning: bool, /) -> None: ...
    @staticmethod
    def inspectorServices() -> typing.List[str]: ...
    @staticmethod
    def nativeDebuggerServices() -> typing.List[str]: ...
    @staticmethod
    def profilerServices() -> typing.List[str]: ...
    @staticmethod
    def setServices(services: collections.abc.Sequence[str], /) -> None: ...
    @staticmethod
    def startDebugConnector(pluginName: str, /, configuration: typing.Dict[str, typing.Any] = ...) -> bool: ...
    @staticmethod
    def startTcpDebugServer(port: int, /, mode: PySide6.QtQml.QQmlDebuggingEnabler.StartMode = ..., hostName: str = ...) -> bool: ...


class QQmlEngine(PySide6.QtQml.QJSEngine):

    exit                     : typing.ClassVar[Signal] = ... # exit(int)
    offlineStoragePathChanged: typing.ClassVar[Signal] = ... # offlineStoragePathChanged()
    quit                     : typing.ClassVar[Signal] = ... # quit()
    warnings                 : typing.ClassVar[Signal] = ... # warnings(QList<QQmlError>)

    def __init__(self, /, p: PySide6.QtCore.QObject | None = ..., *, offlineStoragePath: str | None = ...) -> None: ...

    def addImageProvider(self, id: str, arg__2: PySide6.QtQml.QQmlImageProviderBase, /) -> None: ...
    def addImportPath(self, dir: str | bytes | os.PathLike[str], /) -> None: ...
    def addNamedBundle(self, arg__1: str, arg__2: str, /) -> bool: ...
    def addPluginPath(self, dir: str | bytes | os.PathLike[str], /) -> None: ...
    def addUrlInterceptor(self, urlInterceptor: PySide6.QtQml.QQmlAbstractUrlInterceptor, /) -> None: ...
    def baseUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def captureProperty(self, object: PySide6.QtCore.QObject, property: PySide6.QtCore.QMetaProperty, /) -> None: ...
    def clearComponentCache(self, /) -> None: ...
    def clearSingletons(self, /) -> None: ...
    @staticmethod
    def contextForObject(arg__1: PySide6.QtCore.QObject, /) -> PySide6.QtQml.QQmlContext: ...
    def event(self, arg__1: PySide6.QtCore.QEvent, /) -> bool: ...
    def imageProvider(self, id: str, /) -> PySide6.QtQml.QQmlImageProviderBase: ...
    def importPathList(self, /) -> typing.List[str]: ...
    def importPlugin(self, filePath: str, uri: str, errors: collections.abc.Sequence[PySide6.QtQml.QQmlError], /) -> bool: ...
    def incubationController(self, /) -> PySide6.QtQml.QQmlIncubationController: ...
    def interceptUrl(self, url: PySide6.QtCore.QUrl | str, type: PySide6.QtQml.QQmlAbstractUrlInterceptor.DataType, /) -> PySide6.QtCore.QUrl: ...
    def markCurrentFunctionAsTranslationBinding(self, /) -> None: ...
    def networkAccessManager(self, /) -> PySide6.QtNetwork.QNetworkAccessManager: ...
    def networkAccessManagerFactory(self, /) -> PySide6.QtQml.QQmlNetworkAccessManagerFactory: ...
    def offlineStorageDatabaseFilePath(self, databaseName: str, /) -> str: ...
    def offlineStoragePath(self, /) -> str: ...
    def outputWarningsToStandardError(self, /) -> bool: ...
    def pluginPathList(self, /) -> typing.List[str]: ...
    def removeImageProvider(self, id: str, /) -> None: ...
    def removeUrlInterceptor(self, urlInterceptor: PySide6.QtQml.QQmlAbstractUrlInterceptor, /) -> None: ...
    def retranslate(self, /) -> None: ...
    def rootContext(self, /) -> PySide6.QtQml.QQmlContext: ...
    def setBaseUrl(self, arg__1: PySide6.QtCore.QUrl | str, /) -> None: ...
    @staticmethod
    def setContextForObject(arg__1: PySide6.QtCore.QObject, arg__2: PySide6.QtQml.QQmlContext, /) -> None: ...
    def setImportPathList(self, paths: collections.abc.Sequence[str], /) -> None: ...
    def setIncubationController(self, arg__1: PySide6.QtQml.QQmlIncubationController, /) -> None: ...
    def setNetworkAccessManagerFactory(self, arg__1: PySide6.QtQml.QQmlNetworkAccessManagerFactory, /) -> None: ...
    def setOfflineStoragePath(self, dir: str, /) -> None: ...
    def setOutputWarningsToStandardError(self, arg__1: bool, /) -> None: ...
    def setPluginPathList(self, paths: collections.abc.Sequence[str], /) -> None: ...
    def setUrlInterceptor(self, urlInterceptor: PySide6.QtQml.QQmlAbstractUrlInterceptor, /) -> None: ...
    @typing.overload
    def singletonInstance(self, uri: str, typeName: str, /) -> PySide6.QtCore.QObject | PySide6.QtQml.QJSValue | None: ...
    @typing.overload
    def singletonInstance(self, qmlTypeId: int, /) -> PySide6.QtCore.QObject | PySide6.QtQml.QJSValue | None: ...
    def trimComponentCache(self, /) -> None: ...
    def urlInterceptor(self, /) -> PySide6.QtQml.QQmlAbstractUrlInterceptor: ...
    def urlInterceptors(self, /) -> typing.List[PySide6.QtQml.QQmlAbstractUrlInterceptor]: ...


class QQmlError(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlError, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __repr__(self, /) -> str: ...
    def column(self, /) -> int: ...
    def description(self, /) -> str: ...
    def isValid(self, /) -> bool: ...
    def line(self, /) -> int: ...
    def messageType(self, /) -> PySide6.QtCore.QtMsgType: ...
    def object(self, /) -> PySide6.QtCore.QObject: ...
    def setColumn(self, arg__1: int, /) -> None: ...
    def setDescription(self, arg__1: str, /) -> None: ...
    def setLine(self, arg__1: int, /) -> None: ...
    def setMessageType(self, messageType: PySide6.QtCore.QtMsgType, /) -> None: ...
    def setObject(self, arg__1: PySide6.QtCore.QObject, /) -> None: ...
    def setUrl(self, arg__1: PySide6.QtCore.QUrl | str, /) -> None: ...
    def swap(self, other: PySide6.QtQml.QQmlError, /) -> None: ...
    def toString(self, /) -> str: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QQmlExpression(PySide6.QtCore.QObject):

    valueChanged             : typing.ClassVar[Signal] = ... # valueChanged()

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlContext, arg__2: PySide6.QtCore.QObject, arg__3: str, /, arg__4: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlScriptString, /, arg__2: PySide6.QtQml.QQmlContext | None = ..., arg__3: PySide6.QtCore.QObject | None = ..., arg__4: PySide6.QtCore.QObject | None = ...) -> None: ...

    def clearError(self, /) -> None: ...
    def columnNumber(self, /) -> int: ...
    def context(self, /) -> PySide6.QtQml.QQmlContext: ...
    def engine(self, /) -> PySide6.QtQml.QQmlEngine: ...
    def error(self, /) -> PySide6.QtQml.QQmlError: ...
    def evaluate(self, /) -> typing.Tuple[typing.Any, bool]: ...
    def expression(self, /) -> str: ...
    def hasError(self, /) -> bool: ...
    def lineNumber(self, /) -> int: ...
    def notifyOnValueChanged(self, /) -> bool: ...
    def scopeObject(self, /) -> PySide6.QtCore.QObject: ...
    def setExpression(self, arg__1: str, /) -> None: ...
    def setNotifyOnValueChanged(self, arg__1: bool, /) -> None: ...
    def setSourceLocation(self, fileName: str, line: int, /, column: int | None = ...) -> None: ...
    def sourceFile(self, /) -> str: ...


class QQmlExtensionInterface(PySide6.QtQml.QQmlTypesExtensionInterface):

    def __init__(self, /) -> None: ...

    def initializeEngine(self, engine: PySide6.QtQml.QQmlEngine, uri: bytes | bytearray | memoryview, /) -> None: ...


class QQmlExtensionPlugin(PySide6.QtCore.QObject, PySide6.QtQml.QQmlExtensionInterface):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def baseUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def initializeEngine(self, engine: PySide6.QtQml.QQmlEngine, uri: bytes | bytearray | memoryview, /) -> None: ...
    def registerTypes(self, uri: bytes | bytearray | memoryview, /) -> None: ...
    def unregisterTypes(self, /) -> None: ...


class QQmlFile(Shiboken.Object):

    class Status(enum.Enum):

        Null                      = ...  # 0x0
        Ready                     = ...  # 0x1
        Error                     = ...  # 0x2
        Loading                   = ...  # 0x3


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, engine: PySide6.QtQml.QQmlEngine, url: str, /) -> None: ...
    @typing.overload
    def __init__(self, engine: PySide6.QtQml.QQmlEngine, url: PySide6.QtCore.QUrl | str, /) -> None: ...

    @typing.overload
    def clear(self, /) -> None: ...
    @typing.overload
    def clear(self, object: PySide6.QtCore.QObject, /) -> None: ...
    @typing.overload
    def connectDownloadProgress(self, arg__1: PySide6.QtCore.QObject, arg__2: bytes | bytearray | memoryview, /) -> bool: ...
    @typing.overload
    def connectDownloadProgress(self, arg__1: PySide6.QtCore.QObject, arg__2: int, /) -> bool: ...
    @typing.overload
    def connectFinished(self, arg__1: PySide6.QtCore.QObject, arg__2: bytes | bytearray | memoryview, /) -> bool: ...
    @typing.overload
    def connectFinished(self, arg__1: PySide6.QtCore.QObject, arg__2: int, /) -> bool: ...
    def data(self, /) -> bytes | bytearray | memoryview: ...
    def dataByteArray(self, /) -> PySide6.QtCore.QByteArray: ...
    def error(self, /) -> str: ...
    def isError(self, /) -> bool: ...
    def isLoading(self, /) -> bool: ...
    @typing.overload
    @staticmethod
    def isLocalFile(url: str, /) -> bool: ...
    @typing.overload
    @staticmethod
    def isLocalFile(url: PySide6.QtCore.QUrl | str, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def isReady(self, /) -> bool: ...
    @typing.overload
    @staticmethod
    def isSynchronous(url: str, /) -> bool: ...
    @typing.overload
    @staticmethod
    def isSynchronous(url: PySide6.QtCore.QUrl | str, /) -> bool: ...
    @typing.overload
    def load(self, arg__1: PySide6.QtQml.QQmlEngine, arg__2: str, /) -> None: ...
    @typing.overload
    def load(self, arg__1: PySide6.QtQml.QQmlEngine, arg__2: PySide6.QtCore.QUrl | str, /) -> None: ...
    def size(self, /) -> int: ...
    def status(self, /) -> PySide6.QtQml.QQmlFile.Status: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...
    @typing.overload
    @staticmethod
    def urlToLocalFileOrQrc(arg__1: str, /) -> str: ...
    @typing.overload
    @staticmethod
    def urlToLocalFileOrQrc(arg__1: PySide6.QtCore.QUrl | str, /) -> str: ...


class QQmlFileSelector(PySide6.QtCore.QObject):

    def __init__(self, engine: PySide6.QtQml.QQmlEngine, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    @staticmethod
    def get(arg__1: PySide6.QtQml.QQmlEngine, /) -> PySide6.QtQml.QQmlFileSelector: ...
    def selector(self, /) -> PySide6.QtCore.QFileSelector: ...
    def setExtraSelectors(self, strings: collections.abc.Sequence[str], /) -> None: ...
    def setSelector(self, selector: PySide6.QtCore.QFileSelector, /) -> None: ...


class QQmlImageProviderBase(PySide6.QtCore.QObject):

    class Flag(enum.Flag):

        ForceAsynchronousImageLoading = ...  # 0x1

    class ImageType(enum.Enum):

        Invalid                   = ...  # 0x0
        Image                     = ...  # 0x1
        Pixmap                    = ...  # 0x2
        Texture                   = ...  # 0x3
        ImageResponse             = ...  # 0x4


    def flags(self, /) -> PySide6.QtQml.QQmlImageProviderBase.Flag: ...
    def imageType(self, /) -> PySide6.QtQml.QQmlImageProviderBase.ImageType: ...


class QQmlIncubationController(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def engine(self, /) -> PySide6.QtQml.QQmlEngine: ...
    def incubateFor(self, msecs: int, /) -> None: ...
    def incubateWhile(self, flag: PySide6.QtQml.VolatileBool, /, msecs: int | None = ...) -> None: ...
    def incubatingObjectCount(self, /) -> int: ...
    def incubatingObjectCountChanged(self, arg__1: int, /) -> None: ...


class QQmlIncubator(Shiboken.Object):

    class IncubationMode(enum.Enum):

        Asynchronous              = ...  # 0x0
        AsynchronousIfNested      = ...  # 0x1
        Synchronous               = ...  # 0x2

    class Status(enum.Enum):

        Null                      = ...  # 0x0
        Ready                     = ...  # 0x1
        Loading                   = ...  # 0x2
        Error                     = ...  # 0x3


    def __init__(self, /, arg__1: PySide6.QtQml.QQmlIncubator.IncubationMode = ...) -> None: ...

    def clear(self, /) -> None: ...
    def errors(self, /) -> typing.List[PySide6.QtQml.QQmlError]: ...
    def forceCompletion(self, /) -> None: ...
    def incubationMode(self, /) -> PySide6.QtQml.QQmlIncubator.IncubationMode: ...
    def isError(self, /) -> bool: ...
    def isLoading(self, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def isReady(self, /) -> bool: ...
    def object(self, /) -> PySide6.QtCore.QObject: ...
    def setInitialProperties(self, initialProperties: typing.Dict[str, typing.Any], /) -> None: ...
    def setInitialState(self, arg__1: PySide6.QtCore.QObject, /) -> None: ...
    def status(self, /) -> PySide6.QtQml.QQmlIncubator.Status: ...
    def statusChanged(self, arg__1: PySide6.QtQml.QQmlIncubator.Status, /) -> None: ...


class QQmlListReference(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, o: PySide6.QtCore.QObject, property: bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def __init__(self, o: PySide6.QtCore.QObject, property: bytes | bytearray | memoryview, engine: PySide6.QtQml.QQmlEngine, /) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlListReference, /) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any, /) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any, engine: PySide6.QtQml.QQmlEngine, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtQml.QQmlListReference, /) -> bool: ...
    def append(self, arg__1: PySide6.QtCore.QObject, /) -> bool: ...
    def at(self, arg__1: int, /) -> PySide6.QtCore.QObject: ...
    def canAppend(self, /) -> bool: ...
    def canAt(self, /) -> bool: ...
    def canClear(self, /) -> bool: ...
    def canCount(self, /) -> bool: ...
    def canRemoveLast(self, /) -> bool: ...
    def canReplace(self, /) -> bool: ...
    def clear(self, /) -> bool: ...
    def count(self, /) -> int: ...
    def isManipulable(self, /) -> bool: ...
    def isReadable(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def listElementType(self, /) -> PySide6.QtCore.QMetaObject: ...
    def object(self, /) -> PySide6.QtCore.QObject: ...
    def removeLast(self, /) -> bool: ...
    def replace(self, arg__1: int, arg__2: PySide6.QtCore.QObject, /) -> bool: ...
    def size(self, /) -> int: ...


class QQmlModuleImportSpecialVersions(enum.Flag):

    QQmlModuleImportAuto      = ...  # -2
    QQmlModuleImportLatest    = ...  # -1
    QQmlModuleImportModuleAny = ...  # -1


class QQmlNetworkAccessManagerFactory(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def create(self, parent: PySide6.QtCore.QObject, /) -> PySide6.QtNetwork.QNetworkAccessManager: ...


class QQmlParserStatus(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def classBegin(self, /) -> None: ...
    def componentComplete(self, /) -> None: ...


class QQmlProperty(Shiboken.Object):

    class PropertyTypeCategory(enum.Enum):

        InvalidCategory           = ...  # 0x0
        List                      = ...  # 0x1
        Object                    = ...  # 0x2
        Normal                    = ...  # 0x3

    class Type(enum.Enum):

        Invalid                   = ...  # 0x0
        Property                  = ...  # 0x1
        SignalProperty            = ...  # 0x2


    @typing.overload
    def __init__(self, arg__1: PySide6.QtCore.QObject, arg__2: PySide6.QtQml.QQmlEngine, /, *, object: PySide6.QtCore.QObject | None = ..., name: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtCore.QObject, arg__2: PySide6.QtQml.QQmlContext, /, *, object: PySide6.QtCore.QObject | None = ..., name: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtCore.QObject, arg__2: str, arg__3: PySide6.QtQml.QQmlEngine, /, *, object: PySide6.QtCore.QObject | None = ..., name: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtCore.QObject, arg__2: str, arg__3: PySide6.QtQml.QQmlContext, /, *, object: PySide6.QtCore.QObject | None = ..., name: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtCore.QObject, arg__2: str, /, *, object: PySide6.QtCore.QObject | None = ..., name: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtCore.QObject, /, *, object: PySide6.QtCore.QObject | None = ..., name: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlProperty, /, *, object: PySide6.QtCore.QObject | None = ..., name: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, object: PySide6.QtCore.QObject | None = ..., name: str | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, arg__1: PySide6.QtQml.QQmlProperty | PySide6.QtCore.QObject, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    @typing.overload
    def connectNotifySignal(self, dest: PySide6.QtCore.QObject, slot: bytes | bytearray | memoryview, /) -> bool: ...
    @typing.overload
    def connectNotifySignal(self, dest: PySide6.QtCore.QObject, method: int, /) -> bool: ...
    def hasNotifySignal(self, /) -> bool: ...
    def index(self, /) -> int: ...
    def isBindable(self, /) -> bool: ...
    def isDesignable(self, /) -> bool: ...
    def isProperty(self, /) -> bool: ...
    def isResettable(self, /) -> bool: ...
    def isSignalProperty(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def isWritable(self, /) -> bool: ...
    def method(self, /) -> PySide6.QtCore.QMetaMethod: ...
    def name(self, /) -> str: ...
    def needsNotifySignal(self, /) -> bool: ...
    def object(self, /) -> PySide6.QtCore.QObject: ...
    def property(self, /) -> PySide6.QtCore.QMetaProperty: ...
    def propertyMetaType(self, /) -> PySide6.QtCore.QMetaType: ...
    def propertyType(self, /) -> int: ...
    def propertyTypeCategory(self, /) -> PySide6.QtQml.QQmlProperty.PropertyTypeCategory: ...
    def propertyTypeName(self, /) -> bytes | bytearray | memoryview: ...
    @typing.overload  # type: ignore[misc, overload-cannot-match]
    @staticmethod
    def read(arg__1: PySide6.QtCore.QObject, arg__2: str, /) -> typing.Any: ...  # type: ignore[misc, overload-cannot-match]
    @typing.overload  # type: ignore[misc, overload-cannot-match]
    @staticmethod
    def read(arg__1: PySide6.QtCore.QObject, arg__2: str, arg__3: PySide6.QtQml.QQmlEngine, /) -> typing.Any: ...  # type: ignore[misc, overload-cannot-match]
    @typing.overload  # type: ignore[misc, overload-cannot-match]
    @staticmethod
    def read(arg__1: PySide6.QtCore.QObject, arg__2: str, arg__3: PySide6.QtQml.QQmlContext, /) -> typing.Any: ...  # type: ignore[misc, overload-cannot-match]
    @typing.overload  # type: ignore[misc, overload-cannot-match]
    def read(self, /) -> typing.Any: ...  # type: ignore[misc, overload-cannot-match]
    def reset(self, /) -> bool: ...
    def swap(self, other: PySide6.QtQml.QQmlProperty | PySide6.QtCore.QObject, /) -> None: ...
    def type(self, /) -> PySide6.QtQml.QQmlProperty.Type: ...
    @typing.overload  # type: ignore[misc, overload-cannot-match]
    @staticmethod
    def write(arg__1: PySide6.QtCore.QObject, arg__2: str, arg__3: typing.Any, /) -> bool: ...  # type: ignore[misc, overload-cannot-match]
    @typing.overload  # type: ignore[misc, overload-cannot-match]
    @staticmethod
    def write(arg__1: PySide6.QtCore.QObject, arg__2: str, arg__3: typing.Any, arg__4: PySide6.QtQml.QQmlEngine, /) -> bool: ...  # type: ignore[misc, overload-cannot-match]
    @typing.overload  # type: ignore[misc, overload-cannot-match]
    @staticmethod
    def write(arg__1: PySide6.QtCore.QObject, arg__2: str, arg__3: typing.Any, arg__4: PySide6.QtQml.QQmlContext, /) -> bool: ...  # type: ignore[misc, overload-cannot-match]
    @typing.overload  # type: ignore[misc, overload-cannot-match]
    def write(self, arg__1: typing.Any, /) -> bool: ...  # type: ignore[misc, overload-cannot-match]


class QQmlPropertyMap(PySide6.QtCore.QObject):

    valueChanged             : typing.ClassVar[Signal] = ... # valueChanged(QString,QVariant)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def clear(self, key: str, /) -> None: ...
    def contains(self, key: str, /) -> bool: ...
    def count(self, /) -> int: ...
    def freeze(self, /) -> None: ...
    @typing.overload
    def insert(self, key: str, value: typing.Any, /) -> None: ...
    @typing.overload
    def insert(self, values: typing.Dict[str, typing.Any], /) -> None: ...
    def isEmpty(self, /) -> bool: ...
    def keys(self, /) -> typing.List[str]: ...
    def size(self, /) -> int: ...
    def updateValue(self, key: str, input: typing.Any, /) -> typing.Any: ...
    def value(self, key: str, /) -> typing.Any: ...


class QQmlPropertyValueSource(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def setTarget(self, arg__1: PySide6.QtQml.QQmlProperty | PySide6.QtCore.QObject, /) -> None: ...


class QQmlScriptString(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtQml.QQmlScriptString, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, arg__1: PySide6.QtQml.QQmlScriptString, /) -> bool: ...
    def __ne__(self, arg__1: PySide6.QtQml.QQmlScriptString, /) -> bool: ...
    def booleanLiteral(self, /) -> typing.Tuple[bool, bool]: ...
    def isEmpty(self, /) -> bool: ...
    def isNullLiteral(self, /) -> bool: ...
    def isUndefinedLiteral(self, /) -> bool: ...
    def numberLiteral(self, /) -> typing.Tuple[float, bool]: ...
    def stringLiteral(self, /) -> str: ...


class QQmlTypesExtensionInterface(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def registerTypes(self, uri: bytes | bytearray | memoryview, /) -> None: ...


class QmlAttached: ...


class QmlExtended: ...


class QmlForeign: ...


class QmlNamedElement: ...


class QmlUncreatable: ...


class VolatileBool:
    def __repr__(self, /) -> str: ...
    def get(self, /) -> bool: ...
    def set(self, a: object, /) -> None: ...


def QmlAnonymous(arg__1: object, /) -> object: ...
def QmlElement(arg__1: object, /) -> object: ...
def QmlSingleton(arg__1: object, /) -> object: ...
def qjsEngine(arg__1: PySide6.QtCore.QObject, /) -> PySide6.QtQml.QJSEngine | None: ...
def qmlAttachedPropertiesObject(type_obj: type, arg__2: PySide6.QtCore.QObject, /, arg__3: bool = ...) -> PySide6.QtCore.QObject: ...
def qmlClearTypeRegistrations() -> None: ...
def qmlContext(arg__1: PySide6.QtCore.QObject, /) -> PySide6.QtQml.QQmlContext | None: ...
def qmlEngine(arg__1: PySide6.QtCore.QObject, /) -> PySide6.QtQml.QQmlEngine | None: ...
def qmlProtectModule(uri: bytes | bytearray | memoryview, majVersion: int, /) -> bool: ...
def qmlRegisterModule(uri: bytes | bytearray | memoryview, versionMajor: int, versionMinor: int, /) -> None: ...
def qmlRegisterSingletonInstance(type_obj: type, uri: str, version_major: int, version_minor: int, qml_name: bytes | bytearray | memoryview, callback: object, /) -> int: ...
@typing.overload
def qmlRegisterSingletonType(uri: str, version_major: int, version_minor: int, qml_name: bytes | bytearray | memoryview, callback: object, /) -> int: ...
@typing.overload
def qmlRegisterSingletonType(type_obj: type, uri: str, version_major: int, version_minor: int, qml_name: bytes | bytearray | memoryview, /) -> int: ...
@typing.overload
def qmlRegisterSingletonType(type_obj: type, uri: str, version_major: int, version_minor: int, qml_name: bytes | bytearray | memoryview, callback: object, /) -> int: ...
@typing.overload
def qmlRegisterSingletonType(url: PySide6.QtCore.QUrl | str, uri: bytes | bytearray | memoryview, versionMajor: int, versionMinor: int, qmlName: bytes | bytearray | memoryview, /) -> int: ...
@typing.overload
def qmlRegisterType(type_obj: type, uri: str, version_major: int, version_minor: int, qml_name: bytes | bytearray | memoryview, /) -> int: ...
@typing.overload
def qmlRegisterType(url: PySide6.QtCore.QUrl | str, uri: bytes | bytearray | memoryview, versionMajor: int, versionMinor: int, qmlName: bytes | bytearray | memoryview, /) -> int: ...
def qmlRegisterUncreatableMetaObject(staticMetaObject: PySide6.QtCore.QMetaObject, uri: bytes | bytearray | memoryview, versionMajor: int, versionMinor: int, qmlName: bytes | bytearray | memoryview, reason: str, /) -> int: ...
def qmlRegisterUncreatableType(type_obj: type, uri: str, version_major: int, version_minor: int, qml_name: bytes | bytearray | memoryview, message: bytes | bytearray | memoryview, /) -> int: ...
def qmlTypeId(uri: bytes | bytearray | memoryview, versionMajor: int, versionMinor: int, qmlName: bytes | bytearray | memoryview, /) -> int: ...


# eof
