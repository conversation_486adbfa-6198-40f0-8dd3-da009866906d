# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtQuick, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtQuick`

import PySide6.QtQuick
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtOpenGL
import PySide6.QtQml

import enum
import typing
import collections
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QIntList: ...


class QQuickAsyncImageProvider(PySide6.QtQuick.QQuickImageProvider):

    def __init__(self, /) -> None: ...

    def requestImageResponse(self, id: str, requestedSize: PySide6.QtCore.QSize, /) -> PySide6.QtQuick.QQuickImageResponse: ...


class QQuickFramebufferObject(PySide6.QtQuick.QQuickItem):

    mirrorVerticallyChanged  : typing.ClassVar[Signal] = ... # mirrorVerticallyChanged(bool)
    textureFollowsItemSizeChanged: typing.ClassVar[Signal] = ... # textureFollowsItemSizeChanged(bool)

    class Renderer(Shiboken.Object):

        def __init__(self, /) -> None: ...

        def createFramebufferObject(self, size: PySide6.QtCore.QSize, /) -> PySide6.QtOpenGL.QOpenGLFramebufferObject: ...
        def framebufferObject(self, /) -> PySide6.QtOpenGL.QOpenGLFramebufferObject: ...
        def invalidateFramebufferObject(self, /) -> None: ...
        def render(self, /) -> None: ...
        def synchronize(self, arg__1: PySide6.QtQuick.QQuickFramebufferObject, /) -> None: ...
        def update(self, /) -> None: ...


    def __init__(self, /, parent: PySide6.QtQuick.QQuickItem | None = ..., *, textureFollowsItemSize: bool | None = ..., mirrorVertically: bool | None = ...) -> None: ...

    def createRenderer(self, /) -> PySide6.QtQuick.QQuickFramebufferObject.Renderer: ...
    def geometryChange(self, newGeometry: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, oldGeometry: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def isTextureProvider(self, /) -> bool: ...
    def mirrorVertically(self, /) -> bool: ...
    def releaseResources(self, /) -> None: ...
    def setMirrorVertically(self, enable: bool, /) -> None: ...
    def setTextureFollowsItemSize(self, follows: bool, /) -> None: ...
    def textureFollowsItemSize(self, /) -> bool: ...
    def textureProvider(self, /) -> PySide6.QtQuick.QSGTextureProvider: ...
    def updatePaintNode(self, arg__1: PySide6.QtQuick.QSGNode, arg__2: PySide6.QtQuick.QQuickItem.UpdatePaintNodeData, /) -> PySide6.QtQuick.QSGNode: ...


class QQuickGraphicsConfiguration(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtQuick.QQuickGraphicsConfiguration, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def deviceExtensions(self, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    def isAutomaticPipelineCacheEnabled(self, /) -> bool: ...
    def isDebugLayerEnabled(self, /) -> bool: ...
    def isDebugMarkersEnabled(self, /) -> bool: ...
    def isDepthBufferEnabledFor2D(self, /) -> bool: ...
    def pipelineCacheLoadFile(self, /) -> str: ...
    def pipelineCacheSaveFile(self, /) -> str: ...
    @staticmethod
    def preferredInstanceExtensions() -> typing.List[PySide6.QtCore.QByteArray]: ...
    def prefersSoftwareDevice(self, /) -> bool: ...
    def setAutomaticPipelineCache(self, enable: bool, /) -> None: ...
    def setDebugLayer(self, enable: bool, /) -> None: ...
    def setDebugMarkers(self, enable: bool, /) -> None: ...
    def setDepthBufferFor2D(self, enable: bool, /) -> None: ...
    def setDeviceExtensions(self, extensions: collections.abc.Sequence[PySide6.QtCore.QByteArray], /) -> None: ...
    def setPipelineCacheLoadFile(self, filename: str, /) -> None: ...
    def setPipelineCacheSaveFile(self, filename: str, /) -> None: ...
    def setPreferSoftwareDevice(self, enable: bool, /) -> None: ...
    def setTimestamps(self, enable: bool, /) -> None: ...
    def timestampsEnabled(self, /) -> bool: ...


class QQuickGraphicsDevice(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtQuick.QQuickGraphicsDevice, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    @staticmethod
    def fromAdapter(adapterLuidLow: int, adapterLuidHigh: int, /, featureLevel: int | None = ...) -> PySide6.QtQuick.QQuickGraphicsDevice: ...
    @staticmethod
    def fromDeviceAndContext(device: int, context: int, /) -> PySide6.QtQuick.QQuickGraphicsDevice: ...
    @staticmethod
    def fromOpenGLContext(context: PySide6.QtGui.QOpenGLContext, /) -> PySide6.QtQuick.QQuickGraphicsDevice: ...
    @staticmethod
    def fromRhi(rhi: PySide6.QtGui.QRhi, /) -> PySide6.QtQuick.QQuickGraphicsDevice: ...
    def isNull(self, /) -> bool: ...


class QQuickImageProvider(PySide6.QtQml.QQmlImageProviderBase):

    def __init__(self, type: PySide6.QtQml.QQmlImageProviderBase.ImageType, /, flags: PySide6.QtQml.QQmlImageProviderBase.Flag = ...) -> None: ...

    def flags(self, /) -> PySide6.QtQml.QQmlImageProviderBase.Flag: ...
    def imageType(self, /) -> PySide6.QtQml.QQmlImageProviderBase.ImageType: ...
    def requestImage(self, id: str, size: PySide6.QtCore.QSize, requestedSize: PySide6.QtCore.QSize, /) -> PySide6.QtGui.QImage: ...
    def requestPixmap(self, id: str, size: PySide6.QtCore.QSize, requestedSize: PySide6.QtCore.QSize, /) -> PySide6.QtGui.QPixmap: ...
    def requestTexture(self, id: str, size: PySide6.QtCore.QSize, requestedSize: PySide6.QtCore.QSize, /) -> PySide6.QtQuick.QQuickTextureFactory: ...


class QQuickImageResponse(PySide6.QtCore.QObject):

    finished                 : typing.ClassVar[Signal] = ... # finished()

    def __init__(self, /) -> None: ...

    def cancel(self, /) -> None: ...
    def errorString(self, /) -> str: ...
    def textureFactory(self, /) -> PySide6.QtQuick.QQuickTextureFactory: ...


class QQuickItem(PySide6.QtCore.QObject, PySide6.QtQml.QQmlParserStatus):

    activeFocusChanged       : typing.ClassVar[Signal] = ... # activeFocusChanged(bool)
    activeFocusOnTabChanged  : typing.ClassVar[Signal] = ... # activeFocusOnTabChanged(bool)
    antialiasingChanged      : typing.ClassVar[Signal] = ... # antialiasingChanged(bool)
    baselineOffsetChanged    : typing.ClassVar[Signal] = ... # baselineOffsetChanged(double)
    childrenChanged          : typing.ClassVar[Signal] = ... # childrenChanged()
    childrenRectChanged      : typing.ClassVar[Signal] = ... # childrenRectChanged(QRectF)
    clipChanged              : typing.ClassVar[Signal] = ... # clipChanged(bool)
    containmentMaskChanged   : typing.ClassVar[Signal] = ... # containmentMaskChanged()
    enabledChanged           : typing.ClassVar[Signal] = ... # enabledChanged()
    focusChanged             : typing.ClassVar[Signal] = ... # focusChanged(bool)
    focusPolicyChanged       : typing.ClassVar[Signal] = ... # focusPolicyChanged(Qt::FocusPolicy)
    heightChanged            : typing.ClassVar[Signal] = ... # heightChanged()
    implicitHeightChanged    : typing.ClassVar[Signal] = ... # implicitHeightChanged()
    implicitWidthChanged     : typing.ClassVar[Signal] = ... # implicitWidthChanged()
    opacityChanged           : typing.ClassVar[Signal] = ... # opacityChanged()
    paletteChanged           : typing.ClassVar[Signal] = ... # paletteChanged()
    paletteCreated           : typing.ClassVar[Signal] = ... # paletteCreated()
    parentChanged            : typing.ClassVar[Signal] = ... # parentChanged(QQuickItem*)
    rotationChanged          : typing.ClassVar[Signal] = ... # rotationChanged()
    scaleChanged             : typing.ClassVar[Signal] = ... # scaleChanged()
    smoothChanged            : typing.ClassVar[Signal] = ... # smoothChanged(bool)
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QString)
    transformOriginChanged   : typing.ClassVar[Signal] = ... # transformOriginChanged(TransformOrigin)
    visibleChanged           : typing.ClassVar[Signal] = ... # visibleChanged()
    visibleChildrenChanged   : typing.ClassVar[Signal] = ... # visibleChildrenChanged()
    widthChanged             : typing.ClassVar[Signal] = ... # widthChanged()
    windowChanged            : typing.ClassVar[Signal] = ... # windowChanged(QQuickWindow*)
    xChanged                 : typing.ClassVar[Signal] = ... # xChanged()
    yChanged                 : typing.ClassVar[Signal] = ... # yChanged()
    zChanged                 : typing.ClassVar[Signal] = ... # zChanged()

    class Flag(enum.Flag):

        ItemClipsChildrenToShape  = ...  # 0x1
        ItemAcceptsInputMethod    = ...  # 0x2
        ItemIsFocusScope          = ...  # 0x4
        ItemHasContents           = ...  # 0x8
        ItemAcceptsDrops          = ...  # 0x10
        ItemIsViewport            = ...  # 0x20
        ItemObservesViewport      = ...  # 0x40

    class ItemChange(enum.Enum):

        ItemChildAddedChange      = ...  # 0x0
        ItemChildRemovedChange    = ...  # 0x1
        ItemSceneChange           = ...  # 0x2
        ItemVisibleHasChanged     = ...  # 0x3
        ItemParentHasChanged      = ...  # 0x4
        ItemOpacityHasChanged     = ...  # 0x5
        ItemActiveFocusHasChanged = ...  # 0x6
        ItemRotationHasChanged    = ...  # 0x7
        ItemAntialiasingHasChanged = ...  # 0x8
        ItemDevicePixelRatioHasChanged = ...  # 0x9
        ItemEnabledHasChanged     = ...  # 0xa
        ItemScaleHasChanged       = ...  # 0xb
        ItemTransformHasChanged   = ...  # 0xc

    class TransformOrigin(enum.Enum):

        TopLeft                   = ...  # 0x0
        Top                       = ...  # 0x1
        TopRight                  = ...  # 0x2
        Left                      = ...  # 0x3
        Center                    = ...  # 0x4
        Right                     = ...  # 0x5
        BottomLeft                = ...  # 0x6
        Bottom                    = ...  # 0x7
        BottomRight               = ...  # 0x8

    class UpdatePaintNodeData(Shiboken.Object):

        def __init__(self, UpdatePaintNodeData: PySide6.QtQuick.QQuickItem.UpdatePaintNodeData, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...


    def __init__(self, /, parent: PySide6.QtQuick.QQuickItem | None = ..., *, x: float | None = ..., y: float | None = ..., z: float | None = ..., width: float | None = ..., height: float | None = ..., opacity: float | None = ..., enabled: bool | None = ..., visible: bool | None = ..., state: str | None = ..., childrenRect: PySide6.QtCore.QRectF | None = ..., baselineOffset: float | None = ..., clip: bool | None = ..., focus: bool | None = ..., activeFocus: bool | None = ..., activeFocusOnTab: bool | None = ..., focusPolicy: PySide6.QtCore.Qt.FocusPolicy | None = ..., rotation: float | None = ..., scale: float | None = ..., transformOrigin: PySide6.QtQuick.QQuickItem.TransformOrigin | None = ..., transformOriginPoint: PySide6.QtCore.QPointF | None = ..., smooth: bool | None = ..., antialiasing: bool | None = ..., implicitWidth: float | None = ..., implicitHeight: float | None = ..., containmentMask: PySide6.QtCore.QObject | None = ...) -> None: ...

    def __repr__(self, /) -> str: ...
    def acceptHoverEvents(self, /) -> bool: ...
    def acceptTouchEvents(self, /) -> bool: ...
    def acceptedMouseButtons(self, /) -> PySide6.QtCore.Qt.MouseButton: ...
    def activeFocusOnTab(self, /) -> bool: ...
    def antialiasing(self, /) -> bool: ...
    def baselineOffset(self, /) -> float: ...
    def boundingRect(self, /) -> PySide6.QtCore.QRectF: ...
    def childAt(self, x: float, y: float, /) -> PySide6.QtQuick.QQuickItem: ...
    def childItems(self, /) -> typing.List[PySide6.QtQuick.QQuickItem]: ...
    def childMouseEventFilter(self, arg__1: PySide6.QtQuick.QQuickItem, arg__2: PySide6.QtCore.QEvent, /) -> bool: ...
    def childrenRect(self, /) -> PySide6.QtCore.QRectF: ...
    def classBegin(self, /) -> None: ...
    def clip(self, /) -> bool: ...
    def clipRect(self, /) -> PySide6.QtCore.QRectF: ...
    def componentComplete(self, /) -> None: ...
    def containmentMask(self, /) -> PySide6.QtCore.QObject: ...
    def contains(self, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> bool: ...
    def cursor(self, /) -> PySide6.QtGui.QCursor: ...
    def dragEnterEvent(self, arg__1: PySide6.QtGui.QDragEnterEvent, /) -> None: ...
    def dragLeaveEvent(self, arg__1: PySide6.QtGui.QDragLeaveEvent, /) -> None: ...
    def dragMoveEvent(self, arg__1: PySide6.QtGui.QDragMoveEvent, /) -> None: ...
    def dropEvent(self, arg__1: PySide6.QtGui.QDropEvent, /) -> None: ...
    def dumpItemTree(self, /) -> None: ...
    def ensurePolished(self, /) -> None: ...
    def event(self, arg__1: PySide6.QtCore.QEvent, /) -> bool: ...
    def filtersChildMouseEvents(self, /) -> bool: ...
    def flags(self, /) -> PySide6.QtQuick.QQuickItem.Flag: ...
    def focusInEvent(self, arg__1: PySide6.QtGui.QFocusEvent, /) -> None: ...
    def focusOutEvent(self, arg__1: PySide6.QtGui.QFocusEvent, /) -> None: ...
    def focusPolicy(self, /) -> PySide6.QtCore.Qt.FocusPolicy: ...
    @typing.overload
    def forceActiveFocus(self, /) -> None: ...
    @typing.overload
    def forceActiveFocus(self, reason: PySide6.QtCore.Qt.FocusReason, /) -> None: ...
    def geometryChange(self, newGeometry: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, oldGeometry: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def grabMouse(self, /) -> None: ...
    @typing.overload
    def grabToImage(self, /, targetSize: PySide6.QtCore.QSize = ...) -> PySide6.QtQuick.QSharedPointer_QQuickItemGrabResult: ...
    @typing.overload
    def grabToImage(self, callback: PySide6.QtQml.QJSValue | PySide6.QtQml.QJSValue.SpecialValue | bool | str | bytes | bytearray | memoryview | float | int, /, targetSize: PySide6.QtCore.QSize = ...) -> bool: ...
    def grabTouchPoints(self, ids: collections.abc.Sequence[int], /) -> None: ...
    def hasActiveFocus(self, /) -> bool: ...
    def hasFocus(self, /) -> bool: ...
    def height(self, /) -> float: ...
    def heightValid(self, /) -> bool: ...
    def hoverEnterEvent(self, event: PySide6.QtGui.QHoverEvent, /) -> None: ...
    def hoverLeaveEvent(self, event: PySide6.QtGui.QHoverEvent, /) -> None: ...
    def hoverMoveEvent(self, event: PySide6.QtGui.QHoverEvent, /) -> None: ...
    def implicitHeight(self, /) -> float: ...
    def implicitWidth(self, /) -> float: ...
    def inputMethodEvent(self, arg__1: PySide6.QtGui.QInputMethodEvent, /) -> None: ...
    def inputMethodQuery(self, query: PySide6.QtCore.Qt.InputMethodQuery, /) -> typing.Any: ...
    def isAncestorOf(self, child: PySide6.QtQuick.QQuickItem, /) -> bool: ...
    def isComponentComplete(self, /) -> bool: ...
    def isEnabled(self, /) -> bool: ...
    def isFocusScope(self, /) -> bool: ...
    def isTextureProvider(self, /) -> bool: ...
    def isUnderMouse(self, /) -> bool: ...
    def isVisible(self, /) -> bool: ...
    def itemTransform(self, arg__1: PySide6.QtQuick.QQuickItem, /) -> typing.Tuple[bool, PySide6.QtGui.QTransform]: ...
    def keepMouseGrab(self, /) -> bool: ...
    def keepTouchGrab(self, /) -> bool: ...
    def keyPressEvent(self, event: PySide6.QtGui.QKeyEvent, /) -> None: ...
    def keyReleaseEvent(self, event: PySide6.QtGui.QKeyEvent, /) -> None: ...
    @typing.overload
    def mapFromGlobal(self, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> PySide6.QtCore.QPointF: ...
    @typing.overload
    def mapFromGlobal(self, x: float, y: float, /) -> PySide6.QtCore.QPointF: ...
    @typing.overload
    def mapFromItem(self, item: PySide6.QtQuick.QQuickItem, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> PySide6.QtCore.QRectF: ...
    @typing.overload
    def mapFromItem(self, item: PySide6.QtQuick.QQuickItem, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> PySide6.QtCore.QPointF: ...
    @typing.overload
    def mapFromItem(self, item: PySide6.QtQuick.QQuickItem, x: float, y: float, /) -> PySide6.QtCore.QPointF: ...
    @typing.overload
    def mapFromItem(self, item: PySide6.QtQuick.QQuickItem, x: float, y: float, width: float, height: float, /) -> PySide6.QtCore.QRectF: ...
    def mapFromScene(self, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> PySide6.QtCore.QPointF: ...
    def mapRectFromItem(self, item: PySide6.QtQuick.QQuickItem, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> PySide6.QtCore.QRectF: ...
    def mapRectFromScene(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> PySide6.QtCore.QRectF: ...
    def mapRectToItem(self, item: PySide6.QtQuick.QQuickItem, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> PySide6.QtCore.QRectF: ...
    def mapRectToScene(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> PySide6.QtCore.QRectF: ...
    @typing.overload
    def mapToGlobal(self, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> PySide6.QtCore.QPointF: ...
    @typing.overload
    def mapToGlobal(self, x: float, y: float, /) -> PySide6.QtCore.QPointF: ...
    @typing.overload
    def mapToItem(self, item: PySide6.QtQuick.QQuickItem, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> PySide6.QtCore.QRectF: ...
    @typing.overload
    def mapToItem(self, item: PySide6.QtQuick.QQuickItem, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> PySide6.QtCore.QPointF: ...
    @typing.overload
    def mapToItem(self, item: PySide6.QtQuick.QQuickItem, x: float, y: float, /) -> PySide6.QtCore.QPointF: ...
    @typing.overload
    def mapToItem(self, item: PySide6.QtQuick.QQuickItem, x: float, y: float, width: float, height: float, /) -> PySide6.QtCore.QRectF: ...
    def mapToScene(self, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> PySide6.QtCore.QPointF: ...
    def mouseDoubleClickEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseMoveEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseReleaseEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseUngrabEvent(self, /) -> None: ...
    def nextItemInFocusChain(self, /, forward: bool = ...) -> PySide6.QtQuick.QQuickItem: ...
    def opacity(self, /) -> float: ...
    def parentItem(self, /) -> PySide6.QtQuick.QQuickItem: ...
    def polish(self, /) -> None: ...
    def position(self, /) -> PySide6.QtCore.QPointF: ...
    def releaseResources(self, /) -> None: ...
    def resetAntialiasing(self, /) -> None: ...
    def resetHeight(self, /) -> None: ...
    def resetWidth(self, /) -> None: ...
    def rotation(self, /) -> float: ...
    def scale(self, /) -> float: ...
    def scopedFocusItem(self, /) -> PySide6.QtQuick.QQuickItem: ...
    def setAcceptHoverEvents(self, enabled: bool, /) -> None: ...
    def setAcceptTouchEvents(self, accept: bool, /) -> None: ...
    def setAcceptedMouseButtons(self, buttons: PySide6.QtCore.Qt.MouseButton, /) -> None: ...
    def setActiveFocusOnTab(self, arg__1: bool, /) -> None: ...
    def setAntialiasing(self, arg__1: bool, /) -> None: ...
    def setBaselineOffset(self, arg__1: float, /) -> None: ...
    def setClip(self, arg__1: bool, /) -> None: ...
    def setContainmentMask(self, mask: PySide6.QtCore.QObject, /) -> None: ...
    def setCursor(self, cursor: PySide6.QtGui.QCursor | PySide6.QtCore.Qt.CursorShape | PySide6.QtGui.QPixmap, /) -> None: ...
    def setEnabled(self, arg__1: bool, /) -> None: ...
    def setFiltersChildMouseEvents(self, filter: bool, /) -> None: ...
    def setFlag(self, flag: PySide6.QtQuick.QQuickItem.Flag, /, enabled: bool = ...) -> None: ...
    def setFlags(self, flags: PySide6.QtQuick.QQuickItem.Flag, /) -> None: ...
    @typing.overload
    def setFocus(self, arg__1: bool, /) -> None: ...
    @typing.overload
    def setFocus(self, focus: bool, reason: PySide6.QtCore.Qt.FocusReason, /) -> None: ...
    def setFocusPolicy(self, policy: PySide6.QtCore.Qt.FocusPolicy, /) -> None: ...
    def setHeight(self, arg__1: float, /) -> None: ...
    def setImplicitHeight(self, arg__1: float, /) -> None: ...
    def setImplicitSize(self, arg__1: float, arg__2: float, /) -> None: ...
    def setImplicitWidth(self, arg__1: float, /) -> None: ...
    def setKeepMouseGrab(self, arg__1: bool, /) -> None: ...
    def setKeepTouchGrab(self, arg__1: bool, /) -> None: ...
    def setOpacity(self, arg__1: float, /) -> None: ...
    def setParentItem(self, parent: PySide6.QtQuick.QQuickItem, /) -> None: ...
    def setPosition(self, arg__1: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> None: ...
    def setRotation(self, arg__1: float, /) -> None: ...
    def setScale(self, arg__1: float, /) -> None: ...
    def setSize(self, size: PySide6.QtCore.QSizeF | PySide6.QtCore.QSize, /) -> None: ...
    def setSmooth(self, arg__1: bool, /) -> None: ...
    def setState(self, arg__1: str, /) -> None: ...
    def setTransformOrigin(self, arg__1: PySide6.QtQuick.QQuickItem.TransformOrigin, /) -> None: ...
    def setTransformOriginPoint(self, arg__1: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> None: ...
    def setVisible(self, arg__1: bool, /) -> None: ...
    def setWidth(self, arg__1: float, /) -> None: ...
    def setX(self, arg__1: float, /) -> None: ...
    def setY(self, arg__1: float, /) -> None: ...
    def setZ(self, arg__1: float, /) -> None: ...
    def size(self, /) -> PySide6.QtCore.QSizeF: ...
    def smooth(self, /) -> bool: ...
    def stackAfter(self, arg__1: PySide6.QtQuick.QQuickItem, /) -> None: ...
    def stackBefore(self, arg__1: PySide6.QtQuick.QQuickItem, /) -> None: ...
    def state(self, /) -> str: ...
    def textureProvider(self, /) -> PySide6.QtQuick.QSGTextureProvider: ...
    def touchEvent(self, event: PySide6.QtGui.QTouchEvent, /) -> None: ...
    def touchUngrabEvent(self, /) -> None: ...
    def transformOrigin(self, /) -> PySide6.QtQuick.QQuickItem.TransformOrigin: ...
    def transformOriginPoint(self, /) -> PySide6.QtCore.QPointF: ...
    def ungrabMouse(self, /) -> None: ...
    def ungrabTouchPoints(self, /) -> None: ...
    def unsetCursor(self, /) -> None: ...
    def update(self, /) -> None: ...
    def updateInputMethod(self, /, queries: PySide6.QtCore.Qt.InputMethodQuery = ...) -> None: ...
    def updatePaintNode(self, arg__1: PySide6.QtQuick.QSGNode, arg__2: PySide6.QtQuick.QQuickItem.UpdatePaintNodeData, /) -> PySide6.QtQuick.QSGNode: ...
    def updatePolish(self, /) -> None: ...
    def viewportItem(self, /) -> PySide6.QtQuick.QQuickItem: ...
    def wheelEvent(self, event: PySide6.QtGui.QWheelEvent, /) -> None: ...
    def width(self, /) -> float: ...
    def widthValid(self, /) -> bool: ...
    def window(self, /) -> PySide6.QtQuick.QQuickWindow: ...
    def x(self, /) -> float: ...
    def y(self, /) -> float: ...
    def z(self, /) -> float: ...


class QQuickItemGrabResult(PySide6.QtCore.QObject):

    ready                    : typing.ClassVar[Signal] = ... # ready()
    def event(self, arg__1: PySide6.QtCore.QEvent, /) -> bool: ...
    def image(self, /) -> PySide6.QtGui.QImage: ...
    @typing.overload
    def saveToFile(self, fileName: str, /) -> bool: ...
    @typing.overload
    def saveToFile(self, fileName: PySide6.QtCore.QUrl | str, /) -> bool: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QQuickOpenGLUtils(Shiboken.Object):
    @staticmethod
    def resetOpenGLState() -> None: ...


class QQuickPaintedItem(PySide6.QtQuick.QQuickItem):

    contentsScaleChanged     : typing.ClassVar[Signal] = ... # contentsScaleChanged()
    contentsSizeChanged      : typing.ClassVar[Signal] = ... # contentsSizeChanged()
    fillColorChanged         : typing.ClassVar[Signal] = ... # fillColorChanged()
    renderTargetChanged      : typing.ClassVar[Signal] = ... # renderTargetChanged()
    textureSizeChanged       : typing.ClassVar[Signal] = ... # textureSizeChanged()

    class PerformanceHint(enum.Flag):

        FastFBOResizing           = ...  # 0x1

    class RenderTarget(enum.Enum):

        Image                     = ...  # 0x0
        FramebufferObject         = ...  # 0x1
        InvertedYFramebufferObject = ...  # 0x2


    def __init__(self, /, parent: PySide6.QtQuick.QQuickItem | None = ..., *, contentsSize: PySide6.QtCore.QSize | None = ..., fillColor: PySide6.QtGui.QColor | None = ..., contentsScale: float | None = ..., renderTarget: PySide6.QtQuick.QQuickPaintedItem.RenderTarget | None = ..., textureSize: PySide6.QtCore.QSize | None = ...) -> None: ...

    def antialiasing(self, /) -> bool: ...
    def contentsBoundingRect(self, /) -> PySide6.QtCore.QRectF: ...
    def contentsScale(self, /) -> float: ...
    def contentsSize(self, /) -> PySide6.QtCore.QSize: ...
    def fillColor(self, /) -> PySide6.QtGui.QColor: ...
    def isTextureProvider(self, /) -> bool: ...
    def mipmap(self, /) -> bool: ...
    def opaquePainting(self, /) -> bool: ...
    def paint(self, painter: PySide6.QtGui.QPainter, /) -> None: ...
    def performanceHints(self, /) -> PySide6.QtQuick.QQuickPaintedItem.PerformanceHint: ...
    def releaseResources(self, /) -> None: ...
    def renderTarget(self, /) -> PySide6.QtQuick.QQuickPaintedItem.RenderTarget: ...
    def resetContentsSize(self, /) -> None: ...
    def setAntialiasing(self, enable: bool, /) -> None: ...
    def setContentsScale(self, arg__1: float, /) -> None: ...
    def setContentsSize(self, arg__1: PySide6.QtCore.QSize, /) -> None: ...
    def setFillColor(self, arg__1: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setMipmap(self, enable: bool, /) -> None: ...
    def setOpaquePainting(self, opaque: bool, /) -> None: ...
    def setPerformanceHint(self, hint: PySide6.QtQuick.QQuickPaintedItem.PerformanceHint, /, enabled: bool = ...) -> None: ...
    def setPerformanceHints(self, hints: PySide6.QtQuick.QQuickPaintedItem.PerformanceHint, /) -> None: ...
    def setRenderTarget(self, target: PySide6.QtQuick.QQuickPaintedItem.RenderTarget, /) -> None: ...
    def setTextureSize(self, size: PySide6.QtCore.QSize, /) -> None: ...
    def textureProvider(self, /) -> PySide6.QtQuick.QSGTextureProvider: ...
    def textureSize(self, /) -> PySide6.QtCore.QSize: ...
    def update(self, /, rect: PySide6.QtCore.QRect = ...) -> None: ...
    def updatePaintNode(self, arg__1: PySide6.QtQuick.QSGNode, arg__2: PySide6.QtQuick.QQuickItem.UpdatePaintNodeData, /) -> PySide6.QtQuick.QSGNode: ...


class QQuickRenderControl(PySide6.QtCore.QObject):

    renderRequested          : typing.ClassVar[Signal] = ... # renderRequested()
    sceneChanged             : typing.ClassVar[Signal] = ... # sceneChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def beginFrame(self, /) -> None: ...
    def commandBuffer(self, /) -> PySide6.QtGui.QRhiCommandBuffer: ...
    def endFrame(self, /) -> None: ...
    def initialize(self, /) -> bool: ...
    def invalidate(self, /) -> None: ...
    def polishItems(self, /) -> None: ...
    def prepareThread(self, targetThread: PySide6.QtCore.QThread, /) -> None: ...
    def render(self, /) -> None: ...
    def renderWindow(self, offset: PySide6.QtCore.QPoint, /) -> PySide6.QtGui.QWindow: ...
    @staticmethod
    def renderWindowFor(win: PySide6.QtQuick.QQuickWindow, /, offset: PySide6.QtCore.QPoint | None = ...) -> PySide6.QtGui.QWindow: ...
    def rhi(self, /) -> PySide6.QtGui.QRhi: ...
    def samples(self, /) -> int: ...
    def setSamples(self, sampleCount: int, /) -> None: ...
    def sync(self, /) -> bool: ...
    def window(self, /) -> PySide6.QtQuick.QQuickWindow: ...


class QQuickRenderTarget(Shiboken.Object):

    class Flag(enum.Flag):

        MultisampleResolve        = ...  # 0x1


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtQuick.QQuickRenderTarget, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtQuick.QQuickRenderTarget, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtQuick.QQuickRenderTarget, /) -> bool: ...
    def depthTexture(self, /) -> PySide6.QtGui.QRhiTexture: ...
    def devicePixelRatio(self, /) -> float: ...
    @typing.overload
    @staticmethod
    def fromD3D11Texture(texture: int, pixelSize: PySide6.QtCore.QSize, /, sampleCount: int = ...) -> PySide6.QtQuick.QQuickRenderTarget: ...
    @typing.overload
    @staticmethod
    def fromD3D11Texture(texture: int, format: int, pixelSize: PySide6.QtCore.QSize, /, sampleCount: int = ...) -> PySide6.QtQuick.QQuickRenderTarget: ...
    @typing.overload
    @staticmethod
    def fromD3D11Texture(texture: int, format: int, pixelSize: PySide6.QtCore.QSize, sampleCount: int, flags: PySide6.QtQuick.QQuickRenderTarget.Flag, /) -> PySide6.QtQuick.QQuickRenderTarget: ...
    @typing.overload
    @staticmethod
    def fromD3D12Texture(texture: int, resourceState: int, format: int, pixelSize: PySide6.QtCore.QSize, /, sampleCount: int = ...) -> PySide6.QtQuick.QQuickRenderTarget: ...
    @typing.overload
    @staticmethod
    def fromD3D12Texture(texture: int, resourceState: int, format: int, viewFormat: int, pixelSize: PySide6.QtCore.QSize, sampleCount: int, arraySize: int, flags: PySide6.QtQuick.QQuickRenderTarget.Flag, /) -> PySide6.QtQuick.QQuickRenderTarget: ...
    @staticmethod
    def fromOpenGLRenderBuffer(renderbufferId: int, pixelSize: PySide6.QtCore.QSize, /, sampleCount: int = ...) -> PySide6.QtQuick.QQuickRenderTarget: ...
    @typing.overload
    @staticmethod
    def fromOpenGLTexture(textureId: int, pixelSize: PySide6.QtCore.QSize, /, sampleCount: int = ...) -> PySide6.QtQuick.QQuickRenderTarget: ...
    @typing.overload
    @staticmethod
    def fromOpenGLTexture(textureId: int, format: int, pixelSize: PySide6.QtCore.QSize, /, sampleCount: int = ...) -> PySide6.QtQuick.QQuickRenderTarget: ...
    @typing.overload
    @staticmethod
    def fromOpenGLTexture(textureId: int, format: int, pixelSize: PySide6.QtCore.QSize, sampleCount: int, arraySize: int, flags: PySide6.QtQuick.QQuickRenderTarget.Flag, /) -> PySide6.QtQuick.QQuickRenderTarget: ...
    @staticmethod
    def fromPaintDevice(device: PySide6.QtGui.QPaintDevice, /) -> PySide6.QtQuick.QQuickRenderTarget: ...
    @staticmethod
    def fromRhiRenderTarget(renderTarget: PySide6.QtGui.QRhiRenderTarget, /) -> PySide6.QtQuick.QQuickRenderTarget: ...
    def isNull(self, /) -> bool: ...
    def mirrorVertically(self, /) -> bool: ...
    def setDepthTexture(self, texture: PySide6.QtGui.QRhiTexture, /) -> None: ...
    def setDevicePixelRatio(self, ratio: float, /) -> None: ...
    def setMirrorVertically(self, enable: bool, /) -> None: ...


class QQuickRhiItem(PySide6.QtQuick.QQuickItem):

    alphaBlendingChanged     : typing.ClassVar[Signal] = ... # alphaBlendingChanged()
    autoRenderTargetChanged  : typing.ClassVar[Signal] = ... # autoRenderTargetChanged()
    colorBufferFormatChanged : typing.ClassVar[Signal] = ... # colorBufferFormatChanged()
    effectiveColorBufferSizeChanged: typing.ClassVar[Signal] = ... # effectiveColorBufferSizeChanged()
    fixedColorBufferHeightChanged: typing.ClassVar[Signal] = ... # fixedColorBufferHeightChanged()
    fixedColorBufferWidthChanged: typing.ClassVar[Signal] = ... # fixedColorBufferWidthChanged()
    mirrorVerticallyChanged  : typing.ClassVar[Signal] = ... # mirrorVerticallyChanged()
    sampleCountChanged       : typing.ClassVar[Signal] = ... # sampleCountChanged()

    class TextureFormat(enum.Enum):

        RGBA8                     = ...  # 0x0
        RGBA16F                   = ...  # 0x1
        RGBA32F                   = ...  # 0x2
        RGB10A2                   = ...  # 0x3


    def __init__(self, /, parent: PySide6.QtQuick.QQuickItem | None = ..., *, sampleCount: int | None = ..., colorBufferFormat: PySide6.QtQuick.QQuickRhiItem.TextureFormat | None = ..., mirrorVertically: bool | None = ..., alphaBlending: bool | None = ..., fixedColorBufferWidth: int | None = ..., fixedColorBufferHeight: int | None = ..., effectiveColorBufferSize: PySide6.QtCore.QSize | None = ...) -> None: ...

    def alphaBlending(self, /) -> bool: ...
    def colorBufferFormat(self, /) -> PySide6.QtQuick.QQuickRhiItem.TextureFormat: ...
    def createRenderer(self, /) -> PySide6.QtQuick.QQuickRhiItemRenderer: ...
    def effectiveColorBufferSize(self, /) -> PySide6.QtCore.QSize: ...
    def event(self, arg__1: PySide6.QtCore.QEvent, /) -> bool: ...
    def fixedColorBufferHeight(self, /) -> int: ...
    def fixedColorBufferWidth(self, /) -> int: ...
    def geometryChange(self, newGeometry: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, oldGeometry: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def isAutoRenderTargetEnabled(self, /) -> bool: ...
    def isMirrorVerticallyEnabled(self, /) -> bool: ...
    def isTextureProvider(self, /) -> bool: ...
    def releaseResources(self, /) -> None: ...
    def sampleCount(self, /) -> int: ...
    def setAlphaBlending(self, enable: bool, /) -> None: ...
    def setAutoRenderTarget(self, enabled: bool, /) -> None: ...
    def setColorBufferFormat(self, format: PySide6.QtQuick.QQuickRhiItem.TextureFormat, /) -> None: ...
    def setFixedColorBufferHeight(self, height: int, /) -> None: ...
    def setFixedColorBufferWidth(self, width: int, /) -> None: ...
    def setMirrorVertically(self, enable: bool, /) -> None: ...
    def setSampleCount(self, samples: int, /) -> None: ...
    def textureProvider(self, /) -> PySide6.QtQuick.QSGTextureProvider: ...
    def updatePaintNode(self, arg__1: PySide6.QtQuick.QSGNode, arg__2: PySide6.QtQuick.QQuickItem.UpdatePaintNodeData, /) -> PySide6.QtQuick.QSGNode: ...


class QQuickRhiItemRenderer(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def colorTexture(self, /) -> PySide6.QtGui.QRhiTexture: ...
    def depthStencilBuffer(self, /) -> PySide6.QtGui.QRhiRenderBuffer: ...
    def initialize(self, cb: PySide6.QtGui.QRhiCommandBuffer, /) -> None: ...
    def msaaColorBuffer(self, /) -> PySide6.QtGui.QRhiRenderBuffer: ...
    def render(self, cb: PySide6.QtGui.QRhiCommandBuffer, /) -> None: ...
    def renderTarget(self, /) -> PySide6.QtGui.QRhiRenderTarget: ...
    def resolveTexture(self, /) -> PySide6.QtGui.QRhiTexture: ...
    def rhi(self, /) -> PySide6.QtGui.QRhi: ...
    def synchronize(self, item: PySide6.QtQuick.QQuickRhiItem, /) -> None: ...
    def update(self, /) -> None: ...


class QQuickTextDocument(PySide6.QtCore.QObject):

    errorStringChanged       : typing.ClassVar[Signal] = ... # errorStringChanged()
    modifiedChanged          : typing.ClassVar[Signal] = ... # modifiedChanged()
    sourceChanged            : typing.ClassVar[Signal] = ... # sourceChanged()
    statusChanged            : typing.ClassVar[Signal] = ... # statusChanged()
    textDocumentChanged      : typing.ClassVar[Signal] = ... # textDocumentChanged()

    class Status(enum.Enum):

        Null                      = ...  # 0x0
        Loading                   = ...  # 0x1
        Loaded                    = ...  # 0x2
        Saving                    = ...  # 0x3
        Saved                     = ...  # 0x4
        ReadError                 = ...  # 0x5
        WriteError                = ...  # 0x6
        NonLocalFileError         = ...  # 0x7


    def __init__(self, parent: PySide6.QtQuick.QQuickItem, /, *, source: PySide6.QtCore.QUrl | None = ..., modified: bool | None = ..., status: PySide6.QtQuick.QQuickTextDocument.Status | None = ..., errorString: str | None = ...) -> None: ...

    def errorString(self, /) -> str: ...
    def isModified(self, /) -> bool: ...
    def save(self, /) -> None: ...
    def saveAs(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setModified(self, modified: bool, /) -> None: ...
    def setSource(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setTextDocument(self, document: PySide6.QtGui.QTextDocument, /) -> None: ...
    def source(self, /) -> PySide6.QtCore.QUrl: ...
    def status(self, /) -> PySide6.QtQuick.QQuickTextDocument.Status: ...
    def textDocument(self, /) -> PySide6.QtGui.QTextDocument: ...


class QQuickTextureFactory(PySide6.QtCore.QObject):

    def __init__(self, /) -> None: ...

    def createTexture(self, window: PySide6.QtQuick.QQuickWindow, /) -> PySide6.QtQuick.QSGTexture: ...
    def image(self, /) -> PySide6.QtGui.QImage: ...
    def textureByteCount(self, /) -> int: ...
    @staticmethod
    def textureFactoryForImage(image: PySide6.QtGui.QImage, /) -> PySide6.QtQuick.QQuickTextureFactory: ...
    def textureSize(self, /) -> PySide6.QtCore.QSize: ...


class QQuickTransform(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def appendToItem(self, arg__1: PySide6.QtQuick.QQuickItem, /) -> None: ...
    def applyTo(self, matrix: PySide6.QtGui.QMatrix4x4 | PySide6.QtGui.QTransform, /) -> None: ...
    def prependToItem(self, arg__1: PySide6.QtQuick.QQuickItem, /) -> None: ...
    def update(self, /) -> None: ...


class QQuickView(PySide6.QtQuick.QQuickWindow):

    statusChanged            : typing.ClassVar[Signal] = ... # statusChanged(QQuickView::Status)

    class ResizeMode(enum.Enum):

        SizeViewToRootObject      = ...  # 0x0
        SizeRootObjectToView      = ...  # 0x1

    class Status(enum.Enum):

        Null                      = ...  # 0x0
        Ready                     = ...  # 0x1
        Loading                   = ...  # 0x2
        Error                     = ...  # 0x3


    @typing.overload
    def __init__(self, engine: PySide6.QtQml.QQmlEngine, parent: PySide6.QtGui.QWindow, /, *, resizeMode: PySide6.QtQuick.QQuickView.ResizeMode | None = ..., status: PySide6.QtQuick.QQuickView.Status | None = ..., source: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, source: PySide6.QtCore.QUrl, renderControl: PySide6.QtQuick.QQuickRenderControl, /, *, resizeMode: PySide6.QtQuick.QQuickView.ResizeMode | None = ..., status: PySide6.QtQuick.QQuickView.Status | None = ...) -> None: ...
    @typing.overload
    def __init__(self, source: PySide6.QtCore.QUrl, /, parent: PySide6.QtGui.QWindow | None = ..., *, resizeMode: PySide6.QtQuick.QQuickView.ResizeMode | None = ..., status: PySide6.QtQuick.QQuickView.Status | None = ...) -> None: ...
    @typing.overload
    def __init__(self, uri: str, typeName: str, /, parent: PySide6.QtGui.QWindow | None = ..., *, resizeMode: PySide6.QtQuick.QQuickView.ResizeMode | None = ..., status: PySide6.QtQuick.QQuickView.Status | None = ..., source: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtGui.QWindow | None = ..., *, resizeMode: PySide6.QtQuick.QQuickView.ResizeMode | None = ..., status: PySide6.QtQuick.QQuickView.Status | None = ..., source: PySide6.QtCore.QUrl | None = ...) -> None: ...

    def engine(self, /) -> PySide6.QtQml.QQmlEngine: ...
    def errors(self, /) -> typing.List[PySide6.QtQml.QQmlError]: ...
    def initialSize(self, /) -> PySide6.QtCore.QSize: ...
    def keyPressEvent(self, arg__1: PySide6.QtGui.QKeyEvent, /) -> None: ...
    def keyReleaseEvent(self, arg__1: PySide6.QtGui.QKeyEvent, /) -> None: ...
    def loadFromModule(self, uri: str, typeName: str, /) -> None: ...
    def mouseMoveEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mousePressEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseReleaseEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def resizeEvent(self, arg__1: PySide6.QtGui.QResizeEvent, /) -> None: ...
    def resizeMode(self, /) -> PySide6.QtQuick.QQuickView.ResizeMode: ...
    def rootContext(self, /) -> PySide6.QtQml.QQmlContext: ...
    def rootObject(self, /) -> PySide6.QtQuick.QQuickItem: ...
    def setContent(self, url: PySide6.QtCore.QUrl | str, component: PySide6.QtQml.QQmlComponent, item: PySide6.QtCore.QObject, /) -> None: ...
    def setInitialProperties(self, initialProperties: typing.Dict[str, typing.Any], /) -> None: ...
    def setResizeMode(self, arg__1: PySide6.QtQuick.QQuickView.ResizeMode, /) -> None: ...
    def setSource(self, arg__1: PySide6.QtCore.QUrl | str, /) -> None: ...
    def sizeHint(self, /) -> PySide6.QtCore.QSize: ...
    def source(self, /) -> PySide6.QtCore.QUrl: ...
    def status(self, /) -> PySide6.QtQuick.QQuickView.Status: ...
    def timerEvent(self, arg__1: PySide6.QtCore.QTimerEvent, /) -> None: ...


class QQuickWindow(PySide6.QtGui.QWindow):

    activeFocusItemChanged   : typing.ClassVar[Signal] = ... # activeFocusItemChanged()
    afterAnimating           : typing.ClassVar[Signal] = ... # afterAnimating()
    afterFrameEnd            : typing.ClassVar[Signal] = ... # afterFrameEnd()
    afterRenderPassRecording : typing.ClassVar[Signal] = ... # afterRenderPassRecording()
    afterRendering           : typing.ClassVar[Signal] = ... # afterRendering()
    afterSynchronizing       : typing.ClassVar[Signal] = ... # afterSynchronizing()
    beforeFrameBegin         : typing.ClassVar[Signal] = ... # beforeFrameBegin()
    beforeRenderPassRecording: typing.ClassVar[Signal] = ... # beforeRenderPassRecording()
    beforeRendering          : typing.ClassVar[Signal] = ... # beforeRendering()
    beforeSynchronizing      : typing.ClassVar[Signal] = ... # beforeSynchronizing()
    closing                  : typing.ClassVar[Signal] = ... # closing(QQuickCloseEvent*)
    colorChanged             : typing.ClassVar[Signal] = ... # colorChanged(QColor)
    frameSwapped             : typing.ClassVar[Signal] = ... # frameSwapped()
    paletteChanged           : typing.ClassVar[Signal] = ... # paletteChanged()
    paletteCreated           : typing.ClassVar[Signal] = ... # paletteCreated()
    sceneGraphAboutToStop    : typing.ClassVar[Signal] = ... # sceneGraphAboutToStop()
    sceneGraphError          : typing.ClassVar[Signal] = ... # sceneGraphError(QQuickWindow::SceneGraphError,QString)
    sceneGraphInitialized    : typing.ClassVar[Signal] = ... # sceneGraphInitialized()
    sceneGraphInvalidated    : typing.ClassVar[Signal] = ... # sceneGraphInvalidated()

    class CreateTextureOption(enum.Flag):

        TextureHasAlphaChannel    = ...  # 0x1
        TextureHasMipmaps         = ...  # 0x2
        TextureOwnsGLTexture      = ...  # 0x4
        TextureCanUseAtlas        = ...  # 0x8
        TextureIsOpaque           = ...  # 0x10

    class GraphicsStateInfo(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, GraphicsStateInfo: PySide6.QtQuick.QQuickWindow.GraphicsStateInfo, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...

    class RenderStage(enum.Enum):

        BeforeSynchronizingStage  = ...  # 0x0
        AfterSynchronizingStage   = ...  # 0x1
        BeforeRenderingStage      = ...  # 0x2
        AfterRenderingStage       = ...  # 0x3
        AfterSwapStage            = ...  # 0x4
        NoStage                   = ...  # 0x5

    class SceneGraphError(enum.Enum):

        ContextNotAvailable       = ...  # 0x1

    class TextRenderType(enum.Enum):

        QtTextRendering           = ...  # 0x0
        NativeTextRendering       = ...  # 0x1
        CurveTextRendering        = ...  # 0x2


    @typing.overload
    def __init__(self, renderControl: PySide6.QtQuick.QQuickRenderControl, /, *, color: PySide6.QtGui.QColor | None = ..., contentItem: PySide6.QtQuick.QQuickItem | None = ..., activeFocusItem: PySide6.QtQuick.QQuickItem | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtGui.QWindow | None = ..., *, color: PySide6.QtGui.QColor | None = ..., contentItem: PySide6.QtQuick.QQuickItem | None = ..., activeFocusItem: PySide6.QtQuick.QQuickItem | None = ...) -> None: ...

    def __repr__(self, /) -> str: ...
    def accessibleRoot(self, /) -> PySide6.QtGui.QAccessibleInterface: ...
    def activeFocusItem(self, /) -> PySide6.QtQuick.QQuickItem: ...
    def beginExternalCommands(self, /) -> None: ...
    def closeEvent(self, arg__1: PySide6.QtGui.QCloseEvent, /) -> None: ...
    def color(self, /) -> PySide6.QtGui.QColor: ...
    def contentItem(self, /) -> PySide6.QtQuick.QQuickItem: ...
    def createImageNode(self, /) -> PySide6.QtQuick.QSGImageNode: ...
    def createNinePatchNode(self, /) -> PySide6.QtQuick.QSGNinePatchNode: ...
    def createRectangleNode(self, /) -> PySide6.QtQuick.QSGRectangleNode: ...
    def createTextNode(self, /) -> PySide6.QtQuick.QSGTextNode: ...
    @typing.overload
    def createTextureFromImage(self, image: PySide6.QtGui.QImage, /) -> PySide6.QtQuick.QSGTexture: ...
    @typing.overload
    def createTextureFromImage(self, image: PySide6.QtGui.QImage, options: PySide6.QtQuick.QQuickWindow.CreateTextureOption, /) -> PySide6.QtQuick.QSGTexture: ...
    def createTextureFromRhiTexture(self, texture: PySide6.QtGui.QRhiTexture, /, options: PySide6.QtQuick.QQuickWindow.CreateTextureOption = ...) -> PySide6.QtQuick.QSGTexture: ...
    def effectiveDevicePixelRatio(self, /) -> float: ...
    def endExternalCommands(self, /) -> None: ...
    def event(self, arg__1: PySide6.QtCore.QEvent, /) -> bool: ...
    def exposeEvent(self, arg__1: PySide6.QtGui.QExposeEvent, /) -> None: ...
    def focusInEvent(self, arg__1: PySide6.QtGui.QFocusEvent, /) -> None: ...
    def focusObject(self, /) -> PySide6.QtCore.QObject: ...
    def focusOutEvent(self, arg__1: PySide6.QtGui.QFocusEvent, /) -> None: ...
    def grabWindow(self, /) -> PySide6.QtGui.QImage: ...
    @staticmethod
    def graphicsApi() -> PySide6.QtQuick.QSGRendererInterface.GraphicsApi: ...
    def graphicsConfiguration(self, /) -> PySide6.QtQuick.QQuickGraphicsConfiguration: ...
    def graphicsDevice(self, /) -> PySide6.QtQuick.QQuickGraphicsDevice: ...
    def graphicsStateInfo(self, /) -> PySide6.QtQuick.QQuickWindow.GraphicsStateInfo: ...
    @staticmethod
    def hasDefaultAlphaBuffer() -> bool: ...
    def hideEvent(self, arg__1: PySide6.QtGui.QHideEvent, /) -> None: ...
    def incubationController(self, /) -> PySide6.QtQml.QQmlIncubationController: ...
    def isPersistentGraphics(self, /) -> bool: ...
    def isPersistentSceneGraph(self, /) -> bool: ...
    def isSceneGraphInitialized(self, /) -> bool: ...
    def keyPressEvent(self, arg__1: PySide6.QtGui.QKeyEvent, /) -> None: ...
    def keyReleaseEvent(self, arg__1: PySide6.QtGui.QKeyEvent, /) -> None: ...
    def mouseDoubleClickEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseGrabberItem(self, /) -> PySide6.QtQuick.QQuickItem: ...
    def mouseMoveEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mousePressEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseReleaseEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def releaseResources(self, /) -> None: ...
    def renderTarget(self, /) -> PySide6.QtQuick.QQuickRenderTarget: ...
    def rendererInterface(self, /) -> PySide6.QtQuick.QSGRendererInterface: ...
    def resizeEvent(self, arg__1: PySide6.QtGui.QResizeEvent, /) -> None: ...
    def rhi(self, /) -> PySide6.QtGui.QRhi: ...
    @staticmethod
    def sceneGraphBackend() -> str: ...
    def scheduleRenderJob(self, job: PySide6.QtCore.QRunnable, schedule: PySide6.QtQuick.QQuickWindow.RenderStage, /) -> None: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    @staticmethod
    def setDefaultAlphaBuffer(useAlpha: bool, /) -> None: ...
    @staticmethod
    def setGraphicsApi(api: PySide6.QtQuick.QSGRendererInterface.GraphicsApi, /) -> None: ...
    def setGraphicsConfiguration(self, config: PySide6.QtQuick.QQuickGraphicsConfiguration, /) -> None: ...
    def setGraphicsDevice(self, device: PySide6.QtQuick.QQuickGraphicsDevice, /) -> None: ...
    def setPersistentGraphics(self, persistent: bool, /) -> None: ...
    def setPersistentSceneGraph(self, persistent: bool, /) -> None: ...
    def setRenderTarget(self, target: PySide6.QtQuick.QQuickRenderTarget, /) -> None: ...
    @staticmethod
    def setSceneGraphBackend(backend: str, /) -> None: ...
    @staticmethod
    def setTextRenderType(renderType: PySide6.QtQuick.QQuickWindow.TextRenderType, /) -> None: ...
    def showEvent(self, arg__1: PySide6.QtGui.QShowEvent, /) -> None: ...
    def swapChain(self, /) -> PySide6.QtGui.QRhiSwapChain: ...
    def tabletEvent(self, arg__1: PySide6.QtGui.QTabletEvent, /) -> None: ...
    @staticmethod
    def textRenderType() -> PySide6.QtQuick.QQuickWindow.TextRenderType: ...
    def update(self, /) -> None: ...
    def wheelEvent(self, arg__1: PySide6.QtGui.QWheelEvent, /) -> None: ...


class QSGBasicGeometryNode(PySide6.QtQuick.QSGNode):

    def __init__(self, type: PySide6.QtQuick.QSGNode.NodeType, /) -> None: ...

    def clipList(self, /) -> PySide6.QtQuick.QSGClipNode: ...
    def geometry(self, /) -> PySide6.QtQuick.QSGGeometry: ...
    def matrix(self, /) -> PySide6.QtGui.QMatrix4x4: ...
    def setGeometry(self, geometry: PySide6.QtQuick.QSGGeometry, /) -> None: ...
    def setRendererClipList(self, c: PySide6.QtQuick.QSGClipNode, /) -> None: ...
    def setRendererMatrix(self, m: PySide6.QtGui.QMatrix4x4 | PySide6.QtGui.QTransform, /) -> None: ...


class QSGClipNode(PySide6.QtQuick.QSGBasicGeometryNode):

    def __init__(self, /) -> None: ...

    def clipRect(self, /) -> PySide6.QtCore.QRectF: ...
    def isRectangular(self, /) -> bool: ...
    def setClipRect(self, arg__1: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def setIsRectangular(self, rectHint: bool, /) -> None: ...


class QSGDynamicTexture(PySide6.QtQuick.QSGTexture):

    def __init__(self, /) -> None: ...

    def updateTexture(self, /) -> bool: ...


class QSGFlatColorMaterial(PySide6.QtQuick.QSGMaterial):

    def __init__(self, /) -> None: ...

    def color(self, /) -> PySide6.QtGui.QColor: ...
    def compare(self, other: PySide6.QtQuick.QSGMaterial, /) -> int: ...
    def createShader(self, renderMode: PySide6.QtQuick.QSGRendererInterface.RenderMode, /) -> PySide6.QtQuick.QSGMaterialShader: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def type(self, /) -> PySide6.QtQuick.QSGMaterialType: ...


class QSGGeometry(Shiboken.Object):

    class Attribute(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, Attribute: PySide6.QtQuick.QSGGeometry.Attribute, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        @staticmethod
        def create(pos: int, tupleSize: int, primitiveType: int, /, isPosition: bool = ...) -> PySide6.QtQuick.QSGGeometry.Attribute: ...
        @staticmethod
        def createWithAttributeType(pos: int, tupleSize: int, primitiveType: int, attributeType: PySide6.QtQuick.QSGGeometry.AttributeType, /) -> PySide6.QtQuick.QSGGeometry.Attribute: ...

    class AttributeSet(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, AttributeSet: PySide6.QtQuick.QSGGeometry.AttributeSet, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...

    class AttributeType(enum.Enum):

        UnknownAttribute          = ...  # 0x0
        PositionAttribute         = ...  # 0x1
        ColorAttribute            = ...  # 0x2
        TexCoordAttribute         = ...  # 0x3
        TexCoord1Attribute        = ...  # 0x4
        TexCoord2Attribute        = ...  # 0x5

    class ColoredPoint2D(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, ColoredPoint2D: PySide6.QtQuick.QSGGeometry.ColoredPoint2D, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def set(self, nx: float, ny: float, nr: int, ng: int, nb: int, na: int, /) -> None: ...

    class DataPattern(enum.Enum):

        AlwaysUploadPattern       = ...  # 0x0
        StreamPattern             = ...  # 0x1
        DynamicPattern            = ...  # 0x2
        StaticPattern             = ...  # 0x3

    class DrawingMode(enum.IntEnum):

        DrawPoints                = ...  # 0x0
        DrawLines                 = ...  # 0x1
        DrawLineLoop              = ...  # 0x2
        DrawLineStrip             = ...  # 0x3
        DrawTriangles             = ...  # 0x4
        DrawTriangleStrip         = ...  # 0x5
        DrawTriangleFan           = ...  # 0x6

    class Point2D(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, Point2D: PySide6.QtQuick.QSGGeometry.Point2D, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def set(self, nx: float, ny: float, /) -> None: ...

    class TexturedPoint2D(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, TexturedPoint2D: PySide6.QtQuick.QSGGeometry.TexturedPoint2D, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def set(self, nx: float, ny: float, ntx: float, nty: float, /) -> None: ...

    class Type(enum.Enum):

        ByteType                  = ...  # 0x1400
        UnsignedByteType          = ...  # 0x1401
        ShortType                 = ...  # 0x1402
        UnsignedShortType         = ...  # 0x1403
        IntType                   = ...  # 0x1404
        UnsignedIntType           = ...  # 0x1405
        FloatType                 = ...  # 0x1406
        Bytes2Type                = ...  # 0x1407
        Bytes3Type                = ...  # 0x1408
        Bytes4Type                = ...  # 0x1409
        DoubleType                = ...  # 0x140a


    def __init__(self, attribs: PySide6.QtQuick.QSGGeometry.AttributeSet, vertexCount: int, /, indexCount: int | None = ..., indexType: int = ...) -> None: ...

    def allocate(self, vertexCount: int, /, indexCount: int | None = ...) -> None: ...
    def attributeCount(self, /) -> int: ...
    def attributes(self, /) -> PySide6.QtQuick.QSGGeometry.Attribute: ...
    @staticmethod
    def defaultAttributes_ColoredPoint2D() -> PySide6.QtQuick.QSGGeometry.AttributeSet: ...
    @staticmethod
    def defaultAttributes_Point2D() -> PySide6.QtQuick.QSGGeometry.AttributeSet: ...
    @staticmethod
    def defaultAttributes_TexturedPoint2D() -> PySide6.QtQuick.QSGGeometry.AttributeSet: ...
    def drawingMode(self, /) -> int: ...
    def indexCount(self, /) -> int: ...
    def indexData(self, /) -> int: ...
    def indexDataAsUInt(self, /) -> typing.List[int]: ...
    def indexDataAsUShort(self, /) -> typing.List[int]: ...
    def indexDataPattern(self, /) -> PySide6.QtQuick.QSGGeometry.DataPattern: ...
    def indexType(self, /) -> int: ...
    def lineWidth(self, /) -> float: ...
    def markIndexDataDirty(self, /) -> None: ...
    def markVertexDataDirty(self, /) -> None: ...
    def setDrawingMode(self, mode: int, /) -> None: ...
    def setIndexDataPattern(self, p: PySide6.QtQuick.QSGGeometry.DataPattern, /) -> None: ...
    def setLineWidth(self, w: float, /) -> None: ...
    def setVertexDataAsPoint2D(self, points: collections.abc.Sequence[PySide6.QtQuick.QSGGeometry.Point2D], /) -> None: ...
    def setVertexDataPattern(self, p: PySide6.QtQuick.QSGGeometry.DataPattern, /) -> None: ...
    def sizeOfIndex(self, /) -> int: ...
    def sizeOfVertex(self, /) -> int: ...
    @staticmethod
    def updateColoredRectGeometry(g: PySide6.QtQuick.QSGGeometry, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    @staticmethod
    def updateRectGeometry(g: PySide6.QtQuick.QSGGeometry, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    @staticmethod
    def updateTexturedRectGeometry(g: PySide6.QtQuick.QSGGeometry, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, sourceRect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def vertexCount(self, /) -> int: ...
    def vertexData(self, /) -> int: ...
    def vertexDataAsColoredPoint2D(self, /) -> PySide6.QtQuick.QSGGeometry.ColoredPoint2D: ...
    def vertexDataAsPoint2D(self, /) -> object: ...
    def vertexDataAsTexturedPoint2D(self, /) -> PySide6.QtQuick.QSGGeometry.TexturedPoint2D: ...
    def vertexDataPattern(self, /) -> PySide6.QtQuick.QSGGeometry.DataPattern: ...


class QSGGeometryNode(PySide6.QtQuick.QSGBasicGeometryNode):

    def __init__(self, /) -> None: ...

    def __repr__(self, /) -> str: ...
    def activeMaterial(self, /) -> PySide6.QtQuick.QSGMaterial: ...
    def inheritedOpacity(self, /) -> float: ...
    def material(self, /) -> PySide6.QtQuick.QSGMaterial: ...
    def opaqueMaterial(self, /) -> PySide6.QtQuick.QSGMaterial: ...
    def renderOrder(self, /) -> int: ...
    def setInheritedOpacity(self, opacity: float, /) -> None: ...
    def setMaterial(self, material: PySide6.QtQuick.QSGMaterial, /) -> None: ...
    def setOpaqueMaterial(self, material: PySide6.QtQuick.QSGMaterial, /) -> None: ...
    def setRenderOrder(self, order: int, /) -> None: ...


class QSGImageNode(PySide6.QtQuick.QSGGeometryNode):

    class TextureCoordinatesTransformFlag(enum.Flag):

        NoTransform               = ...  # 0x0
        MirrorHorizontally        = ...  # 0x1
        MirrorVertically          = ...  # 0x2


    def __init__(self, /) -> None: ...

    def anisotropyLevel(self, /) -> PySide6.QtQuick.QSGTexture.AnisotropyLevel: ...
    def filtering(self, /) -> PySide6.QtQuick.QSGTexture.Filtering: ...
    def mipmapFiltering(self, /) -> PySide6.QtQuick.QSGTexture.Filtering: ...
    def ownsTexture(self, /) -> bool: ...
    @staticmethod
    def rebuildGeometry(g: PySide6.QtQuick.QSGGeometry, texture: PySide6.QtQuick.QSGTexture, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, sourceRect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, texCoordMode: PySide6.QtQuick.QSGImageNode.TextureCoordinatesTransformFlag, /) -> None: ...
    def rect(self, /) -> PySide6.QtCore.QRectF: ...
    def setAnisotropyLevel(self, level: PySide6.QtQuick.QSGTexture.AnisotropyLevel, /) -> None: ...
    def setFiltering(self, filtering: PySide6.QtQuick.QSGTexture.Filtering, /) -> None: ...
    def setMipmapFiltering(self, filtering: PySide6.QtQuick.QSGTexture.Filtering, /) -> None: ...
    def setOwnsTexture(self, owns: bool, /) -> None: ...
    @typing.overload
    def setRect(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float, /) -> None: ...
    @typing.overload
    def setSourceRect(self, r: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    @typing.overload
    def setSourceRect(self, x: float, y: float, w: float, h: float, /) -> None: ...
    def setTexture(self, texture: PySide6.QtQuick.QSGTexture, /) -> None: ...
    def setTextureCoordinatesTransform(self, mode: PySide6.QtQuick.QSGImageNode.TextureCoordinatesTransformFlag, /) -> None: ...
    def sourceRect(self, /) -> PySide6.QtCore.QRectF: ...
    def texture(self, /) -> PySide6.QtQuick.QSGTexture: ...
    def textureCoordinatesTransform(self, /) -> PySide6.QtQuick.QSGImageNode.TextureCoordinatesTransformFlag: ...


class QSGMaterial(Shiboken.Object):

    class Flag(enum.Flag):

        Blending                  = ...  # 0x1
        RequiresDeterminant       = ...  # 0x2
        RequiresFullMatrixExceptTranslate = ...  # 0x6
        RequiresFullMatrix        = ...  # 0xe
        CustomCompileStep         = ...  # 0x10
        NoBatching                = ...  # 0x10
        MultiView2                = ...  # 0x10000
        MultiView3                = ...  # 0x20000
        MultiView4                = ...  # 0x40000


    def __init__(self, /) -> None: ...

    def compare(self, other: PySide6.QtQuick.QSGMaterial, /) -> int: ...
    def createShader(self, renderMode: PySide6.QtQuick.QSGRendererInterface.RenderMode, /) -> PySide6.QtQuick.QSGMaterialShader: ...
    def flags(self, /) -> PySide6.QtQuick.QSGMaterial.Flag: ...
    def setFlag(self, flags: PySide6.QtQuick.QSGMaterial.Flag, /, on: bool = ...) -> None: ...
    def type(self, /) -> PySide6.QtQuick.QSGMaterialType: ...
    def viewCount(self, /) -> int: ...


class QSGMaterialShader(Shiboken.Object):

    class Flag(enum.Flag):

        UpdatesGraphicsPipelineState = ...  # 0x1

    class GraphicsPipelineState(Shiboken.Object):

        class BlendFactor(enum.Enum):

            Zero                      = ...  # 0x0
            One                       = ...  # 0x1
            SrcColor                  = ...  # 0x2
            OneMinusSrcColor          = ...  # 0x3
            DstColor                  = ...  # 0x4
            OneMinusDstColor          = ...  # 0x5
            SrcAlpha                  = ...  # 0x6
            OneMinusSrcAlpha          = ...  # 0x7
            DstAlpha                  = ...  # 0x8
            OneMinusDstAlpha          = ...  # 0x9
            ConstantColor             = ...  # 0xa
            OneMinusConstantColor     = ...  # 0xb
            ConstantAlpha             = ...  # 0xc
            OneMinusConstantAlpha     = ...  # 0xd
            SrcAlphaSaturate          = ...  # 0xe
            Src1Color                 = ...  # 0xf
            OneMinusSrc1Color         = ...  # 0x10
            Src1Alpha                 = ...  # 0x11
            OneMinusSrc1Alpha         = ...  # 0x12

        class BlendOp(enum.Enum):

            Add                       = ...  # 0x0
            Subtract                  = ...  # 0x1
            ReverseSubtract           = ...  # 0x2
            Min                       = ...  # 0x3
            Max                       = ...  # 0x4

        class ColorMaskComponent(enum.Flag):

            R                         = ...  # 0x1
            G                         = ...  # 0x2
            B                         = ...  # 0x4
            A                         = ...  # 0x8

        class CullMode(enum.Enum):

            CullNone                  = ...  # 0x0
            CullFront                 = ...  # 0x1
            CullBack                  = ...  # 0x2

        class PolygonMode(enum.Enum):

            Fill                      = ...  # 0x0
            Line                      = ...  # 0x1


        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, GraphicsPipelineState: PySide6.QtQuick.QSGMaterialShader.GraphicsPipelineState, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...

    class RenderState(Shiboken.Object):

        class DirtyState(enum.Flag):

            DirtyMatrix               = ...  # 0x1
            DirtyOpacity              = ...  # 0x2
            DirtyCachedMaterialData   = ...  # 0x4
            DirtyAll                  = ...  # 0xffff


        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, RenderState: PySide6.QtQuick.QSGMaterialShader.RenderState, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        @typing.overload
        def combinedMatrix(self, /) -> PySide6.QtGui.QMatrix4x4: ...
        @typing.overload
        def combinedMatrix(self, index: int, /) -> PySide6.QtGui.QMatrix4x4: ...
        def determinant(self, /) -> float: ...
        def devicePixelRatio(self, /) -> float: ...
        def deviceRect(self, /) -> PySide6.QtCore.QRect: ...
        def dirtyStates(self, /) -> PySide6.QtQuick.QSGMaterialShader.RenderState.DirtyState: ...
        def isMatrixDirty(self, /) -> bool: ...
        def isOpacityDirty(self, /) -> bool: ...
        def modelViewMatrix(self, /) -> PySide6.QtGui.QMatrix4x4: ...
        def opacity(self, /) -> float: ...
        @typing.overload
        def projectionMatrix(self, /) -> PySide6.QtGui.QMatrix4x4: ...
        @typing.overload
        def projectionMatrix(self, index: int, /) -> PySide6.QtGui.QMatrix4x4: ...
        def projectionMatrixCount(self, /) -> int: ...
        def resourceUpdateBatch(self, /) -> PySide6.QtGui.QRhiResourceUpdateBatch: ...
        def rhi(self, /) -> PySide6.QtGui.QRhi: ...
        def uniformData(self, /) -> PySide6.QtCore.QByteArray: ...
        def viewportRect(self, /) -> PySide6.QtCore.QRect: ...

    class Stage(enum.Enum):

        VertexStage               = ...  # 0x0
        FragmentStage             = ...  # 0x1


    def __init__(self, /) -> None: ...

    def combinedImageSamplerCount(self, binding: int, /) -> int: ...
    def flags(self, /) -> PySide6.QtQuick.QSGMaterialShader.Flag: ...
    def setFlag(self, flags: PySide6.QtQuick.QSGMaterialShader.Flag, /, on: bool = ...) -> None: ...
    def setFlags(self, flags: PySide6.QtQuick.QSGMaterialShader.Flag, /) -> None: ...
    @typing.overload
    def setShaderFileName(self, stage: PySide6.QtQuick.QSGMaterialShader.Stage, filename: str, /) -> None: ...
    @typing.overload
    def setShaderFileName(self, stage: PySide6.QtQuick.QSGMaterialShader.Stage, filename: str, viewCount: int, /) -> None: ...
    def updateGraphicsPipelineState(self, state: PySide6.QtQuick.QSGMaterialShader.RenderState, ps: PySide6.QtQuick.QSGMaterialShader.GraphicsPipelineState, newMaterial: PySide6.QtQuick.QSGMaterial, oldMaterial: PySide6.QtQuick.QSGMaterial, /) -> bool: ...
    def updateUniformData(self, state: PySide6.QtQuick.QSGMaterialShader.RenderState, newMaterial: PySide6.QtQuick.QSGMaterial, oldMaterial: PySide6.QtQuick.QSGMaterial, /) -> bool: ...


class QSGMaterialType(Shiboken.Object):

    def __init__(self, /) -> None: ...


class QSGNinePatchNode(PySide6.QtQuick.QSGGeometryNode):

    def __init__(self, /) -> None: ...

    @staticmethod
    def rebuildGeometry(texture: PySide6.QtQuick.QSGTexture, geometry: PySide6.QtQuick.QSGGeometry, padding: PySide6.QtGui.QVector4D, bounds: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, dpr: float, /) -> None: ...
    def setBounds(self, bounds: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def setDevicePixelRatio(self, ratio: float, /) -> None: ...
    def setPadding(self, left: float, top: float, right: float, bottom: float, /) -> None: ...
    def setTexture(self, texture: PySide6.QtQuick.QSGTexture, /) -> None: ...
    def update(self, /) -> None: ...


class QSGNode(Shiboken.Object):

    class DirtyStateBit(enum.Flag):

        DirtyUsePreprocess        = ...  # 0x2
        DirtySubtreeBlocked       = ...  # 0x80
        DirtyMatrix               = ...  # 0x100
        DirtyNodeAdded            = ...  # 0x400
        DirtyNodeRemoved          = ...  # 0x800
        DirtyGeometry             = ...  # 0x1000
        DirtyMaterial             = ...  # 0x2000
        DirtyOpacity              = ...  # 0x4000
        DirtyForceUpdate          = ...  # 0x8000
        DirtyPropagationMask      = ...  # 0xc500

    class Flag(enum.Flag):

        OwnedByParent             = ...  # 0x1
        UsePreprocess             = ...  # 0x2
        OwnsGeometry              = ...  # 0x10000
        OwnsMaterial              = ...  # 0x20000
        OwnsOpaqueMaterial        = ...  # 0x40000
        IsVisitableNode           = ...  # 0x1000000

    class NodeType(enum.Enum):

        BasicNodeType             = ...  # 0x0
        GeometryNodeType          = ...  # 0x1
        TransformNodeType         = ...  # 0x2
        ClipNodeType              = ...  # 0x3
        OpacityNodeType           = ...  # 0x4
        RootNodeType              = ...  # 0x5
        RenderNodeType            = ...  # 0x6


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, type: PySide6.QtQuick.QSGNode.NodeType, /) -> None: ...

    def __repr__(self, /) -> str: ...
    def appendChildNode(self, node: PySide6.QtQuick.QSGNode, /) -> None: ...
    def childAtIndex(self, i: int, /) -> PySide6.QtQuick.QSGNode: ...
    def childCount(self, /) -> int: ...
    def clearDirty(self, /) -> None: ...
    def dirtyState(self, /) -> PySide6.QtQuick.QSGNode.DirtyStateBit: ...
    def firstChild(self, /) -> PySide6.QtQuick.QSGNode: ...
    def flags(self, /) -> PySide6.QtQuick.QSGNode.Flag: ...
    def insertChildNodeAfter(self, node: PySide6.QtQuick.QSGNode, after: PySide6.QtQuick.QSGNode, /) -> None: ...
    def insertChildNodeBefore(self, node: PySide6.QtQuick.QSGNode, before: PySide6.QtQuick.QSGNode, /) -> None: ...
    def isSubtreeBlocked(self, /) -> bool: ...
    def lastChild(self, /) -> PySide6.QtQuick.QSGNode: ...
    def markDirty(self, bits: PySide6.QtQuick.QSGNode.DirtyStateBit, /) -> None: ...
    def nextSibling(self, /) -> PySide6.QtQuick.QSGNode: ...
    def parent(self, /) -> PySide6.QtQuick.QSGNode: ...
    def prependChildNode(self, node: PySide6.QtQuick.QSGNode, /) -> None: ...
    def preprocess(self, /) -> None: ...
    def previousSibling(self, /) -> PySide6.QtQuick.QSGNode: ...
    def removeAllChildNodes(self, /) -> None: ...
    def removeChildNode(self, node: PySide6.QtQuick.QSGNode, /) -> None: ...
    def reparentChildNodesTo(self, newParent: PySide6.QtQuick.QSGNode, /) -> None: ...
    def setFlag(self, arg__1: PySide6.QtQuick.QSGNode.Flag, /, arg__2: bool = ...) -> None: ...
    def setFlags(self, arg__1: PySide6.QtQuick.QSGNode.Flag, /, arg__2: bool = ...) -> None: ...
    def type(self, /) -> PySide6.QtQuick.QSGNode.NodeType: ...


class QSGNodeVisitor(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def enterClipNode(self, arg__1: PySide6.QtQuick.QSGClipNode, /) -> None: ...
    def enterGeometryNode(self, arg__1: PySide6.QtQuick.QSGGeometryNode, /) -> None: ...
    def enterOpacityNode(self, arg__1: PySide6.QtQuick.QSGOpacityNode, /) -> None: ...
    def enterTransformNode(self, arg__1: PySide6.QtQuick.QSGTransformNode, /) -> None: ...
    def leaveClipNode(self, arg__1: PySide6.QtQuick.QSGClipNode, /) -> None: ...
    def leaveGeometryNode(self, arg__1: PySide6.QtQuick.QSGGeometryNode, /) -> None: ...
    def leaveOpacityNode(self, arg__1: PySide6.QtQuick.QSGOpacityNode, /) -> None: ...
    def leaveTransformNode(self, arg__1: PySide6.QtQuick.QSGTransformNode, /) -> None: ...
    def visitChildren(self, n: PySide6.QtQuick.QSGNode, /) -> None: ...
    def visitNode(self, n: PySide6.QtQuick.QSGNode, /) -> None: ...


class QSGOpacityNode(PySide6.QtQuick.QSGNode):

    def __init__(self, /) -> None: ...

    def __repr__(self, /) -> str: ...
    def combinedOpacity(self, /) -> float: ...
    def isSubtreeBlocked(self, /) -> bool: ...
    def opacity(self, /) -> float: ...
    def setCombinedOpacity(self, opacity: float, /) -> None: ...
    def setOpacity(self, opacity: float, /) -> None: ...


class QSGOpaqueTextureMaterial(PySide6.QtQuick.QSGMaterial):

    def __init__(self, /) -> None: ...

    def anisotropyLevel(self, /) -> PySide6.QtQuick.QSGTexture.AnisotropyLevel: ...
    def compare(self, other: PySide6.QtQuick.QSGMaterial, /) -> int: ...
    def createShader(self, renderMode: PySide6.QtQuick.QSGRendererInterface.RenderMode, /) -> PySide6.QtQuick.QSGMaterialShader: ...
    def filtering(self, /) -> PySide6.QtQuick.QSGTexture.Filtering: ...
    def horizontalWrapMode(self, /) -> PySide6.QtQuick.QSGTexture.WrapMode: ...
    def mipmapFiltering(self, /) -> PySide6.QtQuick.QSGTexture.Filtering: ...
    def setAnisotropyLevel(self, level: PySide6.QtQuick.QSGTexture.AnisotropyLevel, /) -> None: ...
    def setFiltering(self, filteringType: PySide6.QtQuick.QSGTexture.Filtering, /) -> None: ...
    def setHorizontalWrapMode(self, mode: PySide6.QtQuick.QSGTexture.WrapMode, /) -> None: ...
    def setMipmapFiltering(self, filteringType: PySide6.QtQuick.QSGTexture.Filtering, /) -> None: ...
    def setTexture(self, texture: PySide6.QtQuick.QSGTexture, /) -> None: ...
    def setVerticalWrapMode(self, mode: PySide6.QtQuick.QSGTexture.WrapMode, /) -> None: ...
    def texture(self, /) -> PySide6.QtQuick.QSGTexture: ...
    def type(self, /) -> PySide6.QtQuick.QSGMaterialType: ...
    def verticalWrapMode(self, /) -> PySide6.QtQuick.QSGTexture.WrapMode: ...


class QSGRectangleNode(PySide6.QtQuick.QSGGeometryNode):

    def __init__(self, /) -> None: ...

    def color(self, /) -> PySide6.QtGui.QColor: ...
    def rect(self, /) -> PySide6.QtCore.QRectF: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    @typing.overload
    def setRect(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float, /) -> None: ...


class QSGRenderNode(PySide6.QtQuick.QSGNode):

    class RenderState(Shiboken.Object):

        def __init__(self, /) -> None: ...

        def clipRegion(self, /) -> PySide6.QtGui.QRegion: ...
        def get(self, state: bytes | bytearray | memoryview, /) -> int: ...
        def projectionMatrix(self, /) -> PySide6.QtGui.QMatrix4x4: ...
        def scissorEnabled(self, /) -> bool: ...
        def scissorRect(self, /) -> PySide6.QtCore.QRect: ...
        def stencilEnabled(self, /) -> bool: ...
        def stencilValue(self, /) -> int: ...

    class RenderingFlag(enum.Flag):

        BoundedRectRendering      = ...  # 0x1
        DepthAwareRendering       = ...  # 0x2
        OpaqueRendering           = ...  # 0x4
        NoExternalRendering       = ...  # 0x8

    class StateFlag(enum.Flag):

        DepthState                = ...  # 0x1
        StencilState              = ...  # 0x2
        ScissorState              = ...  # 0x4
        ColorState                = ...  # 0x8
        BlendState                = ...  # 0x10
        CullState                 = ...  # 0x20
        ViewportState             = ...  # 0x40
        RenderTargetState         = ...  # 0x80


    def __init__(self, /) -> None: ...

    def changedStates(self, /) -> PySide6.QtQuick.QSGRenderNode.StateFlag: ...
    def clipList(self, /) -> PySide6.QtQuick.QSGClipNode: ...
    def commandBuffer(self, /) -> PySide6.QtGui.QRhiCommandBuffer: ...
    def flags(self, /) -> PySide6.QtQuick.QSGRenderNode.RenderingFlag: ...
    def inheritedOpacity(self, /) -> float: ...
    def matrix(self, /) -> PySide6.QtGui.QMatrix4x4: ...
    def prepare(self, /) -> None: ...
    @typing.overload
    def projectionMatrix(self, /) -> PySide6.QtGui.QMatrix4x4: ...
    @typing.overload
    def projectionMatrix(self, index: int, /) -> PySide6.QtGui.QMatrix4x4: ...
    def rect(self, /) -> PySide6.QtCore.QRectF: ...
    def releaseResources(self, /) -> None: ...
    def render(self, state: PySide6.QtQuick.QSGRenderNode.RenderState, /) -> None: ...
    def renderTarget(self, /) -> PySide6.QtGui.QRhiRenderTarget: ...


class QSGRendererInterface(Shiboken.Object):

    class GraphicsApi(enum.Enum):

        Unknown                   = ...  # 0x0
        Software                  = ...  # 0x1
        OpenVG                    = ...  # 0x2
        OpenGL                    = ...  # 0x3
        OpenGLRhi                 = ...  # 0x3
        Direct3D11                = ...  # 0x4
        Direct3D11Rhi             = ...  # 0x4
        Vulkan                    = ...  # 0x5
        VulkanRhi                 = ...  # 0x5
        Metal                     = ...  # 0x6
        MetalRhi                  = ...  # 0x6
        Null                      = ...  # 0x7
        NullRhi                   = ...  # 0x7
        Direct3D12                = ...  # 0x8

    class RenderMode(enum.Enum):

        RenderMode2D              = ...  # 0x0
        RenderMode2DNoDepthBuffer = ...  # 0x1
        RenderMode3D              = ...  # 0x2

    class Resource(enum.Enum):

        DeviceResource            = ...  # 0x0
        CommandQueueResource      = ...  # 0x1
        CommandListResource       = ...  # 0x2
        PainterResource           = ...  # 0x3
        RhiResource               = ...  # 0x4
        RhiSwapchainResource      = ...  # 0x5
        RhiRedirectCommandBuffer  = ...  # 0x6
        RhiRedirectRenderTarget   = ...  # 0x7
        PhysicalDeviceResource    = ...  # 0x8
        OpenGLContextResource     = ...  # 0x9
        DeviceContextResource     = ...  # 0xa
        CommandEncoderResource    = ...  # 0xb
        VulkanInstanceResource    = ...  # 0xc
        RenderPassResource        = ...  # 0xd
        RedirectPaintDevice       = ...  # 0xe
        GraphicsQueueFamilyIndexResource = ...  # 0xf
        GraphicsQueueIndexResource = ...  # 0x10

    class ShaderCompilationType(enum.Flag):

        RuntimeCompilation        = ...  # 0x1
        OfflineCompilation        = ...  # 0x2

    class ShaderSourceType(enum.Flag):

        ShaderSourceString        = ...  # 0x1
        ShaderSourceFile          = ...  # 0x2
        ShaderByteCode            = ...  # 0x4

    class ShaderType(enum.Enum):

        UnknownShadingLanguage    = ...  # 0x0
        GLSL                      = ...  # 0x1
        HLSL                      = ...  # 0x2
        RhiShader                 = ...  # 0x3


    def __init__(self, /) -> None: ...

    @typing.overload
    def getResource(self, window: PySide6.QtQuick.QQuickWindow, resource: PySide6.QtQuick.QSGRendererInterface.Resource, /) -> int: ...
    @typing.overload
    def getResource(self, window: PySide6.QtQuick.QQuickWindow, resource: bytes | bytearray | memoryview, /) -> int: ...
    def graphicsApi(self, /) -> PySide6.QtQuick.QSGRendererInterface.GraphicsApi: ...
    @staticmethod
    def isApiRhiBased(api: PySide6.QtQuick.QSGRendererInterface.GraphicsApi, /) -> bool: ...
    def shaderCompilationType(self, /) -> PySide6.QtQuick.QSGRendererInterface.ShaderCompilationType: ...
    def shaderSourceType(self, /) -> PySide6.QtQuick.QSGRendererInterface.ShaderSourceType: ...
    def shaderType(self, /) -> PySide6.QtQuick.QSGRendererInterface.ShaderType: ...


class QSGRootNode(PySide6.QtQuick.QSGNode):

    def __init__(self, /) -> None: ...

    def __repr__(self, /) -> str: ...


class QSGSimpleRectNode(PySide6.QtQuick.QSGGeometryNode):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...

    def color(self, /) -> PySide6.QtGui.QColor: ...
    def rect(self, /) -> PySide6.QtCore.QRectF: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    @typing.overload
    def setRect(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float, /) -> None: ...


class QSGSimpleTextureNode(PySide6.QtQuick.QSGGeometryNode):

    class TextureCoordinatesTransformFlag(enum.Flag):

        NoTransform               = ...  # 0x0
        MirrorHorizontally        = ...  # 0x1
        MirrorVertically          = ...  # 0x2


    def __init__(self, /) -> None: ...

    def filtering(self, /) -> PySide6.QtQuick.QSGTexture.Filtering: ...
    def ownsTexture(self, /) -> bool: ...
    def rect(self, /) -> PySide6.QtCore.QRectF: ...
    def setFiltering(self, filtering: PySide6.QtQuick.QSGTexture.Filtering, /) -> None: ...
    def setOwnsTexture(self, owns: bool, /) -> None: ...
    @typing.overload
    def setRect(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float, /) -> None: ...
    @typing.overload
    def setSourceRect(self, r: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    @typing.overload
    def setSourceRect(self, x: float, y: float, w: float, h: float, /) -> None: ...
    def setTexture(self, texture: PySide6.QtQuick.QSGTexture, /) -> None: ...
    def setTextureCoordinatesTransform(self, mode: PySide6.QtQuick.QSGSimpleTextureNode.TextureCoordinatesTransformFlag, /) -> None: ...
    def sourceRect(self, /) -> PySide6.QtCore.QRectF: ...
    def texture(self, /) -> PySide6.QtQuick.QSGTexture: ...
    def textureCoordinatesTransform(self, /) -> PySide6.QtQuick.QSGSimpleTextureNode.TextureCoordinatesTransformFlag: ...


class QSGTextNode(PySide6.QtQuick.QSGTransformNode):

    class RenderType(enum.Enum):

        QtRendering               = ...  # 0x0
        NativeRendering           = ...  # 0x1
        CurveRendering            = ...  # 0x2

    class TextStyle(enum.Enum):

        Normal                    = ...  # 0x0
        Outline                   = ...  # 0x1
        Raised                    = ...  # 0x2
        Sunken                    = ...  # 0x3


    def __init__(self, /) -> None: ...

    def addTextDocument(self, position: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, document: PySide6.QtGui.QTextDocument, /, selectionStart: int = ..., selectionCount: int = ...) -> None: ...
    def addTextLayout(self, position: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, layout: PySide6.QtGui.QTextLayout, /, selectionStart: int = ..., selectionCount: int = ..., lineStart: int | None = ..., lineCount: int = ...) -> None: ...
    def clear(self, /) -> None: ...
    def color(self, /) -> PySide6.QtGui.QColor: ...
    def filtering(self, /) -> PySide6.QtQuick.QSGTexture.Filtering: ...
    def linkColor(self, /) -> PySide6.QtGui.QColor: ...
    def renderType(self, /) -> PySide6.QtQuick.QSGTextNode.RenderType: ...
    def renderTypeQuality(self, /) -> int: ...
    def selectionColor(self, /) -> PySide6.QtGui.QColor: ...
    def selectionTextColor(self, /) -> PySide6.QtGui.QColor: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setFiltering(self, arg__1: PySide6.QtQuick.QSGTexture.Filtering, /) -> None: ...
    def setLinkColor(self, linkColor: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setRenderType(self, renderType: PySide6.QtQuick.QSGTextNode.RenderType, /) -> None: ...
    def setRenderTypeQuality(self, renderTypeQuality: int, /) -> None: ...
    def setSelectionColor(self, selectionColor: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setSelectionTextColor(self, selectionTextColor: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setStyleColor(self, styleColor: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setTextStyle(self, textStyle: PySide6.QtQuick.QSGTextNode.TextStyle, /) -> None: ...
    def setViewport(self, viewport: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def styleColor(self, /) -> PySide6.QtGui.QColor: ...
    def textStyle(self, /) -> PySide6.QtQuick.QSGTextNode.TextStyle: ...
    def viewport(self, /) -> PySide6.QtCore.QRectF: ...


class QSGTexture(PySide6.QtCore.QObject):

    class AnisotropyLevel(enum.Enum):

        AnisotropyNone            = ...  # 0x0
        Anisotropy2x              = ...  # 0x1
        Anisotropy4x              = ...  # 0x2
        Anisotropy8x              = ...  # 0x3
        Anisotropy16x             = ...  # 0x4

    class Filtering(enum.Enum):

        None_                     = ...  # 0x0
        Nearest                   = ...  # 0x1
        Linear                    = ...  # 0x2

    class WrapMode(enum.Enum):

        Repeat                    = ...  # 0x0
        ClampToEdge               = ...  # 0x1
        MirroredRepeat            = ...  # 0x2


    def __init__(self, /) -> None: ...

    def anisotropyLevel(self, /) -> PySide6.QtQuick.QSGTexture.AnisotropyLevel: ...
    def commitTextureOperations(self, rhi: PySide6.QtGui.QRhi, resourceUpdates: PySide6.QtGui.QRhiResourceUpdateBatch, /) -> None: ...
    def comparisonKey(self, /) -> int: ...
    def convertToNormalizedSourceRect(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> PySide6.QtCore.QRectF: ...
    def filtering(self, /) -> PySide6.QtQuick.QSGTexture.Filtering: ...
    def hasAlphaChannel(self, /) -> bool: ...
    def hasMipmaps(self, /) -> bool: ...
    def horizontalWrapMode(self, /) -> PySide6.QtQuick.QSGTexture.WrapMode: ...
    def isAtlasTexture(self, /) -> bool: ...
    def mipmapFiltering(self, /) -> PySide6.QtQuick.QSGTexture.Filtering: ...
    def normalizedTextureSubRect(self, /) -> PySide6.QtCore.QRectF: ...
    def removedFromAtlas(self, /, resourceUpdates: PySide6.QtGui.QRhiResourceUpdateBatch | None = ...) -> PySide6.QtQuick.QSGTexture: ...
    def resolveInterface(self, name: bytes | bytearray | memoryview, revision: int, /) -> int: ...
    def rhiTexture(self, /) -> PySide6.QtGui.QRhiTexture: ...
    def setAnisotropyLevel(self, level: PySide6.QtQuick.QSGTexture.AnisotropyLevel, /) -> None: ...
    def setFiltering(self, filter: PySide6.QtQuick.QSGTexture.Filtering, /) -> None: ...
    def setHorizontalWrapMode(self, hwrap: PySide6.QtQuick.QSGTexture.WrapMode, /) -> None: ...
    def setMipmapFiltering(self, filter: PySide6.QtQuick.QSGTexture.Filtering, /) -> None: ...
    def setVerticalWrapMode(self, vwrap: PySide6.QtQuick.QSGTexture.WrapMode, /) -> None: ...
    def textureSize(self, /) -> PySide6.QtCore.QSize: ...
    def verticalWrapMode(self, /) -> PySide6.QtQuick.QSGTexture.WrapMode: ...


class QSGTextureMaterial(PySide6.QtQuick.QSGOpaqueTextureMaterial):

    def __init__(self, /) -> None: ...

    def createShader(self, renderMode: PySide6.QtQuick.QSGRendererInterface.RenderMode, /) -> PySide6.QtQuick.QSGMaterialShader: ...
    def type(self, /) -> PySide6.QtQuick.QSGMaterialType: ...


class QSGTextureProvider(PySide6.QtCore.QObject):

    textureChanged           : typing.ClassVar[Signal] = ... # textureChanged()

    def __init__(self, /) -> None: ...

    def texture(self, /) -> PySide6.QtQuick.QSGTexture: ...


class QSGTransformNode(PySide6.QtQuick.QSGNode):

    def __init__(self, /) -> None: ...

    def __repr__(self, /) -> str: ...
    def combinedMatrix(self, /) -> PySide6.QtGui.QMatrix4x4: ...
    def matrix(self, /) -> PySide6.QtGui.QMatrix4x4: ...
    def setCombinedMatrix(self, matrix: PySide6.QtGui.QMatrix4x4 | PySide6.QtGui.QTransform, /) -> None: ...
    def setMatrix(self, matrix: PySide6.QtGui.QMatrix4x4 | PySide6.QtGui.QTransform, /) -> None: ...


class QSGVertexColorMaterial(PySide6.QtQuick.QSGMaterial):

    def __init__(self, /) -> None: ...

    def compare(self, other: PySide6.QtQuick.QSGMaterial, /) -> int: ...
    def createShader(self, renderMode: PySide6.QtQuick.QSGRendererInterface.RenderMode, /) -> PySide6.QtQuick.QSGMaterialShader: ...
    def type(self, /) -> PySide6.QtQuick.QSGMaterialType: ...


class QSharedPointer_QQuickItemGrabResult(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, pointee: PySide6.QtQuick.QQuickItemGrabResult, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __dir__(self, /) -> collections.abc.Iterable[str]: ...
    def __repr__(self, /) -> str: ...
    def data(self, /) -> PySide6.QtQuick.QQuickItemGrabResult: ...
    @typing.overload
    def reset(self, /) -> None: ...
    @typing.overload
    def reset(self, t: PySide6.QtQuick.QQuickItemGrabResult, /) -> None: ...


# eof
