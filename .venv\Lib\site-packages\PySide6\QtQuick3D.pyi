# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtQuick3D, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtQuick3D`

import PySide6.QtQuick3D
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtQml

import enum
import typing
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QIntList: ...


class QQuick3D(Shiboken.Object):

    def __init__(self, /) -> None: ...

    @staticmethod
    def idealSurfaceFormat(samples: int = ...) -> PySide6.QtGui.QSurfaceFormat: ...


class QQuick3DGeometry(PySide6.QtQuick3D.QQuick3DObject):

    geometryChanged          : typing.ClassVar[Signal] = ... # geometryChanged()
    geometryNodeDirty        : typing.ClassVar[Signal] = ... # geometryNodeDirty()

    class Attribute(Shiboken.Object):

        class ComponentType(enum.Enum):

            U16Type                   = ...  # 0x0
            U32Type                   = ...  # 0x1
            I32Type                   = ...  # 0x2
            F32Type                   = ...  # 0x3

        class Semantic(enum.Enum):

            IndexSemantic             = ...  # 0x0
            PositionSemantic          = ...  # 0x1
            NormalSemantic            = ...  # 0x2
            TexCoord0Semantic         = ...  # 0x3
            TexCoordSemantic          = ...  # 0x3
            TangentSemantic           = ...  # 0x4
            BinormalSemantic          = ...  # 0x5
            JointSemantic             = ...  # 0x6
            WeightSemantic            = ...  # 0x7
            ColorSemantic             = ...  # 0x8
            TargetPositionSemantic    = ...  # 0x9
            TargetNormalSemantic      = ...  # 0xa
            TargetTangentSemantic     = ...  # 0xb
            TargetBinormalSemantic    = ...  # 0xc
            TexCoord1Semantic         = ...  # 0xd


        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, Attribute: PySide6.QtQuick3D.QQuick3DGeometry.Attribute, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...

    class PrimitiveType(enum.Enum):

        Points                    = ...  # 0x0
        LineStrip                 = ...  # 0x1
        Lines                     = ...  # 0x2
        TriangleStrip             = ...  # 0x3
        TriangleFan               = ...  # 0x4
        Triangles                 = ...  # 0x5

    class TargetAttribute(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, TargetAttribute: PySide6.QtQuick3D.QQuick3DGeometry.TargetAttribute, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...


    def __init__(self, /, parent: PySide6.QtQuick3D.QQuick3DObject | None = ...) -> None: ...

    @typing.overload
    def addAttribute(self, att: PySide6.QtQuick3D.QQuick3DGeometry.Attribute, /) -> None: ...
    @typing.overload
    def addAttribute(self, semantic: PySide6.QtQuick3D.QQuick3DGeometry.Attribute.Semantic, offset: int, componentType: PySide6.QtQuick3D.QQuick3DGeometry.Attribute.ComponentType, /) -> None: ...
    def addSubset(self, offset: int, count: int, boundsMin: PySide6.QtGui.QVector3D, boundsMax: PySide6.QtGui.QVector3D, /, name: str = ...) -> None: ...
    @typing.overload
    def addTargetAttribute(self, att: PySide6.QtQuick3D.QQuick3DGeometry.TargetAttribute, /) -> None: ...
    @typing.overload
    def addTargetAttribute(self, targetId: int, semantic: PySide6.QtQuick3D.QQuick3DGeometry.Attribute.Semantic, offset: int, /, stride: int | None = ...) -> None: ...
    def attribute(self, index: int, /) -> PySide6.QtQuick3D.QQuick3DGeometry.Attribute: ...
    def attributeCount(self, /) -> int: ...
    def boundsMax(self, /) -> PySide6.QtGui.QVector3D: ...
    def boundsMin(self, /) -> PySide6.QtGui.QVector3D: ...
    def clear(self, /) -> None: ...
    def indexData(self, /) -> PySide6.QtCore.QByteArray: ...
    def markAllDirty(self, /) -> None: ...
    def primitiveType(self, /) -> PySide6.QtQuick3D.QQuick3DGeometry.PrimitiveType: ...
    def setBounds(self, min: PySide6.QtGui.QVector3D, max: PySide6.QtGui.QVector3D, /) -> None: ...
    @typing.overload
    def setIndexData(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def setIndexData(self, offset: int, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setPrimitiveType(self, type: PySide6.QtQuick3D.QQuick3DGeometry.PrimitiveType, /) -> None: ...
    def setStride(self, stride: int, /) -> None: ...
    @typing.overload
    def setTargetData(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def setTargetData(self, offset: int, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def setVertexData(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def setVertexData(self, offset: int, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def stride(self, /) -> int: ...
    def subsetBoundsMax(self, subset: int, /) -> PySide6.QtGui.QVector3D: ...
    def subsetBoundsMin(self, subset: int, /) -> PySide6.QtGui.QVector3D: ...
    @typing.overload
    def subsetCount(self, /) -> int: ...
    @typing.overload
    def subsetCount(self, subset: int, /) -> int: ...
    def subsetName(self, subset: int, /) -> str: ...
    def subsetOffset(self, subset: int, /) -> int: ...
    def targetAttribute(self, index: int, /) -> PySide6.QtQuick3D.QQuick3DGeometry.TargetAttribute: ...
    def targetAttributeCount(self, /) -> int: ...
    def targetData(self, /) -> PySide6.QtCore.QByteArray: ...
    def vertexData(self, /) -> PySide6.QtCore.QByteArray: ...


class QQuick3DInstancing(PySide6.QtQuick3D.QQuick3DObject):

    depthSortingEnabledChanged: typing.ClassVar[Signal] = ... # depthSortingEnabledChanged()
    hasTransparencyChanged   : typing.ClassVar[Signal] = ... # hasTransparencyChanged()
    instanceCountOverrideChanged: typing.ClassVar[Signal] = ... # instanceCountOverrideChanged()
    instanceNodeDirty        : typing.ClassVar[Signal] = ... # instanceNodeDirty()
    instanceTableChanged     : typing.ClassVar[Signal] = ... # instanceTableChanged()
    shadowBoundsMaximumChanged: typing.ClassVar[Signal] = ... # shadowBoundsMaximumChanged()
    shadowBoundsMinimumChanged: typing.ClassVar[Signal] = ... # shadowBoundsMinimumChanged()

    class InstanceTableEntry(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, InstanceTableEntry: PySide6.QtQuick3D.QQuick3DInstancing.InstanceTableEntry, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def getColor(self, /) -> PySide6.QtGui.QColor: ...
        def getPosition(self, /) -> PySide6.QtGui.QVector3D: ...
        def getRotation(self, /) -> PySide6.QtGui.QQuaternion: ...
        def getScale(self, /) -> PySide6.QtGui.QVector3D: ...


    def __init__(self, /, parent: PySide6.QtQuick3D.QQuick3DObject | None = ..., *, instanceCountOverride: int | None = ..., hasTransparency: bool | None = ..., depthSortingEnabled: bool | None = ..., shadowBoundsMinimum: PySide6.QtGui.QVector3D | None = ..., shadowBoundsMaximum: PySide6.QtGui.QVector3D | None = ...) -> None: ...

    @staticmethod
    def calculateTableEntry(position: PySide6.QtGui.QVector3D, scale: PySide6.QtGui.QVector3D, eulerRotation: PySide6.QtGui.QVector3D, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /, customData: PySide6.QtGui.QVector4D = ...) -> PySide6.QtQuick3D.QQuick3DInstancing.InstanceTableEntry: ...
    @staticmethod
    def calculateTableEntryFromQuaternion(position: PySide6.QtGui.QVector3D, scale: PySide6.QtGui.QVector3D, rotation: PySide6.QtGui.QQuaternion, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /, customData: PySide6.QtGui.QVector4D = ...) -> PySide6.QtQuick3D.QQuick3DInstancing.InstanceTableEntry: ...
    def depthSortingEnabled(self, /) -> bool: ...
    def getInstanceBuffer(self, /) -> typing.Tuple[bool, str]: ...
    def hasTransparency(self, /) -> bool: ...
    def instanceBuffer(self, /) -> typing.Tuple[PySide6.QtCore.QByteArray, int]: ...
    def instanceColor(self, index: int, /) -> PySide6.QtGui.QColor: ...
    def instanceCountOverride(self, /) -> int: ...
    def instanceCustomData(self, index: int, /) -> PySide6.QtGui.QVector4D: ...
    def instancePosition(self, index: int, /) -> PySide6.QtGui.QVector3D: ...
    def instanceRotation(self, index: int, /) -> PySide6.QtGui.QQuaternion: ...
    def instanceScale(self, index: int, /) -> PySide6.QtGui.QVector3D: ...
    def markDirty(self, /) -> None: ...
    def setDepthSortingEnabled(self, enabled: bool, /) -> None: ...
    def setHasTransparency(self, hasTransparency: bool, /) -> None: ...
    def setInstanceCountOverride(self, instanceCountOverride: int, /) -> None: ...
    def setShadowBoundsMaximum(self, newShadowBoundsMinimum: PySide6.QtGui.QVector3D, /) -> None: ...
    def setShadowBoundsMinimum(self, newShadowBoundsMinimum: PySide6.QtGui.QVector3D, /) -> None: ...
    def shadowBoundsMaximum(self, /) -> PySide6.QtGui.QVector3D: ...
    def shadowBoundsMinimum(self, /) -> PySide6.QtGui.QVector3D: ...


class QQuick3DObject(PySide6.QtCore.QObject, PySide6.QtQml.QQmlParserStatus):

    childrenChanged          : typing.ClassVar[Signal] = ... # childrenChanged()
    parentChanged            : typing.ClassVar[Signal] = ... # parentChanged()
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged()

    class ItemChange(enum.Enum):

        ItemChildAddedChange      = ...  # 0x0
        ItemChildRemovedChange    = ...  # 0x1
        ItemSceneChange           = ...  # 0x2
        ItemVisibleHasChanged     = ...  # 0x3
        ItemParentHasChanged      = ...  # 0x4
        ItemOpacityHasChanged     = ...  # 0x5
        ItemActiveFocusHasChanged = ...  # 0x6
        ItemRotationHasChanged    = ...  # 0x7
        ItemAntialiasingHasChanged = ...  # 0x8
        ItemDevicePixelRatioHasChanged = ...  # 0x9
        ItemEnabledHasChanged     = ...  # 0xa


    def childItems(self, /) -> typing.List[PySide6.QtQuick3D.QQuick3DObject]: ...
    def classBegin(self, /) -> None: ...
    def componentComplete(self, /) -> None: ...
    def isComponentComplete(self, /) -> bool: ...
    def markAllDirty(self, /) -> None: ...
    def parentItem(self, /) -> PySide6.QtQuick3D.QQuick3DObject: ...
    def preSync(self, /) -> None: ...
    def setParentItem(self, parentItem: PySide6.QtQuick3D.QQuick3DObject, /) -> None: ...
    def setState(self, state: str, /) -> None: ...
    def state(self, /) -> str: ...
    def update(self, /) -> None: ...


class QQuick3DRenderExtension(PySide6.QtQuick3D.QQuick3DObject):

    def __init__(self, /, parent: PySide6.QtQuick3D.QQuick3DObject | None = ...) -> None: ...


class QQuick3DTextureData(PySide6.QtQuick3D.QQuick3DObject):

    textureDataNodeDirty     : typing.ClassVar[Signal] = ... # textureDataNodeDirty()

    class Format(enum.Enum):

        None_                     = ...  # 0x0
        RGBA8                     = ...  # 0x1
        RGBA16F                   = ...  # 0x2
        RGBA32F                   = ...  # 0x3
        RGBE8                     = ...  # 0x4
        R8                        = ...  # 0x5
        R16                       = ...  # 0x6
        R16F                      = ...  # 0x7
        R32F                      = ...  # 0x8
        BC1                       = ...  # 0x9
        BC2                       = ...  # 0xa
        BC3                       = ...  # 0xb
        BC4                       = ...  # 0xc
        BC5                       = ...  # 0xd
        BC6H                      = ...  # 0xe
        BC7                       = ...  # 0xf
        DXT1_RGBA                 = ...  # 0x10
        DXT1_RGB                  = ...  # 0x11
        DXT3_RGBA                 = ...  # 0x12
        DXT5_RGBA                 = ...  # 0x13
        ETC2_RGB8                 = ...  # 0x14
        ETC2_RGB8A1               = ...  # 0x15
        ETC2_RGBA8                = ...  # 0x16
        ASTC_4x4                  = ...  # 0x17
        ASTC_5x4                  = ...  # 0x18
        ASTC_5x5                  = ...  # 0x19
        ASTC_6x5                  = ...  # 0x1a
        ASTC_6x6                  = ...  # 0x1b
        ASTC_8x5                  = ...  # 0x1c
        ASTC_8x6                  = ...  # 0x1d
        ASTC_8x8                  = ...  # 0x1e
        ASTC_10x5                 = ...  # 0x1f
        ASTC_10x6                 = ...  # 0x20
        ASTC_10x8                 = ...  # 0x21
        ASTC_10x10                = ...  # 0x22
        ASTC_12x10                = ...  # 0x23
        ASTC_12x12                = ...  # 0x24


    def __init__(self, /, parent: PySide6.QtQuick3D.QQuick3DObject | None = ...) -> None: ...

    def depth(self, /) -> int: ...
    def format(self, /) -> PySide6.QtQuick3D.QQuick3DTextureData.Format: ...
    def hasTransparency(self, /) -> bool: ...
    def markAllDirty(self, /) -> None: ...
    def setDepth(self, depth: int, /) -> None: ...
    def setFormat(self, format: PySide6.QtQuick3D.QQuick3DTextureData.Format, /) -> None: ...
    def setHasTransparency(self, hasTransparency: bool, /) -> None: ...
    def setSize(self, size: PySide6.QtCore.QSize, /) -> None: ...
    def setTextureData(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def size(self, /) -> PySide6.QtCore.QSize: ...
    def textureData(self, /) -> PySide6.QtCore.QByteArray: ...


# eof
