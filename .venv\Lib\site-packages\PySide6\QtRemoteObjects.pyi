# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtRemoteObjects, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtRemoteObjects`

import PySide6.QtRemoteObjects
import PySide6.QtCore
import PySide6.QtNetwork

import enum
import typing
import collections
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QAbstractItemModelReplica(PySide6.QtCore.QAbstractItemModel):

    initialized              : typing.ClassVar[Signal] = ... # initialized()
    def availableRoles(self, /) -> typing.List[int]: ...
    def columnCount(self, /, parent: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex = ...) -> int: ...
    def data(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, /, role: int = ...) -> typing.Any: ...
    def flags(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, /) -> PySide6.QtCore.Qt.ItemFlag: ...
    def hasChildren(self, /, parent: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex = ...) -> bool: ...
    def hasData(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, role: int, /) -> bool: ...
    def headerData(self, section: int, orientation: PySide6.QtCore.Qt.Orientation, role: int, /) -> typing.Any: ...
    def index(self, row: int, column: int, /, parent: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex = ...) -> PySide6.QtCore.QModelIndex: ...
    def isInitialized(self, /) -> bool: ...
    def multiData(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, roleDataSpan: PySide6.QtCore.QModelRoleDataSpan | PySide6.QtCore.QModelRoleData, /) -> None: ...
    @typing.overload
    def parent(self, /) -> PySide6.QtCore.QObject: ...
    @typing.overload
    def parent(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, /) -> PySide6.QtCore.QModelIndex: ...
    def roleNames(self, /) -> typing.Dict[int, PySide6.QtCore.QByteArray]: ...
    def rootCacheSize(self, /) -> int: ...
    def rowCount(self, /, parent: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex = ...) -> int: ...
    def selectionModel(self, /) -> PySide6.QtCore.QItemSelectionModel: ...
    def setData(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, value: typing.Any, /, role: int = ...) -> bool: ...
    def setRootCacheSize(self, rootCacheSize: int, /) -> None: ...


class QConnectionAbstractServer(PySide6.QtCore.QObject):

    newConnection            : typing.ClassVar[Signal] = ... # newConnection()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def address(self, /) -> PySide6.QtCore.QUrl: ...
    def close(self, /) -> None: ...
    def configureNewConnection(self, /) -> PySide6.QtRemoteObjects.QtROServerIoDevice: ...
    def hasPendingConnections(self, /) -> bool: ...
    def listen(self, address: PySide6.QtCore.QUrl | str, /) -> bool: ...
    def nextPendingConnection(self, /) -> PySide6.QtRemoteObjects.QtROServerIoDevice: ...
    def serverError(self, /) -> PySide6.QtNetwork.QAbstractSocket.SocketError: ...


class QIntList: ...


class QRemoteObjectAbstractPersistedStore(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def restoreProperties(self, repName: str, repSig: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> typing.List[typing.Any]: ...
    def saveProperties(self, repName: str, repSig: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, values: collections.abc.Sequence[typing.Any], /) -> None: ...


class QRemoteObjectDynamicReplica(PySide6.QtRemoteObjects.QRemoteObjectReplica): ...


class QRemoteObjectHost(PySide6.QtRemoteObjects.QRemoteObjectHostBase):

    hostUrlChanged           : typing.ClassVar[Signal] = ... # hostUrlChanged()

    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, hostUrl: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, address: PySide6.QtCore.QUrl | str, parent: PySide6.QtCore.QObject, /, *, hostUrl: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, address: PySide6.QtCore.QUrl | str, /, registryAddress: PySide6.QtCore.QUrl | str = ..., allowedSchemas: PySide6.QtRemoteObjects.QRemoteObjectHostBase.AllowedSchemas = ..., parent: PySide6.QtCore.QObject | None = ..., *, hostUrl: PySide6.QtCore.QUrl | None = ...) -> None: ...

    def hostUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def setHostUrl(self, hostAddress: PySide6.QtCore.QUrl | str, /, allowedSchemas: PySide6.QtRemoteObjects.QRemoteObjectHostBase.AllowedSchemas = ...) -> bool: ...
    @staticmethod
    def setLocalServerOptions(options: PySide6.QtNetwork.QLocalServer.SocketOption, /) -> None: ...


class QRemoteObjectHostBase(PySide6.QtRemoteObjects.QRemoteObjectNode):

    class AllowedSchemas(enum.Enum):

        BuiltInSchemasOnly        = ...  # 0x0
        AllowExternalRegistration = ...  # 0x1


    def addHostSideConnection(self, ioDevice: PySide6.QtCore.QIODevice, /) -> None: ...
    def disableRemoting(self, remoteObject: PySide6.QtCore.QObject, /) -> bool: ...
    @typing.overload
    def enableRemoting(self, model: PySide6.QtCore.QAbstractItemModel, name: str, roles: collections.abc.Sequence[int], /, selectionModel: PySide6.QtCore.QItemSelectionModel | None = ...) -> bool: ...
    @typing.overload
    def enableRemoting(self, object: PySide6.QtCore.QObject, /, name: str = ...) -> bool: ...
    def hostUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def proxy(self, registryUrl: PySide6.QtCore.QUrl | str, /, hostUrl: PySide6.QtCore.QUrl | str = ...) -> bool: ...
    def reverseProxy(self, /) -> bool: ...
    def setHostUrl(self, hostAddress: PySide6.QtCore.QUrl | str, /, allowedSchemas: PySide6.QtRemoteObjects.QRemoteObjectHostBase.AllowedSchemas = ...) -> bool: ...
    def setName(self, name: str, /) -> None: ...


class QRemoteObjectNode(PySide6.QtCore.QObject):

    error                    : typing.ClassVar[Signal] = ... # error(QRemoteObjectNode::ErrorCode)
    heartbeatIntervalChanged : typing.ClassVar[Signal] = ... # heartbeatIntervalChanged(int)
    remoteObjectAdded        : typing.ClassVar[Signal] = ... # remoteObjectAdded(QRemoteObjectSourceLocation)
    remoteObjectRemoved      : typing.ClassVar[Signal] = ... # remoteObjectRemoved(QRemoteObjectSourceLocation)

    class ErrorCode(enum.Enum):

        NoError                   = ...  # 0x0
        RegistryNotAcquired       = ...  # 0x1
        RegistryAlreadyHosted     = ...  # 0x2
        NodeIsNoServer            = ...  # 0x3
        ServerAlreadyCreated      = ...  # 0x4
        UnintendedRegistryHosting = ...  # 0x5
        OperationNotValidOnClientNode = ...  # 0x6
        SourceNotRegistered       = ...  # 0x7
        MissingObjectName         = ...  # 0x8
        HostUrlInvalid            = ...  # 0x9
        ProtocolMismatch          = ...  # 0xa
        ListenFailed              = ...  # 0xb
        SocketAccessError         = ...  # 0xc


    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, registryUrl: PySide6.QtCore.QUrl | None = ..., persistedStore: PySide6.QtRemoteObjects.QRemoteObjectAbstractPersistedStore | None = ..., heartbeatInterval: int | None = ...) -> None: ...
    @typing.overload
    def __init__(self, registryAddress: PySide6.QtCore.QUrl | str, /, parent: PySide6.QtCore.QObject | None = ..., *, registryUrl: PySide6.QtCore.QUrl | None = ..., persistedStore: PySide6.QtRemoteObjects.QRemoteObjectAbstractPersistedStore | None = ..., heartbeatInterval: int | None = ...) -> None: ...

    def acquire(self, arg__1: type, /, name: object | None = ...) -> type: ...
    def acquireDynamic(self, name: str, /) -> PySide6.QtRemoteObjects.QRemoteObjectDynamicReplica: ...
    def acquireModel(self, name: str, /, action: PySide6.QtRemoteObjects.QtRemoteObjects.InitialAction = ..., rolesHint: collections.abc.Sequence[int] = ...) -> PySide6.QtRemoteObjects.QAbstractItemModelReplica: ...
    def addClientSideConnection(self, ioDevice: PySide6.QtCore.QIODevice, /) -> None: ...
    def connectToNode(self, address: PySide6.QtCore.QUrl | str, /) -> bool: ...
    def heartbeatInterval(self, /) -> int: ...
    def instances(self, typeName: str, /) -> typing.List[str]: ...
    def lastError(self, /) -> PySide6.QtRemoteObjects.QRemoteObjectNode.ErrorCode: ...
    def persistedStore(self, /) -> PySide6.QtRemoteObjects.QRemoteObjectAbstractPersistedStore: ...
    def registry(self, /) -> PySide6.QtRemoteObjects.QRemoteObjectRegistry: ...
    def registryUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def setHeartbeatInterval(self, interval: int, /) -> None: ...
    def setName(self, name: str, /) -> None: ...
    def setPersistedStore(self, persistedStore: PySide6.QtRemoteObjects.QRemoteObjectAbstractPersistedStore, /) -> None: ...
    def setRegistryUrl(self, registryAddress: PySide6.QtCore.QUrl | str, /) -> bool: ...
    def timerEvent(self, arg__1: PySide6.QtCore.QTimerEvent, /) -> None: ...
    def waitForRegistry(self, /, timeout: int = ...) -> bool: ...


class QRemoteObjectPendingCall(Shiboken.Object):

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        InvalidMessage            = ...  # 0x1


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtRemoteObjects.QRemoteObjectPendingCall, /) -> None: ...

    def error(self, /) -> PySide6.QtRemoteObjects.QRemoteObjectPendingCall.Error: ...
    @staticmethod
    def fromCompletedCall(returnValue: typing.Any, /) -> PySide6.QtRemoteObjects.QRemoteObjectPendingCall: ...
    def isFinished(self, /) -> bool: ...
    def returnValue(self, /) -> typing.Any: ...
    def waitForFinished(self, /, timeout: int = ...) -> bool: ...


class QRemoteObjectPendingCallWatcher(PySide6.QtCore.QObject, PySide6.QtRemoteObjects.QRemoteObjectPendingCall):

    finished                 : typing.ClassVar[Signal] = ... # finished(QRemoteObjectPendingCallWatcher*)

    def __init__(self, call: PySide6.QtRemoteObjects.QRemoteObjectPendingCall, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def isFinished(self, /) -> bool: ...
    def waitForFinished(self, /) -> None: ...


class QRemoteObjectRegistry(PySide6.QtRemoteObjects.QRemoteObjectReplica):

    remoteObjectAdded        : typing.ClassVar[Signal] = ... # remoteObjectAdded(QRemoteObjectSourceLocation)
    remoteObjectRemoved      : typing.ClassVar[Signal] = ... # remoteObjectRemoved(QRemoteObjectSourceLocation)
    def addSource(self, entry: typing.Tuple[str, PySide6.QtRemoteObjects.QRemoteObjectSourceLocationInfo], /) -> None: ...
    def initialize(self, /) -> None: ...
    def pushToRegistryIfNeeded(self, /) -> None: ...
    @staticmethod
    def registerMetatypes() -> None: ...
    def removeSource(self, entry: typing.Tuple[str, PySide6.QtRemoteObjects.QRemoteObjectSourceLocationInfo], /) -> None: ...
    def sourceLocations(self, /) -> typing.Dict[str, PySide6.QtRemoteObjects.QRemoteObjectSourceLocationInfo]: ...


class QRemoteObjectRegistryHost(PySide6.QtRemoteObjects.QRemoteObjectHostBase):

    def __init__(self, /, registryAddress: PySide6.QtCore.QUrl | str = ..., parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def setRegistryUrl(self, registryUrl: PySide6.QtCore.QUrl | str, /) -> bool: ...


class QRemoteObjectReplica(PySide6.QtCore.QObject):

    initialized              : typing.ClassVar[Signal] = ... # initialized()
    notified                 : typing.ClassVar[Signal] = ... # notified()
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(State,State)

    class ConstructorType(enum.IntEnum):

        DefaultConstructor        = ...  # 0x0
        ConstructWithNode         = ...  # 0x1

    class State(enum.Enum):

        Uninitialized             = ...  # 0x0
        Default                   = ...  # 0x1
        Valid                     = ...  # 0x2
        Suspect                   = ...  # 0x3
        SignatureMismatch         = ...  # 0x4


    def __init__(self, /, t: PySide6.QtRemoteObjects.QRemoteObjectReplica.ConstructorType = ..., *, node: PySide6.QtRemoteObjects.QRemoteObjectNode | None = ..., state: PySide6.QtRemoteObjects.QRemoteObjectReplica.State | None = ...) -> None: ...

    def initialize(self, /) -> None: ...
    def initializeNode(self, node: PySide6.QtRemoteObjects.QRemoteObjectNode, /, name: str = ...) -> None: ...
    def isInitialized(self, /) -> bool: ...
    def isReplicaValid(self, /) -> bool: ...
    def node(self, /) -> PySide6.QtRemoteObjects.QRemoteObjectNode: ...
    def persistProperties(self, repName: str, repSig: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, props: collections.abc.Sequence[typing.Any], /) -> None: ...
    def propAsVariant(self, i: int, /) -> typing.Any: ...
    def retrieveProperties(self, repName: str, repSig: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> typing.List[typing.Any]: ...
    def send(self, call: PySide6.QtCore.QMetaObject.Call, index: int, args: collections.abc.Sequence[typing.Any], /) -> None: ...
    def sendWithReply(self, call: PySide6.QtCore.QMetaObject.Call, index: int, args: collections.abc.Sequence[typing.Any], /) -> PySide6.QtRemoteObjects.QRemoteObjectPendingCall: ...
    def setChild(self, i: int, arg__2: typing.Any, /) -> None: ...
    def setNode(self, node: PySide6.QtRemoteObjects.QRemoteObjectNode, /) -> None: ...
    def state(self, /) -> PySide6.QtRemoteObjects.QRemoteObjectReplica.State: ...
    def waitForSource(self, /, timeout: int = ...) -> bool: ...


class QRemoteObjectSettingsStore(PySide6.QtRemoteObjects.QRemoteObjectAbstractPersistedStore):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def restoreProperties(self, repName: str, repSig: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> typing.List[typing.Any]: ...
    def saveProperties(self, repName: str, repSig: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, values: collections.abc.Sequence[typing.Any], /) -> None: ...


class QRemoteObjectSourceLocationInfo(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QRemoteObjectSourceLocationInfo: PySide6.QtRemoteObjects.QRemoteObjectSourceLocationInfo, /) -> None: ...
    @typing.overload
    def __init__(self, typeName_: str, hostUrl_: PySide6.QtCore.QUrl | str, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtRemoteObjects.QRemoteObjectSourceLocationInfo, /) -> bool: ...
    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __ne__(self, other: PySide6.QtRemoteObjects.QRemoteObjectSourceLocationInfo, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...


class QtROClientFactory(Shiboken.Object):
    def create(self, url: PySide6.QtCore.QUrl | str, /, parent: PySide6.QtCore.QObject | None = ...) -> PySide6.QtRemoteObjects.QtROClientIoDevice: ...
    @staticmethod
    def instance() -> PySide6.QtRemoteObjects.QtROClientFactory: ...
    def isValid(self, url: PySide6.QtCore.QUrl | str, /) -> bool: ...


class QtROClientIoDevice(PySide6.QtRemoteObjects.QtROIoDeviceBase):

    setError                 : typing.ClassVar[Signal] = ... # setError(QRemoteObjectNode::ErrorCode)
    shouldReconnect          : typing.ClassVar[Signal] = ... # shouldReconnect(QtROClientIoDevice*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def connectToServer(self, /) -> None: ...
    def deviceType(self, /) -> str: ...
    def disconnectFromServer(self, /) -> None: ...
    def doDisconnectFromServer(self, /) -> None: ...
    def setUrl(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QtROIoDeviceBase(PySide6.QtCore.QObject):

    disconnected             : typing.ClassVar[Signal] = ... # disconnected()
    readyRead                : typing.ClassVar[Signal] = ... # readyRead()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def addSource(self, arg__1: str, /) -> None: ...
    def bytesAvailable(self, /) -> int: ...
    def close(self, /) -> None: ...
    def connection(self, /) -> PySide6.QtCore.QIODevice: ...
    def deviceType(self, /) -> str: ...
    def doClose(self, /) -> None: ...
    def initializeDataStream(self, /) -> None: ...
    def isClosing(self, /) -> bool: ...
    def isOpen(self, /) -> bool: ...
    def read(self, arg__1: PySide6.QtRemoteObjects.QtRemoteObjects.QRemoteObjectPacketTypeEnum, arg__2: str, /) -> bool: ...
    def remoteObjects(self, /) -> typing.Set[str]: ...
    def removeSource(self, arg__1: str, /) -> None: ...
    @typing.overload
    def write(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def write(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, arg__2: int, /) -> None: ...


class QtROServerFactory(Shiboken.Object):
    def create(self, url: PySide6.QtCore.QUrl | str, /, parent: PySide6.QtCore.QObject | None = ...) -> PySide6.QtRemoteObjects.QConnectionAbstractServer: ...
    @staticmethod
    def instance() -> PySide6.QtRemoteObjects.QtROServerFactory: ...
    def isValid(self, url: PySide6.QtCore.QUrl | str, /) -> bool: ...


class QtROServerIoDevice(PySide6.QtRemoteObjects.QtROIoDeviceBase):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def deviceType(self, /) -> str: ...


class QtRemoteObjects(Shiboken.Object):

    class InitialAction(enum.Enum):

        FetchRootSize             = ...  # 0x0
        PrefetchData              = ...  # 0x1

    class QRemoteObjectPacketTypeEnum(enum.Enum):

        Invalid                   = ...  # 0x0
        Handshake                 = ...  # 0x1
        InitPacket                = ...  # 0x2
        InitDynamicPacket         = ...  # 0x3
        AddObject                 = ...  # 0x4
        RemoveObject              = ...  # 0x5
        InvokePacket              = ...  # 0x6
        InvokeReplyPacket         = ...  # 0x7
        PropertyChangePacket      = ...  # 0x8
        ObjectList                = ...  # 0x9
        Ping                      = ...  # 0xa
        Pong                      = ...  # 0xb


    @typing.overload
    @staticmethod
    def copyStoredProperties(mo: PySide6.QtCore.QMetaObject, src: PySide6.QtCore.QDataStream, dst: int, /) -> None: ...
    @typing.overload
    @staticmethod
    def copyStoredProperties(mo: PySide6.QtCore.QMetaObject, src: int, dst: PySide6.QtCore.QDataStream, /) -> None: ...
    @typing.overload
    @staticmethod
    def copyStoredProperties(mo: PySide6.QtCore.QMetaObject, src: int, dst: int, /) -> None: ...


class RepFile:

    def __init__(self, content: str, /) -> None: ...


# eof
