# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtStateMachine, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtStateMachine`

import PySide6.QtStateMachine
import PySide6.QtCore
import PySide6.QtGui

import enum
import typing
import collections
from PySide6.QtCore import Signal


class QAbstractState(PySide6.QtCore.QObject):

    activeChanged            : typing.ClassVar[Signal] = ... # activeChanged(bool)
    entered                  : typing.ClassVar[Signal] = ... # entered()
    exited                   : typing.ClassVar[Signal] = ... # exited()

    def __init__(self, /, parent: PySide6.QtStateMachine.QState | None = ..., *, active: bool | None = ...) -> None: ...

    def active(self, /) -> bool: ...
    def event(self, e: PySide6.QtCore.QEvent, /) -> bool: ...
    def machine(self, /) -> PySide6.QtStateMachine.QStateMachine: ...
    def onEntry(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def onExit(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def parentState(self, /) -> PySide6.QtStateMachine.QState: ...


class QAbstractTransition(PySide6.QtCore.QObject):

    targetStateChanged       : typing.ClassVar[Signal] = ... # targetStateChanged()
    targetStatesChanged      : typing.ClassVar[Signal] = ... # targetStatesChanged()
    triggered                : typing.ClassVar[Signal] = ... # triggered()

    class TransitionType(enum.Enum):

        ExternalTransition        = ...  # 0x0
        InternalTransition        = ...  # 0x1


    def __init__(self, /, sourceState: PySide6.QtStateMachine.QState | None = ..., *, targetState: PySide6.QtStateMachine.QAbstractState | None = ..., targetStates: collections.abc.Sequence[PySide6.QtStateMachine.QAbstractState] | None = ..., transitionType: PySide6.QtStateMachine.QAbstractTransition.TransitionType | None = ...) -> None: ...

    def addAnimation(self, animation: PySide6.QtCore.QAbstractAnimation, /) -> None: ...
    def animations(self, /) -> typing.List[PySide6.QtCore.QAbstractAnimation]: ...
    def event(self, e: PySide6.QtCore.QEvent, /) -> bool: ...
    def eventTest(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def machine(self, /) -> PySide6.QtStateMachine.QStateMachine: ...
    def onTransition(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def removeAnimation(self, animation: PySide6.QtCore.QAbstractAnimation, /) -> None: ...
    def setTargetState(self, target: PySide6.QtStateMachine.QAbstractState, /) -> None: ...
    def setTargetStates(self, targets: collections.abc.Sequence[PySide6.QtStateMachine.QAbstractState], /) -> None: ...
    def setTransitionType(self, type: PySide6.QtStateMachine.QAbstractTransition.TransitionType, /) -> None: ...
    def sourceState(self, /) -> PySide6.QtStateMachine.QState: ...
    def targetState(self, /) -> PySide6.QtStateMachine.QAbstractState: ...
    def targetStates(self, /) -> typing.List[PySide6.QtStateMachine.QAbstractState]: ...
    def transitionType(self, /) -> PySide6.QtStateMachine.QAbstractTransition.TransitionType: ...


class QEventTransition(PySide6.QtStateMachine.QAbstractTransition):

    @typing.overload
    def __init__(self, object: PySide6.QtCore.QObject, type: PySide6.QtCore.QEvent.Type, /, sourceState: PySide6.QtStateMachine.QState | None = ..., *, eventSource: PySide6.QtCore.QObject | None = ..., eventType: PySide6.QtCore.QEvent.Type | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, sourceState: PySide6.QtStateMachine.QState | None = ..., *, eventSource: PySide6.QtCore.QObject | None = ..., eventType: PySide6.QtCore.QEvent.Type | None = ...) -> None: ...

    def event(self, e: PySide6.QtCore.QEvent, /) -> bool: ...
    def eventSource(self, /) -> PySide6.QtCore.QObject: ...
    def eventTest(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def eventType(self, /) -> PySide6.QtCore.QEvent.Type: ...
    def onTransition(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def setEventSource(self, object: PySide6.QtCore.QObject, /) -> None: ...
    def setEventType(self, type: PySide6.QtCore.QEvent.Type, /) -> None: ...


class QFinalState(PySide6.QtStateMachine.QAbstractState):

    def __init__(self, /, parent: PySide6.QtStateMachine.QState | None = ...) -> None: ...

    def event(self, e: PySide6.QtCore.QEvent, /) -> bool: ...
    def onEntry(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def onExit(self, event: PySide6.QtCore.QEvent, /) -> None: ...


class QHistoryState(PySide6.QtStateMachine.QAbstractState):

    defaultStateChanged      : typing.ClassVar[Signal] = ... # defaultStateChanged()
    defaultTransitionChanged : typing.ClassVar[Signal] = ... # defaultTransitionChanged()
    historyTypeChanged       : typing.ClassVar[Signal] = ... # historyTypeChanged()

    class HistoryType(enum.Enum):

        ShallowHistory            = ...  # 0x0
        DeepHistory               = ...  # 0x1


    @typing.overload
    def __init__(self, type: PySide6.QtStateMachine.QHistoryState.HistoryType, /, parent: PySide6.QtStateMachine.QState | None = ..., *, defaultState: PySide6.QtStateMachine.QAbstractState | None = ..., defaultTransition: PySide6.QtStateMachine.QAbstractTransition | None = ..., historyType: PySide6.QtStateMachine.QHistoryState.HistoryType | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtStateMachine.QState | None = ..., *, defaultState: PySide6.QtStateMachine.QAbstractState | None = ..., defaultTransition: PySide6.QtStateMachine.QAbstractTransition | None = ..., historyType: PySide6.QtStateMachine.QHistoryState.HistoryType | None = ...) -> None: ...

    def defaultState(self, /) -> PySide6.QtStateMachine.QAbstractState: ...
    def defaultTransition(self, /) -> PySide6.QtStateMachine.QAbstractTransition: ...
    def event(self, e: PySide6.QtCore.QEvent, /) -> bool: ...
    def historyType(self, /) -> PySide6.QtStateMachine.QHistoryState.HistoryType: ...
    def onEntry(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def onExit(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def setDefaultState(self, state: PySide6.QtStateMachine.QAbstractState, /) -> None: ...
    def setDefaultTransition(self, transition: PySide6.QtStateMachine.QAbstractTransition, /) -> None: ...
    def setHistoryType(self, type: PySide6.QtStateMachine.QHistoryState.HistoryType, /) -> None: ...


class QIntList: ...


class QKeyEventTransition(PySide6.QtStateMachine.QEventTransition):

    @typing.overload
    def __init__(self, object: PySide6.QtCore.QObject, type: PySide6.QtCore.QEvent.Type, key: int, /, sourceState: PySide6.QtStateMachine.QState | None = ..., *, modifierMask: PySide6.QtCore.Qt.KeyboardModifier | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, sourceState: PySide6.QtStateMachine.QState | None = ..., *, key: int | None = ..., modifierMask: PySide6.QtCore.Qt.KeyboardModifier | None = ...) -> None: ...

    def eventTest(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def key(self, /) -> int: ...
    def modifierMask(self, /) -> PySide6.QtCore.Qt.KeyboardModifier: ...
    def onTransition(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def setKey(self, key: int, /) -> None: ...
    def setModifierMask(self, modifiers: PySide6.QtCore.Qt.KeyboardModifier, /) -> None: ...


class QMouseEventTransition(PySide6.QtStateMachine.QEventTransition):

    @typing.overload
    def __init__(self, object: PySide6.QtCore.QObject, type: PySide6.QtCore.QEvent.Type, button: PySide6.QtCore.Qt.MouseButton, /, sourceState: PySide6.QtStateMachine.QState | None = ..., *, modifierMask: PySide6.QtCore.Qt.KeyboardModifier | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, sourceState: PySide6.QtStateMachine.QState | None = ..., *, button: PySide6.QtCore.Qt.MouseButton | None = ..., modifierMask: PySide6.QtCore.Qt.KeyboardModifier | None = ...) -> None: ...

    def button(self, /) -> PySide6.QtCore.Qt.MouseButton: ...
    def eventTest(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def hitTestPath(self, /) -> PySide6.QtGui.QPainterPath: ...
    def modifierMask(self, /) -> PySide6.QtCore.Qt.KeyboardModifier: ...
    def onTransition(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def setButton(self, button: PySide6.QtCore.Qt.MouseButton, /) -> None: ...
    def setHitTestPath(self, path: PySide6.QtGui.QPainterPath, /) -> None: ...
    def setModifierMask(self, modifiers: PySide6.QtCore.Qt.KeyboardModifier, /) -> None: ...


class QSignalTransition(PySide6.QtStateMachine.QAbstractTransition):

    senderObjectChanged      : typing.ClassVar[Signal] = ... # senderObjectChanged()
    signalChanged            : typing.ClassVar[Signal] = ... # signalChanged()

    @typing.overload
    def __init__(self, signal: PySide6.QtCore.QByteArray, /, state: PySide6.QtStateMachine.QState | None = ..., *, senderObject: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, sender: PySide6.QtCore.QObject, signal: PySide6.QtCore.QByteArray, /, sourceState: PySide6.QtStateMachine.QState | None = ..., *, senderObject: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, sourceState: PySide6.QtStateMachine.QState | None = ..., *, senderObject: PySide6.QtCore.QObject | None = ..., signal: PySide6.QtCore.QByteArray | None = ...) -> None: ...

    def event(self, e: PySide6.QtCore.QEvent, /) -> bool: ...
    def eventTest(self, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def onTransition(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def senderObject(self, /) -> PySide6.QtCore.QObject: ...
    def setSenderObject(self, sender: PySide6.QtCore.QObject, /) -> None: ...
    def setSignal(self, signal: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def signal(self, /) -> PySide6.QtCore.QByteArray: ...


class QState(PySide6.QtStateMachine.QAbstractState):

    childModeChanged         : typing.ClassVar[Signal] = ... # childModeChanged()
    errorStateChanged        : typing.ClassVar[Signal] = ... # errorStateChanged()
    finished                 : typing.ClassVar[Signal] = ... # finished()
    initialStateChanged      : typing.ClassVar[Signal] = ... # initialStateChanged()
    propertiesAssigned       : typing.ClassVar[Signal] = ... # propertiesAssigned()

    class ChildMode(enum.Enum):

        ExclusiveStates           = ...  # 0x0
        ParallelStates            = ...  # 0x1

    class RestorePolicy(enum.Enum):

        DontRestoreProperties     = ...  # 0x0
        RestoreProperties         = ...  # 0x1


    @typing.overload
    def __init__(self, childMode: PySide6.QtStateMachine.QState.ChildMode, /, parent: PySide6.QtStateMachine.QState | None = ..., *, initialState: PySide6.QtStateMachine.QAbstractState | None = ..., errorState: PySide6.QtStateMachine.QAbstractState | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtStateMachine.QState | None = ..., *, initialState: PySide6.QtStateMachine.QAbstractState | None = ..., errorState: PySide6.QtStateMachine.QAbstractState | None = ..., childMode: PySide6.QtStateMachine.QState.ChildMode | None = ...) -> None: ...

    @typing.overload
    def addTransition(self, target: PySide6.QtStateMachine.QAbstractState, /) -> PySide6.QtStateMachine.QAbstractTransition: ...
    @typing.overload
    def addTransition(self, transition: PySide6.QtStateMachine.QAbstractTransition, /) -> None: ...
    @typing.overload
    def addTransition(self, sender: PySide6.QtCore.QObject, signal: str, target: PySide6.QtStateMachine.QAbstractState, /) -> PySide6.QtStateMachine.QSignalTransition: ...
    @typing.overload
    def addTransition(self, signal: object, arg__2: PySide6.QtStateMachine.QAbstractState, /) -> PySide6.QtStateMachine.QSignalTransition: ...
    def assignProperty(self, object: PySide6.QtCore.QObject, name: str, value: typing.Any, /) -> None: ...
    def childMode(self, /) -> PySide6.QtStateMachine.QState.ChildMode: ...
    def errorState(self, /) -> PySide6.QtStateMachine.QAbstractState: ...
    def event(self, e: PySide6.QtCore.QEvent, /) -> bool: ...
    def initialState(self, /) -> PySide6.QtStateMachine.QAbstractState: ...
    def onEntry(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def onExit(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def removeTransition(self, transition: PySide6.QtStateMachine.QAbstractTransition, /) -> None: ...
    def setChildMode(self, mode: PySide6.QtStateMachine.QState.ChildMode, /) -> None: ...
    def setErrorState(self, state: PySide6.QtStateMachine.QAbstractState, /) -> None: ...
    def setInitialState(self, state: PySide6.QtStateMachine.QAbstractState, /) -> None: ...
    def transitions(self, /) -> typing.List[PySide6.QtStateMachine.QAbstractTransition]: ...


class QStateMachine(PySide6.QtStateMachine.QState):

    runningChanged           : typing.ClassVar[Signal] = ... # runningChanged(bool)
    started                  : typing.ClassVar[Signal] = ... # started()
    stopped                  : typing.ClassVar[Signal] = ... # stopped()

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        NoInitialStateError       = ...  # 0x1
        NoDefaultStateInHistoryStateError = ...  # 0x2
        NoCommonAncestorForTransitionError = ...  # 0x3
        StateMachineChildModeSetToParallelError = ...  # 0x4

    class EventPriority(enum.Enum):

        NormalPriority            = ...  # 0x0
        HighPriority              = ...  # 0x1

    class SignalEvent(PySide6.QtCore.QEvent):

        def __init__(self, sender: PySide6.QtCore.QObject, signalIndex: int, arguments: collections.abc.Sequence[typing.Any], /) -> None: ...

        def arguments(self, /) -> typing.List[typing.Any]: ...
        def sender(self, /) -> PySide6.QtCore.QObject: ...
        def signalIndex(self, /) -> int: ...

    class WrappedEvent(PySide6.QtCore.QEvent):

        def __init__(self, object: PySide6.QtCore.QObject, event: PySide6.QtCore.QEvent, /) -> None: ...

        def event(self, /) -> PySide6.QtCore.QEvent: ...
        def object(self, /) -> PySide6.QtCore.QObject: ...


    @typing.overload
    def __init__(self, childMode: PySide6.QtStateMachine.QState.ChildMode, /, parent: PySide6.QtCore.QObject | None = ..., *, errorString: str | None = ..., globalRestorePolicy: PySide6.QtStateMachine.QState.RestorePolicy | None = ..., running: bool | None = ..., animated: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, errorString: str | None = ..., globalRestorePolicy: PySide6.QtStateMachine.QState.RestorePolicy | None = ..., running: bool | None = ..., animated: bool | None = ...) -> None: ...

    def addDefaultAnimation(self, animation: PySide6.QtCore.QAbstractAnimation, /) -> None: ...
    def addState(self, state: PySide6.QtStateMachine.QAbstractState, /) -> None: ...
    def beginMicrostep(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def beginSelectTransitions(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def cancelDelayedEvent(self, id: int, /) -> bool: ...
    def clearError(self, /) -> None: ...
    def configuration(self, /) -> typing.Set[PySide6.QtStateMachine.QAbstractState]: ...
    def defaultAnimations(self, /) -> typing.List[PySide6.QtCore.QAbstractAnimation]: ...
    def endMicrostep(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def endSelectTransitions(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def error(self, /) -> PySide6.QtStateMachine.QStateMachine.Error: ...
    def errorString(self, /) -> str: ...
    def event(self, e: PySide6.QtCore.QEvent, /) -> bool: ...
    def eventFilter(self, watched: PySide6.QtCore.QObject, event: PySide6.QtCore.QEvent, /) -> bool: ...
    def globalRestorePolicy(self, /) -> PySide6.QtStateMachine.QState.RestorePolicy: ...
    def isAnimated(self, /) -> bool: ...
    def isRunning(self, /) -> bool: ...
    def onEntry(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def onExit(self, event: PySide6.QtCore.QEvent, /) -> None: ...
    def postDelayedEvent(self, event: PySide6.QtCore.QEvent, delay: int, /) -> int: ...
    def postEvent(self, event: PySide6.QtCore.QEvent, /, priority: PySide6.QtStateMachine.QStateMachine.EventPriority = ...) -> None: ...
    def removeDefaultAnimation(self, animation: PySide6.QtCore.QAbstractAnimation, /) -> None: ...
    def removeState(self, state: PySide6.QtStateMachine.QAbstractState, /) -> None: ...
    def setAnimated(self, enabled: bool, /) -> None: ...
    def setGlobalRestorePolicy(self, restorePolicy: PySide6.QtStateMachine.QState.RestorePolicy, /) -> None: ...
    def setRunning(self, running: bool, /) -> None: ...
    def start(self, /) -> None: ...
    def stop(self, /) -> None: ...


# eof
