# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtSvg, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtSvg`

import PySide6.QtSvg
import PySide6.QtCore
import PySide6.QtGui

import enum
import typing
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QIntList: ...


class QSvgGenerator(PySide6.QtGui.QPaintDevice):

    class SvgVersion(enum.Enum):

        SvgTiny12                 = ...  # 0x0
        Svg11                     = ...  # 0x1


    @typing.overload
    def __init__(self, version: PySide6.QtSvg.QSvgGenerator.SvgVersion, /, *, size: PySide6.QtCore.QSize | None = ..., viewBox: PySide6.QtCore.QRectF | None = ..., title: str | None = ..., description: str | None = ..., fileName: str | None = ..., outputDevice: PySide6.QtCore.QIODevice | None = ..., resolution: int | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, size: PySide6.QtCore.QSize | None = ..., viewBox: PySide6.QtCore.QRectF | None = ..., title: str | None = ..., description: str | None = ..., fileName: str | None = ..., outputDevice: PySide6.QtCore.QIODevice | None = ..., resolution: int | None = ...) -> None: ...

    def description(self, /) -> str: ...
    def fileName(self, /) -> str: ...
    def metric(self, metric: PySide6.QtGui.QPaintDevice.PaintDeviceMetric, /) -> int: ...
    def outputDevice(self, /) -> PySide6.QtCore.QIODevice: ...
    def paintEngine(self, /) -> PySide6.QtGui.QPaintEngine: ...
    def resolution(self, /) -> int: ...
    def setDescription(self, description: str, /) -> None: ...
    def setFileName(self, fileName: str, /) -> None: ...
    def setOutputDevice(self, outputDevice: PySide6.QtCore.QIODevice, /) -> None: ...
    def setResolution(self, dpi: int, /) -> None: ...
    def setSize(self, size: PySide6.QtCore.QSize, /) -> None: ...
    def setTitle(self, title: str, /) -> None: ...
    @typing.overload
    def setViewBox(self, viewBox: PySide6.QtCore.QRect, /) -> None: ...
    @typing.overload
    def setViewBox(self, viewBox: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def size(self, /) -> PySide6.QtCore.QSize: ...
    def svgVersion(self, /) -> PySide6.QtSvg.QSvgGenerator.SvgVersion: ...
    def title(self, /) -> str: ...
    def viewBox(self, /) -> PySide6.QtCore.QRect: ...
    def viewBoxF(self, /) -> PySide6.QtCore.QRectF: ...


class QSvgRenderer(PySide6.QtCore.QObject):

    repaintNeeded            : typing.ClassVar[Signal] = ... # repaintNeeded()

    @typing.overload
    def __init__(self, contents: PySide6.QtCore.QXmlStreamReader, /, parent: PySide6.QtCore.QObject | None = ..., *, viewBox: PySide6.QtCore.QRectF | None = ..., framesPerSecond: int | None = ..., currentFrame: int | None = ..., aspectRatioMode: PySide6.QtCore.Qt.AspectRatioMode | None = ..., options: PySide6.QtSvg.QtSvg.Option | None = ..., animationEnabled: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, filename: str, /, parent: PySide6.QtCore.QObject | None = ..., *, viewBox: PySide6.QtCore.QRectF | None = ..., framesPerSecond: int | None = ..., currentFrame: int | None = ..., aspectRatioMode: PySide6.QtCore.Qt.AspectRatioMode | None = ..., options: PySide6.QtSvg.QtSvg.Option | None = ..., animationEnabled: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, viewBox: PySide6.QtCore.QRectF | None = ..., framesPerSecond: int | None = ..., currentFrame: int | None = ..., aspectRatioMode: PySide6.QtCore.Qt.AspectRatioMode | None = ..., options: PySide6.QtSvg.QtSvg.Option | None = ..., animationEnabled: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, contents: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, parent: PySide6.QtCore.QObject | None = ..., *, viewBox: PySide6.QtCore.QRectF | None = ..., framesPerSecond: int | None = ..., currentFrame: int | None = ..., aspectRatioMode: PySide6.QtCore.Qt.AspectRatioMode | None = ..., options: PySide6.QtSvg.QtSvg.Option | None = ..., animationEnabled: bool | None = ...) -> None: ...

    def animated(self, /) -> bool: ...
    def animationDuration(self, /) -> int: ...
    def aspectRatioMode(self, /) -> PySide6.QtCore.Qt.AspectRatioMode: ...
    def boundsOnElement(self, id: str, /) -> PySide6.QtCore.QRectF: ...
    def currentFrame(self, /) -> int: ...
    def defaultSize(self, /) -> PySide6.QtCore.QSize: ...
    def elementExists(self, id: str, /) -> bool: ...
    def framesPerSecond(self, /) -> int: ...
    def isAnimationEnabled(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    @typing.overload
    def load(self, contents: PySide6.QtCore.QXmlStreamReader, /) -> bool: ...
    @typing.overload
    def load(self, filename: str, /) -> bool: ...
    @typing.overload
    def load(self, contents: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> bool: ...
    def options(self, /) -> PySide6.QtSvg.QtSvg.Option: ...
    @typing.overload
    def render(self, p: PySide6.QtGui.QPainter, /) -> None: ...
    @typing.overload
    def render(self, p: PySide6.QtGui.QPainter, elementId: str, /, bounds: PySide6.QtCore.QRectF | PySide6.QtCore.QRect = ...) -> None: ...
    @typing.overload
    def render(self, p: PySide6.QtGui.QPainter, bounds: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def setAnimationEnabled(self, enable: bool, /) -> None: ...
    def setAspectRatioMode(self, mode: PySide6.QtCore.Qt.AspectRatioMode, /) -> None: ...
    def setCurrentFrame(self, arg__1: int, /) -> None: ...
    @staticmethod
    def setDefaultOptions(flags: PySide6.QtSvg.QtSvg.Option, /) -> None: ...
    def setFramesPerSecond(self, num: int, /) -> None: ...
    def setOptions(self, flags: PySide6.QtSvg.QtSvg.Option, /) -> None: ...
    @typing.overload
    def setViewBox(self, viewbox: PySide6.QtCore.QRect, /) -> None: ...
    @typing.overload
    def setViewBox(self, viewbox: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def transformForElement(self, id: str, /) -> PySide6.QtGui.QTransform: ...
    def viewBox(self, /) -> PySide6.QtCore.QRect: ...
    def viewBoxF(self, /) -> PySide6.QtCore.QRectF: ...


class QtSvg(Shiboken.Object):

    class Option(enum.Flag):

        NoOption                  = ...  # 0x0
        Tiny12FeaturesOnly        = ...  # 0x1
        AssumeTrustedSource       = ...  # 0x2
        DisableSMILAnimations     = ...  # 0x10
        DisableCSSAnimations      = ...  # 0x20
        DisableAnimations         = ...  # 0xf0


# eof
