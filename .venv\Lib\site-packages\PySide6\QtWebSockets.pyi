# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtWebSockets, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtWebSockets`

import PySide6.QtWebSockets
import PySide6.QtCore
import PySide6.QtNetwork

import os
import enum
import typing
import collections
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QIntList: ...


class QMaskGenerator(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def nextMask(self, /) -> int: ...
    def seed(self, /) -> bool: ...


class QWebSocket(PySide6.QtCore.QObject):

    aboutToClose             : typing.ClassVar[Signal] = ... # aboutToClose()
    alertReceived            : typing.ClassVar[Signal] = ... # alertReceived(QSsl::AlertLevel,QSsl::AlertType,QString)
    alertSent                : typing.ClassVar[Signal] = ... # alertSent(QSsl::AlertLevel,QSsl::AlertType,QString)
    authenticationRequired   : typing.ClassVar[Signal] = ... # authenticationRequired(QAuthenticator*)
    binaryFrameReceived      : typing.ClassVar[Signal] = ... # binaryFrameReceived(QByteArray,bool)
    binaryMessageReceived    : typing.ClassVar[Signal] = ... # binaryMessageReceived(QByteArray)
    bytesWritten             : typing.ClassVar[Signal] = ... # bytesWritten(qlonglong)
    connected                : typing.ClassVar[Signal] = ... # connected()
    disconnected             : typing.ClassVar[Signal] = ... # disconnected()
    error                    : typing.ClassVar[Signal] = ... # error(QAbstractSocket::SocketError)
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QAbstractSocket::SocketError)
    handshakeInterruptedOnError: typing.ClassVar[Signal] = ... # handshakeInterruptedOnError(QSslError)
    peerVerifyError          : typing.ClassVar[Signal] = ... # peerVerifyError(QSslError)
    pong                     : typing.ClassVar[Signal] = ... # pong(qulonglong,QByteArray)
    preSharedKeyAuthenticationRequired: typing.ClassVar[Signal] = ... # preSharedKeyAuthenticationRequired(QSslPreSharedKeyAuthenticator*)
    proxyAuthenticationRequired: typing.ClassVar[Signal] = ... # proxyAuthenticationRequired(QNetworkProxy,QAuthenticator*)
    readChannelFinished      : typing.ClassVar[Signal] = ... # readChannelFinished()
    sslErrors                : typing.ClassVar[Signal] = ... # sslErrors(QList<QSslError>)
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QAbstractSocket::SocketState)
    textFrameReceived        : typing.ClassVar[Signal] = ... # textFrameReceived(QString,bool)
    textMessageReceived      : typing.ClassVar[Signal] = ... # textMessageReceived(QString)

    def __init__(self, /, origin: str = ..., version: PySide6.QtWebSockets.QWebSocketProtocol.Version = ..., parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def abort(self, /) -> None: ...
    def bytesToWrite(self, /) -> int: ...
    def close(self, /, closeCode: PySide6.QtWebSockets.QWebSocketProtocol.CloseCode = ..., reason: str = ...) -> None: ...
    def closeCode(self, /) -> PySide6.QtWebSockets.QWebSocketProtocol.CloseCode: ...
    def closeReason(self, /) -> str: ...
    def continueInterruptedHandshake(self, /) -> None: ...
    def errorString(self, /) -> str: ...
    def flush(self, /) -> bool: ...
    def handshakeOptions(self, /) -> PySide6.QtWebSockets.QWebSocketHandshakeOptions: ...
    @typing.overload
    def ignoreSslErrors(self, /) -> None: ...
    @typing.overload
    def ignoreSslErrors(self, errors: collections.abc.Sequence[PySide6.QtNetwork.QSslError], /) -> None: ...
    def isValid(self, /) -> bool: ...
    def localAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def localPort(self, /) -> int: ...
    def maskGenerator(self, /) -> PySide6.QtWebSockets.QMaskGenerator: ...
    def maxAllowedIncomingFrameSize(self, /) -> int: ...
    def maxAllowedIncomingMessageSize(self, /) -> int: ...
    @staticmethod
    def maxIncomingFrameSize() -> int: ...
    @staticmethod
    def maxIncomingMessageSize() -> int: ...
    @staticmethod
    def maxOutgoingFrameSize() -> int: ...
    @typing.overload
    def open(self, request: PySide6.QtNetwork.QNetworkRequest, /) -> None: ...
    @typing.overload
    def open(self, request: PySide6.QtNetwork.QNetworkRequest, options: PySide6.QtWebSockets.QWebSocketHandshakeOptions, /) -> None: ...
    @typing.overload
    def open(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    @typing.overload
    def open(self, url: PySide6.QtCore.QUrl | str, options: PySide6.QtWebSockets.QWebSocketHandshakeOptions, /) -> None: ...
    def origin(self, /) -> str: ...
    def outgoingFrameSize(self, /) -> int: ...
    def pauseMode(self, /) -> PySide6.QtNetwork.QAbstractSocket.PauseMode: ...
    def peerAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def peerName(self, /) -> str: ...
    def peerPort(self, /) -> int: ...
    def ping(self, /, payload: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> None: ...
    def proxy(self, /) -> PySide6.QtNetwork.QNetworkProxy: ...
    def readBufferSize(self, /) -> int: ...
    def request(self, /) -> PySide6.QtNetwork.QNetworkRequest: ...
    def requestUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def resourceName(self, /) -> str: ...
    def resume(self, /) -> None: ...
    def sendBinaryMessage(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> int: ...
    def sendTextMessage(self, message: str, /) -> int: ...
    def setMaskGenerator(self, maskGenerator: PySide6.QtWebSockets.QMaskGenerator, /) -> None: ...
    def setMaxAllowedIncomingFrameSize(self, maxAllowedIncomingFrameSize: int, /) -> None: ...
    def setMaxAllowedIncomingMessageSize(self, maxAllowedIncomingMessageSize: int, /) -> None: ...
    def setOutgoingFrameSize(self, outgoingFrameSize: int, /) -> None: ...
    def setPauseMode(self, pauseMode: PySide6.QtNetwork.QAbstractSocket.PauseMode, /) -> None: ...
    def setProxy(self, networkProxy: PySide6.QtNetwork.QNetworkProxy | PySide6.QtNetwork.QNetworkProxy.ProxyType, /) -> None: ...
    def setReadBufferSize(self, size: int, /) -> None: ...
    def setSslConfiguration(self, sslConfiguration: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def sslConfiguration(self, /) -> PySide6.QtNetwork.QSslConfiguration: ...
    def state(self, /) -> PySide6.QtNetwork.QAbstractSocket.SocketState: ...
    def subprotocol(self, /) -> str: ...
    def version(self, /) -> PySide6.QtWebSockets.QWebSocketProtocol.Version: ...


class QWebSocketCorsAuthenticator(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtWebSockets.QWebSocketCorsAuthenticator, /) -> None: ...
    @typing.overload
    def __init__(self, origin: str, /) -> None: ...

    def allowed(self, /) -> bool: ...
    def origin(self, /) -> str: ...
    def setAllowed(self, allowed: bool, /) -> None: ...
    def swap(self, other: PySide6.QtWebSockets.QWebSocketCorsAuthenticator, /) -> None: ...


class QWebSocketHandshakeOptions(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtWebSockets.QWebSocketHandshakeOptions, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtWebSockets.QWebSocketHandshakeOptions, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtWebSockets.QWebSocketHandshakeOptions, /) -> bool: ...
    def setSubprotocols(self, protocols: collections.abc.Sequence[str], /) -> None: ...
    def subprotocols(self, /) -> typing.List[str]: ...
    def swap(self, other: PySide6.QtWebSockets.QWebSocketHandshakeOptions, /) -> None: ...


class QWebSocketProtocol(Shiboken.Object):

    class CloseCode(enum.Enum):

        CloseCodeNormal           = ...  # 0x3e8
        CloseCodeGoingAway        = ...  # 0x3e9
        CloseCodeProtocolError    = ...  # 0x3ea
        CloseCodeDatatypeNotSupported = ...  # 0x3eb
        CloseCodeReserved1004     = ...  # 0x3ec
        CloseCodeMissingStatusCode = ...  # 0x3ed
        CloseCodeAbnormalDisconnection = ...  # 0x3ee
        CloseCodeWrongDatatype    = ...  # 0x3ef
        CloseCodePolicyViolated   = ...  # 0x3f0
        CloseCodeTooMuchData      = ...  # 0x3f1
        CloseCodeMissingExtension = ...  # 0x3f2
        CloseCodeBadOperation     = ...  # 0x3f3
        CloseCodeTlsHandshakeFailed = ...  # 0x3f7

    class Version(enum.Enum):

        VersionUnknown            = ...  # -1
        Version0                  = ...  # 0x0
        Version4                  = ...  # 0x4
        Version5                  = ...  # 0x5
        Version6                  = ...  # 0x6
        Version7                  = ...  # 0x7
        Version8                  = ...  # 0x8
        Version13                 = ...  # 0xd
        VersionLatest             = ...  # 0xd


class QWebSocketServer(PySide6.QtCore.QObject):

    acceptError              : typing.ClassVar[Signal] = ... # acceptError(QAbstractSocket::SocketError)
    alertReceived            : typing.ClassVar[Signal] = ... # alertReceived(QSsl::AlertLevel,QSsl::AlertType,QString)
    alertSent                : typing.ClassVar[Signal] = ... # alertSent(QSsl::AlertLevel,QSsl::AlertType,QString)
    closed                   : typing.ClassVar[Signal] = ... # closed()
    handshakeInterruptedOnError: typing.ClassVar[Signal] = ... # handshakeInterruptedOnError(QSslError)
    newConnection            : typing.ClassVar[Signal] = ... # newConnection()
    originAuthenticationRequired: typing.ClassVar[Signal] = ... # originAuthenticationRequired(QWebSocketCorsAuthenticator*)
    peerVerifyError          : typing.ClassVar[Signal] = ... # peerVerifyError(QSslError)
    preSharedKeyAuthenticationRequired: typing.ClassVar[Signal] = ... # preSharedKeyAuthenticationRequired(QSslPreSharedKeyAuthenticator*)
    serverError              : typing.ClassVar[Signal] = ... # serverError(QWebSocketProtocol::CloseCode)
    sslErrors                : typing.ClassVar[Signal] = ... # sslErrors(QList<QSslError>)

    class SslMode(enum.Enum):

        SecureMode                = ...  # 0x0
        NonSecureMode             = ...  # 0x1


    def __init__(self, serverName: str, secureMode: PySide6.QtWebSockets.QWebSocketServer.SslMode, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def close(self, /) -> None: ...
    def error(self, /) -> PySide6.QtWebSockets.QWebSocketProtocol.CloseCode: ...
    def errorString(self, /) -> str: ...
    def handleConnection(self, socket: PySide6.QtNetwork.QTcpSocket, /) -> None: ...
    def handshakeTimeout(self, /) -> int: ...
    def handshakeTimeoutMS(self, /) -> int: ...
    def hasPendingConnections(self, /) -> bool: ...
    def isListening(self, /) -> bool: ...
    def listen(self, /, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress = ..., port: int | None = ...) -> bool: ...
    def maxPendingConnections(self, /) -> int: ...
    def nativeDescriptor(self, /) -> int: ...
    def nextPendingConnection(self, /) -> PySide6.QtWebSockets.QWebSocket: ...
    def pauseAccepting(self, /) -> None: ...
    def proxy(self, /) -> PySide6.QtNetwork.QNetworkProxy: ...
    def resumeAccepting(self, /) -> None: ...
    def secureMode(self, /) -> PySide6.QtWebSockets.QWebSocketServer.SslMode: ...
    def serverAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def serverName(self, /) -> str: ...
    def serverPort(self, /) -> int: ...
    def serverUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def setHandshakeTimeout(self, msec: int, /) -> None: ...
    def setMaxPendingConnections(self, numConnections: int, /) -> None: ...
    def setNativeDescriptor(self, descriptor: int, /) -> bool: ...
    def setProxy(self, networkProxy: PySide6.QtNetwork.QNetworkProxy | PySide6.QtNetwork.QNetworkProxy.ProxyType, /) -> None: ...
    def setServerName(self, serverName: str, /) -> None: ...
    def setSocketDescriptor(self, socketDescriptor: int, /) -> bool: ...
    def setSslConfiguration(self, sslConfiguration: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def setSupportedSubprotocols(self, protocols: collections.abc.Sequence[str], /) -> None: ...
    def socketDescriptor(self, /) -> int: ...
    def sslConfiguration(self, /) -> PySide6.QtNetwork.QSslConfiguration: ...
    def supportedSubprotocols(self, /) -> typing.List[str]: ...
    def supportedVersions(self, /) -> typing.List[PySide6.QtWebSockets.QWebSocketProtocol.Version]: ...


# eof
