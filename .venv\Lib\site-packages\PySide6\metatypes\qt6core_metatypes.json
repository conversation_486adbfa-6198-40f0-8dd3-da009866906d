[{"classes": [{"className": "QAbstractAnimation", "enums": [{"isClass": false, "isFlag": false, "name": "Direction", "values": ["Forward", "Backward"]}, {"isClass": false, "isFlag": false, "name": "State", "values": ["Stopped", "Paused", "Running"]}], "lineNumber": 19, "object": true, "properties": [{"bindable": "bindableState", "constant": false, "designable": true, "final": false, "index": 0, "name": "state", "notify": "stateChanged", "read": "state", "required": false, "scriptable": true, "stored": true, "type": "State", "user": false}, {"bindable": "bindableLoopCount", "constant": false, "designable": true, "final": false, "index": 1, "name": "loopCount", "read": "loopCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoopCount"}, {"bindable": "bindableCurrentTime", "constant": false, "designable": true, "final": false, "index": 2, "name": "currentTime", "read": "currentTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCurrentTime"}, {"bindable": "bindableCurrentLoop", "constant": false, "designable": true, "final": false, "index": 3, "name": "currentLoop", "notify": "currentLoopChanged", "read": "currentLoop", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"bindable": "bindableDirection", "constant": false, "designable": true, "final": false, "index": 4, "name": "direction", "notify": "directionChanged", "read": "direction", "required": false, "scriptable": true, "stored": true, "type": "Direction", "user": false, "write": "setDirection"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "duration", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "QAbstractAnimation", "signals": [{"access": "public", "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newState", "type": "QAbstractAnimation::State"}, {"name": "oldState", "type": "QAbstractAnimation::State"}], "index": 1, "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "currentLoop", "type": "int"}], "index": 2, "name": "currentLoopChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QAbstractAnimation::Direction"}], "index": 3, "name": "directionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "policy", "type": "QAbstractAnimation::DeletionPolicy"}], "index": 4, "name": "start", "returnType": "void"}, {"access": "public", "index": 5, "isCloned": true, "name": "start", "returnType": "void"}, {"access": "public", "index": 6, "name": "pause", "returnType": "void"}, {"access": "public", "index": 7, "name": "resume", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 8, "name": "setPaused", "returnType": "void"}, {"access": "public", "index": 9, "name": "stop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "msecs", "type": "int"}], "index": 10, "name": "setCurrentTime", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QAnimationDriver", "lineNumber": 106, "object": true, "qualifiedClassName": "QAnimationDriver", "signals": [{"access": "public", "index": 0, "name": "started", "returnType": "void"}, {"access": "public", "index": 1, "name": "stopped", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractanimation.h", "outputRevision": 69}, {"classes": [{"className": "QDefaultAnimationDriver", "lineNumber": 80, "object": true, "qualifiedClassName": "QDefaultAnimationDriver", "slots": [{"access": "private", "index": 0, "name": "startTimer", "returnType": "void"}, {"access": "private", "index": 1, "name": "stopTimer", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAnimationDriver"}]}, {"className": "QAbstractAnimationTimer", "lineNumber": 109, "object": true, "qualifiedClassName": "QAbstractAnimationTimer", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QUnifiedTimer", "lineNumber": 126, "object": true, "qualifiedClassName": "QUnifiedTimer", "slots": [{"access": "private", "index": 0, "name": "startTimers", "returnType": "void"}, {"access": "private", "index": 1, "name": "stopTimer", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QAnimationTimer", "lineNumber": 219, "object": true, "qualifiedClassName": "QAnimationTimer", "slots": [{"access": "private", "index": 0, "name": "startAnimations", "returnType": "void"}, {"access": "private", "index": 1, "name": "stopTimer", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAnimationTimer"}]}], "inputFile": "qabstractanimation_p.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractEventDispatcher", "lineNumber": 16, "object": true, "qualifiedClassName": "QAbstractEventDispatcher", "signals": [{"access": "public", "index": 0, "name": "aboutToBlock", "returnType": "void"}, {"access": "public", "index": 1, "name": "awake", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QAbstractEventDispatcherV2", "lineNumber": 96, "object": true, "qualifiedClassName": "QAbstractEventDispatcherV2", "superClasses": [{"access": "public", "name": "QAbstractEventDispatcher"}]}], "inputFile": "qabstracteventdispatcher.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractItemModel", "enums": [{"isClass": false, "isFlag": false, "name": "LayoutChangeHint", "values": ["NoLayoutChangeHint", "VerticalSortHint", "HorizontalSortHint"]}, {"isClass": true, "isFlag": false, "name": "CheckIndexOption", "values": ["NoOption", "IndexIsValid", "DoNotUseParent", "ParentIsInvalid"]}], "lineNumber": 258, "methods": [{"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "column", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 26, "isConst": true, "name": "hasIndex", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "column", "type": "int"}], "index": 27, "isCloned": true, "isConst": true, "name": "hasIndex", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "column", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 28, "isConst": true, "name": "index", "returnType": "QModelIndex"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "column", "type": "int"}], "index": 29, "isCloned": true, "isConst": true, "name": "index", "returnType": "QModelIndex"}, {"access": "public", "arguments": [{"name": "child", "type": "QModelIndex"}], "index": 30, "isConst": true, "name": "parent", "returnType": "QModelIndex"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "column", "type": "int"}, {"name": "idx", "type": "QModelIndex"}], "index": 31, "isConst": true, "name": "sibling", "returnType": "QModelIndex"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}], "index": 32, "isConst": true, "name": "rowCount", "returnType": "int"}, {"access": "public", "index": 33, "isCloned": true, "isConst": true, "name": "rowCount", "returnType": "int"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}], "index": 34, "isConst": true, "name": "columnCount", "returnType": "int"}, {"access": "public", "index": 35, "isCloned": true, "isConst": true, "name": "columnCount", "returnType": "int"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}], "index": 36, "isConst": true, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "bool"}, {"access": "public", "index": 37, "isCloned": true, "isConst": true, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}, {"name": "role", "type": "int"}], "index": 38, "isConst": true, "name": "data", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}], "index": 39, "isCloned": true, "isConst": true, "name": "data", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "role", "type": "int"}], "index": 40, "name": "setData", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 41, "isCloned": true, "name": "setData", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "section", "type": "int"}, {"name": "orientation", "type": "Qt::Orientation"}, {"name": "role", "type": "int"}], "index": 42, "isConst": true, "name": "headerData", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "section", "type": "int"}, {"name": "orientation", "type": "Qt::Orientation"}], "index": 43, "isCloned": true, "isConst": true, "name": "headerData", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "count", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 44, "name": "insertRows", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "count", "type": "int"}], "index": 45, "isCloned": true, "name": "insertRows", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "column", "type": "int"}, {"name": "count", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 46, "name": "insertColumns", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "column", "type": "int"}, {"name": "count", "type": "int"}], "index": 47, "isCloned": true, "name": "insertColumns", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "count", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 48, "name": "removeRows", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "count", "type": "int"}], "index": 49, "isCloned": true, "name": "removeRows", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "column", "type": "int"}, {"name": "count", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 50, "name": "removeColumns", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "column", "type": "int"}, {"name": "count", "type": "int"}], "index": 51, "isCloned": true, "name": "removeColumns", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceRow", "type": "int"}, {"name": "count", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationChild", "type": "int"}], "index": 52, "name": "moveRows", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceColumn", "type": "int"}, {"name": "count", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationChild", "type": "int"}], "index": 53, "name": "moveColumns", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 54, "name": "insertRow", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "row", "type": "int"}], "index": 55, "isCloned": true, "name": "insertRow", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "column", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 56, "name": "insertColumn", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "column", "type": "int"}], "index": 57, "isCloned": true, "name": "insertColumn", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 58, "name": "removeRow", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "row", "type": "int"}], "index": 59, "isCloned": true, "name": "removeRow", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "column", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 60, "name": "removeColumn", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "column", "type": "int"}], "index": 61, "isCloned": true, "name": "removeColumn", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceRow", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationChild", "type": "int"}], "index": 62, "name": "moveRow", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceColumn", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationChild", "type": "int"}], "index": 63, "name": "moveColumn", "returnType": "bool", "revision": 1540}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}], "index": 64, "name": "fetchMore", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}], "index": 65, "isConst": true, "name": "canFetchMore", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}], "index": 66, "isConst": true, "name": "flags", "returnType": "Qt::<PERSON>emFlags"}, {"access": "public", "arguments": [{"name": "column", "type": "int"}, {"name": "order", "type": "Qt::<PERSON>rt<PERSON><PERSON>r"}], "index": 67, "name": "sort", "returnType": "void", "revision": 1540}, {"access": "public", "arguments": [{"name": "column", "type": "int"}], "index": 68, "isCloned": true, "name": "sort", "returnType": "void", "revision": 1540}, {"access": "public", "arguments": [{"name": "start", "type": "QModelIndex"}, {"name": "role", "type": "int"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "hits", "type": "int"}, {"name": "flags", "type": "Qt::MatchFlags"}], "index": 69, "isConst": true, "name": "match", "returnType": "QModelIndexList"}, {"access": "public", "arguments": [{"name": "start", "type": "QModelIndex"}, {"name": "role", "type": "int"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "hits", "type": "int"}], "index": 70, "isCloned": true, "isConst": true, "name": "match", "returnType": "QModelIndexList"}, {"access": "public", "arguments": [{"name": "start", "type": "QModelIndex"}, {"name": "role", "type": "int"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 71, "isCloned": true, "isConst": true, "name": "match", "returnType": "QModelIndexList"}], "object": true, "qualifiedClassName": "QAbstractItemModel", "signals": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}, {"name": "roles", "type": "QList<int>"}], "index": 0, "name": "dataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "index": 1, "isCloned": true, "name": "dataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "orientation", "type": "Qt::Orientation"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 2, "name": "headerDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QList<QPersistentModelIndex>"}, {"name": "hint", "type": "QAbstractItemModel::LayoutChangeHint"}], "index": 3, "name": "layoutChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QList<QPersistentModelIndex>"}], "index": 4, "isCloned": true, "name": "layoutChanged", "returnType": "void"}, {"access": "public", "index": 5, "isCloned": true, "name": "layoutChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QList<QPersistentModelIndex>"}, {"name": "hint", "type": "QAbstractItemModel::LayoutChangeHint"}], "index": 6, "name": "layoutAboutToBeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QList<QPersistentModelIndex>"}], "index": 7, "isCloned": true, "name": "layoutAboutToBeChanged", "returnType": "void"}, {"access": "public", "index": 8, "isCloned": true, "name": "layoutAboutToBeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 9, "name": "rowsAboutToBeInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 10, "name": "rowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 11, "name": "rowsAboutToBeRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 12, "name": "rowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 13, "name": "columnsAboutToBeInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 14, "name": "columnsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 15, "name": "columnsAboutToBeRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 16, "name": "columnsRemoved", "returnType": "void"}, {"access": "public", "index": 17, "name": "modelAboutToBeReset", "returnType": "void"}, {"access": "public", "index": 18, "name": "modelReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceStart", "type": "int"}, {"name": "sourceEnd", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationRow", "type": "int"}], "index": 19, "name": "rowsAboutToBeMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceStart", "type": "int"}, {"name": "sourceEnd", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationRow", "type": "int"}], "index": 20, "name": "rowsMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceStart", "type": "int"}, {"name": "sourceEnd", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationColumn", "type": "int"}], "index": 21, "name": "columnsAboutToBeMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceStart", "type": "int"}, {"name": "sourceEnd", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationColumn", "type": "int"}], "index": 22, "name": "columnsMoved", "returnType": "void"}], "slots": [{"access": "public", "index": 23, "name": "submit", "returnType": "bool"}, {"access": "public", "index": 24, "name": "revert", "returnType": "void"}, {"access": "protected", "index": 25, "name": "resetInternalData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QAbstractTableModel", "lineNumber": 450, "object": true, "qualifiedClassName": "QAbstractTableModel", "superClasses": [{"access": "public", "name": "QAbstractItemModel"}]}, {"className": "QAbstractListModel", "lineNumber": 476, "object": true, "qualifiedClassName": "QAbstractListModel", "superClasses": [{"access": "public", "name": "QAbstractItemModel"}]}], "inputFile": "qabstractitemmodel.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractProxyModel", "lineNumber": 16, "methods": [{"access": "public", "arguments": [{"name": "proxyIndex", "type": "QModelIndex"}], "index": 8, "isConst": true, "name": "mapToSource", "returnType": "QModelIndex"}, {"access": "public", "arguments": [{"name": "sourceIndex", "type": "QModelIndex"}], "index": 9, "isConst": true, "name": "mapFromSource", "returnType": "QModelIndex"}, {"access": "public", "arguments": [{"name": "selection", "type": "QItemSelection"}], "index": 10, "isConst": true, "name": "mapSelectionToSource", "returnType": "QItemSelection"}, {"access": "public", "arguments": [{"name": "selection", "type": "QItemSelection"}], "index": 11, "isConst": true, "name": "mapSelectionFromSource", "returnType": "QItemSelection"}], "object": true, "properties": [{"bindable": "bindableSourceModel", "constant": false, "designable": true, "final": false, "index": 0, "name": "sourceModel", "notify": "sourceModelChanged", "read": "sourceModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setSourceModel"}], "qualifiedClassName": "QAbstractProxyModel", "signals": [{"access": "public", "index": 0, "name": "sourceModelChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "_q_sourceModelDestroyed", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QModelIndex"}, {"type": "int"}, {"type": "int"}], "index": 2, "name": "_q_sourceModelRowsAboutToBeInserted", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QModelIndex"}, {"type": "int"}, {"type": "int"}], "index": 3, "name": "_q_sourceModelRowsInserted", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QModelIndex"}, {"type": "int"}, {"type": "int"}], "index": 4, "name": "_q_sourceModelRowsRemoved", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QModelIndex"}, {"type": "int"}, {"type": "int"}], "index": 5, "name": "_q_sourceModelColumnsAboutToBeInserted", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QModelIndex"}, {"type": "int"}, {"type": "int"}], "index": 6, "name": "_q_sourceModelColumnsInserted", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QModelIndex"}, {"type": "int"}, {"type": "int"}], "index": 7, "name": "_q_sourceModelColumnsRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractItemModel"}]}], "inputFile": "qabstractproxymodel.h", "outputRevision": 69}, {"classes": [{"className": "QAnimationGroup", "lineNumber": 14, "object": true, "qualifiedClassName": "QAnimationGroup", "superClasses": [{"access": "public", "name": "QAbstractAnimation"}]}], "inputFile": "qanimationgroup.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON>", "lineNumber": 15, "object": true, "qualifiedClassName": "<PERSON><PERSON><PERSON><PERSON>", "slots": [{"access": "private", "index": 0, "name": "_q_emitSignals", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qbuffer.h", "outputRevision": 69}, {"classes": [{"className": "QCalendar", "enums": [{"isClass": true, "isFlag": false, "name": "System", "values": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "J<PERSON><PERSON>", "IslamicCivil", "Last", "User"]}], "gadget": true, "lineNumber": 52, "qualifiedClassName": "QCalendar"}], "inputFile": "qcalendar.h", "outputRevision": 69}, {"classes": [{"className": "QCborError", "enums": [{"isClass": false, "isFlag": false, "name": "Code", "type": "int", "values": ["UnknownE<PERSON>r", "AdvancePastEnd", "InputOutputError", "GarbageAtEnd", "EndOfFile", "UnexpectedBreak", "UnknownType", "IllegalType", "IllegalNumber", "IllegalSimpleType", "InvalidUtf8String", "DataTooLarge", "NestingTooDeep", "UnsupportedType", "NoError"]}], "gadget": true, "lineNumber": 63, "qualifiedClassName": "QCborError"}], "inputFile": "qcborcommon.h", "outputRevision": 69}, {"classes": [{"className": "QCborStreamReader", "enums": [{"isClass": false, "isFlag": false, "name": "Type", "type": "quint8", "values": ["UnsignedInteger", "NegativeInteger", "ByteString", "ByteArray", "TextString", "String", "Array", "Map", "Tag", "SimpleType", "HalfFloat", "Float16", "Float", "Double", "Invalid"]}, {"isClass": false, "isFlag": false, "name": "StringResultCode", "values": ["EndOfString", "Ok", "Error"]}], "gadget": true, "lineNumber": 29, "qualifiedClassName": "QCborStreamReader"}], "inputFile": "qcborstreamreader.h", "outputRevision": 69}, {"classes": [{"className": "QCborValue", "enums": [{"isClass": false, "isFlag": false, "name": "Type", "type": "int", "values": ["Integer", "ByteArray", "String", "Array", "Map", "Tag", "SimpleType", "False", "True", "<PERSON><PERSON>", "Undefined", "Double", "DateTime", "Url", "RegularExpression", "<PERSON><PERSON>", "Invalid"]}], "gadget": true, "lineNumber": 46, "qualifiedClassName": "QCborValue"}], "inputFile": "qcborvalue.h", "outputRevision": 69}, {"classes": [{"className": "QChronoTimer", "lineNumber": 20, "object": true, "properties": [{"bindable": "bindableSingleShot", "constant": false, "designable": true, "final": true, "index": 0, "name": "singleShot", "read": "isSingleShot", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSingleShot"}, {"bindable": "bindableInterval", "constant": false, "designable": true, "final": true, "index": 1, "name": "interval", "read": "interval", "required": false, "scriptable": true, "stored": true, "type": "std::chrono::nanoseconds", "user": false, "write": "setInterval"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "remainingTime", "read": "remainingTime", "required": false, "scriptable": true, "stored": true, "type": "std::chrono::nanoseconds", "user": false}, {"bindable": "bindableTimerType", "constant": false, "designable": true, "final": true, "index": 3, "name": "timerType", "read": "timerType", "required": false, "scriptable": true, "stored": true, "type": "Qt::TimerType", "user": false, "write": "setTimerType"}, {"bindable": "bindableActive", "constant": false, "designable": true, "final": true, "index": 4, "name": "active", "read": "isActive", "required": false, "scriptable": true, "stored": false, "type": "bool", "user": false}], "qualifiedClassName": "QChronoTimer", "signals": [{"access": "public", "index": 0, "name": "timeout", "returnType": "void"}], "slots": [{"access": "public", "index": 1, "name": "start", "returnType": "void"}, {"access": "public", "index": 2, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qchronotimer.h", "outputRevision": 69}, {"classes": [{"className": "QConcatenateTablesProxyModel", "lineNumber": 15, "methods": [{"access": "public", "arguments": [{"name": "sourceModel", "type": "QAbstractItemModel*"}], "index": 0, "name": "addSourceModel", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceModel", "type": "QAbstractItemModel*"}], "index": 1, "name": "removeSourceModel", "returnType": "void"}], "object": true, "qualifiedClassName": "QConcatenateTablesProxyModel", "superClasses": [{"access": "public", "name": "QAbstractItemModel"}]}], "inputFile": "qconcatenatetablesproxymodel.h", "outputRevision": 69}, {"classes": [{"className": "QCoreApplication", "lineNumber": 42, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "applicationName", "notify": "applicationNameChanged", "read": "applicationName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setApplicationName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "applicationVersion", "notify": "applicationVersionChanged", "read": "applicationVersion", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setApplicationVersion"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "organizationName", "notify": "organizationNameChanged", "read": "organizationName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setOrganizationName"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "organizationDomain", "notify": "organizationDomainChanged", "read": "organizationDomain", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setOrganizationDomain"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "quitLockEnabled", "read": "isQuitLockEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setQuitLockEnabled"}], "qualifiedClassName": "QCoreApplication", "signals": [{"access": "public", "index": 0, "name": "aboutToQuit", "returnType": "void"}, {"access": "public", "index": 1, "name": "organizationNameChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "organizationDomainChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "applicationNameChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "applicationVersionChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 5, "name": "quit", "returnType": "void"}, {"access": "public", "arguments": [{"name": "retcode", "type": "int"}], "index": 6, "name": "exit", "returnType": "void"}, {"access": "public", "index": 7, "isCloned": true, "name": "exit", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcoreapplication.h", "outputRevision": 69}, {"classes": [{"className": "QEvent", "enums": [{"isClass": false, "isFlag": false, "name": "Type", "values": ["None", "Timer", "MouseButtonPress", "MouseButtonRelease", "MouseButtonDblClick", "MouseMove", "KeyPress", "KeyRelease", "FocusIn", "FocusOut", "FocusAboutToChange", "Enter", "Leave", "Paint", "Move", "Resize", "Create", "Destroy", "Show", "<PERSON>de", "Close", "Quit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ParentAboutToChange", "ThreadChange", "WindowActivate", "WindowDeactivate", "ShowToParent", "<PERSON>de<PERSON>o<PERSON><PERSON>nt", "Wheel", "WindowTitleChange", "WindowIconChange", "ApplicationWindowIconChange", "ApplicationFontChange", "ApplicationLayoutDirectionChange", "ApplicationPaletteChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clipboard", "Speech", "MetaCall", "SockAct", "WinEventAct", "DeferredDelete", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DragLeave", "Drop", "DragResponse", "ChildAdded", "ChildPolished", "ChildRemoved", "ShowWindowRequest", "PolishRequest", "Polish", "LayoutRequest", "UpdateRequest", "UpdateLater", "EmbeddingControl", "ActivateControl", "DeactivateControl", "ContextMenu", "InputMethod", "TabletMove", "LocaleChange", "LanguageChange", "LayoutDirectionChange", "Style", "TabletPress", "TabletRelease", "OkRequest", "HelpRequest", "IconDrag", "FontChange", "EnabledChange", "ActivationChange", "StyleChange", "IconTextChange", "ModifiedChange", "MouseTrackingChange", "WindowBlocked", "WindowUnblocked", "WindowStateChange", "ReadOnlyChange", "ToolTip", "WhatsThis", "StatusTip", "ActionChanged", "ActionAdded", "ActionRemoved", "FileOpen", "Shortcut", "ShortcutOverride", "WhatsThisClicked", "ToolBarChange", "ApplicationActivate", "ApplicationActivated", "ApplicationDeactivate", "ApplicationDeactivated", "QueryWhatsThis", "EnterWhatsThisMode", "LeaveWhatsThisMode", "ZOrderChange", "HoverEnter", "HoverLeave", "HoverMove", "AcceptDropsChange", "ZeroTimerEvent", "GraphicsSceneMouseMove", "GraphicsSceneMousePress", "GraphicsSceneMouseRelease", "GraphicsSceneMouseDoubleClick", "GraphicsSceneContextMenu", "GraphicsSceneHoverEnter", "GraphicsSceneHoverMove", "GraphicsSceneHoverLeave", "GraphicsSceneHelp", "GraphicsSceneDragEnter", "GraphicsSceneDragMove", "GraphicsSceneDragLeave", "GraphicsSceneDrop", "GraphicsSceneWheel", "GraphicsSceneLeave", "KeyboardLayoutChange", "DynamicPropertyChange", "TabletEnterProximity", "TabletLeaveProximity", "NonClientAreaMouseMove", "NonClientAreaMouseButtonPress", "NonClientAreaMouseButtonRelease", "NonClientAreaMouseButtonDblClick", "MacSizeChange", "ContentsRectChange", "MacGLWindowChange", "FutureCallOut", "GraphicsSceneResize", "GraphicsSceneMove", "CursorChange", "ToolTipChange", "NetworkReplyUpdated", "GrabMouse", "UngrabMouse", "GrabKeyboard", "UngrabKeyboard", "StateMachineSignal", "StateMachineWrapped", "TouchBegin", "TouchUpdate", "TouchEnd", "NativeGesture", "RequestSoftwareInputPanel", "CloseSoftwareInputPanel", "WinIdChange", "Gesture", "GestureOverride", "ScrollPrepare", "<PERSON><PERSON>", "Expose", "InputMethodQuery", "OrientationChange", "TouchCancel", "ThemeChange", "SockClose", "PlatformPanel", "StyleAnimationUpdate", "ApplicationStateChange", "WindowChangeInternal", "ScreenChangeInternal", "PlatformSurface", "Pointer", "TabletTrackingChange", "WindowAboutToChangeInternal", "DevicePixelRatioChange", "ChildWindowAdded", "ChildWindowRemoved", "ParentWindowAboutToChange", "ParentWindowChange", "SafeAreaMarginsChange", "User", "MaxUser"]}], "gadget": true, "lineNumber": 45, "qualifiedClassName": "QEvent"}], "inputFile": "qcoreevent.h", "outputRevision": 69}, {"classes": [{"className": "QCryptographicHash", "enums": [{"isClass": false, "isFlag": false, "name": "Algorithm", "values": ["Md4", "Md5", "Sha1", "Sha224", "Sha256", "Sha384", "Sha512", "Keccak_224", "Keccak_256", "Keccak_384", "Keccak_512", "RealSha3_224", "RealSha3_256", "RealSha3_384", "RealSha3_512", "Sha3_224", "Sha3_256", "Sha3_384", "Sha3_512", "Blake2b_160", "Blake2b_256", "Blake2b_384", "Blake2b_512", "Blake2s_128", "Blake2s_160", "Blake2s_224", "Blake2s_256", "NumAlgorithms"]}], "gadget": true, "lineNumber": 19, "qualifiedClassName": "QCryptographicHash"}], "inputFile": "qcryptographichash.h", "outputRevision": 69}, {"classes": [{"className": "QEasingCurve", "enums": [{"isClass": false, "isFlag": false, "name": "Type", "values": ["Linear", "InQuad", "OutQuad", "InOutQuad", "OutInQuad", "InCubic", "OutCubic", "InOutCubic", "OutInCubic", "InQuart", "OutQuart", "InOutQuart", "OutInQuart", "In<PERSON><PERSON>t", "OutQuint", "InOutQuint", "OutInQuint", "InSine", "OutSine", "InOutSine", "OutInSine", "InExpo", "OutExpo", "InOutExpo", "OutInExpo", "InCirc", "OutCirc", "InOutCirc", "OutInCirc", "InElastic", "OutElastic", "InOutElastic", "OutInElastic", "InBack", "OutBack", "InOutBack", "OutInBack", "InBounce", "OutBounce", "InOutBounce", "OutInBounce", "InCurve", "OutCurve", "SineCurve", "CosineCurve", "BezierSpline", "TCBSpline", "Custom", "NCurveTypes"]}], "gadget": true, "lineNumber": 19, "qualifiedClassName": "QEasingCurve"}], "inputFile": "qeasingcurve.h", "outputRevision": 69}, {"classes": [{"className": "QEventDispatcherWin32", "lineNumber": 32, "object": true, "qualifiedClassName": "QEventDispatcherWin32", "superClasses": [{"access": "public", "name": "QAbstractEventDispatcher"}]}], "inputFile": "qeventdispatcher_win_p.h", "outputRevision": 69}, {"classes": [{"className": "QEventLoop", "enums": [{"alias": "ProcessEventsFlag", "isClass": false, "isFlag": true, "name": "ProcessEventsFlags", "values": ["AllEvents", "ExcludeUserInputEvents", "ExcludeSocketNotifiers", "WaitForMoreEvents", "X11ExcludeTimers", "EventLoopExec", "DialogExec", "ApplicationExec"]}], "lineNumber": 15, "object": true, "qualifiedClassName": "QEventLoop", "slots": [{"access": "public", "arguments": [{"name": "returnCode", "type": "int"}], "index": 0, "name": "exit", "returnType": "void"}, {"access": "public", "index": 1, "isCloned": true, "name": "exit", "returnType": "void"}, {"access": "public", "index": 2, "name": "quit", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qeventloop.h", "outputRevision": 69}, {"classes": [{"className": "QFactoryLoader", "lineNumber": 61, "object": true, "qualifiedClassName": "QFactoryLoader", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qfactoryloader_p.h", "outputRevision": 69}, {"classes": [{"className": "QFile", "lineNumber": 92, "object": true, "qualifiedClassName": "QFile", "superClasses": [{"access": "public", "name": "QFileDevice"}]}], "inputFile": "qfile.h", "outputRevision": 69}, {"classes": [{"className": "QFileDevice", "lineNumber": 31, "object": true, "qualifiedClassName": "QFileDevice", "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qfiledevice.h", "outputRevision": 69}, {"classes": [{"className": "QFileSelector", "lineNumber": 13, "object": true, "qualifiedClassName": "QFileSelector", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qfileselector.h", "outputRevision": 69}, {"classes": [{"className": "QFileSystemWatcher", "lineNumber": 16, "object": true, "qualifiedClassName": "QFileSystemWatcher", "signals": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 0, "name": "fileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 1, "name": "directoryChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qfilesystemwatcher.h", "outputRevision": 69}, {"classes": [{"className": "QFileSystemWatcherEngine", "lineNumber": 29, "object": true, "qualifiedClassName": "QFileSystemWatcherEngine", "signals": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}, {"name": "removed", "type": "bool"}], "index": 0, "name": "fileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}, {"name": "removed", "type": "bool"}], "index": 1, "name": "directoryChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qfilesystemwatcher_p.h", "outputRevision": 69}, {"classes": [{"className": "QPollingFileSystemWatcherEngine", "lineNumber": 31, "object": true, "qualifiedClassName": "QPollingFileSystemWatcherEngine", "superClasses": [{"access": "public", "name": "QFileSystemWatcherEngine"}]}], "inputFile": "qfilesystemwatcher_polling_p.h", "outputRevision": 69}, {"classes": [{"className": "QWindowsFileSystemWatcherEngine", "lineNumber": 37, "object": true, "qualifiedClassName": "QWindowsFileSystemWatcherEngine", "signals": [{"access": "public", "arguments": [{"type": "QString"}], "index": 0, "name": "driveLockForRemoval", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 1, "name": "driveLockForRemovalFailed", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 2, "name": "driveRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QFileSystemWatcherEngine"}]}, {"className": "QWindowsFileSystemWatcherEngineThread", "lineNumber": 112, "object": true, "qualifiedClassName": "QWindowsFileSystemWatcherEngineThread", "signals": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}, {"name": "removed", "type": "bool"}], "index": 0, "name": "fileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QString"}, {"name": "removed", "type": "bool"}], "index": 1, "name": "directoryChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QThread"}]}], "inputFile": "qfilesystemwatcher_win_p.h", "outputRevision": 69}, {"classes": [{"className": "QFutureWatcherBase", "lineNumber": 18, "object": true, "qualifiedClassName": "QFutureWatcherBase", "signals": [{"access": "public", "index": 0, "name": "started", "returnType": "void"}, {"access": "public", "index": 1, "name": "finished", "returnType": "void"}, {"access": "public", "index": 2, "name": "canceled", "returnType": "void"}, {"access": "public", "index": 3, "name": "paused", "returnType": "void"}, {"access": "public", "index": 4, "name": "suspending", "returnType": "void"}, {"access": "public", "index": 5, "name": "suspended", "returnType": "void"}, {"access": "public", "index": 6, "name": "resumed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resultIndex", "type": "int"}], "index": 7, "name": "resultReadyAt", "returnType": "void"}, {"access": "public", "arguments": [{"name": "beginIndex", "type": "int"}, {"name": "endIndex", "type": "int"}], "index": 8, "name": "resultsReadyAt", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minimum", "type": "int"}, {"name": "maximum", "type": "int"}], "index": 9, "name": "progressRangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "progressValue", "type": "int"}], "index": 10, "name": "progressValueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "progressText", "type": "QString"}], "index": 11, "name": "progressTextChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 12, "name": "cancel", "returnType": "void"}, {"access": "public", "arguments": [{"name": "suspend", "type": "bool"}], "index": 13, "name": "setSuspended", "returnType": "void"}, {"access": "public", "index": 14, "name": "suspend", "returnType": "void"}, {"access": "public", "index": 15, "name": "resume", "returnType": "void"}, {"access": "public", "index": 16, "name": "toggleSuspended", "returnType": "void"}, {"access": "public", "arguments": [{"name": "paused", "type": "bool"}], "index": 17, "name": "setPaused", "returnType": "void"}, {"access": "public", "index": 18, "name": "pause", "returnType": "void"}, {"access": "public", "index": 19, "name": "togglePaused", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qfuturewatcher.h", "outputRevision": 69}, {"classes": [{"className": "QIdentityProxyModel", "lineNumber": 17, "object": true, "qualifiedClassName": "QIdentityProxyModel", "superClasses": [{"access": "public", "name": "QAbstractProxyModel"}]}], "inputFile": "qidentityproxymodel.h", "outputRevision": 69}, {"classes": [{"className": "QIODevice", "lineNumber": 30, "object": true, "qualifiedClassName": "QIODevice", "signals": [{"access": "public", "index": 0, "name": "readyRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "channel", "type": "int"}], "index": 1, "name": "channelReadyRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bytes", "type": "qint64"}], "index": 2, "name": "bytes<PERSON>ritten", "returnType": "void"}, {"access": "public", "arguments": [{"name": "channel", "type": "int"}, {"name": "bytes", "type": "qint64"}], "index": 3, "name": "channelBytesWritten", "returnType": "void"}, {"access": "public", "index": 4, "name": "aboutToClose", "returnType": "void"}, {"access": "public", "index": 5, "name": "readChannelFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QIODeviceBase"}]}], "inputFile": "qiodevice.h", "outputRevision": 69}, {"classes": [{"className": "QItemSelectionModel", "enums": [{"alias": "SelectionFlag", "isClass": false, "isFlag": true, "name": "SelectionFlags", "values": ["NoUpdate", "Clear", "Select", "Deselect", "Toggle", "Current", "Rows", "Columns", "SelectCurrent", "ToggleCurrent", "ClearAndSelect"]}], "lineNumber": 88, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}], "index": 12, "isConst": true, "name": "isSelected", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 13, "isConst": true, "name": "isRowSelected", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}], "index": 14, "isCloned": true, "isConst": true, "name": "isRowSelected", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "column", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 15, "isConst": true, "name": "isColumnSelected", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "column", "type": "int"}], "index": 16, "isCloned": true, "isConst": true, "name": "isColumnSelected", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 17, "isConst": true, "name": "rowIntersectsSelection", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}], "index": 18, "isCloned": true, "isConst": true, "name": "rowIntersectsSelection", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "column", "type": "int"}, {"name": "parent", "type": "QModelIndex"}], "index": 19, "isConst": true, "name": "columnIntersectsSelection", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "column", "type": "int"}], "index": 20, "isCloned": true, "isConst": true, "name": "columnIntersectsSelection", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "column", "type": "int"}], "index": 21, "isConst": true, "name": "selectedRows", "returnType": "QModelIndexList"}, {"access": "public", "index": 22, "isCloned": true, "isConst": true, "name": "selectedRows", "returnType": "QModelIndexList"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}], "index": 23, "isConst": true, "name": "selectedColumns", "returnType": "QModelIndexList"}, {"access": "public", "index": 24, "isCloned": true, "isConst": true, "name": "selectedColumns", "returnType": "QModelIndexList"}], "object": true, "properties": [{"bindable": "bindableModel", "constant": false, "designable": true, "final": false, "index": 0, "name": "model", "notify": "modelChanged", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": false, "final": false, "index": 1, "name": "hasSelection", "notify": "selectionChanged", "read": "hasSelection", "required": false, "scriptable": true, "stored": false, "type": "bool", "user": false}, {"constant": false, "designable": false, "final": false, "index": 2, "name": "currentIndex", "notify": "currentChanged", "read": "currentIndex", "required": false, "scriptable": true, "stored": false, "type": "QModelIndex", "user": false}, {"constant": false, "designable": false, "final": false, "index": 3, "name": "selection", "notify": "selectionChanged", "read": "selection", "required": false, "scriptable": true, "stored": false, "type": "QItemSelection", "user": false}, {"constant": false, "designable": false, "final": false, "index": 4, "name": "selectedIndexes", "notify": "selectionChanged", "read": "selectedIndexes", "required": false, "scriptable": true, "stored": false, "type": "QModelIndexList", "user": false}], "qualifiedClassName": "QItemSelectionModel", "signals": [{"access": "public", "arguments": [{"name": "selected", "type": "QItemSelection"}, {"name": "deselected", "type": "QItemSelection"}], "index": 0, "name": "selectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "current", "type": "QModelIndex"}, {"name": "previous", "type": "QModelIndex"}], "index": 1, "name": "currentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "current", "type": "QModelIndex"}, {"name": "previous", "type": "QModelIndex"}], "index": 2, "name": "currentRowChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "current", "type": "QModelIndex"}, {"name": "previous", "type": "QModelIndex"}], "index": 3, "name": "currentColumnChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "model", "type": "QAbstractItemModel*"}], "index": 4, "name": "modelChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}, {"name": "command", "type": "QItemSelectionModel::SelectionFlags"}], "index": 5, "name": "setCurrentIndex", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}, {"name": "command", "type": "QItemSelectionModel::SelectionFlags"}], "index": 6, "name": "select", "returnType": "void"}, {"access": "public", "arguments": [{"name": "selection", "type": "QItemSelection"}, {"name": "command", "type": "QItemSelectionModel::SelectionFlags"}], "index": 7, "name": "select", "returnType": "void"}, {"access": "public", "index": 8, "name": "clear", "returnType": "void"}, {"access": "public", "index": 9, "name": "reset", "returnType": "void"}, {"access": "public", "index": 10, "name": "clearSelection", "returnType": "void"}, {"access": "public", "index": 11, "name": "clearCurrentIndex", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qitemselectionmodel.h", "outputRevision": 69}, {"classes": [{"className": "QLibrary", "enums": [{"isClass": false, "isFlag": false, "name": "LoadHint", "values": ["ResolveAllSymbolsHint", "ExportExternalSymbolsHint", "LoadArchiveMemberHint", "PreventUnloadHint", "DeepBindHint"]}, {"alias": "LoadHint", "isClass": false, "isFlag": true, "name": "LoadHints", "values": ["ResolveAllSymbolsHint", "ExportExternalSymbolsHint", "LoadArchiveMemberHint", "PreventUnloadHint", "DeepBindHint"]}], "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fileName", "read": "fileName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setFileName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "loadHints", "read": "loadHints", "required": false, "scriptable": true, "stored": true, "type": "LoadHints", "user": false, "write": "setLoadHints"}], "qualifiedClassName": "QLibrary", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlibrary.h", "outputRevision": 69}, {"classes": [{"className": "QLocale", "enums": [{"isClass": false, "isFlag": false, "name": "Language", "type": "ushort", "values": ["AnyLanguage", "C", "Abkhazian", "Afar", "Afrikaans", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Akkadian", "<PERSON><PERSON><PERSON>", "Albanian", "AmericanSignLanguage", "Amharic", "AncientEgyptian", "AncientGreek", "Arabic", "Aragonese", "<PERSON><PERSON><PERSON>", "Armenian", "Assamese", "Asturian", "<PERSON><PERSON>", "Atsam", "<PERSON><PERSON>", "Avestan", "Aymara", "Azerbaijani", "Bafia", "Balinese", "Bambara", "<PERSON><PERSON><PERSON>", "Bangla", "Basaa", "Bashkir", "Basque", "BatakToba", "Belarusian", "Bemba", "<PERSON><PERSON>", "B<PERSON>jpuri", "B<PERSON>lama", "<PERSON>lin", "Bodo", "Bosnian", "Breton", "Buginese", "Bulgarian", "Burmese", "Cantonese", "Catalan", "Cebuano", "CentralAtlasTamazight", "CentralKurdish", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chechen", "Cherokee", "Chickasaw", "Chiga", "Chinese", "Church", "Chuvash", "Colognian", "Coptic", "Cornish", "Corsican", "<PERSON><PERSON>", "Croatian", "Czech", "Danish", "Divehi", "<PERSON><PERSON>", "Duala", "Dutch", "Dzongkha", "Embu", "English", "<PERSON><PERSON><PERSON>", "Esperanto", "Estonian", "<PERSON><PERSON>", "Ewondo", "Faroese", "Fijian", "Filipino", "Finnish", "French", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Gaelic", "Ga", "Galician", "Ganda", "<PERSON>z", "Georgian", "German", "Gothic", "Greek", "Guarani", "Gujarati", "Gus<PERSON>", "Haitian", "Hausa", "Hawaiian", "Hebrew", "<PERSON><PERSON>", "Hindi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hungarian", "Icelandic", "Ido", "Igbo", "InariSami", "Indonesian", "Ingush", "Interlingua", "Interlingue", "Inuktitut", "Inupiaq", "Irish", "Italian", "Japanese", "Javanese", "Jju", "Jo<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ka<PERSON>le", "<PERSON><PERSON>", "<PERSON><PERSON>all<PERSON>ut", "Kalenjin", "Kamba", "Kannada", "<PERSON><PERSON><PERSON>", "Kashmiri", "Kazakh", "<PERSON><PERSON>", "Khmer", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kinyarwanda", "<PERSON><PERSON>", "Kong<PERSON>", "Konkani", "Korean", "<PERSON><PERSON>", "KoyraboroSenni", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kurdish", "<PERSON><PERSON><PERSON>", "Kyrgyz", "Lakota", "<PERSON><PERSON>", "Lao", "Latin", "Latvian", "<PERSON><PERSON><PERSON><PERSON>", "Limburgish", "Lingala", "LiteraryChinese", "Lithuanian", "<PERSON><PERSON><PERSON>", "LowerSorbian", "LowGerman", "LubaKatanga", "LuleSami", "<PERSON><PERSON>", "Luxembourgish", "<PERSON><PERSON><PERSON>", "Macedonian", "Mac<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Makonde", "Malagasy", "Malayalam", "Malay", "Maltese", "Mandingo", "Manipuri", "Manx", "<PERSON><PERSON>", "Mapuche", "Marathi", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Meta", "Mohawk", "Mongolian", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Muscogee", "<PERSON><PERSON>", "NauruLanguage", "Navajo", "Ndonga", "Nepali", "New<PERSON>", "Ngiemboon", "Ng<PERSON><PERSON>", "NigerianPidgin", "Nko", "NorthernLuri", "NorthernSami", "NorthernSotho", "NorthNdebele", "NorwegianBokmal", "NorwegianNynorsk", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nyan<PERSON>le", "Occitan", "Odia", "Ojibwa", "Old<PERSON><PERSON><PERSON>", "OldNorse", "<PERSON><PERSON><PERSON><PERSON>", "Oromo", "Osage", "Ossetic", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Papiamento", "Pashto", "Persian", "Phoenic<PERSON>", "Polish", "Portuguese", "Prussian", "Punjabi", "Quechua", "Romanian", "Romansh", "Rombo", "<PERSON><PERSON>", "Russian", "Rwa", "Sa<PERSON>", "Sakha", "Samburu", "Samoan", "Sango", "<PERSON>u", "Sanskrit", "<PERSON><PERSON>", "Sardinian", "Saurashtra", "<PERSON><PERSON>", "Serbian", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "SichuanYi", "Sicilian", "Sidamo", "Silesian", "Sindhi", "Sinhala", "SkoltSami", "Slovak", "Slovenian", "Soga", "Somali", "SouthernKurdish", "SouthernSami", "SouthernSotho", "SouthNdebele", "Spanish", "StandardMoroccanTamazight", "Sundanese", "Swahili", "<PERSON><PERSON>", "Swedish", "SwissGerman", "Syriac", "<PERSON><PERSON><PERSON>", "Tahitian", "Tai<PERSON>am", "<PERSON><PERSON>", "Tajik", "Tamil", "<PERSON><PERSON><PERSON>", "Tasawaq", "Tatar", "Telugu", "<PERSON><PERSON>", "Thai", "Tibetan", "Tigre", "<PERSON><PERSON><PERSON><PERSON>", "TokelauLanguage", "TokPisin", "Tongan", "Tsonga", "Tswana", "Turkish", "Turkmen", "TuvaluLanguage", "Tyap", "Ugaritic", "Ukrainian", "UpperSorbian", "Urdu", "Uyghur", "Uzbek", "Vai", "<PERSON><PERSON><PERSON>", "Vietnamese", "Volapuk", "<PERSON><PERSON><PERSON>", "Walloon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Welsh", "WesternBalochi", "WesternFris<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xhosa", "Yang<PERSON>", "Yiddish", "Yoruba", "Zarma", "<PERSON><PERSON>", "Zulu", "Kaingang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "NorthernFrisian", "Rajasthani", "<PERSON><PERSON><PERSON>", "TokiPona", "Pijin", "Obolo", "<PERSON><PERSON><PERSON>", "Ligurian", "Rohingya", "<PERSON><PERSON><PERSON>", "<PERSON>ii", "<PERSON><PERSON>", "Venetian", "<PERSON><PERSON>", "KaraKalpak", "SwampyCree", "<PERSON><PERSON>", "Bengali", "Bhutani", "Byelorussian", "Cambodian", "CentralMoroccoTamazight", "<PERSON><PERSON><PERSON>", "Frisian", "Greenlandic", "Inupiak", "Kirghiz", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Oriya", "RhaetoRomance", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Walamo", "LastLanguage"]}, {"isClass": false, "isFlag": false, "name": "<PERSON><PERSON><PERSON>", "type": "ushort", "values": ["AnyScript", "AdlamScript", "AhomScript", "AnatolianHieroglyphsScript", "ArabicScript", "ArmenianScript", "AvestanScript", "BalineseScript", "BamumScript", "BanglaScript", "BassaVahScript", "BatakScript", "BhaiksukiScript", "BopomofoScript", "BrahmiScript", "BrailleScript", "BugineseScript", "BuhidScript", "CanadianAboriginalScript", "CarianScript", "CaucasianAlbanianScript", "ChakmaScript", "ChamScript", "CherokeeScript", "CopticScript", "CuneiformScript", "CypriotScript", "CyrillicScript", "DeseretScript", "DevanagariScript", "DuployanScript", "EgyptianHieroglyphsScript", "ElbasanScript", "EthiopicScript", "FraserScript", "GeorgianScript", "GlagoliticScript", "GothicScript", "Grantha<PERSON>", "GreekScript", "GujaratiScript", "GurmukhiScript", "HangulScript", "HanScript", "HanunooScript", "HanWithBopomofoScript", "HatranScript", "HebrewScript", "HiraganaScript", "ImperialAramaicScript", "InscriptionalPahlaviScript", "InscriptionalParthianScript", "JamoScript", "JapaneseScript", "JavaneseScript", "KaithiScript", "KannadaScript", "KatakanaScript", "KayahLiScript", "KharoshthiScript", "KhmerScript", "KhojkiScript", "KhudawadiScript", "KoreanScript", "LannaScript", "LaoScript", "LatinScript", "LepchaScript", "LimbuScript", "LinearAScript", "LinearBScript", "LycianScript", "LydianScript", "Ma<PERSON>janiScript", "MalayalamScript", "MandaeanScript", "ManichaeanScript", "MarchenScript", "MeiteiMayekScript", "MendeScript", "MeroiticCursiveScript", "MeroiticScript", "ModiScript", "MongolianScript", "MroScript", "MultaniScript", "MyanmarScript", "NabataeanScript", "NewaScript", "NewTaiLueScript", "NkoScript", "OdiaScript", "OghamScript", "OlChikiScript", "OldHungarianScript", "OldItalicScript", "OldNorthArabianScript", "OldPermicScript", "OldPersianScript", "OldSouthArabianScript", "OrkhonScript", "OsageScript", "OsmanyaScript", "PahawhHmongScript", "PalmyreneScript", "PauCinHauScript", "PhagsPaScript", "PhoenicianScript", "PollardPhoneticScript", "PsalterPahlaviScript", "RejangScript", "RunicScript", "SamaritanScript", "SaurashtraScript", "SharadaScript", "ShavianScript", "SiddhamScript", "SignWritingScript", "SimplifiedHanScript", "SinhalaScript", "SoraSompengScript", "SundaneseScript", "SylotiNagriScript", "SyriacScript", "TagalogScript", "TagbanwaScript", "TaiLeScript", "TaiVietScript", "TakriScript", "TamilScript", "TangutScript", "TeluguScript", "ThaanaScript", "ThaiScript", "TibetanScript", "TifinaghScript", "TirhutaScript", "TraditionalHanScript", "UgariticScript", "VaiScript", "VarangKshitiScript", "YiScript", "HanifiScript", "BengaliScript", "MendeKikakuiScript", "OriyaScript", "SimplifiedChineseScript", "TraditionalChineseScript", "LastScript"]}, {"isClass": false, "isFlag": false, "name": "Country", "type": "ushort", "values": ["AnyTerritory", "Afghanistan", "AlandIslands", "Albania", "Algeria", "AmericanSamoa", "Andorra", "Angola", "<PERSON><PERSON><PERSON>", "Antarctica", "AntiguaAndBarbuda", "Argentina", "Armenia", "Aruba", "AscensionIsland", "Australia", "Austria", "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", "Bermuda", "Bhutan", "Bolivia", "BosniaAndHerzegovina", "Botswana", "BouvetIsland", "Brazil", "BritishIndianOceanTerritory", "BritishVirginIslands", "Brunei", "Bulgaria", "BurkinaFaso", "Burundi", "Cambodia", "Cameroon", "Canada", "CanaryIslands", "CapeVerde", "CaribbeanNetherlands", "CaymanIslands", "CentralAfricanRepublic", "<PERSON>uta<PERSON>nd<PERSON><PERSON><PERSON>", "Chad", "Chile", "China", "ChristmasIsland", "ClippertonIsland", "CocosIslands", "Colombia", "Comoros", "CongoBrazzaville", "CongoKinshasa", "CookIslands", "CostaRica", "Croatia", "Cuba", "Curacao", "Cyprus", "Czechia", "Denmark", "DiegoG<PERSON><PERSON>", "Djibouti", "Dominica", "DominicanRepublic", "Ecuador", "Egypt", "ElSalvador", "EquatorialGuinea", "Eritrea", "Estonia", "<PERSON><PERSON><PERSON><PERSON>", "Ethiopia", "Europe", "EuropeanUnion", "FalklandIslands", "FaroeIslands", "Fiji", "Finland", "France", "FrenchGuiana", "FrenchPolynesia", "FrenchSouthernTerritories", "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Gibraltar", "Greece", "Greenland", "Grenada", "Guadeloupe", "Guam", "Guatemala", "Guernsey", "GuineaBissau", "Guinea", "Guyana", "Haiti", "HeardAndMcDonaldIslands", "Honduras", "HongKong", "Hungary", "Iceland", "India", "Indonesia", "Iran", "Iraq", "Ireland", "IsleOfMan", "Israel", "Italy", "IvoryCoast", "Jamaica", "Japan", "Jersey", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Kosovo", "Kuwait", "Kyrgyzstan", "Laos", "LatinAmerica", "Latvia", "Lebanon", "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania", "Luxembourg", "Macao", "Macedonia", "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta", "MarshallIslands", "Martinique", "Mauritania", "Mauritius", "Mayotte", "Mexico", "Micronesia", "Moldova", "Monaco", "Mongolia", "Montenegro", "Montserrat", "Morocco", "Mozambique", "Myanmar", "Namibia", "NauruTerritory", "Nepal", "Netherlands", "NewCaledonia", "NewZealand", "Nicaragua", "Nigeria", "Niger", "Niue", "NorfolkIsland", "NorthernMarianaIslands", "NorthKorea", "Norway", "Oman", "OutlyingOceania", "Pakistan", "<PERSON><PERSON>", "PalestinianTerritories", "Panama", "PapuaNewGuinea", "Paraguay", "Peru", "Philippines", "Pitcairn", "Poland", "Portugal", "PuertoRico", "Qatar", "Reunion", "Romania", "Russia", "Rwanda", "SaintBarthel<PERSON>y", "SaintHelena", "SaintKittsAndNevis", "SaintLucia", "Saint<PERSON><PERSON><PERSON>", "SaintPierreAndMiquelon", "SaintVincentAndGrenadines", "Samoa", "<PERSON><PERSON><PERSON><PERSON>", "SaoTomeAndPrincipe", "SaudiArabia", "Senegal", "Serbia", "Seychelles", "SierraLeone", "Singapore", "SintMaarten", "Slovakia", "Slovenia", "SolomonIslands", "Somalia", "SouthAfrica", "SouthGeorgiaAndSouthSandwichIslands", "SouthKorea", "SouthSudan", "Spain", "SriLanka", "Sudan", "Suriname", "SvalbardAndJanMayen", "Sweden", "Switzerland", "Syria", "Taiwan", "Tajikistan", "Tanzania", "Thailand", "TimorLeste", "Togo", "TokelauTerritory", "Tonga", "TrinidadAndTobago", "TristanDaCunha", "Tunisia", "Turkey", "Turkmenistan", "TurksAndCaicosIslands", "TuvaluTerritory", "Uganda", "Ukraine", "UnitedArabEmirates", "UnitedKingdom", "UnitedStatesOutlyingIslands", "UnitedStates", "UnitedStatesVirginIslands", "Uruguay", "Uzbekistan", "Vanuatu", "VaticanCity", "Venezuela", "Vietnam", "WallisAndFutuna", "WesternSahara", "World", "Yemen", "Zambia", "Zimbabwe", "AnyCountry", "Bonaire", "BosniaAndHerzegowina", "Cura<PERSON>ao", "CzechRepublic", "DemocraticRepublicOfCongo", "DemocraticRepublicOfKorea", "EastTimor", "LatinAmericaAndTheCaribbean", "Macau", "NauruCountry", "PeoplesRepublicOfCongo", "RepublicOfKorea", "RussianFederation", "SaintVincentAndTheGrenadines", "SouthGeorgiaAndTheSouthSandwichIslands", "SvalbardAndJanMayenIslands", "Swaziland", "SyrianArabRepublic", "TokelauCountry", "TuvaluCountry", "UnitedStatesMinorOutlyingIslands", "VaticanCityState", "WallisAndFutunaIslands", "LastTerritory", "LastCountry"]}, {"isClass": false, "isFlag": false, "name": "MeasurementSystem", "values": ["MetricSystem", "ImperialUSSystem", "ImperialUKSystem", "ImperialSystem"]}, {"isClass": false, "isFlag": false, "name": "FormatType", "values": ["LongFormat", "ShortFormat", "NarrowFormat"]}, {"alias": "NumberOption", "isClass": false, "isFlag": true, "name": "NumberOptions", "values": ["DefaultNumberOptions", "OmitGroupSeparator", "RejectGroupSeparator", "OmitLeadingZeroInExponent", "RejectLeadingZeroInExponent", "IncludeTrailingZeroesAfterDot", "RejectTrailingZeroesAfterDot"]}, {"isClass": true, "isFlag": false, "name": "TagSeparator", "type": "char", "values": ["Dash", "Underscore"]}, {"isClass": false, "isFlag": false, "name": "CurrencySymbolFormat", "values": ["CurrencyIsoCode", "CurrencySymbol", "CurrencyDisplayName"]}, {"alias": "DataSizeFormat", "isClass": false, "isFlag": true, "name": "DataSizeFormats", "values": ["DataSizeBase1000", "DataSizeSIQuantifiers", "DataSizeIecFormat", "DataSizeTraditionalFormat", "DataSizeSIFormat"]}, {"alias": "LanguageCodeType", "isClass": false, "isFlag": true, "name": "LanguageCodeTypes", "values": ["ISO639Part1", "ISO639Part2B", "ISO639Part2T", "ISO639Part3", "LegacyLanguageCode", "ISO639Part2", "ISO639Alpha2", "ISO639Alpha3", "ISO639", "AnyLanguageCode"]}, {"isClass": false, "isFlag": false, "name": "QuotationStyle", "values": ["StandardQuotation", "AlternateQuotation"]}], "gadget": true, "lineNumber": 28, "qualifiedClassName": "QLocale"}], "inputFile": "qlocale.h", "outputRevision": 69}, {"classes": [{"className": "QMimeData", "lineNumber": 15, "object": true, "qualifiedClassName": "QMimeData", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmimedata.h", "outputRevision": 69}, {"classes": [{"className": "QMimeType", "gadget": true, "lineNumber": 24, "methods": [{"access": "public", "arguments": [{"name": "mimeTypeName", "type": "QString"}], "index": 0, "isConst": true, "name": "inherits", "returnType": "bool"}], "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "valid", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "isDefault", "read": "isDefault", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "comment", "read": "comment", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "genericIconName", "read": "genericIconName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "iconName", "read": "iconName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "globPatterns", "read": "globPatterns", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "parentMimeTypes", "read": "parentMimeTypes", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "allAncestors", "read": "allAncestors", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": true, "designable": true, "final": false, "index": 9, "name": "aliases", "read": "aliases", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": true, "designable": true, "final": false, "index": 10, "name": "suffixes", "read": "suffixes", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": true, "designable": true, "final": false, "index": 11, "name": "preferredSuffix", "read": "preferredSuffix", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 12, "name": "filterString", "read": "filterString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QMimeType"}], "inputFile": "qmimetype.h", "outputRevision": 69}, {"classes": [{"className": "Qt", "enums": [{"isClass": false, "isFlag": false, "name": "GlobalColor", "values": ["color0", "color1", "black", "white", "<PERSON><PERSON><PERSON>", "gray", "lightGray", "red", "green", "blue", "cyan", "magenta", "yellow", "darkRed", "<PERSON><PERSON><PERSON>", "darkBlue", "dark<PERSON>yan", "darkMagenta", "<PERSON><PERSON><PERSON><PERSON>", "transparent"]}, {"isClass": true, "isFlag": false, "name": "ColorScheme", "values": ["Unknown", "Light", "Dark"]}, {"alias": "MouseB<PERSON>on", "isClass": false, "isFlag": true, "name": "MouseButtons", "values": ["NoButton", "LeftButton", "RightButton", "MiddleButton", "BackButton", "XButton1", "ExtraButton1", "ForwardButton", "XButton2", "ExtraButton2", "TaskButton", "ExtraButton3", "ExtraButton4", "ExtraButton5", "ExtraButton6", "ExtraButton7", "ExtraButton8", "ExtraButton9", "ExtraButton10", "ExtraButton11", "ExtraButton12", "ExtraButton13", "ExtraButton14", "ExtraButton15", "ExtraButton16", "ExtraButton17", "ExtraButton18", "ExtraButton19", "ExtraButton20", "ExtraButton21", "ExtraButton22", "ExtraButton23", "ExtraButton24", "AllButtons", "MaxMouseButton", "MouseButtonMask"]}, {"isClass": false, "isFlag": false, "name": "Orientation", "values": ["Horizontal", "Vertical"]}, {"alias": "Orientation", "isClass": false, "isFlag": true, "name": "Orientations", "values": ["Horizontal", "Vertical"]}, {"isClass": false, "isFlag": false, "name": "FocusPolicy", "values": ["NoFocus", "TabFocus", "ClickFocus", "StrongFocus", "WheelFocus"]}, {"isClass": false, "isFlag": false, "name": "TabFocusBehavior", "values": ["NoTabFocus", "TabFocusTextControls", "TabFocusListControls", "TabFocusAllControls"]}, {"isClass": false, "isFlag": false, "name": "SortOrder", "values": ["AscendingOrder", "DescendingOrder"]}, {"alias": "SplitBehaviorFlags", "isClass": false, "isFlag": true, "name": "SplitBehavior", "values": ["KeepEmptyParts", "SkipEmptyParts"]}, {"alias": "AlignmentFlag", "isClass": false, "isFlag": true, "name": "Alignment", "values": ["AlignLeft", "AlignLeading", "AlignRight", "AlignTrailing", "AlignHCenter", "AlignJustify", "AlignAbsolute", "AlignHorizontal_Mask", "AlignTop", "AlignBottom", "AlignVCenter", "AlignBaseline", "AlignVertical_Mask", "AlignCenter"]}, {"isClass": false, "isFlag": false, "name": "TextFlag", "values": ["TextSingleLine", "TextDontClip", "TextExpandTabs", "TextShowMnemonic", "TextWordWrap", "TextWrapAnywhere", "TextDontPrint", "TextIncludeTrailingSpaces", "TextHideMnemonic", "TextJustificationForced", "TextForceLeftToRight", "TextForceRightToLeft", "TextLongestVariant"]}, {"isClass": false, "isFlag": false, "name": "TextElideMode", "values": ["ElideLeft", "ElideRight", "ElideMiddle", "ElideNone"]}, {"isClass": false, "isFlag": false, "name": "WindowType", "values": ["Widget", "Window", "Dialog", "Sheet", "Drawer", "Popup", "Tool", "ToolTip", "SplashScreen", "Desktop", "SubWindow", "ForeignWindow", "CoverWindow", "WindowType_Mask", "MSWindowsFixedSizeDialogHint", "MSWindowsOwnDC", "BypassWindowManagerHint", "X11BypassWindowManagerHint", "FramelessWindowHint", "WindowTitleHint", "WindowSystemMenuHint", "WindowMinimizeButtonHint", "WindowMaximizeButtonHint", "WindowMinMaxButtonsHint", "WindowContextHelpButtonHint", "WindowShadeButtonHint", "WindowStaysOnTopHint", "WindowTransparentForInput", "WindowOverridesSystemGestures", "WindowDoesNotAcceptFocus", "MaximizeUsingFullscreenGeometryHint", "ExpandedClientAreaHint", "NoTitleBarBackgroundHint", "CustomizeWindowHint", "WindowStaysOnBottomHint", "WindowCloseButtonHint", "MacWindowToolBarButtonHint", "BypassGraphicsProxyWidget", "NoDropShadowWindowHint", "WindowFullscreenButtonHint"]}, {"alias": "WindowType", "isClass": false, "isFlag": true, "name": "WindowFlags", "values": ["Widget", "Window", "Dialog", "Sheet", "Drawer", "Popup", "Tool", "ToolTip", "SplashScreen", "Desktop", "SubWindow", "ForeignWindow", "CoverWindow", "WindowType_Mask", "MSWindowsFixedSizeDialogHint", "MSWindowsOwnDC", "BypassWindowManagerHint", "X11BypassWindowManagerHint", "FramelessWindowHint", "WindowTitleHint", "WindowSystemMenuHint", "WindowMinimizeButtonHint", "WindowMaximizeButtonHint", "WindowMinMaxButtonsHint", "WindowContextHelpButtonHint", "WindowShadeButtonHint", "WindowStaysOnTopHint", "WindowTransparentForInput", "WindowOverridesSystemGestures", "WindowDoesNotAcceptFocus", "MaximizeUsingFullscreenGeometryHint", "ExpandedClientAreaHint", "NoTitleBarBackgroundHint", "CustomizeWindowHint", "WindowStaysOnBottomHint", "WindowCloseButtonHint", "MacWindowToolBarButtonHint", "BypassGraphicsProxyWidget", "NoDropShadowWindowHint", "WindowFullscreenButtonHint"]}, {"isClass": false, "isFlag": false, "name": "WindowState", "values": ["WindowNoState", "WindowMinimized", "WindowMaximized", "WindowFullScreen", "WindowActive"]}, {"alias": "WindowState", "isClass": false, "isFlag": true, "name": "WindowStates", "values": ["WindowNoState", "WindowMinimized", "WindowMaximized", "WindowFullScreen", "WindowActive"]}, {"isClass": false, "isFlag": false, "name": "ApplicationState", "values": ["ApplicationSuspended", "ApplicationHidden", "ApplicationInactive", "ApplicationActive"]}, {"isClass": false, "isFlag": false, "name": "ScreenOrientation", "values": ["PrimaryOrientation", "PortraitOrientation", "LandscapeOrientation", "InvertedPortraitOrientation", "InvertedLandscapeOrientation"]}, {"alias": "ScreenOrientation", "isClass": false, "isFlag": true, "name": "ScreenOrientations", "values": ["PrimaryOrientation", "PortraitOrientation", "LandscapeOrientation", "InvertedPortraitOrientation", "InvertedLandscapeOrientation"]}, {"isClass": false, "isFlag": false, "name": "WidgetAttribute", "values": ["WA_Disabled", "WA_UnderMouse", "WA_MouseTracking", "WA_OpaquePaintEvent", "WA_StaticContents", "WA_LaidOut", "WA_PaintOnScreen", "WA_NoSystemBackground", "WA_UpdatesDisabled", "WA_Mapped", "WA_InputMethodEnabled", "WA_WState_Visible", "WA_WState_Hidden", "WA_ForceDisabled", "WA_KeyCompression", "WA_PendingMoveEvent", "WA_PendingResizeEvent", "WA_SetPalette", "WA_SetFont", "WA_SetCursor", "WA_NoChildEventsFromChildren", "WA_WindowModified", "WA_Resized", "WA_Moved", "WA_PendingUpdate", "WA_InvalidSize", "WA_CustomWhatsThis", "WA_LayoutOnEntireRect", "WA_OutsideWSRange", "WA_GrabbedShortcut", "WA_TransparentForMouseEvents", "WA_PaintUnclipped", "WA_SetWindowIcon", "WA_NoMouseReplay", "WA_DeleteOnClose", "WA_RightToLeft", "WA_SetLayoutDirection", "WA_NoChildEventsForParent", "WA_ForceUpdatesDisabled", "WA_WState_Created", "WA_WState_CompressKeys", "WA_WState_InPaintEvent", "WA_WState_Reparented", "WA_WState_ConfigPending", "WA_WState_Polished", "WA_WState_OwnSizePolicy", "WA_WState_ExplicitShowHide", "WA_ShowModal", "WA_MouseNoMask", "WA_NoMousePropagation", "WA_Hover", "WA_InputMethodTransparent", "WA_QuitOnClose", "WA_KeyboardFocusChange", "WA_AcceptDrops", "WA_DropSiteRegistered", "WA_WindowPropagation", "WA_NoX11EventCompression", "WA_TintedBackground", "WA_X11OpenGLOverlay", "WA_AlwaysShowToolTips", "WA_MacOpaqueSizeGrip", "WA_SetStyle", "WA_SetLocale", "WA_MacShowFocusRect", "WA_MacNormalSize", "WA_MacSmallSize", "WA_MacMiniSize", "WA_LayoutUsesWidgetRect", "WA_StyledBackground", "WA_CanHostQMdiSubWindowTitleBar", "WA_MacAlwaysShowToolWindow", "WA_StyleSheet", "WA_ShowWithoutActivating", "WA_X11BypassTransientForHint", "WA_NativeWindow", "WA_DontCreateNativeAncestors", "WA_DontShowOnScreen", "WA_X11NetWmWindowTypeDesktop", "WA_X11NetWmWindowTypeDock", "WA_X11NetWmWindowTypeToolBar", "WA_X11NetWmWindowTypeMenu", "WA_X11NetWmWindowTypeUtility", "WA_X11NetWmWindowTypeSplash", "WA_X11NetWmWindowTypeDialog", "WA_X11NetWmWindowTypeDropDownMenu", "WA_X11NetWmWindowTypePopupMenu", "WA_X11NetWmWindowTypeToolTip", "WA_X11NetWmWindowTypeNotification", "WA_X11NetWmWindowTypeCombo", "WA_X11NetWmWindowTypeDND", "WA_SetWindowModality", "WA_WState_WindowOpacitySet", "WA_TranslucentBackground", "WA_AcceptTouchEvents", "WA_WState_AcceptedTouchBeginEvent", "WA_TouchPadAcceptSingleTouchEvents", "WA_X11DoNotAcceptFocus", "WA_AlwaysStackOnTop", "WA_TabletTracking", "WA_ContentsMarginsRespectsSafeArea", "WA_StyleSheetTarget", "WA_AttributeCount"]}, {"isClass": false, "isFlag": false, "name": "ApplicationAttribute", "values": ["AA_QtQuickUseDefaultSizePolicy", "AA_DontShowIconsInMenus", "AA_NativeWindows", "AA_DontCreateNativeWidgetSiblings", "AA_PluginApplication", "AA_DontUseNativeMenuBar", "AA_MacDontSwapCtrlAndMeta", "AA_Use96Dpi", "AA_DisableNativeVirtualKeyboard", "AA_DontUseNativeMenuWindows", "AA_SynthesizeTouchForUnhandledMouseEvents", "AA_SynthesizeMouseForUnhandledTouchEvents", "AA_UseHighDpiPixmaps", "AA_ForceRasterWidgets", "AA_UseDesktopOpenGL", "AA_UseOpenGLES", "AA_UseSoftwareOpenGL", "AA_ShareOpenGLContexts", "AA_SetPalette", "AA_EnableHighDpiScaling", "AA_DisableHighDpiScaling", "AA_UseStyleSheetPropagationInWidgetStyles", "AA_DontUseNativeDialogs", "AA_SynthesizeMouseForUnhandledTabletEvents", "AA_CompressHighFrequencyEvents", "AA_DontCheckOpenGLContextThreadAffinity", "AA_DisableShaderDiskCache", "AA_DontShowShortcutsInContextMenus", "AA_CompressTabletEvents", "AA_DisableSessionManager", "AA_AttributeCount"]}, {"alias": "ImageConversionFlag", "isClass": false, "isFlag": true, "name": "ImageConversionFlags", "values": ["ColorMode_Mask", "AutoColor", "ColorOnly", "MonoOnly", "Alpha<PERSON><PERSON><PERSON>_<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OrderedAlpha<PERSON><PERSON><PERSON>", "DiffuseAl<PERSON><PERSON><PERSON><PERSON>", "NoAlpha", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Ordered<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DitherMode_Mask", "AutoDither", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NoOpaqueDetection", "NoFormatConversion"]}, {"isClass": false, "isFlag": false, "name": "BGMode", "values": ["TransparentMode", "OpaqueMode"]}, {"isClass": false, "isFlag": false, "name": "Key", "values": ["Key_Space", "Key_Any", "Key_Exclam", "Key_QuoteDbl", "Key_NumberSign", "Key_Dollar", "Key_Percent", "Key_Ampersand", "Key_Apostrophe", "Key_ParenLeft", "Key_ParenRight", "Key_Asterisk", "Key_Plus", "Key_Comma", "Key_Minus", "Key_Period", "Key_Slash", "Key_0", "Key_1", "Key_2", "Key_3", "Key_4", "Key_5", "Key_6", "Key_7", "Key_8", "Key_9", "Key_Colon", "Key_Semicolon", "Key_Less", "Key_Equal", "Key_Greater", "Key_Question", "Key_At", "Key_A", "Key_B", "Key_C", "Key_D", "Key_E", "Key_F", "Key_G", "Key_H", "Key_I", "Key_J", "Key_K", "Key_L", "Key_M", "Key_N", "Key_O", "Key_P", "Key_Q", "Key_R", "Key_S", "Key_T", "Key_U", "Key_V", "Key_W", "Key_X", "Key_Y", "Key_Z", "Key_BracketLeft", "Key_<PERSON>slash", "Key_BracketRight", "Key_AsciiCircum", "Key_Underscore", "Key_QuoteLeft", "Key_BraceLeft", "Key_Bar", "Key_BraceRight", "Key_AsciiTilde", "Key_nobreakspace", "Key_exclamdown", "Key_cent", "Key_sterling", "Key_currency", "Key_yen", "Key_brokenbar", "Key_section", "Key_diaeresis", "Key_copyright", "Key_ordfeminine", "Key_guillemotleft", "Key_notsign", "Key_hyphen", "Key_registered", "Key_macron", "Key_degree", "Key_plusminus", "Key_twosuperior", "Key_threesuperior", "Key_acute", "Key_micro", "Key_mu", "Key_paragraph", "Key_periodcentered", "Key_cedilla", "Key_onesuperior", "Key_masculine", "<PERSON>_guillem<PERSON><PERSON>", "Key_onequarter", "Key_onehalf", "Key_threequarters", "Key_questiondown", "Key_<PERSON><PERSON>", "Key_<PERSON><PERSON><PERSON>", "Key_Acircumflex", "<PERSON>_<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Key_Aring", "Key_AE", "Key_Ccedilla", "<PERSON>_<PERSON><PERSON>", "Key_Eacute", "Key_Ecircumflex", "<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "Key_I<PERSON>", "Key_<PERSON><PERSON>ute", "Key_Icircumflex", "Key_<PERSON><PERSON><PERSON><PERSON>", "Key_ETH", "Key_Ntilde", "<PERSON>_<PERSON><PERSON>", "Key_O<PERSON>ute", "Key_Ocircumflex", "Key_Otilde", "<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "Key_multiply", "Key_Ooblique", "<PERSON>_<PERSON><PERSON>", "Key_Uacute", "Key_Ucircumflex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Key_Yacute", "Key_THORN", "Key_ssharp", "Key_division", "<PERSON>_<PERSON><PERSON><PERSON>is", "Key_Escape", "Key_Tab", "Key_Backtab", "Key_Backspace", "Key_Return", "Key_Enter", "Key_Insert", "Key_Delete", "Key_Pause", "Key_Print", "Key_SysReq", "Key_Clear", "Key_Home", "Key_End", "Key_Left", "Key_Up", "Key_Right", "Key_Down", "Key_PageUp", "Key_PageDown", "Key_Shift", "Key_Control", "Key_Meta", "Key_Alt", "Key_CapsLock", "Key_NumLock", "Key_ScrollLock", "Key_F1", "Key_F2", "Key_F3", "Key_F4", "Key_F5", "Key_F6", "Key_F7", "Key_F8", "Key_F9", "Key_F10", "Key_F11", "Key_F12", "Key_F13", "Key_F14", "Key_F15", "Key_F16", "Key_F17", "Key_F18", "Key_F19", "Key_F20", "Key_F21", "Key_F22", "Key_F23", "Key_F24", "Key_F25", "Key_F26", "Key_F27", "Key_F28", "Key_F29", "Key_F30", "Key_F31", "Key_F32", "Key_F33", "Key_F34", "Key_F35", "Key_Super_L", "Key_Super_R", "Key_Menu", "Key_Hyper_L", "Key_Hyper_R", "Key_Help", "Key_Direction_L", "Key_Direction_R", "Key_AltGr", "Key_Multi_key", "Key_Codeinput", "Key_SingleCandidate", "Key_MultipleCandidate", "Key_PreviousCandidate", "Key_Mode_switch", "Key_Kanji", "Key_<PERSON><PERSON><PERSON>", "Key_Henkan", "Key_<PERSON>ji", "Key_Hiragana", "<PERSON>_<PERSON><PERSON>na", "Key_<PERSON><PERSON>na_<PERSON><PERSON>na", "Key_Zenkaku", "Key_<PERSON><PERSON><PERSON>", "Key_<PERSON><PERSON><PERSON>_Hankaku", "Key_Touroku", "Key_Massyo", "<PERSON>_<PERSON><PERSON>_<PERSON>", "Key_Kana_Shift", "Key_Eisu_Shift", "Key_Eisu_toggle", "Key_Hangul", "Key_Hangul_Start", "Key_Hangul_End", "Key_Hangul_Hanja", "Key_<PERSON>_<PERSON>o", "Key_Hangul_Romaja", "Key_<PERSON>_<PERSON><PERSON>ja", "Key_Hangul_Banja", "Key_Hangul_PreHanja", "Key_Hangul_PostHanja", "Key_Hangul_Special", "Key_Dead_Grave", "Key_Dead_Acute", "Key_Dead_Circumflex", "Key_Dead_Tilde", "Key_<PERSON>_<PERSON><PERSON>", "Key_Dead_Breve", "Key_Dead_Abovedot", "Key_<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "Key_Dead_Abovering", "Key_Dead_Doubleacute", "Key_<PERSON>_<PERSON><PERSON>", "Key_<PERSON>_<PERSON><PERSON><PERSON>", "Key_<PERSON>_<PERSON><PERSON><PERSON>", "Key_Dead_Iota", "Key_Dead_Voiced_Sound", "Key_Dead_Semivoiced_Sound", "Key_Dead_Belowdot", "Key_Dead_Hook", "Key_Dead_Horn", "Key_Dead_Stroke", "Key_Dead_Abovecomma", "Key_Dead_Abovereversedcomma", "Key_Dead_Doublegrave", "Key_Dead_Belowring", "Key_Dead_Belowmacron", "Key_Dead_Belowcircumflex", "Key_Dead_Belowtilde", "Key_Dead_Belowbreve", "Key_Dead_<PERSON><PERSON><PERSON><PERSON>", "Key_Dead_Invertedbreve", "Key_Dead_Belowcomma", "Key_<PERSON>_<PERSON><PERSON><PERSON>cy", "Key_Dead_a", "Key_Dead_A", "Key_Dead_e", "Key_Dead_E", "Key_Dead_i", "Key_Dead_I", "Key_Dead_o", "Key_Dead_O", "Key_Dead_u", "Key_Dead_U", "Key_Dead_<PERSON>_<PERSON>hwa", "Key_Dead_Capital_Schwa", "Key_Dead_Greek", "Key_Dead_Lowline", "Key_Dead_Aboveverticalline", "Key_Dead_Belowverticalline", "Key_Dead_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Key_Back", "Key_Forward", "Key_Stop", "Key_Refresh", "Key_VolumeDown", "Key_VolumeMute", "Key_VolumeUp", "Key_BassBoost", "Key_BassUp", "Key_BassDown", "Key_TrebleUp", "Key_TrebleDown", "Key_MediaPlay", "Key_MediaStop", "Key_MediaPrevious", "Key_MediaNext", "Key_MediaRecord", "Key_MediaPause", "Key_MediaTogglePlayPause", "Key_HomePage", "Key_Favorites", "Key_Search", "Key_Standby", "Key_OpenUrl", "Key_LaunchMail", "Key_LaunchMedia", "Key_Launch0", "Key_Launch1", "Key_Launch2", "Key_Launch3", "Key_Launch4", "Key_Launch5", "Key_Launch6", "Key_Launch7", "Key_Launch8", "Key_Launch9", "Key_LaunchA", "Key_LaunchB", "Key_LaunchC", "Key_LaunchD", "Key_LaunchE", "Key_LaunchF", "Key_MonBrightnessUp", "Key_MonBrightnessDown", "Key_KeyboardLightOnOff", "Key_KeyboardBrightnessUp", "Key_KeyboardBrightnessDown", "Key_PowerOff", "Key_WakeUp", "Key_Eject", "Key_ScreenSaver", "Key_WWW", "Key_Memo", "Key_LightBulb", "Key_Shop", "Key_History", "Key_AddFavorite", "Key_HotLinks", "Key_BrightnessAdjust", "Key_Finance", "Key_Community", "Key_AudioRewind", "Key_BackForward", "Key_ApplicationLeft", "Key_ApplicationRight", "Key_Book", "Key_CD", "Key_Calculator", "Key_ToDoList", "Key_ClearGrab", "Key_Close", "Key_Copy", "Key_Cut", "Key_Display", "Key_DOS", "Key_Documents", "Key_Excel", "Key_Explorer", "Key_Game", "Key_Go", "Key_iTouch", "Key_LogOff", "Key_Market", "Key_Meeting", "Key_MenuKB", "Key_MenuPB", "Key_MySites", "Key_News", "Key_OfficeHome", "Key_Option", "Key_Paste", "Key_Phone", "Key_Calendar", "Key_Reply", "Key_Reload", "Key_RotateWindows", "Key_RotationPB", "Key_RotationKB", "Key_Save", "Key_Send", "Key_Spell", "Key_SplitScreen", "Key_Support", "Key_TaskPane", "Key_Terminal", "Key_Tools", "Key_Travel", "Key_Video", "Key_Word", "Key_Xfer", "Key_ZoomIn", "Key_ZoomOut", "Key_Away", "Key_Messenger", "Key_WebCam", "Key_MailForward", "Key_Pictures", "Key_Music", "Key_Battery", "Key_Bluetooth", "Key_WLAN", "Key_UWB", "Key_AudioForward", "Key_AudioRepeat", "Key_AudioRandomPlay", "Key_Subtitle", "Key_AudioCycleTrack", "Key_Time", "Key_Hibernate", "Key_View", "Key_TopMenu", "Key_PowerDown", "Key_Suspend", "Key_ContrastAdjust", "Key_LaunchG", "Key_LaunchH", "Key_TouchpadToggle", "Key_TouchpadOn", "Key_TouchpadOff", "Key_Mic<PERSON>ute", "Key_Red", "Key_Green", "Key_Yellow", "Key_Blue", "Key_ChannelUp", "Key_ChannelDown", "Key_Guide", "Key_Info", "Key_Settings", "Key_MicVolumeUp", "Key_MicVolumeDown", "Key_New", "Key_Open", "Key_Find", "Key_Undo", "Key_Redo", "Key_MediaLast", "Key_Select", "Key_Yes", "Key_No", "Key_Cancel", "Key_Printer", "Key_Execute", "Key_Sleep", "Key_Play", "Key_Zoom", "Key_Exit", "Key_Context1", "Key_Context2", "Key_Context3", "Key_Context4", "Key_Call", "Key_Hangup", "Key_Flip", "Key_ToggleCallHangup", "Key_VoiceDial", "Key_LastNumberRedial", "Key_Camera", "Key_CameraFocus", "Key_unknown"]}, {"isClass": false, "isFlag": false, "name": "KeyboardModifier", "values": ["NoModifier", "ShiftModifier", "ControlModifier", "AltModifier", "MetaModifier", "KeypadModifier", "GroupSwitchModifier", "KeyboardModifierMask"]}, {"alias": "KeyboardModifier", "isClass": false, "isFlag": true, "name": "KeyboardModifiers", "values": ["NoModifier", "ShiftModifier", "ControlModifier", "AltModifier", "MetaModifier", "KeypadModifier", "GroupSwitchModifier", "KeyboardModifierMask"]}, {"isClass": false, "isFlag": false, "name": "Modifier", "values": ["META", "SHIFT", "CTRL", "ALT", "MODIFIER_MASK"]}, {"alias": "Modifier", "isClass": false, "isFlag": true, "name": "Modifiers", "values": ["META", "SHIFT", "CTRL", "ALT", "MODIFIER_MASK"]}, {"isClass": false, "isFlag": false, "name": "ArrowType", "values": ["NoArrow", "UpArrow", "DownArrow", "LeftArrow", "RightArrow"]}, {"isClass": false, "isFlag": false, "name": "PenStyle", "values": ["NoPen", "SolidLine", "DashLine", "DotLine", "DashDotLine", "DashDotDotLine", "CustomDashLine"]}, {"isClass": false, "isFlag": false, "name": "PenCapStyle", "values": ["FlatCap", "SquareCap", "RoundCap", "MPenCapStyle"]}, {"isClass": false, "isFlag": false, "name": "PenJoinStyle", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Round<PERSON>oin", "SvgMiterJoin", "MPenJoinStyle"]}, {"isClass": false, "isFlag": false, "name": "BrushStyle", "values": ["NoBrush", "SolidPattern", "Dense1Pattern", "Dense2Pattern", "Dense3Pattern", "Dense4Pattern", "Dense5Pattern", "Dense6Pattern", "Dense7Pattern", "HorPattern", "VerPattern", "CrossPattern", "BDiagPattern", "FDiagPattern", "DiagCrossPattern", "LinearGradientPattern", "RadialGradientPattern", "ConicalGradientPattern", "TexturePattern"]}, {"isClass": false, "isFlag": false, "name": "SizeMode", "values": ["AbsoluteSize", "RelativeSize"]}, {"isClass": false, "isFlag": false, "name": "CursorShape", "values": ["ArrowCursor", "UpArrowCursor", "CrossCursor", "Wait<PERSON>ursor", "IBeamCursor", "SizeVerCursor", "SizeHorCursor", "SizeBDiagCursor", "SizeFDiagCursor", "SizeAllCursor", "BlankCursor", "SplitVCursor", "SplitHCursor", "PointingHandCursor", "ForbiddenCursor", "WhatsThisCursor", "BusyCursor", "OpenHandCursor", "ClosedHandCursor", "DragCopyCursor", "DragMoveCursor", "DragLinkCursor", "LastCursor", "BitmapCursor", "CustomCursor"]}, {"isClass": false, "isFlag": false, "name": "TextFormat", "values": ["PlainText", "RichText", "AutoText", "MarkdownText"]}, {"isClass": false, "isFlag": false, "name": "AspectRatioMode", "values": ["IgnoreAspectRatio", "KeepAspectRatio", "KeepAspectRatioByExpanding"]}, {"isClass": false, "isFlag": false, "name": "DockWidgetArea", "values": ["LeftDockWidgetArea", "RightDockWidgetArea", "TopDockWidgetArea", "BottomDockWidgetArea", "DockWidgetArea_Mask", "AllDockWidgetAreas", "NoDockWidgetArea"]}, {"alias": "DockWidgetArea", "isClass": false, "isFlag": true, "name": "DockWidgetAreas", "values": ["LeftDockWidgetArea", "RightDockWidgetArea", "TopDockWidgetArea", "BottomDockWidgetArea", "DockWidgetArea_Mask", "AllDockWidgetAreas", "NoDockWidgetArea"]}, {"isClass": false, "isFlag": false, "name": "ToolBarArea", "values": ["LeftToolBarArea", "RightToolBarArea", "TopToolBarArea", "BottomToolBarArea", "ToolBarArea_Mask", "AllToolBarAreas", "NoToolBarArea"]}, {"alias": "ToolBarArea", "isClass": false, "isFlag": true, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "values": ["LeftToolBarArea", "RightToolBarArea", "TopToolBarArea", "BottomToolBarArea", "ToolBarArea_Mask", "AllToolBarAreas", "NoToolBarArea"]}, {"isClass": false, "isFlag": false, "name": "DateFormat", "values": ["TextDate", "ISODate", "RFC2822Date", "ISODateWithMs"]}, {"isClass": false, "isFlag": false, "name": "TimeSpec", "values": ["LocalTime", "UTC", "OffsetFromUTC", "TimeZone"]}, {"isClass": false, "isFlag": false, "name": "DayOfWeek", "values": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]}, {"isClass": false, "isFlag": false, "name": "ScrollBarPolicy", "values": ["ScrollBarAsNeeded", "ScrollBarAlwaysOff", "ScrollBarAlwaysOn"]}, {"isClass": false, "isFlag": false, "name": "CaseSensitivity", "values": ["CaseInsensitive", "CaseSensitive"]}, {"isClass": false, "isFlag": false, "name": "Corner", "values": ["TopLeftCorner", "TopRightCorner", "BottomLeftCorner", "BottomRightCorner"]}, {"isClass": false, "isFlag": false, "name": "Edge", "values": ["TopEdge", "LeftEdge", "RightEdge", "BottomEdge"]}, {"alias": "Edge", "isClass": false, "isFlag": true, "name": "<PERSON>s", "values": ["TopEdge", "LeftEdge", "RightEdge", "BottomEdge"]}, {"isClass": false, "isFlag": false, "name": "ConnectionType", "values": ["AutoConnection", "DirectConnection", "QueuedConnection", "BlockingQueuedConnection", "UniqueConnection", "SingleShotConnection"]}, {"isClass": false, "isFlag": false, "name": "ShortcutContext", "values": ["WidgetShortcut", "WindowShortcut", "ApplicationShortcut", "WidgetWithChildrenShortcut"]}, {"isClass": false, "isFlag": false, "name": "FillRule", "values": ["OddEvenFill", "WindingFill"]}, {"isClass": false, "isFlag": false, "name": "MaskMode", "values": ["MaskInColor", "MaskOutColor"]}, {"isClass": false, "isFlag": false, "name": "ClipOperation", "values": ["NoClip", "<PERSON>lace<PERSON><PERSON>", "IntersectClip"]}, {"isClass": false, "isFlag": false, "name": "ItemSelectionMode", "values": ["ContainsItemShape", "IntersectsItemShape", "ContainsItemBoundingRect", "IntersectsItemBoundingRect"]}, {"isClass": false, "isFlag": false, "name": "ItemSelectionOperation", "values": ["ReplaceSelection", "AddToSelection"]}, {"isClass": false, "isFlag": false, "name": "TransformationMode", "values": ["FastTransformation", "SmoothTransformation"]}, {"isClass": false, "isFlag": false, "name": "Axis", "values": ["XAxis", "YA<PERSON>s", "ZAxis"]}, {"isClass": false, "isFlag": false, "name": "FocusReason", "values": ["MouseFocusReason", "TabFocusReason", "BacktabFocusReason", "ActiveWindowFocusReason", "PopupFocusReason", "ShortcutFocusReason", "MenuBarFocusReason", "OtherFocusReason", "NoFocusReason"]}, {"isClass": false, "isFlag": false, "name": "ContextMenuPolicy", "values": ["NoContextMenu", "DefaultContextMenu", "ActionsContextMenu", "CustomContextMenu", "PreventContextMenu"]}, {"isClass": true, "isFlag": false, "name": "ContextMenuTrigger", "values": ["Press", "Release"]}, {"isClass": false, "isFlag": false, "name": "InputMethodQuery", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "ImCursorRectangle", "ImFont", "ImCursorPosition", "ImSurroundingText", "ImCurrentSelection", "ImMaximumTextLength", "ImAnchorPosition", "ImHints", "ImPreferredLanguage", "ImAbsolutePosition", "ImTextBeforeCursor", "ImTextAfterCursor", "ImEnterKeyType", "ImAnchorRectangle", "ImInputItemClipRectangle", "ImReadOnly", "ImPlatformData", "ImQueryInput", "ImQueryAll"]}, {"alias": "InputMethodQuery", "isClass": false, "isFlag": true, "name": "InputMethodQueries", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "ImCursorRectangle", "ImFont", "ImCursorPosition", "ImSurroundingText", "ImCurrentSelection", "ImMaximumTextLength", "ImAnchorPosition", "ImHints", "ImPreferredLanguage", "ImAbsolutePosition", "ImTextBeforeCursor", "ImTextAfterCursor", "ImEnterKeyType", "ImAnchorRectangle", "ImInputItemClipRectangle", "ImReadOnly", "ImPlatformData", "ImQueryInput", "ImQueryAll"]}, {"isClass": false, "isFlag": false, "name": "InputMethodHint", "values": ["ImhNone", "ImhHiddenText", "ImhSensitiveData", "ImhNoAutoUppercase", "ImhPreferNumbers", "ImhPreferUppercase", "ImhPreferLowercase", "ImhNoPredictiveText", "ImhDate", "ImhTime", "ImhPreferLatin", "ImhMultiLine", "ImhNoEditMenu", "ImhNoTextHandles", "ImhDigitsOnly", "ImhFormattedNumbersOnly", "ImhUppercaseOnly", "ImhLowercaseOnly", "ImhDialableCharactersOnly", "ImhEmailCharactersOnly", "ImhUrlCharactersOnly", "ImhLatinOnly", "ImhExclusiveInputMask"]}, {"alias": "InputMethodHint", "isClass": false, "isFlag": true, "name": "InputMethodHints", "values": ["ImhNone", "ImhHiddenText", "ImhSensitiveData", "ImhNoAutoUppercase", "ImhPreferNumbers", "ImhPreferUppercase", "ImhPreferLowercase", "ImhNoPredictiveText", "ImhDate", "ImhTime", "ImhPreferLatin", "ImhMultiLine", "ImhNoEditMenu", "ImhNoTextHandles", "ImhDigitsOnly", "ImhFormattedNumbersOnly", "ImhUppercaseOnly", "ImhLowercaseOnly", "ImhDialableCharactersOnly", "ImhEmailCharactersOnly", "ImhUrlCharactersOnly", "ImhLatinOnly", "ImhExclusiveInputMask"]}, {"isClass": false, "isFlag": false, "name": "EnterKeyType", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EnterKeyReturn", "EnterKeyDone", "EnterKeyGo", "EnterKeySend", "EnterKeySearch", "EnterKeyNext", "EnterKeyPrevious"]}, {"isClass": false, "isFlag": false, "name": "ToolButtonStyle", "values": ["ToolButtonIconOnly", "ToolButtonTextOnly", "ToolButtonTextBesideIcon", "ToolButtonTextUnderIcon", "ToolButtonFollowStyle"]}, {"isClass": false, "isFlag": false, "name": "LayoutDirection", "values": ["LeftToRight", "RightToLeft", "LayoutDirectionAuto"]}, {"isClass": false, "isFlag": false, "name": "DropAction", "values": ["CopyAction", "MoveAction", "LinkAction", "ActionMask", "TargetMoveAction", "IgnoreAction"]}, {"alias": "DropAction", "isClass": false, "isFlag": true, "name": "DropActions", "values": ["CopyAction", "MoveAction", "LinkAction", "ActionMask", "TargetMoveAction", "IgnoreAction"]}, {"isClass": false, "isFlag": false, "name": "CheckState", "values": ["Unchecked", "PartiallyChecked", "Checked"]}, {"isClass": false, "isFlag": false, "name": "ItemDataRole", "values": ["DisplayRole", "DecorationRole", "EditRole", "ToolTipRole", "StatusTipRole", "WhatsThisRole", "FontRole", "TextAlignmentRole", "BackgroundRole", "ForegroundRole", "CheckStateRole", "AccessibleTextRole", "AccessibleDescriptionRole", "SizeHintRole", "InitialSortOrderRole", "DisplayPropertyRole", "DecorationPropertyRole", "ToolTipPropertyRole", "StatusTipPropertyRole", "WhatsThisPropertyRole", "UserRole"]}, {"alias": "ItemFlag", "isClass": false, "isFlag": true, "name": "ItemFlags", "values": ["NoItemFlags", "ItemIsSelectable", "ItemIsEditable", "ItemIsDragEnabled", "ItemIsDropEnabled", "ItemIsUserCheckable", "ItemIsEnabled", "ItemIsAutoTristate", "ItemNeverHasChildren", "ItemIsUserTristate"]}, {"alias": "MatchFlag", "isClass": false, "isFlag": true, "name": "MatchFlags", "values": ["MatchExactly", "MatchContains", "MatchStartsWith", "MatchEndsWith", "MatchRegularExpression", "MatchWildcard", "MatchFixedString", "MatchTypeMask", "MatchCaseSensitive", "MatchWrap", "MatchRecursive"]}, {"isClass": false, "isFlag": false, "name": "WindowModality", "values": ["NonModal", "WindowModal", "ApplicationModal"]}, {"isClass": false, "isFlag": false, "name": "TextInteractionFlag", "values": ["NoTextInteraction", "TextSelectableByMouse", "TextSelectableByKeyboard", "LinksAccessibleByMouse", "LinksAccessibleByKeyboard", "TextEditable", "TextEditorInteraction", "TextBrowserInteraction"]}, {"alias": "TextInteractionFlag", "isClass": false, "isFlag": true, "name": "TextInteractionFlags", "values": ["NoTextInteraction", "TextSelectableByMouse", "TextSelectableByKeyboard", "LinksAccessibleByMouse", "LinksAccessibleByKeyboard", "TextEditable", "TextEditorInteraction", "TextBrowserInteraction"]}, {"isClass": false, "isFlag": false, "name": "SizeHint", "values": ["MinimumSize", "PreferredSize", "MaximumSize", "MinimumDescent", "NSizeHints"]}, {"alias": "TouchPointState", "isClass": false, "isFlag": true, "name": "TouchPointStates", "values": ["TouchPointUnknownState", "TouchPointPressed", "TouchPointMoved", "TouchPointStationary", "TouchPointReleased"]}, {"isClass": false, "isFlag": false, "name": "GestureState", "values": ["NoGesture", "GestureStarted", "GestureUpdated", "GestureFinished", "GestureCanceled"]}, {"isClass": false, "isFlag": false, "name": "GestureType", "values": ["TapGesture", "TapAndHoldGesture", "PanGesture", "PinchGesture", "SwipeGesture", "CustomGesture", "LastGestureType"]}, {"isClass": false, "isFlag": false, "name": "NativeGestureType", "values": ["BeginNativeGesture", "EndNativeGesture", "PanNativeGesture", "ZoomNativeGesture", "SmartZoomNativeGesture", "RotateNativeGesture", "SwipeNativeGesture"]}, {"isClass": false, "isFlag": false, "name": "CursorMoveStyle", "values": ["LogicalMoveStyle", "VisualMoveStyle"]}, {"isClass": false, "isFlag": false, "name": "TimerType", "values": ["PreciseTimer", "CoarseTimer", "VeryCoarseTimer"]}, {"isClass": true, "isFlag": false, "name": "TimerId", "values": ["Invalid"]}, {"isClass": false, "isFlag": false, "name": "ScrollPhase", "values": ["NoScrollPhase", "ScrollBegin", "ScrollUpdate", "ScrollEnd", "ScrollMomentum"]}, {"isClass": false, "isFlag": false, "name": "MouseEventSource", "values": ["MouseEventNotSynthesized", "MouseEventSynthesizedBySystem", "MouseEventSynthesizedByQt", "MouseEventSynthesizedByApplication"]}, {"alias": "MouseEventFlag", "isClass": false, "isFlag": true, "name": "MouseEventFlags", "values": ["NoMouseEventFlag", "MouseEventCreatedDoubleClick", "MouseEventFlagMask"]}, {"isClass": false, "isFlag": false, "name": "ChecksumType", "values": ["ChecksumIso3309", "ChecksumItuV41"]}, {"isClass": true, "isFlag": false, "name": "HighDpiScaleFactorRoundingPolicy", "values": ["Unset", "Round", "Ceil", "Floor", "RoundPreferFloor", "PassThrough"]}, {"isClass": true, "isFlag": false, "name": "PermissionStatus", "values": ["Undetermined", "Granted", "Denied"]}], "lineNumber": 25, "namespace": true, "qualifiedClassName": "Qt"}], "inputFile": "qnamespace.h", "outputRevision": 69}, {"classes": [{"className": "QNonContiguousByteDevice", "lineNumber": 28, "object": true, "qualifiedClassName": "QNonContiguousByteDevice", "signals": [{"access": "public", "index": 0, "name": "readyRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "current", "type": "qint64"}, {"name": "total", "type": "qint64"}], "index": 1, "name": "readProgress", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QNonContiguousByteDeviceByteArrayImpl", "lineNumber": 67, "object": true, "qualifiedClassName": "QNonContiguousByteDeviceByteArrayImpl", "superClasses": [{"access": "public", "name": "QNonContiguousByteDevice"}]}, {"className": "QNonContiguousByteDeviceRingBufferImpl", "lineNumber": 87, "object": true, "qualifiedClassName": "QNonContiguousByteDeviceRingBufferImpl", "superClasses": [{"access": "public", "name": "QNonContiguousByteDevice"}]}, {"className": "QNonContiguousByteDeviceIoDeviceImpl", "lineNumber": 105, "object": true, "qualifiedClassName": "QNonContiguousByteDeviceIoDeviceImpl", "superClasses": [{"access": "public", "name": "QNonContiguousByteDevice"}]}, {"className": "QByteDeviceWrappingIoDevice", "lineNumber": 130, "object": true, "qualifiedClassName": "QByteDeviceWrappingIoDevice", "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qnoncontiguousbytedevice_p.h", "outputRevision": 69}, {"classes": [{"className": "QObjectCleanupHandler", "lineNumber": 11, "object": true, "qualifiedClassName": "QObjectCleanupHandler", "slots": [{"access": "private", "arguments": [{"type": "QObject*"}], "index": 0, "name": "objectDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qobjectcleanuphandler.h", "outputRevision": 69}, {"classes": [{"className": "QParallelAnimationGroup", "lineNumber": 14, "object": true, "qualifiedClassName": "QParallelAnimationGroup", "slots": [{"access": "private", "index": 0, "name": "_q_uncontrolledAnimationFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAnimationGroup"}]}], "inputFile": "qparallelanimationgroup.h", "outputRevision": 69}, {"classes": [{"className": "QPauseAnimation", "lineNumber": 15, "object": true, "properties": [{"bindable": "bindableDuration", "constant": false, "designable": true, "final": false, "index": 0, "name": "duration", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDuration"}], "qualifiedClassName": "QPauseAnimation", "superClasses": [{"access": "public", "name": "QAbstractAnimation"}]}], "inputFile": "qpauseanimation.h", "outputRevision": 69}, {"classes": [{"className": "QLocationPermission", "enums": [{"isClass": false, "isFlag": false, "name": "Accuracy", "type": "quint8", "values": ["Approximate", "Precise"]}, {"isClass": false, "isFlag": false, "name": "Availability", "type": "quint8", "values": ["WhenInUse", "Always"]}], "gadget": true, "lineNumber": 98, "qualifiedClassName": "QLocationPermission"}, {"className": "QCalendarPermission", "enums": [{"isClass": false, "isFlag": false, "name": "AccessMode", "type": "quint8", "values": ["Read<PERSON>nly", "ReadWrite"]}], "gadget": true, "lineNumber": 131, "qualifiedClassName": "QCalendarPermission"}, {"className": "QContactsPermission", "enums": [{"isClass": false, "isFlag": false, "name": "AccessMode", "type": "quint8", "values": ["Read<PERSON>nly", "ReadWrite"]}], "gadget": true, "lineNumber": 154, "qualifiedClassName": "QContactsPermission"}, {"className": "QBluetoothPermission", "enums": [{"alias": "CommunicationMode", "isClass": false, "isFlag": true, "name": "CommunicationModes", "type": "quint8", "values": ["Access", "Advertise", "<PERSON><PERSON><PERSON>"]}], "gadget": true, "lineNumber": 177, "qualifiedClassName": "QBluetoothPermission"}], "inputFile": "qpermissions.h", "outputRevision": 69}, {"classes": [{"className": "QPlugin<PERSON><PERSON>der", "lineNumber": 20, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fileName", "read": "fileName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setFileName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "loadHints", "read": "loadHints", "required": false, "scriptable": true, "stored": true, "type": "QLibrary::LoadHints", "user": false, "write": "setLoadHints"}], "qualifiedClassName": "QPlugin<PERSON><PERSON>der", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qpluginloader.h", "outputRevision": 69}, {"classes": [{"className": "QProcess", "enums": [{"isClass": false, "isFlag": false, "name": "ProcessError", "values": ["FailedToStart", "Crashed", "Timedout", "ReadError", "WriteError", "UnknownE<PERSON>r"]}, {"isClass": false, "isFlag": false, "name": "ProcessState", "values": ["NotRunning", "Starting", "Running"]}, {"isClass": false, "isFlag": false, "name": "ProcessChannel", "values": ["StandardOutput", "StandardError"]}, {"isClass": false, "isFlag": false, "name": "ProcessChannelMode", "values": ["SeparateChannels", "MergedChannels", "ForwardedChannels", "ForwardedOutputChannel", "ForwardedErrorChannel"]}, {"isClass": false, "isFlag": false, "name": "InputChannelMode", "values": ["ManagedInputChannel", "ForwardedInputChannel"]}, {"isClass": false, "isFlag": false, "name": "ExitStatus", "values": ["NormalExit", "CrashExit"]}], "lineNumber": 81, "object": true, "qualifiedClassName": "QProcess", "signals": [{"access": "public", "index": 0, "name": "started", "returnType": "void"}, {"access": "public", "arguments": [{"name": "exitCode", "type": "int"}, {"name": "exitStatus", "type": "QProcess::ExitStatus"}], "index": 1, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "exitCode", "type": "int"}], "index": 2, "isCloned": true, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QProcess::ProcessError"}], "index": 3, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QProcess::ProcessState"}], "index": 4, "name": "stateChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "readyReadStandardOutput", "returnType": "void"}, {"access": "public", "index": 6, "name": "readyReadStandardError", "returnType": "void"}], "slots": [{"access": "public", "index": 7, "name": "terminate", "returnType": "void"}, {"access": "public", "index": 8, "name": "kill", "returnType": "void"}, {"access": "private", "index": 9, "name": "_q_canReadStandardOutput", "returnType": "bool"}, {"access": "private", "index": 10, "name": "_q_canReadStandardError", "returnType": "bool"}, {"access": "private", "index": 11, "name": "_q_startupNotification", "returnType": "bool"}, {"access": "private", "index": 12, "name": "_q_processDied", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qprocess.h", "outputRevision": 69}, {"classes": [{"className": "QPropertyAnimation", "lineNumber": 14, "object": true, "properties": [{"bindable": "bindablePropertyName", "constant": false, "designable": true, "final": false, "index": 0, "name": "propertyName", "read": "propertyName", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false, "write": "setPropertyName"}, {"bindable": "bindableTargetObject", "constant": false, "designable": true, "final": false, "index": 1, "name": "targetObject", "read": "targetObject", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setTargetObject"}], "qualifiedClassName": "QPropertyAnimation", "superClasses": [{"access": "public", "name": "QVariantAnimation"}]}], "inputFile": "qpropertyanimation.h", "outputRevision": 69}, {"classes": [{"className": "QSaveFile", "lineNumber": 23, "object": true, "qualifiedClassName": "QSaveFile", "superClasses": [{"access": "public", "name": "QFileDevice"}]}], "inputFile": "qsavefile.h", "outputRevision": 69}, {"classes": [{"className": "QSequentialAnimationGroup", "lineNumber": 16, "object": true, "properties": [{"bindable": "bindableCurrentAnimation", "constant": false, "designable": true, "final": false, "index": 0, "name": "currentAnimation", "notify": "currentAnimationChanged", "read": "currentAnimation", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAnimation*", "user": false}], "qualifiedClassName": "QSequentialAnimationGroup", "signals": [{"access": "public", "arguments": [{"name": "current", "type": "QAbstractAnimation*"}], "index": 0, "name": "currentAnimationChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "_q_uncontrolledAnimationFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAnimationGroup"}]}], "inputFile": "qsequentialanimationgroup.h", "outputRevision": 69}, {"classes": [{"className": "QSettings", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["NoError", "AccessError", "FormatError"]}, {"isClass": false, "isFlag": false, "name": "Format", "values": ["NativeFormat", "IniFormat", "Registry32Format", "Registry64Format", "InvalidFormat", "CustomFormat1", "CustomFormat2", "CustomFormat3", "CustomFormat4", "CustomFormat5", "CustomFormat6", "CustomFormat7", "CustomFormat8", "CustomFormat9", "CustomFormat10", "CustomFormat11", "CustomFormat12", "CustomFormat13", "CustomFormat14", "CustomFormat15", "CustomFormat16"]}, {"isClass": false, "isFlag": false, "name": "<PERSON><PERSON>", "values": ["UserScope", "SystemScope"]}], "lineNumber": 26, "object": true, "qualifiedClassName": "QSettings", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsettings.h", "outputRevision": 69}, {"classes": [{"className": "QSharedMemory", "enums": [{"isClass": false, "isFlag": false, "name": "AccessMode", "values": ["Read<PERSON>nly", "ReadWrite"]}, {"isClass": false, "isFlag": false, "name": "SharedMemoryError", "values": ["NoError", "PermissionDenied", "InvalidSize", "KeyError", "AlreadyExists", "NotFound", "LockError", "OutOfResources", "UnknownE<PERSON>r"]}], "lineNumber": 22, "object": true, "qualifiedClassName": "QSharedMemory", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsharedmemory.h", "outputRevision": 69}, {"classes": [{"className": "QSignalMapper", "lineNumber": 13, "object": true, "qualifiedClassName": "QSignalMapper", "signals": [{"access": "public", "arguments": [{"type": "int"}], "index": 0, "name": "mappedInt", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 1, "name": "mappedString", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QObject*"}], "index": 2, "name": "mappedObject", "returnType": "void"}], "slots": [{"access": "public", "index": 3, "name": "map", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sender", "type": "QObject*"}], "index": 4, "name": "map", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsignalmapper.h", "outputRevision": 69}, {"classes": [{"className": "QSingleShotTimer", "lineNumber": 30, "object": true, "qualifiedClassName": "QSingleShotTimer", "signals": [{"access": "public", "index": 0, "name": "timeout", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsingleshottimer_p.h", "outputRevision": 69}, {"classes": [{"className": "QSocketNotifier", "lineNumber": 13, "object": true, "qualifiedClassName": "QSocketNotifier", "signals": [{"access": "public", "arguments": [{"name": "socket", "type": "QSocketDescriptor"}, {"name": "activationEvent", "type": "QSocketNotifier::Type"}], "index": 0, "name": "activated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socket", "type": "QSocketDescriptor"}], "index": 1, "isCloned": true, "name": "activated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socket", "type": "int"}], "index": 2, "name": "activated", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"type": "bool"}], "index": 3, "name": "setEnabled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsocketnotifier.h", "outputRevision": 69}, {"classes": [{"className": "QSortFilterProxyModel", "lineNumber": 20, "object": true, "properties": [{"bindable": "bindableFilterRegularExpression", "constant": false, "designable": true, "final": false, "index": 0, "name": "filterRegularExpression", "read": "filterRegularExpression", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setFilterRegularExpression"}, {"bindable": "bindableFilterKeyColumn", "constant": false, "designable": true, "final": false, "index": 1, "name": "filterKeyColumn", "read": "filterKeyColumn", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFilterKeyColumn"}, {"bindable": "bindableDynamicSortFilter", "constant": false, "designable": true, "final": false, "index": 2, "name": "dynamicSortFilter", "read": "dynamicSortFilter", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDynamicSortFilter"}, {"bindable": "bindableFilterCaseSensitivity", "constant": false, "designable": true, "final": false, "index": 3, "name": "filterCaseSensitivity", "notify": "filterCaseSensitivityChanged", "read": "filterCaseSensitivity", "required": false, "scriptable": true, "stored": true, "type": "Qt::CaseSensitivity", "user": false, "write": "setFilterCaseSensitivity"}, {"bindable": "bindableSortCaseSensitivity", "constant": false, "designable": true, "final": false, "index": 4, "name": "sortCaseSensitivity", "notify": "sortCaseSensitivityChanged", "read": "sortCaseSensitivity", "required": false, "scriptable": true, "stored": true, "type": "Qt::CaseSensitivity", "user": false, "write": "setSortCaseSensitivity"}, {"bindable": "bindableIsSortLocaleAware", "constant": false, "designable": true, "final": false, "index": 5, "name": "isSortLocaleAware", "notify": "sortLocaleAwareChanged", "read": "isSortLocaleAware", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSortLocaleAware"}, {"bindable": "bindableSortRole", "constant": false, "designable": true, "final": false, "index": 6, "name": "sortRole", "notify": "sortRoleChanged", "read": "sortRole", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSortRole"}, {"bindable": "bindableFilterRole", "constant": false, "designable": true, "final": false, "index": 7, "name": "filterRole", "notify": "filterRoleChanged", "read": "filterRole", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFilterRole"}, {"bindable": "bindableRecursiveFilteringEnabled", "constant": false, "designable": true, "final": false, "index": 8, "name": "recursiveFilteringEnabled", "notify": "recursiveFilteringEnabledChanged", "read": "isRecursiveFilteringEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRecursiveFilteringEnabled"}, {"bindable": "bindableAutoAcceptChildRows", "constant": false, "designable": true, "final": false, "index": 9, "name": "autoAcceptChildRows", "notify": "autoAcceptChildRowsChanged", "read": "autoAcceptChildRows", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoAcceptChildRows"}], "qualifiedClassName": "QSortFilterProxyModel", "signals": [{"access": "public", "arguments": [{"name": "dynamicSortFilter", "type": "bool"}], "index": 0, "name": "dynamicSortFilterChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filterCaseSensitivity", "type": "Qt::CaseSensitivity"}], "index": 1, "name": "filterCaseSensitivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sortCaseSensitivity", "type": "Qt::CaseSensitivity"}], "index": 2, "name": "sortCaseSensitivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sortLocaleAware", "type": "bool"}], "index": 3, "name": "sortLocaleAwareChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sortRole", "type": "int"}], "index": 4, "name": "sortRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filterRole", "type": "int"}], "index": 5, "name": "filterRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "recursiveFilteringEnabled", "type": "bool"}], "index": 6, "name": "recursiveFilteringEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "autoAcceptChildRows", "type": "bool"}], "index": 7, "name": "autoAcceptChildRowsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "pattern", "type": "QString"}], "index": 8, "name": "setFilterRegularExpression", "returnType": "void"}, {"access": "public", "arguments": [{"name": "regularExpression", "type": "QRegularExpression"}], "index": 9, "name": "setFilterRegularExpression", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QString"}], "index": 10, "name": "setFilterWildcard", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QString"}], "index": 11, "name": "setFilterFixedString", "returnType": "void"}, {"access": "public", "index": 12, "name": "invalidate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractProxyModel"}]}], "inputFile": "qsortfilterproxymodel.h", "outputRevision": 69}, {"classes": [{"className": "QS<PERSON>dard<PERSON><PERSON><PERSON>", "enums": [{"isClass": false, "isFlag": false, "name": "StandardLocation", "values": ["DesktopLocation", "DocumentsLocation", "FontsLocation", "ApplicationsLocation", "MusicLocation", "MoviesLocation", "PicturesLocation", "TempLocation", "HomeLocation", "AppLocalDataLocation", "CacheLocation", "GenericDataLocation", "RuntimeLocation", "ConfigLocation", "DownloadLocation", "GenericCacheLocation", "GenericConfigLocation", "AppDataLocation", "AppConfigLocation", "PublicShareLocation", "TemplatesLocation", "StateLocation", "GenericStateLocation"]}, {"alias": "LocateOption", "isClass": false, "isFlag": true, "name": "LocateOptions", "values": ["LocateFile", "LocateDirectory"]}], "gadget": true, "lineNumber": 15, "qualifiedClassName": "QS<PERSON>dard<PERSON><PERSON><PERSON>"}], "inputFile": "qstandardpaths.h", "outputRevision": 69}, {"classes": [{"className": "QStringListModel", "lineNumber": 14, "object": true, "qualifiedClassName": "QStringListModel", "superClasses": [{"access": "public", "name": "QAbstractListModel"}]}], "inputFile": "qstringlistmodel.h", "outputRevision": 69}, {"classes": [{"className": "QSystemSemaphore", "enums": [{"isClass": false, "isFlag": false, "name": "AccessMode", "values": ["Open", "Create"]}], "gadget": true, "lineNumber": 20, "qualifiedClassName": "QSystemSemaphore"}], "inputFile": "qsystemsemaphore.h", "outputRevision": 69}, {"classes": [{"className": "QTemporaryFile", "lineNumber": 22, "object": true, "qualifiedClassName": "QTemporaryFile", "superClasses": [{"access": "public", "name": "QFile"}]}], "inputFile": "qtemporaryfile.h", "outputRevision": 69}, {"classes": [{"className": "QDeviceClosedNotifier", "lineNumber": 28, "object": true, "qualifiedClassName": "QDeviceClosedNotifier", "slots": [{"access": "public", "index": 0, "name": "flushStream", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtextstream_p.h", "outputRevision": 69}, {"classes": [{"className": "QThread", "enums": [{"isClass": true, "isFlag": false, "name": "QualityOfService", "values": ["Auto", "High", "Eco"]}], "lineNumber": 27, "object": true, "qualifiedClassName": "QThread", "signals": [{"access": "public", "index": 0, "name": "started", "returnType": "void"}, {"access": "public", "index": 1, "name": "finished", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"type": "Priority"}], "index": 2, "name": "start", "returnType": "void"}, {"access": "public", "index": 3, "isCloned": true, "name": "start", "returnType": "void"}, {"access": "public", "index": 4, "name": "terminate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "retcode", "type": "int"}], "index": 5, "name": "exit", "returnType": "void"}, {"access": "public", "index": 6, "isCloned": true, "name": "exit", "returnType": "void"}, {"access": "public", "index": 7, "name": "quit", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qthread.h", "outputRevision": 69}, {"classes": [{"className": "QThreadPool", "lineNumber": 21, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "expiryTimeout", "read": "expiryTimeout", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setExpiryTimeout"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "maxThreadCount", "read": "maxThreadCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaxThreadCount"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "activeThreadCount", "read": "activeThreadCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "stackSize", "read": "stackSize", "required": false, "scriptable": true, "stored": true, "type": "uint", "user": false, "write": "setStackSize"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "threadPriority", "read": "threadPriority", "required": false, "scriptable": true, "stored": true, "type": "QThread::Priority", "user": false, "write": "setThreadPriority"}], "qualifiedClassName": "QThreadPool", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qthreadpool.h", "outputRevision": 69}, {"classes": [{"className": "QTimeLine", "lineNumber": 18, "object": true, "properties": [{"bindable": "bindableDuration", "constant": false, "designable": true, "final": false, "index": 0, "name": "duration", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDuration"}, {"bindable": "bindableUpdateInterval", "constant": false, "designable": true, "final": false, "index": 1, "name": "updateInterval", "read": "updateInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setUpdateInterval"}, {"bindable": "bindableCurrentTime", "constant": false, "designable": true, "final": false, "index": 2, "name": "currentTime", "read": "currentTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCurrentTime"}, {"bindable": "bindableDirection", "constant": false, "designable": true, "final": false, "index": 3, "name": "direction", "read": "direction", "required": false, "scriptable": true, "stored": true, "type": "Direction", "user": false, "write": "setDirection"}, {"bindable": "bindableLoopCount", "constant": false, "designable": true, "final": false, "index": 4, "name": "loopCount", "read": "loopCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoopCount"}, {"bindable": "bindableEasingCurve", "constant": false, "designable": true, "final": false, "index": 5, "name": "easingCurve", "read": "easingCurve", "required": false, "scriptable": true, "stored": true, "type": "QEasingCurve", "user": false, "write": "setEasingCurve"}], "qualifiedClassName": "QTimeLine", "signals": [{"access": "public", "arguments": [{"name": "x", "type": "qreal"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 1, "name": "frameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newState", "type": "QTimeLine::State"}], "index": 2, "name": "stateChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "finished", "returnType": "void"}], "slots": [{"access": "public", "index": 4, "name": "start", "returnType": "void"}, {"access": "public", "index": 5, "name": "resume", "returnType": "void"}, {"access": "public", "index": 6, "name": "stop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "paused", "type": "bool"}], "index": 7, "name": "setPaused", "returnType": "void"}, {"access": "public", "arguments": [{"name": "msec", "type": "int"}], "index": 8, "name": "setCurrentTime", "returnType": "void"}, {"access": "public", "index": 9, "name": "toggleDirection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtimeline.h", "outputRevision": 69}, {"classes": [{"className": "QTimer", "lineNumber": 19, "object": true, "properties": [{"bindable": "bindableSingleShot", "constant": false, "designable": true, "final": false, "index": 0, "name": "singleShot", "read": "isSingleShot", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSingleShot"}, {"bindable": "bindableInterval", "constant": false, "designable": true, "final": false, "index": 1, "name": "interval", "read": "interval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setInterval"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "remainingTime", "read": "remainingTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"bindable": "bindableTimerType", "constant": false, "designable": true, "final": false, "index": 3, "name": "timerType", "read": "timerType", "required": false, "scriptable": true, "stored": true, "type": "Qt::TimerType", "user": false, "write": "setTimerType"}, {"bindable": "bindableActive", "constant": false, "designable": true, "final": false, "index": 4, "name": "active", "read": "isActive", "required": false, "scriptable": true, "stored": false, "type": "bool", "user": false}], "qualifiedClassName": "QTimer", "signals": [{"access": "public", "index": 0, "name": "timeout", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "msec", "type": "int"}], "index": 1, "name": "start", "returnType": "void"}, {"access": "public", "index": 2, "name": "start", "returnType": "void"}, {"access": "public", "index": 3, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtimer.h", "outputRevision": 69}, {"classes": [{"className": "QNativeIpcKey", "enums": [{"isClass": true, "isFlag": false, "name": "Type", "type": "quint16", "values": ["SystemV", "PosixRealtime", "Windows"]}], "gadget": true, "lineNumber": 17, "qualifiedClassName": "QNativeIpcKey"}], "inputFile": "qtipccommon.h", "outputRevision": 69}, {"classes": [{"className": "QTranslator", "lineNumber": 18, "object": true, "qualifiedClassName": "QTranslator", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtranslator.h", "outputRevision": 69}, {"classes": [{"className": "QTransposeProxyModel", "lineNumber": 16, "object": true, "qualifiedClassName": "QTransposeProxyModel", "superClasses": [{"access": "public", "name": "QAbstractProxyModel"}]}], "inputFile": "qtransposeproxymodel.h", "outputRevision": 69}, {"classes": [{"className": "QVariantAnimation", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "startValue", "read": "startValue", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setStartValue"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "endValue", "read": "endValue", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setEndValue"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "currentValue", "notify": "valueChanged", "read": "currentValue", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false}, {"bindable": "bindableDuration", "constant": false, "designable": true, "final": false, "index": 3, "name": "duration", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDuration"}, {"bindable": "bindableEasingCurve", "constant": false, "designable": true, "final": false, "index": 4, "name": "easingCurve", "read": "easingCurve", "required": false, "scriptable": true, "stored": true, "type": "QEasingCurve", "user": false, "write": "setEasingCurve"}], "qualifiedClassName": "QVariantAnimation", "signals": [{"access": "public", "arguments": [{"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAnimation"}]}], "inputFile": "qvariantanimation.h", "outputRevision": 69}, {"classes": [{"className": "QWindowsPipeReader", "lineNumber": 27, "object": true, "qualifiedClassName": "QWindowsPipeReader", "signals": [{"access": "public", "arguments": [{"type": "<PERSON><PERSON>"}, {"type": "QString"}], "index": 0, "name": "winError", "returnType": "void"}, {"access": "public", "index": 1, "name": "readyRead", "returnType": "void"}, {"access": "public", "index": 2, "name": "pipeClosed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwindowspipereader_p.h", "outputRevision": 69}, {"classes": [{"className": "QWindowsPipeWriter", "lineNumber": 27, "object": true, "qualifiedClassName": "QWindowsPipeWriter", "signals": [{"access": "public", "arguments": [{"name": "bytes", "type": "qint64"}], "index": 0, "name": "bytes<PERSON>ritten", "returnType": "void"}, {"access": "public", "index": 1, "name": "writeFailed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwindowspipewriter_p.h", "outputRevision": 69}, {"classes": [{"className": "QWinEventNotifier", "lineNumber": 14, "object": true, "qualifiedClassName": "QWinEventNotifier", "signals": [{"access": "public", "arguments": [{"name": "hEvent", "type": "HANDLE"}], "index": 0, "name": "activated", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 1, "name": "setEnabled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwineventnotifier.h", "outputRevision": 69}, {"classes": [{"className": "QEmptyItemModel", "lineNumber": 692, "object": true, "qualifiedClassName": "QEmptyItemModel", "superClasses": [{"access": "public", "name": "QAbstractItemModel"}]}], "inputFile": "qabstractitemmodel.cpp", "outputRevision": 69}, {"classes": [{"className": "QWindowsRemovableDriveListener", "lineNumber": 57, "object": true, "qualifiedClassName": "QWindowsRemovableDriveListener", "signals": [{"access": "public", "index": 0, "name": "driveAdded", "returnType": "void"}, {"access": "public", "index": 1, "name": "driveRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 2, "name": "driveRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 3, "name": "driveLockForRemoval", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 4, "name": "driveLockForRemovalFailed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QAbstractNativeEventFilter"}]}], "inputFile": "qfilesystemwatcher_win.cpp", "outputRevision": 69}, {"classes": [{"className": "QObjectContinuationWrapper", "lineNumber": 45, "object": true, "qualifiedClassName": "QObjectContinuationWrapper", "signals": [{"access": "public", "index": 0, "name": "run", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qfutureinterface.cpp", "outputRevision": 69}, {"classes": [{"className": "QThreadPoolThread", "lineNumber": 24, "object": true, "qualifiedClassName": "QThreadPoolThread", "superClasses": [{"access": "public", "name": "QThread"}]}], "inputFile": "qthreadpool.cpp", "outputRevision": 69}, {"classes": [{"className": "QObject", "constructors": [{"access": "public", "arguments": [{"name": "parent", "type": "QObject*"}], "index": 0, "name": "QObject", "returnType": ""}, {"access": "public", "index": 1, "isCloned": true, "name": "QObject", "returnType": ""}], "lineNumber": 104, "object": true, "properties": [{"bindable": "bindableObjectName", "constant": false, "designable": true, "final": false, "index": 0, "name": "objectName", "notify": "objectNameChanged", "read": "objectName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setObjectName"}], "qualifiedClassName": "QObject", "signals": [{"access": "public", "arguments": [{"type": "QObject*"}], "index": 0, "name": "destroyed", "returnType": "void"}, {"access": "public", "index": 1, "isCloned": true, "name": "destroyed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "objectName", "type": "QString"}], "index": 2, "name": "objectNameChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 3, "name": "deleteLater", "returnType": "void"}]}], "inputFile": "qobject.h", "outputRevision": 69}]