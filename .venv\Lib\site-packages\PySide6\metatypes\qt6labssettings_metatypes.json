[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "Settings"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QQmlSettings", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 29, "methods": [{"access": "public", "arguments": [{"name": "key", "type": "QString"}, {"name": "defaultValue", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 1, "isConst": true, "name": "value", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "key", "type": "QString"}], "index": 2, "isCloned": true, "isConst": true, "name": "value", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "key", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 3, "name": "setValue", "returnType": "void"}, {"access": "public", "index": 4, "name": "sync", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "category", "read": "category", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCategory"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "fileName", "read": "fileName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setFileName"}], "qualifiedClassName": "QQmlSettings", "slots": [{"access": "private", "index": 0, "name": "_q_propertyChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qqmlsettings_p.h", "outputRevision": 69}]