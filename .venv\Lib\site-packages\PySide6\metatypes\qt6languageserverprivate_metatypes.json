[{"classes": [{"className": "QLspSpecification", "enums": [{"isClass": true, "isFlag": false, "name": "TraceValue", "values": ["Off", "Messages", "Verbose"]}, {"isClass": true, "isFlag": false, "name": "ErrorCodes", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "InvalidRequest", "MethodNotFound", "InvalidParams", "InternalError", "jsonrpcReservedErrorRangeStart", "serverErrorStart", "ServerNotInitialized", "UnknownErrorCode", "jsonrpcReservedErrorRangeEnd", "serverErrorEnd", "lspReservedErrorRangeStart", "ContentModified", "RequestCancelled", "lspReservedErrorRangeEnd"]}, {"isClass": true, "isFlag": false, "name": "DiagnosticSeverity", "values": ["Error", "Warning", "Information", "Hint"]}, {"isClass": true, "isFlag": false, "name": "DiagnosticTag", "values": ["Unnecessary", "Deprecated"]}, {"isClass": true, "isFlag": false, "name": "ResourceOperationKind", "values": ["Create", "<PERSON><PERSON>", "Delete"]}, {"isClass": true, "isFlag": false, "name": "FailureHandlingKind", "values": ["Abort", "Transactional", "TextOnlyTransactional", "Undo"]}, {"isClass": true, "isFlag": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "values": ["PlainText", "<PERSON><PERSON>"]}, {"isClass": true, "isFlag": false, "name": "InitializeErrorCode", "values": ["UnknownProtocolVersion"]}, {"isClass": true, "isFlag": false, "name": "MessageType", "values": ["Error", "Warning", "Info", "Log"]}, {"isClass": true, "isFlag": false, "name": "WatchKind", "values": ["Create", "Change", "Delete"]}, {"isClass": true, "isFlag": false, "name": "FileChangeType", "values": ["Created", "Changed", "Deleted"]}, {"isClass": true, "isFlag": false, "name": "FileOperationPatternKind", "values": ["File", "Folder"]}, {"isClass": true, "isFlag": false, "name": "TextDocumentSyncKind", "values": ["None", "Full", "Incremental"]}, {"isClass": true, "isFlag": false, "name": "TextDocumentSaveReason", "values": ["Manual", "AfterDelay", "FocusOut"]}, {"isClass": true, "isFlag": false, "name": "CompletionTriggerKind", "values": ["Invoked", "TriggerCharacter", "TriggerForIncompleteCompletions"]}, {"isClass": true, "isFlag": false, "name": "InsertTextFormat", "values": ["PlainText", "Snippet"]}, {"isClass": true, "isFlag": false, "name": "CompletionItemTag", "values": ["Deprecated"]}, {"isClass": true, "isFlag": false, "name": "InsertTextMode", "values": ["AsIs", "AdjustIndentation"]}, {"isClass": true, "isFlag": false, "name": "CompletionItemKind", "values": ["Text", "Method", "Function", "<PERSON><PERSON><PERSON><PERSON>", "Field", "Variable", "Class", "Interface", "<PERSON><PERSON><PERSON>", "Property", "Unit", "Value", "Enum", "Keyword", "Snippet", "Color", "File", "Reference", "Folder", "EnumMember", "Constant", "Struct", "Event", "Operator", "TypeParameter"]}, {"isClass": true, "isFlag": false, "name": "SignatureHelpTriggerKind", "values": ["Invoked", "TriggerCharacter", "ContentChange"]}, {"isClass": true, "isFlag": false, "name": "DocumentHighlightKind", "values": ["Text", "Read", "Write"]}, {"isClass": true, "isFlag": false, "name": "SymbolKind", "values": ["File", "<PERSON><PERSON><PERSON>", "Namespace", "Package", "Class", "Method", "Property", "Field", "<PERSON><PERSON><PERSON><PERSON>", "Enum", "Interface", "Function", "Variable", "Constant", "String", "Number", "Boolean", "Array", "Object", "Key", "<PERSON><PERSON>", "EnumMember", "Struct", "Event", "Operator", "TypeParameter"]}, {"isClass": true, "isFlag": false, "name": "SymbolTag", "values": ["Deprecated"]}, {"isClass": true, "isFlag": false, "name": "CodeActionKind", "values": ["Empty", "QuickFix", "Refa<PERSON>", "RefactorExtract", "RefactorInline", "RefactorRewrite", "Source", "SourceOrganizeImports"]}, {"isClass": true, "isFlag": false, "name": "PrepareSupportDefaultBehavior", "values": ["Identifier"]}, {"isClass": true, "isFlag": false, "name": "FoldingRangeKind", "values": ["Comment", "Imports", "Region"]}, {"isClass": true, "isFlag": false, "name": "SemanticTokenTypes", "values": ["Namespace", "Type", "Class", "Enum", "Interface", "Struct", "TypeParameter", "Parameter", "Variable", "Property", "EnumMember", "Event", "Function", "Method", "Macro", "Keyword", "Modifier", "Comment", "String", "Number", "Regexp", "Operator"]}, {"isClass": true, "isFlag": false, "name": "SemanticTokenModifiers", "values": ["Declaration", "Definition", "<PERSON><PERSON><PERSON>", "Static", "Deprecated", "Abstract", "Async", "Modification", "Documentation", "DefaultLibrary"]}, {"isClass": true, "isFlag": false, "name": "TokenFormat", "values": ["Relative"]}, {"isClass": true, "isFlag": false, "name": "UniquenessLevel", "values": ["Document", "Project", "Group", "Scheme", "Global"]}, {"isClass": true, "isFlag": false, "name": "Monike<PERSON><PERSON><PERSON>", "values": ["Import", "Export", "Local"]}], "lineNumber": 33, "namespace": true, "qualifiedClassName": "QLspSpecification"}], "inputFile": "qlanguageserverspectypes_p.h", "outputRevision": 69}, {"classes": [{"className": "QLspNotifySignals", "lineNumber": 24, "object": true, "qualifiedClassName": "QLspNotifySignals", "signals": [{"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::CancelParamsType"}], "index": 0, "name": "receivedCancelNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::InitializedParamsType"}], "index": 1, "name": "receivedInitializedNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::ExitParamsType"}], "index": 2, "name": "receivedExitNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::LogTraceParamsType"}], "index": 3, "name": "receivedLogTraceNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::SetTraceParamsType"}], "index": 4, "name": "receivedSetTraceNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::ShowMessageParamsType"}], "index": 5, "name": "receivedShowMessageNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::LogMessageParamsType"}], "index": 6, "name": "receivedLogMessageNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::WorkDoneProgressCancelParamsType"}], "index": 7, "name": "receivedWorkDoneProgressCancelNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::TelemetryEventParamsType"}], "index": 8, "name": "receivedTelemetryEventNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::DidChangeWorkspaceFoldersParamsType"}], "index": 9, "name": "receivedDidChangeWorkspaceFoldersNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::DidChangeConfigurationParamsType"}], "index": 10, "name": "receivedDidChangeConfigurationNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::DidChangeWatchedFilesParamsType"}], "index": 11, "name": "receivedDidChangeWatchedFilesNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::CreateFilesParamsType"}], "index": 12, "name": "receivedCreateFilesNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::RenameFilesParamsType"}], "index": 13, "name": "receivedRenameFilesNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::DeleteFilesParamsType"}], "index": 14, "name": "receivedDeleteFilesNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::DidOpenTextDocumentParamsType"}], "index": 15, "name": "receivedDidOpenTextDocumentNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::DidChangeTextDocumentParamsType"}], "index": 16, "name": "receivedDidChangeTextDocumentNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::WillSaveTextDocumentParamsType"}], "index": 17, "name": "receivedWillSaveTextDocumentNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::DidSaveTextDocumentParamsType"}], "index": 18, "name": "receivedDidSaveTextDocumentNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::DidCloseTextDocumentParamsType"}], "index": 19, "name": "receivedDidCloseTextDocumentNotification", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QLspSpecification::Notifications::PublishDiagnosticsParamsType"}], "index": 20, "name": "receivedPublishDiagnosticsNotification", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlspnotifysignals_p.h", "outputRevision": 69}]