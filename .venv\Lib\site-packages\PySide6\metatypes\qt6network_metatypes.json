[{"classes": [{"className": "QNetworkConnectionMonitor", "lineNumber": 27, "object": true, "qualifiedClassName": "QNetworkConnectionMonitor", "signals": [{"access": "public", "arguments": [{"name": "isOnline", "type": "bool"}], "index": 0, "name": "reachabilityChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnetconmonitor_p.h", "outputRevision": 69}, {"classes": [{"className": "QHttpHeaders", "enums": [{"isClass": true, "isFlag": false, "name": "WellKnownHeader", "values": ["AIM", "Accept", "AcceptAdditions", "AcceptCH", "AcceptDatetime", "AcceptEncoding", "AcceptFeatures", "AcceptLanguage", "AcceptPatch", "AcceptPost", "AcceptRanges", "AcceptSignature", "AccessControlAllowCredentials", "AccessControlAllowHeaders", "AccessControlAllowMethods", "AccessControlAllowOrigin", "AccessControlExposeHeaders", "AccessControlMaxAge", "AccessControlRequestHeaders", "AccessControlRequestMethod", "Age", "Allow", "ALPN", "AltSvc", "AltUsed", "Alternates", "ApplyToRedirectRef", "AuthenticationControl", "AuthenticationInfo", "Authorization", "CacheControl", "<PERSON>ache<PERSON><PERSON><PERSON>", "CalManagedID", "CalDAVTimezones", "CapsuleProtocol", "CDNCacheControl", "CDNLoop", "CertNotAfter", "CertNotBefore", "ClearSiteData", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Close", "Connection", "ContentDigest", "ContentDisposition", "ContentEncoding", "ContentID", "ContentLanguage", "ContentLength", "ContentLocation", "ContentRange", "ContentSecurityPolicy", "ContentSecurityPolicyReportOnly", "ContentType", "<PERSON><PERSON>", "CrossOriginEmbedderPolicy", "CrossOriginEmbedderPolicyReportOnly", "CrossOriginOpenerPolicy", "CrossOriginOpenerPolicyReportOnly", "CrossOriginResourcePolicy", "DASL", "Date", "DAV", "DeltaBase", "De<PERSON><PERSON>", "Destination", "DifferentialID", "DPoP", "DPoPNonce", "EarlyData", "ETag", "Expect", "ExpectCT", "Expires", "Forwarded", "From", "<PERSON><PERSON><PERSON>", "Host", "If", "IfMatch", "IfModifiedSince", "IfNoneMatch", "IfRange", "IfScheduleTagMatch", "IfUnmodifiedSince", "IM", "IncludeReferredTokenBindingID", "KeepAlive", "Label", "LastEventID", "LastModified", "Link", "Location", "LockToken", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MementoDatetime", "<PERSON>er", "MIMEVersion", "Negotiate", "NEL", "ODataEntityId", "ODataIsolation", "ODataMaxVersion", "ODataVersion", "OptionalWWWAuthenticate", "OrderingType", "Origin", "OriginAgentCluster", "OSCORE", "OSLCCoreVersion", "Overwrite", "PingFrom", "PingTo", "Position", "Prefer", "PreferenceApplied", "Priority", "ProxyAuthenticate", "ProxyAuthenticationInfo", "ProxyAuthorization", "ProxyStatus", "PublicKeyPins", "PublicKeyPinsReportOnly", "Range", "RedirectRef", "<PERSON><PERSON><PERSON>", "Refresh", "ReplayNonce", "ReprDigest", "RetryAfter", "ScheduleReply", "ScheduleTag", "SecPurpose", "SecTokenBinding", "SecWebSocketAccept", "SecWebSocketExtensions", "SecWebSocketKey", "SecWebSocketProtocol", "SecWebSocketVersion", "Server", "ServerTiming", "<PERSON><PERSON><PERSON><PERSON>", "Signature", "SignatureInput", "SLUG", "SoapAction", "StatusURI", "StrictTransportSecurity", "Sunset", "SurrogateCapability", "SurrogateControl", "TCN", "TE", "Timeout", "Topic", "Traceparent", "Tracestate", "Trailer", "TransferEncoding", "TTL", "Upgrade", "Urgency", "UserAgent", "VariantVary", "Vary", "Via", "WantContentDigest", "WantReprDigest", "WWWAuthenticate", "XContentTypeOptions", "XFrameOptions", "AcceptCharset", "CPEPInfo", "Pragma", "ProtocolInfo", "ProtocolQuery"]}], "gadget": true, "lineNumber": 19, "qualifiedClassName": "QHttpHeaders"}], "inputFile": "qhttpheaders.h", "outputRevision": 69}, {"classes": [{"className": "QSslPreSharedKeyAuthenticator", "gadget": true, "lineNumber": 17, "qualifiedClassName": "QSslPreSharedKeyAuthenticator"}], "inputFile": "qsslpresharedkeyauthenticator.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractNetworkCache", "lineNumber": 79, "object": true, "qualifiedClassName": "QAbstractNetworkCache", "slots": [{"access": "public", "index": 0, "name": "clear", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractnetworkcache.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractSocket", "enums": [{"isClass": false, "isFlag": false, "name": "SocketType", "values": ["TcpSocket", "UdpSocket", "SctpSocket", "UnknownSocketType"]}, {"isClass": false, "isFlag": false, "name": "NetworkLayerProtocol", "values": ["IPv4Protocol", "IPv6Protocol", "AnyIPProtocol", "UnknownNetworkLayerProtocol"]}, {"isClass": false, "isFlag": false, "name": "SocketError", "values": ["ConnectionRefusedError", "RemoteHostClosedError", "HostNotFoundError", "SocketAccessError", "SocketResourceError", "SocketTimeoutError", "DatagramTooLargeError", "NetworkError", "AddressInUseError", "SocketAddressNotAvailableError", "UnsupportedSocketOperationError", "UnfinishedSocketOperationError", "ProxyAuthenticationRequiredError", "SslHandshakeFailedError", "ProxyConnectionRefusedError", "ProxyConnectionClosedError", "ProxyConnectionTimeoutError", "ProxyNotFoundError", "ProxyProtocolError", "OperationError", "SslInternalError", "SslInvalidUserDataError", "TemporaryError", "UnknownSocketError"]}, {"isClass": false, "isFlag": false, "name": "SocketState", "values": ["UnconnectedState", "HostLookupState", "ConnectingState", "ConnectedState", "BoundState", "ListeningState", "ClosingState"]}, {"isClass": false, "isFlag": false, "name": "SocketOption", "values": ["LowDelayOption", "KeepAliveOption", "MulticastTtlOption", "MulticastLoopbackOption", "TypeOfServiceOption", "SendBufferSizeSocketOption", "ReceiveBufferSizeSocketOption", "PathMtuSocketOption"]}], "lineNumber": 28, "object": true, "qualifiedClassName": "QAbstractSocket", "signals": [{"access": "public", "index": 0, "name": "hostFound", "returnType": "void"}, {"access": "public", "index": 1, "name": "connected", "returnType": "void"}, {"access": "public", "index": 2, "name": "disconnected", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QAbstractSocket::SocketState"}], "index": 3, "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QAbstractSocket::SocketError"}], "index": 4, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "proxy", "type": "QNetworkProxy"}, {"name": "authenticator", "type": "QAuthenticator*"}], "index": 5, "name": "proxyAuthenticationRequired", "returnType": "void"}], "slots": [{"access": "private", "index": 6, "name": "_q_connectToNextAddress", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QHostInfo"}], "index": 7, "name": "_q_startConnecting", "returnType": "void"}, {"access": "private", "index": 8, "name": "_q_abortConnectionAttempt", "returnType": "void"}, {"access": "private", "index": 9, "name": "_q_testConnection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qabstractsocket.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractSocketEngine", "lineNumber": 50, "object": true, "qualifiedClassName": "QAbstractSocketEngine", "slots": [{"access": "public", "index": 0, "name": "readNotification", "returnType": "void"}, {"access": "public", "index": 1, "name": "writeNotification", "returnType": "void"}, {"access": "public", "index": 2, "name": "closeNotification", "returnType": "void"}, {"access": "public", "index": 3, "name": "exceptionNotification", "returnType": "void"}, {"access": "public", "index": 4, "name": "connectionNotification", "returnType": "void"}, {"access": "public", "arguments": [{"name": "proxy", "type": "QNetworkProxy"}, {"name": "authenticator", "type": "QAuthenticator*"}], "index": 5, "name": "proxyAuthenticationRequired", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractsocketengine_p.h", "outputRevision": 69}, {"classes": [{"className": "QAuthenticator", "gadget": true, "lineNumber": 17, "qualifiedClassName": "QAuthenticator"}], "inputFile": "qauthenticator.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "RegisterEnumClassesUnscoped", "value": "false"}], "className": "QDnsTlsAssociationRecord", "enums": [{"isClass": true, "isFlag": false, "name": "CertificateUsage", "type": "quint8", "values": ["CertificateAuthorityConstrait", "ServiceCertificateConstraint", "TrustAnchorAssertion", "DomainIssuedCertificate", "PrivateUse", "PKIX_TA", "PKIX_EE", "DANE_TA", "DANE_EE", "Priv<PERSON>ert"]}, {"isClass": true, "isFlag": false, "name": "Selector", "type": "quint8", "values": ["FullCertificate", "SubjectPublicKeyInfo", "PrivateUse", "Cert", "SPKI", "PrivSel"]}, {"isClass": true, "isFlag": false, "name": "MatchingType", "type": "quint8", "values": ["Exact", "Sha256", "Sha512", "PrivateUse", "PrivMatch"]}], "gadget": true, "lineNumber": 144, "qualifiedClassName": "QDnsTlsAssociationRecord"}, {"className": "QDnsLookup", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "ResolverError", "OperationCancelledError", "InvalidRequestError", "InvalidReplyError", "ServerFailureError", "ServerRefusedError", "NotFoundError", "TimeoutError"]}, {"isClass": false, "isFlag": false, "name": "Type", "values": ["A", "AAAA", "ANY", "CNAME", "MX", "NS", "PTR", "SRV", "TLSA", "TXT"]}, {"isClass": false, "isFlag": false, "name": "Protocol", "type": "quint8", "values": ["Standard", "DnsOverTls"]}], "lineNumber": 215, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "error", "notify": "finished", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "authenticData", "notify": "finished", "read": "isAuthenticData", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "errorString", "notify": "finished", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"bindable": "bindableName", "constant": false, "designable": true, "final": false, "index": 3, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"bindable": "bindableType", "constant": false, "designable": true, "final": false, "index": 4, "name": "type", "notify": "typeChanged", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "Type", "user": false, "write": "setType"}, {"bindable": "bindableNameserver", "constant": false, "designable": true, "final": false, "index": 5, "name": "nameserver", "notify": "nameserverChanged", "read": "nameserver", "required": false, "scriptable": true, "stored": true, "type": "QHostAddress", "user": false, "write": "setNameserver"}, {"bindable": "bindableNameserverPort", "constant": false, "designable": true, "final": false, "index": 6, "name": "nameserverPort", "notify": "nameserverPortChanged", "read": "nameserverPort", "required": false, "scriptable": true, "stored": true, "type": "quint16", "user": false, "write": "setNameserverPort"}, {"bindable": "bindableNameserverProtocol", "constant": false, "designable": true, "final": false, "index": 7, "name": "nameserverProtocol", "notify": "nameserverProtocolChanged", "read": "nameserverProtocol", "required": false, "scriptable": true, "stored": true, "type": "Protocol", "user": false, "write": "setNameserverProtocol"}], "qualifiedClassName": "QDnsLookup", "signals": [{"access": "public", "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 1, "name": "nameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "QDnsLookup::Type"}], "index": 2, "name": "typeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "nameserver", "type": "QHostAddress"}], "index": 3, "name": "nameserverChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "port", "type": "quint16"}], "index": 4, "name": "nameserverPortChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "protocol", "type": "QDnsLookup::Protocol"}], "index": 5, "name": "nameserverProtocolChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 6, "name": "abort", "returnType": "void"}, {"access": "public", "index": 7, "name": "lookup", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdnslookup.h", "outputRevision": 69}, {"classes": [{"className": "QDnsLookupRunnable", "lineNumber": 197, "object": true, "qualifiedClassName": "QDnsLookupRunnable", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QDnsLookupReply"}], "index": 0, "name": "finished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QRunnable"}]}], "inputFile": "qdnslookup_p.h", "outputRevision": 69}, {"classes": [{"className": "QDtlsClientVerifier", "lineNumber": 43, "object": true, "qualifiedClassName": "QDtlsClientVerifier", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QDtls", "lineNumber": 82, "object": true, "qualifiedClassName": "QDtls", "signals": [{"access": "public", "arguments": [{"name": "authenticator", "type": "QSslPreSharedKeyAuthenticator*"}], "index": 0, "name": "pskRequired", "returnType": "void"}, {"access": "public", "index": 1, "name": "handshakeTimeout", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdtls.h", "outputRevision": 69}, {"classes": [{"className": "QHostAddress", "gadget": true, "lineNumber": 39, "qualifiedClassName": "QHostAddress"}], "inputFile": "qhostaddress.h", "outputRevision": 69}, {"classes": [{"className": "QHostInfo", "gadget": true, "lineNumber": 18, "qualifiedClassName": "QHostInfo"}], "inputFile": "qhostinfo.h", "outputRevision": 69}, {"classes": [{"className": "QHostInfoResult", "lineNumber": 42, "object": true, "qualifiedClassName": "QHostInfoResult", "signals": [{"access": "public", "arguments": [{"name": "info", "type": "QHostInfo"}], "index": 0, "name": "resultsReady", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "info", "type": "QHostInfo"}], "index": 1, "name": "finalizePostResultsReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qhostinfo_p.h", "outputRevision": 69}, {"classes": [{"className": "QHttp2Stream", "enums": [{"isClass": true, "isFlag": false, "name": "State", "values": ["Idle", "ReservedRemote", "Open", "HalfClosedLocal", "HalfClosedRemote", "Closed"]}], "lineNumber": 93, "object": true, "qualifiedClassName": "QHttp2Stream", "signals": [{"access": "public", "arguments": [{"name": "headers", "type": "HPack::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "endStream", "type": "bool"}], "index": 0, "name": "headersReceived", "returnType": "void"}, {"access": "public", "index": 1, "name": "headersUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errorCode", "type": "Http2::Http2Error"}, {"name": "errorString", "type": "QString"}], "index": 2, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newState", "type": "QHttp2Stream::State"}], "index": 3, "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newStreamID", "type": "quint32"}], "index": 4, "name": "promisedStreamReceived", "returnType": "void"}, {"access": "public", "index": 5, "name": "uploadBlocked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QByteArray"}, {"name": "endStream", "type": "bool"}], "index": 6, "name": "dataReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errorCode", "type": "quint32"}], "index": 7, "name": "rstFrameRecived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bytes<PERSON>ritten", "type": "qint64"}], "index": 8, "name": "bytes<PERSON>ritten", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errorString", "type": "QString"}], "index": 9, "name": "uploadDeviceError", "returnType": "void"}, {"access": "public", "index": 10, "name": "uploadFinished", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "errorCode", "type": "Http2::Http2Error"}], "index": 11, "name": "sendRST_STREAM", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "headers", "type": "HPack::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "endStream", "type": "bool"}, {"name": "priority", "type": "quint8"}], "index": 12, "name": "sendHEADERS", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "headers", "type": "HPack::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "endStream", "type": "bool"}], "index": 13, "isCloned": true, "name": "sendHEADERS", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "payload", "type": "QByteArray"}, {"name": "endStream", "type": "bool"}], "index": 14, "name": "sendDATA", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "device", "type": "QIODevice*"}, {"name": "endStream", "type": "bool"}], "index": 15, "name": "sendDATA", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "device", "type": "QNonContiguousByteDevice*"}, {"name": "endStream", "type": "bool"}], "index": 16, "name": "sendDATA", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "delta", "type": "quint32"}], "index": 17, "name": "sendWINDOW_UPDATE", "returnType": "void"}, {"access": "private", "index": 18, "name": "maybeResumeUpload", "returnType": "void"}, {"access": "private", "index": 19, "name": "uploadDeviceReadChannelFinished", "returnType": "void"}, {"access": "private", "index": 20, "name": "uploadDeviceDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QHttp2Connection", "enums": [{"isClass": true, "isFlag": false, "name": "CreateStreamError", "values": ["MaxConcurrentStreamsReached", "StreamIdsExhausted", "ReceivedGOAWAY", "UnknownE<PERSON>r"]}], "lineNumber": 206, "object": true, "qualifiedClassName": "QHttp2Connection", "signals": [{"access": "public", "arguments": [{"name": "stream", "type": "QHttp2Stream*"}], "index": 0, "name": "newIncomingStream", "returnType": "void"}, {"access": "public", "arguments": [{"name": "stream", "type": "QHttp2Stream*"}], "index": 1, "name": "newPromisedStream", "returnType": "void"}, {"access": "public", "index": 2, "name": "errorReceived", "returnType": "void"}, {"access": "public", "index": 3, "name": "connectionClosed", "returnType": "void"}, {"access": "public", "index": 4, "name": "settingsFrameReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QHttp2Connection::PingState"}], "index": 5, "name": "pingFrameRecived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errorCode", "type": "Http2::Http2Error"}, {"name": "errorString", "type": "QString"}], "index": 6, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errorCode", "type": "Http2::Http2Error"}, {"name": "lastStreamID", "type": "quint32"}], "index": 7, "name": "receivedGOAWAY", "returnType": "void"}, {"access": "public", "arguments": [{"name": "streamID", "type": "quint32"}], "index": 8, "name": "receivedEND_STREAM", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "CreateStreamError"}], "index": 9, "name": "incomingStreamErrorOccured", "returnType": "void"}], "slots": [{"access": "public", "index": 10, "name": "sendPing", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "data", "type": "QByteArrayView"}], "index": 11, "name": "sendPing", "returnType": "bool"}, {"access": "public", "index": 12, "name": "handleReadyRead", "returnType": "void"}, {"access": "public", "index": 13, "name": "handleConnectionClosure", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qhttp2connection_p.h", "outputRevision": 69}, {"classes": [{"className": "QHttp2ProtocolHandler", "lineNumber": 48, "methods": [{"access": "public", "index": 2, "name": "handleConnectionClosure", "returnType": "void"}, {"access": "private", "index": 3, "name": "_q_receiveReply", "returnType": "void"}, {"access": "private", "index": 4, "name": "sendRequest", "returnType": "bool"}], "object": true, "qualifiedClassName": "QHttp2ProtocolHandler", "slots": [{"access": "private", "arguments": [{"name": "reply", "type": "QObject*"}], "index": 0, "name": "_q_reply<PERSON><PERSON>royed", "returnType": "void"}, {"access": "private", "arguments": [{"name": "uploadData", "type": "QObject*"}], "index": 1, "name": "_q_uploadDataDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QAbstractProtocolHandler"}]}], "inputFile": "qhttp2protocolhandler_p.h", "outputRevision": 69}, {"classes": [{"className": "QHttpMultiPart", "lineNumber": 58, "object": true, "qualifiedClassName": "QHttpMultiPart", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qhttpmultipart.h", "outputRevision": 69}, {"classes": [{"className": "QHttpNetworkConnection", "lineNumber": 57, "object": true, "qualifiedClassName": "QHttpNetworkConnection", "slots": [{"access": "public", "arguments": [{"name": "isOnline", "type": "bool"}], "index": 0, "name": "onlineStateChanged", "returnType": "void"}, {"access": "private", "index": 1, "name": "_q_startNextRequest", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QHostInfo"}], "index": 2, "name": "_q_hostLookupFinished", "returnType": "void"}, {"access": "private", "index": 3, "name": "_q_connectDelayedChannel", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qhttpnetworkconnection_p.h", "outputRevision": 69}, {"classes": [{"className": "QHttpNetworkConnectionChannel", "lineNumber": 65, "object": true, "qualifiedClassName": "QHttpNetworkConnectionChannel", "slots": [{"access": "protected", "index": 0, "name": "_q_receiveReply", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "bytes", "type": "qint64"}], "index": 1, "name": "_q_bytesWritten", "returnType": "void"}, {"access": "protected", "index": 2, "name": "_q_readyRead", "returnType": "void"}, {"access": "protected", "index": 3, "name": "_q_disconnected", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "socket", "type": "QAbstractSocket*"}], "index": 4, "name": "_q_connected_abstract_socket", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "socket", "type": "QLocalSocket*"}], "index": 5, "name": "_q_connected_local_socket", "returnType": "void"}, {"access": "protected", "index": 6, "name": "_q_connected", "returnType": "void"}, {"access": "protected", "arguments": [{"type": "QAbstractSocket::SocketError"}], "index": 7, "name": "_q_error", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "proxy", "type": "QNetworkProxy"}, {"name": "auth", "type": "QAuthenticator*"}], "index": 8, "name": "_q_proxyAuthenticationRequired", "returnType": "void"}, {"access": "protected", "index": 9, "name": "_q_uploadDataReadyRead", "returnType": "void"}, {"access": "protected", "index": 10, "name": "_q_encrypted", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "errors", "type": "QList<QSslError>"}], "index": 11, "name": "_q_sslErrors", "returnType": "void"}, {"access": "protected", "arguments": [{"type": "QSslPreSharedKeyAuthenticator*"}], "index": 12, "name": "_q_preSharedKeyAuthenticationRequired", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "bytes", "type": "qint64"}], "index": 13, "name": "_q_encryptedBytesW<PERSON>ten", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qhttpnetworkconnectionchannel_p.h", "outputRevision": 69}, {"classes": [{"className": "QHttpNetworkReply", "lineNumber": 57, "object": true, "qualifiedClassName": "QHttpNetworkReply", "signals": [{"access": "public", "index": 0, "name": "encrypted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errors", "type": "QList<QSslError>"}], "index": 1, "name": "sslErrors", "returnType": "void"}, {"access": "public", "arguments": [{"name": "authenticator", "type": "QSslPreSharedKeyAuthenticator*"}], "index": 2, "name": "preSharedKeyAuthenticationRequired", "returnType": "void"}, {"access": "public", "index": 3, "name": "socketStartedConnecting", "returnType": "void"}, {"access": "public", "index": 4, "name": "requestSent", "returnType": "void"}, {"access": "public", "index": 5, "name": "readyRead", "returnType": "void"}, {"access": "public", "index": 6, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errorCode", "type": "QNetworkReply::NetworkError"}, {"name": "detail", "type": "QString"}], "index": 7, "name": "finishedWithError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errorCode", "type": "QNetworkReply::NetworkError"}], "index": 8, "isCloned": true, "name": "finishedWithError", "returnType": "void"}, {"access": "public", "index": 9, "name": "headerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "done", "type": "qint64"}, {"name": "total", "type": "qint64"}], "index": 10, "name": "dataReadProgress", "returnType": "void"}, {"access": "public", "arguments": [{"name": "done", "type": "qint64"}, {"name": "total", "type": "qint64"}], "index": 11, "name": "dataSendProgress", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QHttpNetworkRequest"}, {"name": "authenticator", "type": "QAuthenticator*"}], "index": 12, "name": "cacheCredentials", "returnType": "void"}, {"access": "public", "arguments": [{"name": "proxy", "type": "QNetworkProxy"}, {"name": "authenticator", "type": "QAuthenticator*"}], "index": 13, "name": "proxyAuthenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QHttpNetworkRequest"}, {"name": "authenticator", "type": "QAuthenticator*"}], "index": 14, "name": "authenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "httpStatus", "type": "int"}, {"name": "maxRedirectsRemaining", "type": "int"}], "index": 15, "name": "redirected", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QHttpNetworkHeader"}]}], "inputFile": "qhttpnetworkreply_p.h", "outputRevision": 69}, {"classes": [{"className": "QHttpSocketEngine", "lineNumber": 36, "object": true, "qualifiedClassName": "QHttpSocketEngine", "slots": [{"access": "public", "index": 0, "name": "slotSocketConnected", "returnType": "void"}, {"access": "public", "index": 1, "name": "slotSocketDisconnected", "returnType": "void"}, {"access": "public", "index": 2, "name": "slotSocketReadNotification", "returnType": "void"}, {"access": "public", "index": 3, "name": "slotSocketBytesWritten", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QAbstractSocket::SocketError"}], "index": 4, "name": "slotSocketError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QAbstractSocket::SocketState"}], "index": 5, "name": "slotSocketStateChanged", "returnType": "void"}, {"access": "private", "index": 6, "name": "emitPendingReadNotification", "returnType": "void"}, {"access": "private", "index": 7, "name": "emitPendingWriteNotification", "returnType": "void"}, {"access": "private", "index": 8, "name": "emitPendingConnectionNotification", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSocketEngine"}]}], "inputFile": "qhttpsocketengine_p.h", "outputRevision": 69}, {"classes": [{"className": "QHttpThreadDelegate", "lineNumber": 48, "object": true, "qualifiedClassName": "QHttpThreadDelegate", "signals": [{"access": "public", "arguments": [{"name": "request", "type": "QHttpNetworkRequest"}, {"type": "QAuthenticator*"}], "index": 0, "name": "authenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QNetworkProxy"}, {"type": "QAuthenticator*"}], "index": 1, "name": "proxyAuthenticationRequired", "returnType": "void"}, {"access": "public", "index": 2, "name": "encrypted", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QList<QSslError>"}, {"type": "bool*"}, {"type": "QList<QSslError>*"}], "index": 3, "name": "sslErrors", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QSslConfiguration"}], "index": 4, "name": "sslConfigurationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QSslPreSharedKeyAuthenticator*"}], "index": 5, "name": "preSharedKeyAuthenticationRequired", "returnType": "void"}, {"access": "public", "index": 6, "name": "socketStartedConnecting", "returnType": "void"}, {"access": "public", "index": 7, "name": "requestSent", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QHttpHeaders"}, {"type": "int"}, {"type": "QString"}, {"type": "bool"}, {"type": "QSharedPointer<char>"}, {"type": "qint64"}, {"type": "qint64"}, {"type": "bool"}, {"type": "bool"}], "index": 8, "name": "downloadMetaData", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qint64"}, {"type": "qint64"}], "index": 9, "name": "downloadProgress", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QByteArray"}], "index": 10, "name": "downloadData", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QNetworkReply::NetworkError"}, {"type": "QString"}], "index": 11, "name": "error", "returnType": "void"}, {"access": "public", "index": 12, "name": "downloadFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "httpStatus", "type": "int"}, {"name": "maxRedirectsRemainig", "type": "int"}], "index": 13, "name": "redirected", "returnType": "void"}], "slots": [{"access": "public", "index": 14, "name": "startRequest", "returnType": "void"}, {"access": "public", "index": 15, "name": "abortRequest", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "qint64"}], "index": 16, "name": "readBufferSizeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "qint64"}], "index": 17, "name": "readBufferFreed", "returnType": "void"}, {"access": "public", "index": 18, "name": "startRequestSynchronously", "returnType": "void"}, {"access": "protected", "index": 19, "name": "readyReadSlot", "returnType": "void"}, {"access": "protected", "index": 20, "name": "finishedSlot", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "errorCode", "type": "QNetworkReply::NetworkError"}, {"name": "detail", "type": "QString"}], "index": 21, "name": "finishedWithErrorSlot", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "errorCode", "type": "QNetworkReply::NetworkError"}], "index": 22, "isCloned": true, "name": "finishedWithErrorSlot", "returnType": "void"}, {"access": "protected", "index": 23, "name": "synchronousFinishedSlot", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "errorCode", "type": "QNetworkReply::NetworkError"}, {"name": "detail", "type": "QString"}], "index": 24, "name": "synchronousFinishedWithErrorSlot", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "errorCode", "type": "QNetworkReply::NetworkError"}], "index": 25, "isCloned": true, "name": "synchronousFinishedWithErrorSlot", "returnType": "void"}, {"access": "protected", "index": 26, "name": "headerChangedSlot", "returnType": "void"}, {"access": "protected", "index": 27, "name": "synchronousHeaderChangedSlot", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "done", "type": "qint64"}, {"name": "total", "type": "qint64"}], "index": 28, "name": "dataReadProgressSlot", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "request", "type": "QHttpNetworkRequest"}, {"name": "authenticator", "type": "QAuthenticator*"}], "index": 29, "name": "cacheCredentialsSlot", "returnType": "void"}, {"access": "protected", "index": 30, "name": "encryptedSlot", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "errors", "type": "QList<QSslError>"}], "index": 31, "name": "sslErrorsSlot", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "authenticator", "type": "QSslPreSharedKeyAuthenticator*"}], "index": 32, "name": "preSharedKeyAuthenticationRequiredSlot", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "request", "type": "QHttpNetworkRequest"}, {"type": "QAuthenticator*"}], "index": 33, "name": "synchronousAuthenticationRequiredSlot", "returnType": "void"}, {"access": "protected", "arguments": [{"type": "QNetworkProxy"}, {"type": "QAuthenticator*"}], "index": 34, "name": "synchronousProxyAuthenticationRequiredSlot", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QNonContiguousByteDeviceThreadForwardImpl", "lineNumber": 163, "object": true, "qualifiedClassName": "QNonContiguousByteDeviceThreadForwardImpl", "signals": [{"access": "public", "arguments": [{"type": "qint64"}], "index": 0, "name": "wantData", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pos", "type": "qint64"}, {"name": "amount", "type": "qint64"}], "index": 1, "name": "processedData", "returnType": "void"}, {"access": "public", "arguments": [{"name": "b", "type": "bool*"}], "index": 2, "name": "resetData", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "pos", "type": "qint64"}, {"name": "dataArray", "type": "QByteArray"}, {"name": "dataAtEnd", "type": "bool"}, {"name": "dataSize", "type": "qint64"}], "index": 3, "name": "haveDataSlot", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QNonContiguousByteDevice"}]}], "inputFile": "qhttpthreaddelegate_p.h", "outputRevision": 69}, {"classes": [{"className": "QLocalServer", "enums": [{"isClass": false, "isFlag": false, "name": "SocketOption", "values": ["NoOptions", "UserAccessOption", "GroupAccessOption", "OtherAccessOption", "WorldAccessOption", "AbstractNamespaceOption"]}, {"alias": "SocketOption", "isClass": false, "isFlag": true, "name": "SocketOptions", "values": ["NoOptions", "UserAccessOption", "GroupAccessOption", "OtherAccessOption", "WorldAccessOption", "AbstractNamespaceOption"]}], "lineNumber": 19, "object": true, "properties": [{"bindable": "bindableSocketOptions", "constant": false, "designable": true, "final": false, "index": 0, "name": "socketOptions", "read": "socketOptions", "required": false, "scriptable": true, "stored": true, "type": "SocketOptions", "user": false, "write": "setSocketOptions"}], "qualifiedClassName": "QLocalServer", "signals": [{"access": "public", "index": 0, "name": "newConnection", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "_q_onNewConnection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlocalserver.h", "outputRevision": 69}, {"classes": [{"className": "QLocalSocket", "enums": [{"alias": "SocketOption", "isClass": false, "isFlag": true, "name": "SocketOptions", "values": ["NoOptions", "AbstractNamespaceOption"]}], "lineNumber": 18, "object": true, "properties": [{"bindable": "bindableSocketOptions", "constant": false, "designable": true, "final": false, "index": 0, "name": "socketOptions", "read": "socketOptions", "required": false, "scriptable": true, "stored": true, "type": "SocketOptions", "user": false, "write": "setSocketOptions"}], "qualifiedClassName": "QLocalSocket", "signals": [{"access": "public", "index": 0, "name": "connected", "returnType": "void"}, {"access": "public", "index": 1, "name": "disconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socketError", "type": "QLocalSocket::LocalSocketError"}], "index": 2, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socketState", "type": "QLocalSocket::LocalSocketState"}], "index": 3, "name": "stateChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 4, "name": "_q_pipeClosed", "returnType": "void"}, {"access": "private", "arguments": [{"type": "<PERSON><PERSON>"}, {"type": "QString"}], "index": 5, "name": "_q_winError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qlocalsocket.h", "outputRevision": 69}, {"classes": [{"className": "QNativeSocketEngine", "lineNumber": 101, "object": true, "qualifiedClassName": "QNativeSocketEngine", "slots": [{"access": "public", "index": 0, "name": "connectionNotification", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSocketEngine"}]}], "inputFile": "qnativesocketengine_p.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkAccessBackend", "enums": [{"isClass": true, "isFlag": false, "name": "TargetType", "values": ["Networked", "Local"]}, {"isClass": true, "isFlag": false, "name": "SecurityFeature", "values": ["None", "TLS"]}, {"isClass": true, "isFlag": false, "name": "IOFeature", "values": ["None", "ZeroCopy", "NeedResetableUpload", "SupportsSynchronousMode"]}], "lineNumber": 36, "object": true, "qualifiedClassName": "QNetworkAccessBackend", "slots": [{"access": "public", "index": 0, "name": "readyRead", "returnType": "void"}, {"access": "protected", "index": 1, "name": "finished", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "code", "type": "QNetworkReply::NetworkError"}, {"name": "errorString", "type": "QString"}], "index": 2, "name": "error", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "proxy", "type": "QNetworkProxy"}, {"name": "auth", "type": "QAuthenticator*"}], "index": 3, "name": "proxyAuthenticationRequired", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "auth", "type": "QAuthenticator*"}], "index": 4, "name": "authenticationRequired", "returnType": "void"}, {"access": "protected", "index": 5, "name": "metaDataChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "destination", "type": "QUrl"}], "index": 6, "name": "redirectionRequested", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QNetworkAccessBackendFactory", "lineNumber": 143, "object": true, "qualifiedClassName": "QNetworkAccessBackendFactory", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnetworkaccessbackend_p.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkAccessCache", "lineNumber": 34, "object": true, "qualifiedClassName": "QNetworkAccessCache", "signals": [{"access": "public", "arguments": [{"type": "QNetworkAccessCache::CacheableObject*"}], "index": 0, "name": "entryReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnetworkaccesscache_p.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkAccessFileBackend", "lineNumber": 26, "object": true, "qualifiedClassName": "QNetworkAccessFileBackend", "slots": [{"access": "public", "index": 0, "name": "uploadReadyReadSlot", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QNetworkAccessBackend"}]}], "inputFile": "qnetworkaccessfilebackend_p.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkAccessManager", "lineNumber": 35, "object": true, "qualifiedClassName": "QNetworkAccessManager", "signals": [{"access": "public", "arguments": [{"name": "proxy", "type": "QNetworkProxy"}, {"name": "authenticator", "type": "QAuthenticator*"}], "index": 0, "name": "proxyAuthenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QNetworkReply*"}, {"name": "authenticator", "type": "QAuthenticator*"}], "index": 1, "name": "authenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QNetworkReply*"}], "index": 2, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QNetworkReply*"}], "index": 3, "name": "encrypted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QNetworkReply*"}, {"name": "errors", "type": "QList<QSslError>"}], "index": 4, "name": "sslErrors", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QNetworkReply*"}, {"name": "authenticator", "type": "QSslPreSharedKeyAuthenticator*"}], "index": 5, "name": "preSharedKeyAuthenticationRequired", "returnType": "void"}], "slots": [{"access": "protected", "index": 6, "isConst": true, "name": "supportedSchemesImplementation", "returnType": "QStringList"}, {"access": "private", "arguments": [{"type": "QList<QSslError>"}], "index": 7, "name": "_q_replySslErrors", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QSslPreSharedKeyAuthenticator*"}], "index": 8, "name": "_q_replyPreSharedKeyAuthenticationRequired", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnetworkaccessmanager.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkCookie", "enums": [{"isClass": true, "isFlag": false, "name": "SameSite", "values": ["<PERSON><PERSON><PERSON>", "None", "Lax", "Strict"]}], "gadget": true, "lineNumber": 22, "qualifiedClassName": "QNetworkCookie"}], "inputFile": "qnetworkcookie.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkCookieJar", "lineNumber": 17, "object": true, "qualifiedClassName": "QNetworkCookieJar", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnetworkcookiejar.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkDiskCache", "lineNumber": 15, "object": true, "qualifiedClassName": "QNetworkDiskCache", "slots": [{"access": "public", "index": 0, "name": "clear", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractNetworkCache"}]}], "inputFile": "qnetworkdiskcache.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkFile", "lineNumber": 24, "object": true, "qualifiedClassName": "QNetworkFile", "signals": [{"access": "public", "arguments": [{"name": "ok", "type": "bool"}], "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QHttpHeaders::WellKnownHeader"}, {"name": "value", "type": "QByteArray"}], "index": 1, "name": "headerRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QNetworkReply::NetworkError"}, {"name": "message", "type": "QString"}], "index": 2, "name": "networkError", "returnType": "void"}], "slots": [{"access": "public", "index": 3, "name": "open", "returnType": "void"}, {"access": "public", "index": 4, "name": "close", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QFile"}]}], "inputFile": "qnetworkfile_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "RegisterEnumClassesUnscoped", "value": "false"}], "className": "QNetworkInformation", "enums": [{"isClass": true, "isFlag": false, "name": "Reachability", "values": ["Unknown", "Disconnected", "Local", "Site", "Online"]}, {"isClass": true, "isFlag": false, "name": "TransportMedium", "values": ["Unknown", "Ethernet", "Cellular", "WiFi", "Bluetooth"]}, {"alias": "Feature", "isClass": true, "isFlag": true, "name": "Features", "values": ["Reachability", "CaptivePortal", "TransportMedium", "Metered"]}], "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "reachability", "notify": "reachabilityChanged", "read": "reachability", "required": false, "scriptable": true, "stored": true, "type": "Reachability", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "isBehindCaptivePortal", "notify": "isBehindCaptivePortalChanged", "read": "isBehindCaptivePortal", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "transportMedium", "notify": "transportMediumChanged", "read": "transportMedium", "required": false, "scriptable": true, "stored": true, "type": "TransportMedium", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "isMetered", "notify": "isMeteredChanged", "read": "isMetered", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QNetworkInformation", "signals": [{"access": "public", "arguments": [{"name": "newReachability", "type": "QNetworkInformation::Reachability"}], "index": 0, "name": "reachabilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "bool"}], "index": 1, "name": "isBehindCaptivePortalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "current", "type": "QNetworkInformation::TransportMedium"}], "index": 2, "name": "transportMediumChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isMetered", "type": "bool"}], "index": 3, "name": "isMeteredChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnetworkinformation.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkInformationBackend", "lineNumber": 27, "object": true, "qualifiedClassName": "QNetworkInformationBackend", "signals": [{"access": "public", "arguments": [{"name": "reachability", "type": "QNetworkInformation::Reachability"}], "index": 0, "name": "reachabilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "index": 1, "name": "behindCaptivePortalChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "medium", "type": "QNetworkInformation::TransportMedium"}], "index": 2, "name": "transportMediumChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isMetered", "type": "bool"}], "index": 3, "name": "isMeteredChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QNetworkInformationBackendFactory", "lineNumber": 135, "object": true, "qualifiedClassName": "QNetworkInformationBackendFactory", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qnetworkinformation_p.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkInterface", "enums": [{"alias": "InterfaceFlag", "isClass": false, "isFlag": true, "name": "InterfaceFlags", "values": ["IsUp", "IsRunning", "CanBroadcast", "IsLoopBack", "IsPointToPoint", "CanMulticast"]}, {"isClass": false, "isFlag": false, "name": "InterfaceType", "values": ["Loopback", "Virtual", "Ethernet", "Slip", "CanBus", "Ppp", "Fddi", "Wifi", "Ieee80211", "Phonet", "Ieee802154", "SixLoWPAN", "Ieee80216", "Ieee1394", "Unknown"]}], "gadget": true, "lineNumber": 72, "qualifiedClassName": "QNetworkInterface"}], "inputFile": "qnetworkinterface.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkProxyQuery", "enums": [{"isClass": false, "isFlag": false, "name": "QueryType", "values": ["TcpSocket", "UdpSocket", "SctpSocket", "TcpServer", "UrlRequest", "SctpServer"]}], "gadget": true, "lineNumber": 20, "qualifiedClassName": "QNetworkProxyQuery"}, {"className": "QNetworkProxy", "gadget": true, "lineNumber": 78, "qualifiedClassName": "QNetworkProxy"}], "inputFile": "qnetworkproxy.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkReply", "enums": [{"isClass": false, "isFlag": false, "name": "NetworkError", "values": ["NoError", "ConnectionRefusedError", "RemoteHostClosedError", "HostNotFoundError", "TimeoutError", "OperationCanceledError", "SslHandshakeFailedError", "TemporaryNetworkFailureError", "NetworkSessionFailedError", "BackgroundRequestNotAllowedError", "TooManyRedirectsError", "InsecureRedirectError", "UnknownNetworkError", "ProxyConnectionRefusedError", "ProxyConnectionClosedError", "ProxyNotFoundError", "ProxyTimeoutError", "ProxyAuthenticationRequiredError", "UnknownProxyError", "ContentAccessDenied", "ContentOperationNotPermittedError", "ContentNotFoundError", "AuthenticationRequiredError", "ContentReSendError", "ContentConflictError", "ContentGoneError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ProtocolUnknownError", "ProtocolInvalidOperationError", "ProtocolFailure", "InternalServerError", "OperationNotImplementedError", "ServiceUnavailableError", "UnknownServerError"]}], "lineNumber": 28, "object": true, "qualifiedClassName": "QNetworkReply", "signals": [{"access": "public", "index": 0, "name": "socketStartedConnecting", "returnType": "void"}, {"access": "public", "index": 1, "name": "requestSent", "returnType": "void"}, {"access": "public", "index": 2, "name": "metaDataChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QNetworkReply::NetworkError"}], "index": 4, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "index": 5, "name": "encrypted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errors", "type": "QList<QSslError>"}], "index": 6, "name": "sslErrors", "returnType": "void"}, {"access": "public", "arguments": [{"name": "authenticator", "type": "QSslPreSharedKeyAuthenticator*"}], "index": 7, "name": "preSharedKeyAuthenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 8, "name": "redirected", "returnType": "void"}, {"access": "public", "index": 9, "name": "redirectAllowed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bytesSent", "type": "qint64"}, {"name": "bytesTotal", "type": "qint64"}], "index": 10, "name": "uploadProgress", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bytesReceived", "type": "qint64"}, {"name": "bytesTotal", "type": "qint64"}], "index": 11, "name": "downloadProgress", "returnType": "void"}], "slots": [{"access": "public", "index": 12, "name": "abort", "returnType": "void"}, {"access": "public", "index": 13, "name": "ignoreSslErrors", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qnetworkreply.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkReplyDataImpl", "lineNumber": 28, "object": true, "qualifiedClassName": "QNetworkReplyDataImpl", "superClasses": [{"access": "public", "name": "QNetworkReply"}]}], "inputFile": "qnetworkreplydataimpl_p.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkReplyFileImpl", "lineNumber": 30, "object": true, "qualifiedClassName": "QNetworkReplyFileImpl", "slots": [{"access": "private", "arguments": [{"name": "isOpen", "type": "bool"}], "index": 0, "name": "fileOpenFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QNetworkReply"}]}], "inputFile": "qnetworkreplyfileimpl_p.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkReplyHttpImpl", "lineNumber": 51, "object": true, "qualifiedClassName": "QNetworkReplyHttpImpl", "signals": [{"access": "public", "index": 0, "name": "startHttpRequest", "returnType": "void"}, {"access": "public", "index": 1, "name": "abortHttpRequest", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "qint64"}], "index": 2, "name": "readBufferSizeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "qint64"}], "index": 3, "name": "readBufferFreed", "returnType": "void"}, {"access": "public", "index": 4, "name": "startHttpRequestSynchronously", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pos", "type": "qint64"}, {"name": "dataArray", "type": "QByteArray"}, {"name": "dataAtEnd", "type": "bool"}, {"name": "dataSize", "type": "qint64"}], "index": 5, "name": "haveUploadData", "returnType": "void"}], "slots": [{"access": "public", "index": 6, "name": "_q_startOperation", "returnType": "void"}, {"access": "public", "index": 7, "name": "_q_cacheLoadReadyRead", "returnType": "void"}, {"access": "public", "index": 8, "name": "_q_bufferOutgoingData", "returnType": "void"}, {"access": "public", "index": 9, "name": "_q_bufferOutgoingDataFinished", "returnType": "void"}, {"access": "public", "index": 10, "name": "_q_transferTimedOut", "returnType": "void"}, {"access": "public", "index": 11, "name": "_q_finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QNetworkReply::NetworkError"}, {"type": "QString"}], "index": 12, "name": "_q_error", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QByteArray"}], "index": 13, "name": "replyDownloadData", "returnType": "void"}, {"access": "public", "index": 14, "name": "replyFinished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qint64"}, {"type": "qint64"}], "index": 15, "name": "replyDownloadProgressSlot", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QHttpNetworkRequest"}, {"type": "QAuthenticator*"}], "index": 16, "name": "httpAuthenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QNetworkReply::NetworkError"}, {"type": "QString"}], "index": 17, "name": "httpError", "returnType": "void"}, {"access": "public", "index": 18, "name": "replyEncrypted", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QList<QSslError>"}, {"type": "bool*"}, {"type": "QList<QSslError>*"}], "index": 19, "name": "replySslErrors", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QSslConfiguration"}], "index": 20, "name": "replySslConfigurationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QSslPreSharedKeyAuthenticator*"}], "index": 21, "name": "replyPreSharedKeyAuthenticationRequiredSlot", "returnType": "void"}, {"access": "public", "arguments": [{"name": "proxy", "type": "QNetworkProxy"}, {"name": "auth", "type": "QAuthenticator*"}], "index": 22, "name": "proxyAuthenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "r", "type": "bool*"}], "index": 23, "name": "resetUploadDataSlot", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qint64"}], "index": 24, "name": "wantUploadDataSlot", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qint64"}, {"type": "qint64"}], "index": 25, "name": "sentUploadDataSlot", "returnType": "void"}, {"access": "public", "index": 26, "name": "uploadByteDeviceReadyReadSlot", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qint64"}, {"type": "qint64"}], "index": 27, "name": "emitReplyUploadProgress", "returnType": "void"}, {"access": "public", "index": 28, "name": "_q_cacheSaveDeviceAboutToClose", "returnType": "void"}, {"access": "public", "index": 29, "name": "_q_metaDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QUrl"}, {"type": "int"}, {"type": "int"}], "index": 30, "name": "onRedirected", "returnType": "void"}, {"access": "public", "index": 31, "name": "followRedirect", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QNetworkReply"}]}], "inputFile": "qnetworkreplyhttpimpl_p.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkReplyImpl", "lineNumber": 38, "object": true, "qualifiedClassName": "QNetworkReplyImpl", "slots": [{"access": "public", "index": 0, "name": "_q_startOperation", "returnType": "void"}, {"access": "public", "index": 1, "name": "_q_copyReadyRead", "returnType": "void"}, {"access": "public", "index": 2, "name": "_q_copyReadChannelFinished", "returnType": "void"}, {"access": "public", "index": 3, "name": "_q_bufferOutgoingData", "returnType": "void"}, {"access": "public", "index": 4, "name": "_q_bufferOutgoingDataFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QNetworkReply"}]}], "inputFile": "qnetworkreplyimpl_p.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkRequest", "enums": [{"isClass": false, "isFlag": false, "name": "KnownHeaders", "values": ["ContentTypeHeader", "ContentLengthHeader", "LocationHeader", "LastModifiedHeader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentDispositionHeader", "UserAgentHeader", "ServerHeader", "IfModifiedSinceHeader", "ETagHeader", "IfMatchHeader", "IfNoneMatchHeader", "NumKnownHeaders"]}], "gadget": true, "lineNumber": 25, "qualifiedClassName": "QNetworkRequest"}], "inputFile": "qnetworkrequest.h", "outputRevision": 69}, {"classes": [{"className": "QRestAccessManager", "lineNumber": 77, "object": true, "qualifiedClassName": "QRestAccessManager", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qrestaccessmanager.h", "outputRevision": 69}, {"classes": [{"className": "QSocks5SocketEngine", "lineNumber": 30, "object": true, "qualifiedClassName": "QSocks5SocketEngine", "slots": [{"access": "private", "index": 0, "name": "_q_controlSocketConnected", "returnType": "void"}, {"access": "private", "index": 1, "name": "_q_controlSocketReadNotification", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QAbstractSocket::SocketError"}], "index": 2, "name": "_q_controlSocketErrorOccurred", "returnType": "void"}, {"access": "private", "index": 3, "name": "_q_udpSocketReadNotification", "returnType": "void"}, {"access": "private", "index": 4, "name": "_q_controlSocketBytesWritten", "returnType": "void"}, {"access": "private", "index": 5, "name": "_q_emitPendingReadNotification", "returnType": "void"}, {"access": "private", "index": 6, "name": "_q_emitPendingWriteNotification", "returnType": "void"}, {"access": "private", "index": 7, "name": "_q_emitPendingConnectionNotification", "returnType": "void"}, {"access": "private", "index": 8, "name": "_q_controlSocketDisconnected", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QAbstractSocket::SocketState"}], "index": 9, "name": "_q_controlSocketStateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractSocketEngine"}]}], "inputFile": "qsocks5socketengine_p.h", "outputRevision": 69}, {"classes": [{"className": "QSsl", "enums": [{"isClass": false, "isFlag": false, "name": "KeyType", "values": ["Private<PERSON><PERSON>", "PublicKey"]}, {"isClass": false, "isFlag": false, "name": "EncodingFormat", "values": ["Pem", "Der"]}, {"isClass": false, "isFlag": false, "name": "KeyAlgorithm", "values": ["Opaque", "Rsa", "Dsa", "Ec", "Dh"]}, {"isClass": false, "isFlag": false, "name": "AlternativeNameEntryType", "values": ["EmailEntry", "DnsEntry", "IpAddressEntry"]}, {"isClass": false, "isFlag": false, "name": "SslProtocol", "values": ["TlsV1_0", "TlsV1_1", "TlsV1_2", "AnyProtocol", "SecureProtocols", "TlsV1_0OrLater", "TlsV1_1OrLater", "TlsV1_2OrLater", "DtlsV1_0", "DtlsV1_0OrLater", "DtlsV1_2", "DtlsV1_2OrLater", "TlsV1_3", "TlsV1_3OrLater", "UnknownProtocol"]}, {"isClass": false, "isFlag": false, "name": "SslOption", "values": ["SslOptionDisableEmptyFragments", "SslOptionDisableSessionTickets", "SslOptionDisableCompression", "SslOptionDisableServerNameIndication", "SslOptionDisableLegacyRenegotiation", "SslOptionDisableSessionSharing", "SslOptionDisableSessionPersistence", "SslOptionDisableServerCipherPreference"]}, {"isClass": true, "isFlag": false, "name": "AlertLevel", "values": ["Warning", "Fatal", "Unknown"]}, {"isClass": true, "isFlag": false, "name": "AlertType", "values": ["CloseNotify", "UnexpectedMessage", "BadRecordMac", "RecordOverflow", "DecompressionFailure", "HandshakeFailure", "NoCertificate", "BadCertificate", "UnsupportedCertificate", "CertificateRevoked", "CertificateExpired", "CertificateUnknown", "IllegalParameter", "UnknownCa", "AccessDenied", "DecodeError", "DecryptError", "ExportRestriction", "ProtocolVersion", "InsufficientSecurity", "InternalError", "InappropriateFallback", "UserCancelled", "NoRenegotiation", "MissingExtension", "UnsupportedExtension", "CertificateUnobtainable", "UnrecognizedName", "BadCertificateStatusResponse", "BadCertificateHashValue", "UnknownPskIdentity", "CertificateRequired", "NoApplicationProtocol", "UnknownAlertMessage"]}, {"isClass": true, "isFlag": false, "name": "ImplementedClass", "values": ["Key", "Certificate", "Socket", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EllipticCurve", "Dtls", "DtlsCookie"]}, {"isClass": true, "isFlag": false, "name": "SupportedFeature", "values": ["CertificateVerification", "ClientSideAlpn", "ServerSideAlpn", "Ocsp", "Psk", "SessionTicket", "<PERSON><PERSON><PERSON>"]}], "lineNumber": 19, "namespace": true, "qualifiedClassName": "QSsl"}], "inputFile": "qssl.h", "outputRevision": 69}, {"classes": [{"className": "QSslError", "enums": [{"isClass": false, "isFlag": false, "name": "SslError", "values": ["NoError", "UnableToGetIssuerCertificate", "UnableToDecryptCertificateSignature", "UnableToDecodeIssuerPublicKey", "CertificateSignatureFailed", "CertificateNotYetValid", "CertificateExpired", "InvalidNotBeforeField", "InvalidNotAfterField", "SelfSignedCertificate", "SelfSignedCertificateInChain", "UnableToGetLocalIssuerCertificate", "UnableToVerifyFirstCertificate", "CertificateRevoked", "InvalidCaCertificate", "PathLengthExceeded", "InvalidPurpose", "CertificateUntrusted", "CertificateRejected", "SubjectIssuerMismatch", "AuthorityIssuerSerialNumberMismatch", "NoPeerCertificate", "HostNameMismatch", "NoSslSupport", "CertificateBlacklisted", "CertificateStatusUnknown", "OcspNoResponseFound", "OcspMalformedRequest", "OcspMalformedResponse", "OcspInternalError", "OcspTryLater", "OcspSigRequred", "OcspUnauthorized", "OcspResponseCannotBeTrusted", "OcspResponseCertIdUnknown", "OcspResponseExpired", "OcspStatusUnknown", "UnspecifiedError"]}], "gadget": true, "lineNumber": 25, "qualifiedClassName": "QSslError"}], "inputFile": "qsslerror.h", "outputRevision": 69}, {"classes": [{"className": "QSslServer", "lineNumber": 24, "object": true, "qualifiedClassName": "QSslServer", "signals": [{"access": "public", "arguments": [{"name": "socket", "type": "QSslSocket*"}, {"name": "errors", "type": "QList<QSslError>"}], "index": 0, "name": "sslErrors", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socket", "type": "QSslSocket*"}, {"name": "error", "type": "QSslError"}], "index": 1, "name": "peerVerifyError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socket", "type": "QSslSocket*"}, {"name": "error", "type": "QAbstractSocket::SocketError"}], "index": 2, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socket", "type": "QSslSocket*"}, {"name": "authenticator", "type": "QSslPreSharedKeyAuthenticator*"}], "index": 3, "name": "preSharedKeyAuthenticationRequired", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socket", "type": "QSslSocket*"}, {"name": "level", "type": "QSsl::<PERSON>ertLevel"}, {"name": "type", "type": "QSsl::AlertType"}, {"name": "description", "type": "QString"}], "index": 4, "name": "alertSent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socket", "type": "QSslSocket*"}, {"name": "level", "type": "QSsl::<PERSON>ertLevel"}, {"name": "type", "type": "QSsl::AlertType"}, {"name": "description", "type": "QString"}], "index": 5, "name": "alertReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socket", "type": "QSslSocket*"}, {"name": "error", "type": "QSslError"}], "index": 6, "name": "handshakeInterruptedOnError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socket", "type": "QSslSocket*"}], "index": 7, "name": "startedEncryptionHandshake", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QTcpServer"}]}], "inputFile": "qsslserver.h", "outputRevision": 69}, {"classes": [{"className": "QSslSocket", "enums": [{"isClass": false, "isFlag": false, "name": "SslMode", "values": ["UnencryptedMode", "SslClientMode", "SslServerMode"]}, {"isClass": false, "isFlag": false, "name": "PeerVerifyMode", "values": ["VerifyNone", "QueryPeer", "VerifyPeer", "AutoVerifyPeer"]}], "lineNumber": 28, "object": true, "qualifiedClassName": "QSslSocket", "signals": [{"access": "public", "index": 0, "name": "encrypted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QSslError"}], "index": 1, "name": "peerVerifyError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errors", "type": "QList<QSslError>"}], "index": 2, "name": "sslErrors", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newMode", "type": "QSslSocket::SslMode"}], "index": 3, "name": "modeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "totalBytes", "type": "qint64"}], "index": 4, "name": "encryptedBytesWritten", "returnType": "void"}, {"access": "public", "arguments": [{"name": "authenticator", "type": "QSslPreSharedKeyAuthenticator*"}], "index": 5, "name": "preSharedKeyAuthenticationRequired", "returnType": "void"}, {"access": "public", "index": 6, "name": "newSessionTicketReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "QSsl::<PERSON>ertLevel"}, {"name": "type", "type": "QSsl::AlertType"}, {"name": "description", "type": "QString"}], "index": 7, "name": "alertSent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "QSsl::<PERSON>ertLevel"}, {"name": "type", "type": "QSsl::AlertType"}, {"name": "description", "type": "QString"}], "index": 8, "name": "alertReceived", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QSslError"}], "index": 9, "name": "handshakeInterruptedOnError", "returnType": "void"}], "slots": [{"access": "public", "index": 10, "name": "startClientEncryption", "returnType": "void"}, {"access": "public", "index": 11, "name": "startServerEncryption", "returnType": "void"}, {"access": "public", "index": 12, "name": "ignoreSslErrors", "returnType": "void"}, {"access": "private", "index": 13, "name": "_q_connectedSlot", "returnType": "void"}, {"access": "private", "index": 14, "name": "_q_hostFoundSlot", "returnType": "void"}, {"access": "private", "index": 15, "name": "_q_disconnectedSlot", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QAbstractSocket::SocketState"}], "index": 16, "name": "_q_stateChangedSlot", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QAbstractSocket::SocketError"}], "index": 17, "name": "_q_errorSlot", "returnType": "void"}, {"access": "private", "index": 18, "name": "_q_readyReadSlot", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}], "index": 19, "name": "_q_channelReadyReadSlot", "returnType": "void"}, {"access": "private", "arguments": [{"type": "qint64"}], "index": 20, "name": "_q_bytesWrittenSlot", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}, {"type": "qint64"}], "index": 21, "name": "_q_channelBytesWrittenSlot", "returnType": "void"}, {"access": "private", "index": 22, "name": "_q_readChannelFinishedSlot", "returnType": "void"}, {"access": "private", "index": 23, "name": "_q_flushWriteBuffer", "returnType": "void"}, {"access": "private", "index": 24, "name": "_q_flushReadBuffer", "returnType": "void"}, {"access": "private", "index": 25, "name": "_q_resumeImplementation", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QTcpSocket"}]}], "inputFile": "qsslsocket.h", "outputRevision": 69}, {"classes": [{"className": "QTcpServer", "lineNumber": 21, "object": true, "qualifiedClassName": "QTcpServer", "signals": [{"access": "public", "index": 0, "name": "newConnection", "returnType": "void"}, {"access": "public", "index": 1, "name": "pendingConnectionAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "socketError", "type": "QAbstractSocket::SocketError"}], "index": 2, "name": "acceptError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtcpserver.h", "outputRevision": 69}, {"classes": [{"className": "QTcpSocket", "lineNumber": 17, "object": true, "qualifiedClassName": "QTcpSocket", "superClasses": [{"access": "public", "name": "QAbstractSocket"}]}], "inputFile": "qtcpsocket.h", "outputRevision": 69}, {"classes": [{"className": "QTlsBackend", "lineNumber": 267, "object": true, "qualifiedClassName": "QTlsBackend", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtlsbackend_p.h", "outputRevision": 69}, {"classes": [{"className": "QUdpSocket", "lineNumber": 20, "object": true, "qualifiedClassName": "QUdpSocket", "superClasses": [{"access": "public", "name": "QAbstractSocket"}]}], "inputFile": "qudpsocket.h", "outputRevision": 69}, {"classes": [{"className": "QNetworkInformationDummyBackend", "lineNumber": 55, "object": true, "qualifiedClassName": "QNetworkInformationDummyBackend", "superClasses": [{"access": "public", "name": "QNetworkInformationBackend"}]}], "inputFile": "qnetworkinformation.cpp", "outputRevision": 69}]