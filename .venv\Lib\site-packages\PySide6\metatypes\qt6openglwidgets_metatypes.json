[{"classes": [{"className": "QOpenGLWidget", "enums": [{"isClass": false, "isFlag": false, "name": "Update<PERSON><PERSON><PERSON>or", "values": ["NoPartialUpdate", "PartialUpdate"]}, {"isClass": false, "isFlag": false, "name": "TargetBuffer", "type": "uint8_t", "values": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}], "lineNumber": 17, "object": true, "qualifiedClassName": "QOpenGLWidget", "signals": [{"access": "public", "index": 0, "name": "aboutToCompose", "returnType": "void"}, {"access": "public", "index": 1, "name": "frameSwapped", "returnType": "void"}, {"access": "public", "index": 2, "name": "aboutToResize", "returnType": "void"}, {"access": "public", "index": 3, "name": "resized", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qopenglwidget.h", "outputRevision": 69}]