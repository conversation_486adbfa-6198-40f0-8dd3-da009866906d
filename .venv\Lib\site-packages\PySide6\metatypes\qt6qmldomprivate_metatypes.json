[{"classes": [{"className": "FormatTextStatus", "enums": [{"isClass": true, "isFlag": false, "name": "StateType", "type": "quint8", "values": ["Invalid", "TopmostIntro", "TopQml", "TopJs", "ObjectdefinitionOrJs", "MultilineCommentStart", "MultilineCommentCont", "ImportStart", "ImportMaybeDotOrVersionOrAs", "ImportDot", "ImportMaybeAs", "ImportAs", "PropertyStart", "PropertyModifiers", "RequiredProperty", "PropertyListOpen", "PropertyName", "PropertyMaybeInitializer", "ComponentStart", "ComponentName", "TypeAnnotation", "TypeParameter", "EnumStart", "SignalStart", "SignalMaybeArglist", "SignalArglistOpen", "FunctionStart", "FunctionArglistOpen", "FunctionArglistClosed", "BindingOrObjectdefinition", "BindingAssignment", "ObjectdefinitionOpen", "Expression", "ExpressionContinuation", "ExpressionMaybeContinuation", "ExpressionOrObjectdefinition", "ExpressionOrLabel", "ParenOpen", "BracketOpen", "ObjectliteralOpen", "ObjectliteralAssignment", "BracketElementStart", "BracketElementMaybeObjectdefinition", "TernaryOp", "TernaryOpAfterColon", "JsblockOpen", "EmptyStatement", "BreakcontinueStatement", "IfStatement", "<PERSON><PERSON><PERSON><PERSON>", "ElseClause", "ConditionOpen", "Substatement", "SubstatementOpen", "LabelledStatement", "ReturnStatement", "ThrowStatement", "StatementWithCondition", "StatementWithConditionParenOpen", "TryStatement", "CatchStatement", "FinallyStatement", "Maybe<PERSON>atch<PERSON>r<PERSON><PERSON>ly", "DoStatement", "DoStatementWhileParenOpen", "SwitchStatement", "CaseStart", "CaseCont"]}], "gadget": true, "lineNumber": 34, "qualifiedClassName": "QQmlJS::Dom::FormatTextStatus"}, {"className": "FormatPartialStatus", "gadget": true, "lineNumber": 203, "qualifiedClassName": "QQmlJS::Dom::FormatPartialStatus"}], "inputFile": "qqmldomcodeformatter_p.h", "outputRevision": 69}, {"classes": [{"className": "IndentingLineWriter", "gadget": true, "lineNumber": 30, "qualifiedClassName": "QQmlJS::Dom::IndentingLineWriter", "superClasses": [{"access": "public", "fullyQualifiedName": "QQmlJS::Dom::LineWriter", "name": "LineWriter"}]}], "inputFile": "qqmldomindentinglinewriter_p.h", "outputRevision": 69}, {"classes": [{"className": "Token", "gadget": true, "lineNumber": 31, "qualifiedClassName": "QQmlJS::Dom::<PERSON><PERSON>"}], "inputFile": "qqmldomscanner_p.h", "outputRevision": 69}, {"classes": [{"className": "Dom", "enums": [{"isClass": true, "isFlag": false, "name": "PathRoot", "values": ["Other", "<PERSON><PERSON><PERSON>", "Cpp", "Libs", "Top", "Env", "Universe"]}, {"isClass": true, "isFlag": false, "name": "PathCurrent", "values": ["Other", "<PERSON>b<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "<PERSON><PERSON><PERSON>", "Ids", "Types", "LookupStrict", "LookupDynamic", "Lookup"]}, {"isClass": true, "isFlag": false, "name": "Language", "values": ["QmlQuick1", "QmlQuick2", "QmlQuick3", "QmlCompiled", "QmlAnnotation", "Qbs"]}, {"isClass": true, "isFlag": false, "name": "ResolveOption", "values": ["None", "TraceVisit"]}, {"isClass": true, "isFlag": false, "name": "VisitOption", "values": ["None", "VisitSelf", "VisitAdopted", "Recurse", "NoPath", "<PERSON><PERSON><PERSON>"]}, {"isClass": true, "isFlag": false, "name": "LookupOption", "values": ["Normal", "Strict", "VisitTopClassType", "SkipFirstScope"]}, {"isClass": true, "isFlag": false, "name": "LookupType", "values": ["PropertyDef", "Binding", "Property", "Method", "Type", "CppType", "Symbol"]}, {"isClass": true, "isFlag": false, "name": "VisitPrototypesOption", "values": ["Normal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RevisitWarn", "ManualProceedToScope"]}, {"isClass": true, "isFlag": false, "name": "<PERSON><PERSON><PERSON>", "values": ["Empty", "Object", "List", "Map", "Value", "ScriptElement"]}, {"isClass": true, "isFlag": false, "name": "DomType", "values": ["Empty", "ExternalItemInfo", "ExternalItemPair", "QmlDirectory", "QmldirFile", "JsFile", "QmlFile", "QmltypesFile", "GlobalScope", "EnumItem", "EnumDecl", "JsResource", "QmltypesComponent", "QmlComponent", "GlobalComponent", "ModuleAutoExport", "ModuleIndex", "ModuleScope", "ImportScope", "Export", "Import", "Pragma", "Id", "QmlObject", "ConstantData", "SimpleObjectWrap", "ScriptExpression", "Reference", "PropertyDefinition", "Binding", "MethodParameter", "MethodInfo", "Version", "Comment", "CommentedElement", "RegionComments", "AstComments", "FileLocationsInfo", "PropertyInfo", "MockObject", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "List", "ListP", "LoadInfo", "ErrorMessage", "FileLocationsNode", "DomEnvironment", "DomUniverse", "ScriptElementWrap", "ScriptElementStart", "ScriptBlockStatement", "ScriptIdentifierExpression", "<PERSON><PERSON>tL<PERSON><PERSON>", "ScriptRegExpLiteral", "ScriptForStatement", "ScriptIfStatement", "ScriptPostExpression", "ScriptUnaryExpression", "ScriptBinaryExpression", "ScriptVariableDeclaration", "ScriptVariableDeclarationEntry", "ScriptReturnStatement", "ScriptGenericElement", "ScriptCallExpression", "ScriptFormalParameter", "ScriptArray", "ScriptObject", "ScriptProperty", "ScriptType", "ScriptElision", "ScriptArrayEntry", "ScriptPattern", "ScriptSwitchStatement", "ScriptCaseBlock", "ScriptCaseClause", "ScriptDefaultClause", "ScriptWhileStatement", "ScriptDoWhileStatement", "ScriptForEachStatement", "ScriptTemplateExpressionPart", "ScriptTemplateLiteral", "ScriptTemplateStringPart", "ScriptTaggedTemplate", "ScriptTryCatchStatement", "ScriptThrowStatement", "ScriptLabelledStatement", "ScriptBreakStatement", "ScriptContinueStatement", "ScriptConditionalExpression", "ScriptEmptyStatement", "ScriptParenthesizedExpression", "ScriptFunctionExpression", "ScriptYieldExpression", "ScriptNewExpression", "ScriptNewMemberExpression", "ScriptThisExpression", "ScriptSuperLiteral", "ScriptElementStop"]}, {"isClass": true, "isFlag": false, "name": "SimpleWrapOption", "values": ["None", "ValueType"]}, {"isClass": true, "isFlag": false, "name": "BindingValueKind", "values": ["Object", "ScriptExpression", "Array", "Empty"]}, {"isClass": true, "isFlag": false, "name": "BindingType", "values": ["Normal", "OnBinding"]}, {"isClass": true, "isFlag": false, "name": "ListOptions", "values": ["Normal", "Reverse"]}, {"isClass": true, "isFlag": false, "name": "EscapeOptions", "values": ["OuterQuotes", "NoOuterQuotes"]}, {"isClass": true, "isFlag": false, "name": "ErrorLevel", "values": ["Debug", "Info", "Warning", "Error", "Fatal"]}, {"isClass": true, "isFlag": false, "name": "AstDumperOption", "values": ["None", "NoLocations", "NoAnnotations", "DumpNode", "<PERSON>loppyCompare"]}, {"isClass": true, "isFlag": false, "name": "GoTo", "values": ["Strict", "MostLikely"]}, {"isClass": true, "isFlag": false, "name": "AddOption", "values": ["KeepExisting", "Overwrite"]}, {"isClass": true, "isFlag": false, "name": "FilterUpOptions", "values": ["ReturnOuter", "ReturnOuterNoSelf", "ReturnInner"]}, {"isClass": true, "isFlag": false, "name": "WriteOutCheck", "values": ["None", "Reparse", "ReparseCompare", "ReparseStable", "<PERSON><PERSON><PERSON>"]}, {"isClass": true, "isFlag": false, "name": "LocalSymbolsType", "values": ["None", "ObjectType", "ValueType", "Signal", "Method", "Attribute", "Id", "Namespace", "Global", "MethodParameter", "<PERSON><PERSON>", "AttachedType"]}, {"isClass": false, "isFlag": false, "name": "FileLocationRegion", "type": "int", "values": ["AsTokenRegion", "BreakKeywordRegion", "DoKeywordRegion", "CaseKeywordRegion", "CatchKeywordRegion", "ColonTokenRegion", "CommaTokenRegion", "ComponentKeywordRegion", "ContinueKeywordRegion", "DefaultKeywordRegion", "DollarLeftBraceTokenRegion", "EllipsisTokenRegion", "ElseKeywordRegion", "EnumKeywordRegion", "EnumValueRegion", "EqualTokenRegion", "ForKeywordRegion", "FinallyKeywordRegion", "FirstSemicolonTokenRegion", "FunctionKeywordRegion", "IdColonTokenRegion", "IdNameRegion", "IdTokenRegion", "IdentifierRegion", "IfKeywordRegion", "ImportTokenRegion", "ImportUriRegion", "InOfTokenRegion", "LeftBacktickTokenRegion", "LeftBraceRegion", "LeftBracketRegion", "LeftParenthesisRegion", "MainRegion", "NewKeywordRegion", "OperatorTokenRegion", "OnTargetRegion", "OnTokenRegion", "PragmaKeywordRegion", "PragmaValuesRegion", "PropertyKeywordRegion", "QuestionMarkTokenRegion", "ReadonlyKeywordRegion", "RequiredKeywordRegion", "ReturnKeywordRegion", "RightBacktickTokenRegion", "RightBraceRegion", "RightBracketRegion", "RightParenthesisRegion", "SecondSemicolonRegion", "SemicolonTokenRegion", "SignalKeywordRegion", "SuperKeywordRegion", "StarTokenRegion", "SwitchKeywordRegion", "ThisKeywordRegion", "ThrowKeywordRegion", "TryKeywordRegion", "TypeIdentifierRegion", "TypeModifierRegion", "VersionRegion", "WhileKeywordRegion", "YieldKeywordRegion"]}], "lineNumber": 27, "namespace": true, "qualifiedClassName": "QQmlJS::Dom"}], "inputFile": "qqmldomconstants_p.h", "outputRevision": 69}, {"classes": [{"className": "ScriptExpression", "enums": [{"isClass": true, "isFlag": false, "name": "ExpressionType", "values": ["BindingExpression", "FunctionBody", "ArgInitializer", "ArgumentStructure", "ReturnType", "JSCode", "ESMCode"]}], "gadget": true, "lineNumber": 388, "qualifiedClassName": "QQmlJS::Dom::ScriptExpression", "superClasses": [{"access": "public", "name": "OwningItem"}]}, {"className": "MethodInfo", "enums": [{"isClass": false, "isFlag": false, "name": "MethodType", "values": ["Signal", "Method"]}], "gadget": true, "lineNumber": 752, "qualifiedClassName": "QQmlJS::Dom::MethodInfo", "superClasses": [{"access": "public", "name": "AttributeInfo"}]}], "inputFile": "qqmldomelements_p.h", "outputRevision": 69}, {"classes": [{"className": "ErrorGroup", "gadget": true, "lineNumber": 45, "qualifiedClassName": "QQmlJS::Dom::ErrorGroup"}, {"className": "ErrorGroups", "gadget": true, "lineNumber": 63, "qualifiedClassName": "QQmlJS::Dom::ErrorGroups"}, {"className": "ErrorMessage", "gadget": true, "lineNumber": 101, "qualifiedClassName": "QQmlJS::Dom::ErrorMessage"}], "inputFile": "qqmldomerrormessage_p.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON>", "gadget": true, "lineNumber": 32, "qualifiedClassName": "QQmlJS::Dom::<PERSON><PERSON><PERSON>er"}], "inputFile": "qqmldomfieldfilter_p.h", "outputRevision": 69}, {"classes": [{"className": "FileWriter", "gadget": true, "lineNumber": 29, "qualifiedClassName": "QQmlJS::Dom::FileWriter"}], "inputFile": "qqmldomfilewriter_p.h", "outputRevision": 69}, {"classes": [{"className": "Reference", "gadget": true, "lineNumber": 645, "qualifiedClassName": "QQmlJS::Dom::Reference", "superClasses": [{"access": "public", "name": "Dom<PERSON><PERSON>"}]}], "inputFile": "qqmldomitem_p.h", "outputRevision": 69}, {"classes": [{"className": "LineWriterOptions", "enums": [{"isClass": true, "isFlag": false, "name": "LineEndings", "values": ["Unix", "Windows", "OldMacOs"]}, {"isClass": true, "isFlag": false, "name": "TrailingSpace", "values": ["Preserve", "Remove"]}, {"isClass": true, "isFlag": false, "name": "AttributesSequence", "values": ["Normalize", "Preserve"]}], "gadget": true, "lineNumber": 74, "qualifiedClassName": "QQmlJS::Dom::LineWriterOptions"}, {"className": "PendingSourceLocation", "gadget": true, "lineNumber": 105, "qualifiedClassName": "QQmlJS::Dom::PendingSourceLocation"}, {"className": "LineWriter", "gadget": true, "lineNumber": 120, "qualifiedClassName": "QQmlJS::Dom::LineWriter"}], "inputFile": "qqmldomlinewriter_p.h", "outputRevision": 69}, {"classes": [{"className": "Path", "gadget": true, "lineNumber": 593, "qualifiedClassName": "QQmlJS::Dom::Path"}], "inputFile": "qqmldompath_p.h", "outputRevision": 69}, {"classes": [{"className": "DomUniverse", "gadget": true, "lineNumber": 173, "qualifiedClassName": "QQmlJS::Dom::DomUniverse", "superClasses": [{"access": "public", "name": "DomTop"}, {"access": "public", "name": "std::enable_shared_from_this<DomUniverse>"}]}, {"className": "RefCacheEntry", "enums": [{"isClass": true, "isFlag": false, "name": "<PERSON><PERSON><PERSON>", "values": ["None", "First", "All"]}], "gadget": true, "lineNumber": 701, "qualifiedClassName": "QQmlJS::Dom::RefCacheEntry"}, {"className": "DomEnvironment", "enums": [{"isClass": true, "isFlag": false, "name": "Option", "values": ["<PERSON><PERSON><PERSON>", "KeepValid", "Exported", "NoReload", "WeakLoad", "SingleThreaded", "NoDependencies"]}], "gadget": true, "lineNumber": 716, "qualifiedClassName": "QQmlJS::Dom::DomEnvironment", "superClasses": [{"access": "public", "name": "DomTop"}, {"access": "public", "name": "std::enable_shared_from_this<DomEnvironment>"}]}], "inputFile": "qqmldomtop_p.h", "outputRevision": 69}]