[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "LocalStorage"}, {"name": "QML.AddedInVersion", "value": "512"}, {"name": "QML.Singleton", "value": "true"}], "className": "QQmlLocalStorage", "lineNumber": 26, "methods": [{"access": "public", "arguments": [{"name": "args", "type": "QQmlV4FunctionPtr"}], "index": 0, "name": "openDatabaseSync", "returnType": "void"}], "object": true, "qualifiedClassName": "QQmlLocalStorage", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmllocalstorage_p.h", "outputRevision": 69}]