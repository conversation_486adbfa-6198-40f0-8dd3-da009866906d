[{"classes": [{"className": "QQuickWorkerScriptEngine", "lineNumber": 31, "object": true, "qualifiedClassName": "QQuickWorkerScriptEngine", "superClasses": [{"access": "public", "name": "QThread"}]}, {"classInfos": [{"name": "QML.Element", "value": "WorkerScript"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickWorkerScript", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 50, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "ready", "notify": "readyChanged", "read": "ready", "required": false, "revision": 527, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QQuickWorkerScript", "signals": [{"access": "public", "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "readyChanged", "returnType": "void", "revision": 527}, {"access": "public", "arguments": [{"name": "messageObject", "type": "QJSValue"}], "index": 2, "name": "message", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"type": "QQmlV4FunctionPtr"}], "index": 3, "name": "sendMessage", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquickworkerscript_p.h", "outputRevision": 69}, {"classes": [{"className": "QQuickWorkerScriptEnginePrivate", "lineNumber": 107, "object": true, "qualifiedClassName": "QQuickWorkerScriptEnginePrivate", "signals": [{"access": "public", "index": 0, "name": "stopThread", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickworkerscript.cpp", "outputRevision": 69}]