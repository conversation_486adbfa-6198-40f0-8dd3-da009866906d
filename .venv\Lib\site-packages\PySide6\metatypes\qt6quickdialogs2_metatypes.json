[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "ColorDialog"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "QQuickColorDialog", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "selectedColor", "notify": "selectedColorChanged", "read": "selectedColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSelectedColor"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "options", "notify": "optionsChanged", "read": "options", "required": false, "reset": "resetOptions", "scriptable": true, "stored": true, "type": "QColorDialogOptions::ColorDialogOptions", "user": false, "write": "setOptions"}], "qualifiedClassName": "QQuickColorDialog", "signals": [{"access": "public", "index": 0, "name": "selectedColorChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "optionsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickAbstractDialog"}]}], "inputFile": "qquickcolordialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "data"}, {"name": "QML.Element", "value": "anonymous"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuickAbstractDialog", "enums": [{"isClass": false, "isFlag": false, "name": "StandardCode", "values": ["Rejected", "Accepted"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 38, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "data", "read": "data", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "parentWindow", "notify": "parentWindowChanged", "read": "parentWindow", "required": false, "reset": "resetParentWindow", "scriptable": true, "stored": true, "type": "QWindow*", "user": false, "write": "setParentWindow"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "title", "notify": "titleChanged", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitle"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "flags", "notify": "flagsChanged", "read": "flags", "required": false, "scriptable": true, "stored": true, "type": "Qt::WindowFlags", "user": false, "write": "setFlags"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "modality", "notify": "modalityChanged", "read": "modality", "required": false, "scriptable": true, "stored": true, "type": "Qt::WindowModality", "user": false, "write": "setModality"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "result", "notify": "resultC<PERSON>ed", "read": "result", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setResult"}], "qualifiedClassName": "QQuickAbstractDialog", "signals": [{"access": "public", "index": 0, "name": "accepted", "returnType": "void"}, {"access": "public", "index": 1, "name": "rejected", "returnType": "void"}, {"access": "public", "index": 2, "name": "parentWindowChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "titleChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "flagsChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "modalityChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "resultC<PERSON>ed", "returnType": "void"}], "slots": [{"access": "public", "index": 8, "name": "open", "returnType": "void"}, {"access": "public", "index": 9, "name": "close", "returnType": "void"}, {"access": "public", "index": 10, "name": "accept", "returnType": "void"}, {"access": "public", "index": 11, "name": "reject", "returnType": "void"}, {"access": "public", "arguments": [{"name": "result", "type": "int"}], "index": 12, "name": "done", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquickabstractdialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "FileDialog"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuickFileDialog", "enums": [{"isClass": false, "isFlag": false, "name": "FileMode", "values": ["OpenFile", "OpenFiles", "SaveFile"]}], "lineNumber": 27, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "fileMode", "notify": "fileModeChanged", "read": "fileMode", "required": false, "scriptable": true, "stored": true, "type": "FileMode", "user": false, "write": "setFileMode"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "selectedFile", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "selectedFile", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSelectedFile"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "selectedFiles", "notify": "selectedFilesChanged", "read": "selectedFiles", "required": false, "scriptable": true, "stored": true, "type": "QList<QUrl>", "user": false}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "currentFile", "notify": "currentFileChanged", "read": "currentFile", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setCurrentFile"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "currentFiles", "notify": "currentFilesChanged", "read": "currentFiles", "required": false, "scriptable": true, "stored": true, "type": "QList<QUrl>", "user": false, "write": "setCurrentFiles"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "currentFolder", "notify": "currentFolderChanged", "read": "currentFolder", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setCurrentFolder"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "options", "notify": "optionsChanged", "read": "options", "required": false, "reset": "resetOptions", "scriptable": true, "stored": true, "type": "QFileDialogOptions::FileDialogOptions", "user": false, "write": "setOptions"}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "nameFilters", "notify": "nameFiltersChanged", "read": "nameFilters", "required": false, "reset": "resetNameFilters", "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setNameFilters"}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QQuickFileNameFilter*", "user": false}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "defaultSuffix", "notify": "defaultSuffixChanged", "read": "defaultSuffix", "required": false, "reset": "resetDefaultSuffix", "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setDefaultSuffix"}, {"constant": false, "designable": true, "final": true, "index": 10, "name": "acceptLabel", "notify": "accept<PERSON>abel<PERSON><PERSON><PERSON>", "read": "acceptLabel", "required": false, "reset": "resetAcceptLabel", "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setAcceptLabel"}, {"constant": false, "designable": true, "final": true, "index": 11, "name": "<PERSON><PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "reset": "resetRejectLabel", "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRejectLabel"}], "qualifiedClassName": "QQuickFileDialog", "signals": [{"access": "public", "index": 0, "name": "fileModeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 2, "name": "selectedFilesChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "currentFileChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "currentFilesChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "currentFolderChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "optionsChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "nameFiltersChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "defaultSuffixChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "accept<PERSON>abel<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 10, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickAbstractDialog"}]}], "inputFile": "qquickfiledialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "FolderDialog"}, {"name": "QML.AddedInVersion", "value": "1539"}], "className": "QQuickFolderDialog", "lineNumber": 27, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "currentFolder", "notify": "currentFolderChanged", "read": "currentFolder", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setCurrentFolder"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "selectedFolder", "notify": "selectedFolderChanged", "read": "selectedFolder", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSelectedFolder"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "options", "notify": "optionsChanged", "read": "options", "required": false, "reset": "resetOptions", "scriptable": true, "stored": true, "type": "QFileDialogOptions::FileDialogOptions", "user": false, "write": "setOptions"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "acceptLabel", "notify": "accept<PERSON>abel<PERSON><PERSON><PERSON>", "read": "acceptLabel", "required": false, "reset": "resetAcceptLabel", "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setAcceptLabel"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "reset": "resetRejectLabel", "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRejectLabel"}], "qualifiedClassName": "QQuickFolderDialog", "signals": [{"access": "public", "index": 0, "name": "currentFolderChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "selectedFolderChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "optionsChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "accept<PERSON>abel<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickAbstractDialog"}]}], "inputFile": "qquickfolderdialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "FontDialog"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuickFontDialog", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "selected<PERSON>ont", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "selected<PERSON>ont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setSelectedFont"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "currentFont", "notify": "currentFontChanged", "read": "currentFont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setCurrentFont"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "options", "notify": "optionsChanged", "read": "options", "required": false, "reset": "resetOptions", "scriptable": true, "stored": true, "type": "QFontDialogOptions::FontDialogOptions", "user": false, "write": "setOptions"}], "qualifiedClassName": "QQuickFontDialog", "signals": [{"access": "public", "index": 0, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 1, "name": "currentFontChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "optionsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickAbstractDialog"}]}], "inputFile": "qquickfontdialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Extended", "value": "QPlatformDialogHelper"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}, {"name": "QML.Element", "value": "MessageDialog"}, {"name": "QML.AddedInVersion", "value": "1539"}], "className": "QQuickMessageDialog", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "informativeText", "notify": "informativeTextChanged", "read": "informativeText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setInformativeText"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "detailedText", "notify": "detailedTextChanged", "read": "detailedText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setDetailedText"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "buttons", "notify": "buttonsChanged", "read": "buttons", "required": false, "scriptable": true, "stored": true, "type": "QPlatformDialogHelper::StandardButtons", "user": false, "write": "setButtons"}], "qualifiedClassName": "QQuickMessageDialog", "signals": [{"access": "public", "index": 0, "name": "textChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "informativeTextChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "detailedTextChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "buttonsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "button", "type": "QPlatformDialogHelper::StandardButton"}, {"name": "role", "type": "QPlatformDialogHelper::ButtonRole"}], "index": 4, "name": "buttonClicked", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "button", "type": "QPlatformDialogHelper::StandardButton"}, {"name": "role", "type": "QPlatformDialogHelper::ButtonRole"}], "index": 5, "name": "handleClick", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickAbstractDialog"}]}], "inputFile": "qquickmessagedialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QQuickFileNameFilter"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuickFileNameFilterQuickDialogs2Foreign", "gadget": true, "lineNumber": 24, "qualifiedClassName": "QQuickFileNameFilterQuickDialogs2Foreign"}], "inputFile": "qtquickdialogs2foreign_p.h", "outputRevision": 69}]