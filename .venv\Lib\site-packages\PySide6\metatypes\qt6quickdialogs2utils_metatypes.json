[{"classes": [{"className": "QQuickFileNameFilter", "lineNumber": 27, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "index", "notify": "indexChanged", "read": "index", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setIndex"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "extensions", "notify": "extensionsChanged", "read": "extensions", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "globs", "notify": "globsChanged", "read": "globs", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}], "qualifiedClassName": "QQuickFileNameFilter", "signals": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 0, "name": "indexChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 1, "name": "nameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extensions", "type": "QStringList"}], "index": 2, "name": "extensionsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "globs", "type": "QStringList"}], "index": 3, "name": "globsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickfilenamefilter_p.h", "outputRevision": 69}]