[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "particle"}, {"name": "QML.AddedInVersion", "value": "1543"}], "className": "QQuickV4ParticleData", "gadget": true, "lineNumber": 23, "methods": [{"access": "public", "index": 0, "name": "discard", "returnType": "void"}], "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "initialX", "read": "initialX", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_initialX"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "initialVX", "read": "initialVX", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_initialVX"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "initialAX", "read": "initialAX", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_initialAX"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "initialY", "read": "initialY", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_initialY"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "initialVY", "read": "initialVY", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_initialVY"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "initialAY", "read": "initialAY", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_initialAY"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "t", "read": "t", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_t"}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "startSize", "read": "startSize", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_startSize"}, {"constant": false, "designable": true, "final": true, "index": 8, "name": "endSize", "read": "endSize", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_endSize"}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "lifeSpan", "read": "lifeSpan", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_lifeSpan"}, {"constant": false, "designable": true, "final": true, "index": 10, "name": "rotation", "read": "rotation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_rotation"}, {"constant": false, "designable": true, "final": true, "index": 11, "name": "rotationVelocity", "read": "rotationVelocity", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_rotationVelocity"}, {"constant": false, "designable": true, "final": true, "index": 12, "name": "autoRotate", "read": "autoRotate", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set_autoRotate"}, {"constant": false, "designable": true, "final": true, "index": 13, "name": "update", "read": "update", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set_update"}, {"constant": false, "designable": true, "final": true, "index": 14, "name": "xDeformationVectorX", "read": "xDeformationVectorX", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_xDeformationVectorX"}, {"constant": false, "designable": true, "final": true, "index": 15, "name": "yDeformationVectorX", "read": "yDeformationVectorX", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_yDeformationVectorX"}, {"constant": false, "designable": true, "final": true, "index": 16, "name": "xDeformationVectorY", "read": "xDeformationVectorY", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_xDeformationVectorY"}, {"constant": false, "designable": true, "final": true, "index": 17, "name": "yDeformationVectorY", "read": "yDeformationVectorY", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_yDeformationVectorY"}, {"constant": false, "designable": true, "final": true, "index": 18, "name": "animationIndex", "read": "animationIndex", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_animationIndex"}, {"constant": false, "designable": true, "final": true, "index": 19, "name": "frameDuration", "read": "frameDuration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_frameDuration"}, {"constant": false, "designable": true, "final": true, "index": 20, "name": "frameAt", "read": "frameAt", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_frameAt"}, {"constant": false, "designable": true, "final": true, "index": 21, "name": "frameCount", "read": "frameCount", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_frameCount"}, {"constant": false, "designable": true, "final": true, "index": 22, "name": "animationT", "read": "animationT", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_animationT"}, {"constant": false, "designable": true, "final": false, "index": 23, "name": "x", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_x"}, {"constant": false, "designable": true, "final": false, "index": 24, "name": "vx", "read": "vx", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_vx"}, {"constant": false, "designable": true, "final": false, "index": 25, "name": "ax", "read": "ax", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_ax"}, {"constant": false, "designable": true, "final": false, "index": 26, "name": "y", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_y"}, {"constant": false, "designable": true, "final": false, "index": 27, "name": "vy", "read": "vy", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_vy"}, {"constant": false, "designable": true, "final": false, "index": 28, "name": "ay", "read": "ay", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_ay"}, {"constant": false, "designable": true, "final": false, "index": 29, "name": "red", "read": "red", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_red"}, {"constant": false, "designable": true, "final": false, "index": 30, "name": "green", "read": "green", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_green"}, {"constant": false, "designable": true, "final": false, "index": 31, "name": "blue", "read": "blue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_blue"}, {"constant": false, "designable": true, "final": false, "index": 32, "name": "alpha", "read": "alpha", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "set_alpha"}, {"constant": false, "designable": true, "final": false, "index": 33, "name": "lifeLeft", "read": "lifeLeft", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 34, "name": "currentSize", "read": "currentSize", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "QQuickV4ParticleData"}], "inputFile": "qquickv4particledata_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Age"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickAgeAffector", "lineNumber": 21, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "lifeLeft", "notify": "lifeLeftChanged", "read": "lifeLeft", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLifeLeft"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "advancePosition", "notify": "advancePositionChanged", "read": "advancePosition", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAdvancePosition"}], "qualifiedClassName": "QQuickAgeAffector", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 0, "name": "lifeLeftChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 1, "name": "advancePositionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 2, "name": "setLifeLeft", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 3, "name": "setAdvancePosition", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleAffector"}]}], "inputFile": "qquickage_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "AngleDirection"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickAngleDirection", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "angle", "notify": "angleChanged", "read": "angle", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAngle"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "magnitude", "notify": "magnitudeChanged", "read": "magnitude", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMagnitude"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "angleVariation", "notify": "angleVariationChanged", "read": "angleVariation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAngleVariation"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "magnitudeVariation", "notify": "magnitudeVariationChanged", "read": "magnitudeVariation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMagnitudeVariation"}], "qualifiedClassName": "QQuickAngleDirection", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 0, "name": "angleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 1, "name": "magnitudeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 2, "name": "angleVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 3, "name": "magnitudeVariationChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 4, "name": "setAngle", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 5, "name": "setMagnitude", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 6, "name": "setAngleVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 7, "name": "setMagnitudeVariation", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickDirection"}]}], "inputFile": "qquickangledirection_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "directions"}, {"name": "QML.Element", "value": "CumulativeDirection"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickCumulativeDirection", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "directions", "read": "directions", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuickDirection>", "user": false}], "qualifiedClassName": "QQuickCumulativeDirection", "superClasses": [{"access": "public", "name": "QQuickDirection"}]}], "inputFile": "qquickcumulativedirection_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Affector"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickCustomAffector", "lineNumber": 27, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "relative", "notify": "relativeChanged", "read": "relative", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRelative"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "reset": "position<PERSON><PERSON><PERSON>", "scriptable": true, "stored": true, "type": "QQuickDirection*", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "velocity", "notify": "velocityChanged", "read": "velocity", "required": false, "reset": "velocityReset", "scriptable": true, "stored": true, "type": "QQuickDirection*", "user": false, "write": "setVelocity"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "acceleration", "notify": "accelerationChanged", "read": "acceleration", "required": false, "reset": "accelerationReset", "scriptable": true, "stored": true, "type": "QQuickDirection*", "user": false, "write": "setAcceleration"}], "qualifiedClassName": "QQuickCustomAffector", "signals": [{"access": "public", "arguments": [{"name": "particles", "type": "QList<QQuickV4ParticleData>"}, {"name": "dt", "type": "qreal"}], "index": 0, "name": "affectParticles", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 1, "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 2, "name": "velocityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 3, "name": "accelerationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 4, "name": "relativeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 5, "name": "setPosition", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 6, "name": "setVelocity", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 7, "name": "setAcceleration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 8, "name": "setRelative", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleAffector"}]}], "inputFile": "qquickcustomaffector_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "NullVector"}, {"name": "QML.AddedInVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Abstract type. Use one of the inheriting types instead."}], "className": "QQuickDirection", "lineNumber": 26, "object": true, "qualifiedClassName": "QQuickDirection", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickdirection_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "EllipseShape"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickEllipseExtruder", "lineNumber": 21, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fill", "notify": "fillChanged", "read": "fill", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFill"}], "qualifiedClassName": "QQuickEllipseExtruder", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 0, "name": "fillChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 1, "name": "setFill", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleExtruder"}]}], "inputFile": "qquickellipseextruder_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Friction"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickFrictionAffector", "lineNumber": 21, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "factor", "notify": "factorChanged", "read": "factor", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setFactor"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "threshold", "notify": "thresholdChanged", "read": "threshold", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QQuickFrictionAffector", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 0, "name": "factorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 1, "name": "thresholdChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 2, "name": "setFactor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleAffector"}]}], "inputFile": "qquickfriction_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Gravity"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickGravityAffector", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "magnitude", "notify": "magnitudeChanged", "read": "magnitude", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMagnitude"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "acceleration", "notify": "magnitudeChanged", "read": "magnitude", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAcceleration"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "angle", "notify": "angleChanged", "read": "angle", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAngle"}], "qualifiedClassName": "QQuickGravityAffector", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 0, "name": "magnitudeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 1, "name": "angleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 2, "name": "setMagnitude", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 3, "name": "setAcceleration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 4, "name": "setAngle", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleAffector"}]}], "inputFile": "qquickgravity_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "GroupGoal"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickGroupGoalAffector", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "goalState", "notify": "goalState<PERSON>hanged", "read": "goalState", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setGoalState"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "jump", "notify": "jumpChanged", "read": "jump", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setJump"}], "qualifiedClassName": "QQuickGroupGoalAffector", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 0, "name": "goalState<PERSON>hanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 1, "name": "jumpChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 2, "name": "setGoalState", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 3, "name": "setJump", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleAffector"}]}], "inputFile": "qquickgroupgoal_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ImageParticle"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickImageParticle", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}, {"isClass": false, "isFlag": false, "name": "EntryEffect", "values": ["None", "Fade", "Scale"]}], "lineNumber": 149, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "imageChanged", "read": "image", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setImage"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "sprites", "read": "sprites", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuickSprite>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "colorTable", "notify": "colortableChanged", "read": "colortable", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setColortable"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "sizeTable", "notify": "sizetableChanged", "read": "sizetable", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSizetable"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "opacityTable", "notify": "opacitytableChanged", "read": "opacitytable", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setOpacitytable"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "reset": "resetColor", "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "colorVariation", "notify": "colorVariationChanged", "read": "colorVariation", "required": false, "reset": "resetColor", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setColorVariation"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "redVariation", "notify": "redVariationChanged", "read": "redVariation", "required": false, "reset": "resetColor", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRedVariation"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "greenVariation", "notify": "greenVariationChanged", "read": "greenVariation", "required": false, "reset": "resetColor", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setGreenVariation"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "blueVariation", "notify": "blueVariationChanged", "read": "blueVariation", "required": false, "reset": "resetColor", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBlueVariation"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "alpha", "notify": "alphaChanged", "read": "alpha", "required": false, "reset": "resetColor", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "alphaVariation", "notify": "alphaVariationChanged", "read": "alphaVariation", "required": false, "reset": "resetColor", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAlphaVariation"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "rotation", "notify": "rotationChanged", "read": "rotation", "required": false, "reset": "resetRotation", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRotation"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "rotationVariation", "notify": "rotationVariationChanged", "read": "rotationVariation", "required": false, "reset": "resetRotation", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRotationVariation"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "rotationVelocity", "notify": "rotationVelocityChanged", "read": "rotationVelocity", "required": false, "reset": "resetRotation", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRotationVelocity"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "rotationVelocityVariation", "notify": "rotationVelocityVariationChanged", "read": "rotationVelocityVariation", "required": false, "reset": "resetRotation", "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRotationVelocityVariation"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "autoRotation", "notify": "autoRotationChanged", "read": "autoRotation", "required": false, "reset": "resetRotation", "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoRotation"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "xVector", "notify": "xVectorChanged", "read": "xVector", "required": false, "reset": "resetDeformation", "scriptable": true, "stored": true, "type": "QQuickDirection*", "user": false, "write": "setXVector"}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "yVector", "notify": "yVectorChanged", "read": "yVector", "required": false, "reset": "resetDeformation", "scriptable": true, "stored": true, "type": "QQuickDirection*", "user": false, "write": "setYVector"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "spritesInterpolate", "notify": "spritesInterpolateChanged", "read": "spritesInterpolate", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSpritesInterpolate"}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "entryEffect", "notify": "entryEffectChanged", "read": "entryEffect", "required": false, "scriptable": true, "stored": true, "type": "EntryEffect", "user": false, "write": "setEntryEffect"}], "qualifiedClassName": "QQuickImageParticle", "signals": [{"access": "public", "index": 0, "name": "imageChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "colortableChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "sizetableChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "opacitytableChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "colorChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "colorVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 6, "name": "alphaVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 7, "name": "alphaChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 8, "name": "redVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 9, "name": "greenVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 10, "name": "blueVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 11, "name": "rotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 12, "name": "rotationVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 13, "name": "rotationVelocityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 14, "name": "rotationVelocityVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 15, "name": "autoRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 16, "name": "xVectorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 17, "name": "yVectorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 18, "name": "spritesInterpolateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 19, "name": "bypassOptimizationsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "EntryEffect"}], "index": 20, "name": "entryEffectChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "Status"}], "index": 21, "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 22, "name": "setAlphaVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 23, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 24, "name": "setRedVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 25, "name": "setGreenVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 26, "name": "setBlueVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 27, "name": "setRotation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 28, "name": "setRotationVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 29, "name": "setRotationVelocity", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 30, "name": "setRotationVelocityVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 31, "name": "setAutoRotation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 32, "name": "setXVector", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 33, "name": "setYVector", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 34, "name": "setSpritesInterpolate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 35, "name": "setBypassOptimizations", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "EntryEffect"}], "index": 36, "name": "setEntryEffect", "returnType": "void"}, {"access": "private", "index": 37, "name": "createEngine", "returnType": "void"}, {"access": "private", "arguments": [{"name": "spriteIndex", "type": "int"}], "index": 38, "name": "spriteAdvance", "returnType": "void"}, {"access": "private", "arguments": [{"name": "time", "type": "qreal"}], "index": 39, "name": "spritesUpdate", "returnType": "void"}, {"access": "private", "index": 40, "isCloned": true, "name": "spritesUpdate", "returnType": "void"}, {"access": "private", "index": 41, "name": "mainThreadFetchImageData", "returnType": "void"}, {"access": "private", "index": 42, "name": "invalidateSceneGraph", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticlePainter"}]}], "inputFile": "qquickimageparticle_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ItemParticle"}, {"name": "QML.AddedInVersion", "value": "512"}, {"name": "QML.Attached", "value": "QQuickItemParticleAttached"}], "className": "QQuickItemParticle", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fade", "notify": "fadeChanged", "read": "fade", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFade"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "delegate", "notify": "delegate<PERSON><PERSON><PERSON>", "read": "delegate", "required": false, "scriptable": true, "stored": true, "type": "QQmlComponent*", "user": false, "write": "setDelegate"}], "qualifiedClassName": "QQuickItemParticle", "signals": [{"access": "public", "index": 0, "name": "fadeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQmlComponent*"}], "index": 1, "name": "delegate<PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "item", "type": "QQuickItem*"}], "index": 2, "name": "freeze", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QQuickItem*"}], "index": 3, "name": "unfreeze", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QQuickItem*"}, {"name": "prioritize", "type": "bool"}], "index": 4, "name": "take", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QQuickItem*"}], "index": 5, "isCloned": true, "name": "take", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QQuickItem*"}], "index": 6, "name": "give", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 7, "name": "setFade", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQmlComponent*"}], "index": 8, "name": "setDelegate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticlePainter"}]}, {"className": "QQuickItemParticleAttached", "lineNumber": 98, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "particle", "read": "particle", "required": false, "scriptable": true, "stored": true, "type": "QQuickItemParticle*", "user": false}], "qualifiedClassName": "QQuickItemParticleAttached", "signals": [{"access": "public", "index": 0, "name": "detached", "returnType": "void"}, {"access": "public", "index": 1, "name": "attached", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickitemparticle_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "LineShape"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickLineExtruder", "lineNumber": 19, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "mirrored", "notify": "mirroredChanged", "read": "mirrored", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QQuickLineExtruder", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 0, "name": "mirroredChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 1, "name": "set<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleExtruder"}]}], "inputFile": "qquicklineextruder_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MaskShape"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickMaskExtruder", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}], "qualifiedClassName": "QQuickMaskExtruder", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "QUrl"}], "index": 0, "name": "sourceChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "QUrl"}], "index": 1, "name": "setSource", "returnType": "void"}, {"access": "private", "index": 2, "name": "startMaskLoading", "returnType": "void"}, {"access": "private", "index": 3, "name": "finishMaskLoading", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleExtruder"}]}], "inputFile": "qquickmaskextruder_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ParticleAffector"}, {"name": "QML.AddedInVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Abstract type. Use one of the inheriting types instead."}], "className": "QQuickParticleAffector", "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "system", "notify": "systemChanged", "read": "system", "required": false, "scriptable": true, "stored": true, "type": "QQuickParticleSystem*", "user": false, "write": "setSystem"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "groups", "notify": "groupsChanged", "read": "groups", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setGroups"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "whenCollidingWith", "notify": "whenCollidingWithChanged", "read": "whenCollidingWith", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setWhenCollidingWith"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "enabled", "notify": "enabledChanged", "read": "enabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "once", "notify": "onceChanged", "read": "onceOff", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "shape", "notify": "shapeChanged", "read": "shape", "required": false, "scriptable": true, "stored": true, "type": "QQuickParticleExtruder*", "user": false, "write": "setShape"}], "qualifiedClassName": "QQuickParticleAffector", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleSystem*"}], "index": 0, "name": "systemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QStringList"}], "index": 1, "name": "groupsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 2, "name": "enabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 3, "name": "onceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleExtruder*"}], "index": 4, "name": "shapeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 5, "name": "affected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QStringList"}], "index": 6, "name": "whenCollidingWithChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleSystem*"}], "index": 7, "name": "setSystem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QStringList"}], "index": 8, "name": "setGroups", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 9, "name": "setEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 10, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleExtruder*"}], "index": 11, "name": "setShape", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QStringList"}], "index": 12, "name": "setWhenCollidingWith", "returnType": "void"}, {"access": "public", "index": 13, "name": "updateOffsets", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qquickparticleaffector_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Emitter"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickParticleEmitter", "enums": [{"isClass": false, "isFlag": false, "name": "Lifetime", "values": ["InfiniteLife"]}], "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "system", "notify": "systemChanged", "read": "system", "required": false, "scriptable": true, "stored": true, "type": "QQuickParticleSystem*", "user": false, "write": "setSystem"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "group", "notify": "groupChanged", "read": "group", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setGroup"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "shape", "notify": "extruder<PERSON><PERSON>ed", "read": "extruder", "required": false, "scriptable": true, "stored": true, "type": "QQuickParticleExtruder*", "user": false, "write": "setExtruder"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "enabled", "notify": "enabledChanged", "read": "enabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "startTime", "notify": "startTimeChanged", "read": "startTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setStartTime"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "emitRate", "notify": "particlesPerSecondChanged", "read": "particlesPerSecond", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setParticlesPerSecond"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "lifeSpan", "notify": "particleDurationChanged", "read": "particleDuration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setParticleDuration"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "lifeSpanVariation", "notify": "particleDurationVariationChanged", "read": "particleDurationVariation", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setParticleDurationVariation"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "maximumEmitted", "notify": "maximumEmittedChanged", "read": "maxParticleCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaxParticleCount"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "size", "notify": "particleSizeChanged", "read": "particleSize", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setParticleSize"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "endSize", "notify": "particleEndSizeChanged", "read": "particleEndSize", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setParticleEndSize"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "sizeVariation", "notify": "particleSizeVariationChanged", "read": "particleSizeVariation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setParticleSizeVariation"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "velocity", "notify": "velocityChanged", "read": "velocity", "required": false, "scriptable": true, "stored": true, "type": "QQuickDirection*", "user": false, "write": "setVelocity"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "acceleration", "notify": "accelerationChanged", "read": "acceleration", "required": false, "scriptable": true, "stored": true, "type": "QQuickDirection*", "user": false, "write": "setAcceleration"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "velocityFromMovement", "notify": "velocityFromMovementChanged", "read": "velocityFromMovement", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setVelocityFromMovement"}], "qualifiedClassName": "QQuickParticleEmitter", "signals": [{"access": "public", "arguments": [{"name": "particles", "type": "QList<QQuickV4ParticleData>"}], "index": 0, "name": "emitParticles", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qreal"}], "index": 1, "name": "particlesPerSecondChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 2, "name": "particleDurationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 3, "name": "enabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleSystem*"}], "index": 4, "name": "systemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 5, "name": "groupChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 6, "name": "particleDurationVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleExtruder*"}], "index": 7, "name": "extruder<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 8, "name": "particleSizeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 9, "name": "particleEndSizeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 10, "name": "particleSizeVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 11, "name": "velocityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 12, "name": "accelerationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 13, "name": "maximumEmittedChanged", "returnType": "void"}, {"access": "public", "index": 14, "name": "particleCountChanged", "returnType": "void"}, {"access": "public", "index": 15, "name": "velocityFromMovementChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 16, "name": "startTimeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "milliseconds", "type": "int"}], "index": 17, "name": "pulse", "returnType": "void"}, {"access": "public", "arguments": [{"name": "num", "type": "int"}], "index": 18, "name": "burst", "returnType": "void"}, {"access": "public", "arguments": [{"name": "num", "type": "int"}, {"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 19, "name": "burst", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 20, "name": "setEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 21, "name": "setParticlesPerSecond", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 22, "name": "setParticleDuration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleSystem*"}], "index": 23, "name": "setSystem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 24, "name": "setGroup", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 25, "name": "setParticleDurationVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleExtruder*"}], "index": 26, "name": "setExtruder", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 27, "name": "setParticleSize", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 28, "name": "setParticleEndSize", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 29, "name": "setParticleSizeVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 30, "name": "setVelocity", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickDirection*"}], "index": 31, "name": "setAcceleration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 32, "name": "setMaxParticleCount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 33, "name": "setStartTime", "returnType": "void"}, {"access": "public", "index": 34, "name": "reset", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qquickparticleemitter_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ParticleExtruder"}, {"name": "QML.AddedInVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Abstract type. Use one of the inheriting types instead."}], "className": "QQuickParticleExtruder", "lineNumber": 27, "object": true, "qualifiedClassName": "QQuickParticleExtruder", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickparticleextruder_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "particleChildren"}, {"name": "QML.Element", "value": "ParticleGroup"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickParticleGroup", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "system", "notify": "systemChanged", "read": "system", "required": false, "scriptable": true, "stored": true, "type": "QQuickParticleSystem*", "user": false, "write": "setSystem"}, {"constant": false, "designable": false, "final": false, "index": 1, "name": "particleChildren", "read": "particleChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "QQuickParticleGroup", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleSystem*"}], "index": 0, "name": "systemChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleSystem*"}], "index": 1, "name": "setSystem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "obj", "type": "QObject*"}], "index": 2, "name": "delayRedirect", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickStochasticState"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquickparticlegroup_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ParticlePainter"}, {"name": "QML.AddedInVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Abstract type. Use one of the inheriting types instead."}], "className": "QQuickParticlePainter", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "system", "notify": "systemChanged", "read": "system", "required": false, "scriptable": true, "stored": true, "type": "QQuickParticleSystem*", "user": false, "write": "setSystem"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "groups", "notify": "groupsChanged", "read": "groups", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setGroups"}], "qualifiedClassName": "QQuickParticlePainter", "signals": [{"access": "public", "index": 0, "name": "countChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleSystem*"}], "index": 1, "name": "systemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QStringList"}], "index": 2, "name": "groupsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleSystem*"}], "index": 3, "name": "setSystem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QStringList"}], "index": 4, "name": "setGroups", "returnType": "void"}, {"access": "public", "arguments": [{"name": "resetPending", "type": "bool"}], "index": 5, "name": "calcSystemOffset", "returnType": "void"}, {"access": "public", "index": 6, "isCloned": true, "name": "calcSystemOffset", "returnType": "void"}, {"access": "private", "index": 7, "name": "sceneGraphInvalidated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qquickparticlepainter_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ParticleSystem"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickParticleSystem", "lineNumber": 308, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "running", "notify": "running<PERSON><PERSON>ed", "read": "isRunning", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRunning"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "paused", "notify": "paused<PERSON><PERSON>ed", "read": "isPaused", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPaused"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "empty", "notify": "emptyChanged", "read": "isEmpty", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QQuickParticleSystem", "signals": [{"access": "public", "index": 0, "name": "systemInitialized", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 1, "name": "running<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 2, "name": "paused<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 3, "name": "emptyChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 4, "name": "start", "returnType": "void"}, {"access": "public", "index": 5, "name": "stop", "returnType": "void"}, {"access": "public", "index": 6, "name": "restart", "returnType": "void"}, {"access": "public", "index": 7, "name": "pause", "returnType": "void"}, {"access": "public", "index": 8, "name": "resume", "returnType": "void"}, {"access": "public", "index": 9, "name": "reset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 10, "name": "setRunning", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 11, "name": "setPaused", "returnType": "void"}, {"access": "public", "index": 12, "isConst": true, "name": "duration", "returnType": "int"}, {"access": "private", "index": 13, "name": "emittersChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "p", "type": "QQuickParticlePainter*"}], "index": 14, "name": "loadPainter", "returnType": "void"}, {"access": "private", "index": 15, "name": "createEngine", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 16, "name": "particleStateChange", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}, {"className": "QQuickParticleSystemAnimation", "lineNumber": 438, "object": true, "qualifiedClassName": "QQuickParticleSystemAnimation", "superClasses": [{"access": "public", "name": "QAbstractAnimation"}]}], "inputFile": "qquickparticlesystem_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Attractor"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickAttractorAffector", "enums": [{"isClass": false, "isFlag": false, "name": "Proportion", "values": ["Constant", "Linear", "Quadratic", "InverseLinear", "InverseQuadratic"]}, {"isClass": false, "isFlag": false, "name": "AffectableParameters", "values": ["Position", "Velocity", "Acceleration"]}], "lineNumber": 21, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "strength", "notify": "strengthChanged", "read": "strength", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setStrength"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pointX", "notify": "pointXChanged", "read": "pointX", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setPointX"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pointY", "notify": "pointYChanged", "read": "pointY", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setPointY"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "affectedParameter", "notify": "affectedParameterChanged", "read": "affectedParameter", "required": false, "scriptable": true, "stored": true, "type": "AffectableParameters", "user": false, "write": "setAffectedParameter"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "proportionalToDistance", "notify": "proportionalToDistanceChanged", "read": "proportionalToDistance", "required": false, "scriptable": true, "stored": true, "type": "Proportion", "user": false, "write": "setProportionalToDistance"}], "qualifiedClassName": "QQuickAttractorAffector", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 0, "name": "strengthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 1, "name": "pointXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 2, "name": "pointYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "AffectableParameters"}], "index": 3, "name": "affectedParameterChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "Proportion"}], "index": 4, "name": "proportionalToDistanceChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 5, "name": "setStrength", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 6, "name": "setPointX", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 7, "name": "setPointY", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "AffectableParameters"}], "index": 8, "name": "setAffectedParameter", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "Proportion"}], "index": 9, "name": "setProportionalToDistance", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleAffector"}]}], "inputFile": "qquickpointattractor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PointDirection"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickPointDirection", "lineNumber": 21, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "xVariation", "notify": "xVariationChanged", "read": "xVariation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setXVariation"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "yVariation", "notify": "yVariationChanged", "read": "yVariation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setYVariation"}], "qualifiedClassName": "QQuickPointDirection", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 0, "name": "xChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 1, "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 2, "name": "xVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 3, "name": "yVariationChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 4, "name": "setX", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 5, "name": "setY", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 6, "name": "setXVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 7, "name": "setYVariation", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickDirection"}]}], "inputFile": "qquickpointdirection_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "RectangleShape"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickRectangleExtruder", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fill", "notify": "fillChanged", "read": "fill", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFill"}], "qualifiedClassName": "QQuickRectangleExtruder", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 0, "name": "fillChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 1, "name": "setFill", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleExtruder"}]}], "inputFile": "qquickrectangleextruder_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "SpriteGoal"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickSpriteGoalAffector", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "goalState", "notify": "goalState<PERSON>hanged", "read": "goalState", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setGoalState"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "jump", "notify": "jumpChanged", "read": "jump", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setJump"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "systemStates", "notify": "systemStatesChanged", "read": "systemStates", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSystemStates"}], "qualifiedClassName": "QQuickSpriteGoalAffector", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 0, "name": "goalState<PERSON>hanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 1, "name": "jumpChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 2, "name": "systemStatesChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 3, "name": "setGoalState", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 4, "name": "setJump", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 5, "name": "setSystemStates", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleAffector"}]}], "inputFile": "qquickspritegoal_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TargetDirection"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickTargetDirection", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "targetX", "notify": "targetXChanged", "read": "targetX", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTargetX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "targetY", "notify": "targetYChanged", "read": "targetY", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTargetY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "targetItem", "notify": "targetItemChanged", "read": "targetItem", "required": false, "scriptable": true, "stored": true, "type": "QQuickItem*", "user": false, "write": "setTargetItem"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "targetVariation", "notify": "targetVariationChanged", "read": "targetVariation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTargetVariation"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "proportionalMagnitude", "notify": "proprotionalMagnitudeChanged", "read": "proportionalMagnitude", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setProportionalMagnitude"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "magnitude", "notify": "magnitudeChanged", "read": "magnitude", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMagnitude"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "magnitudeVariation", "notify": "magnitudeVariationChanged", "read": "magnitudeVariation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMagnitudeVariation"}], "qualifiedClassName": "QQuickTargetDirection", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 0, "name": "targetXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 1, "name": "targetYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 2, "name": "targetVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 3, "name": "magnitudeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 4, "name": "proprotionalMagnitudeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 5, "name": "magnitudeVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickItem*"}], "index": 6, "name": "targetItemChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 7, "name": "setTargetX", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 8, "name": "setTargetY", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 9, "name": "setTargetVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 10, "name": "setMagnitude", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 11, "name": "setProportionalMagnitude", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 12, "name": "setMagnitudeVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickItem*"}], "index": 13, "name": "setTargetItem", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickDirection"}]}], "inputFile": "qquicktargetdirection_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TrailEmitter"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickTrailEmitter", "enums": [{"isClass": false, "isFlag": false, "name": "EmitSize", "values": ["ParticleSize"]}], "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "follow", "notify": "follow<PERSON><PERSON>ed", "read": "follow", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "emitRatePerParticle", "notify": "particlesPerParticlePerSecondChanged", "read": "particlesPerParticlePerSecond", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setParticlesPerParticlePerSecond"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "emitShape", "notify": "emissionShapeChanged", "read": "emissonShape", "required": false, "scriptable": true, "stored": true, "type": "QQuickParticleExtruder*", "user": false, "write": "setEmissionShape"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "emitHeight", "notify": "emitterYVariationChanged", "read": "emitterYVariation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setEmitterYVariation"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "emitWidth", "notify": "emitterXVariationChanged", "read": "emitterXVariation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setEmitterXVariation"}], "qualifiedClassName": "QQuickTrailEmitter", "signals": [{"access": "public", "arguments": [{"name": "particles", "type": "QList<QQuickV4ParticleData>"}, {"name": "followed", "type": "QQuickV4ParticleData"}], "index": 0, "name": "emitFollowParticles", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 1, "name": "particlesPerParticlePerSecondChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 2, "name": "emitterXVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 3, "name": "emitterYVariationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 4, "name": "follow<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleExtruder*"}], "index": 5, "name": "emissionShapeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "int"}], "index": 6, "name": "setParticlesPerParticlePerSecond", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 7, "name": "setEmitterXVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 8, "name": "setEmitterYVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 9, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QQuickParticleExtruder*"}], "index": 10, "name": "setEmissionShape", "returnType": "void"}, {"access": "private", "index": 11, "name": "recalcParticlesPerSecond", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleEmitter"}]}], "inputFile": "qquicktrailemitter_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Turbulence"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickTurbulenceAffector", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "strength", "notify": "strengthChanged", "read": "strength", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setStrength"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "noiseSource", "notify": "noiseSourceChanged", "read": "noiseSource", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setNoiseSource"}], "qualifiedClassName": "QQuickTurbulenceAffector", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 0, "name": "strengthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QUrl"}], "index": 1, "name": "noiseSourceChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 2, "name": "setStrength", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QUrl"}], "index": 3, "name": "setNoiseSource", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleAffector"}]}], "inputFile": "qquickturbulence_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QQuickWanderAffector", "enums": [{"isClass": false, "isFlag": false, "name": "AffectableParameters", "values": ["Position", "Velocity", "Acceleration"]}], "lineNumber": 31, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pace", "notify": "paceChanged", "read": "pace", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setPace"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "xV<PERSON>ce", "notify": "xVarianceChanged", "read": "xV<PERSON>ce", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setX<PERSON><PERSON>ce"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "y<PERSON><PERSON><PERSON>", "notify": "yV<PERSON><PERSON><PERSON><PERSON>ed", "read": "y<PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "affectedParameter", "notify": "affectedParameterChanged", "read": "affectedParameter", "required": false, "scriptable": true, "stored": true, "type": "AffectableParameters", "user": false, "write": "setAffectedParameter"}], "qualifiedClassName": "QQuickWanderAffector", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 0, "name": "xVarianceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 1, "name": "yV<PERSON><PERSON><PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 2, "name": "paceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "AffectableParameters"}], "index": 3, "name": "affectedParameterChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 4, "name": "setX<PERSON><PERSON>ce", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 6, "name": "setPace", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "AffectableParameters"}], "index": 7, "name": "setAffectedParameter", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickParticleAffector"}]}], "inputFile": "qquickwander_p.h", "outputRevision": 69}]