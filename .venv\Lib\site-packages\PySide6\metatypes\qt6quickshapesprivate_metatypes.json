[{"classes": [{"className": "QQuickShapeCurveRunnable", "lineNumber": 144, "object": true, "qualifiedClassName": "QQuickShapeCurveRunnable", "signals": [{"access": "public", "arguments": [{"name": "self", "type": "QQuickShapeCurveRunnable*"}], "index": 0, "name": "done", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QRunnable"}]}], "inputFile": "qquickshapecurverenderer_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "stops"}, {"name": "QML.Element", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "ShapeGradient is an abstract base class."}], "className": "QQuickShapeGradient", "enums": [{"isClass": false, "isFlag": false, "name": "SpreadMode", "values": ["PadSpread", "ReflectSpread", "RepeatSpread"]}], "lineNumber": 38, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "spread", "notify": "spreadChanged", "read": "spread", "required": false, "scriptable": true, "stored": true, "type": "SpreadMode", "user": false, "write": "setSpread"}], "qualifiedClassName": "QQuickShapeGradient", "signals": [{"access": "public", "index": 0, "name": "spreadChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickGradient"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "stops"}, {"name": "QML.Element", "value": "LinearGradient"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QQuickShapeLinearGradient", "lineNumber": 68, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "x1", "notify": "x1Changed", "read": "x1", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setX1"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "y1", "notify": "y1Changed", "read": "y1", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setY1"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "x2", "notify": "x2Changed", "read": "x2", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setX2"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "y2", "notify": "y2Changed", "read": "y2", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setY2"}], "qualifiedClassName": "QQuickShapeLinearGradient", "signals": [{"access": "public", "index": 0, "name": "x1Changed", "returnType": "void"}, {"access": "public", "index": 1, "name": "y1Changed", "returnType": "void"}, {"access": "public", "index": 2, "name": "x2Changed", "returnType": "void"}, {"access": "public", "index": 3, "name": "y2Changed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickShapeGradient"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "stops"}, {"name": "QML.Element", "value": "RadialGrad<PERSON>"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QQuickShapeRadialGradient", "lineNumber": 102, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "centerX", "notify": "centerXChanged", "read": "centerX", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setCenterX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "centerY", "notify": "centerYChanged", "read": "centerY", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setCenterY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "centerRadius", "notify": "centerRadiusChanged", "read": "centerRadius", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setCenterRadius"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "focalX", "notify": "focalXChanged", "read": "focalX", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setFocalX"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "focalY", "notify": "focalYChanged", "read": "focalY", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setFocalY"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "focalRadius", "notify": "focalRadiusChanged", "read": "focalRadius", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setFocalRadius"}], "qualifiedClassName": "QQuickShapeRadialGradient", "signals": [{"access": "public", "index": 0, "name": "centerXChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "centerYChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "focalXChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "focalYChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "centerRadiusChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "focalRadiusChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickShapeGradient"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "stops"}, {"name": "QML.Element", "value": "ConicalGradient"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QQuickShapeConicalGradient", "lineNumber": 151, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "centerX", "notify": "centerXChanged", "read": "centerX", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setCenterX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "centerY", "notify": "centerYChanged", "read": "centerY", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setCenterY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "angle", "notify": "angleChanged", "read": "angle", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAngle"}], "qualifiedClassName": "QQuickShapeConicalGradient", "signals": [{"access": "public", "index": 0, "name": "centerXChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "centerYChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "angleChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickShapeGradient"}]}, {"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QQuickShapePath", "enums": [{"isClass": false, "isFlag": false, "name": "FillRule", "values": ["OddEvenFill", "WindingFill"]}, {"isClass": false, "isFlag": false, "name": "Join<PERSON><PERSON><PERSON>", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Round<PERSON>oin"]}, {"isClass": false, "isFlag": false, "name": "CapStyle", "values": ["FlatCap", "SquareCap", "RoundCap"]}, {"isClass": false, "isFlag": false, "name": "StrokeStyle", "values": ["SolidLine", "DashLine"]}, {"alias": "PathHint", "isClass": false, "isFlag": true, "name": "PathHints", "values": ["PathLinear", "PathQuadratic", "PathConvex", "PathFillOnRight", "PathSolid", "PathNonIntersecting", "PathNonOverlappingControlPointTriangles"]}], "lineNumber": 183, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "strokeColor", "notify": "strokeColorChanged", "read": "strokeColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setStrokeColor"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "strokeWidth", "notify": "stroke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "strokeWidth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setStrokeWidth"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "fillColor", "notify": "fillColorChanged", "read": "fillColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setFillColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "fillRule", "notify": "fillRuleChanged", "read": "fillRule", "required": false, "scriptable": true, "stored": true, "type": "FillRule", "user": false, "write": "setFillRule"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "joinStyle", "notify": "joinStyleChanged", "read": "joinStyle", "required": false, "scriptable": true, "stored": true, "type": "Join<PERSON><PERSON><PERSON>", "user": false, "write": "setJoinStyle"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "miterLimit", "notify": "miterLimitChanged", "read": "miterLimit", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMiterLimit"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "capStyle", "notify": "capStyleChanged", "read": "capStyle", "required": false, "scriptable": true, "stored": true, "type": "CapStyle", "user": false, "write": "setCapStyle"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "strokeStyle", "notify": "strokeStyleChanged", "read": "strokeStyle", "required": false, "scriptable": true, "stored": true, "type": "StrokeStyle", "user": false, "write": "setStrokeStyle"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "dashOffset", "notify": "dashOffsetChanged", "read": "dashOffset", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setDashOffset"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "dashPattern", "notify": "dashPatternChanged", "read": "dashPattern", "required": false, "scriptable": true, "stored": true, "type": "QList<qreal>", "user": false, "write": "setDashPattern"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "fillGradient", "read": "fillGradient", "required": false, "reset": "resetFillGradient", "scriptable": true, "stored": true, "type": "QQuickShapeGradient*", "user": false, "write": "setFillGradient"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "scale", "notify": "scaleChanged", "read": "scale", "required": false, "revision": 270, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setScale"}, {"constant": false, "designable": true, "final": true, "index": 12, "name": "pathHints", "notify": "pathHintsChanged", "read": "pathHints", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "PathHints", "user": false, "write": "setPathHints"}, {"constant": false, "designable": true, "final": true, "index": 13, "name": "fillTransform", "notify": "fillTransformChanged", "read": "fillTransform", "required": false, "revision": 1544, "scriptable": true, "stored": true, "type": "QMatrix4x4", "user": false, "write": "setFillTransform"}, {"constant": false, "designable": true, "final": true, "index": 14, "name": "fillItem", "notify": "fillItemChanged", "read": "fillItem", "required": false, "revision": 1544, "scriptable": true, "stored": true, "type": "QQuickItem*", "user": false, "write": "setFillItem"}], "qualifiedClassName": "QQuickShapePath", "signals": [{"access": "public", "index": 0, "name": "shapePathChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "strokeColorChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "stroke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 3, "name": "fillColorChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "fillRuleChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "joinStyleChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "miterLimitChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "capStyleChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "strokeStyleChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "dashOffsetChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "dashPatternChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "pathHintsChanged", "returnType": "void", "revision": 1543}, {"access": "public", "index": 12, "name": "fillTransformChanged", "returnType": "void", "revision": 1544}, {"access": "public", "index": 13, "name": "fillItemChanged", "returnType": "void", "revision": 1544}], "slots": [{"access": "private", "index": 14, "name": "_q_fillGradientChanged", "returnType": "void"}, {"access": "private", "index": 15, "name": "_q_fillItemDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickPath"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "data"}, {"name": "QML.Element", "value": "<PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QQuickShape", "enums": [{"isClass": false, "isFlag": false, "name": "RendererType", "values": ["<PERSON><PERSON><PERSON><PERSON>", "Geometry<PERSON><PERSON><PERSON>", "Nvpr<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Processing"]}, {"isClass": false, "isFlag": false, "name": "ContainsMode", "values": ["BoundingRectContains", "FillContains"]}, {"isClass": false, "isFlag": false, "name": "FillMode", "values": ["NoResize", "PreserveAspectFit", "PreserveAspectCrop", "<PERSON><PERSON><PERSON>"]}, {"isClass": false, "isFlag": false, "name": "HAlignment", "values": ["AlignLeft", "AlignRight", "AlignHCenter"]}, {"isClass": false, "isFlag": false, "name": "VAlignment", "values": ["AlignTop", "AlignBottom", "AlignVCenter"]}], "lineNumber": 316, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rendererType", "notify": "rendererChanged", "read": "rendererType", "required": false, "scriptable": true, "stored": true, "type": "RendererType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "asynchronous", "notify": "asynchronousChanged", "read": "asynchronous", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAsynchronous"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "vendorExtensionsEnabled", "notify": "vendorExtensionsEnabledChanged", "read": "vendorExtensionsEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVendorExtensionsEnabled"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "preferredRendererType", "notify": "preferredRendererTypeChanged", "read": "preferredRendererType", "required": false, "revision": 1542, "scriptable": true, "stored": true, "type": "RendererType", "user": false, "write": "setPreferredRendererType"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "containsMode", "notify": "containsModeChanged", "read": "containsMode", "required": false, "revision": 267, "scriptable": true, "stored": true, "type": "ContainsMode", "user": false, "write": "setContainsMode"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "boundingRect", "notify": "boundingRectChanged", "read": "boundingRect", "required": false, "revision": 1542, "scriptable": true, "stored": true, "type": "QRectF", "user": false}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "fillMode", "notify": "fillModeChanged", "read": "fillMode", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "FillMode", "user": false, "write": "setFillMode"}, {"constant": false, "designable": true, "final": true, "index": 8, "name": "horizontalAlignment", "notify": "horizontalAlignmentChanged", "read": "horizontalAlignment", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "HAlignment", "user": false, "write": "setHorizontalAlignment"}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "verticalAlignment", "notify": "verticalAlignmentChanged", "read": "verticalAlignment", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "VAlignment", "user": false, "write": "setVerticalAlignment"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "data", "read": "data", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "QQuickShape", "signals": [{"access": "public", "index": 0, "name": "rendererChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "asynchronousChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "vendorExtensionsEnabledChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "preferredRendererTypeChanged", "returnType": "void", "revision": 1542}, {"access": "public", "index": 5, "name": "boundingRectChanged", "returnType": "void", "revision": 1542}, {"access": "public", "index": 6, "name": "containsModeChanged", "returnType": "void", "revision": 267}, {"access": "public", "index": 7, "name": "fillModeChanged", "returnType": "void", "revision": 1543}, {"access": "public", "index": 8, "name": "horizontalAlignmentChanged", "returnType": "void", "revision": 1543}, {"access": "public", "index": 9, "name": "verticalAlignmentChanged", "returnType": "void", "revision": 1543}], "slots": [{"access": "private", "index": 10, "name": "_q_shapePathChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qquickshape_p.h", "outputRevision": 69}, {"classes": [{"className": "QQuickShapeFillRunnable", "lineNumber": 139, "object": true, "qualifiedClassName": "QQuickShapeFillRunnable", "signals": [{"access": "public", "arguments": [{"name": "self", "type": "QQuickShapeFillRunnable*"}], "index": 0, "name": "done", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QRunnable"}]}, {"className": "QQuickShapeStrokeRunnable", "lineNumber": 163, "object": true, "qualifiedClassName": "QQuickShapeStrokeRunnable", "signals": [{"access": "public", "arguments": [{"name": "self", "type": "QQuickShapeStrokeRunnable*"}], "index": 0, "name": "done", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QRunnable"}]}, {"className": "QQuickShapeGenericStrokeFillNode", "lineNumber": 186, "object": true, "qualifiedClassName": "QQuickShapeGenericStrokeFillNode", "slots": [{"access": "private", "index": 0, "name": "handleTextureChanged", "returnType": "void"}, {"access": "private", "index": 1, "name": "handleTextureProviderDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QSGGeometryNode"}]}], "inputFile": "qquickshapegenericrenderer_p.h", "outputRevision": 69}]