[{"classes": [{"className": "QQuickWidget", "enums": [{"isClass": false, "isFlag": false, "name": "ResizeMode", "values": ["SizeViewToRootObject", "SizeRootObjectToView"]}, {"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "resizeMode", "read": "resizeMode", "required": false, "scriptable": true, "stored": true, "type": "ResizeMode", "user": false, "write": "setResizeMode"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "source", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}], "qualifiedClassName": "QQuickWidget", "signals": [{"access": "public", "arguments": [{"type": "QQuickWidget::Status"}], "index": 0, "name": "statusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QQuickWindow::SceneGraphError"}, {"name": "message", "type": "QString"}], "index": 1, "name": "sceneGraphError", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"type": "QUrl"}], "index": 2, "name": "setSource", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "component", "type": "QQmlComponent*"}, {"name": "item", "type": "QObject*"}], "index": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "initialProperties", "type": "QVariantMap"}], "index": 4, "name": "setInitialProperties", "returnType": "void"}, {"access": "public", "arguments": [{"name": "uri", "type": "QAnyStringView"}, {"name": "typeName", "type": "QAnyStringView"}], "index": 5, "name": "loadFromModule", "returnType": "void"}, {"access": "private", "index": 6, "name": "continueExecute", "returnType": "void"}, {"access": "private", "index": 7, "name": "createFramebufferObject", "returnType": "void"}, {"access": "private", "index": 8, "name": "destroyFramebufferObject", "returnType": "void"}, {"access": "private", "index": 9, "name": "triggerUpdate", "returnType": "void"}, {"access": "private", "arguments": [{"name": "focusObject", "type": "QObject*"}], "index": 10, "name": "propagateFocusObjectChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qquickwidget.h", "outputRevision": 69}, {"classes": [{"className": "QQuickWidgetOffscreenWindow", "lineNumber": 120, "object": true, "qualifiedClassName": "QQuickWidgetOffscreenWindow", "superClasses": [{"access": "public", "name": "QQuickWindow"}]}], "inputFile": "qquickwidget_p.h", "outputRevision": 69}]