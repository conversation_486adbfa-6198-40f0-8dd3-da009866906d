[{"classes": [{"className": "QSqlDatabase", "gadget": true, "lineNumber": 37, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "numericalPrecisionPolicy", "read": "numericalPrecisionPolicy", "required": false, "scriptable": true, "stored": true, "type": "QSql::NumericalPrecisionPolicy", "user": false, "write": "setNumericalPrecisionPolicy"}], "qualifiedClassName": "QSqlDatabase"}], "inputFile": "qsqldatabase.h", "outputRevision": 69}, {"classes": [{"className": "QSqlDriver", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "numericalPrecisionPolicy", "read": "numericalPrecisionPolicy", "required": false, "scriptable": true, "stored": true, "type": "QSql::NumericalPrecisionPolicy", "user": false, "write": "setNumericalPrecisionPolicy"}], "qualifiedClassName": "QSqlDriver", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "source", "type": "QSqlDriver::NotificationSource"}, {"name": "payload", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 0, "name": "notification", "returnType": "void"}], "slots": [{"access": "public", "index": 1, "name": "cancelQuery", "returnType": "bool"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsqldriver.h", "outputRevision": 69}, {"classes": [{"className": "QSqlDriverPlugin", "lineNumber": 18, "object": true, "qualifiedClassName": "QSqlDriverPlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsqldriverplugin.h", "outputRevision": 69}, {"classes": [{"className": "QSqlField", "gadget": true, "lineNumber": 18, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "value", "read": "value", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setValue"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "defaultValue", "read": "defaultValue", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setDefaultValue"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "tableName", "read": "tableName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTableName"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "metaType", "read": "metaType", "required": false, "scriptable": true, "stored": true, "type": "QMetaType", "user": false, "write": "setMetaType"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "requiredStatus", "read": "requiredStatus", "required": false, "scriptable": true, "stored": true, "type": "RequiredStatus", "user": false, "write": "setRequiredStatus"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "readOnly", "read": "isReadOnly", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReadOnly"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "generated", "read": "isGenerated", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGenerated"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "autoValue", "read": "isAutoValue", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoValue"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "length", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "precision", "read": "precision", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setPrecision"}], "qualifiedClassName": "QSqlField"}], "inputFile": "qsqlfield.h", "outputRevision": 69}, {"classes": [{"className": "QSqlIndex", "gadget": true, "lineNumber": 17, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "cursor<PERSON><PERSON>", "read": "cursor<PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCursorName"}], "qualifiedClassName": "QSqlIndex", "superClasses": [{"access": "public", "name": "QSqlRecord"}]}], "inputFile": "qsqlindex.h", "outputRevision": 69}, {"classes": [{"className": "QSqlQuery", "gadget": true, "lineNumber": 23, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "forwardOnly", "read": "isForwardOnly", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set<PERSON>orwardOnly"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "positionalBindingEnabled", "read": "isPositionalBindingEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPositionalBindingEnabled"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "numericalPrecisionPolicy", "read": "numericalPrecisionPolicy", "required": false, "scriptable": true, "stored": true, "type": "QSql::NumericalPrecisionPolicy", "user": false, "write": "setNumericalPrecisionPolicy"}], "qualifiedClassName": "QSqlQuery"}], "inputFile": "qsqlquery.h", "outputRevision": 69}, {"classes": [{"className": "QSqlQueryModel", "lineNumber": 20, "object": true, "qualifiedClassName": "QSqlQueryModel", "superClasses": [{"access": "public", "name": "QAbstractTableModel"}]}], "inputFile": "qsqlquerymodel.h", "outputRevision": 69}, {"classes": [{"className": "QSqlRelationalTableModel", "lineNumber": 47, "object": true, "qualifiedClassName": "QSqlRelationalTableModel", "slots": [{"access": "public", "arguments": [{"name": "row", "type": "int"}], "index": 0, "name": "revertRow", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSqlTableModel"}]}], "inputFile": "qsqlrelationaltablemodel.h", "outputRevision": 69}, {"classes": [{"className": "QSqlTableModel", "lineNumber": 21, "object": true, "qualifiedClassName": "QSqlTableModel", "signals": [{"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "record", "type": "QSqlRecord&"}], "index": 0, "name": "primeInsert", "returnType": "void"}, {"access": "public", "arguments": [{"name": "record", "type": "QSqlRecord&"}], "index": 1, "name": "beforeInsert", "returnType": "void"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}, {"name": "record", "type": "QSqlRecord&"}], "index": 2, "name": "beforeUpdate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}], "index": 3, "name": "beforeDelete", "returnType": "void"}], "slots": [{"access": "public", "index": 4, "name": "select", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "row", "type": "int"}], "index": 5, "name": "selectRow", "returnType": "bool"}, {"access": "public", "index": 6, "name": "submit", "returnType": "bool"}, {"access": "public", "index": 7, "name": "revert", "returnType": "void"}, {"access": "public", "index": 8, "name": "submitAll", "returnType": "bool"}, {"access": "public", "index": 9, "name": "revertAll", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSqlQueryModel"}]}], "inputFile": "qsqltablemodel.h", "outputRevision": 69}]