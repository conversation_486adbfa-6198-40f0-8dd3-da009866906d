[{"classes": [{"className": "QSvg<PERSON><PERSON><PERSON>", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "viewBox", "read": "viewBoxF", "required": false, "scriptable": true, "stored": true, "type": "QRectF", "user": false, "write": "setViewBox"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "framesPerSecond", "read": "framesPerSecond", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFramesPerSecond"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "currentFrame", "read": "currentFrame", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCurrentFrame"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "aspectRatioMode", "read": "aspectRatioMode", "required": false, "scriptable": true, "stored": true, "type": "Qt::AspectRatioMode", "user": false, "write": "setAspectRatioMode"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "options", "read": "options", "required": false, "scriptable": true, "stored": true, "type": "QtSvg::Options", "user": false, "write": "setOptions"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "animationEnabled", "read": "isAnimationEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAnimationEnabled"}], "qualifiedClassName": "QSvg<PERSON><PERSON><PERSON>", "signals": [{"access": "public", "index": 0, "name": "repaintNeeded", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "filename", "type": "QString"}], "index": 1, "name": "load", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "contents", "type": "QByteArray"}], "index": 2, "name": "load", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "contents", "type": "QXmlStreamReader*"}], "index": 3, "name": "load", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "p", "type": "QPainter*"}], "index": 4, "name": "render", "returnType": "void"}, {"access": "public", "arguments": [{"name": "p", "type": "QPainter*"}, {"name": "bounds", "type": "QRectF"}], "index": 5, "name": "render", "returnType": "void"}, {"access": "public", "arguments": [{"name": "p", "type": "QPainter*"}, {"name": "elementId", "type": "QString"}, {"name": "bounds", "type": "QRectF"}], "index": 6, "name": "render", "returnType": "void"}, {"access": "public", "arguments": [{"name": "p", "type": "QPainter*"}, {"name": "elementId", "type": "QString"}], "index": 7, "isCloned": true, "name": "render", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsvgrenderer.h", "outputRevision": 69}]