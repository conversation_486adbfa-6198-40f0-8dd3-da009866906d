import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qtquickdialogs2quickimplforeign_p.h"
        name: "QQuickAbstractButton"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        Enum {
            name: "Display"
            values: [
                "IconOnly",
                "TextOnly",
                "TextBesideIcon",
                "TextUnderIcon"
            ]
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            reset: "resetText"
            notify: "textChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "down"
            type: "bool"
            read: "isDown"
            write: "setDown"
            reset: "resetDown"
            notify: "downChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            notify: "pressedChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "checked"
            type: "bool"
            read: "isChecked"
            write: "setChecked"
            notify: "checkedChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "checkable"
            type: "bool"
            read: "isCheckable"
            write: "setCheckable"
            notify: "checkableChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "autoExclusive"
            type: "bool"
            read: "autoExclusive"
            write: "setAutoExclusive"
            notify: "autoExclusiveChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "autoRepeat"
            type: "bool"
            read: "autoRepeat"
            write: "setAutoRepeat"
            notify: "autoRepeatChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "indicator"
            type: "QQuickItem"
            isPointer: true
            read: "indicator"
            write: "setIndicator"
            notify: "indicatorChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "icon"
            revision: 515
            type: "QQuickIcon"
            read: "icon"
            write: "setIcon"
            notify: "iconChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "display"
            revision: 515
            type: "Display"
            read: "display"
            write: "setDisplay"
            notify: "displayChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "action"
            revision: 515
            type: "QQuickAction"
            isPointer: true
            read: "action"
            write: "setAction"
            notify: "actionChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "autoRepeatDelay"
            revision: 516
            type: "int"
            read: "autoRepeatDelay"
            write: "setAutoRepeatDelay"
            notify: "autoRepeatDelayChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "autoRepeatInterval"
            revision: 516
            type: "int"
            read: "autoRepeatInterval"
            write: "setAutoRepeatInterval"
            notify: "autoRepeatIntervalChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "pressX"
            revision: 516
            type: "double"
            read: "pressX"
            notify: "pressXChanged"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "pressY"
            revision: 516
            type: "double"
            read: "pressY"
            notify: "pressYChanged"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitIndicatorWidth"
            revision: 517
            type: "double"
            read: "implicitIndicatorWidth"
            notify: "implicitIndicatorWidthChanged"
            index: 15
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitIndicatorHeight"
            revision: 517
            type: "double"
            read: "implicitIndicatorHeight"
            notify: "implicitIndicatorHeightChanged"
            index: 16
            isReadonly: true
            isFinal: true
        }
        Signal { name: "pressed" }
        Signal { name: "released" }
        Signal { name: "canceled" }
        Signal { name: "clicked" }
        Signal { name: "pressAndHold" }
        Signal { name: "doubleClicked" }
        Signal { name: "textChanged" }
        Signal { name: "downChanged" }
        Signal { name: "pressedChanged" }
        Signal { name: "checkedChanged" }
        Signal { name: "checkableChanged" }
        Signal { name: "autoExclusiveChanged" }
        Signal { name: "autoRepeatChanged" }
        Signal { name: "indicatorChanged" }
        Signal { name: "toggled"; revision: 514 }
        Signal { name: "iconChanged"; revision: 515 }
        Signal { name: "displayChanged"; revision: 515 }
        Signal { name: "actionChanged"; revision: 515 }
        Signal { name: "autoRepeatDelayChanged"; revision: 516 }
        Signal { name: "autoRepeatIntervalChanged"; revision: 516 }
        Signal { name: "pressXChanged"; revision: 516 }
        Signal { name: "pressYChanged"; revision: 516 }
        Signal { name: "implicitIndicatorWidthChanged"; revision: 517 }
        Signal { name: "implicitIndicatorHeightChanged"; revision: 517 }
        Method { name: "toggle" }
        Method { name: "click"; revision: 1544 }
        Method { name: "animateClick"; revision: 1544 }
        Method { name: "accessiblePressAction" }
    }
    Component {
        file: "private/qquickabstractcolorpicker_p.h"
        name: "QQuickAbstractColorPicker"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        deferredNames: ["background", "contentItem", "handle"]
        exports: [
            "QtQuick.Dialogs.quickimpl/AbstractColorPicker 6.4",
            "QtQuick.Dialogs.quickimpl/AbstractColorPicker 6.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1540, 1543]
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
        }
        Property {
            name: "hue"
            type: "double"
            read: "hue"
            write: "setHue"
            notify: "colorChanged"
            index: 1
        }
        Property {
            name: "saturation"
            type: "double"
            read: "saturation"
            write: "setSaturation"
            notify: "colorChanged"
            index: 2
        }
        Property {
            name: "value"
            type: "double"
            read: "value"
            write: "setValue"
            notify: "colorChanged"
            index: 3
        }
        Property {
            name: "lightness"
            type: "double"
            read: "lightness"
            write: "setLightness"
            notify: "colorChanged"
            index: 4
        }
        Property {
            name: "alpha"
            type: "double"
            read: "alpha"
            write: "setAlpha"
            notify: "colorChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            write: "setPressed"
            notify: "pressedChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "handle"
            type: "QQuickItem"
            isPointer: true
            read: "handle"
            write: "setHandle"
            notify: "handleChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "implicitHandleWidth"
            type: "double"
            read: "implicitHandleWidth"
            notify: "implicitHandleWidthChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitHandleHeight"
            type: "double"
            read: "implicitHandleHeight"
            notify: "implicitHandleHeightChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal { name: "pressedChanged" }
        Signal { name: "handleChanged" }
        Signal { name: "implicitHandleWidthChanged" }
        Signal { name: "implicitHandleHeightChanged" }
        Signal {
            name: "colorPicked"
            Parameter { name: "color"; type: "QColor" }
        }
    }
    Component {
        file: "private/qquickcolordialogimpl_p.h"
        name: "QQuickColorDialogImpl"
        accessSemantics: "reference"
        prototype: "QQuickDialog"
        exports: [
            "QtQuick.Dialogs.quickimpl/ColorDialogImpl 6.4",
            "QtQuick.Dialogs.quickimpl/ColorDialogImpl 6.8"
        ]
        exportMetaObjectRevisions: [1540, 1544]
        attachedType: "QQuickColorDialogImplAttached"
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
        }
        Property {
            name: "hue"
            type: "double"
            read: "hue"
            write: "setHue"
            notify: "colorChanged"
            index: 1
        }
        Property {
            name: "saturation"
            type: "double"
            read: "saturation"
            write: "setSaturation"
            notify: "colorChanged"
            index: 2
        }
        Property {
            name: "value"
            type: "double"
            read: "value"
            write: "setValue"
            notify: "colorChanged"
            index: 3
        }
        Property {
            name: "lightness"
            type: "double"
            read: "lightness"
            write: "setLightness"
            notify: "colorChanged"
            index: 4
        }
        Property {
            name: "alpha"
            type: "double"
            read: "alpha"
            write: "setAlpha"
            notify: "colorChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "red"
            type: "int"
            read: "red"
            write: "setRed"
            notify: "colorChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "green"
            type: "int"
            read: "green"
            write: "setGreen"
            notify: "colorChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "blue"
            type: "int"
            read: "blue"
            write: "setBlue"
            notify: "colorChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "isHsl"
            type: "bool"
            read: "isHsl"
            write: "setHsl"
            notify: "specChanged"
            index: 9
            isFinal: true
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal { name: "specChanged" }
        Method { name: "invokeEyeDropper" }
    }
    Component {
        file: "private/qquickcolordialogimpl_p.h"
        name: "QQuickColorDialogImplAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "buttonBox"
            type: "QQuickDialogButtonBox"
            isPointer: true
            read: "buttonBox"
            write: "setButtonBox"
            notify: "buttonBoxChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "eyeDropperButton"
            type: "QQuickAbstractButton"
            isPointer: true
            read: "eyeDropperButton"
            write: "setEyeDropperButton"
            notify: "eyeDropperButtonChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "colorPicker"
            type: "QQuickAbstractColorPicker"
            isPointer: true
            read: "colorPicker"
            write: "setColorPicker"
            notify: "colorPickerChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "colorInputs"
            type: "QQuickColorInputs"
            isPointer: true
            read: "colorInputs"
            write: "setColorInputs"
            notify: "colorInputsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "alphaSlider"
            type: "QQuickSlider"
            isPointer: true
            read: "alphaSlider"
            write: "setAlphaSlider"
            notify: "alphaSliderChanged"
            index: 4
            isFinal: true
        }
        Signal { name: "buttonBoxChanged" }
        Signal { name: "eyeDropperButtonChanged" }
        Signal { name: "colorPickerChanged" }
        Signal { name: "colorInputsChanged" }
        Signal { name: "alphaSliderChanged" }
    }
    Component {
        file: "private/qquickcolorinputs_p.h"
        name: "QQuickColorInputs"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: ["QtQuick.Dialogs.quickimpl/ColorInputsImpl 6.9"]
        exportMetaObjectRevisions: [1545]
        Enum {
            name: "Mode"
            values: ["Hex", "Rgb", "Hsv", "Hsl"]
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "red"
            type: "int"
            read: "red"
            notify: "colorChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "green"
            type: "int"
            read: "green"
            notify: "colorChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "blue"
            type: "int"
            read: "blue"
            notify: "colorChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hue"
            type: "double"
            read: "hue"
            notify: "colorChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hslSaturation"
            type: "double"
            read: "hslSaturation"
            notify: "colorChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hsvSaturation"
            type: "double"
            read: "hsvSaturation"
            notify: "colorChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "value"
            type: "double"
            read: "value"
            notify: "colorChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "lightness"
            type: "double"
            read: "lightness"
            notify: "colorChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "alpha"
            type: "double"
            read: "alpha"
            notify: "colorChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "showAlpha"
            type: "bool"
            read: "showAlpha"
            write: "setShowAlpha"
            notify: "showAlphaChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "mode"
            type: "Mode"
            read: "currentMode"
            write: "setCurrentMode"
            notify: "currentModeChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 12
            isFinal: true
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "c"; type: "QColor" }
        }
        Signal {
            name: "colorModified"
            Parameter { name: "c"; type: "QColor" }
        }
        Signal { name: "hslChanged" }
        Signal {
            name: "showAlphaChanged"
            Parameter { type: "bool" }
        }
        Signal { name: "currentModeChanged" }
        Signal { name: "delegateChanged" }
    }
    Component {
        file: "private/qtquickdialogs2quickimplforeign_p.h"
        name: "QQuickControl"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            reset: "resetFont"
            notify: "fontChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "availableWidth"
            type: "double"
            read: "availableWidth"
            notify: "availableWidthChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "availableHeight"
            type: "double"
            read: "availableHeight"
            notify: "availableHeightChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "padding"
            type: "double"
            read: "padding"
            write: "setPadding"
            reset: "resetPadding"
            notify: "paddingChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "topPadding"
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            reset: "resetTopPadding"
            notify: "topPaddingChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "leftPadding"
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            reset: "resetLeftPadding"
            notify: "leftPaddingChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "rightPadding"
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            reset: "resetRightPadding"
            notify: "rightPaddingChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "bottomPadding"
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            reset: "resetBottomPadding"
            notify: "bottomPaddingChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "spacing"
            type: "double"
            read: "spacing"
            write: "setSpacing"
            reset: "resetSpacing"
            notify: "spacingChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "locale"
            type: "QLocale"
            read: "locale"
            write: "setLocale"
            reset: "resetLocale"
            notify: "localeChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "mirrored"
            type: "bool"
            read: "isMirrored"
            notify: "mirroredChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "focusPolicy"
            type: "Qt::FocusPolicy"
            read: "focusPolicy"
            write: "setFocusPolicy"
            notify: "focusPolicyChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "focusReason"
            type: "Qt::FocusReason"
            read: "focusReason"
            write: "setFocusReason"
            notify: "focusReasonChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "visualFocus"
            type: "bool"
            read: "hasVisualFocus"
            notify: "visualFocusChanged"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hovered"
            type: "bool"
            read: "isHovered"
            notify: "hoveredChanged"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hoverEnabled"
            type: "bool"
            read: "isHoverEnabled"
            write: "setHoverEnabled"
            reset: "resetHoverEnabled"
            notify: "hoverEnabledChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "wheelEnabled"
            type: "bool"
            read: "isWheelEnabled"
            write: "setWheelEnabled"
            notify: "wheelEnabledChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "background"
            type: "QQuickItem"
            isPointer: true
            read: "background"
            write: "setBackground"
            notify: "backgroundChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "contentItem"
            type: "QQuickItem"
            isPointer: true
            read: "contentItem"
            write: "setContentItem"
            notify: "contentItemChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "baselineOffset"
            type: "double"
            read: "baselineOffset"
            write: "setBaselineOffset"
            reset: "resetBaselineOffset"
            notify: "baselineOffsetChanged"
            index: 19
            isFinal: true
        }
        Property {
            name: "horizontalPadding"
            revision: 517
            type: "double"
            read: "horizontalPadding"
            write: "setHorizontalPadding"
            reset: "resetHorizontalPadding"
            notify: "horizontalPaddingChanged"
            index: 20
            isFinal: true
        }
        Property {
            name: "verticalPadding"
            revision: 517
            type: "double"
            read: "verticalPadding"
            write: "setVerticalPadding"
            reset: "resetVerticalPadding"
            notify: "verticalPaddingChanged"
            index: 21
            isFinal: true
        }
        Property {
            name: "implicitContentWidth"
            revision: 517
            type: "double"
            read: "implicitContentWidth"
            notify: "implicitContentWidthChanged"
            index: 22
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitContentHeight"
            revision: 517
            type: "double"
            read: "implicitContentHeight"
            notify: "implicitContentHeightChanged"
            index: 23
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundWidth"
            revision: 517
            type: "double"
            read: "implicitBackgroundWidth"
            notify: "implicitBackgroundWidthChanged"
            index: 24
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundHeight"
            revision: 517
            type: "double"
            read: "implicitBackgroundHeight"
            notify: "implicitBackgroundHeightChanged"
            index: 25
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "topInset"
            revision: 517
            type: "double"
            read: "topInset"
            write: "setTopInset"
            reset: "resetTopInset"
            notify: "topInsetChanged"
            index: 26
            isFinal: true
        }
        Property {
            name: "leftInset"
            revision: 517
            type: "double"
            read: "leftInset"
            write: "setLeftInset"
            reset: "resetLeftInset"
            notify: "leftInsetChanged"
            index: 27
            isFinal: true
        }
        Property {
            name: "rightInset"
            revision: 517
            type: "double"
            read: "rightInset"
            write: "setRightInset"
            reset: "resetRightInset"
            notify: "rightInsetChanged"
            index: 28
            isFinal: true
        }
        Property {
            name: "bottomInset"
            revision: 517
            type: "double"
            read: "bottomInset"
            write: "setBottomInset"
            reset: "resetBottomInset"
            notify: "bottomInsetChanged"
            index: 29
            isFinal: true
        }
        Signal { name: "fontChanged" }
        Signal { name: "availableWidthChanged" }
        Signal { name: "availableHeightChanged" }
        Signal { name: "paddingChanged" }
        Signal { name: "topPaddingChanged" }
        Signal { name: "leftPaddingChanged" }
        Signal { name: "rightPaddingChanged" }
        Signal { name: "bottomPaddingChanged" }
        Signal { name: "spacingChanged" }
        Signal { name: "localeChanged" }
        Signal { name: "focusReasonChanged" }
        Signal { name: "mirroredChanged" }
        Signal { name: "visualFocusChanged" }
        Signal { name: "hoveredChanged" }
        Signal { name: "hoverEnabledChanged" }
        Signal { name: "wheelEnabledChanged" }
        Signal { name: "backgroundChanged" }
        Signal { name: "contentItemChanged" }
        Signal { name: "baselineOffsetChanged" }
        Signal { name: "horizontalPaddingChanged"; revision: 517 }
        Signal { name: "verticalPaddingChanged"; revision: 517 }
        Signal { name: "implicitContentWidthChanged"; revision: 517 }
        Signal { name: "implicitContentHeightChanged"; revision: 517 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 517 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 517 }
        Signal { name: "topInsetChanged"; revision: 517 }
        Signal { name: "leftInsetChanged"; revision: 517 }
        Signal { name: "rightInsetChanged"; revision: 517 }
        Signal { name: "bottomInsetChanged"; revision: 517 }
    }
    Component {
        file: "private/qtquickdialogs2quickimplforeign_p.h"
        name: "QQuickDialog"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickPopup"
        extension: "QPlatformDialogHelper"
        extensionIsNamespace: true
        Enum {
            name: "StandardCode"
            values: ["Rejected", "Accepted"]
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "titleChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "header"
            type: "QQuickItem"
            isPointer: true
            read: "header"
            write: "setHeader"
            notify: "headerChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "footer"
            type: "QQuickItem"
            isPointer: true
            read: "footer"
            write: "setFooter"
            notify: "footerChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "standardButtons"
            type: "QPlatformDialogHelper::StandardButtons"
            read: "standardButtons"
            write: "setStandardButtons"
            notify: "standardButtonsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "result"
            revision: 515
            type: "int"
            read: "result"
            write: "setResult"
            notify: "resultChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "implicitHeaderWidth"
            revision: 517
            type: "double"
            read: "implicitHeaderWidth"
            notify: "implicitHeaderWidthChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitHeaderHeight"
            revision: 517
            type: "double"
            read: "implicitHeaderHeight"
            notify: "implicitHeaderHeightChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitFooterWidth"
            revision: 517
            type: "double"
            read: "implicitFooterWidth"
            notify: "implicitFooterWidthChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitFooterHeight"
            revision: 517
            type: "double"
            read: "implicitFooterHeight"
            notify: "implicitFooterHeightChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Signal { name: "accepted" }
        Signal { name: "rejected" }
        Signal { name: "titleChanged" }
        Signal { name: "headerChanged" }
        Signal { name: "footerChanged" }
        Signal { name: "standardButtonsChanged" }
        Signal { name: "applied"; revision: 515 }
        Signal { name: "reset"; revision: 515 }
        Signal { name: "discarded"; revision: 515 }
        Signal { name: "helpRequested"; revision: 515 }
        Signal { name: "resultChanged"; revision: 515 }
        Signal { name: "implicitHeaderWidthChanged" }
        Signal { name: "implicitHeaderHeightChanged" }
        Signal { name: "implicitFooterWidthChanged" }
        Signal { name: "implicitFooterHeightChanged" }
        Method { name: "accept" }
        Method { name: "reject" }
        Method {
            name: "done"
            Parameter { name: "result"; type: "int" }
        }
        Method {
            name: "standardButton"
            revision: 515
            type: "QQuickAbstractButton"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "button"; type: "QPlatformDialogHelper::StandardButton" }
        }
    }
    Component {
        file: "private/qquickfiledialogdelegate_p.h"
        name: "QQuickFileDialogDelegate"
        accessSemantics: "reference"
        prototype: "QQuickItemDelegate"
        exports: [
            "QtQuick.Dialogs.quickimpl/FileDialogDelegate 6.2",
            "QtQuick.Dialogs.quickimpl/FileDialogDelegate 6.3",
            "QtQuick.Dialogs.quickimpl/FileDialogDelegate 6.7",
            "QtQuick.Dialogs.quickimpl/FileDialogDelegate 6.8"
        ]
        exportMetaObjectRevisions: [1538, 1539, 1543, 1544]
        Property {
            name: "dialog"
            type: "QQuickDialog"
            isPointer: true
            read: "dialog"
            write: "setDialog"
            notify: "dialogChanged"
            index: 0
        }
        Property {
            name: "file"
            type: "QUrl"
            read: "file"
            write: "setFile"
            notify: "fileChanged"
            index: 1
        }
        Signal { name: "dialogChanged" }
        Signal { name: "fileChanged" }
    }
    Component {
        file: "private/qquickfiledialogimpl_p.h"
        name: "QQuickFileDialogImpl"
        accessSemantics: "reference"
        prototype: "QQuickDialog"
        exports: [
            "QtQuick.Dialogs.quickimpl/FileDialogImpl 6.2",
            "QtQuick.Dialogs.quickimpl/FileDialogImpl 6.8",
            "QtQuick.Dialogs.quickimpl/FileDialogImpl 6.9"
        ]
        exportMetaObjectRevisions: [1538, 1544, 1545]
        attachedType: "QQuickFileDialogImplAttached"
        Property {
            name: "currentFolder"
            type: "QUrl"
            read: "currentFolder"
            write: "setCurrentFolder"
            notify: "currentFolderChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "selectedFile"
            type: "QUrl"
            read: "selectedFile"
            write: "setSelectedFile"
            notify: "selectedFileChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "nameFilters"
            type: "QStringList"
            read: "nameFilters"
            notify: "nameFiltersChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "selectedNameFilter"
            type: "QQuickFileNameFilter"
            isPointer: true
            read: "selectedNameFilter"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "fileName"
            type: "QString"
            read: "fileName"
            write: "setFileName"
            notify: "selectedFileChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "currentFolderName"
            type: "QString"
            read: "currentFolderName"
            notify: "selectedFileChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Signal {
            name: "currentFolderChanged"
            Parameter { name: "folderUrl"; type: "QUrl" }
        }
        Signal {
            name: "selectedFileChanged"
            Parameter { name: "selectedFileUrl"; type: "QUrl" }
        }
        Signal { name: "nameFiltersChanged" }
        Signal {
            name: "fileSelected"
            Parameter { name: "fileUrl"; type: "QUrl" }
        }
        Signal {
            name: "filterSelected"
            Parameter { name: "filter"; type: "QString" }
        }
        Method {
            name: "selectNameFilter"
            Parameter { name: "filter"; type: "QString" }
        }
    }
    Component {
        file: "private/qquickfiledialogimpl_p.h"
        name: "QQuickFileDialogImplAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "buttonBox"
            type: "QQuickDialogButtonBox"
            isPointer: true
            read: "buttonBox"
            write: "setButtonBox"
            notify: "buttonBoxChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "nameFiltersComboBox"
            type: "QQuickComboBox"
            isPointer: true
            read: "nameFiltersComboBox"
            write: "setNameFiltersComboBox"
            notify: "nameFiltersComboBoxChanged"
            index: 1
        }
        Property {
            name: "fileDialogListView"
            type: "QQuickListView"
            isPointer: true
            read: "fileDialogListView"
            write: "setFileDialogListView"
            notify: "fileDialogListViewChanged"
            index: 2
        }
        Property {
            name: "breadcrumbBar"
            type: "QQuickFolderBreadcrumbBar"
            isPointer: true
            read: "breadcrumbBar"
            write: "setBreadcrumbBar"
            notify: "breadcrumbBarChanged"
            index: 3
        }
        Property {
            name: "fileNameLabel"
            type: "QQuickLabel"
            isPointer: true
            read: "fileNameLabel"
            write: "setFileNameLabel"
            notify: "fileNameLabelChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "fileNameTextField"
            type: "QQuickTextField"
            isPointer: true
            read: "fileNameTextField"
            write: "setFileNameTextField"
            notify: "fileNameTextFieldChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "overwriteConfirmationDialog"
            type: "QQuickDialog"
            isPointer: true
            read: "overwriteConfirmationDialog"
            write: "setOverwriteConfirmationDialog"
            notify: "overwriteConfirmationDialogChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "sideBar"
            type: "QQuickSideBar"
            isPointer: true
            read: "sideBar"
            write: "setSideBar"
            notify: "sideBarChanged"
            index: 7
            isFinal: true
        }
        Signal { name: "buttonBoxChanged" }
        Signal { name: "nameFiltersComboBoxChanged" }
        Signal { name: "fileDialogListViewChanged" }
        Signal { name: "breadcrumbBarChanged" }
        Signal { name: "fileNameLabelChanged" }
        Signal { name: "fileNameTextFieldChanged" }
        Signal { name: "overwriteConfirmationDialogChanged" }
        Signal { name: "sideBarChanged"; revision: 1545 }
    }
    Component {
        file: "private/qtquickdialogs2quickimplforeign_p.h"
        name: "QQuickFileNameFilter"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "index"
            type: "int"
            read: "index"
            write: "setIndex"
            notify: "indexChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            notify: "nameChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "extensions"
            type: "QStringList"
            read: "extensions"
            notify: "extensionsChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "globs"
            type: "QStringList"
            read: "globs"
            notify: "globsChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Signal {
            name: "indexChanged"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "extensionsChanged"
            Parameter { name: "extensions"; type: "QStringList" }
        }
        Signal {
            name: "globsChanged"
            Parameter { name: "globs"; type: "QStringList" }
        }
    }
    Component {
        file: "private/qquickfolderbreadcrumbbar_p.h"
        name: "QQuickFolderBreadcrumbBar"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: [
            "QtQuick.Dialogs.quickimpl/FolderBreadcrumbBar 6.2",
            "QtQuick.Dialogs.quickimpl/FolderBreadcrumbBar 6.3",
            "QtQuick.Dialogs.quickimpl/FolderBreadcrumbBar 6.7"
        ]
        exportMetaObjectRevisions: [1538, 1539, 1543]
        Property {
            name: "dialog"
            type: "QQuickDialog"
            isPointer: true
            read: "dialog"
            write: "setDialog"
            notify: "dialogChanged"
            index: 0
        }
        Property {
            name: "buttonDelegate"
            type: "QQmlComponent"
            isPointer: true
            read: "buttonDelegate"
            write: "setButtonDelegate"
            notify: "buttonDelegateChanged"
            index: 1
        }
        Property {
            name: "separatorDelegate"
            type: "QQmlComponent"
            isPointer: true
            read: "separatorDelegate"
            write: "setSeparatorDelegate"
            notify: "separatorDelegateChanged"
            index: 2
        }
        Property {
            name: "upButton"
            type: "QQuickAbstractButton"
            isPointer: true
            read: "upButton"
            write: "setUpButton"
            notify: "upButtonChanged"
            index: 3
        }
        Property {
            name: "textField"
            type: "QQuickTextField"
            isPointer: true
            read: "textField"
            write: "setTextField"
            notify: "textFieldChanged"
            index: 4
        }
        Property {
            name: "upButtonSpacing"
            type: "int"
            read: "upButtonSpacing"
            write: "setUpButtonSpacing"
            notify: "upButtonSpacingChanged"
            index: 5
        }
        Signal { name: "dialogChanged" }
        Signal { name: "buttonDelegateChanged" }
        Signal { name: "separatorDelegateChanged" }
        Signal { name: "upButtonChanged" }
        Signal { name: "upButtonSpacingChanged" }
        Signal { name: "textFieldChanged" }
    }
    Component {
        file: "private/qquickfolderdialogimpl_p.h"
        name: "QQuickFolderDialogImpl"
        accessSemantics: "reference"
        prototype: "QQuickDialog"
        exports: [
            "QtQuick.Dialogs.quickimpl/FolderDialogImpl 6.3",
            "QtQuick.Dialogs.quickimpl/FolderDialogImpl 6.8"
        ]
        exportMetaObjectRevisions: [1539, 1544]
        attachedType: "QQuickFolderDialogImplAttached"
        Property {
            name: "currentFolder"
            type: "QUrl"
            read: "currentFolder"
            write: "setCurrentFolder"
            notify: "currentFolderChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "selectedFolder"
            type: "QUrl"
            read: "selectedFolder"
            write: "setSelectedFolder"
            notify: "selectedFolderChanged"
            index: 1
            isFinal: true
        }
        Signal {
            name: "currentFolderChanged"
            Parameter { name: "folderUrl"; type: "QUrl" }
        }
        Signal {
            name: "selectedFolderChanged"
            Parameter { name: "folderUrl"; type: "QUrl" }
        }
        Signal { name: "nameFiltersChanged" }
    }
    Component {
        file: "private/qquickfolderdialogimpl_p.h"
        name: "QQuickFolderDialogImplAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "folderDialogListView"
            type: "QQuickListView"
            isPointer: true
            read: "folderDialogListView"
            write: "setFolderDialogListView"
            notify: "folderDialogListViewChanged"
            index: 0
        }
        Property {
            name: "breadcrumbBar"
            type: "QQuickFolderBreadcrumbBar"
            isPointer: true
            read: "breadcrumbBar"
            write: "setBreadcrumbBar"
            notify: "breadcrumbBarChanged"
            index: 1
        }
        Signal { name: "folderDialogListViewChanged" }
        Signal { name: "breadcrumbBarChanged" }
    }
    Component {
        file: "private/qquickfontdialogimpl_p.h"
        name: "QQuickFontDialogImpl"
        accessSemantics: "reference"
        prototype: "QQuickDialog"
        exports: [
            "QtQuick.Dialogs.quickimpl/FontDialogImpl 6.2",
            "QtQuick.Dialogs.quickimpl/FontDialogImpl 6.8"
        ]
        exportMetaObjectRevisions: [1538, 1544]
        attachedType: "QQuickFontDialogImplAttached"
        Property {
            name: "currentFont"
            type: "QFont"
            read: "currentFont"
            write: "setCurrentFont"
            notify: "currentFontChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "optionsChanged" }
        Signal {
            name: "currentFontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
    }
    Component {
        file: "private/qquickfontdialogimpl_p.h"
        name: "QQuickFontDialogImplAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "familyListView"
            type: "QQuickListView"
            isPointer: true
            read: "familyListView"
            write: "setFamilyListView"
            notify: "familyListViewChanged"
            index: 0
        }
        Property {
            name: "styleListView"
            type: "QQuickListView"
            isPointer: true
            read: "styleListView"
            write: "setStyleListView"
            notify: "styleListViewChanged"
            index: 1
        }
        Property {
            name: "sizeListView"
            type: "QQuickListView"
            isPointer: true
            read: "sizeListView"
            write: "setSizeListView"
            notify: "sizeListViewChanged"
            index: 2
        }
        Property {
            name: "sampleEdit"
            type: "QQuickTextEdit"
            isPointer: true
            read: "sampleEdit"
            write: "setSampleEdit"
            notify: "sampleEditChanged"
            index: 3
        }
        Property {
            name: "buttonBox"
            type: "QQuickDialogButtonBox"
            isPointer: true
            read: "buttonBox"
            write: "setButtonBox"
            notify: "buttonBoxChanged"
            index: 4
        }
        Property {
            name: "writingSystemComboBox"
            type: "QQuickComboBox"
            isPointer: true
            read: "writingSystemComboBox"
            write: "setWritingSystemComboBox"
            notify: "writingSystemComboBoxChanged"
            index: 5
        }
        Property {
            name: "underlineCheckBox"
            type: "QQuickCheckBox"
            isPointer: true
            read: "underlineCheckBox"
            write: "setUnderlineCheckBox"
            notify: "underlineCheckBoxChanged"
            index: 6
        }
        Property {
            name: "strikeoutCheckBox"
            type: "QQuickCheckBox"
            isPointer: true
            read: "strikeoutCheckBox"
            write: "setStrikeoutCheckBox"
            notify: "strikeoutCheckBoxChanged"
            index: 7
        }
        Property {
            name: "familyEdit"
            type: "QQuickTextField"
            isPointer: true
            read: "familyEdit"
            write: "setFamilyEdit"
            notify: "familyEditChanged"
            index: 8
        }
        Property {
            name: "styleEdit"
            type: "QQuickTextField"
            isPointer: true
            read: "styleEdit"
            write: "setStyleEdit"
            notify: "styleEditChanged"
            index: 9
        }
        Property {
            name: "sizeEdit"
            type: "QQuickTextField"
            isPointer: true
            read: "sizeEdit"
            write: "setSizeEdit"
            notify: "sizeEditChanged"
            index: 10
        }
        Signal { name: "buttonBoxChanged" }
        Signal { name: "familyListViewChanged" }
        Signal { name: "styleListViewChanged" }
        Signal { name: "sizeListViewChanged" }
        Signal { name: "sampleEditChanged" }
        Signal { name: "writingSystemComboBoxChanged" }
        Signal { name: "underlineCheckBoxChanged" }
        Signal { name: "strikeoutCheckBoxChanged" }
        Signal { name: "familyEditChanged" }
        Signal { name: "styleEditChanged" }
        Signal { name: "sizeEditChanged" }
    }
    Component {
        file: "private/qtquickdialogs2quickimplforeign_p.h"
        name: "QQuickIcon"
        accessSemantics: "value"
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            reset: "resetName"
            index: 0
            isFinal: true
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            reset: "resetSource"
            index: 1
            isFinal: true
        }
        Property {
            name: "width"
            type: "int"
            read: "width"
            write: "setWidth"
            reset: "resetWidth"
            index: 2
            isFinal: true
        }
        Property {
            name: "height"
            type: "int"
            read: "height"
            write: "setHeight"
            reset: "resetHeight"
            index: 3
            isFinal: true
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            reset: "resetColor"
            index: 4
            isFinal: true
        }
        Property {
            name: "cache"
            type: "bool"
            read: "cache"
            write: "setCache"
            reset: "resetCache"
            index: 5
            isFinal: true
        }
    }
    Component {
        file: "private/qquickmessagedialogimpl_p.h"
        name: "QQuickMessageDialogImpl"
        accessSemantics: "reference"
        prototype: "QQuickDialog"
        exports: [
            "QtQuick.Dialogs.quickimpl/MessageDialogImpl 6.3",
            "QtQuick.Dialogs.quickimpl/MessageDialogImpl 6.8"
        ]
        exportMetaObjectRevisions: [1539, 1544]
        attachedType: "QQuickMessageDialogImplAttached"
        Property {
            name: "text"
            type: "QString"
            read: "text"
            notify: "optionsChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "informativeText"
            type: "QString"
            read: "informativeText"
            notify: "optionsChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "detailedText"
            type: "QString"
            read: "detailedText"
            notify: "optionsChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "showDetailedText"
            type: "bool"
            read: "showDetailedText"
            notify: "showDetailedTextChanged"
            index: 3
            isReadonly: true
        }
        Signal {
            name: "buttonClicked"
            Parameter { name: "button"; type: "QPlatformDialogHelper::StandardButton" }
            Parameter { name: "role"; type: "QPlatformDialogHelper::ButtonRole" }
        }
        Signal { name: "showDetailedTextChanged" }
        Signal { name: "optionsChanged" }
        Method { name: "toggleShowDetailedText" }
    }
    Component {
        file: "private/qquickmessagedialogimpl_p.h"
        name: "QQuickMessageDialogImplAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "buttonBox"
            type: "QQuickDialogButtonBox"
            isPointer: true
            read: "buttonBox"
            write: "setButtonBox"
            notify: "buttonBoxChanged"
            index: 0
        }
        Property {
            name: "detailedTextButton"
            type: "QQuickButton"
            isPointer: true
            read: "detailedTextButton"
            write: "setDetailedTextButton"
            notify: "detailedTextButtonChanged"
            index: 1
        }
        Signal { name: "buttonBoxChanged" }
        Signal { name: "detailedTextButtonChanged" }
    }
    Component {
        file: "private/qtquickdialogs2quickimplforeign_p.h"
        name: "QQuickPopup"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus", "QQuickSafeAreaAttachable"]
        Enum {
            name: "ClosePolicy"
            alias: "ClosePolicyFlag"
            isFlag: true
            values: [
                "NoAutoClose",
                "CloseOnPressOutside",
                "CloseOnPressOutsideParent",
                "CloseOnReleaseOutside",
                "CloseOnReleaseOutsideParent",
                "CloseOnEscape"
            ]
        }
        Enum {
            name: "TransformOrigin"
            values: [
                "TopLeft",
                "Top",
                "TopRight",
                "Left",
                "Center",
                "Right",
                "BottomLeft",
                "Bottom",
                "BottomRight"
            ]
        }
        Enum {
            name: "PopupType"
            values: ["Item", "Window", "Native"]
        }
        Property {
            name: "x"
            type: "double"
            read: "x"
            write: "setX"
            notify: "xChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "y"
            type: "double"
            read: "y"
            write: "setY"
            notify: "yChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "z"
            type: "double"
            read: "z"
            write: "setZ"
            reset: "resetZ"
            notify: "zChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "width"
            type: "double"
            read: "width"
            write: "setWidth"
            reset: "resetWidth"
            notify: "widthChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "height"
            type: "double"
            read: "height"
            write: "setHeight"
            reset: "resetHeight"
            notify: "heightChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "implicitWidth"
            type: "double"
            read: "implicitWidth"
            write: "setImplicitWidth"
            notify: "implicitWidthChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "implicitHeight"
            type: "double"
            read: "implicitHeight"
            write: "setImplicitHeight"
            notify: "implicitHeightChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "contentWidth"
            type: "double"
            read: "contentWidth"
            write: "setContentWidth"
            notify: "contentWidthChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "contentHeight"
            type: "double"
            read: "contentHeight"
            write: "setContentHeight"
            notify: "contentHeightChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "availableWidth"
            type: "double"
            read: "availableWidth"
            notify: "availableWidthChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "availableHeight"
            type: "double"
            read: "availableHeight"
            notify: "availableHeightChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "margins"
            type: "double"
            read: "margins"
            write: "setMargins"
            reset: "resetMargins"
            notify: "marginsChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "topMargin"
            type: "double"
            read: "topMargin"
            write: "setTopMargin"
            reset: "resetTopMargin"
            notify: "topMarginChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "leftMargin"
            type: "double"
            read: "leftMargin"
            write: "setLeftMargin"
            reset: "resetLeftMargin"
            notify: "leftMarginChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "rightMargin"
            type: "double"
            read: "rightMargin"
            write: "setRightMargin"
            reset: "resetRightMargin"
            notify: "rightMarginChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "bottomMargin"
            type: "double"
            read: "bottomMargin"
            write: "setBottomMargin"
            reset: "resetBottomMargin"
            notify: "bottomMarginChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "padding"
            type: "double"
            read: "padding"
            write: "setPadding"
            reset: "resetPadding"
            notify: "paddingChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "topPadding"
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            reset: "resetTopPadding"
            notify: "topPaddingChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "leftPadding"
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            reset: "resetLeftPadding"
            notify: "leftPaddingChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "rightPadding"
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            reset: "resetRightPadding"
            notify: "rightPaddingChanged"
            index: 19
            isFinal: true
        }
        Property {
            name: "bottomPadding"
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            reset: "resetBottomPadding"
            notify: "bottomPaddingChanged"
            index: 20
            isFinal: true
        }
        Property {
            name: "locale"
            type: "QLocale"
            read: "locale"
            write: "setLocale"
            reset: "resetLocale"
            notify: "localeChanged"
            index: 21
            isFinal: true
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            reset: "resetFont"
            notify: "fontChanged"
            index: 22
            isFinal: true
        }
        Property {
            name: "parent"
            type: "QQuickItem"
            isPointer: true
            read: "parentItem"
            write: "setParentItem"
            reset: "resetParentItem"
            notify: "parentChanged"
            index: 23
            isFinal: true
        }
        Property {
            name: "background"
            type: "QQuickItem"
            isPointer: true
            read: "background"
            write: "setBackground"
            notify: "backgroundChanged"
            index: 24
            isFinal: true
        }
        Property {
            name: "contentItem"
            type: "QQuickItem"
            isPointer: true
            read: "contentItem"
            write: "setContentItem"
            notify: "contentItemChanged"
            index: 25
            isFinal: true
        }
        Property {
            name: "contentData"
            type: "QObject"
            isList: true
            read: "contentData"
            index: 26
            privateClass: "QQuickPopupPrivate"
            isReadonly: true
        }
        Property {
            name: "contentChildren"
            type: "QQuickItem"
            isList: true
            read: "contentChildren"
            notify: "contentChildrenChanged"
            index: 27
            privateClass: "QQuickPopupPrivate"
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "clip"
            type: "bool"
            read: "clip"
            write: "setClip"
            notify: "clipChanged"
            index: 28
            isFinal: true
        }
        Property {
            name: "focus"
            type: "bool"
            read: "hasFocus"
            write: "setFocus"
            notify: "focusChanged"
            index: 29
            isFinal: true
        }
        Property {
            name: "activeFocus"
            type: "bool"
            read: "hasActiveFocus"
            notify: "activeFocusChanged"
            index: 30
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "modal"
            type: "bool"
            read: "isModal"
            write: "setModal"
            notify: "modalChanged"
            index: 31
            isFinal: true
        }
        Property {
            name: "dim"
            type: "bool"
            read: "dim"
            write: "setDim"
            reset: "resetDim"
            notify: "dimChanged"
            index: 32
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 33
            isFinal: true
        }
        Property {
            name: "opacity"
            type: "double"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 34
            isFinal: true
        }
        Property {
            name: "scale"
            type: "double"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 35
            isFinal: true
        }
        Property {
            name: "closePolicy"
            type: "ClosePolicy"
            read: "closePolicy"
            write: "setClosePolicy"
            reset: "resetClosePolicy"
            notify: "closePolicyChanged"
            index: 36
            isFinal: true
        }
        Property {
            name: "transformOrigin"
            type: "TransformOrigin"
            read: "transformOrigin"
            write: "setTransformOrigin"
            index: 37
            isFinal: true
        }
        Property {
            name: "enter"
            type: "QQuickTransition"
            isPointer: true
            read: "enter"
            write: "setEnter"
            notify: "enterChanged"
            index: 38
            isFinal: true
        }
        Property {
            name: "exit"
            type: "QQuickTransition"
            isPointer: true
            read: "exit"
            write: "setExit"
            notify: "exitChanged"
            index: 39
            isFinal: true
        }
        Property {
            name: "spacing"
            revision: 513
            type: "double"
            read: "spacing"
            write: "setSpacing"
            reset: "resetSpacing"
            notify: "spacingChanged"
            index: 40
            isFinal: true
        }
        Property {
            name: "opened"
            revision: 515
            type: "bool"
            read: "isOpened"
            notify: "openedChanged"
            index: 41
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "mirrored"
            revision: 515
            type: "bool"
            read: "isMirrored"
            notify: "mirroredChanged"
            index: 42
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "enabled"
            revision: 515
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 43
            isFinal: true
        }
        Property {
            name: "palette"
            revision: 515
            type: "QQuickPalette"
            isPointer: true
            read: "palette"
            write: "setPalette"
            reset: "resetPalette"
            notify: "paletteChanged"
            index: 44
            privateClass: "QQuickPopupPrivate"
        }
        Property {
            name: "horizontalPadding"
            type: "double"
            read: "horizontalPadding"
            write: "setHorizontalPadding"
            reset: "resetHorizontalPadding"
            notify: "horizontalPaddingChanged"
            index: 45
            isFinal: true
        }
        Property {
            name: "verticalPadding"
            type: "double"
            read: "verticalPadding"
            write: "setVerticalPadding"
            reset: "resetVerticalPadding"
            notify: "verticalPaddingChanged"
            index: 46
            isFinal: true
        }
        Property {
            name: "anchors"
            revision: 517
            type: "QQuickPopupAnchors"
            isPointer: true
            read: "getAnchors"
            index: 47
            privateClass: "QQuickPopupPrivate"
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "implicitContentWidth"
            revision: 517
            type: "double"
            read: "implicitContentWidth"
            notify: "implicitContentWidthChanged"
            index: 48
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitContentHeight"
            revision: 517
            type: "double"
            read: "implicitContentHeight"
            notify: "implicitContentHeightChanged"
            index: 49
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundWidth"
            revision: 517
            type: "double"
            read: "implicitBackgroundWidth"
            notify: "implicitBackgroundWidthChanged"
            index: 50
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundHeight"
            revision: 517
            type: "double"
            read: "implicitBackgroundHeight"
            notify: "implicitBackgroundHeightChanged"
            index: 51
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "topInset"
            revision: 517
            type: "double"
            read: "topInset"
            write: "setTopInset"
            reset: "resetTopInset"
            notify: "topInsetChanged"
            index: 52
            isFinal: true
        }
        Property {
            name: "leftInset"
            revision: 517
            type: "double"
            read: "leftInset"
            write: "setLeftInset"
            reset: "resetLeftInset"
            notify: "leftInsetChanged"
            index: 53
            isFinal: true
        }
        Property {
            name: "rightInset"
            revision: 517
            type: "double"
            read: "rightInset"
            write: "setRightInset"
            reset: "resetRightInset"
            notify: "rightInsetChanged"
            index: 54
            isFinal: true
        }
        Property {
            name: "bottomInset"
            revision: 517
            type: "double"
            read: "bottomInset"
            write: "setBottomInset"
            reset: "resetBottomInset"
            notify: "bottomInsetChanged"
            index: 55
            isFinal: true
        }
        Property {
            name: "popupType"
            revision: 1544
            type: "PopupType"
            read: "popupType"
            write: "setPopupType"
            notify: "popupTypeChanged"
            index: 56
            isFinal: true
        }
        Signal { name: "opened" }
        Signal { name: "closed" }
        Signal { name: "aboutToShow" }
        Signal { name: "aboutToHide" }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "zChanged" }
        Signal { name: "widthChanged" }
        Signal { name: "heightChanged" }
        Signal { name: "implicitWidthChanged" }
        Signal { name: "implicitHeightChanged" }
        Signal { name: "contentWidthChanged" }
        Signal { name: "contentHeightChanged" }
        Signal { name: "availableWidthChanged" }
        Signal { name: "availableHeightChanged" }
        Signal { name: "marginsChanged" }
        Signal { name: "topMarginChanged" }
        Signal { name: "leftMarginChanged" }
        Signal { name: "rightMarginChanged" }
        Signal { name: "bottomMarginChanged" }
        Signal { name: "paddingChanged" }
        Signal { name: "topPaddingChanged" }
        Signal { name: "leftPaddingChanged" }
        Signal { name: "rightPaddingChanged" }
        Signal { name: "bottomPaddingChanged" }
        Signal { name: "fontChanged" }
        Signal { name: "localeChanged" }
        Signal { name: "parentChanged" }
        Signal { name: "backgroundChanged" }
        Signal { name: "contentItemChanged" }
        Signal { name: "contentChildrenChanged" }
        Signal { name: "clipChanged" }
        Signal { name: "focusChanged" }
        Signal { name: "activeFocusChanged" }
        Signal { name: "modalChanged" }
        Signal { name: "dimChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "opacityChanged" }
        Signal { name: "scaleChanged" }
        Signal { name: "closePolicyChanged" }
        Signal { name: "enterChanged" }
        Signal { name: "exitChanged" }
        Signal {
            name: "windowChanged"
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Signal { name: "spacingChanged"; revision: 513 }
        Signal { name: "openedChanged"; revision: 515 }
        Signal { name: "mirroredChanged"; revision: 515 }
        Signal { name: "enabledChanged"; revision: 515 }
        Signal { name: "paletteChanged"; revision: 515 }
        Signal { name: "paletteCreated"; revision: 515 }
        Signal { name: "horizontalPaddingChanged"; revision: 517 }
        Signal { name: "verticalPaddingChanged"; revision: 517 }
        Signal { name: "implicitContentWidthChanged"; revision: 517 }
        Signal { name: "implicitContentHeightChanged"; revision: 517 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 517 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 517 }
        Signal { name: "topInsetChanged"; revision: 517 }
        Signal { name: "leftInsetChanged"; revision: 517 }
        Signal { name: "rightInsetChanged"; revision: 517 }
        Signal { name: "bottomInsetChanged"; revision: 517 }
        Signal { name: "popupTypeChanged"; revision: 1544 }
        Method { name: "open" }
        Method { name: "close" }
        Method {
            name: "forceActiveFocus"
            Parameter { name: "reason"; type: "Qt::FocusReason" }
        }
        Method { name: "forceActiveFocus"; isCloned: true }
    }
    Component {
        file: "private/qquicksaturationlightnesspicker_p.h"
        name: "QQuickSaturationLightnessPicker"
        accessSemantics: "reference"
        prototype: "QQuickAbstractColorPicker"
        exports: [
            "QtQuick.Dialogs.quickimpl/SaturationLightnessPickerImpl 6.0",
            "QtQuick.Dialogs.quickimpl/SaturationLightnessPickerImpl 6.3",
            "QtQuick.Dialogs.quickimpl/SaturationLightnessPickerImpl 6.4",
            "QtQuick.Dialogs.quickimpl/SaturationLightnessPickerImpl 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1539, 1540, 1543]
    }
    Component {
        file: "private/qquicksidebar_p.h"
        name: "QQuickSideBar"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: ["QtQuick.Dialogs.quickimpl/SideBar 6.9"]
        exportMetaObjectRevisions: [1545]
        Property {
            name: "dialog"
            type: "QQuickDialog"
            isPointer: true
            read: "dialog"
            write: "setDialog"
            notify: "dialogChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "folderPaths"
            type: "QStandardPaths::StandardLocation"
            isList: true
            read: "folderPaths"
            write: "setFolderPaths"
            notify: "folderPathsChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "effectiveFolderPaths"
            type: "QStandardPaths::StandardLocation"
            isList: true
            read: "effectiveFolderPaths"
            notify: "effectiveFolderPathsChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "favoritePaths"
            type: "QUrl"
            isList: true
            read: "favoritePaths"
            notify: "favoritePathsChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "buttonDelegate"
            type: "QQmlComponent"
            isPointer: true
            read: "buttonDelegate"
            write: "setButtonDelegate"
            notify: "buttonDelegateChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "separatorDelegate"
            type: "QQmlComponent"
            isPointer: true
            read: "separatorDelegate"
            write: "setSeparatorDelegate"
            notify: "separatorDelegateChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "addFavoriteDelegate"
            type: "QQmlComponent"
            isPointer: true
            read: "addFavoriteDelegate"
            write: "setAddFavoriteDelegate"
            notify: "addFavoriteDelegateChanged"
            index: 6
            isFinal: true
        }
        Signal { name: "dialogChanged" }
        Signal { name: "folderPathsChanged" }
        Signal { name: "effectiveFolderPathsChanged" }
        Signal { name: "favoritePathsChanged" }
        Signal { name: "buttonDelegateChanged" }
        Signal { name: "separatorDelegateChanged" }
        Signal { name: "addFavoriteDelegateChanged" }
    }
}
