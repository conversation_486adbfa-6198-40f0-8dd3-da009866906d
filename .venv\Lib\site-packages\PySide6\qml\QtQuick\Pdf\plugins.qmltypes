import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qpdfbookmarkmodel.h"
        name: "QPdfBookmarkModel"
        accessSemantics: "reference"
        prototype: "QAbstractItemModel"
        Enum {
            name: "Role"
            isScoped: true
            type: "int"
            values: ["Title", "Level", "Page", "Location", "Zoom", "NRoles"]
        }
        Property {
            name: "document"
            type: "QPdfDocument"
            isPointer: true
            read: "document"
            write: "setDocument"
            notify: "documentChanged"
            index: 0
        }
        Signal {
            name: "documentChanged"
            Parameter { name: "document"; type: "QPdfDocument"; isPointer: true }
        }
        Method { name: "_q_documentStatusChanged" }
    }
    Component {
        file: "qpdfdocument.h"
        name: "QPdfDocument"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "Status"
            isScoped: true
            values: ["Null", "Loading", "Ready", "Unloading", "Error"]
        }
        Enum {
            name: "Error"
            isScoped: true
            values: [
                "None",
                "Unknown",
                "DataNotYetAvailable",
                "FileNotFound",
                "InvalidFileFormat",
                "IncorrectPassword",
                "UnsupportedSecurityScheme"
            ]
        }
        Enum {
            name: "MetaDataField"
            isScoped: true
            values: [
                "Title",
                "Subject",
                "Author",
                "Keywords",
                "Producer",
                "Creator",
                "CreationDate",
                "ModificationDate"
            ]
        }
        Enum {
            name: "PageModelRole"
            isScoped: true
            values: ["Label", "PointSize", "NRoles"]
        }
        Property {
            name: "pageCount"
            type: "int"
            read: "pageCount"
            notify: "pageCountChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "password"
            type: "QString"
            read: "password"
            write: "setPassword"
            notify: "passwordChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "pageModel"
            type: "QAbstractListModel"
            isPointer: true
            read: "pageModel"
            notify: "pageModelChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Signal { name: "passwordChanged" }
        Signal { name: "passwordRequired" }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "QPdfDocument::Status" }
        }
        Signal {
            name: "pageCountChanged"
            Parameter { name: "pageCount"; type: "int" }
        }
        Signal { name: "pageModelChanged" }
        Method { name: "_q_tryLoadingWithSizeFromContentHeader" }
        Method { name: "_q_copyFromSequentialSourceDevice" }
        Method {
            name: "pagePointSize"
            type: "QSizeF"
            isMethodConstant: true
            Parameter { name: "page"; type: "int" }
        }
        Method {
            name: "pageLabel"
            type: "QString"
            Parameter { name: "page"; type: "int" }
        }
        Method {
            name: "pageIndexForLabel"
            type: "int"
            Parameter { name: "label"; type: "QString" }
        }
        Method {
            name: "getSelection"
            type: "QPdfSelection"
            Parameter { name: "page"; type: "int" }
            Parameter { name: "start"; type: "QPointF" }
            Parameter { name: "end"; type: "QPointF" }
        }
        Method {
            name: "getSelectionAtIndex"
            type: "QPdfSelection"
            Parameter { name: "page"; type: "int" }
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "maxLength"; type: "int" }
        }
        Method {
            name: "getAllText"
            type: "QPdfSelection"
            Parameter { name: "page"; type: "int" }
        }
    }
    Component {
        file: "private/qquickpdfpagenavigator_p.h"
        name: "QPdfLink"
        accessSemantics: "value"
        exports: ["QtQuick.Pdf/pdfLink 6.4"]
        isCreatable: false
        exportMetaObjectRevisions: [1540]
        Property { name: "valid"; type: "bool"; read: "isValid"; index: 0; isReadonly: true }
        Property { name: "page"; type: "int"; read: "page"; index: 1; isReadonly: true }
        Property { name: "location"; type: "QPointF"; read: "location"; index: 2; isReadonly: true }
        Property { name: "zoom"; type: "double"; read: "zoom"; index: 3; isReadonly: true }
        Property { name: "url"; type: "QUrl"; read: "url"; index: 4; isReadonly: true }
        Property {
            name: "contextBefore"
            type: "QString"
            read: "contextBefore"
            index: 5
            isReadonly: true
        }
        Property { name: "contextAfter"; type: "QString"; read: "contextAfter"; index: 6; isReadonly: true }
        Property {
            name: "rectangles"
            type: "QRectF"
            isList: true
            read: "rectangles"
            index: 7
            isReadonly: true
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "copyToClipboard"
            isMethodConstant: true
            Parameter { name: "mode"; type: "QClipboard::Mode" }
        }
        Method { name: "copyToClipboard"; isCloned: true; isMethodConstant: true }
    }
    Component {
        file: "qpdflinkmodel.h"
        name: "QPdfLinkModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        Enum {
            name: "Role"
            isScoped: true
            values: [
                "Link",
                "Rectangle",
                "Url",
                "Page",
                "Location",
                "Zoom",
                "NRoles"
            ]
        }
        Property {
            name: "document"
            type: "QPdfDocument"
            isPointer: true
            read: "document"
            write: "setDocument"
            notify: "documentChanged"
            index: 0
        }
        Property {
            name: "page"
            type: "int"
            read: "page"
            write: "setPage"
            notify: "pageChanged"
            index: 1
        }
        Signal { name: "documentChanged" }
        Signal {
            name: "pageChanged"
            Parameter { name: "page"; type: "int" }
        }
        Method {
            name: "setDocument"
            Parameter { name: "document"; type: "QPdfDocument"; isPointer: true }
        }
        Method {
            name: "setPage"
            Parameter { name: "page"; type: "int" }
        }
        Method {
            name: "onStatusChanged"
            Parameter { name: "status"; type: "QPdfDocument::Status" }
        }
    }
    Component {
        file: "qpdfpagenavigator.h"
        name: "QPdfPageNavigator"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "currentPage"
            type: "int"
            read: "currentPage"
            notify: "currentPageChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "currentLocation"
            type: "QPointF"
            read: "currentLocation"
            notify: "currentLocationChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "currentZoom"
            type: "double"
            read: "currentZoom"
            notify: "currentZoomChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "backAvailable"
            type: "bool"
            read: "backAvailable"
            notify: "backAvailableChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "forwardAvailable"
            type: "bool"
            read: "forwardAvailable"
            notify: "forwardAvailableChanged"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "currentPageChanged"
            Parameter { name: "page"; type: "int" }
        }
        Signal {
            name: "currentLocationChanged"
            Parameter { name: "location"; type: "QPointF" }
        }
        Signal {
            name: "currentZoomChanged"
            Parameter { name: "zoom"; type: "double" }
        }
        Signal {
            name: "backAvailableChanged"
            Parameter { name: "available"; type: "bool" }
        }
        Signal {
            name: "forwardAvailableChanged"
            Parameter { name: "available"; type: "bool" }
        }
        Signal {
            name: "jumped"
            Parameter { name: "current"; type: "QPdfLink" }
        }
        Method { name: "clear" }
        Method {
            name: "jump"
            Parameter { name: "destination"; type: "QPdfLink" }
        }
        Method {
            name: "jump"
            Parameter { name: "page"; type: "int" }
            Parameter { name: "location"; type: "QPointF" }
            Parameter { name: "zoom"; type: "double" }
        }
        Method {
            name: "jump"
            isCloned: true
            Parameter { name: "page"; type: "int" }
            Parameter { name: "location"; type: "QPointF" }
        }
        Method {
            name: "update"
            Parameter { name: "page"; type: "int" }
            Parameter { name: "location"; type: "QPointF" }
            Parameter { name: "zoom"; type: "double" }
        }
        Method { name: "forward" }
        Method { name: "back" }
    }
    Component {
        file: "qpdfsearchmodel.h"
        name: "QPdfSearchModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        Enum {
            name: "Role"
            isScoped: true
            type: "int"
            values: [
                "Page",
                "IndexOnPage",
                "Location",
                "ContextBefore",
                "ContextAfter",
                "NRoles"
            ]
        }
        Property {
            name: "document"
            type: "QPdfDocument"
            isPointer: true
            read: "document"
            write: "setDocument"
            notify: "documentChanged"
            index: 0
        }
        Property {
            name: "searchString"
            type: "QString"
            read: "searchString"
            write: "setSearchString"
            notify: "searchStringChanged"
            index: 1
        }
        Property {
            name: "count"
            revision: 1544
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Signal { name: "documentChanged" }
        Signal { name: "searchStringChanged" }
        Signal { name: "countChanged"; revision: 1544 }
        Method {
            name: "setSearchString"
            Parameter { name: "searchString"; type: "QString" }
        }
        Method {
            name: "setDocument"
            Parameter { name: "document"; type: "QPdfDocument"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickpdfbookmarkmodel_p.h"
        name: "QQuickPdfBookmarkModel"
        accessSemantics: "reference"
        prototype: "QPdfBookmarkModel"
        exports: ["QtQuick.Pdf/PdfBookmarkModel 6.4"]
        exportMetaObjectRevisions: [1540]
        Property {
            name: "document"
            type: "QQuickPdfDocument"
            isPointer: true
            read: "document"
            write: "setDocument"
            notify: "documentChanged"
            index: 0
        }
        Signal { name: "documentChanged" }
    }
    Component {
        file: "private/qquickpdfdocument_p.h"
        name: "QQuickPdfDocument"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QPdfDocument"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick.Pdf/PdfDocument 5.15",
            "QtQuick.Pdf/PdfDocument 6.0"
        ]
        exportMetaObjectRevisions: [1295, 1536]
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "maxPageWidth"
            type: "double"
            read: "maxPageWidth"
            notify: "metaDataChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "maxPageHeight"
            type: "double"
            read: "maxPageHeight"
            notify: "metaDataChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "error"
            type: "QString"
            read: "error"
            notify: "errorChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            notify: "metaDataChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "subject"
            type: "QString"
            read: "subject"
            notify: "metaDataChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "author"
            type: "QString"
            read: "author"
            notify: "metaDataChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "keywords"
            type: "QString"
            read: "keywords"
            notify: "metaDataChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "producer"
            type: "QString"
            read: "producer"
            notify: "metaDataChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "creator"
            type: "QString"
            read: "creator"
            notify: "metaDataChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "creationDate"
            type: "QDateTime"
            read: "creationDate"
            notify: "metaDataChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "modificationDate"
            type: "QDateTime"
            read: "modificationDate"
            notify: "metaDataChanged"
            index: 11
            isReadonly: true
            isFinal: true
        }
        Signal { name: "sourceChanged" }
        Signal { name: "errorChanged" }
        Signal { name: "metaDataChanged" }
    }
    Component {
        file: "private/qquickpdflinkmodel_p.h"
        name: "QQuickPdfLinkModel"
        accessSemantics: "reference"
        prototype: "QPdfLinkModel"
        exports: [
            "QtQuick.Pdf/PdfLinkModel 5.15",
            "QtQuick.Pdf/PdfLinkModel 6.0",
            "QtQuick.Pdf/PdfLinkModel 6.4"
        ]
        exportMetaObjectRevisions: [1295, 1536, 1540]
        Property {
            name: "document"
            type: "QQuickPdfDocument"
            isPointer: true
            read: "document"
            write: "setDocument"
            notify: "documentChanged"
            index: 0
        }
    }
    Component {
        file: "private/qquickpdfpageimage_p.h"
        name: "QQuickPdfPageImage"
        accessSemantics: "reference"
        prototype: "QQuickImage"
        exports: [
            "QtQuick.Pdf/PdfPageImage 6.4",
            "QtQuick.Pdf/PdfPageImage 6.7",
            "QtQuick.Pdf/PdfPageImage 6.8"
        ]
        exportMetaObjectRevisions: [1540, 1543, 1544]
        Property {
            name: "document"
            type: "QQuickPdfDocument"
            isPointer: true
            read: "document"
            write: "setDocument"
            notify: "documentChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "documentChanged" }
    }
    Component {
        file: "private/qquickpdfpagenavigator_p.h"
        name: "QQuickPdfPageNavigator"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QPdfPageNavigator"
        exports: [
            "QtQuick.Pdf/PdfPageNavigator 5.15",
            "QtQuick.Pdf/PdfPageNavigator 6.0"
        ]
        exportMetaObjectRevisions: [1295, 1536]
    }
    Component {
        file: "private/qquickpdfsearchmodel_p.h"
        name: "QQuickPdfSearchModel"
        accessSemantics: "reference"
        prototype: "QPdfSearchModel"
        exports: [
            "QtQuick.Pdf/PdfSearchModel 5.15",
            "QtQuick.Pdf/PdfSearchModel 6.0",
            "QtQuick.Pdf/PdfSearchModel 6.4",
            "QtQuick.Pdf/PdfSearchModel 6.8"
        ]
        exportMetaObjectRevisions: [1295, 1536, 1540, 1544]
        Property {
            name: "document"
            type: "QQuickPdfDocument"
            isPointer: true
            read: "document"
            write: "setDocument"
            notify: "documentChanged"
            index: 0
        }
        Property {
            name: "currentPage"
            type: "int"
            read: "currentPage"
            write: "setCurrentPage"
            notify: "currentPageChanged"
            index: 1
        }
        Property {
            name: "currentResult"
            type: "int"
            read: "currentResult"
            write: "setCurrentResult"
            notify: "currentResultChanged"
            index: 2
        }
        Property {
            name: "currentResultLink"
            type: "QPdfLink"
            read: "currentResultLink"
            notify: "currentResultLinkChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "currentPageBoundingPolygons"
            type: "QPolygonF"
            isList: true
            read: "currentPageBoundingPolygons"
            notify: "currentPageBoundingPolygonsChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "currentResultBoundingPolygons"
            type: "QPolygonF"
            isList: true
            read: "currentResultBoundingPolygons"
            notify: "currentResultBoundingPolygonsChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "currentResultBoundingRect"
            type: "QRectF"
            read: "currentResultBoundingRect"
            notify: "currentResultBoundingRectChanged"
            index: 6
            isReadonly: true
        }
        Signal { name: "currentPageChanged" }
        Signal { name: "currentResultChanged" }
        Signal { name: "currentResultLinkChanged" }
        Signal { name: "currentPageBoundingPolygonsChanged" }
        Signal { name: "currentResultBoundingPolygonsChanged" }
        Signal { name: "currentResultBoundingRectChanged" }
        Method {
            name: "boundingPolygonsOnPage"
            type: "QPolygonF"
            isList: true
            Parameter { name: "page"; type: "int" }
        }
    }
    Component {
        file: "private/qquickpdfselection_p.h"
        name: "QQuickPdfSelection"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Pdf/PdfSelection 5.15",
            "QtQuick.Pdf/PdfSelection 6.0",
            "QtQuick.Pdf/PdfSelection 6.3",
            "QtQuick.Pdf/PdfSelection 6.7"
        ]
        exportMetaObjectRevisions: [1295, 1536, 1539, 1543]
        Property {
            name: "document"
            type: "QQuickPdfDocument"
            isPointer: true
            read: "document"
            write: "setDocument"
            notify: "documentChanged"
            index: 0
        }
        Property {
            name: "page"
            type: "int"
            read: "page"
            write: "setPage"
            notify: "pageChanged"
            index: 1
        }
        Property {
            name: "renderScale"
            type: "double"
            read: "renderScale"
            write: "setRenderScale"
            notify: "renderScaleChanged"
            index: 2
        }
        Property {
            name: "from"
            type: "QPointF"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 3
        }
        Property { name: "to"; type: "QPointF"; read: "to"; write: "setTo"; notify: "toChanged"; index: 4 }
        Property {
            name: "hold"
            type: "bool"
            read: "hold"
            write: "setHold"
            notify: "holdChanged"
            index: 5
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            notify: "textChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "geometry"
            type: "QPolygonF"
            isList: true
            read: "geometry"
            notify: "selectedAreaChanged"
            index: 7
            isReadonly: true
        }
        Signal { name: "documentChanged" }
        Signal { name: "pageChanged" }
        Signal { name: "renderScaleChanged" }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal { name: "holdChanged" }
        Signal { name: "textChanged" }
        Signal { name: "selectedAreaChanged" }
        Method { name: "clear" }
        Method { name: "selectAll" }
        Method { name: "copyToClipboard"; isMethodConstant: true }
        Method {
            name: "inputMethodQuery"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "query"; type: "Qt::InputMethodQuery" }
            Parameter { name: "argument"; type: "QVariant" }
        }
    }
}
