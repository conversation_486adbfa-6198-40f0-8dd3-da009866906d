import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickkeyframe_p.h"
        name: "QQuickKeyframe"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.Timeline/Keyframe 1.0",
            "QtQuick.Timeline/Keyframe 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "frame"
            type: "double"
            read: "frame"
            write: "setFrame"
            notify: "frameChanged"
            index: 0
        }
        Property {
            name: "easing"
            type: "QEasingCurve"
            read: "easing"
            write: "setEasing"
            notify: "easingCurveChanged"
            index: 1
        }
        Property {
            name: "value"
            type: "QVariant"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 2
        }
        Signal { name: "frameChanged" }
        Signal { name: "easingCurveChanged" }
        Signal { name: "valueChanged" }
    }
    Component {
        file: "private/qquickkeyframe_p.h"
        name: "QQuickKeyframeGroup"
        accessSemantics: "reference"
        defaultProperty: "keyframes"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick.Timeline/KeyframeGroup 1.0",
            "QtQuick.Timeline/KeyframeGroup 1.1",
            "QtQuick.Timeline/KeyframeGroup 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 1536]
        Property {
            name: "target"
            type: "QObject"
            isPointer: true
            read: "target"
            write: "setTargetObject"
            notify: "targetChanged"
            index: 0
        }
        Property {
            name: "property"
            type: "QString"
            read: "property"
            write: "setProperty"
            notify: "propertyChanged"
            index: 1
        }
        Property {
            name: "keyframes"
            type: "QQuickKeyframe"
            isList: true
            read: "keyframes"
            index: 2
            isReadonly: true
        }
        Property {
            name: "keyframeSource"
            revision: 257
            type: "QUrl"
            read: "keyframeSource"
            write: "setKeyframeSource"
            notify: "keyframeSourceChanged"
            index: 3
        }
        Signal { name: "targetChanged" }
        Signal { name: "propertyChanged" }
        Signal { name: "keyframeSourceChanged"; revision: 257 }
    }
    Component {
        file: "private/qquicktimeline_p.h"
        name: "QQuickTimeline"
        accessSemantics: "reference"
        defaultProperty: "keyframeGroups"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick.Timeline/Timeline 1.0",
            "QtQuick.Timeline/Timeline 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "startFrame"
            type: "double"
            read: "startFrame"
            write: "setStartFrame"
            notify: "startFrameChanged"
            index: 0
        }
        Property {
            name: "endFrame"
            type: "double"
            read: "endFrame"
            write: "setEndFrame"
            notify: "endFrameChanged"
            index: 1
        }
        Property {
            name: "currentFrame"
            type: "double"
            read: "currentFrame"
            write: "setCurrentFrame"
            notify: "currentFrameChanged"
            index: 2
        }
        Property {
            name: "keyframeGroups"
            type: "QQuickKeyframeGroup"
            isList: true
            read: "keyframeGroups"
            index: 3
            isReadonly: true
        }
        Property {
            name: "animations"
            type: "QQuickTimelineAnimation"
            isList: true
            read: "animations"
            index: 4
            isReadonly: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 5
        }
        Signal { name: "enabledChanged" }
        Signal { name: "startFrameChanged" }
        Signal { name: "endFrameChanged" }
        Signal { name: "currentFrameChanged" }
    }
    Component {
        file: "private/qquicktimelineanimation_p.h"
        name: "QQuickTimelineAnimation"
        accessSemantics: "reference"
        prototype: "QQuickNumberAnimation"
        exports: [
            "QtQuick.Timeline/TimelineAnimation 1.0",
            "QtQuick.Timeline/TimelineAnimation 2.0",
            "QtQuick.Timeline/TimelineAnimation 2.12",
            "QtQuick.Timeline/TimelineAnimation 6.0"
        ]
        exportMetaObjectRevisions: [256, 512, 524, 1536]
        Property {
            name: "pingPong"
            type: "bool"
            read: "pingPong"
            write: "setPingPong"
            notify: "pingPongChanged"
            index: 0
        }
        Signal { name: "pingPongChanged" }
        Signal { name: "finished" }
    }
}
