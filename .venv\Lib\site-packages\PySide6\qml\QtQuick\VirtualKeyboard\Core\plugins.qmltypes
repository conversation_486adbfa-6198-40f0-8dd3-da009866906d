import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qvirtualkeyboardabstractinputmethod.h"
        name: "QVirtualKeyboardAbstractInputMethod"
        accessSemantics: "reference"
        prototype: "QObject"
        Signal {
            name: "selectionListChanged"
            Parameter { name: "type"; type: "QVirtualKeyboardSelectionListModel::Type" }
        }
        Signal {
            name: "selectionListActiveItemChanged"
            Parameter { name: "type"; type: "QVirtualKeyboardSelectionListModel::Type" }
            Parameter { name: "index"; type: "int" }
        }
        Signal { name: "selectionListsChanged" }
        Method { name: "reset" }
        Method { name: "update" }
        Method { name: "clearInputMode"; revision: 1537 }
    }
    Component {
        file: "private/qvirtualkeyboardfeatures_namespace_p.h"
        name: "QVirtualKeyboardFeatures"
        accessSemantics: "none"
        exports: ["QtQuick.VirtualKeyboard.Core/VirtualKeyboardFeatures 6.4"]
        isCreatable: false
        exportMetaObjectRevisions: [1540]
        Enum {
            name: "Feature"
            values: ["Handwriting"]
        }
    }
    Component {
        file: "qvirtualkeyboardinputcontext.h"
        name: "QVirtualKeyboardInputContext"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.VirtualKeyboard.Core/InputContext 1.0",
            "QtQuick.VirtualKeyboard.Core/InputContext 2.0",
            "QtQuick.VirtualKeyboard.Core/InputContext 2.4",
            "QtQuick.VirtualKeyboard.Core/InputContext 6.0",
            "QtQuick.VirtualKeyboard.Core/InputContext 6.1"
        ]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [256, 512, 516, 1536, 1537]
        Property {
            name: "shift"
            type: "bool"
            read: "isShiftActive"
            notify: "shiftActiveChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "shiftActive"
            revision: 516
            type: "bool"
            read: "isShiftActive"
            notify: "shiftActiveChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "capsLock"
            type: "bool"
            read: "isCapsLockActive"
            notify: "capsLockActiveChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "capsLockActive"
            revision: 516
            type: "bool"
            read: "isCapsLockActive"
            notify: "capsLockActiveChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "uppercase"
            type: "bool"
            read: "isUppercase"
            notify: "uppercaseChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "anchorPosition"
            type: "int"
            read: "anchorPosition"
            notify: "anchorPositionChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "cursorPosition"
            type: "int"
            read: "cursorPosition"
            notify: "cursorPositionChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "inputMethodHints"
            type: "Qt::InputMethodHints"
            read: "inputMethodHints"
            notify: "inputMethodHintsChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "preeditText"
            type: "QString"
            read: "preeditText"
            write: "setPreeditText"
            notify: "preeditTextChanged"
            index: 8
        }
        Property {
            name: "surroundingText"
            type: "QString"
            read: "surroundingText"
            notify: "surroundingTextChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "selectedText"
            type: "QString"
            read: "selectedText"
            notify: "selectedTextChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "anchorRectangle"
            type: "QRectF"
            read: "anchorRectangle"
            notify: "anchorRectangleChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "cursorRectangle"
            type: "QRectF"
            read: "cursorRectangle"
            notify: "cursorRectangleChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "animating"
            type: "bool"
            read: "isAnimating"
            write: "setAnimating"
            notify: "animatingChanged"
            index: 13
        }
        Property {
            name: "locale"
            type: "QString"
            read: "locale"
            notify: "localeChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "inputItem"
            type: "QObject"
            isPointer: true
            read: "inputItem"
            notify: "inputItemChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "inputEngine"
            type: "QVirtualKeyboardInputEngine"
            isPointer: true
            read: "inputEngine"
            index: 16
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "selectionControlVisible"
            type: "bool"
            read: "isSelectionControlVisible"
            notify: "selectionControlVisibleChanged"
            index: 17
            isReadonly: true
        }
        Property {
            name: "anchorRectIntersectsClipRect"
            type: "bool"
            read: "anchorRectIntersectsClipRect"
            notify: "anchorRectIntersectsClipRectChanged"
            index: 18
            isReadonly: true
        }
        Property {
            name: "cursorRectIntersectsClipRect"
            type: "bool"
            read: "cursorRectIntersectsClipRect"
            notify: "cursorRectIntersectsClipRectChanged"
            index: 19
            isReadonly: true
        }
        Property {
            name: "priv"
            revision: 512
            type: "QVirtualKeyboardInputContextPrivate"
            isPointer: true
            read: "priv"
            index: 20
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "keyboardObserver"
            revision: 1537
            type: "QVirtualKeyboardObserver"
            isPointer: true
            read: "keyboardObserver"
            index: 21
            isReadonly: true
            isPropertyConstant: true
        }
        Signal { name: "preeditTextChanged" }
        Signal { name: "inputMethodHintsChanged" }
        Signal { name: "surroundingTextChanged" }
        Signal { name: "selectedTextChanged" }
        Signal { name: "anchorPositionChanged" }
        Signal { name: "cursorPositionChanged" }
        Signal { name: "anchorRectangleChanged" }
        Signal { name: "cursorRectangleChanged" }
        Signal { name: "shiftActiveChanged" }
        Signal { name: "capsLockActiveChanged" }
        Signal { name: "uppercaseChanged" }
        Signal { name: "animatingChanged" }
        Signal { name: "localeChanged" }
        Signal { name: "inputItemChanged" }
        Signal { name: "selectionControlVisibleChanged" }
        Signal { name: "anchorRectIntersectsClipRectChanged" }
        Signal { name: "cursorRectIntersectsClipRectChanged" }
        Method {
            name: "sendKeyClick"
            Parameter { name: "key"; type: "int" }
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "modifiers"; type: "int" }
        }
        Method {
            name: "sendKeyClick"
            isCloned: true
            Parameter { name: "key"; type: "int" }
            Parameter { name: "text"; type: "QString" }
        }
        Method { name: "commit" }
        Method {
            name: "commit"
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "replaceFrom"; type: "int" }
            Parameter { name: "replaceLength"; type: "int" }
        }
        Method {
            name: "commit"
            isCloned: true
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "replaceFrom"; type: "int" }
        }
        Method {
            name: "commit"
            isCloned: true
            Parameter { name: "text"; type: "QString" }
        }
        Method { name: "clear" }
        Method {
            name: "setSelectionOnFocusObject"
            Parameter { name: "anchorPos"; type: "QPointF" }
            Parameter { name: "cursorPos"; type: "QPointF" }
        }
    }
    Component {
        file: "private/qvirtualkeyboardinputcontext_p.h"
        name: "QVirtualKeyboardInputContextPrivate"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.VirtualKeyboard.Core/InputContextPrivate 2.0",
            "QtQuick.VirtualKeyboard.Core/InputContextPrivate 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "State"
            isFlag: true
            isScoped: true
            values: [
                "Reselect",
                "InputMethodEvent",
                "KeyEvent",
                "InputMethodClick",
                "SyncShadowInput",
                "SetFocus"
            ]
        }
        Property {
            name: "focus"
            type: "bool"
            read: "focus"
            write: "setFocus"
            notify: "focusChanged"
            index: 0
        }
        Property {
            name: "keyboardRectangle"
            type: "QRectF"
            read: "keyboardRectangle"
            write: "setKeyboardRectangle"
            notify: "keyboardRectangleChanged"
            index: 1
        }
        Property {
            name: "previewRectangle"
            type: "QRectF"
            read: "previewRectangle"
            write: "setPreviewRectangle"
            notify: "previewRectangleChanged"
            index: 2
        }
        Property {
            name: "previewVisible"
            type: "bool"
            read: "previewVisible"
            write: "setPreviewVisible"
            notify: "previewVisibleChanged"
            index: 3
        }
        Property {
            name: "locale"
            type: "QString"
            read: "locale"
            write: "setLocale"
            notify: "localeChanged"
            index: 4
        }
        Property {
            name: "inputItem"
            type: "QObject"
            isPointer: true
            read: "inputItem"
            notify: "inputItemChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "shiftHandler"
            type: "QtVirtualKeyboard::ShiftHandler"
            isPointer: true
            read: "shiftHandler"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "shadow"
            type: "QtVirtualKeyboard::ShadowInputContext"
            isPointer: true
            read: "shadow"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Signal { name: "focusChanged" }
        Signal { name: "keyboardRectangleChanged" }
        Signal { name: "previewRectangleChanged" }
        Signal { name: "previewVisibleChanged" }
        Signal { name: "localeChanged" }
        Signal { name: "inputItemChanged" }
        Signal {
            name: "navigationKeyPressed"
            Parameter { name: "key"; type: "int" }
            Parameter { name: "isAutoRepeat"; type: "bool" }
        }
        Signal {
            name: "navigationKeyReleased"
            Parameter { name: "key"; type: "int" }
            Parameter { name: "isAutoRepeat"; type: "bool" }
        }
        Method { name: "hideInputPanel" }
        Method {
            name: "updateAvailableLocales"
            Parameter { name: "availableLocales"; type: "QStringList" }
        }
        Method {
            name: "forceCursorPosition"
            Parameter { name: "anchorPosition"; type: "int" }
            Parameter { name: "cursorPosition"; type: "int" }
        }
        Method { name: "onInputItemChanged" }
        Method {
            name: "setKeyboardObserver"
            Parameter { name: "keyboardObserver"; type: "QVirtualKeyboardObserver"; isPointer: true }
        }
        Method {
            name: "fileExists"
            type: "bool"
            Parameter { name: "fileUrl"; type: "QUrl" }
        }
        Method {
            name: "hasEnterKeyAction"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "registerInputPanel"
            Parameter { name: "inputPanel"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "contains"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "keyboardFunctionKey"
            type: "QtVirtualKeyboard::KeyboardFunctionKey"
            isMethodConstant: true
            Parameter { name: "keyboardFunction"; type: "QtVirtualKeyboard::KeyboardFunction" }
        }
    }
    Component {
        file: "qvirtualkeyboardinputengine.h"
        name: "QVirtualKeyboardInputEngine"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.VirtualKeyboard.Core/InputEngine 1.0",
            "QtQuick.VirtualKeyboard.Core/InputEngine 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Enum {
            name: "TextCase"
            isScoped: true
            values: ["Lower", "Upper"]
        }
        Enum {
            name: "InputMode"
            isScoped: true
            values: [
                "Latin",
                "Numeric",
                "Dialable",
                "Pinyin",
                "Cangjie",
                "Zhuyin",
                "Hangul",
                "Hiragana",
                "Katakana",
                "FullwidthLatin",
                "Greek",
                "Cyrillic",
                "Arabic",
                "Hebrew",
                "ChineseHandwriting",
                "JapaneseHandwriting",
                "KoreanHandwriting",
                "Thai",
                "Stroke",
                "Romaji",
                "HiraganaFlick"
            ]
        }
        Enum {
            name: "PatternRecognitionMode"
            isScoped: true
            values: [
                "None",
                "PatternRecognitionDisabled",
                "Handwriting",
                "HandwritingRecoginition"
            ]
        }
        Enum {
            name: "ReselectFlag"
            isFlag: true
            isScoped: true
            values: ["WordBeforeCursor", "WordAfterCursor", "WordAtCursor"]
        }
        Property {
            name: "activeKey"
            type: "Qt::Key"
            read: "activeKey"
            notify: "activeKeyChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "previousKey"
            type: "Qt::Key"
            read: "previousKey"
            notify: "previousKeyChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "inputMethod"
            type: "QVirtualKeyboardAbstractInputMethod"
            isPointer: true
            read: "inputMethod"
            write: "setInputMethod"
            notify: "inputMethodChanged"
            index: 2
        }
        Property {
            name: "inputModes"
            type: "int"
            isList: true
            read: "inputModes"
            notify: "inputModesChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "inputMode"
            type: "InputMode"
            read: "inputMode"
            write: "setInputMode"
            notify: "inputModeChanged"
            index: 4
        }
        Property {
            name: "patternRecognitionModes"
            type: "int"
            isList: true
            read: "patternRecognitionModes"
            notify: "patternRecognitionModesChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "wordCandidateListModel"
            type: "QVirtualKeyboardSelectionListModel"
            isPointer: true
            read: "wordCandidateListModel"
            notify: "wordCandidateListModelChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "wordCandidateListVisibleHint"
            type: "bool"
            read: "wordCandidateListVisibleHint"
            notify: "wordCandidateListVisibleHintChanged"
            index: 7
            isReadonly: true
        }
        Signal {
            name: "virtualKeyClicked"
            Parameter { name: "key"; type: "Qt::Key" }
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "modifiers"; type: "Qt::KeyboardModifiers" }
            Parameter { name: "isAutoRepeat"; type: "bool" }
        }
        Signal {
            name: "activeKeyChanged"
            Parameter { name: "key"; type: "Qt::Key" }
        }
        Signal {
            name: "previousKeyChanged"
            Parameter { name: "key"; type: "Qt::Key" }
        }
        Signal { name: "inputMethodChanged" }
        Signal { name: "inputMethodReset" }
        Signal { name: "inputMethodUpdate" }
        Signal { name: "inputModesChanged" }
        Signal { name: "inputModeChanged" }
        Signal { name: "patternRecognitionModesChanged" }
        Signal { name: "wordCandidateListModelChanged" }
        Signal { name: "wordCandidateListVisibleHintChanged" }
        Method { name: "reset" }
        Method { name: "update" }
        Method { name: "shiftChanged" }
        Method { name: "updateSelectionListModels" }
        Method { name: "updateInputModes" }
        Method {
            name: "virtualKeyPress"
            type: "bool"
            Parameter { name: "key"; type: "Qt::Key" }
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "modifiers"; type: "Qt::KeyboardModifiers" }
            Parameter { name: "repeat"; type: "bool" }
        }
        Method { name: "virtualKeyCancel" }
        Method {
            name: "virtualKeyRelease"
            type: "bool"
            Parameter { name: "key"; type: "Qt::Key" }
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "modifiers"; type: "Qt::KeyboardModifiers" }
        }
        Method {
            name: "virtualKeyClick"
            type: "bool"
            Parameter { name: "key"; type: "Qt::Key" }
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "modifiers"; type: "Qt::KeyboardModifiers" }
        }
        Method {
            name: "traceBegin"
            type: "QVirtualKeyboardTrace"
            isPointer: true
            Parameter { name: "traceId"; type: "int" }
            Parameter { name: "patternRecognitionMode"; type: "PatternRecognitionMode" }
            Parameter { name: "traceCaptureDeviceInfo"; type: "QVariantMap" }
            Parameter { name: "traceScreenInfo"; type: "QVariantMap" }
        }
        Method {
            name: "traceEnd"
            type: "bool"
            Parameter { name: "trace"; type: "QVirtualKeyboardTrace"; isPointer: true }
        }
        Method {
            name: "reselect"
            type: "bool"
            Parameter { name: "cursorPosition"; type: "int" }
            Parameter { name: "reselectFlags"; type: "ReselectFlags" }
        }
    }
    Component {
        file: "qvirtualkeyboardobserver.h"
        name: "QVirtualKeyboardObserver"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick.VirtualKeyboard.Core/KeyboardObserver 6.1"]
        exportMetaObjectRevisions: [1537]
        Property {
            name: "layout"
            type: "QVariant"
            read: "layout"
            notify: "layoutChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "layoutChanged" }
        Method { name: "invalidateLayout" }
    }
    Component {
        file: "qvirtualkeyboardselectionlistmodel.h"
        name: "QVirtualKeyboardSelectionListModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        exports: [
            "QtQuick.VirtualKeyboard.Core/SelectionListModel 1.0",
            "QtQuick.VirtualKeyboard.Core/SelectionListModel 6.0",
            "QtQuick.VirtualKeyboard.Core/SelectionListModel 6.4"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536, 1540]
        Enum {
            name: "Type"
            isScoped: true
            values: ["WordCandidateList"]
        }
        Enum {
            name: "Role"
            isScoped: true
            values: [
                "Display",
                "DisplayRole",
                "WordCompletionLength",
                "WordCompletionLengthRole",
                "Dictionary",
                "CanRemoveSuggestion"
            ]
        }
        Enum {
            name: "DictionaryType"
            isScoped: true
            values: ["Default", "User"]
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "countChanged" }
        Signal {
            name: "activeItemChanged"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "itemSelected"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "selectionListChanged"
            Parameter { name: "type"; type: "Type" }
        }
        Method {
            name: "selectionListActiveItemChanged"
            Parameter { name: "type"; type: "Type" }
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "dataSourceDestroyed" }
        Method {
            name: "selectItem"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "removeItem"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "dataAt"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "role"; type: "Role" }
        }
        Method {
            name: "dataAt"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "qvirtualkeyboardtrace.h"
        name: "QVirtualKeyboardTrace"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.VirtualKeyboard.Core/Trace 2.0",
            "QtQuick.VirtualKeyboard.Core/Trace 6.0",
            "QtQuick.VirtualKeyboard.Core/Trace 6.1"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536, 1537]
        Property {
            name: "traceId"
            type: "int"
            read: "traceId"
            write: "setTraceId"
            notify: "traceIdChanged"
            index: 0
        }
        Property {
            name: "channels"
            type: "QStringList"
            read: "channels"
            write: "setChannels"
            notify: "channelsChanged"
            index: 1
        }
        Property {
            name: "length"
            type: "int"
            read: "length"
            notify: "lengthChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "final"
            type: "bool"
            read: "isFinal"
            write: "setFinal"
            notify: "finalChanged"
            index: 3
        }
        Property {
            name: "canceled"
            type: "bool"
            read: "isCanceled"
            write: "setCanceled"
            notify: "canceledChanged"
            index: 4
        }
        Property {
            name: "opacity"
            type: "double"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 5
        }
        Signal {
            name: "traceIdChanged"
            Parameter { name: "traceId"; type: "int" }
        }
        Signal { name: "channelsChanged" }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "int" }
        }
        Signal {
            name: "finalChanged"
            Parameter { name: "isFinal"; type: "bool" }
        }
        Signal {
            name: "canceledChanged"
            Parameter { name: "isCanceled"; type: "bool" }
        }
        Signal {
            name: "opacityChanged"
            Parameter { name: "opacity"; type: "double" }
        }
        Method {
            name: "points"
            type: "QVariantList"
            isMethodConstant: true
            Parameter { name: "pos"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "points"
            type: "QVariantList"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "pos"; type: "int" }
        }
        Method { name: "points"; type: "QVariantList"; isCloned: true; isMethodConstant: true }
        Method {
            name: "addPoint"
            type: "int"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "setChannelData"
            Parameter { name: "channel"; type: "QString" }
            Parameter { name: "index"; type: "int" }
            Parameter { name: "data"; type: "QVariant" }
        }
        Method {
            name: "channelData"
            type: "QVariantList"
            isMethodConstant: true
            Parameter { name: "channel"; type: "QString" }
            Parameter { name: "pos"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "channelData"
            type: "QVariantList"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "channel"; type: "QString" }
            Parameter { name: "pos"; type: "int" }
        }
        Method {
            name: "channelData"
            type: "QVariantList"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "channel"; type: "QString" }
        }
        Method {
            name: "startHideTimer"
            revision: 1537
            Parameter { name: "delayMs"; type: "int" }
        }
    }
    Component {
        file: "private/qvirtualkeyboardnamespace_p.h"
        name: "QtVirtualKeyboard"
        accessSemantics: "none"
        exports: ["QtQuick.VirtualKeyboard.Core/QtVirtualKeyboard 6.4"]
        isCreatable: false
        exportMetaObjectRevisions: [1540]
        Enum {
            name: "KeyType"
            isScoped: true
            values: [
                "BaseKey",
                "BackspaceKey",
                "ChangeLanguageKey",
                "EnterKey",
                "FillerKey",
                "HandwritingModeKey",
                "HideKeyboardKey",
                "InputModeKey",
                "Key",
                "ModeKey",
                "NumberKey",
                "ShiftKey",
                "SpaceKey",
                "SymbolModeKey",
                "FlickKey"
            ]
        }
        Enum {
            name: "KeyboardFunction"
            isScoped: true
            values: [
                "HideInputPanel",
                "ChangeLanguage",
                "ToggleHandwritingMode"
            ]
        }
        Enum {
            name: "KeyboardFunctionKeys"
            alias: "KeyboardFunctionKey"
            isFlag: true
            isScoped: true
            type: "uint"
            values: ["None", "Hide", "Language", "All"]
        }
    }
    Component {
        file: "private/enterkeyaction_p.h"
        name: "QtVirtualKeyboard::EnterKeyAction"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.VirtualKeyboard.Core/EnterKeyAction 1.0",
            "QtQuick.VirtualKeyboard.Core/EnterKeyAction 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        attachedType: "QtVirtualKeyboard::EnterKeyActionAttachedType"
        Enum {
            name: "Id"
            values: ["None", "Go", "Search", "Send", "Next", "Done"]
        }
    }
    Component {
        file: "private/enterkeyactionattachedtype_p.h"
        name: "QtVirtualKeyboard::EnterKeyActionAttachedType"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "actionId"
            type: "int"
            read: "actionId"
            write: "setActionId"
            notify: "actionIdChanged"
            index: 0
        }
        Property {
            name: "label"
            type: "QString"
            read: "label"
            write: "setLabel"
            notify: "labelChanged"
            index: 1
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 2
        }
        Signal { name: "actionIdChanged" }
        Signal { name: "labelChanged" }
        Signal { name: "enabledChanged" }
    }
    Component {
        file: "private/inputmethod_p.h"
        name: "QtVirtualKeyboard::InputMethod"
        accessSemantics: "reference"
        prototype: "QVirtualKeyboardAbstractInputMethod"
        exports: [
            "QtQuick.VirtualKeyboard.Core/InputMethod 1.0",
            "QtQuick.VirtualKeyboard.Core/InputMethod 6.0",
            "QtQuick.VirtualKeyboard.Core/InputMethod 6.1"
        ]
        exportMetaObjectRevisions: [256, 1536, 1537]
        Property {
            name: "inputContext"
            type: "QVirtualKeyboardInputContext"
            isPointer: true
            read: "inputContext"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "inputEngine"
            type: "QVirtualKeyboardInputEngine"
            isPointer: true
            read: "inputEngine"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/plaininputmethod_p.h"
        name: "QtVirtualKeyboard::PlainInputMethod"
        accessSemantics: "reference"
        prototype: "QVirtualKeyboardAbstractInputMethod"
        exports: [
            "QtQuick.VirtualKeyboard.Core/PlainInputMethod 2.0",
            "QtQuick.VirtualKeyboard.Core/PlainInputMethod 6.0",
            "QtQuick.VirtualKeyboard.Core/PlainInputMethod 6.1"
        ]
        exportMetaObjectRevisions: [512, 1536, 1537]
    }
    Component {
        file: "private/shadowinputcontext_p.h"
        name: "QtVirtualKeyboard::ShadowInputContext"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.VirtualKeyboard.Core/ShadowInputContext 2.0",
            "QtQuick.VirtualKeyboard.Core/ShadowInputContext 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "inputItem"
            type: "QObject"
            isPointer: true
            read: "inputItem"
            write: "setInputItem"
            notify: "inputItemChanged"
            index: 0
        }
        Property {
            name: "anchorRectangle"
            type: "QRectF"
            read: "anchorRectangle"
            notify: "anchorRectangleChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "cursorRectangle"
            type: "QRectF"
            read: "cursorRectangle"
            notify: "cursorRectangleChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "anchorRectIntersectsClipRect"
            type: "bool"
            read: "anchorRectIntersectsClipRect"
            notify: "anchorRectIntersectsClipRectChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "cursorRectIntersectsClipRect"
            type: "bool"
            read: "cursorRectIntersectsClipRect"
            notify: "cursorRectIntersectsClipRectChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "selectionControlVisible"
            type: "bool"
            read: "selectionControlVisible"
            notify: "selectionControlVisibleChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "inputItemChanged" }
        Signal { name: "anchorRectangleChanged" }
        Signal { name: "cursorRectangleChanged" }
        Signal { name: "anchorRectIntersectsClipRectChanged" }
        Signal { name: "cursorRectIntersectsClipRectChanged" }
        Signal { name: "selectionControlVisibleChanged" }
        Method {
            name: "setSelectionOnFocusObject"
            Parameter { name: "anchorPos"; type: "QPointF" }
            Parameter { name: "cursorPos"; type: "QPointF" }
        }
        Method { name: "updateSelectionProperties" }
    }
    Component {
        file: "private/shifthandler_p.h"
        name: "QtVirtualKeyboard::ShiftHandler"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.VirtualKeyboard.Core/ShiftHandler 1.0",
            "QtQuick.VirtualKeyboard.Core/ShiftHandler 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "sentenceEndingCharacters"
            type: "QString"
            read: "sentenceEndingCharacters"
            write: "setSentenceEndingCharacters"
            notify: "sentenceEndingCharactersChanged"
            index: 0
        }
        Property {
            name: "autoCapitalizationEnabled"
            type: "bool"
            read: "isAutoCapitalizationEnabled"
            notify: "autoCapitalizationEnabledChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "toggleShiftEnabled"
            type: "bool"
            read: "isToggleShiftEnabled"
            notify: "toggleShiftEnabledChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "shiftActive"
            type: "bool"
            read: "isShiftActive"
            write: "setShiftActive"
            notify: "shiftActiveChanged"
            index: 3
        }
        Property {
            name: "capsLockActive"
            type: "bool"
            read: "isCapsLockActive"
            write: "setCapsLockActive"
            notify: "capsLockActiveChanged"
            index: 4
        }
        Property {
            name: "uppercase"
            type: "bool"
            read: "isUppercase"
            notify: "uppercaseChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "sentenceEndingCharactersChanged" }
        Signal { name: "toggleShiftEnabledChanged" }
        Signal { name: "autoCapitalizationEnabledChanged" }
        Signal { name: "shiftActiveChanged" }
        Signal { name: "capsLockActiveChanged" }
        Signal { name: "uppercaseChanged" }
        Method { name: "reset" }
        Method { name: "autoCapitalize" }
        Method { name: "restart" }
        Method { name: "localeChanged" }
        Method { name: "inputMethodVisibleChanged" }
        Method { name: "toggleShift" }
        Method { name: "clearToggleShiftTimer" }
    }
    Component {
        file: "private/virtualkeyboard_p.h"
        name: "QtVirtualKeyboard::VirtualKeyboard"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick.VirtualKeyboard.Core/VirtualKeyboard 6.1"]
        isCreatable: false
        exportMetaObjectRevisions: [1537]
        attachedType: "QtVirtualKeyboard::VirtualKeyboardAttachedType"
    }
    Component {
        file: "private/virtualkeyboardattachedtype_p.h"
        name: "QtVirtualKeyboard::VirtualKeyboardAttachedType"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "extraDictionaries"
            type: "QStringList"
            read: "extraDictionaries"
            write: "setExtraDictionaries"
            notify: "extraDictionariesChanged"
            index: 0
        }
        Signal { name: "extraDictionariesChanged" }
    }
}
