module QtQuick.VirtualKeyboard
linktarget Qt6::qtvkbplugin
optional plugin qtvkbplugin
classname QtQuick_VirtualKeyboardPlugin
typeinfo VirtualKeyboardQml.qmltypes
import QtQuick.VirtualKeyboard.Core auto
import QtQuick.VirtualKeyboard.Layouts auto
import QtQuick.VirtualKeyboard.Components auto
depends QtQuick auto
depends QtQuick.Window auto
depends QtQuick.Layouts auto
depends Qt.labs.folderlistmodel auto
depends QtQuick.VirtualKeyboard.Styles auto
depends QtQuick.VirtualKeyboard.Plugins auto
prefer :/qt-project.org/imports/QtQuick/VirtualKeyboard/
HandwritingInputPanel 6.0 HandwritingInputPanel.qml
HandwritingInputPanel 2.0 HandwritingInputPanel.qml
HandwritingInputPanel 1.0 HandwritingInputPanel.qml
InputPanel 6.0 InputPanel.qml
InputPanel 2.0 InputPanel.qml
InputPanel 1.0 InputPanel.qml
EnterKey 6.0 EnterKey.qml
EnterKey 2.0 EnterKey.qml
EnterKey 1.0 EnterKey.qml

