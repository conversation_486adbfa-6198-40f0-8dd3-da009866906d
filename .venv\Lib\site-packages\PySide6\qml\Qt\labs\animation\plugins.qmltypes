import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickboundaryrule_p.h"
        name: "QQuickBoundaryRule"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus", "QQmlPropertyValueInterceptor"]
        exports: [
            "Qt.labs.animation/BoundaryRule 1.0",
            "Qt.labs.animation/BoundaryRule 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Enum {
            name: "OvershootFilter"
            values: ["None", "Peak"]
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "minimum"
            type: "double"
            read: "minimum"
            write: "setMinimum"
            notify: "minimumChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "minimumOvershoot"
            type: "double"
            read: "minimumOvershoot"
            write: "setMinimumOvershoot"
            notify: "minimumOvershootChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "maximum"
            type: "double"
            read: "maximum"
            write: "setMaximum"
            notify: "maximumChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "maximumOvershoot"
            type: "double"
            read: "maximumOvershoot"
            write: "setMaximumOvershoot"
            notify: "maximumOvershootChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "overshootScale"
            type: "double"
            read: "overshootScale"
            write: "setOvershootScale"
            notify: "overshootScaleChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "currentOvershoot"
            type: "double"
            read: "currentOvershoot"
            notify: "currentOvershootChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "peakOvershoot"
            type: "double"
            read: "peakOvershoot"
            notify: "peakOvershootChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "overshootFilter"
            type: "OvershootFilter"
            read: "overshootFilter"
            write: "setOvershootFilter"
            notify: "overshootFilterChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "easing"
            type: "QEasingCurve"
            read: "easing"
            write: "setEasing"
            notify: "easingChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "returnDuration"
            type: "int"
            read: "returnDuration"
            write: "setReturnDuration"
            notify: "returnDurationChanged"
            index: 10
            isFinal: true
        }
        Signal { name: "enabledChanged" }
        Signal { name: "minimumChanged" }
        Signal { name: "minimumOvershootChanged" }
        Signal { name: "maximumChanged" }
        Signal { name: "maximumOvershootChanged" }
        Signal { name: "overshootScaleChanged" }
        Signal { name: "currentOvershootChanged" }
        Signal { name: "peakOvershootChanged" }
        Signal { name: "overshootFilterChanged" }
        Signal { name: "easingChanged" }
        Signal { name: "returnDurationChanged" }
        Signal { name: "returnedToBounds" }
        Method { name: "returnToBounds"; type: "bool" }
    }
}
