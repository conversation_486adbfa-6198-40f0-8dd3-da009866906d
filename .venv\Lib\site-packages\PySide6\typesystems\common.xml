<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem>
    <template name="const_char_pybuffer">
        PyObject *%out = Shiboken::Buffer::newObject(%in, size);
    </template>

    <template name="pybuffer_const_char">
        Py_ssize_t bufferLen;
        char *%out = reinterpret_cast&lt;char*&gt;(Shiboken::Buffer::getPointer(%PYARG_1, &amp;bufferLen));
    </template>

    <template name="uint_remove">
        uint %out = bufferLen;
    </template>

    <template name="pybytes_const_uchar">
        const uchar *%out = reinterpret_cast&lt;const uchar*>(PyBytes_AsString(%PYARG_1));
    </template>

    <template name="pybytes_uint">
          uint %out = static_cast&lt;uint>(PyBytes_Size(%PYARG_1));
    </template>

    <template name="pysequencesize_int">
        Py_ssize_t %out = PySequence_Size(%PYARG_1);
    </template>

    <!-- Convert an indexable C-style arrray %TYPE[%COUNT] to PySequence -->
    <template name="c-array-to-pysequence">
        Shiboken::AutoDecRef object(PyList_New(0));
        for (int i = 0; i &lt; %COUNT; i++) {
            PyList_Append(object, %CONVERTTOPYTHON[%TYPE](%in[i]));
        }
        PyObject *%out = object.object();
    </template>

    <!-- Convert a PySequence to an indexable C-style arrray %TYPE[] via ArrayPointer -->
    <template name="pysequence-to-c-array">
        const Py_ssize_t count = PySequence_Size(%PYARG_1);
        Shiboken::ArrayPointer&lt;%TYPE&gt; %out(count);
        for (Py_ssize_t i = 0; i &lt; count; ++i) {
            Shiboken::AutoDecRef a(PySequence_GetItem(%PYARG_1, i));
            %out[i] = %CONVERTTOCPP[%TYPE](a);
        }
    </template>

</typesystem>
