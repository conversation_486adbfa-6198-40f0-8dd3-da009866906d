<?xml version="1.0"?>
<!--
// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtCore"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
    <?if windows?>
    <load-typesystem name="typesystem_core_win.xml" generate="yes"/>
    <?endif?>
    <load-typesystem name="typesystem_core_common.xml" generate="yes"/>
</typesystem>
