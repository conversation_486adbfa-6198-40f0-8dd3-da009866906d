//     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file

/* WARNING, this code is GENERATED. Modify the template CodeTemplateCallsPositional.c.j2 instead! */

/* This file is included from another C file, help IDEs to still parse it on its own. */
#ifdef __IDE_ONLY__
#include "nuitka/prelude.h"
#endif

extern PyObject *CALL_FUNCTION_NO_ARGS(PyThreadState *tstate, PyObject *called);
extern PyObject *CALL_FUNCTION_WITH_SINGLE_ARG(PyThreadState *tstate, PyObject *called, PyObject *arg);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS1(PyThreadState *tstate, PyObject *called, PyObject *pos_args);
extern PyObject *CALL_FUNCTION_WITH_ARGS2(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS2(PyThreadState *tstate, PyObject *called, PyObject *pos_args);
extern PyObject *CALL_FUNCTION_WITH_ARGS3(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS3(PyThreadState *tstate, PyObject *called, PyObject *pos_args);
extern PyObject *CALL_FUNCTION_WITH_ARGS4(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS4(PyThreadState *tstate, PyObject *called, PyObject *pos_args);
extern PyObject *CALL_FUNCTION_WITH_ARGS5(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS5(PyThreadState *tstate, PyObject *called, PyObject *pos_args);
extern PyObject *CALL_FUNCTION_WITH_ARGS6(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS6(PyThreadState *tstate, PyObject *called, PyObject *pos_args);
extern PyObject *CALL_FUNCTION_WITH_ARGS7(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS7(PyThreadState *tstate, PyObject *called, PyObject *pos_args);
extern PyObject *CALL_FUNCTION_WITH_ARGS8(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS8(PyThreadState *tstate, PyObject *called, PyObject *pos_args);
extern PyObject *CALL_FUNCTION_WITH_ARGS9(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS9(PyThreadState *tstate, PyObject *called, PyObject *pos_args);
extern PyObject *CALL_FUNCTION_WITH_ARGS10(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS10(PyThreadState *tstate, PyObject *called, PyObject *pos_args);
extern PyObject *CALL_FUNCTION_WITH_NO_ARGS_KW_SPLIT(PyThreadState *tstate, PyObject *called,
                                                     PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS1_VECTORCALL(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                     PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS1_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                   PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS1_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *pos_args,
                                                       PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS2_VECTORCALL(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                     PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS2_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                   PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS2_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *pos_args,
                                                       PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS3_VECTORCALL(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                     PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS3_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                   PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS3_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *pos_args,
                                                       PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS4_VECTORCALL(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                     PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS4_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                   PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS4_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *pos_args,
                                                       PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS5_VECTORCALL(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                     PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS5_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                   PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS5_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *pos_args,
                                                       PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS6_VECTORCALL(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                     PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS6_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                   PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS6_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *pos_args,
                                                       PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS7_VECTORCALL(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                     PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS7_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                   PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS7_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *pos_args,
                                                       PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS8_VECTORCALL(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                     PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS8_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                   PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS8_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *pos_args,
                                                       PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS9_VECTORCALL(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                     PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS9_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                   PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS9_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *pos_args,
                                                       PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS10_VECTORCALL(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                      PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_ARGS10_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *const *args,
                                                    PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_FUNCTION_WITH_POS_ARGS10_KW_SPLIT(PyThreadState *tstate, PyObject *called, PyObject *pos_args,
                                                        PyObject *const *kw_values, PyObject *kw_names);
extern PyObject *CALL_METHODDESCR_WITH_SINGLE_ARG(PyThreadState *tstate, PyObject *called, PyObject *arg);
extern PyObject *CALL_METHODDESCR_WITH_ARGS2(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_METHODDESCR_WITH_ARGS3(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_METHODDESCR_WITH_ARGS4(PyThreadState *tstate, PyObject *called, PyObject *const *args);
extern PyObject *CALL_METHOD_NO_ARGS(PyThreadState *tstate, PyObject *source, PyObject *attr_name);
extern PyObject *CALL_METHOD_WITH_SINGLE_ARG(PyThreadState *tstate, PyObject *source, PyObject *attr_name,
                                             PyObject *arg);
extern PyObject *CALL_METHOD_WITH_ARGS2(PyThreadState *tstate, PyObject *source, PyObject *attr_name,
                                        PyObject *const *args);
extern PyObject *CALL_METHOD_WITH_ARGS3(PyThreadState *tstate, PyObject *source, PyObject *attr_name,
                                        PyObject *const *args);
extern PyObject *CALL_METHOD_WITH_ARGS4(PyThreadState *tstate, PyObject *source, PyObject *attr_name,
                                        PyObject *const *args);
extern PyObject *CALL_METHOD_WITH_ARGS5(PyThreadState *tstate, PyObject *source, PyObject *attr_name,
                                        PyObject *const *args);
extern PyObject *CALL_METHOD_WITH_ARGS6(PyThreadState *tstate, PyObject *source, PyObject *attr_name,
                                        PyObject *const *args);
extern PyObject *CALL_METHOD_WITH_ARGS7(PyThreadState *tstate, PyObject *source, PyObject *attr_name,
                                        PyObject *const *args);
extern PyObject *CALL_METHOD_WITH_ARGS8(PyThreadState *tstate, PyObject *source, PyObject *attr_name,
                                        PyObject *const *args);
extern PyObject *CALL_METHOD_WITH_ARGS9(PyThreadState *tstate, PyObject *source, PyObject *attr_name,
                                        PyObject *const *args);
extern PyObject *CALL_METHOD_WITH_ARGS10(PyThreadState *tstate, PyObject *source, PyObject *attr_name,
                                         PyObject *const *args);

//     Part of "Nuitka", an optimizing Python compiler that is compatible and
//     integrates with CPython, but also works on its own.
//
//     Licensed under the Apache License, Version 2.0 (the "License");
//     you may not use this file except in compliance with the License.
//     You may obtain a copy of the License at
//
//        http://www.apache.org/licenses/LICENSE-2.0
//
//     Unless required by applicable law or agreed to in writing, software
//     distributed under the License is distributed on an "AS IS" BASIS,
//     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//     See the License for the specific language governing permissions and
//     limitations under the License.
