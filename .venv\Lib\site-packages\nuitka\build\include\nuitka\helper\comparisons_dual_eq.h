//     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file

/* WARNING, this code is GENERATED. Modify the template HelperOperationComparisonDual.c.j2 instead! */

/* This file is included from another C file, help IDEs to still parse it on its own. */
#ifdef __IDE_ONLY__
#include "nuitka/prelude.h"
#endif

/* C helpers for type specialized "==" (EQ) comparisons */

/* Code referring to "NILONG" corresponds to Nuitka int/long/C long value and "NILONG" to Nuitka int/long/C long value.
 */
extern PyObject *RICH_COMPARE_EQ_OBJECT_NILONG_NILONG(nuitka_ilong *operand1, nuitka_ilong *operand2);

/* Code referring to "NILONG" corresponds to Nuitka int/long/C long value and "NILONG" to Nuitka int/long/C long value.
 */
extern bool RICH_COMPARE_EQ_CBOOL_NILONG_NILONG(nuitka_ilong *operand1, nuitka_ilong *operand2);

/* Code referring to "NILONG" corresponds to Nuitka int/long/C long value and "CLONG" to C platform long value. */
extern PyObject *RICH_COMPARE_EQ_OBJECT_NILONG_CLONG(nuitka_ilong *operand1, long operand2);

/* Code referring to "NILONG" corresponds to Nuitka int/long/C long value and "CLONG" to C platform long value. */
extern bool RICH_COMPARE_EQ_CBOOL_NILONG_CLONG(nuitka_ilong *operand1, long operand2);

/* Code referring to "NILONG" corresponds to Nuitka int/long/C long value and "DIGIT" to C platform digit value for long
 * Python objects. */
extern PyObject *RICH_COMPARE_EQ_OBJECT_NILONG_DIGIT(nuitka_ilong *operand1, long operand2);

/* Code referring to "NILONG" corresponds to Nuitka int/long/C long value and "DIGIT" to C platform digit value for long
 * Python objects. */
extern bool RICH_COMPARE_EQ_CBOOL_NILONG_DIGIT(nuitka_ilong *operand1, long operand2);

//     Part of "Nuitka", an optimizing Python compiler that is compatible and
//     integrates with CPython, but also works on its own.
//
//     Licensed under the Apache License, Version 2.0 (the "License");
//     you may not use this file except in compliance with the License.
//     You may obtain a copy of the License at
//
//        http://www.apache.org/licenses/LICENSE-2.0
//
//     Unless required by applicable law or agreed to in writing, software
//     distributed under the License is distributed on an "AS IS" BASIS,
//     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//     See the License for the specific language governing permissions and
//     limitations under the License.
