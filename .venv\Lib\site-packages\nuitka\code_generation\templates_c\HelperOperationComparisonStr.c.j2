{#     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file #}

static {{ target.getTypeDecl() }} COMPARE_{{op_code}}_{{target.getHelperCodeName()}}_{{left.getHelperCodeName()}}_{{right.getHelperCodeName()}}({{left.getVariableDecl("operand1")}}, {{right.getVariableDecl("operand2")}}) {
    {{left.getCheckValueCode("operand1")}}
    {{right.getCheckValueCode("operand2")}}

    PyStringObject *a = (PyStringObject *)operand1;
    PyStringObject *b = (PyStringObject *)operand2;

    // Same object has fast path for all operations.
    if (operand1 == operand2) {
{% if operator in ("==", ">=", "<=") %}
        bool r = true;
{% else %}
        bool r = false;
{% endif %}

        // Convert to target type.
        {{ target.getTypeDecl() }} result = {{target.getToValueFromBoolExpression("r")}};
        {{ target.getTakeReferenceStatement("result", immortal=True) }}
        return result;
    }

    Py_ssize_t len_a = Py_SIZE(operand1);
    Py_ssize_t len_b = Py_SIZE(operand2);

{% if operator in ("==", "!=") %}
    if (len_a != len_b) {
        {# Shortcut for equality/inequality, driven by length divergence. #}
{% if operator == "==" %}
        bool r = false;
{% else %}
        bool r = true;
{% endif %}

        // Convert to target type.
        {{ target.getTypeDecl() }} result = {{target.getToValueFromBoolExpression("r")}};
        {{ target.getTakeReferenceStatement("result", immortal=True) }}
        return result;
    } else {
        if ((a->ob_sval[0] == b->ob_sval[0]) && (memcmp(a->ob_sval, b->ob_sval, len_a) == 0)) {
{% if operator == "==" %}
            bool r = true;
{% else %}
            bool r = false;
{% endif %}

            // Convert to target type.
            {{ target.getTypeDecl() }} result = {{target.getToValueFromBoolExpression("r")}};
            {{ target.getTakeReferenceStatement("result", immortal=True) }}
            return result;
        } else {
{% if operator == "==" %}
            bool r = false;
{% else %}
            bool r = true;
{% endif %}

            // Convert to target type.
            {{ target.getTypeDecl() }} result = {{target.getToValueFromBoolExpression("r")}};
            {{ target.getTakeReferenceStatement("result", immortal=True) }}
            return result;
        }
    }
{% else %}

    Py_ssize_t min_len = (len_a < len_b) ? len_a : len_b;
    int c;

    if (min_len > 0) {
        c = Py_CHARMASK(*a->ob_sval) - Py_CHARMASK(*b->ob_sval);

        if (c==0) {
            c = memcmp(a->ob_sval, b->ob_sval, min_len);
        }
    } else {
        c = 0;
    }

    if (c == 0) {
        c = (len_a < len_b) ? -1 : (len_a > len_b) ? 1 : 0;
    }

{% if operator == "<" %}
    c = c < 0;
{% elif operator == "<=" %}
    c = c <= 0;
{% elif operator == ">" %}
    c = c > 0;
{% elif operator == ">=" %}
    c = c >= 0;
{% endif %}

    // Convert to target type.
    {{ target.getTypeDecl() }} result = {{target.getToValueFromBoolExpression("c != 0")}};
    {{ target.getTakeReferenceStatement("result", immortal=True) }}
    return result;

{% endif %}
}

{#     Part of "Nuitka", an optimizing Python compiler that is compatible and   #}
{#     integrates with CPython, but also works on its own.                      #}
{#                                                                              #}
{#     Licensed under the Apache License, Version 2.0 (the "License");          #}
{#     you may not use this file except in compliance with the License.         #}
{#     You may obtain a copy of the License at                                  #}
{#                                                                              #}
{#        http://www.apache.org/licenses/LICENSE-2.0                            #}
{#                                                                              #}
{#     Unless required by applicable law or agreed to in writing, software      #}
{#     distributed under the License is distributed on an "AS IS" BASIS,        #}
{#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. #}
{#     See the License for the specific language governing permissions and      #}
{#     limitations under the License.                                           #}
