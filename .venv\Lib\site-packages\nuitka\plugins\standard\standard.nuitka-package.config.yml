# yamllint disable rule:line-length
# yamllint disable rule:indentation
# yamllint disable rule:comments-indentation
# yamllint disable rule:comments
# too many spelling things, spell-checker: disable
---
- module-name: '_dbus_glib_bindings' # checksum: 437428e9
  implicit-imports:
    - depends:
        - '_dbus_bindings'

- module-name: '_distutils_hack.override' # checksum: f6295952
  anti-bloat:
    - description: 'disable distutils hacks'
      module_code: ''
      when: 'standalone'

- module-name: '_mpl<PERSON><PERSON>' # checksum: c9116f66
  implicit-imports:
    - depends:
        - 'pydoc'

- module-name: '_mysql' # checksum: 543f6964
  implicit-imports:
    - depends:
        - '_mysql_exceptions'

- module-name: '_pytest._code.code' # checksum: a25bbb4b
  implicit-imports:
    - depends:
        - 'py._path.local'

- module-name: '_ruamel_yaml' # checksum: bbdbd674
  implicit-imports:
    - depends:
        - 'ruamel.yaml.error'

- module-name: '_soundfile_data' # checksum: be356cb6
  dlls:
    - from_filenames:
        prefixes:
          - 'libsndfile'

- module-name: '_yaml' # checksum: 35d3c787
  implicit-imports:
    - depends:
        - 'yaml'

- module-name: 'absl.testing' # checksum: 2f66bb94
  anti-bloat:
    - description: 'Need to allow unittest usage'
      bloat-mode-overrides:
        'unittest': 'allow'

- module-name: 'accessible_output2' # checksum: 27cd20b7
  data-files:
    - dirs:
        - 'lib'

- module-name: 'aenum' # checksum: 1241f0d1
  anti-bloat:
    - description: 'avoid Python2/3 both referenced'
      replacements:
        'if PY2:': '"if %s:" % before_python3'

- module-name: 'aenum._common' # checksum: 6d559f39
  anti-bloat:
    - description: 'avoid Python2/3 both referenced'
      replacements:
        'if PY2:': '"if %s:" % before_python3'
        'if PY3:': '"if %s:" % python3_or_higher'

- module-name: 'aiohttp' # checksum: b22e1449
  anti-bloat:
    - description: 'requires annotations'
      annotations: 'yes'

- module-name: 'altair' # checksum: c120bb25
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        'load_ipython_extension': 'un-callable'
      when: 'not use_ipython'

- module-name: 'altair.utils.core' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'altair.utils.v5.display' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'altair.vegalite.v5.api' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'altair.vegalite.v5.display' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'altair.vegalite.v5.schema' # checksum: 245e25bc
  data-files:
    - patterns:
        - '*.json'

- module-name: 'androguard.core.resources' # checksum: ec8efe56
  data-files:
    - patterns:
        - 'public.xml'

- module-name: 'antlr4.FileStream' # checksum: b7fe4fce
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'import unittest': ''
      change_class:
        'TestFileStream': 'un-usable'

- module-name: 'antlr4.InputStream' # checksum: e9e7bd9b
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'import unittest': ''
      change_class:
        'TestInputStream': 'un-usable'

- module-name: 'antlr4.IntervalSet' # checksum: 214ba83e
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'import unittest': ''
      change_class:
        'TestIntervalSet': 'un-usable'

- module-name: 'antlr4.Recognizer' # checksum: 6e605dda
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'import unittest': ''
      change_class:
        'Test': 'un-usable'

- module-name: 'anyio' # checksum: f47c3c24
  anti-bloat:
    - description: 'remove pytest reference'
      no-auto-follow:
        'pytest': 'ignore'
        '_pytest': 'ignore'
      when: 'not use_pytest'

- module-name: 'anyio._core._eventloop' # checksum: c0b82a1e
  implicit-imports:
    - depends:
        - 'anyio._backends._asyncio'

- module-name: 'apkutils.axml' # checksum: ec8efe56
  data-files:
    - patterns:
        - 'public.xml'

- module-name: 'appdirs' # checksum: 9d4faeee
  anti-bloat:
    # Keep this the same as for 'pkg_resources._vendor.appdirs' module.
    # TODO: May allow naming specific "anti-bloat" blocks, and reference them by
    # name, that works like an include statement to the module.
    - description: 'remove pywin32 reference'
      replacements_plain:
        'import win32com.shell': 'raise ImportError'
      change_function:
        '_get_win_folder_with_pywin32': 'un-callable'
      when: 'not use_pywin32 or not win32'

- module-name: 'apscheduler' # checksum: 5005e940
  data-files:
    - include-metadata:
        - 'apscheduler'

  implicit-imports:
    - depends:
        - 'apscheduler.triggers.*'
        - 'apscheduler.triggers.*.*'

- module-name: 'apsw' # checksum: 8ef3407b
  implicit-imports:
    - depends:
        - '.shell'

- module-name: 'apt_inst' # checksum: 92a19929
  implicit-imports:
    - depends:
        - 'apt_pkg'

- module-name: 'arcade' # checksum: 25cf95c9
  data-files:
    - dirs:
        - 'resources'
    - patterns:
        - 'VERSION'
  anti-bloat:
    - no-auto-follow:
        'examples': 'Arcade examples are not available unless you do "--include-module=arcade.examples"'
        'experimental': 'Experimental features of Arcade will not be available unless you do "--include-module=arcade.experimental"'
        'future': 'Upcoming features of Arcade will not be available unless you do "--include-module=arcade.future"'

- module-name: 'aspose' # checksum: 1ffc48f1
  data-files:
    - patterns:
        - '"**/*"+extension_std_suffix'
        - '**/*.dll'
      when: 'not nuitka_python'
  anti-bloat:
    - no-follow:
        'aspose.*': 'workaround for aspose needing to load its submodules'

- module-name: 'astor' # checksum: 49521496
  data-files:
    - patterns:
        - 'VERSION'

- module-name: 'astropy' # checksum: 7c319dbe
  data-files:
    - patterns:
        - 'CITATION'

  anti-bloat:
    - description: 'remove pytest reference'
      replacements_plain:
        'from .tests.runner import TestRunner': ''
        'test = TestRunner.make_test_runner_in(__path__[0])': 'test = None'
      when: 'not use_pytest'
    - description: 'remove pydoc usage'
      global_replacements_plain:
        'import pydoc': ''
      when: 'not use_pydoc'
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'astropy.constants.config' # checksum: 71da0564
  implicit-imports:
    - depends:
        # TODO: These should probably be compile time computed
        - 'astropy.constants.codata2018'
        - 'astropy.constants.iau2015'

- module-name: 'astropy.table.table' # checksum: 6fb51e0f
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        # This pulls in pandas potentially too.
        'show_in_notebook': 'un-callable'
      when: 'not use_ipython'

- module-name: 'astropy.utils.console' # checksum: fbf167a8
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython import get_ipython': 'raise ImportError'
        'from IPython.utils import io': 'raise ImportError'
        'from IPython import version_info': 'raise ImportError'
        'from IPython.display import display': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'astropy.visualization.wcsaxes' # checksum: efd86e9d
  anti-bloat:
    - description: 'remove pytest reference'
      replacements_plain:
        'import pytest': 'raise ImportError'
      when: 'not use_pytest'

- module-name: 'asyncpg.pgproto.pgproto' # checksum: dadd25bf
  implicit-imports:
    - depends:
        - 'asyncpg.pgproto.exceptions'

- module-name: 'asyncpg.protocol.protocol' # checksum: 7da1344f
  implicit-imports:
    - depends:
        - 'asyncpg.pgproto.pgproto'

- module-name: 'atk' # checksum: 7d2edca1
  implicit-imports:
    - depends:
        - 'gobject'

- module-name: 'attr' # checksum: bcbcb80
  options:
    checks:
      - description: "'attrs' is not fully supported before version 23.1.0"
        support_info: 'warning'
        when: 'version("attrs") < (23,1)'

- module-name: 'autoit' # checksum: f4a80874
  dlls:
    - from_filenames:
        relative_path: 'lib'
        prefixes:
          - 'AutoIt'
      when: 'win32'

- module-name: 'av.audio.frame' # checksum: f3ef0d81
  implicit-imports:
    - depends:
        - 'av.frame'
        - 'av.audio.plane'

- module-name: 'av.audio.resampler' # checksum: a63582df
  implicit-imports:
    - depends:
        - 'av.filter'

- module-name: 'av.buffer' # checksum: 916e6f7c
  implicit-imports:
    - depends:
        - 'av.bytesource'
        - 'av.deprecation'

- module-name: 'av.codec.codec' # checksum: a82f4c60
  implicit-imports:
    - depends:
        - 'av.descriptor'

- module-name: 'av.codec.context' # checksum: 750c8827
  implicit-imports:
    - depends:
        - 'av.video.codeccontext'
        - 'av.audio.codeccontext'

- module-name: 'av.container.core' # checksum: 9c9c5cd3
  implicit-imports:
    - depends:
        - 'av.container.pyio'

- module-name: 'av.container.streams' # checksum: 291863de
  implicit-imports:
    - depends:
        - 'av.stream'

- module-name: 'av.descriptor' # checksum: 8377dd72
  implicit-imports:
    - depends:
        - 'av.option'

- module-name: 'av.filter.context' # checksum: 39338ab9
  implicit-imports:
    - depends:
        - 'av.filter.link'

- module-name: 'av.filter.graph' # checksum: 9d8b964e
  implicit-imports:
    - depends:
        - 'av.filter.context'

- module-name: 'av.filter.link' # checksum: 26332ac
  implicit-imports:
    - depends:
        - 'av.filter.pad'

- module-name: 'av.frame' # checksum: 121c2594
  implicit-imports:
    - depends:
        - 'av.buffer'
        - 'av.opaque'

- module-name: 'av.logging' # checksum: 7da91901
  implicit-imports:
    - depends:
        - 'logging'

- module-name: 'av.option' # checksum: d97fbec8
  implicit-imports:
    - depends:
        - 'av.enum'
        - 'av.utils'

- module-name: 'av.packet' # checksum: 3bd21471
  implicit-imports:
    - depends:
        - 'av.dictionary'
        - 'av.sidedata.sidedata'
        - 'av.opaque'

- module-name: 'av.sidedata.sidedata' # checksum: 6adb0fe2
  implicit-imports:
    - depends:
        - 'av.container.streams'
        - 'av.sidedata.motionvectors'

- module-name: 'av.utils' # checksum: 8fbe7302
  implicit-imports:
    - depends:
        - 'fractions'

- module-name: 'av.video.frame' # checksum: 13e95bc4
  implicit-imports:
    - depends:
        - 'av.video.reformatter'
        - 'av.plane'
        - 'av.video.plane'

- module-name: 'azure.cognitiveservices.speech' # checksum: e9989c05
  data-files:
    - patterns:
        - 'libMicrosoft.CognitiveServices.Speech.extension.lu.a'
      when: 'macos'

  dlls:
    - from_filenames:
        prefixes:
          - 'Microsoft.CognitiveServices.Speech'
          - 'libMicrosoft.CognitiveServices.Speech'

- module-name: 'babel' # checksum: 29ed1c5b
  data-files:
    - dirs:
        - 'locale-data'
    - patterns:
        - 'global.dat'

  implicit-imports:
    - depends:
        - 'babel.dates'
        - 'babel.numbers'

- module-name: 'backports.zoneinfo._common' # checksum: 70450440
  implicit-imports:
    - depends:
        - 'tzdata.zoneinfo'

- module-name: 'backports.zoneinfo._czoneinfo' # checksum: 22b5f04e
  implicit-imports:
    - depends:
        - '._common'

- module-name: 'betterproto' # checksum: 81316c3f
  data-files:
    - patterns:
        - 'templates/*.j2'

- module-name: 'bindings' # checksum: 463796d4
  dlls:
    - from_filenames:
        prefixes:
          - 'zaber-motion-lib'
      when: 'version("zaber_motion") == (5,1,2)'

- module-name: 'bitarray' # checksum: b157a72f
  anti-bloat:
    - description: 'remove unittest reference'
      change_function:
        'test': 'un-callable'

- module-name: 'bitsandbytes' # checksum: 14f2c5d0
  dlls:
    - from_filenames:
        prefixes:
          - 'libbitsandbytes_'
        suffixes:
          # Applied to all OSes in bitsandbytes wheels.
          - 'so'
      when: 'version("bitsandbytes") < (0,43)'
    - from_filenames:
        prefixes:
          - 'libbitsandbytes_'
      when: 'version("bitsandbytes") >= (0,43)'

- module-name: 'bitsandbytes.triton.dequantize_rowwise' # checksum: 291bbd89
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'is_triton_available()': 'False'
      when: 'not use_setuptools'

- module-name: 'bitsandbytes.triton.int8_matmul_mixed_dequanitze' # checksum: 291bbd89
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'is_triton_available()': 'False'
      when: 'not use_setuptools'

- module-name: 'bitsandbytes.triton.int8_matmul_rowwise_dequantize' # checksum: 291bbd89
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'is_triton_available()': 'False'
      when: 'not use_setuptools'

- module-name: 'bitsandbytes.triton.quantize_columnwise_and_transpose' # checksum: 291bbd89
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'is_triton_available()': 'False'
      when: 'not use_setuptools'

- module-name: 'bitsandbytes.triton.quantize_global' # checksum: 291bbd89
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'is_triton_available()': 'False'
      when: 'not use_setuptools'

- module-name: 'bitsandbytes.triton.quantize_rowwise' # checksum: 291bbd89
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'is_triton_available()': 'False'
      when: 'not use_setuptools'

- module-name: 'black' # checksum: db60cdd5
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

  # TODO: This ought to be determined at run-time.
  implicit-imports:
    - depends:
        - '30fcd23745efe32ce681__mypyc'

- module-name: 'blib2to3' # checksum: a54cab2a
  data-files:
    - patterns:
        - 'Grammar.txt'
        - 'PatternGrammar.txt'

- module-name: 'bokeh' # checksum: 20c33794
  data-files:
    - dirs:
        - '_sri'
    - patterns:
        - '_sri.json'

- module-name: 'bokeh.core' # checksum: 3b99d0a9
  data-files:
    - dirs:
        - '_templates'

- module-name: 'bokeh.io.notebook' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'boto' # checksum: 245e25bc
  data-files:
    - patterns:
        - '*.json'

- module-name: 'boto3' # checksum: 11d57af9
  data-files:
    - dirs:
        - 'data'

  implicit-imports:
    - depends:
        - 'boto3.ec2'
        - 'boto3.ec2.createtags'
        - 'boto3.ec2.deletetags'
        - 'boto3.dynamodb'
        - 'boto3.s3'
        - 'boto3.s3.inject'
        - 'boto3.s3.transfer'

- module-name: 'botocore' # checksum: 506f8064
  data-files:
    - dirs:
        - 'data'
    - patterns:
        - 'cacert.pem'

- module-name: 'bpy' # checksum: e3ed6145
  data-files:
    - dirs:
        - '.'
        - 'materialx'
        - 'usd'
    - raw_dirs:
        - '4.1'
  dlls:
    - from_filenames:
        prefixes:
          - ''

- module-name: 'branca' # checksum: c4e34d71
  data-files:
    - dirs:
        - 'templates'
    - patterns:
        - '*.json'

- module-name: 'brotli._brotli' # checksum: ff2e6560
  implicit-imports:
    - depends:
        - '_cffi_backend'

- module-name: 'bsdiff4' # checksum: b157a72f
  anti-bloat:
    - description: 'remove unittest reference'
      change_function:
        'test': 'un-callable'

- module-name: 'cairo' # checksum: 16042494
  implicit-imports:
    - depends:
        - 'gi._gi_cairo'

- module-name: 'cairo._cairo' # checksum: 81e49455
  implicit-imports:
    - depends:
        - 'gi._gobject'

- module-name: 'cairocffi' # checksum: 49521496
  data-files:
    - patterns:
        - 'VERSION'

- module-name: 'cairosvg' # checksum: 49521496
  data-files:
    - patterns:
        - 'VERSION'

- module-name: 'cassandra.cluster' # checksum: 5e0046a4
  implicit-imports:
    - depends:
        - 'cassandra.auth'
        - 'cassandra.connection'
        - 'concurrent.futures'
        - 'cassandra.encoder'
        - 'cassandra.metadata'
        - 'cassandra.timestamps'
        - 'cassandra.datastax.insights.reporter'
        - 'cassandra.datastax.cloud'
        - 'cassandra.io.libevreactor'
        - 'cassandra.io.twistedreactor'
        - 'cassandra.io.eventletreactor'
        - 'cassandra.io.asyncorereactor'
        - 'six'
        - 'uuid'

- module-name: 'cassandra.connection' # checksum: b8c24515
  implicit-imports:
    - depends:
        - 'cassandra.marshal'
        - 'cassandra.protocol'
        - 'cassandra.segment'

- module-name: 'cassandra.cqltypes' # checksum: e2377d83
  implicit-imports:
    - depends:
        - 'cassandra.util'

- module-name: 'cassandra.metadata' # checksum: 8f22557b
  implicit-imports:
    - depends:
        - 'cassandra.query'
        - 'cassandra.pool'

- module-name: 'cassandra.protocol' # checksum: c71f52cb
  implicit-imports:
    - depends:
        - 'cassandra.type_codes'
        - 'cassandra.cqltypes'
        - 'cassandra.policies'
        - 'cassandra.cython_deps'

- module-name: 'cassandra.util' # checksum: cac5354
  implicit-imports:
    - depends:
        - 'cassandra.compat'

- module-name: 'cefpython3' # checksum: 1be5e071
  data-files:
    - dirs:
        - 'locales'
    - patterns:
        - '*.bin'
        - '*.pak'
        - '*.dat'

  dlls:
    - from_filenames:
        prefixes:
          - 'subprocess'
        executable: 'yes'
    - from_filenames:
        prefixes:
          - ''

  implicit-imports:
    - depends:
        - 'urllib.request'

- module-name: 'celery' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'certifi' # checksum: 8215c061
  data-files:
    - patterns:
        - 'cacert.pem'

- module-name: 'certifi.core' # checksum: 40d902d3
  anti-bloat:
    - description: 'avoid using importlib.resources without need'
      replacements_plain:
        # Make sure to use fallback to file using code, old ones use import attempts,
        # now ones check versions first.
        'from importlib.resources import path as get_path, read_text': 'raise ImportError'
        'sys.version_info': '(0,)'

- module-name: 'cffi.api' # checksum: 4743eaca
  anti-bloat:
    - description: 'disable source recompile'
      change_function:
        'emit_python_code': 'un-callable'
        'emit_c_code': 'un-callable'
        'compile': 'un-callable'
        'distutils_extension': 'un-callable'
      when: 'not use_cffi_recompiler'

- module-name: 'cffi.ffiplatform' # checksum: 96258a71
  anti-bloat:
    - description: 'disable distutils hacks'
      replacements_plain:
        'import setuptools': 'pass'
    - description: 'disable distutils hacks'
      change_function:
        'get_extension': 'un-callable'
        '_build': 'un-callable'
      when: 'not use_cffi_recompiler'

- module-name: 'chainer' # checksum: c481af6a
  implicit-imports:
    - depends:
        - 'chainer.distributions'
        - 'chainer.distributions.utils'

- module-name: 'chainer.distributions' # checksum: 80520696
  implicit-imports:
    - depends:
        - 'chainer.distributions.utils'

- module-name: 'charset_normalizer' # checksum: 3a81fa9
  implicit-imports:
    - depends:
        - 'charset_normalizer.md__mypyc'

- module-name: 'chia.ssl.create_ssl' # checksum: d95c1483
  implicit-imports:
    - depends:
        - 'mozilla-ca'

- module-name: 'chromadb' # checksum: efc1cb3a
  implicit-imports:
    - depends:
        - 'chromadb.telemetry.posthog'
        - 'chromadb.telemetry.product.posthog'
        - 'chromadb.api.local'
        - 'chromadb.db.duckdb'
        - 'chromadb.db.impl.sqlite'
        - 'chromadb.api.segment'
        - 'chromadb.api.fastapi'
        - 'chromadb.segment.impl.manager.local'
        - 'chromadb.segment.impl.metadata.sqlite'
        - 'chromadb.migrations.embeddings_queue'
        - 'chromadb.migrations.sysdb'
        - 'chromadb.migrations.metadb'
        - 'chromadb.ingest.impl.simple_policy'

- module-name: 'chromadb.migrations.embeddings_queue' # checksum: 5d24c17c
  data-files:
    - patterns:
        - '*.sql'

- module-name: 'chromadb.migrations.metadb' # checksum: 5d24c17c
  data-files:
    - patterns:
        - '*.sql'

- module-name: 'chromadb.migrations.sysdb' # checksum: 5d24c17c
  data-files:
    - patterns:
        - '*.sql'

- module-name: 'clr' # checksum: ba8734b8
  dlls:
    - from_filenames:
        prefixes:
          - 'Python.Runtime'

- module-name: 'clr_loader.ffi' # checksum: 5d37d102
  dlls:
    - from_filenames:
        relative_path: 'dlls/x86'
        prefixes:
          - 'ClrLoader'
      when: 'win32 and arch_x86'
    - from_filenames:
        relative_path: 'dlls/amd64'
        prefixes:
          - 'ClrLoader'
      when: 'win32 and arch_amd64'

- module-name: 'clvm.version' # checksum: 5736ab2
  anti-bloat:
    - description: 'workaround buggy metadata usage'
      replacements_plain:
        '__version__ = get_distribution(__name__).version': 'raise DistributionNotFound'

- module-name: 'cmsis_pack_manager.cmsis_pack_manager' # checksum: a25e9cc1
  dlls:
    - from_filenames:
        prefixes:
          - 'native'

- module-name: 'coincurve._libsecp256k1' # checksum: a0cd690f
  dlls:
    - from_filenames:
        prefixes:
          - 'libsecp256k1'

  implicit-imports:
    - depends:
        - '_cffi_backend'

- module-name: 'comm' # checksum: db60cdd5
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

  # TODO: This is probably untrue, maybe IPython used to pull in black, and this
  # could well be removed.
  implicit-imports:
    - depends:
        - '30fcd23745efe32ce681__mypyc'

- module-name: 'connexion' # checksum: ae5ca038
  data-files:
    - dirs:
        - 'resources'

- module-name: 'cpuinfo.cpuinfo' # checksum: 3ad7b267
  anti-bloat:
    - description: 'workaround for forking itself'
      replacements_plain:
        "getattr(sys, 'frozen', False)": 'True'
        'getattr(sys, "frozen", False)': 'True'
        'CAN_CALL_CPUID_IN_SUBPROCESS = True': 'CAN_CALL_CPUID_IN_SUBPROCESS = False'

- module-name: 'Crypto.Util._raw_api' # checksum: ceb2cd86
  data-files:
    - empty_dirs:
        - '.'

- module-name: 'cryptoauthlib' # checksum: 63eb0512
  data-files:
    - patterns:
        - '*.json'

  dlls:
    - from_filenames:
        prefixes:
          - 'cryptoauth'

- module-name: 'Cryptodome.Util._raw_api' # checksum: ceb2cd86
  data-files:
    - empty_dirs:
        - '.'

- module-name: 'ctypes' # checksum: d5efbb6f
  anti-bloat:
    - description: 'remove comtypes dependency'
      no-auto-follow:
        'comtypes': 'ignore'

- module-name: 'curl_cffi' # checksum: 30c8f50c
  data-files:
    - patterns:
        - 'cacert.pem'

  implicit-imports:
    - depends:
        - '_cffi_backend'

- module-name: 'curses' # checksum: 6982eea
  implicit-imports:
    - depends:
        - '_curses'

- module-name: 'customtkinter' # checksum: fd2992b7
  data-files:
    - dirs:
        - 'assets'

- module-name: 'customtkinter.windows.widgets.theme.theme_manager' # checksum: 9a59423e
  anti-bloat:
    - description: 'workaround for file path on non-Windows'
      replacements_plain:
        'script_directory, "../../../assets"': 'os.path.normpath(os.path.join(script_directory, "../../../assets"))'
      # Not necessary on Windows
      when: 'not win32'

- module-name: 'cv2' # checksum: 3a4aa553
  variables:
    setup_code:
      - 'import cv2'
    declarations:
      'cv2_extra_sub_modules': 'tuple(m for m in cv2.__collect_extra_submodules(False) if m != ".dylibs")'
    when: 'not is_conda_package("cv2")'
  data-files:
    - dirs:
        - 'qt/fonts'
  dlls:
    - from_filenames:
        relative_path: 'qt/plugins/platforms'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        prefixes:
          - 'opencv_videoio'
      when: 'win32'
  anti-bloat:
    - description: 'workaround for config module as code'
      context:
        - 'import textwrap'
        - 'import pkgutil'
      replacements_plain:
        ? "load_first_config([\n"
        : 'if False: (['
      replacements:
        "load_first_config(['config.py'], True)": "''.join(textwrap.indent(get_data('cv2', m, b'').decode('utf8'), '    ') for m in ('config.py', 'config-3.py', 'config-%s.py' % python_version_str))"
    - description: 'workaround for colliding native module import'
      replacements:
        # Before OpenCV 4.6
        'native_module = importlib.import_module("cv2.cv2")': '"import imp; native_module = imp.load_dynamic(\"cv2\", os.path.join(os.path.dirname(__file__), \"cv2%s\"))" % extension_suffix'
      # After OpenCV 4.6
        'native_module = importlib.import_module("cv2")': '"import imp; native_module = imp.load_dynamic(\"cv2\", os.path.join(os.path.dirname(__file__), \"cv2%s\"))" % extension_suffix'
      when: 'standalone and before_python312 and not nuitka_python'
    - description: 'workaround for colliding native module import'
      replacements:
        'native_module = importlib.import_module("cv2")': '"from importlib.machinery import ExtensionFileLoader; from importlib.util import spec_from_file_location; from importlib._bootstrap import _load; __path = os.path.join(os.path.dirname(__file__), \"cv2%s\"); native_module = _load(spec_from_file_location(\"cv2\", __path, loader=ExtensionFileLoader(\"cv2\", __path)))" % extension_suffix'
      when: 'standalone and python312_or_higher and not nuitka_python'
    - description: 'remove error hiding code'
      replacements_plain:
        "print('OpenCV bindings requires \"numpy\" package.')": 'raise'
    - description: 'workaround for cv2 submodules'
      replacements:
        '__collect_extra_submodules(DEBUG)': 'repr(get_variable("cv2_extra_sub_modules"))'
      change_function:
        '__collect_extra_submodules': 'un-callable'
      when: 'standalone and not is_conda_package("cv2")'

  implicit-imports:
    - depends:
        - 'cv2.cv2'
        - 'numpy'
        - 'numpy.core'
    - depends:
        - '["cv2.%s" % m for m in get_variable("cv2_extra_sub_modules")]'
      when: 'not is_conda_package("cv2")'
    - pre-import-code:
        - |
          import os
          os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = os.path.join(os.path.dirname(__file__), 'qt/plugins')
          os.environ['QT_QPA_FONTDIR'] = os.path.join(os.path.dirname(__file__), 'qt/fonts')
      when: 'linux and standalone'
  options:
    checks:
      - description: 'OpenCV2 is only supported with 4.6 or later'
        support_info: 'error'
        when: 'version(get_dist_name("cv2")) < (4,6)'

- module-name: 'cvxpy.utilities.key_utils' # checksum: fc2bb323
  anti-bloat:
    - description: 'workaround for disallowed slice optimization'
      replacements_plain:
        'key[i].step': '(key[i].step if key[i].step is not None else 1)'

- module-name: 'cyclonedx' # checksum: f0f71611
  data-files:
    - dirs:
        - 'schema/_res'

- module-name: 'cytoolz.functoolz' # checksum: e6faf7b7
  implicit-imports:
    - depends:
        - 'cytoolz._signatures'

- module-name: 'cytoolz.itertoolz' # checksum: 30f1e27
  implicit-imports:
    - depends:
        - 'cytoolz.utils'

- module-name: 'dash' # checksum: 26b9c88c
  data-files:
    - dirs:
        - '.'

- module-name: 'dash._jupyter' # checksum: 681007fe
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython import get_ipython': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'dash.dash' # checksum: 18d125cd
  anti-bloat:
    - description: 'remove pytest reference'
      replacements_plain:
        '"_pytest" in sys.modules': 'False'
      when: 'not use_pytest'

- module-name: 'dask' # checksum: d914a438
  data-files:
    - patterns:
        - 'dask.yaml'

- module-name: 'dask.dataframe' # checksum: 54d2aebf
  anti-bloat:
    - description: 'remove pytest testing framework'
      replacements_plain:
        'from dask.dataframe._testing import test_dataframe': 'test_dataframe = None'
      when: 'not use_pytest'

  implicit-imports:
    - depends:
        - 'pyarrow._*'

- module-name: 'dask.dataframe._compat' # checksum: a10ec27c
  anti-bloat:
    - description: 'remove pandas.testing reference'
      replacements_plain:
        'import pandas.testing as tm': 'tm=None'

- module-name: 'dask.dataframe.utils' # checksum: 5af31dde
  anti-bloat:
    - description: 'remove pandas.testing reference'
      replacements_plain:
        'from dask.dataframe._compat import PANDAS_GE_150, tm': ''

- module-name: 'dask.dot' # checksum: ddcb31f2
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'import IPython.display as display': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'dask.widgets' # checksum: 2e123618
  data-files:
    - dirs:
        - 'templates'

- module-name: 'datasets.packaged_modules' # checksum: 4c45e344
  anti-bloat:
    - description: 'workaround dependency source code usages'
      context:
        - 'import datasets.packaged_modules'
      replacements_re:
        'inspect\.getsource\((.*?)\)': '""'
      append_result: '"_PACKAGED_DATASETS_MODULES = %r" % datasets.packaged_modules._PACKAGED_DATASETS_MODULES'

- module-name: 'datasets.utils.py_utils' # checksum: b4c9faa1
  anti-bloat:
    - description: 'add support for compiled functions'
      append_plain: |
        def copyfunc(func):
            try:
                g = func.clone()
            except AttributeError:
                g = types.FunctionType(func.__code__, func.__globals__, func.__name__, func.__defaults__, func.__closure__)
                g.__kwdefaults__ = func.__kwdefaults__

            return g

- module-name: 'datatree' # checksum: 70125037
  anti-bloat:
    - description: 'remove useless metadata usage'
      replacements_plain:
        '__version__ = get_distribution(__name__).version': 'raise DistributionNotFound'

- module-name: 'debian' # checksum: a633a55a
  data-files:
    - include-metadata:
        - 'debian'

- module-name: 'deepspeed' # checksum: 5af29274
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      no-auto-follow:
        'triton': 'ignore'
        'torch.utils.cpp_extension': 'ignore'
      when: 'not use_setuptools'

- module-name: 'delphifmx' # checksum: f818d46e
  dlls:
    - from_filenames:
        relative_path: 'Win32'
        prefixes:
          - 'DelphiFMX'
        suffixes:
          - 'pyd'
      when: 'win32 and arch_x86'
    - from_filenames:
        relative_path: 'Win64'
        prefixes:
          - 'DelphiFMX'
        suffixes:
          - 'pyd'
      when: 'win32 and arch_amd64'
    - from_filenames:
        relative_path: 'Linux64'
        prefixes:
          - 'libDelphiFMX'
      when: 'linux and not android'
    - from_filenames:
        relative_path: 'Android64'
        prefixes:
          - 'libDelphiFMX'
      when: 'android64'
    - from_filenames:
        relative_path: 'Android'
        prefixes:
          - 'libDelphiFMX'
      when: 'android32'
    - from_filenames:
        relative_path: 'OSX64'
        prefixes:
          - 'libDelphiFMX'
      when: 'macos and arch_amd64'
    - from_filenames:
        relative_path: 'OSXARM64'
        prefixes:
          - 'libDelphiFMX'
      when: 'macos and arch_arm64'

- module-name: 'dependency_injector._cwiring' # checksum: 86bf476b
  implicit-imports:
    - depends:
        - 'asyncio'

- module-name: 'dependency_injector.containers' # checksum: 9e1b9ce9
  implicit-imports:
    - depends:
        - 'six'
        - 'dependency_injector.wiring'

- module-name: 'dependency_injector.providers' # checksum: d30f2c6e
  implicit-imports:
    - depends:
        - 'dependency_injector.errors'

- module-name: 'diatheke' # checksum: ad402cfe
  import-hacks:
    - global-sys-path:
        # This package forces itself into "sys.path" and expects absolute
        # imports to be available.
        - ''

- module-name: 'dipy' # checksum: 6059075a
  anti-bloat:
    - description: 'remove dipy and numpy testing framework'
      replacements_plain:
        'from .testing import setup_test': ''

- module-name: 'distributed.client' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'distributed.config' # checksum: 2d3674cb
  data-files:
    - patterns:
        - 'distributed.yaml'

- module-name: 'distributed.deploy.cluster' # checksum: 95c04b64
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython.display import display': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'distributed.diagnostics.progressbar' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'distributed.scheduler' # checksum: ab59707f
  anti-bloat:
    - description: 'remove cython support'
      # TODO: We should replace this with a nuitkarize in Cython maybe.
      replacements_plain:
        'from cython import compiled': 'raise ImportError'
        'if compiled:': 'if False:'

- module-name: 'distributed.utils' # checksum: af35df37
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        'is_kernel': "'(lambda : False)'"
      when: 'not use_ipython'

- module-name: 'django' # checksum: 7652b420
  anti-bloat:
    - description: 'allow unittest inside django, too dependent to remove'
      bloat-mode-overrides:
        'unittest': 'allow'

- module-name: 'django.conf' # checksum: bcf25a05
  data-files:
    - dirs:
        - 'locale/en'

- module-name: 'django.core.management' # checksum: 5ac4f83f
  parameters:
    - 'name': 'settings-module'
      'values': 'checkModuleName(value)'
  variables:
    setup_code:
      - 'import os, importlib'
      - 'settings = importlib.import_module(os.getenv("PARAMETER_SETTINGS_MODULE")) if os.getenv("PARAMETER_SETTINGS_MODULE") else None'
    declarations:
      'django_installed_apps': 'settings.INSTALLED_APPS if settings else ()'
      'django_middleware': 'tuple(v.rsplit(".",1)[0] for v in (getattr(settings, "MIDDLEWARE", ()) if settings else ()))'
      'django_database_engines': 'tuple(db_conf["ENGINE"]+".base" for (name, db_conf) in sorted(settings.DATABASES.items())) if settings else ()'
      'django_url_module': 'getattr(settings, "ROOT_URLCONF", ()) if settings else ()'
      'django_session_serializer': 'getattr(settings, "SESSION_SERIALIZER", "django.contrib.sessions.serializers") if settings else "django.contrib.sessions.serializers"'
      'django_csrf_failure_view': 'getattr(settings, "CSRF_FAILURE_VIEW", "django.views.csrf.csrf_failure").rsplit(".", 1)[0] if settings else "django.views.csrf"'
      'django_caches': 'sum((tuple(v.rsplit(".",1)[0] for v in value.values()) for value in getattr(settings, "CACHES", {"": {"BACKEND": "django.core.cache.backends.locmem.LocMemCache"}}).values()), ())'
      'django_wsgi': 'getattr(settings, "WSGI_APPLICATION", "django.core.handlers.wsgi.dummy").rsplit(".",1)[0] if settings else "django.core.handlers.wsgi"'
      'django_storage': 'getattr(settings, "MESSAGE_STORAGE", "django.contrib.messages.storage.fallback.dummy").rsplit(".",1)[0] if settings else "django.contrib.messages.storage.fallback"'

    environment:
      'PARAMETER_SETTINGS_MODULE': 'get_parameter("settings-module", "")'
  anti-bloat:
    # This makes exceptions to the "*" dependency in the
    # implicit-imports section below.
    - no-follow:
        'django.core.management.commands.test': 'disable tests'
      when: 'not use_pytest'
  implicit-imports:
    - depends:
        - 'django.core.management.commands.*'
        - 'get_parameter("settings-module", ())'
        - '["%s.management.commands.*" % x for x in get_variable("django_installed_apps")]'
        - 'get_variable("django_installed_apps")'
        - '["%s.apps" % x for x in get_variable("django_installed_apps")]'
        - '["%s.models" % x for x in get_variable("django_installed_apps")]'
        - 'get_variable("django_middleware")'
        - 'get_variable("django_database_engines")'
        - 'get_variable("django_url_module")'
        - 'get_variable("django_csrf_failure_view")'
        - 'get_variable("django_caches")'
        - 'get_variable("django_wsgi")'
        - 'get_variable("django_session_serializer")'
        - 'get_variable("django_storage")'
        - 'django.template.loaders.*'
        - 'django.contrib.auth.middleware'
        - 'django.contrib.messages.middleware'
        - 'django.contrib.sessions.middleware'

  options:
    checks:
      - description: "Django settings module must be specified with '--module-parameter=django-settings-module=<settings_module_name>'"
        support_info: 'parameter'
        when: 'get_parameter("settings-module", None) is None'

- module-name: 'django.core.management.commands.shell' # checksum: 271ed2ec
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'
    - description: 'remove bpython reference'
      no-auto-follow:
        'bpython': 'ignore'

- module-name: 'django.db.backends' # checksum: 5a0c4e00
  implicit-imports:
    - depends:
        - 'django.db.backends.dummy.*'

- module-name: 'django.db.backends.base.creation' # checksum: f16012b0
  anti-bloat:
    - description: 'remove unittest reference'
      no-auto-follow:
        'unittest': 'ignore'
      when: 'not use_unittest'

- module-name: 'django.template.engine' # checksum: 19e5edde
  implicit-imports:
    - depends:
        - 'django.templatetags.i18n'

- module-name: 'django.template.library' # checksum: 87fd23ca
  implicit-imports:
    - depends:
        - 'django.template.loader_tags'

- module-name: 'django.utils.autoreload' # checksum: dd39f804
  anti-bloat:
    - description: 'workaround for django self forking'
      change_function:
        'get_child_arguments': "'lambda : [__nuitka_binary_exe]+sys.argv[1:]'"
      when: 'standalone'

- module-name: 'django.views' # checksum: 2e123618
  data-files:
    - dirs:
        - 'templates'

- module-name: 'django.views.debug' # checksum: 466f47d3
  anti-bloat:
    - description: 'workaround columns not available from compiled frame execution'
      replacements_plain:
        'if PY311:': 'if False:'

- module-name: 'dns.rdtypes' # checksum: 119feab4
  implicit-imports:
    - depends:
        - '.ANY.*'
        - '.IN.*'
        - '.CH.A'

- module-name: 'docling_core' # checksum: 690d0f0d
  data-files:
    - include-metadata:
        - 'docling-core'

- module-name: 'docx' # checksum: 2e123618
  data-files:
    - dirs:
        - 'templates'

- module-name: 'dotenv' # checksum: c120bb25
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        'load_ipython_extension': 'un-callable'
      when: 'not use_ipython'

- module-name: 'dotenv.main' # checksum: cb869e1f
  anti-bloat:
    - description: 'workaround to avoid dotenv requiring package directories'
      replacements_plain:
        "raise IOError('Starting path not found')": 'pass'
    - description: 'workaround to avoid dotenv requiring python filenames'
      replacements_plain:
        'while frame.f_code.co_filename == current_file': 'if False'
        'frame_filename = frame.f_code.co_filename': 'frame_filename = os.path.dirname(__file__)'

- module-name: 'duckdb' # checksum: 8daca454
  implicit-imports:
    - depends:
      # The anti-bloat avoids this one, but we need to force it.
        - '_datetime'

- module-name: 'easyocr' # checksum: 58ad3ee8
  data-files:
    - patterns:
        - 'character/*.txt'

- module-name: 'echopype.echodata.convention' # checksum: b6c419f7
  data-files:
    - patterns:
        - '*.yml'

- module-name: 'eel' # checksum: aa0d8efe
  data-files:
    - patterns:
        - 'eel.js'

- module-name: 'eliot._traceback' # checksum: 9793c465
  anti-bloat:
    - description: 'avoid useless duplication of traceback module'
      replacements_plain:
        'load_module(str("_traceback_no_io"), traceback)': '__import__("traceback")'

- module-name: 'enchant' # checksum: 6aaa99cc
  data-files:
    - dirs:
        - 'data'

- module-name: 'engineio' # checksum: 136f2310
  implicit-imports:
    - depends:
        - 'engineio.async_drivers'

- module-name: 'engineio.async_drivers' # checksum: 6b1810c1
  implicit-imports:
    - depends:
        - 'engineio.async_drivers.aiohttp'
        - 'engineio.async_drivers.asgi'
        - 'engineio.async_drivers.eventlet'
        - 'engineio.async_drivers.gevent'
        - 'engineio.async_drivers.gevent_uwsgi'
        - 'engineio.async_drivers.sanic'
        - 'engineio.async_drivers.threading'
        - 'engineio.async_drivers.tornado'

- module-name: 'ens._normalization' # checksum: 4c41dcf2
  data-files:
    - patterns:
        - 'specs/*.json'

- module-name: 'Equation' # checksum: b92f083c
  anti-bloat:
    - description: 'workaround for standalone mode'
      replacements:
        'os.listdir(dirname)': 'repr(os.listdir(get_module_directory("Equation")))'
      when: 'standalone'
  implicit-imports:
    - depends:
        - 'Equation.equation_*'

- module-name: 'eth_utils' # checksum: 38e256fb
  data-files:
    - dirs:
        - '__json'

- module-name: 'evaluate.config' # checksum: becc1d28
  anti-bloat:
    - description: 'workaround python version test not compile time recognized'
      replacements:
        'PY_VERSION < version.parse("3.8")': 'str(__import__("sys").version_info < (3,8))'

- module-name: 'evaluate.module' # checksum: b9453130
  anti-bloat:
    - description: 'add support for compiled methods'
      replacements_plain:
        'self.compute = types.MethodType(': 'self.compute = self.compute.__class__('
        'self.add_batch = types.MethodType(': 'self.add_batch = self.add_batch.__class__('
        'self.add = types.MethodType(': 'self.add = self.add.__class__('

- module-name: 'exchangelib' # checksum: 9f83fa1b
  implicit-imports:
    - depends:
        - 'tzdata'

- module-name: 'facenet_pytorch.models.mtcnn' # checksum: afef1db4
  data-files:
    - dirs:
        - '../data'

- module-name: 'faker.config' # checksum: c4a114e
  implicit-imports:
    - depends:
        - 'faker.providers.*'
        - 'faker.providers.*.*'

- module-name: 'falcon.app' # checksum: 6ad57efc
  implicit-imports:
    - depends:
        - '.app_helpers'
        - '.responders'
        - '.routing'

- module-name: 'falcon.media.handlers' # checksum: e7933ddb
  implicit-imports:
    - depends:
        - 'falcon.vendor.mimeparse'

- module-name: 'falcon.request' # checksum: be9e88c3
  implicit-imports:
    - depends:
        - '.request_helpers'
        - '.forwarded'
        - '.media'

- module-name: 'falcon.response' # checksum: 9c6b607c
  implicit-imports:
    - depends:
        - '.response_helpers'

- module-name: 'fastapi' # checksum: 583d2408
  implicit-imports:
    - depends:
        - 'fastapi.routing'

- module-name: 'fastapi.exceptions' # checksum: 9806d681
  implicit-imports:
    - depends:
        - 'attrs'

- module-name: 'feedparser.html' # checksum: 8b17e6bc
  anti-bloat:
    - description: 'use SGML code more naturally'
      context:
        - 'import inspect'
        - 'import textwrap'
        - 'import sgmllib'
      replacements_plain:
        '__parse_starttag.__code__ = sgmllib.SGMLParser.parse_starttag.__code__': 'pass'
        'def goahead(self, i):': 'def goahead(self, end):'
        'goahead.__code__ = sgmllib.SGMLParser.goahead.__code__': 'pass'
      change_function:
        '__parse_starttag': 'textwrap.dedent(inspect.getsource(sgmllib.SGMLParser.parse_starttag))'
        'goahead': 'textwrap.dedent(inspect.getsource(sgmllib.SGMLParser.goahead))'

- module-name: 'fiona' # checksum: 5b806e55
  data-files:
    - dirs:
        - 'proj_data'

- module-name: 'fiona.crs' # checksum: 46b3b3db
  implicit-imports:
    - depends:
        - 'fiona.enums'

- module-name: 'fiona.ogrext' # checksum: 56a9bc3d
  implicit-imports:
    - depends:
        - 'fiona.schema'
        - 'fiona._shim'

- module-name: 'fire' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'fitz.fitz' # checksum: d0a93b71
  implicit-imports:
    - depends:
        - 'fitz._fitz'

- module-name: 'flask.app' # checksum: f5f4a7d
  implicit-imports:
    - depends:
        - 'jinja2.ext'
        - 'jinja2.ext.autoescape'
        - 'jinja2.ext.with_'

- module-name: 'flask_restx' # checksum: 2c73cd34
  data-files:
    - dirs:
        - 'static'
        - 'schemas'

- module-name: 'flask_restx.swagger' # checksum: aff96af8
  # Templates are put on application level for flask to find it.
  data-files:
    - dest_path: '.'
      dirs:
        - 'templates'

- module-name: 'folium' # checksum: 2e123618
  data-files:
    - dirs:
        - 'templates'

- module-name: 'fontTools' # checksum: 5c1ede46
  anti-bloat:
    - description: 'remove doctest reference'
      no-auto-follow:
        'doctest': 'ignore'
      when: 'not use_unittest'
    - description: 'remove cython usage'
      no-auto-follow:
        'cython': 'ignore'
      when: 'not use_setuptools'
    - description: 'remove argparse reference'
      no-auto-follow:
        'argparse': 'ignore'

- module-name: 'fontTools.cu2qu.cu2qu' # checksum: 5673e8a6
  implicit-imports:
    - depends:
        - 'fontTools.cu2qu.errors'

- module-name: 'Foundation' # checksum: f269907b
  options:
    checks:
      - description: "The 'Foundation' module requires bundle module"
        macos_bundle: 'yes'
        when: 'macos'

- module-name: 'freetype' # checksum: 5a9431c2
  dlls:
    - from_filenames:
        prefixes:
          - 'libfreetype'

- module-name: 'frozendict' # checksum: 49521496
  data-files:
    - patterns:
        - 'VERSION'

- module-name: 'fsspec.transaction' # checksum: f49a0729
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'import distributed': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'future.standard_library' # checksum: 27611f9
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'from future.moves.test import support': 'raise ImportError'
      when: 'not use_unittest'

- module-name: 'geopandas.datasets' # checksum: 3983d794
  data-files:
    - dirs:
        - 'naturalearth_cities'
        - 'naturalearth_lowres'
    - patterns:
        - '*.zip'

- module-name: 'gevent' # checksum: ce776b5f
  implicit-imports:
    - depends:
        - '_cffi_backend'
        - 'gevent._config'
        - 'gevent.core'
        - 'gevent.resolver_thread'
        - 'gevent.resolver_ares'
        - 'gevent.socket'
        - 'gevent.threadpool'
        - 'gevent.thread'
        - 'gevent.threading'
        - 'gevent.select'
        - 'gevent.hub'
        - 'gevent.greenlet'
        - 'gevent.local'
        - 'gevent.event'
        - 'gevent.queue'
        - 'gevent.resolver'
        - 'gevent.subprocess'

    - depends:
        - 'gevent.libuv'
      when: 'win32'

    - depends:
        - 'gevent.libev'
      when: 'not win32'

- module-name: 'gevent._abstract_linkable' # checksum: 2e3ebe49
  implicit-imports:
    - depends:
        - 'gevent.__abstract_linkable'
        - 'gevent._gevent_c_abstract_linkable'

- module-name: 'gevent._ffi' # checksum: fd37b7a8
  implicit-imports:
    - depends:
        - 'gevent._ffi.loop'
        - 'gevent._ffi.callback'
        - 'gevent._ffi.watcher'

- module-name: 'gevent._gevent_c_hub_local' # checksum: 9390e659
  implicit-imports:
    - depends:
        - 'gevent._gevent_c_greenlet_primitives'

- module-name: 'gevent._greenlet' # checksum: 46dfe125
  implicit-imports:
    - depends:
        - 'gevent.__ident'

- module-name: 'gevent._hub_local' # checksum: 23497efa
  implicit-imports:
    - depends:
        - 'gevent.__hub_local'
        - 'gevent.__greenlet_primitives'
        - 'gevent._gevent_c_hub_local'

- module-name: 'gevent._hub_primitives' # checksum: 9235fa67
  implicit-imports:
    - depends:
        - 'gevent.__hub_primitives'
        - 'gevent._gevent_cgreenlet'
        - 'gevent._gevent_c_hub_primitives'

- module-name: 'gevent._imap' # checksum: 75a23344
  implicit-imports:
    - depends:
        - 'gevent.__imap'
        - 'gevent._gevent_c_imap'

- module-name: 'gevent._semaphore' # checksum: d784ce59
  implicit-imports:
    - depends:
        - 'gevent._abstract_linkable'
        - 'gevent.__semaphore'
        - 'gevent._gevent_c_semaphore'

- module-name: 'gevent._util' # checksum: 99746425
  anti-bloat:
    - description: 'remove gevent release framework'
      change_function:
        'postreleaser_before': "'(lambda data: None)'"
        'prereleaser_middle': "'(lambda data: None)'"

- module-name: 'gevent._waiter' # checksum: b3e9fa49
  implicit-imports:
    - depends:
        - 'gevent.__waiter'
        - 'gevent._gevent_c_waiter'

- module-name: 'gevent.event' # checksum: 3fabec99
  implicit-imports:
    - depends:
        - 'gevent._event'
        - 'gevent._gevent_cevent'

- module-name: 'gevent.greenlet' # checksum: 80f53d1d
  implicit-imports:
    - depends:
        - 'gevent._hub_local'
        - 'gevent._greenlet'
        - 'gevent._gevent_c_ident'

- module-name: 'gevent.hub' # checksum: 6b6ccad9
  implicit-imports:
    - depends:
        - 'gevent._hub_primitives'
        - 'gevent._greenlet_primitives'
        - 'gevent._hub_local'
        - 'gevent._waiter'
        - 'gevent._util'
        - 'gevent._ident'
        - 'gevent.exceptions'

- module-name: 'gevent.libev' # checksum: 300a9601
  implicit-imports:
    - depends:
        - 'gevent.libev.corecext'
        - 'gevent.libev.corecffi'
        - 'gevent.libev.watcher'

- module-name: 'gevent.libuv' # checksum: a72411fa
  implicit-imports:
    - depends:
        - 'gevent._interfaces'
        - 'gevent._ffi'
        - 'gevent.libuv.loop'
        - 'gevent.libuv.watcher'

- module-name: 'gevent.libuv.loop' # checksum: c73ddb2a
  implicit-imports:
    - depends:
        - 'gevent.libuv._corecffi'
        - 'gevent._interfaces'

- module-name: 'gevent.local' # checksum: 3af924b3
  implicit-imports:
    - depends:
        - 'gevent._local'
        - 'gevent._gevent_clocal'

- module-name: 'gevent.monkey' # checksum: 8498f7cb
  implicit-imports:
    - depends:
        - 'gevent.builtins'
        - 'gevent.time'
        - 'gevent.local'
        - 'gevent.ssl'
        - 'gevent.events'
        - 'gevent.signal'

- module-name: 'gevent.pool' # checksum: 9e719b31
  implicit-imports:
    - depends:
        - 'gevent._imap'

- module-name: 'gevent.queue' # checksum: 722307e
  implicit-imports:
    - depends:
        - 'gevent._queue'
        - 'gevent._gevent_cqueue'

- module-name: 'gevent.resolver' # checksum: 27625b1f
  implicit-imports:
    - depends:
        - 'gevent.resolver.blocking'
        - 'gevent.resolver.cares'
        - 'gevent.resolver.thread'

- module-name: 'git.objects.submodule.base' # checksum: 3c6d40d1
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'if HIDE_WINDOWS_KNOWN_ERRORS': 'if False'
      when: 'not use_unittest'

- module-name: 'git.util' # checksum: 3c6d40d1
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'if HIDE_WINDOWS_KNOWN_ERRORS': 'if False'
      when: 'not use_unittest'

- module-name: 'gmsh' # checksum: 713accff
  dlls:
    - by_code:
        setup_code: 'import gmsh'
        filename_code: 'gmsh.libpath'
      dest_path: 'lib'

- module-name: 'gooey' # checksum: fec3b7d0
  data-files:
    - dirs:
        - 'languages'
        - 'images'

- module-name: 'gradio' # checksum: e8bad59f
  data-files:
    - dirs:
        - 'templates'
        - 'node'
        - '_frontend_code'
    - patterns:
        - '*.json'

- module-name: 'gradio.blocks' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'gradio.component_meta' # checksum: c9db8138
  anti-bloat:
    - description: 'workaround attempt to create .pyi files during execution'
      change_function:
        'create_or_modify_pyi': "'(lambda *args, **kwargs: None)'"

- module-name: 'gradio.ipython_ext' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'gradio.utils' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'gradio_client' # checksum: 245e25bc
  data-files:
    - patterns:
        - '*.json'

- module-name: 'grpc' # checksum: 72e499e
  data-files:
    - patterns:
        - '_cython/_credentials/roots.pem'

- module-name: 'gruut' # checksum: fddc60ae
  data-files:
    - patterns:
        - 'VERSION'

  implicit-imports:
    - depends:
        - 'gruut_ipa'
        - 'gruut_lang_en'

- module-name: 'gruut_ipa' # checksum: 6aaa99cc
  data-files:
    - dirs:
        - 'data'

- module-name: 'gruut_lang_en' # checksum: ea583d10
  data-files:
    - dirs:
        - 'espeak'
        - 'g2p'
        - 'pos'
    - patterns:
        - 'VERSION'
        - 'lexicon.db'

- module-name: 'gsplat.cuda._backend' # checksum: d537c97a
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'from rich.console import Console': ''
        'from torch.utils.cpp_extension import _get_build_directory, load': ''
      when: 'not use_setuptools'

- module-name: 'gsplat.cuda_legacy._backend' # checksum: d537c97a
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'from rich.console import Console': ''
        'from torch.utils.cpp_extension import _get_build_directory, load': ''
      when: 'not use_setuptools'

- module-name: 'gssapi.raw' # checksum: f8e7301e
  implicit-imports:
    - depends:
        - 'gssapi.raw._enum_extensions.*'

- module-name: 'gtk._gtk' # checksum: 4fb4a03b
  implicit-imports:
    - depends:
        - 'pangocairo'
        - 'pango'
        - 'cairo'
        - 'gio'
        - 'atk'

- module-name: 'gtkunixprint' # checksum: 33e02294
  implicit-imports:
    - depends:
        - 'gobject'
        - 'cairo'
        - 'gtk'

- module-name: 'gym_tetris' # checksum: a12d9972
  data-files:
    - dirs:
        - '_roms'

- module-name: 'gymnasium.envs.registration' # checksum: 4f0b7bbb
  anti-bloat:
    - description: 'better error message for environment backends'
      replacements_plain:
        '    mod = importlib.import_module(mod_name)': |
          #
              try:
                mod = importlib.import_module(mod_name)
              except ModuleNotFoundError:
                import sys
                sys.exit("Nuitka: For this environment to load, need to use this as an option to compile with '--include-module=%s'." % mod_name)
      when: 'not deployment'

- module-name: 'h2o' # checksum: a3ac93f2
  data-files:
    - dirs:
        - 'h2o_data'
        - 'backend/bin'
    - patterns:
        - '*.txt'
        - '*.csv'
        - '*.jar'

- module-name: 'h5py' # checksum: 5a8caf97
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        'enable_ipython_completer': "'(lambda: None)'"
      when: 'not use_ipython'
    - description: 'remove h5py testing framework'
      replacements_plain:
        'from .tests import run_tests': ''
      change_function:
        'run_tests': "'(lambda args=None: None)'"

- module-name: 'h5py.h5' # checksum: 4247d58a
  implicit-imports:
    - depends:
        - 'h5py.defs'

- module-name: 'h5py.h5a' # checksum: b4c94904
  implicit-imports:
    - depends:
        - 'h5py._proxy'

- module-name: 'h5py.h5p' # checksum: 36dbc221
  implicit-imports:
    - depends:
        - 'h5py.h5ac'

- module-name: 'h5py.h5s' # checksum: 6502258b
  implicit-imports:
    - depends:
        - 'h5py.utils'

- module-name: 'huggingface_hub._login' # checksum: 3c12cb9a
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'import ipywidgets.widgets as widgets': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'huggingface_hub._snapshot_download' # checksum: 94377758
  anti-bloat:
    - description: 'workaround hard module import attribute bug'
      replacements_plain:
        'from .utils import tqdm as hf_tqdm': 'from .utils.tqdm import tqdm as hf_tqdm'

- module-name: 'huggingface_hub.commands.user' # checksum: 3c12cb9a
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'import ipywidgets.widgets as widgets': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'huggingface_hub.file_download' # checksum: da417692
  anti-bloat:
    - description: 'workaround hard module import attribute bug'
      append_plain: 'from .utils.tqdm import tqdm'

- module-name: 'hydra' # checksum: d3aae307
  data-files:
    - dirs:
        - 'conf'

- module-name: 'hydra.core.plugins' # checksum: f4d9feae
  implicit-imports:
    - depends:
        - 'hydra._internal.core_plugins.*'
        - 'hydra_plugins.*'

- module-name: 'imageio.core.imopen' # checksum: 7af58240
  anti-bloat:
    - replacements_plain:
        '`pip install imageio[{config.install_name}]` to install it': '`--include-module={config.module_name}` with Nuitka to include it'
        'err_type = ImportError': 'err_type = RuntimeError'
      when: 'not deployment'

  implicit-imports:
    - depends:
        - 'imageio.plugins.pillow_legacy'
        - 'imageio.plugins.freeimage'
        - 'imageio.plugins.simpleitk'
        - 'imageio.plugins.pillow'

- module-name: 'imageio_ffmpeg' # checksum: cbcd4133
  dlls:
    - from_filenames:
        relative_path: 'binaries'
        prefixes:
          - 'ffmpeg'
        executable: 'yes'

- module-name: 'imageio_ffmpeg._utils' # checksum: 77b155b9
  anti-bloat:
    - description: 'Resolve correct path for ffmpeg binary'
      replacements_plain:
        'return str(path.parent)': 'return os.path.join(os.path.dirname(__file__), "binaries")'

- module-name: 'imagej' # checksum: 763ace32
  anti-bloat:
    - description: 'compile time resolve version metadata'
      # This is necessary, because they have a function that prevents compile
      # time from seeing it. We might later force those to be inlined instead.
      replacements:
        'sj.get_version("pyimagej")': 'repr(version("pyimagej"))'

- module-name: 'imgui_bundle.immapp.immapp_notebook' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'importlib_metadata' # checksum: 37dae78
  anti-bloat:
    - description: 'remove useless metadata usage'
      replacements:
        '__version__ = version(__name__)': 'repr(version("importlib_metadata"))'
      when: 'standalone'

- module-name: 'importlib_resources._compat' # checksum: 1251a882
  anti-bloat:
    - description: 'workaround for file reader not used'
      replacements_plain:
        '_file_reader(self.spec)': '_native_reader(self.spec) or _file_reader(self.spec)'

- module-name: 'incremental' # checksum: c94164bb
  anti-bloat:
    - description: 'remove setuptools usage'
      no-auto-follow:
        'setuptools': 'ignore'
      when: 'not use_setuptools'
    - description: 'remove toml file usage'
      change_function:
        '_load_pyproject_toml': 'un-callable'
        '_load_toml': 'un-callable'
      when: 'standalone'

- module-name: 'ipcqueue' # checksum: ff2e6560
  implicit-imports:
    - depends:
        - '_cffi_backend'

- module-name: 'IPython.core.oinspect' # checksum: e7304002
  implicit-imports:
    - depends:
        - 'pygments.lexers.python'

- module-name: 'jaraco.text' # checksum: 6792b73d
  data-files:
    - patterns:
        - 'Lorem ipsum.txt'

- module-name: 'jarowinkler' # checksum: ac739d1f
  implicit-imports:
    - depends:
        - 'jarowinkler._initialize_cpp'

- module-name: 'jaxlib.version' # checksum: cd20d237
  anti-bloat:
    - description: 'remove git call'
      change_function:
        # No git version after compilation of course.
        '_version_from_git_tree': "'None'"
    - description: 'remove setuptools reference'
      change_function:
        '_get_cmdclass': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'jaxtyping' # checksum: 809b3e69
  data-files:
    - include-metadata:
        - 'jaxtyping'

  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'jedi' # checksum: 6be45546
  anti-bloat:
    - description: 'allow pydoc inside jedi, too dependent to remove'
      bloat-mode-overrides:
        'pydoc': 'allow'

- module-name: 'jenn.core' # checksum: bddd1843
  data-files:
    - patterns:
        - 'schema.json'

- module-name: 'jinja2.environment' # checksum: c71498fb
  anti-bloat:
    - description: 'remove Python3.5 only code'
      no-auto-follow:
        'jinja2.asyncsupport': 'ignore'
      when: 'before_python35'

- module-name: 'jinja2.lexer' # checksum: 1efd768a
  anti-bloat:
    - description: 'remove Python3 only code'
      no-auto-follow:
        'jinja2._identifier': 'ignore'
      when: 'before_python3'

- module-name: 'joblib._dask' # checksum: e3ae7a22
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'import distributed': 'raise ImportError'
        'import dask': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'joblib._memmapping_reducer' # checksum: 685c89b4
  anti-bloat:
    - description: 'workaround incorrect warning'
      replacements_plain:
        'warnings.warn': ''

- module-name: 'joblib._parallel_backends' # checksum: 850f2858
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'from distributed import get_worker': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'joblib.externals.cloudpickle.cloudpickle' # checksum: 1d738df0
  anti-bloat:
    - no-auto-follow:
        'tornado': 'can break tornado integration of joblib'

- module-name: 'joblib.externals.loky.backend.popen_loky_posix' # checksum: 2f19f091
  anti-bloat:
    - description: 'workaround for forking itself'
      replacements_plain:
        'if __name__ == "__main__":': 'def main():'
      append_plain: |
        if __name__ == "__main__": main()
      when: 'not win32'

- module-name: 'joblib.externals.loky.backend.popen_loky_win32' # checksum: 1c46facc
  anti-bloat:
    - description: 'workaround for forking itself'
      replacements_plain:
        "getattr(sys, 'frozen', False)": 'False'
        'getattr(sys, "frozen", False)': 'False'
        'assert is_forking(sys.argv), "Not forking"': ''
      when: 'win32'

- module-name: 'joblib.externals.loky.backend.resource_tracker' # checksum: 685c89b4
  anti-bloat:
    - description: 'workaround incorrect warning'
      replacements_plain:
        'warnings.warn': ''

- module-name: 'joblib.memory' # checksum: 49583532
  anti-bloat:
    - description: 'remove pydoc'
      replacements_plain:
        'import pydoc': ''
        'if inspect.isfunction(func):': 'if False:'

- module-name: 'jpype' # checksum: 7b04152a
  data-files:
    - dest_path: 'jpype'
      patterns:
        - '../org.jpype.jar'
      when: 'is_conda_package("jpype")'

- module-name: 'jsonrpcserver.main' # checksum: 245e25bc
  data-files:
    - patterns:
        - '*.json'

- module-name: 'jsonschema' # checksum: c1520175
  data-files:
    - dirs:
        - 'schemas'

- module-name: 'jsonschema_specifications' # checksum: c1520175
  data-files:
    - dirs:
        - 'schemas'

- module-name: 'jupyter_client' # checksum: f62be95d
  data-files:
    - include-metadata:
        - 'jupyter_client'

- module-name: 'keras' # checksum: 292d6ceb
  import-hacks:
    # This package adds another directory to the search path of itself,
    # which we will not find packages in unless added.
    - package-paths:
        - 'keras.api'

- module-name: 'keras.api._v2.keras.__internal__.utils' # checksum: 63342938
  anti-bloat:
    - description: 'remove keras testing tools reference'
      replacements_plain:
        'from keras.testing_infra.test_utils import layer_test': ''

- module-name: 'keras.src' # checksum: 9760fd72
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'from keras.src.testing_infra import test_utils': 'test_utils = None'
      when: 'not use_unittest'

- module-name: 'keras.src.utils.vis_utils' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'keras.utils.vis_utils' # checksum: 6cba68c8
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython import display': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'keyring' # checksum: 582ae7a2
  data-files:
    - include-metadata:
        - 'keyring'
  implicit-imports:
    - depends:
        - 'keyring.backends.*'

- module-name: 'keyring.backends.Windows' # checksum: a414afda
  implicit-imports:
    - depends:
        - 'win32timezone'
        - 'win32ctypes.core.ctypes.*'

- module-name: 'kivy' # checksum: 648dc799
  data-files:
    - dirs:
        - 'data'
  implicit-imports:
    - depends:
        - 'kivy.uix.*'

  options:
    checks:
      - description: 'Kivy is a GUI framework'
        macos_bundle: 'recommend'

- module-name: 'kivy._clock' # checksum: fbb1ca92
  implicit-imports:
    - depends:
        - 'kivy.weakmethod'

- module-name: 'kivy.core.clipboard' # checksum: d1e34cc4
  implicit-imports:
    - depends:
        - 'kivy.core.clipboard.clipboard_winctypes'
      when: 'win32'
    - depends:
        - 'kivy.core.clipboard.clipboard_nspaste'
      when: 'macos and has_module("pyobjus")'
    - depends:
        - 'kivy.core.clipboard.clipboard_sdl2'
      when: 'macos and not has_module("pyobjus")'
    - depends:
        - 'kivy.core.clipboard.clipboard_xclip'
        - 'kivy.core.clipboard.clipboard_xsel'
        - 'kivy.core.clipboard.clipboard_dbusklipper'
        - 'kivy.core.clipboard.clipboard_gtk3'
        - 'kivy.core.clipboard.clipboard_sdl2'
      when: 'linux'

- module-name: 'kivy.graphics.compiler' # checksum: c064234c
  implicit-imports:
    - depends:
        - 'kivy.graphics.shader'

- module-name: 'kivy.graphics.instructions' # checksum: b7c2a5f3
  implicit-imports:
    - depends:
        - 'kivy.graphics.buffer'
        - 'kivy.graphics.vertex'
        - 'kivy.graphics.vbo'

- module-name: 'kivy.graphics.vbo' # checksum: 1e23e34c
  implicit-imports:
    - depends:
        - 'kivy.graphics.compiler'

- module-name: 'kivymd' # checksum: 6d57c0fa
  data-files:
    - dirs:
        - 'fonts'
        - 'images'
        - 'data'
        - 'uix'
  implicit-imports:
    - depends:
        - 'kivymd.icon_definitions'
        - 'kivymd.uix.*'

- module-name: 'kiwisolver' # checksum: a0fc11a6
  dlls:
    - from_filenames:
        prefixes:
          - '_cext'

- module-name: 'lab_lamma' # checksum: 2c54cdc3
  implicit-imports:
    - depends:
        - 'vipm_io'
        - 'keyring'

- module-name: 'langchain.chains.llm_summarization_checker' # checksum: 9ac5f4e6
  data-files:
    - patterns:
        - '*.txt'

- module-name: 'lark' # checksum: c9c9b5a
  data-files:
    - dirs:
        - 'grammars'

- module-name: 'librosa' # checksum: 20378624
  anti-bloat:
    - description: 'allow numba inside of librosa, too dependent to remove'
      bloat-mode-overrides:
        'numba': 'allow'

- module-name: 'librosa.core.intervals' # checksum: 3b3e5ce2
  data-files:
    - patterns:
        - 'intervals.msgpack'

- module-name: 'libusb_package' # checksum: 5cbb45ba
  dlls:
    - from_filenames:
        prefixes:
          - 'libusb'

- module-name: 'license_expression' # checksum: 6aaa99cc
  data-files:
    - dirs:
        - 'data'

- module-name: 'lightgbm' # checksum: 5f44354a
  data-files:
    - dirs:
        - 'locales'
    - patterns:
        - '*.bin'
        - 'VERSION.txt'
  dlls:
    - from_filenames:
        relative_path: 'bin'
        prefixes:
          - 'lib_lightgbm'

- module-name: 'lightgbm.dask' # checksum: f12519e1
  anti-bloat:
    - description: 'remove docstrings'
      replacements_plain:
        "    _before_kwargs, _kwargs, _after_kwargs = _base_doc.partition('**kwargs')  # type: ignore": ''
        '        {_before_kwargs}client : dask.distributed.Client or None, optional (default=None)': ''
        ? "        {' ':4}Dask client. If ``None``, ``distributed.default_client()`` will be used at runtime. The Dask client used by this class will not be saved if the model object is pickled."
        : ''
        '        {_kwargs}{_after_kwargs}': ''

- module-name: 'lightgbm.libpath' # checksum: f4a8da46
  dlls:
    - from_filenames:
        prefixes:
          - 'lib_lightgbm'

- module-name: 'lightning' # checksum: ffc61f79
  data-files:
    - patterns:
        - 'version.info'

- module-name: 'lightning.app.runners.cloud' # checksum: 19428428
  anti-bloat:
    - description: 'remove lightning.app.testing dependency'
      no-auto-follow:
        'lightning.app.testing.helpers': 'ignore'

- module-name: 'lightning_fabric' # checksum: ffc61f79
  data-files:
    - patterns:
        - 'version.info'

- module-name: 'litellm' # checksum: fa10c772
  data-files:
    - patterns:
        - '*.json'
    - dirs:
        - 'llms/tokenizers'

- module-name: 'llama_cpp' # checksum: 34acaa89
  dlls:
    - from_filenames:
        relative_path: 'lib'
        prefixes:
          - ''

- module-name: 'llvmlite.binding.analysis' # checksum: 3083141d
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        '__IPYTHON__': 'raise NameError'
      when: 'not use_ipython'

- module-name: 'llvmlite.binding.ffi' # checksum: 57dc7edf
  dlls:
    - from_filenames:
        prefixes:
          - 'llvmlite'
          - 'libllvmlite'

- module-name: 'loguru._colorama' # checksum: 34c7690c
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'ipykernel': 'ignore'
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'lttbc' # checksum: d072aa03
  implicit-imports:
    - depends:
        - 'numpy.core.multiarray'

- module-name: 'lxml' # checksum: f89961a7
  implicit-imports:
    - depends:
        - '.builder'
        - '.etree'
        - '.objectify'
        - '.sax'
        - '._elementpath'

- module-name: 'lxml.etree' # checksum: dfaed226
  implicit-imports:
    - depends:
        - '._elementpath'
        - 'gzip'

- module-name: 'lxml.html' # checksum: 1e539447
  implicit-imports:
    - depends:
        - '.html.clean'
        - '.html.diff'
        - '.etree'

- module-name: 'lxml.objectify' # checksum: c4e92339
  implicit-imports:
    - depends:
        - 'lxml.etree'

- module-name: 'lxml.sax' # checksum: 291d11ed
  implicit-imports:
    - depends:
        - 'xml.sax'

- module-name: 'magic' # checksum: 38c304cc
  data-files:
    - dirs:
        - 'libmagic'
    - patterns:
        - 'magic.mgc'

  dlls:
    - from_filenames:
        relative_path: 'libmagic'
        prefixes:
          - 'libmagic'

- module-name: 'markdown' # checksum: 827a8bc8
  implicit-imports:
    - depends:
        - 'markdown.extensions.*'

- module-name: 'markdown2' # checksum: 6d68c5cf
  anti-bloat:
    - description: 'remove docutils usage'
      change_function:
        '_test': 'un-callable'
        'main': 'un-callable'

- module-name: 'matplotlib' # checksum: d5e66e6c
  anti-bloat:
    - description: 'remove setuptools and pytest testing framework reference'
      replacements_plain:
        '(root / ".git").exists()"': 'None'
        'test.__test__ = False': ''
      change_function:
        '_init_tests': "'None'"
        'test': "'None'"
        '_get_version': "'(lambda : _version.version)'"
    - description: 'avoid PySide6 unless used'
      no-auto-follow:
        'PySide6': 'ignore'
      when: 'not use_pyside6'
    - description: 'avoid PySide2 unless used'
      no-auto-follow:
        'PySide2': 'ignore'
      when: 'not use_pyside2'
    - description: 'avoid PyQt5 unless used'
      no-auto-follow:
        'PyQt5': 'ignore'
      when: 'not use_pyqt5'
    - description: 'avoid PyQt6 unless used'
      no-auto-follow:
        'PyQt6': 'ignore'
      when: 'not use_pyqt6'

  implicit-imports:
    - depends:
        - 'matplotlib.backend_managers'
        - 'matplotlib.backend_bases'
        - 'mpl_toolkits'
  options:
    checks:
      - description: "For matplotlib a GUI tooltkit selection might be needed use, use '--enable-plugin=tk-inter|PySide6|PySide2|PyQt6|PyQt5', or use '--enable-plugin=no-qt' for headless mode."
        support_info: 'plugin'
        when: 'not macos and not (use_noqt or use_pyqt5 or use_pyqt6 or use_pyside2 or use_pyside6)'

- module-name: 'matplotlib.backend_bases' # checksum: 5954e33d
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        '_fix_ipython_backend2gui': "'(lambda cls: None)'"
      when: 'not use_ipython'

- module-name: 'matplotlib.backends' # checksum: b30e7761
  implicit-imports:
    - depends:
        - 'matplotlib.backends._backend_agg'
        - 'matplotlib.backends.backend_agg'
    - depends:
        - 'matplotlib.backends._tkagg'
        - 'matplotlib.backends.backend_tkagg'
      when: 'use_tkinter'
    - depends:
        - 'matplotlib.backends._tkagg'
        - 'matplotlib.backends.backend_tkagg'
      when: 'use_tkinter'
    - depends:
        - 'matplotlib.backends.backend_qt5'
        - 'matplotlib.backends.backend_qt5cairo'
      when: 'use_pyqt5'
    - depends:
        - 'matplotlib.backends.backend_qtagg'
        - 'matplotlib.backends.backend_qt'
      when: 'use_pyqt5 or use_pyqt6 or use_pyside2 or use_pyside6'
    - depends:
        - 'matplotlib.backends.backend_macosx'
      when: 'macos and not (use_pyqt5 or use_pyqt6 or use_pyside2 or use_pyside6)'

- module-name: 'matplotlib.backends.backend_cairo' # checksum: 7ada20bb
  implicit-imports:
    - depends:
        - 'cairo'
        - 'cairocffi'

- module-name: 'matplotlib.backends.backend_gtk3' # checksum: cdcdf5f1
  implicit-imports:
    - depends:
        - 'matplotlib.backends.backend_gtk3agg'
        - 'gi'

- module-name: 'matplotlib.backends.backend_gtk3agg' # checksum: 821bb8e1
  implicit-imports:
    - depends:
        - 'matplotlib.backends.backend_gtk3'
        - 'gi'

- module-name: 'matplotlib.backends.backend_qt5' # checksum: 9df6d473
  implicit-imports:
    - depends:
        - 'matplotlib.backends.backend_qt5agg'
        - 'PyQt5'

- module-name: 'matplotlib.backends.backend_qt5agg' # checksum: 169ad914
  implicit-imports:
    - depends:
        - 'matplotlib.backends.backend_qt5'
        - 'PyQt5'

- module-name: 'matplotlib.backends.backend_webagg' # checksum: a81a3b73
  implicit-imports:
    - depends:
        - 'matplotlib.backends.backend_webagg_core'
        - 'tornado'

- module-name: 'matplotlib.backends.backend_webagg_core' # checksum: 42394f38
  implicit-imports:
    - depends:
        - 'matplotlib.backends.backend_webagg'
        - 'tornado'

- module-name: 'matplotlib.backends.backend_wx' # checksum: 91d13ceb
  implicit-imports:
    - depends:
        - 'matplotlib.backends.backend_wxagg'
        - 'wx'

- module-name: 'matplotlib.backends.backend_wxagg' # checksum: 1b2160ae
  implicit-imports:
    - depends:
        - 'matplotlib.backends.backend_wx'
        - 'wx'

- module-name: 'matplotlib.backends.qt_compat' # checksum: b3a099d2
  anti-bloat:
    - description: 'avoid shiboken dependencies in case of no-qt plugin'
      no-auto-follow:
        'shiboken2': 'ignore'
        'shiboken6': 'ignore'
      when: 'use_noqt'

- module-name: 'matplotlib.figure' # checksum: e03fe475
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        '_repr_html_': 'un-callable'
      when: 'not use_ipython'

- module-name: 'matplotlib.pyplot' # checksum: 1b72a2b5
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        'install_repl_displayhook': "'(lambda: None)'"
        'uninstall_repl_displayhook': "'(lambda: None)'"
      when: 'not use_ipython'

- module-name: 'mercurial.encoding' # checksum: de4741d0
  implicit-imports:
    - depends:
        - 'mercurial.charencode'
        - 'mercurial.cext.parsers'

- module-name: 'mitmproxy' # checksum: 4eec16b2
  implicit-imports:
    - depends:
        - 'mitmproxy_macos'
        - 'passlib.handlers.sha1_crypt'

- module-name: 'mitmproxy_macos' # checksum: a855017f
  data-files:
    - patterns:
        - 'Mitmproxy Redirector.app.tar'
    - dirs:
        - 'macos-certificate-truster.app'

- module-name: 'mkl' # checksum: 9a8684da
  dlls:
    - by_code:
        setup_code: |
          import os, sys
          library_path = os.path.join("Library", "bin") if os.name == "nt" else "lib"
          library_path = os.path.join(sys.prefix, library_path)
          library_prefix = "mkl_" if os.name == "nt" else "libmkl_"
        filename_code: |
          [os.path.join(library_path, filename)
           for filename in os.listdir(library_path)
           if filename.startswith(library_prefix)] if os.path.isdir(library_path) else []
      dest_path: '.'
      when: 'is_conda_package("mkl")'

- module-name: 'mkl._mklinit' # checksum: f45333f2
  dlls:
    - by_code:
        setup_code: 'import os;import sys'
        filename_code: "os.path.join(sys.prefix, 'lib', 'libmkl_mc3.1.dylib')"
      dest_path: '.'
      when: 'is_conda_package("mkl") and macos'

- module-name: 'mmcv.utils' # checksum: 7f54a1b
  anti-bloat:
    - description: 'remove mmcv testing framework'
      replacements_plain:
        'from .testing import': 'if False: from .testing import'
      append_plain: '__all__ = [_name for _name in __all__ if _name in globals()]'

- module-name: 'mmcv.utils.parrots_wrapper' # checksum: 8b72a836
  anti-bloat:
    - description: 'remove torch.utils.cpp_extension usage'
      change_function:
        '_get_extension': "'(lambda: None, None, None)'"
        'is_rocm_pytorch': "'(lambda: False)'"
        '_get_cuda_home': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'mmengine.utils.dl_utils.collect_env' # checksum: 5b16a015
  anti-bloat:
    - description: 'remove torch.utils.cpp_extension usage'
      change_function:
        '_get_cuda_home': 'un-callable'

- module-name: 'mmengine.utils.dl_utils.parrots_wrapper' # checksum: 8b72a836
  anti-bloat:
    - description: 'remove torch.utils.cpp_extension usage'
      change_function:
        '_get_extension': "'(lambda: None, None, None)'"
        'is_rocm_pytorch': "'(lambda: False)'"
        '_get_cuda_home': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'mne' # checksum: 6aaa99cc
  data-files:
    - dirs:
        - 'data'

- module-name: 'mne.channels' # checksum: f40fc664
  data-files:
    - patterns:
        - 'data/layouts/**.*'
        - 'data/montages/**.*'
        - 'data/neighbors/**.*'

- module-name: 'mne.fixes' # checksum: dd3b5473
  anti-bloat:
    - no-auto-follow:
        'numba': 'ignore'
      when: 'not use_numba'

- module-name: 'mne.html_templates' # checksum: d0ca6c4a
  data-files:
    - patterns:
        - '**/*.jinja'
        - '**/*.css'
        - '**/*.js'

- module-name: 'mne.icons' # checksum: d7996667
  data-files:
    - patterns:
        - '**/*.png'
        - '**/*.ico'
        - '**/*.svg'

- module-name: 'mne.io.array.array' # checksum: 6051d6a
  anti-bloat:
    - description: 'workaround frame dictionary not being populated'
      replacements_plain:
        'super().__init__(': '__import__("inspect").currentframe().f_locals.update(locals());super().__init__('

- module-name: 'mne.io.artemis123.resources' # checksum: 8e56f9f4
  data-files:
    - patterns:
        - '*.csv'

- module-name: 'mne.io.curry.curry' # checksum: 6051d6a
  anti-bloat:
    - description: 'workaround frame dictionary not being populated'
      replacements_plain:
        'super().__init__(': '__import__("inspect").currentframe().f_locals.update(locals());super().__init__('

- module-name: 'mne.io.edf.edf' # checksum: 6051d6a
  anti-bloat:
    - description: 'workaround frame dictionary not being populated'
      replacements_plain:
        'super().__init__(': '__import__("inspect").currentframe().f_locals.update(locals());super().__init__('

- module-name: 'mne.io.egi.egi' # checksum: 6051d6a
  anti-bloat:
    - description: 'workaround frame dictionary not being populated'
      replacements_plain:
        'super().__init__(': '__import__("inspect").currentframe().f_locals.update(locals());super().__init__('

- module-name: 'mne.io.egi.egimff' # checksum: 6051d6a
  anti-bloat:
    - description: 'workaround frame dictionary not being populated'
      replacements_plain:
        'super().__init__(': '__import__("inspect").currentframe().f_locals.update(locals());super().__init__('

- module-name: 'mne.io.eyelink.eyelink' # checksum: 6051d6a
  anti-bloat:
    - description: 'workaround frame dictionary not being populated'
      replacements_plain:
        'super().__init__(': '__import__("inspect").currentframe().f_locals.update(locals());super().__init__('

- module-name: 'mne.io.fiff.raw' # checksum: 6051d6a
  anti-bloat:
    - description: 'workaround frame dictionary not being populated'
      replacements_plain:
        'super().__init__(': '__import__("inspect").currentframe().f_locals.update(locals());super().__init__('

- module-name: 'mne.report.js_and_css' # checksum: dbad8298
  data-files:
    - patterns:
        - '*.js'
        - '*.css'

- module-name: 'mne.utils._testing' # checksum: 76efa1b7
  anti-bloat:
    - description: 'remove pytest reference'
      replacements_plain:
        'from unittest import SkipTest': 'SkipTest = None'
      no-auto-follow:
        'pytest': 'ignore'
      when: 'not use_pytest'

- module-name: 'mnemonic' # checksum: cfacd704
  data-files:
    - dirs:
        - 'wordlist'

- module-name: 'mock.mock' # checksum: 76ad686f
  anti-bloat:
    - replacements_plain:
        'from unittest.util import safe_repr': ''
      append_plain: |
        def safe_repr(obj, short=False):
          _MAX_LENGTH = 80

          try:
            result = repr(obj)
          except Exception:
            result = object.__repr__(obj)
          if not short or len(result) < _MAX_LENGTH:
            return result
          return result[:_MAX_LENGTH] + ' [truncated]...'
      when: 'not use_unittest'

- module-name: 'moto' # checksum: bfee0657
  data-files:
    - patterns:
        - 'ec2/resources/instance_types.json'
        - 'ec2/resources/amis.json'
  anti-bloat:
    - description: 'remove pytest testing framework'
      replacements_plain:
        "imp.find_module('pytest')": 'None'
      when: 'not use_pytest'

- module-name: 'moviepy.audio.fx' # checksum: 1851bf53
  implicit-imports:
    - depends:
        - '.*'

- module-name: 'moviepy.video.fx' # checksum: 1851bf53
  implicit-imports:
    - depends:
        - '.*'

- module-name: 'moviepy.video.io.html_tools' # checksum: f97c4550
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython.display import HTML': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'mozilla-ca' # checksum: 6cb98e6
  data-files:
    - patterns:
        - '*.pem'

- module-name: 'mpmath' # checksum: 843223c1
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'doctests': 'un-callable'
        'runtests': 'un-callable'
      when: 'not use_pytest'

- module-name: 'multiprocess.util' # checksum: bbf4504b
  anti-bloat:
    - description: 'remove unittest reference'
      change_function:
        '_cleanup_tests': 'un-callable'
      when: 'not use_unittest'

- module-name: 'multiprocessing.util' # checksum: bbf4504b
  anti-bloat:
    - description: 'remove unittest reference'
      change_function:
        '_cleanup_tests': 'un-callable'
      when: 'not use_unittest'

- module-name: 'nacl._sodium' # checksum: ff2e6560
  implicit-imports:
    - depends:
        - '_cffi_backend'

- module-name: 'names' # checksum: 4b523fa4
  data-files:
    - patterns:
        - 'dist.*'

- module-name: 'nes_py' # checksum: 1f3cc2b5
  dlls:
    - from_filenames:
        prefixes:
          - 'lib_nes_env'
        suffixes:
          - 'pyd'
      when: 'win32'

- module-name: 'networkx' # checksum: 87442f5d
  anti-bloat:
    - description: 'remove networkx.testing usage and pytest reference (via nose)'
      replacements_plain:
        'from networkx.testing.test import run as test': 'test = None'
        'from networkx.tests.test import run as test': 'test = None'
      no-auto-follow:
        'nose': 'ignore'
        'pytest': 'ignore'
      when: 'not use_pytest'
    - description: 'remove optional package references'
      no-auto-follow:
        'matplotlib': 'plotting will lack matplotlib'

- module-name: 'networkx.classes.backends' # checksum: 29bf8c92
  anti-bloat:
    - description: 'remove pytest reference'
      replacements_plain:
        'os.environ.get("NETWORKX_GRAPH_CONVERT")': 'None'
      when: 'not use_pytest'

- module-name: 'nicegui' # checksum: 438c71c
  data-files:
    - dirs:
        - 'static'

- module-name: 'nicegui.elements' # checksum: 27cd20b7
  data-files:
    - dirs:
        - 'lib'

- module-name: 'nicegui.elements.code' # checksum: 5a37c3b1
  implicit-imports:
    - depends:
        - 'pygments.formatters.html'

- module-name: 'nose.core' # checksum: 9c7fc85a
  data-files:
    - patterns:
        - 'usage.txt'

- module-name: 'numba' # checksum: 95f3f759
  parameters:
    - 'name': 'disable-jit'
      'values': 'value in ("yes", "no")'
  variables:
    # TODO: Limit this to the versions of numba doing it, eventually it will go
    # away.
    setup_code:
      - 'from numba.core import config'
    declarations:
      'numba_legacy_type_system': 'str(getattr(config, "USE_LEGACY_TYPE_SYSTEM", 1) == 1)'
  anti-bloat:
    - description: 'remove numba testing reference'
      change_function:
        'test': 'un-callable'
    - description: 'resolve numba legacy type config choice'
      global_replacements:
        'config.USE_LEGACY_TYPE_SYSTEM': 'get_variable("numba_legacy_type_system")'
    - description: 'resolve numba legacy type config choice'
      global_replacements_re:
        '(_RedirectSubpackage\s*\(\s*.*?\s*"(numba.*?)"\s*\))': '\1; import \2'
  options:
    checks:
      - description: "Numba is not yet fully working with Nuitka standalone, try to use '--noinclude-numba-mode=nofollow', but it may fail."
        support_info: 'warning'
        when: 'not use_numba and standalone'
      - description: "Numba JIT is disabled by default in standalone mode, make a choice explicit with '--module-parameter=numba-disable-jit=yes|no'"
        support_info: 'parameter'
        when: 'standalone and get_parameter("disable-jit", None) is None'
  import-hacks:
    - force-environment-variables:
        'NUMBA_DISABLE_JIT': '1'
      when: 'get_parameter("disable-jit", "yes" if standalone else "no") == "yes"'
    # This is for "numba.np.ufunc.omppool".
    - acceptable-missing-dlls:
        - 'libomp'

- module-name: 'numba.core.caching' # checksum: dce4aaa0
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython.paths import get_ipython_cache_dir': 'raise ImportError'
        'from IPython.utils.path import get_ipython_cache_dir': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'numba.core.entrypoints' # checksum: 84d4c71e
  anti-bloat:
    - description: 'avoid importlib backport usage'
      replacements:
        'if PYVERSION': '"if " + repr(sys.version_info[:2])'

- module-name: 'numba.cuda' # checksum: 3173fc81
  anti-bloat:
    - description: 'remove numba testing reference'
      replacements_plain:
        'from numba import runtests': ''
      change_function:
        'test': 'un-callable'

- module-name: 'numba.misc.inspection' # checksum: d1c24af1
  anti-bloat:
    - description: 'remove numba.pycc reference'
      change_function:
        'disassemble_elf_to_cfg': 'un-callable'

- module-name: 'numcodecs.blosc' # checksum: 52bdb974
  implicit-imports:
    - depends:
        - 'numcodecs.compat_ext'

- module-name: 'numexpr' # checksum: f51f1799
  anti-bloat:
    - description: 'remove numexpr.tests usage'
      replacements_plain:
        'import numexpr.tests': 'raise ImportError'

- module-name: 'numexpr.cpuinfo' # checksum: 53d827f7
  anti-bloat:
    - description: 'workaround for "is type" test'
      replacements_plain:
        'type(attr) is types.MethodType': 'isinstance(attr, types.MethodType)'

- module-name: 'numpy' # checksum: 10720e3
  dlls:
    - from_filenames:
        relative_path: '.libs'
        prefixes:
          - 'lib'
      dest_path: '.'
      when: 'win32'
    - by_code:
        setup_code: |
          import os, sys
          library_path = os.path.join("Library", "bin") if os.name == "nt" else "lib"
          library_path = os.path.join(sys.prefix, library_path)
          library_prefix = "mkl_" if os.name == "nt" else "libmkl_"
        filename_code: |
          [os.path.join(library_path, filename)
           for filename in os.listdir(library_path)
           if filename.startswith(library_prefix)] if os.path.isdir(library_path) else []
      dest_path: '.'
      when: 'is_conda_package("numpy") and version("numpy") < (2,)'

  anti-bloat:
    - description: 'avoid URLs for AV noise'
      replacements_plain:
        'https://': ''
    - description: 'remove numpy.distutils references'
      no-auto-follow:
        'numpy.distutils': 'ignore'
      when: 'not use_setuptools'
    - description: 'avoid PyYAML dependency'
      no-auto-follow:
        'yaml': 'ignore'
    - description: 'avoid charset_normalizer dependency'
      no-auto-follow:
        'charset_normalizer': 'ignore'
  implicit-imports:
    - depends:
        - 'numpy._mklinit'
        - 'numpy.compat'
        - 'numpy.lib'
        - 'numpy.linalg'
        - 'numpy.fft'
        - 'numpy.polynomial'
        - 'numpy.random'
        - 'numpy.ctypeslib'
        - 'numpy.ma'
        - 'numpy.matrixlib'

- module-name: 'numpy._core.overrides' # checksum: cda0efc0
  anti-bloat:
    - description: 'workaround numpy issues with compiled code'
      replacements_plain:
        'add_docstring(implementation, dispatcher.__doc__)': "add_docstring(implementation, dispatcher.__doc__ or '')"
        'public_api.__code__ = ': ''

- module-name: 'numpy._pytesttester' # checksum: 5e3b539b
  anti-bloat:
    - description: 'remove numpy testing framework'
      module_code: |
        class PytestTester:
          def __init__(self, name):
            pass

- module-name: 'numpy.core' # checksum: 4ed88aa0
  anti-bloat:
    - description: 'remove misleading numpy message'
      replacements_plain:
        'except ImportError as exc:': |
          except ImportError as exc:
              raise

  implicit-imports:
    - depends:
        - 'numpy.core._dtype_ctypes'
        - 'numpy.core._multiarray_tests'
        - 'numpy._core._multiarray_tests'

- module-name: 'numpy.core.overrides' # checksum: cda0efc0
  anti-bloat:
    - description: 'workaround numpy issues with compiled code'
      replacements_plain:
        'add_docstring(implementation, dispatcher.__doc__)': "add_docstring(implementation, dispatcher.__doc__ or '')"
        'public_api.__code__ = ': ''

- module-name: 'numpy.ctypeslib' # checksum: bed998c3
  anti-bloat:
    - description: 'remove numpy.distutils references'
      context:
        - 'import numpy.distutils.misc_util'
      replacements_plain:
        'from numpy.distutils.misc_util import get_shared_lib_extension': ''
      replacements:
        'get_shared_lib_extension()': 'repr(numpy.distutils.misc_util.get_shared_lib_extension())'
        'get_shared_lib_extension(is_python_ext=True)': 'repr(numpy.distutils.misc_util.get_shared_lib_extension(is_python_ext=True))'
      when: 'not use_setuptools'

- module-name: 'numpy.lib._utils_impl' # checksum: db52e110
  anti-bloat:
    - description: 'Avoid pydoc usage'
      change_function:
        'info': 'un-callable'
      when: 'not use_pydoc'

- module-name: 'numpy.lib.utils' # checksum: bdb2ffa6
  anti-bloat:
    - description: 'remove pydoc usage'
      replacements_plain:
        '.pydoc.allmethods(object)': '()'
        'import pydoc': ''
      when: 'not use_pydoc'

- module-name: 'numpy.random' # checksum: e43161e7
  implicit-imports:
    - depends:
        # These are post-1.18 names. TODO: Once we detect versions of packages, be proper selective here.
        - 'numpy.random._bit_generator'
        - 'numpy.random._bounded_integers'
        - 'numpy.random._common'
        - 'numpy.random._generator'
        - 'numpy.random._mt19937'
        - 'numpy.random._pcg64'
        - 'numpy.random._philox'
        - 'numpy.random._sfc64'

        # These are pre-1.18 names
        - 'numpy.random.bit_generator'
        - 'numpy.random.bounded_integers'
        - 'numpy.random.common'
        - 'numpy.random.generator'
        - 'numpy.random.mt19937'
        - 'numpy.random.pcg64'
        - 'numpy.random.philox'
        - 'numpy.random.sfc64'

        # TODO: Clarify if entropy is needed for 1.18 or at all.
        - 'numpy.random.entropy'
        - 'numpy.random.mtrand'

- module-name: 'numpy.random.bit_generator' # checksum: 7c0e00ef
  implicit-imports:
    - depends:
        - 'secrets'

- module-name: 'numpy.testing' # checksum: 4ad25b36
  anti-bloat:
    - description: 'remove numpy testing framework'
      module_code: |
        from contextlib import contextmanager
        class Tester:
          test = None
          bench = None

        class suppress_warnings:
          def __init__(self, forwarding_rule="always"):
            pass

          def filter(self, category=Warning, message="", module=None):
            pass

          def __enter__(self):
            return self

          def __exit__(self, *exc_info):
            pass

          def __call__(self, func):
            def new_func(*args, **kwargs):
              with self:
                return func(*args, **kwargs)

            return new_func

        def assert_allclose(*args, **kwargs):
          return True

        @contextmanager
        def _assert_warns_context(warning_class, name=None):
            yield

        def assert_warns(warning_class, *args, **kwargs):
          if not args:
              return _assert_warns_context(warning_class)

          func = args[0]
          args = args[1:]
          with _assert_warns_context(warning_class, name=func.__name__):
              return func(*args, **kwargs)

        @contextmanager
        def _assert_no_warnings_context(name=None):
              yield

        def assert_no_warnings(*args, **kwargs):
          if not args:
              return _assert_no_warnings_context()

          func = args[0]
          args = args[1:]
          with _assert_no_warnings_context(name=func.__name__):
              return func(*args, **kwargs)

        def assert_(val, msg=''):
          if not val:
              try:
                  smsg = msg()
              except TypeError:
                  smsg = msg
              raise AssertionError(smsg)

        def assert_equal(*args, **kwargs):
          pass

        def assert_approx_equal(*args, **kwargs):
          pass

        def assert_almost_equal(*args, **kwargs):
          pass

        def assert_array_equal(*args, **kwargs):
          pass

        def assert_array_less(*args, **kwargs):
          pass

        def assert_array_almost_equal(*args, **kwargs):
          pass

        def assert_raises(*args, **kwargs):
          exception_class=args[0]
          callable=args[1]
          args=args[2:]
          try:
              callable(*args, **kwargs)
          except exception_class:
            return True
          except BaseException:
            return False
          else:
            return False

- module-name: 'numpy.testing._private.pytesttester' # checksum: 5e3b539b
  anti-bloat:
    - description: 'remove numpy testing framework'
      module_code: |
        class PytestTester:
          def __init__(self, name):
            pass

- module-name: 'numpy.testing._private.utils' # checksum: 8f5f4b69
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'rundocs': 'un-callable'
      when: 'not use_unittest'

- module-name: 'nvidia' # checksum: 1639b6cb
  dlls:
    - from_filenames:
        relative_path: 'cublas/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'cuda_cupti/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'cuda_nvrtc/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'cuda_runtime/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'cudnn/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'cufft/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'curand/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'cusolver/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'cusparse/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'nccl/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'nvjitlink/lib'
        prefixes:
          - 'lib'
      when: 'linux'
    - from_filenames:
        relative_path: 'nvtx/lib'
        prefixes:
          - 'lib'
      when: 'linux'

- module-name: 'objc' # checksum: 5c6f45a7
  options:
    checks:
      - description: 'pyobjc is not supported before 9.0 or later'
        support_info: 'error'
        when: 'version("pyobjc") < (9,)'

- module-name: 'onnxruntime' # checksum: 37a94e21
  dlls:
    - from_filenames:
        relative_path: 'capi'
        prefixes:
          - 'onnxruntime'
      when: 'win32'
    - from_filenames:
        relative_path: 'capi'
        prefixes:
          - 'libonnxruntime'
      dest_path: '.'
      when: 'linux'

- module-name: 'onnxruntime.capi.onnxruntime_pybind11_state' # checksum: 63fe3556
  implicit-imports:
    - depends:
        - 'numpy'

- module-name: 'open3d' # checksum: 35ff624
  dlls:
    - from_filenames:
        prefixes:
          - 'lib'
          - '*swrast_dri'

- module-name: 'open_clip' # checksum: 8110bd0e
  data-files:
    - dirs:
        - 'model_configs'
    - patterns:
        - '*.tar.gz'

- module-name: 'openapi_spec_validator' # checksum: e31373a2
  data-files:
    - dirs:
        - 'resources/schemas'

- module-name: 'opencc' # checksum: ac51c19
  data-files:
    - dirs:
        - 'clib/share/opencc'
    - patterns:
        - '*.json'
        - '*.ocd2'

  dlls:
    - from_filenames:
        relative_path: 'clib/bin'
        prefixes:
          - 'opencc'
      when: 'win32'

- module-name: 'opentele.exception' # checksum: b9552185
  anti-bloat:
    - description: 'workaround compiled functions not updating locals'
      replacements_plain:
        'parameters = {arg: locals[arg] for arg in args}': 'parameters = {}'

- module-name: 'opentele.utils' # checksum: 32790776
  anti-bloat:
    - description: 'workaround compiled function decorator issues'
      replacements_plain:
        'bases = func.__class__.__bases__': 'bases = func.__class__.__bases__ if func.__class__.__bases__ != (FunctionType,) else (object,)'

- module-name: 'opentelemetry.exporter.jaeger.thrift' # checksum: f0eea343
  import-hacks:
    - global-sys-path:
        # This package forces itself into "sys.path" and expects absolute
        # imports like "jaeger" to be available.
        - 'gen'

- module-name: 'opentelemetry.exporter.jaeger.thrift.gen' # checksum: f7810856
  anti-bloat:
    - description: 'remove "sys.path" hack'
      replacements_plain:
        'sys.path.append': ''

- module-name: 'opentelemetry.propagate' # checksum: d832b4a7
  data-files:
    - include-metadata:
        - 'opentelemetry-api'

  implicit-imports:
    - depends:
        - 'opentelemetry.trace.propagation.tracecontext'
        - 'opentelemetry.baggage.propagation'

- module-name: 'opentelemetry.sdk.resources' # checksum: 6a5cb437
  data-files:
    - include-metadata:
        - 'opentelemetry-sdk'
  anti-bloat:
    - description: 'workaround for metadata functions'
      replacements_plain:
        'opentelemetry.util._importlib_metadata': 'importlib_metadata'

- module-name: 'openvino' # checksum: 4e2c2404
  data-files:
    - dirs:
        - 'libs'

- module-name: 'openvino.inference_engine' # checksum: 9100f948
  dlls:
    - from_filenames:
        relative_path: '../libs'
        prefixes:
          - ''

  implicit-imports:
    - depends:
        - 'openvino.inference_engine.ie_api'

- module-name: 'openvino.inference_engine.constants' # checksum: 63fe3556
  implicit-imports:
    - depends:
        - 'numpy'

- module-name: 'openvino.inference_engine.ie_api' # checksum: e6f9aa73
  implicit-imports:
    - depends:
        - 'openvino.inference_engine.constants'

- module-name: 'orderedmultidict' # checksum: 43e79eae
  anti-bloat:
    - description: 'remove manual version file import'
      context:
        - 'import orderedmultidict.__version__'
      replacements_plain:
        "with open(pjoin(dirname(__file__), '__version__.py')) as f": 'if True'
      replacements:
        'exec(f.read(), meta)': "'__version__ = %r' % orderedmultidict.__version__.__version__"

- module-name: 'orjson' # checksum: 539d17a9
  implicit-imports:
    - depends:
        - 'zoneinfo'
        - 'json'
        - 'uuid'

- module-name: 'osgeo' # checksum: b3e12844
  data-files:
    - dirs:
        - 'data'
  implicit-imports:
    - depends:
        - 'osgeo._gdal'
        - 'osgeo._gdalconst'
        - 'osgeo._gdal_array'
        - 'osgeo._gnm'
        - 'osgeo._ogr'
        - 'osgeo._osr'

- module-name: 'overrides.enforce' # checksum: 8ae9a206
  anti-bloat:
    - description: 'disable overrides function checking'
      module_code: |
        from abc import ABCMeta
        class EnforceOverridesMeta(ABCMeta):
          pass
        class EnforceOverrides(metaclass=EnforceOverridesMeta):
          pass

- module-name: 'overrides.overrides' # checksum: 4937ee18
  anti-bloat:
    - description: 'disable overrides function checking'
      change_function:
        '_overrides': "'(lambda method, *args, **kwargs: method)'"

- module-name: 'paddle' # checksum: 11103e47
  dlls:
    - from_filenames:
        relative_path: 'libs'
        prefixes:
          - 'lib'
          - 'mkl'
          - 'war'
          - 'common'

- module-name: 'paddle.base.core' # checksum: a81e13c9
  anti-bloat:
    - replacements_plain:
        "third_lib_path = current_path + os.sep + '..' + os.sep + 'libs'": "third_lib_path = os.path.normpath(current_path + os.sep + '..' + os.sep + 'libs')"
      change_function:
        'set_paddle_lib_path': "'(lambda: None)'"

- module-name: 'paddle.distributed.fleet.launch_utils' # checksum: 69fc840a
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'import paddle.utils.cpp_extension.extension_utils as utils': ''
      replacements:
        "utils.OS_NAME.startswith('darwin')": 'repr(macos)'
        'utils.IS_WINDOWS': 'repr(win32)'
      when: 'not use_setuptools'

- module-name: 'paddle.fluid.core' # checksum: a81e13c9
  anti-bloat:
    - replacements_plain:
        "third_lib_path = current_path + os.sep + '..' + os.sep + 'libs'": "third_lib_path = os.path.normpath(current_path + os.sep + '..' + os.sep + 'libs')"
      change_function:
        'set_paddle_lib_path': "'(lambda: None)'"

- module-name: 'paddle.jit.sot.opcode_translator.skip_files' # checksum: 5721def1
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'import setuptools': ''
        'setuptools,': ''
      when: 'not use_setuptools'
    - description: 'remove unittest reference'
      replacements_plain:
        'import unittest': ''
        'unittest,': ''
      when: 'not use_unittest'

- module-name: 'paddle.utils' # checksum: 9f797eec
  anti-bloat:
    - description: 'remove setuptools usage'
      # TODO: We need a special form of removing import assignments
      replacements_plain:
        'cpp_extension,': ''
      when: 'not use_setuptools'

- module-name: 'paddleocr' # checksum: 90264499
  import-hacks:
    - global-sys-path:
        # This package forces itself into "sys.path" and expects absolute
        # imports from all of these to be available.
        - ''
        - 'ppstructure'
        - 'tools/infer'
        - 'ppocr/postprocess'
        - 'ppocr'
        - 'ppocr/utils/e2e_utils'
        - 'ppocr/utils'
        - 'ppstructure/layout'
        - 'ppstructure/table'

- module-name: 'paddleocr.paddleocr' # checksum: 284bb6fd
  anti-bloat:
    - replacements_re:
        'tools\s*=\s*_import_file\s*\(\s*.*?make_importable=True.*?\s*\)': '"from . import tools"'

- module-name: 'panda3d' # checksum: 14f69755
  data-files:
    - dest_path: '.'
      dirs:
        - 'etc'

  dlls:
    - from_filenames:
        prefixes:
          - 'libpandagl'
          - 'libpandadx9'
          - 'libp3tinydisplay'
          - 'libp3assimp'
          - 'libp3ffmpeg'
          - 'libp3fmod_audio'
          - 'libp3openal_audio'
          - 'libp3ptloader'
          - 'libpandaegg'
      dest_path: '.'

- module-name: 'pandas' # checksum: b4c6897a
  anti-bloat:
    - description: 'remove pandas testing framework'
      replacements_plain:
        'from pandas.util._tester import test': ''
        'import pandas.testing': ''
        'from pandas import testing': 'testing = None'
        'from pandas import api, arrays, errors, io, plotting, testing': 'from pandas import api, arrays, errors, io, plotting'
    - description: 'avoid numba in pandas'
      no-auto-follow:
        'pandas.core._numba.extensions': 'no numba acceleration'
        'lxml': 'no stylesheet support in pandas.io.formats.xml without including lxml'
      when: 'not use_numba'

- module-name: 'pandas._libs' # checksum: 582d52b9
  implicit-imports:
    - depends:
        - 'pandas._libs.tslibs.np_datetime'
        - 'pandas._libs.tslibs.nattype'
        - 'pandas._libs.tslibs.base'

- module-name: 'pandas._libs.testing' # checksum: 9059205
  implicit-imports:
    - depends:
        - 'cmath'

- module-name: 'pandas._libs.tslibs.fields' # checksum: d61757d5
  implicit-imports:
    - depends:
        - 'pandas._config.localization'

- module-name: 'pandas._testing' # checksum: 6fb47e3c
  anti-bloat:
    - description: 'remove pytest testing framework'
      change_function:
        'ensure_clean': "'(lambda: None)'"
        # TODO: See pandas._testing._io TODO as well.
        'external_error_raised': "'(lambda: None)'"
        'network': "'(lambda: None)'"
        'round_trip_localpath': "'(lambda: None)'"
        'round_trip_pathlib': "'(lambda: None)'"

- module-name: 'pandas._testing._io' # checksum: c724cbab
  anti-bloat:
    - description: 'remove pytest testing framework'
      change_function:
        'network': "'(lambda: None)'"
        # TODO: Actually it would be nice to specify "un-callable" rather than
        # wrong signature for cases, where the function is not usable
        # afterwards. That will make sure we have a nice error exit in case,
        # some test code is run an attempts to use it.
        'round_trip_localpath': "'(lambda: None)'"
        'round_trip_pathlib': "'(lambda: None)'"

- module-name: 'pandas.compat._optional' # checksum: a8712d2
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'distutils.version.LooseVersion(version) < minimum_version': 'False'
        'import distutils.version': ''

- module-name: 'pandas.compat.numpy' # checksum: 60947912
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'LooseVersion(_np_version)': '_np_version'
        'from distutils.version import LooseVersion': ''
      replacements_re:
        "LooseVersion\\([\"'](.*?)[\"']\\)": '"\1"'

- module-name: 'pandas.compat.numpy.function' # checksum: b0aad543
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'LooseVersion(__version__) >= LooseVersion("1.17.0")': '__version__ >= "1.17.0"'
        'LooseVersion(_np_version) >= LooseVersion("1.17.0")': '_np_version >= "1.17.0"'
        'from distutils.version import LooseVersion': ''

- module-name: 'pandas.core.arrays._arrow_utils' # checksum: de3b626c
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'LooseVersion(pyarrow.__version__) >= LooseVersion("0.15")': 'pyarrow.__version__ >= "0.15"'
        'from distutils.version import LooseVersion': ''

- module-name: 'pandas.core.arrays.string_arrow' # checksum: 882a5a11
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'LooseVersion(pa.__version__) < "1.0.0"': 'pa.__version__ < "1.0.0"'
        'from distutils.version import LooseVersion': ''

- module-name: 'pandas.core.computation.ops' # checksum: 96e6926c
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'NUMEXPR_VERSION < LooseVersion("2.6.9")': 'False'
        'from distutils.version import LooseVersion': ''

- module-name: 'pandas.core.groupby.groupby' # checksum: fa0161e8
  anti-bloat:
    - description: 'remove numba reference'
      replacements_plain:
        'maybe_use_numba(engine)': 'False'
      when: 'not use_numba'

- module-name: 'pandas.core.util.numba_' # checksum: 5a47bb5e
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'LooseVersion(numba.__version__) >= LooseVersion("0.49.0")': 'True'
        'from distutils.version import LooseVersion': ''

- module-name: 'pandas.core.window' # checksum: e748c8ea
  implicit-imports:
    - depends:
        - 'pandas._libs.window'
        - 'pandas._libs.skiplist'

- module-name: 'pandas.core.window.rolling' # checksum: fa0161e8
  anti-bloat:
    - description: 'remove numba reference'
      replacements_plain:
        'maybe_use_numba(engine)': 'False'
      when: 'not use_numba'

- module-name: 'pandas.io.clipboard' # checksum: 5d37f784
  anti-bloat:
    - description: 'avoid Qt dependency'
      no-auto-follow:
        'qtpy': 'pandas will not be able to use Qt for clipboard interaction'
        'PyQt5': 'pandas will not be able to use Qt for clipboard interaction'
        'PyQt4': 'pandas will not be able to use Qt for clipboard interaction'
      when: 'not plugin("pyqt5") and not plugin("pyqt6") and not plugin("pyside2") and not plugin("pyside6")'
    - description: 'avoid Qt dependency'
      no-auto-follow:
        'qtpy': 'ignore'
        'PyQt5': 'ignore'
        'PyQt4': 'ignore'
      when: 'plugin("no-qt")'

- module-name: 'pandas.io.excel._base' # checksum: 8868dc8b
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'LooseVersion(xlrd.__version__)': 'xlrd.__version__'
        'from distutils.version import LooseVersion': ''

- module-name: 'pandas.io.formats' # checksum: 2e123618
  data-files:
    - dirs:
        - 'templates'

- module-name: 'pandas.io.formats.printing' # checksum: 9548db34
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        'enable_data_resource_formatter': "'(lambda enable: None)'"
      when: 'not use_ipython'

- module-name: 'pandas.io.formats.style' # checksum: 27209d7d
  implicit-imports:
    - depends:
        - 'jinja2'

- module-name: 'pandas.io.orc' # checksum: 6826194c
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'distutils.version.LooseVersion(pyarrow.__version__) < "0.13.0"': 'pyarrow.__version__ < "0.13.0"'
        'import distutils': ''

- module-name: 'pandas.io.parquet' # checksum: 7d41c774
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'LooseVersion(self.api.__version__) >= "0.16"': 'self.api.__version__ >= "0.16"'
        'from distutils.version import LooseVersion': ''

- module-name: 'pandas.plotting._matplotlib.compat' # checksum: 83bfd28f
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'from distutils.version import LooseVersion': ''
        'op(LooseVersion(mpl.__version__), LooseVersion(version))': 'op(mpl.__version__, version)'

- module-name: 'pandas.util' # checksum: 94cd3530
  anti-bloat:
    - description: 'remove pandas testing framework'
      replacements_plain:
        'if name == "testing":': 'if False:'

- module-name: 'pandera' # checksum: 6ac5aa4f
  anti-bloat:
    - description: 'remove dask reference'
      no-auto-follow:
        'dask': 'ignore'
      when: 'not use_dask'

- module-name: 'panel' # checksum: 94e231ad
  data-files:
    - dirs:
        - '_templates'
        - 'template'
        - 'layout'
        - 'theme'
    - patterns:
        - 'package.json'

- module-name: 'pango' # checksum: 7d2edca1
  implicit-imports:
    - depends:
        - 'gobject'

- module-name: 'pangocairo' # checksum: ac364fb1
  implicit-imports:
    - depends:
        - 'pango'
        - 'cairo'

- module-name: 'passlib.apache' # checksum: 4e9f8101
  implicit-imports:
    - depends:
        - 'passlib.handlers.digests'
        - 'passlib.handlers.bcrypt'
        - 'passlib.handlers.md5_crypt'
        - 'passlib.handlers.des_crypt'
        - 'passlib.handlers.ldap_digests'

- module-name: 'passlib.hash' # checksum: 7794431b
  implicit-imports:
    - depends:
        - 'passlib.handlers.sha2_crypt'

- module-name: 'patsy.build' # checksum: 20b0ea72
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test__max_allowed_dim': 'un-callable'
        'test__eval_factor_numerical': 'un-callable'
        'test__eval_factor_categorical': 'un-callable'
        'test__subterm_column_names_iter_and__build_subterm': 'un-callable'
        'test__examine_factor_types': 'un-callable'
      when: 'not use_pytest'

- module-name: 'patsy.builtins' # checksum: f1b048a7
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_Q': 'un-callable'
      when: 'not use_pytest'

- module-name: 'patsy.categorical' # checksum: 864cd3ca
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_CategoricalSniffer': 'un-callable'
        'test_categorical_to_int': 'un-callable'
      when: 'not use_pytest'

- module-name: 'patsy.constraint' # checksum: 27c4f57c
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_LinearConstraint': 'un-callable'
        'test_LinearConstraint_combine': 'un-callable'
        'test__tokenize_constraint': 'un-callable'
        'test_linear_constraint': 'un-callable'
        '_check_lincon': 'un-callable'
      when: 'not use_pytest'

- module-name: 'patsy.contrasts' # checksum: c9f75dca
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_ContrastMatrix': 'un-callable'
        'test__get_level': 'un-callable'
        'test_Poly': 'un-callable'
      when: 'not use_pytest'

- module-name: 'patsy.design_info' # checksum: 1124a13a
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_FactorInfo': 'un-callable'
        'test_DesignInfo': 'un-callable'
        'test_DesignInfo_from_array': 'un-callable'
        'test_design_matrix': 'un-callable'
        'test_SubtermInfo': 'un-callable'

- module-name: 'patsy.eval' # checksum: 48d81ab1
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_VarLookupDict': 'un-callable'
        'test_ast_names_disallowed_nodes': 'un-callable'
        'test_EvalEnvironment_capture_namespace': 'un-callable'
        'test_EvalEnvironment_eval_namespace': 'un-callable'
        'test_EvalEnvironment_eval_flags': 'un-callable'
        'test_EvalEnvironment_subset': 'un-callable'
      when: 'not use_pytest'

- module-name: 'patsy.infix_parser' # checksum: 548bc9c8
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_infix_parse': 'un-callable'

- module-name: 'patsy.mgcv_cubic_splines' # checksum: 53510f22
  anti-bloat:
    - description: 'remove unittest reference'
      change_function:
        'test__map_cyclic_errors': 'un-callable'
        'test__get_all_sorted_knots': 'un-callable'
        'test_crs_errors': 'un-callable'
        'test_te_errors': 'un-callable'
        'test__row_tensor_product_errors': 'un-callable'

- module-name: 'patsy.missing' # checksum: 600a0c86
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_NAAction_basic': 'un-callable'

- module-name: 'patsy.splines' # checksum: cf7e3269
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_bs_errors': 'un-callable'

- module-name: 'patsy.tokens' # checksum: b500d8e5
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_python_tokenize': 'un-callable'
      when: 'not use_pytest'

- module-name: 'patsy.user_util' # checksum: c68f8f74
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_demo_data': 'un-callable'
        'test_LookupFactor': 'un-callable'
      when: 'not use_pytest'

- module-name: 'patsy.util' # checksum: 8cff2f16
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test_pandas_friendly_reshape': 'un-callable'
        'test_wide_dtype_for_and_widen': 'un-callable'
        'assert_no_pickling': 'un-callable'
      when: 'not use_pytest'
    - description: 'remove IPython reference'
      replacements_plain:
        '"IPython" in sys.modules': 'False'
      when: 'not use_ipython'

- module-name: 'pdbp' # checksum: ae97ca9d
  anti-bloat:
    - no-auto-follow:
        '_pytest': 'ignore'
        'pytest': 'ignore'
      when: 'not use_pytest'

- module-name: 'pdfminer' # checksum: a3629ef7
  data-files:
    - dirs:
        - 'cmap'

- module-name: 'pendulum' # checksum: 71704ba2
  data-files:
    - empty_dir_structures:
        - 'locales'
      when: 'version("pendulum") >= (2,)'

- module-name: 'pendulum.locales' # checksum: 9bd33105
  implicit-imports:
    - depends:
        - '.*.locale'

- module-name: 'persistent._compat' # checksum: 74566d77
  anti-bloat:
    - description: 'use own compilation for compatibility'
      replacements_plain:
        "os.environ.get('PURE_PYTHON')": '"1"'

- module-name: 'persistent.ring' # checksum: ff2e6560
  implicit-imports:
    - depends:
        - '_cffi_backend'

- module-name: 'phonenumbers.data' # checksum: ed193c22
  implicit-imports:
    - depends:
        - '.region_*'

- module-name: 'PIL' # checksum: 52d41000
  anti-bloat:
    - description: 'avoid numpy dependency'
      no-auto-follow:
        'numpy': 'ignore'

- module-name: 'PIL._imagingtk' # checksum: 52df48a8
  implicit-imports:
    - depends:
        - 'PIL._tkinter_finder'

- module-name: 'PIL.Image' # checksum: 560d430e
  anti-bloat:
    - description: 'avoid Qt dependency'
      no-auto-follow:
        'PIL.ImageQt': 'PIL will not be able to create Qt image objects'
      when: 'not plugin("pyqt5") and not plugin("pyqt6") and not plugin("pyside2") and not plugin("pyside6") and not plugin("no-qt")'
    - description: 'avoid Qt dependency'
      no-auto-follow:
        'PIL.ImageQt': 'ignore'
      when: 'plugin("no-qt")'
    - description: 'avoid numpy dependency'
      no-auto-follow:
        'numpy': 'ignore'
        'packaging': 'ignore'
  implicit-imports:
    - depends:
        - 'PIL.BlpImagePlugin'
        - 'PIL.BmpImagePlugin'
        - 'PIL.BufrStubImagePlugin'
        - 'PIL.CurImagePlugin'
        - 'PIL.DcxImagePlugin'
        - 'PIL.DdsImagePlugin'
        - 'PIL.EpsImagePlugin'
        - 'PIL.FitsStubImagePlugin'
        - 'PIL.FliImagePlugin'
        - 'PIL.FpxImagePlugin'
        - 'PIL.FtexImagePlugin'
        - 'PIL.GbrImagePlugin'
        - 'PIL.GifImagePlugin'
        - 'PIL.GribStubImagePlugin'
        - 'PIL.Hdf5StubImagePlugin'
        - 'PIL.IcnsImagePlugin'
        - 'PIL.IcoImagePlugin'
        - 'PIL.ImImagePlugin'
        - 'PIL.ImtImagePlugin'
        - 'PIL.IptcImagePlugin'
        - 'PIL.Jpeg2KImagePlugin'
        - 'PIL.JpegImagePlugin'
        - 'PIL.McIdasImagePlugin'
        - 'PIL.MicImagePlugin'
        - 'PIL.MpegImagePlugin'
        - 'PIL.MpoImagePlugin'
        - 'PIL.MspImagePlugin'
        - 'PIL.PalmImagePlugin'
        - 'PIL.PcdImagePlugin'
        - 'PIL.PcxImagePlugin'
        - 'PIL.PdfImagePlugin'
        - 'PIL.PixarImagePlugin'
        - 'PIL.PngImagePlugin'
        - 'PIL.PpmImagePlugin'
        - 'PIL.PsdImagePlugin'
        - 'PIL.SgiImagePlugin'
        - 'PIL.SpiderImagePlugin'
        - 'PIL.SunImagePlugin'
        - 'PIL.TgaImagePlugin'
        - 'PIL.TiffImagePlugin'
        - 'PIL.WebPImagePlugin'
        - 'PIL.WmfImagePlugin'
        - 'PIL.XbmImagePlugin'
        - 'PIL.XpmImagePlugin'
        - 'PIL.XVThumbImagePlugin'

- module-name: 'PIL.ImageShow' # checksum: 1ae9e8b3
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython.display import display as ipython_display': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'PIL.SpiderImagePlugin' # checksum: cb3e1ec1
  anti-bloat:
    - description: 'remove tkinter reference'
      change_function:
        'tkPhotoImage': 'un-callable'
      when: 'not use_tkinter'

- module-name: 'pint.compat' # checksum: 3e1a4258
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'from dask import array as dask_array': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'pint.registry' # checksum: 9ac5f4e6
  data-files:
    - patterns:
        - '*.txt'

- module-name: 'pint.util' # checksum: a21874f7
  anti-bloat:
    - description: 'workaround __new__ decorator issue'
      append_plain: 'SharedRegistryObject.__new__ = staticmethod(SharedRegistryObject.__new__)'

- module-name: 'pip._vendor' # checksum: 49920604
  anti-bloat:
    - description: 'allow pydoc inside pip vendored modules'
      no-auto-follow:
        'pydoc': 'ignore'

- module-name: 'pip._vendor.rich.jupyter' # checksum: b346e77
  # See rich.jupyter
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        'display': 'un-callable'
      when: 'not use_ipython'

- module-name: 'pip._vendor.rich.live' # checksum: f0442975
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'self.console.is_jupyter': 'False'
      when: 'not use_ipython'

- module-name: 'pip._vendor.rich.pretty' # checksum: bdd67adb
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'ip = get_ipython()': 'raise NameError'
      when: 'not use_ipython'

- module-name: 'pkg_resources' # checksum: d0674fc2
  anti-bloat:
    - description: 'avoid using plistlib dependency on non-macOS'
      replacements_plain:
        'import plistlib': ''
      when: 'not macos'
    - description: 'avoid requiring entry points at runtime'
      replacements_plain:
        'def load(self, require=True,': 'def load(self, require=False,'
        'if not require or args or kwargs:': 'if False:'
    - description: 'disable deprecation warnings'
      replacements_plain:
        'warnings.warn(': 'if False: warnings.warn('
  implicit-imports:
    - depends:
        - 'pkg_resources.extern'

- module-name: 'pkg_resources._vendor.appdirs' # checksum: 9d4faeee
  anti-bloat:
    # Keep this the same as for 'appdirs' module
    - description: 'remove pywin32 reference'
      replacements_plain:
        'import win32com.shell': 'raise ImportError'
      change_function:
        '_get_win_folder_with_pywin32': 'un-callable'
      when: 'not use_pywin32 or not win32'

- module-name: 'pkg_resources._vendor.jaraco' # checksum: e791ac67
  implicit-imports:
    - depends:
        - 'pkg_resources._vendor.jaraco.text'
        - 'pkg_resources._vendor.jaraco.functools'
        - 'pkg_resources._vendor.jaraco.context'

- module-name: 'pkg_resources._vendor.jaraco.text' # checksum: 306c0234
  anti-bloat:
    - description: 'remove useless data file dependency'
      replacements_plain:
        "lorem_ipsum: str = files(__name__).joinpath('Lorem ipsum.txt').read_text()": ''

- module-name: 'pkg_resources._vendor.packaging' # checksum: 595be034
  implicit-imports:
    - depends:
        - 'pkg_resources._vendor.packaging.version'
        - 'pkg_resources._vendor.packaging.specifiers'
        - 'pkg_resources._vendor.packaging.requirements'

- module-name: 'pkg_resources._vendor.pyparsing' # checksum: 8caa976e
  anti-bloat:
    - description: 'remove pdb usage'
      change_function:
        'setBreak': "'(lambda self: None)'"

- module-name: 'platform_utils.paths' # checksum: e9e64aec
  anti-bloat:
    - description: 'workaround embedded data files bug'
      change_function:
        'embedded_data_path': "'(lambda: app_path())'"

- module-name: 'playwright' # checksum: 3c59447d
  data-files:
    - patterns:
        - '**/*.json'
        - '**/*.js'
        - '**/*.ts'
        - '**/*.html'
        - '**/*.css'
        - '**/*.svg'

  dlls:
    - from_filenames:
        relative_path: 'driver'
        prefixes:
          - 'node'
        executable: 'yes'

- module-name: 'playwright._impl._transport' # checksum: 708d0cf7
  anti-bloat:
    - description: 'fix globals.get() usage'
      replacements_plain:
        'globals().get("_compiled__")': 'globals().get("__compiled__")'
      when: 'version("playwright") >= (1, 45, 0)'

    - description: 'support for older versions of playwright'
      replacements_plain:
        'if getattr(sys, "frozen", False):': 'if getattr(sys, "frozen", False) or "__compiled__" in globals():'
      when: 'version("playwright") < (1, 45, 0)'

- module-name: 'playwright_stealth' # checksum: 26b9c88c
  data-files:
    - dirs:
        - '.'

- module-name: 'plotly' # checksum: ecc071a2
  data-files:
    - dirs:
        - 'package_data'
        - 'package_data/templates'
        - 'package_data/datasets'
  implicit-imports:
    - depends:
        - 'plotly.version'

- module-name: 'plotly.graph_objects' # checksum: bf6dd64b
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'import ipywidgets as _ipywidgets': 'raise ImportError'
        'import ipywidgets': 'raise ImportError'
        # Disable lazy loading implicit imports.
        'sys.version_info < (3, 7) or TYPE_CHECKING': 'True'
      when: 'not use_ipython'

- module-name: 'plotly.graph_objs' # checksum: bf6dd64b
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'import ipywidgets as _ipywidgets': 'raise ImportError'
        'import ipywidgets': 'raise ImportError'
        # Disable lazy loading implicit imports.
        'sys.version_info < (3, 7) or TYPE_CHECKING': 'True'
      when: 'not use_ipython'

- module-name: 'plotly.io' # checksum: bc9ae932
  anti-bloat:
    - description: 'include plotly templates'
      replacements_plain:
        # Disable lazy loading implicit imports.
        'sys.version_info < (3, 7) or TYPE_CHECKING': 'True'

- module-name: 'plotly.validator_cache' # checksum: 4e4fa5ab
  implicit-imports:
    - depends:
        # Disable lazy loading implicit imports.
        - 'plotly.validators.*'
        - 'plotly.validators.*.*'
        # Maybe misplaced those here. TODO: Also make recursive dependency a thing.
        - 'plotly.graph_objs.layout.*'
        - 'plotly.graph_objs.layout.*.*'

- module-name: 'plumbum.cli.progress' # checksum: 8a7f9d4c
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        '__IPYTHON__': 'raise NameError'
      no-auto-follow:
        'IPython': 'ignore'
        'ipywidgets': 'ignore'
      when: 'not use_ipython'

- module-name: 'plumbum.colorlib' # checksum: 29865712
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'plumbum.colorlib._ipython_ext': 'ignore'
      when: 'not use_ipython'

- module-name: 'plyer' # checksum: c06d8897
  implicit-imports:
    - depends:
        - 'plyer.platforms.win.*'
      when: 'win32'
    - depends:
        - 'plyer.platforms.linux.*'
      when: 'linux'
    - depends:
        - 'plyer.platforms.macosx.*'
      when: 'macos'
    - depends:
        - 'plyer.platforms.android.*'
      when: 'android'

- module-name: 'polars' # checksum: d072aa03
  implicit-imports:
    - depends:
        - 'numpy.core.multiarray'

- module-name: 'polars.lazyframe.frame' # checksum: e9afa6bc
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython.display import SVG, display': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'polars.series.utils' # checksum: 65b85a57
  anti-bloat:
    - description: 'workaround for compiled method support'
      append_plain: |
        _orig_is_empty_method = _is_empty_method
        def _is_empty_method(func):
          if hasattr(func, "__compiled_constant__"):
              return getattr(func, "__compiled_constant__") is None

          return _orig_is_empty_method(func)

- module-name: 'polars.utils.various' # checksum: 681007fe
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython import get_ipython': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'polyfactory.fields' # checksum: 16abf322
  anti-bloat:
    - description: 'remove pytest reference'
      no-auto-follow:
        'polyfactory.pytest_plugin': 'ignore'
      when: 'not use_pytest'

- module-name: 'pony' # checksum: 3776d9aa
  implicit-imports:
    - depends:
        - 'pony.orm.dbproviders.*'

- module-name: 'pooch' # checksum: e07f91ff
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'test': 'un-callable'
      when: 'not use_pytest'

- module-name: 'ppocr.utils.e2e_utils.pgnet_pp_utils' # checksum: 935978cf
  anti-bloat:
    - replacements_plain:
        'from extract_textpoint_slow import *': 'from .extract_textpoint_slow import *'
        'from extract_textpoint_fast import generate_pivot_list_fast, restore_poly': 'from .extract_textpoint_fast import generate_pivot_list_fast, restore_poly'
  import-hacks:
    - global-sys-path:
        # This package forces itself into "sys.path" and expects absolute
        # imports to be available.
        - ''

- module-name: 'preshed.counter' # checksum: 1e9d1989
  anti-bloat:
    - description: 'do not follow tests'
      no-auto-follow:
        'preshed.tests': 'ignore'
  implicit-imports:
    - depends:
        - 'preshed.cymem'

- module-name: 'psutil' # checksum: 24d7eb52
  anti-bloat:
    - description: 'resolve platform specific imports at compile time'
      context:
        - 'import psutil'
      replacements:
        'from ._common import AIX': "'AIX = %r' % psutil.AIX"
        'from ._common import BSD': "'BSD = %r' % psutil.BSD"
        'from ._common import FREEBSD': "'FREEBSD = %r' % + psutil.FREEBSD"
        'from ._common import LINUX': "'LINUX = %r' % + psutil.LINUX"
        'from ._common import MACOS': "'MACOS = %r' % + psutil.MACOS"
        'from ._common import NETBSD': "'NETBSD = %r' % + psutil.NETBSD"
        'from ._common import OPENBSD': "'OPENBSD = %r' % + psutil.OPENBSD"
        'from ._common import OSX': "'OSX = %r' % psutil.OSX"
        'from ._common import POSIX': "'POSIX = %r' % psutil.POSIX"
        'from ._common import SUNOS': "'SUNOS = %r' % psutil.SUNOS"
        'from ._common import WINDOWS': "'WINDOWS = %r' % psutil.WINDOWS"

- module-name: 'py_mini_racer' # checksum: d45d7bdb
  data-files:
    - patterns:
        - 'icudtl.dat'
        - 'snapshot_blob.bin'

  dlls:
    - from_filenames:
        prefixes:
          - 'mini_racer'
      when: 'win32'
    - from_filenames:
        prefixes:
          - 'libmini_racer'
      when: 'not win32'

- module-name: 'pyarrow' # checksum: 9da51f71
  anti-bloat:
    - description: 'remove setuptools usage'
      no-auto-follow:
        'setuptools_scm': 'ignore'
      when: 'not use_setuptools'

- module-name: 'pyarrow.dataset' # checksum: a16a7618
  implicit-imports:
    - depends:
        - 'pyarrow._acero'
        - 'pyarrow._json'
        - 'pyarrow._csv'

- module-name: 'pyarrow.lib' # checksum: 302be0c2
  implicit-imports:
    - depends:
        - 'pyarrow.vendored.version'

- module-name: 'pyarrow.vendored.docscrape' # checksum: b2d4bca8
  anti-bloat:
    - description: 'remove sphinx reference'
      replacements_plain:
        "if 'sphinx' in sys.modules:": 'if False:'
    - description: 'workaround for MSVC bug with scipy docscrape 1.8.x'
      replacements_plain:
        "return l2.startswith('-'*len(l1)) or l2.startswith('='*len(l1))": "r = l2.startswith('-'*len(l1)) or l2.startswith('='*len(l1)); return r"
      bloat-mode-overrides:
        'pydoc': 'allow'

- module-name: 'pyaxmlparser.resources' # checksum: ec8efe56
  data-files:
    - patterns:
        - 'public.xml'

- module-name: 'pycountry' # checksum: 862bda54
  data-files:
    - dirs:
        - 'databases'

- module-name: 'pycparser.c_parser' # checksum: 908ed561
  implicit-imports:
    - depends:
        - 'pycparser.yacctab'
        - 'pycparser.lextab'

- module-name: 'pycrfsuite' # checksum: 452bfc81
  dlls:
    - from_filenames:
        prefixes:
          - '_pycrfsuite'

  implicit-imports:
    - depends:
        - 'pycrfsuite._dumpparser'
        - 'pycrfsuite._logparser'

- module-name: 'pydantic' # checksum: 190f6343
  implicit-imports:
    - depends:
        - 'pydantic.typing'
        - 'pydantic.fields'
        - 'pydantic.utils'
        - 'pydantic.schema'
        - 'pydantic.env_settings'
        - 'pydantic.main'
        - 'pydantic.error_wrappers'
        - 'pydantic.validators'
        - 'pydantic.mypy'
        - 'pydantic.version'
        - 'pydantic.types'
        - 'pydantic.color'
        - 'pydantic.parse'
        - 'pydantic.json'
        - 'pydantic.datetime_parse'
        - 'pydantic.dataclasses'
        - 'pydantic.class_validators'
        - 'pydantic.networks'
        - 'pydantic.errors'
        # Not part of their general lazy loader, the aboves should become that.
        - 'pydantic.deprecated.decorator'

- module-name: 'pydantic.errors' # checksum: edc1c868
  implicit-imports:
    - depends:
        - 'decimal'

- module-name: 'pydantic.json' # checksum: e9d3ea5
  implicit-imports:
    - depends:
        - 'uuid'

- module-name: 'pydantic.typing' # checksum: 20909c2d
  implicit-imports:
    - depends:
        - 'typing_extensions'

- module-name: 'pydantic.v1.version' # checksum: e038cd11
  anti-bloat:
    - description: 'remove setuptools usage via cython'
      append_plain: |
        compiled = True
      no-auto-follow:
        'cython': 'ignore'
      when: 'not use_setuptools'

- module-name: 'pyfiglet' # checksum: b8621a7d
  implicit-imports:
    - depends:
        - '.fonts'

- module-name: 'pyfiglet.fonts' # checksum: 26b9c88c
  data-files:
    - dirs:
        - '.'

- module-name: 'pygame' # checksum: 64efeec5
  data-files:
    - patterns:
        - 'freesansbold.ttf'
    - patterns:
        - 'pygame_icon_mac.bmp'
      when: 'macos'
    - patterns:
        - 'pygame_icon.bmp'
      when: 'not macos'

  anti-bloat:
    - no-auto-follow:
        'pygame.tests': 'ignore'
      when: 'not use_unittest'
    - no-auto-follow:
        'cv2': 'camera functionality via OpenCV-Python (cv2)'
    - change_function:
        'packager_imports': 'un-callable'
  implicit-imports:
    - depends:
        - 'pygame.*'

  options:
    checks:
      - description: 'PyGame is a GUI framework'
        macos_bundle: 'recommend'

- module-name: 'pygame_menu' # checksum: ae5ca038
  data-files:
    - dirs:
        - 'resources'

- module-name: 'pygeos._geometry' # checksum: 6a3a4737
  implicit-imports:
    - depends:
        - 'pygeos._geos'

- module-name: 'pyglet' # checksum: 942fae5e
  implicit-imports:
    - depends:
        - 'pyglet.app'
        - 'pyglet.canvas'
        - 'pyglet.clock'
        - 'pyglet.com'
        - 'pyglet.event'
        - 'pyglet.font'
        - 'pyglet.gl'
        - 'pyglet.graphics'
        - 'pyglet.input'
        - 'pyglet.image'
        - 'pyglet.lib'
        - 'pyglet.media'
        - 'pyglet.model'
        - 'pyglet.resource'
        - 'pyglet.sprite'
        - 'pyglet.shapes'
        - 'pyglet.text'
        - 'pyglet.window'

- module-name: 'pygments.styles' # checksum: 1851bf53
  implicit-imports:
    - depends:
        - '.*'

- module-name: 'pygsheets' # checksum: 6aaa99cc
  data-files:
    - dirs:
        - 'data'

- module-name: 'pymoo.cython' # checksum: 158685c1
  implicit-imports:
    - depends:
        - 'pymoo.cython.*'

- module-name: 'pymoo.gradient.toolbox' # checksum: ef4099af
  implicit-imports:
    - depends:
        - 'autograd.numpy'

- module-name: 'pymssql' # checksum: 1767fe26
  implicit-imports:
    - depends:
        - 'pymssql._mssql'

- module-name: 'pyocd' # checksum: db396ebb
  data-files:
    - patterns:
        - 'debug/sequences/*.lark'
        - 'debug/svd/*.zip'
    - include-metadata:
        - 'pyocd'
  implicit-imports:
    - depends:
        - 'pyocd.probe.*'
        - 'pyocd.rtos.*'
        - 'pylink-square'
        - 'pyusb'

- module-name: 'pyodbc' # checksum: 893567d5
  implicit-imports:
    - depends:
        - 'hashlib'
        - 'decimal'

- module-name: 'pyparsing' # checksum: 8caa976e
  anti-bloat:
    - description: 'remove pdb usage'
      change_function:
        'setBreak': "'(lambda self: None)'"

- module-name: 'pyparsing.core' # checksum: d71d2c07
  anti-bloat:
    - description: 'remove pdb reference'
      change_function:
        'set_break': 'un-callable'

- module-name: 'pypdfium2' # checksum: 5f480b6b
  data-files:
    - patterns:
        - 'version.json'

- module-name: 'pypdfium2_raw' # checksum: 2df23eb6
  data-files:
    - patterns:
        - 'version.json'

  dlls:
    - from_filenames:
        prefixes:
          - 'pdfium'
          - 'libpdfium'

- module-name: 'pyphen' # checksum: aa53bb0a
  data-files:
    - dirs:
        - 'dictionaries'
  anti-bloat:
    - description: 'remove doctest usage'

- module-name: 'pyproj' # checksum: 701017c5
  data-files:
    - dirs:
        - 'proj_dir'

- module-name: 'pypylon' # checksum: 333ba80f
  data-files:
    - patterns:
        - '**/*.sig'
        - '**/*.info'
        - '**/*.lic'
        - '**/*.pcal'
        - '**/*.pdf'
        - '**/*.omc'
        - '**/*.hotc'
  dlls:
    - from_filenames:
        prefixes:
          - '*Basler'
          - 'Pylon'
          - 'gxapi'
          - 'uxapi'
    - from_filenames:
        relative_path: 'pylonDataProcessingPlugins'
        prefixes:
          - 'Pylon'
    - from_filenames:
        relative_path: 'DataProcessingPluginsB'
        prefixes:
          - 'Pylon'

- module-name: 'PyQt5' # checksum: d5071a1a
  data-files:
    - empty_dirs:
        - 'Qt5'
        - 'Qt'

  options:
    checks:
      - description: 'PyQt5 is a GUI framework'
        macos_bundle: 'recommend'
        when: 'use_pyqt5'
      - description: 'PyQt5 cannot be used without bundle'
        macos_bundle: 'yes'
        when: 'macos and not is_conda_package("pyqt5") and use_pyqt5'

  import-hacks:
    - acceptable-missing-dlls:
        - 'libqpdf'

- module-name: 'PyQt5.pylupdate' # checksum: 4fa39a08
  implicit-imports:
    - depends:
        - 'PyQt5.QtCore'

- module-name: 'PyQt5.pyrcc' # checksum: 4fa39a08
  implicit-imports:
    - depends:
        - 'PyQt5.QtCore'

- module-name: 'PyQt6' # checksum: 9ddfb6bb
  options:
    checks:
      - description: 'PyQt6 is a GUI framework'
        macos_bundle: 'recommend'
        when: 'use_pyqt6'
      - description: 'PyQt6 on macOS is not supported, use PySide6 instead'
        support_info: 'error'
        when: 'macos and use_pyqt6'

- module-name: 'pyqtgraph' # checksum: cddd333f
  data-files:
    - dirs:
        - 'colors'
        - 'icons'

- module-name: 'pyqtgraph.parametertree.interactive' # checksum: acb233c9
  anti-bloat:
    - description: 'allow pydoc not really used bloatful'
      bloat-mode-overrides:
        # TODO: However it would be nice to be able to force-inline a subset of
        # functions for pydoc, like "pydoc.splitdoc" in an easy fashion.
        'pydoc': 'allow'

- module-name: 'pyqtgraph.util.numba_helper' # checksum: 731dde6
  anti-bloat:
    - description: 'remove numba reference'
      replacements_plain:
        'getConfigOption("useNumba")': 'False'
      when: 'not use_numba'

- module-name: 'pyqtlet2' # checksum: 9b0b232a
  data-files:
    - dirs:
        - 'web'

- module-name: 'pyreadstat._readstat_parser' # checksum: be58ab4d
  implicit-imports:
    - depends:
        - 'pandas'

- module-name: 'pyreadstat.pyreadstat' # checksum: 7ce70624
  implicit-imports:
    - depends:
        - 'pyreadstat._readstat_writer'
        - 'pyreadstat.worker'
        - 'multiprocessing'

- module-name: 'pyrect' # checksum: 7a072535
  anti-bloat:
    - description: 'remove doctest reference'
      replacements_plain:
        'import doctest': ''

- module-name: 'pyscf' # checksum: ec3da3c3
  anti-bloat:
    - description: 'remove useless plugin warning'
      replacements_plain:
        "not all('/site-packages/' in p for p in __path__[1:])": 'False'

- module-name: 'pyscf.agf2._agf2' # checksum: c57648ff
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libagf2'

- module-name: 'pyscf.ao2mo' # checksum: b1eae599
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libao2mo'

- module-name: 'pyscf.cc._ccsd' # checksum: 2f8edb26
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libcc'

- module-name: 'pyscf.df.df_jk' # checksum: 9c821128
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libri'

- module-name: 'pyscf.dft' # checksum: 2f70b34e
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libdft'

- module-name: 'pyscf.dft.libxc' # checksum: f3d52af4
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libxc_itrf'

- module-name: 'pyscf.dft.xcfun' # checksum: c7fd93f9
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libxcfun_itrf'

- module-name: 'pyscf.fci' # checksum: 3285ffbc
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libfci'

- module-name: 'pyscf.gto' # checksum: 251ddee
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libcgto'

- module-name: 'pyscf.gto.basis' # checksum: d9c54469
  data-files:
    - patterns:
        - '**/*.dat'

- module-name: 'pyscf.lib.numpy_helper' # checksum: 6a0e9d08
  dlls:
    - from_filenames:
        prefixes:
          - 'libnp_helper'

- module-name: 'pyscf.mcscf' # checksum: a8cbc5fd
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libmcscf'

- module-name: 'pyscf.mrpt.nevpt2' # checksum: a8cbc5fd
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libmcscf'

- module-name: 'pyscf.pbc' # checksum: d3e0f821
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libpbc'

- module-name: 'pyscf.scf._vhf' # checksum: 801ef150
  dlls:
    - from_filenames:
        relative_path: '../lib'
        prefixes:
          - 'libcvhf'

- module-name: 'PySide2' # checksum: c07d1551
  implicit-imports:
    - post-import-code:
        - |
          def patched_disconnect(self, slot=None):
              if hasattr(slot, "im_func"):
                  if hasattr(slot.im_func, "__compiled__"):
                      patched_disconnect._protected = getattr(patched_disconnect, "_protected", [])
                      patched_disconnect._protected.append(slot)

              return orig_disconnect(self, slot)

          from PySide2 import QtCore
          orig_disconnect = QtCore.SignalInstance.disconnect
          QtCore.SignalInstance.disconnect = patched_disconnect

  options:
    checks:
      - description: 'PySide2 is a GUI framework'
        macos_bundle: 'recommend'
        when: 'use_pyside2'
      - description: 'PySide2 cannot be signed unless onefile'
        macos_bundle_as_onefile: 'yes'
        when: 'macos and use_pyside2'
  import-hacks:
    - find-dlls-near-module:
        - 'shiboken2'

- module-name: 'PySide6' # checksum: f4fffb09
  implicit-imports:
    - post-import-code:
        - |
          def patched_disconnect(self, slot=None):
              if hasattr(slot, "im_func"):
                  if hasattr(slot.im_func, "__compiled__"):
                      patched_disconnect._protected = getattr(patched_disconnect, "_protected", [])
                      patched_disconnect._protected.append(slot)

              return orig_disconnect(self, slot)

          def patched_connect(self, slot, type=None):
              type = type or QtCore.Qt.ConnectionType.AutoConnection
              if hasattr(slot, "im_func"):
                  if hasattr(slot.im_func, "__compiled__"):
                      patched_connect._protected = getattr(patched_connect, "_protected", [])
                      patched_connect._protected.append(slot)

                      if not slot.im_func.__name__.startswith("_pyside6_workaround_"):
                        slot.im_func.__name__ = "_pyside6_workaround_" + slot.im_func.__name__

                      try:
                        setattr(slot.im_self.__class__, slot.im_func.__name__, slot.im_func)
                      except Exception:
                        pass

              return orig_connect(self, slot, type)

          from PySide6 import QtCore
          orig_disconnect = QtCore.SignalInstance.disconnect
          QtCore.SignalInstance.disconnect = patched_disconnect
          orig_connect = QtCore.SignalInstance.connect
          QtCore.SignalInstance.connect = patched_connect

  options:
    checks:
      - description: 'PySide6 is a GUI framework'
        macos_bundle: 'recommend'
        when: 'use_pyside6'

  import-hacks:
    - find-dlls-near-module:
        - 'shiboken6'
    - acceptable-missing-dlls:
        - 'libeffectsplugin'
        - 'libeffects'
        - 'libpdfquickplugin'
        - 'libqpdf'
        - 'libqtquick3dhelpersimplplugin'
        - 'libquick3dspatialaudioplugin'
        - 'libqtvkbcomponentsplugin'
        - 'libqtvkbsettingsplugin'
        - 'libqsqlodbc'
        - 'libpq.5'
        - 'libffmpegmediaplugin'
        - 'libqtquickcontrols2fluentwinui3styleimplplugin'
        - 'libqtquickcontrols2fluentwinui3styleplugin'
        - 'libqtwebviewquickplugin'
        - 'libqtquickscene3dplugin'
        - 'libqsqlmimer'

- module-name: 'PySide6.QtCore' # checksum: be0d99ac
  implicit-imports:
    - depends:
        - 'PySide6.support.deprecated'

- module-name: 'PySide6.QtHttpServer' # checksum: edeef99d
  implicit-imports:
    - depends:
        - 'PySide6.QtConcurrent'
        - 'PySide6.QtWebSockets'

- module-name: 'PySide6QtAds' # checksum: 6d4dfaff
  implicit-imports:
    - depends:
        - 'PySide6.QtWidgets'
  import-hacks:
    - find-dlls-near-module:
        - 'shiboken'
        - 'PySide6'

- module-name: 'pysnmp.smi' # checksum: 6af35c04
  data-files:
    - patterns:
        - 'mibs/**/*.py'
  implicit-imports:
    - depends:
        - 'pysnmp.smi.exval'
        - 'pysnmp.cache'
        - 'pysnmp.smi.mibs'
        - 'pysnmp.smi.mibs.instances'

- module-name: 'python_utils.terminal' # checksum: 681007fe
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython import get_ipython': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'pythonnet' # checksum: e461ee8
  # This is a dotnet assembly, not a platform DLL, esp. not on Linux, macOS,
  # etc.
  data-files:
    - patterns:
        - 'runtime/Python.Runtime.dll'

- module-name: 'pytorch_lightning' # checksum: ffc61f79
  data-files:
    - patterns:
        - 'version.info'

- module-name: 'pytorch_lightning.utilities.imports' # checksum: a95c6e78
  anti-bloat:
    # TODO: It would be better if we had a form of replacements, e.g. through
    # the use of variables, that would use the result of the comparison, or if
    # we could force inline of "compare_version" with constant values.
    - description: 'workaround for dynamic rich version check'
      replacements_plain:
        'compare_version("rich", operator.ge, "10.2.2")': 'True'
      when: 'version("rich") is not None and version("rich") >= (10, 2, 2)'
    - description: 'workaround for dynamic rich version check'
      replacements_plain:
        'compare_version("rich", operator.ge, "10.2.2")': 'False'
      when: 'version("rich") is not None and version("rich") < (10, 2, 2)'

- module-name: 'pytorch_lightning.utilities.model_helpers' # checksum: b4f61938
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'from unittest.mock import Mock': ''
        'isinstance(instance_attr, Mock)': 'False'

- module-name: 'pytz' # checksum: 5e067c2f
  data-files:
    - dirs:
        - 'zoneinfo'
  anti-bloat:
    - description: 'remove docutils usage'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'pytzdata' # checksum: 929ce0ca
  data-files:
    - dirs:
        - 'zoneinfo'

- module-name: 'pyvista' # checksum: e4ffea88
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from pyvista.jupyter import set_jupyter_backend, PlotterITK': 'set_jupyter_backend = PlotterITK = None'
      when: 'not use_ipython'

- module-name: 'pyvista.core.dataobject' # checksum: b73d0183
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython.display import HTML, display as _display': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'pyvista.plotting.mapper' # checksum: b64967f7
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from ..jupyter.pv_ipygany import check_colormap': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'pyvista.plotting.plotting' # checksum: cb73cde5
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from ..jupyter.notebook import handle_plotter': 'raise ImportError'
      change_function:
        # This uses IPython to convert to HTML.
        '_save_panel': 'un-callable'
        'export_html': 'un-callable'
        'to_pythreejs': 'un-callable'
      when: 'not use_ipython'

- module-name: 'pyvista.themes' # checksum: e2134fcd
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from pyvista.jupyter import _validate_jupyter_backend': '_validate_jupyter_backend = str'
      when: 'not use_ipython'

- module-name: 'pyviz_comms' # checksum: 734e12f1
  data-files:
    - dirs:
        - 'labextension'
    - patterns:
        - 'notebook.js'

- module-name: 'pywt' # checksum: 43ac1e30
  data-files:
    - dirs:
        - 'data'
  implicit-imports:
    - depends:
        - 'pywt._extensions'

- module-name: 'pywt._extensions' # checksum: 5436eb56
  implicit-imports:
    - depends:
        - 'pywt._extensions._cwt'
        - 'pywt._extensions._dwt'
        - 'pywt._extensions._pywt'
        - 'pywt._extensions._swt'

- module-name: 'pywt._pytesttester' # checksum: 51b37a6d
  anti-bloat:
    - description: 'remove pywt testing framework'
      module_code: |
        class PytestTester:
          def __init__(self, name):
            pass

- module-name: 'pywt._utils' # checksum: 9e6d9638
  anti-bloat:
    - description: 'remove pytest reference (via nose)'
      no-auto-follow:
        'nose': 'ignore'
      when: 'not use_pytest'

- module-name: 'pyzbar' # checksum: a2a725bb
  dlls:
    - from_filenames:
        prefixes:
          - 'libiconv'
          - 'libzbar'

- module-name: 'qt_material' # checksum: 39ea93cb
  data-files:
    - dirs:
        - 'fonts'
        - 'resources'
        - 'themes'
    - patterns:
        - '*.ui'
        - '*.xml'
        - '*.svg'
        - '*.template'
        - '*.ttf'

- module-name: 'qtawesome' # checksum: 35ad0fe8
  data-files:
    - dirs:
        - 'fonts'

- module-name: 'randomname' # checksum: 1caca7c5
  data-files:
    - dirs:
        - 'wordlists'

- module-name: 'rapidfuzz' # checksum: 2248cb4c
  implicit-imports:
    - depends:
        - '.utils_py'
        - '.utils_cpp'
        - '.fuzz_py'
        - '.fuzz_cpp'
        - '.process_cdist_py'
        - '.process_cdist_cpp'
        - '.process_py'
        - '.process_cpp'
        - '.string_metric_py'
        - '.string_metric_cpp'

- module-name: 'rapidfuzz.distance' # checksum: 42cced33
  implicit-imports:
    - depends:
        - '._initialize_py'
        - '._initialize_cpp'
        - '.Hamming_py'
        - '.Hamming_cpp'
        - '.Indel_py'
        - '.Indel_cpp'
        - '.LCSseq_py'
        - '.LCSseq_cpp'
        - '.Levenshtein_py'
        - '.Levenshtein_cpp'
        - '.DamerauLevenshtein_py'
        - '.DamerauLevenshtein_cpp'
        - '.OSA_cpp'
        - '.OSA_py'
        - '.Jaro_py'
        - '.Jaro_cpp'
        - '.JaroWinkler_py'
        - '.JaroWinkler_cpp'
        - '.Postfix_py'
        - '.Postfix_cpp'
        - '.Prefix_py'
        - '.Prefix_cpp'
        - '.metrics_cpp'
        - '.metrics_py'

- module-name: 'rasterio._io' # checksum: 5acc49ae
  implicit-imports:
    - depends:
        - 'rasterio.sample'
        - 'rasterio.vrt'

- module-name: 'rasterio._warp' # checksum: 178bc482
  implicit-imports:
    - depends:
        - 'rasterio._features'

- module-name: 'rdkit' # checksum: e6ef3220
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'kernel_name = get_ipython().__class__.__name__': 'raise NameError'
      when: 'not use_ipython'

- module-name: 'reportlab.rl_config' # checksum: 8e035d91
  implicit-imports:
    - depends:
        - 'reportlab.rl_settings'

- module-name: 'requests' # checksum: 3760c9db
  implicit-imports:
    - depends:
        - 'urllib3'
        - 'idna'
        - 'chardet'
    - depends:
      # In case chardet is not there, another package is used.
        - 'charset_normalizer'
      when: 'not has_module("chardet")'

- module-name: 'requests_toolbelt._compat' # checksum: aed85e8e
  implicit-imports:
    - depends:
        - 'urllib3'
        - 'urllib3._collections'
        - 'urllib3.connection'
        - 'urllib3.connection.appengine'
        - 'urllib3.connectionpool'
        - 'urllib3.contrib'
        - 'urllib3.contrib.appengine'
        - 'urllib3.exceptions'
        - 'urllib3.fields'
        - 'urllib3.filepost'
        - 'urllib3.packages'
        - 'urllib3.packages.six'
        - 'urllib3.packages.ssl_match_hostname'
        - 'urllib3.poolmanager'
        - 'urllib3.request'
        - 'urllib3.response'
        - 'urllib3.util'
        - 'urllib3.util.connection'
        - 'urllib3.util.queue'
        - 'urllib3.util.request'
        - 'urllib3.util.response'
        - 'urllib3.util.retry'
        - 'urllib3.util.ssl_'
        - 'urllib3.util.timeout'
        - 'urllib3.util.url'
        - 'urllib3.util.wait'
        - 'urllib.error'
        - 'urllib.parse'
        - 'urllib.request'
        - 'urllib.response'

- module-name: 'rich.jupyter' # checksum: b346e77
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        'display': 'un-callable'
      when: 'not use_ipython'

- module-name: 'rich.live' # checksum: f0442975
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'self.console.is_jupyter': 'False'
      when: 'not use_ipython'

- module-name: 'rich.pager' # checksum: 8ba7ab27
  anti-bloat:
    - description: 'allow pydoc inside rich pager, uses it'
      bloat-mode-overrides:
        'pydoc': 'allow'

- module-name: 'rich.pretty' # checksum: bdd67adb
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'ip = get_ipython()': 'raise NameError'
      when: 'not use_ipython'

- module-name: 'rlottie_python.rlottie_wrapper' # checksum: 3b2bad36
  dlls:
    # TODO: The "lib" difference out to be handled automatically.
    - from_filenames:
        prefixes:
          - 'rlottie'
      when: 'win32'
    - from_filenames:
        prefixes:
          - 'librlottie'
      when: 'not win32'

- module-name: 'rtree.finder' # checksum: 312489cf
  variables:
    setup_code:
      - 'import ctypes, os, rtree.finder'
      - 'orig_LoadLibrary = ctypes.cdll.LoadLibrary'
      - 'val = []'
      - 'def my_LoadLibrary(filename):'
      - '    val.append(filename)'
      - '    return orig_LoadLibrary(filename)'
      - 'ctypes.cdll.LoadLibrary = my_LoadLibrary'
      - 'rtree.finder.load()'
    declarations:
      'rtree_dll_source_paths': 'val[0]'
      'rtree_dll_names': '"libspatialindex" + (".dll" if os.name=="nt" else ".so")'
  dlls:
    - from_sources:
        'source_paths':
          - 'get_variable("rtree_dll_source_paths")'
        'dest_names':
          - 'get_variable("rtree_dll_names")'
  anti-bloat:
    - description: 'workaround libspatialindex DLL name changes for arches'
      replacements_plain:
        'str(path / lib_name)': 'str(path)'
        'os.environ["PATH"] = ";".join([str(path), oldenv])': 'os.environ["PATH"] = ";".join([str(path.parent), oldenv])'
      when: 'win32'

  implicit-imports:
    - pre-import-code:
        - |
          import os
          os.environ["SPATIALINDEX_C_LIBRARY"] = os.path.join(os.path.dirname(__file__), "libspatialindex" + (".dll" if os.name=="nt" else ".so"))

- module-name: 'scapy.all' # checksum: 8a9944ce
  implicit-imports:
    - depends:
        - 'scapy.layers.*'

- module-name: 'scipy' # checksum: 831d0852
  data-files:
    - patterns:
        - 'stats/_sobol_direction_numbers.npz' # for scipy.stats._sobol._initialize_direction_numbers

  dlls:
    - from_filenames:
        relative_path: '.libs'
        prefixes:
          - ''
    - from_filenames:
        relative_path: 'extra_dll'
        prefixes:
          - ''
      when: 'win32'

  anti-bloat:
    - description: 'remove optional package references'
      no-auto-follow:
        'cupy': 'ignore'
        'torch': 'ignore'
        'jax': 'ignore'
        'dask': 'ignore'
        'matplotlib': 'ignore'
        'sparse': 'ignore'
        'ndonnx': 'ignore'
        'yaml': 'ignore'
      bloat-mode-overrides:
        'pydoc': 'allow'

- module-name: 'scipy._distributor_init' # checksum: c312b65
  anti-bloat:
    - description: 'workaround for scipy DLL loading'
      append_plain: |
        import os
        if os.name == "nt":
          import glob
          libs_dir = os.path.join(os.path.dirname(__file__), '..', 'scipy.libs')
          for filename in glob.glob(os.path.join(libs_dir, '*openblas*dll')):
            WinDLL(filename)
      when: 'win32 and standalone and (1,14) > version("scipy") >= (1,9,2)'

- module-name: 'scipy._lib' # checksum: 4063ed73
  implicit-imports:
    - depends:
        - 'scipy._lib.messagestream'

- module-name: 'scipy._lib._docscrape' # checksum: e4a58ca7
  anti-bloat:
    - description: 'remove sphinx reference'
      replacements_plain:
        "if 'sphinx' in sys.modules:": 'if False:'
    - description: 'workaround for MSVC bug with scipy 1.8.x'
      replacements_plain:
        "return l2.startswith('-'*len(l1)) or l2.startswith('='*len(l1))": "r = l2.startswith('-'*len(l1)) or l2.startswith('='*len(l1)); return r"

- module-name: 'scipy._lib._numpy_compat' # checksum: 58eb0d9e
  anti-bloat:
    - description: 'remove numpy testing framework'
      replacements_plain:
        "NumpyVersion(np.__version__) > '1.7.0.dev'": '0'
        'from numpy.testing import suppress_warnings': 'suppress_warnings = __import__("contextmanager").contextmanager(lambda : (yield))'

- module-name: 'scipy._lib._test_deprecation_call' # checksum: 2db6b2eb
  implicit-imports:
    - depends:
        - 'scipy._lib._test_deprecation_def'

- module-name: 'scipy._lib._test_deprecation_def' # checksum: 10d680aa
  implicit-imports:
    - depends:
        - 'scipy._lib.deprecation'

- module-name: 'scipy._lib._testutils' # checksum: 5e3b539b
  anti-bloat:
    - description: 'remove numpy testing framework'
      module_code: |
        class PytestTester:
          def __init__(self, name):
            pass

- module-name: 'scipy._lib.array_api_compat.common._helpers' # checksum: d79dca22
  anti-bloat:
    - description: 'remove optional package references'
      no-auto-follow:
        'numpy': 'ignore'

- module-name: 'scipy._lib.array_api_compat.numpy' # checksum: 4d6580fa
  implicit-imports:
    - depends:
        - 'scipy._lib.array_api_compat.numpy.fft'

- module-name: 'scipy.integrate._quadrature' # checksum: 97a85907
  anti-bloat:
    - description: 'remove useless function copying'
      change_function:
        '_copy_func': "'(lambda f: f)'"

- module-name: 'scipy.lib.numpy_compat' # checksum: 58eb0d9e
  anti-bloat:
    - description: 'remove numpy testing framework'
      replacements_plain:
        "NumpyVersion(np.__version__) > '1.7.0.dev'": '0'
        'from numpy.testing import suppress_warnings': 'suppress_warnings = __import__("contextmanager").contextmanager(lambda : (yield))'

- module-name: 'scipy.linalg' # checksum: 3f43474e
  implicit-imports:
    - depends:
        - 'scipy.linalg.cython_blas'
        - 'scipy.linalg.cython_lapack'

- module-name: 'scipy.optimize._cobyla_py' # checksum: 3ea0b5b1
  implicit-imports:
    - post-import-code:
        - |
          import scipy.optimize._cobyla
          orig_minimize=scipy.optimize._cobyla.minimize

          def nuitka_compatible_minimize(*args, **kwargs):
            if len(args) > 0:
              arg0 = args[0]
              wrapper = eval("""lambda x, con: arg0(x, con)""", locals())
              args = list(args)
              args[0] = wrapper
            if "callback" in kwargs:
              kw_callback = kwargs["callback"]
              callback_wrapper = eval("""lambda x: kw_callback(x)""", locals())
              kwargs["callback"] = callback_wrapper
            return orig_minimize(*args, **kwargs)
          scipy.optimize._cobyla.minimize=nuitka_compatible_minimize

- module-name: 'scipy.optimize._shgo_lib.triangulation' # checksum: 7547ad25
  anti-bloat:
    - no-auto-follow:
        'matplotlib': 'plotting will lack matplotlib'

- module-name: 'scipy.sparse.csgraph' # checksum: 8997608e
  implicit-imports:
    - depends:
        - 'scipy.sparse.csgraph._validation'

- module-name: 'scipy.sparse.linalg._expm_multiply' # checksum: 8baee295
  anti-bloat:
    - description: 'remove numba reference (via sparse)'
      replacements_plain:
        'import sparse': 'raise ImportError'
      when: 'not use_numba and standalone'

- module-name: 'scipy.sparse.linalg._matfuncs' # checksum: 8baee295
  anti-bloat:
    - description: 'remove numba reference (via sparse)'
      replacements_plain:
        'import sparse': 'raise ImportError'
      when: 'not use_numba and standalone'

- module-name: 'scipy.sparse.linalg.matfuncs' # checksum: 8baee295
  anti-bloat:
    - description: 'remove numba reference (via sparse)'
      replacements_plain:
        'import sparse': 'raise ImportError'
      when: 'not use_numba and standalone'

- module-name: 'scipy.spatial' # checksum: 9701797c
  implicit-imports:
    - depends:
        - 'scipy.spatial.transform'

- module-name: 'scipy.spatial._plotutils' # checksum: 7547ad25
  anti-bloat:
    - no-auto-follow:
        'matplotlib': 'plotting will lack matplotlib'

- module-name: 'scipy.spatial.transform' # checksum: 2634b8e1
  implicit-imports:
    - depends:
        - 'scipy.spatial.transform._rotation_groups'

- module-name: 'scipy.special' # checksum: 93a78ec1
  implicit-imports:
    - depends:
        - 'scipy.special._ufuncs_cxx'

- module-name: 'scipy.special._ufuncs' # checksum: 79044be3
  implicit-imports:
    - depends:
        - 'scipy.special._cdflib'
        - 'scipy.special._special_ufuncs'

- module-name: 'scipy.stats._fit' # checksum: 7547ad25
  anti-bloat:
    - no-auto-follow:
        'matplotlib': 'plotting will lack matplotlib'

- module-name: 'scipy.stats._stats' # checksum: 41c1ec8f
  implicit-imports:
    - depends:
        - 'scipy.special.cython_special'

- module-name: 'scipy.stats._survival' # checksum: 7547ad25
  anti-bloat:
    - no-auto-follow:
        'matplotlib': 'plotting will lack matplotlib'

- module-name: 'scipy.stats.morestats' # checksum: 170d0f71
  anti-bloat:
    - description: 'remove numpy testing framework'
      replacements_plain:
        '@setastest(False)': ''
        'from numpy.testing.decorators import setastest': ''

- module-name: 'scrapy' # checksum: 49521496
  data-files:
    - patterns:
        - 'VERSION'

- module-name: 'scyjava' # checksum: 51174dbf
  anti-bloat:
    - description: 'compile time resolve version metadata'
      # This is necessary, because they have a function that prevents compile
      # time from seeing it. We might later force those to be inlined instead.
      replacements:
        'get_version("scyjava")': 'repr(version("scyjava"))'

- module-name: 'seaborn.external.docscrape' # checksum: f18b6919
  anti-bloat:
    # TODO: Keep the same as 'scipy._lib._docscrape' module
    - description: 'remove sphinx reference'
      replacements_plain:
        "if 'sphinx' in sys.modules:": 'if False:'
    # TODO: Keep the same as 'scipy._lib._docscrape' module
    - description: 'workaround for MSVC bug with scipy docscrape 1.8.x'
      replacements_plain:
        "return l2.startswith('-'*len(l1)) or l2.startswith('='*len(l1))": "r = l2.startswith('-'*len(l1)) or l2.startswith('='*len(l1)); return r"

- module-name: 'seedir.printing' # checksum: a69f5267
  data-files:
    - patterns:
        - 'words.txt'

- module-name: 'selenium' # checksum: f5c2bbb3
  data-files:
    - dirs:
        - '.'
  dlls:
    - from_filenames:
        relative_path: 'webdriver/common/windows'
        prefixes:
          - 'selenium-manager'
        executable: 'yes'
      when: 'win32'

- module-name: 'selenium_driverless' # checksum: bdcb049
  data-files:
    - dirs:
        - '.'
  implicit-imports:
    - depends:
        - 'websockets.legacy.*'

- module-name: 'selenium_stealth' # checksum: 26b9c88c
  data-files:
    - dirs:
        - '.'

- module-name: 'seleniumbase' # checksum: bcee33b2
  anti-bloat:
    - description: 'remove pytest reference'
      no-auto-follow:
        'pytest': 'ignore'
      when: 'not use_pytest'
    - description: 'Need to allow unittest usage'
      bloat-mode-overrides:
        'unittest': 'allow'

- module-name: 'seleniumbase.core.colored_traceback' # checksum: 6e89a07b
  anti-bloat:
    - description: 'disable colored tracebacks for standalone'
      replacements_plain:
        'sys.excepthook =': '# sys.excepthook ='
      when: 'standalone'

- module-name: 'seleniumbase.drivers' # checksum: 63813ab
  data-files:
    - empty_dirs:
        - '.'
  options:
    checks:
      - description: "need to use cached onefile mode with 'seleniumbase.drivers' to download persistently"
        support_info: 'warning'
        when: 'onefile and not onefile_cached'

- module-name: 'sentence_transformers.SentenceTransformer' # checksum: c6062648
  implicit-imports:
    - depends:
        - 'huggingface_hub.repository'

- module-name: 'setuptools' # checksum: aafdeb36
  anti-bloat:
    - description: 'no runtime expansion of sys.path needed'
      replacements_plain:
        'sys.path.extend(': '('
      when: 'version("setuptools") >= (71,)'
    - description: 'find setuptools.distutils directly'
      replacements_plain:
        'import _distutils_hack.override': 'import setuptools._distutils as distutils; sys.modules["distutils"] = distutils'
      when: 'python312_or_higher'
  import-hacks:
    - global-sys-path:
        # This package forces vendored packages into "sys.path" and causes
        # absolute imports from there to be available.
        - '_vendor'
      when: 'version("setuptools") >= (71,)'

- module-name: 'setuptools.command' # checksum: eb17e343
  anti-bloat:
    - description: 'find setuptools distutils'
      replacements_plain:
        'from distutils.command.bdist import bdist': 'from setuptools._distutils.command.bdist import bdist'
      when: 'python312_or_higher'

- module-name: 'setuptools.config.expand' # checksum: cd2c573e
  anti-bloat:
    - description: 'find setuptools distutils'
      replacements_plain:
        'from distutils.errors import DistutilsOptionError': 'from setuptools._distutils.errors import DistutilsOptionError'
      when: 'python312_or_higher'

- module-name: 'setuptools.discovery' # checksum: d8699eb7
  anti-bloat:
    - description: 'find setuptools distutils'
      replacements_plain:
        'from distutils.util import convert_path': 'from setuptools._distutils.util import convert_path'
      when: 'python312_or_higher'

- module-name: 'setuptools.extension' # checksum: a8b782ae
  anti-bloat:
    - description: 'find setuptools distutils'
      replacements_plain:
        'import distutils.core': 'import setuptools._distutils as distutils; import setuptools._distutils.core'
        'import distutils.errors': 'import setuptools._distutils.errors'
        'import distutils.extension': 'import setuptools._distutils.extension'
      when: 'python312_or_higher'

- module-name: 'setuptools.extern' # checksum: 2b2a3793
  # TODO: Vendor importers should maybe get their own thing, pkg_resources does
  # similar, and we might try and avoid duplication between the two of them on
  # some level.
  implicit-imports:
    - depends:
        - 'setuptools._vendor.*'
        - 'setuptools._vendor.*.*'

- module-name: 'setuptools.logging' # checksum: 70588c58
  anti-bloat:
    - description: 'find setuptools distutils'
      replacements_plain:
        'import distutils.log': 'import setuptools._distutils as distutils; import setuptools._distutils.log'
      when: 'python312_or_higher'

- module-name: 'setuptools.monkey' # checksum: c31be2f0
  anti-bloat:
    - description: 'remove MSVC patching on non-Windows'
      replacements_plain:
        "msvc = import_module('setuptools.msvc')": 'return'
      change_function:
        # TODO: No effect if bytecode is used, we might have to change how
        # these are done, and try to do it in source code somehow.
        'patch_for_msvc_specialized_compiler': "'(lambda: None)'"
      when: 'standalone and not win32'
    - description: 'find setuptools distutils'
      replacements_plain:
        'import distutils.filelist': 'import setuptools._distutils as distutils; import setuptools._distutils.filelist'
      when: 'python312_or_higher'

  implicit-imports:
    - depends:
        - '.msvc'
      when: 'standalone and win32'

- module-name: 'setuptools.version' # checksum: 76d680ea
  anti-bloat:
    - description: 'workaround for metadata version of setuptools'
      replacements:
        "pkg_resources.get_distribution('setuptools').version": "repr(__import__('setuptools.version').version.__version__)"

- module-name: 'sgmllib' # checksum: 820d21fd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"

- module-name: 'shap.explainers._exact' # checksum: cfb65066
  anti-bloat:
    - description: 'remove numba reference'
      replacements_plain:
        'from numba import jit': ''
        'from numba import njit': ''
        '@jit': ''
        '@njit': ''
      when: 'not use_numba and standalone'

- module-name: 'shap.explainers._partition' # checksum: cfb65066
  anti-bloat:
    - description: 'remove numba reference'
      replacements_plain:
        'from numba import jit': ''
        'from numba import njit': ''
        '@jit': ''
        '@njit': ''
      when: 'not use_numba and standalone'

- module-name: 'shap.links' # checksum: b91dd6ab
  anti-bloat:
    - description: 'remove numba reference'
      replacements_plain:
        'import numba': ''
        '@numba.jit': ''
        '@numba.njit': ''
      when: 'not use_numba and standalone'

- module-name: 'shap.maskers._image' # checksum: 5e001756
  anti-bloat:
    - description: 'remove numba reference'
      replacements_plain:
        'from numba import jit': ''
        'from numba import njit': ''
        'import numba.typed': ''
        '@jit': ''
        '@njit': ''
        "warnings.simplefilter('ignore', category=NumbaPendingDeprecationWarning)": ''
        'from numba.core.errors import NumbaPendingDeprecationWarning': ''
        'q = numba.typed.List([(0, xmin, xmax, ymin, ymax, zmin, zmax, -1, False)])': 'q = [(0, xmin, xmax, ymin, ymax, zmin, zmax, -1, False)]'
      when: 'not use_numba and standalone'

- module-name: 'shap.maskers._tabular' # checksum: cfb65066
  anti-bloat:
    - description: 'remove numba reference'
      replacements_plain:
        'from numba import jit': ''
        'from numba import njit': ''
        '@jit': ''
        '@njit': ''
      when: 'not use_numba and standalone'

- module-name: 'shap.utils._clustering' # checksum: cfb65066
  anti-bloat:
    - description: 'remove numba reference'
      replacements_plain:
        'from numba import jit': ''
        'from numba import njit': ''
        '@jit': ''
        '@njit': ''
      when: 'not use_numba and standalone'

- module-name: 'shap.utils._masked_model' # checksum: cfb65066
  anti-bloat:
    - description: 'remove numba reference'
      replacements_plain:
        'from numba import jit': ''
        'from numba import njit': ''
        '@jit': ''
        '@njit': ''
      when: 'not use_numba and standalone'

- module-name: 'shapely._geometry_helpers' # checksum: 669d5ef2
  implicit-imports:
    - depends:
        - 'shapely._geos'

- module-name: 'shapely.geometry.base' # checksum: f7162159
  anti-bloat:
    - description: 'remove doctest usage'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'shapely.geometry.collection' # checksum: f7162159
  anti-bloat:
    - description: 'remove doctest usage'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'shapely.geometry.multilinestring' # checksum: f7162159
  anti-bloat:
    - description: 'remove doctest usage'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'shapely.geometry.multipolygon' # checksum: f7162159
  anti-bloat:
    - description: 'remove doctest usage'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'shapely.geos' # checksum: ad839ab3
  dlls:
    # PyPI has it contained, but not for Windows.
    - by_code:
        setup_code: 'import shapely.geos'
        filename_code: 'shapely.geos._lgeos._name'
      when: 'not is_conda_package("shapely") and not win32 and version("shapely") < (2,0)'
    # Anaconda or Win32 are forced to specific path.
    - by_code:
        setup_code: 'import shapely.geos'
        filename_code: 'shapely.geos._lgeos._name'
      dest_path: 'shapely'
      when: 'version("shapely") < (2,0) and (is_conda_package("shapely") or win32 and version("shapely") < (1,8,1))'
  anti-bloat:
    - description: 'change geos DLL location'
      replacements_plain:
        '_lgeos = load_dll("geos_c.dll")': '_lgeos = load_dll("geos_c.dll", fallbacks=[os.path.join(os.path.dirname(__file__), "geos_c.dll")])'
      when: 'win32'
    - description: 'avoid runtime Anaconda environment influence'
      replacements_plain:
        "os.getenv('CONDA_PREFIX', '')": "''"

- module-name: 'shiboken6' # checksum: ccdf53fe
  dlls:
    # Do not put these top level, some packages are really incompatible with
    # this library potentially
    - from_filenames:
        prefixes:
          - 'msvcp'
      when: 'win32'

  implicit-imports:
    - depends:
        - 'argparse'
        - 'enum'
        - 'logging'

- module-name: 'sip' # checksum: 1cf87ab4
  implicit-imports:
    - depends:
        - 'enum'
      when: 'before_python3'

- module-name: 'six' # checksum: e62bccb7
  implicit-imports:
    - depends:
        - 'queue'

- module-name: 'skimage' # checksum: af3da5ed
  data-files:
    - dirs:
        - 'data'
  anti-bloat:
    - description: 'remove pytest reference (via nose)'
      no-auto-follow:
        'nose': 'ignore'
      when: 'not use_pytest'

# TODO: Our lazy loader support may mean these are not really necessary anymore.
- module-name: 'skimage._shared' # checksum: 9e389afa
  implicit-imports:
    - depends:
        - 'skimage._shared.geometry'
        - 'skimage._shared.interpolation'
        - 'skimage._shared.transform'

- module-name: 'skimage._shared.tester' # checksum: 32a31c1c
  anti-bloat:
    - description: 'remove skimage testing framework'
      module_code: |
        class PytestTester:
          def __init__(self, name):
            pass

- module-name: 'skimage.data' # checksum: a060f06f
  implicit-imports:
    - depends:
        - '._fetchers'

- module-name: 'skimage.data._fetchers' # checksum: 93b01f88
  anti-bloat:
    - description: 'remove pytest testing framework'
      replacements_plain:
        "'PYTEST_CURRENT_TEST' in os.environ": 'False'
      when: 'not use_pytest'

- module-name: 'skimage.draw' # checksum: 8b2051fc
  implicit-imports:
    - depends:
        - 'skimage.draw._draw'

- module-name: 'skimage.external.tifffile' # checksum: ca551805
  implicit-imports:
    - depends:
        - 'skimage.external.tifffile._tifffile'

- module-name: 'skimage.external.tifffile.tifffile_local' # checksum: 9838c964
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': 'un-callable'

- module-name: 'skimage.feature' # checksum: 3caac2d
  implicit-imports:
    - depends:
        - 'skimage.feature.brief_cy'
        - 'skimage.feature.censure_cy'
        - 'skimage.feature.corner_cy'
        - 'skimage.feature.orb_cy'
        - 'skimage.feature._cascade'
        - 'skimage.feature._haar'
        - 'skimage.feature._hessian_det_appx'
        - 'skimage.feature._hoghistogram'
        - 'skimage.feature._texture'

- module-name: 'skimage.feature._orb_descriptor_positions' # checksum: 97ea9cef
  data-files:
    - patterns:
        - 'orb_descriptor_positions.txt'

- module-name: 'skimage.feature.orb_cy' # checksum: c8ba0e7d
  implicit-imports:
    - depends:
        - 'skimage.feature._orb_descriptor_positions'

- module-name: 'skimage.filters.rank' # checksum: bfef07a9
  implicit-imports:
    - depends:
        - 'skimage.filters.rank.bilateral_cy'
        - 'skimage.filters.rank.core_cy'
        - 'skimage.filters.rank.core_cy_3d'
        - 'skimage.filters.rank.generic_cy'
        - 'skimage.filters.rank.percentile_cy'

- module-name: 'skimage.future.graph' # checksum: 1a5551d3
  implicit-imports:
    - depends:
        - 'skimage.future.graph._ncut_cy'

- module-name: 'skimage.graph' # checksum: 1307b067
  implicit-imports:
    - depends:
        - 'skimage.graph.heap'
        - 'skimage.graph._mcp'
        - 'skimage.graph._spath'

- module-name: 'skimage.io' # checksum: 6b3d25bb
  data-files:
    - patterns:
        - '_plugins/*.ini'

  implicit-imports:
    - depends:
        - 'skimage.io._plugins'
        - 'skimage.io._plugins._histograms'
        - 'skimage.io._plugins.fits_plugin'
        - 'skimage.io._plugins.gdal_plugin'
        - 'skimage.io._plugins.gtk_plugin'
        - 'skimage.io._plugins.imageio_plugin'
        - 'skimage.io._plugins.imread_plugin'
        - 'skimage.io._plugins.matplotlib_plugin'
        - 'skimage.io._plugins.pil_plugin'
        - 'skimage.io._plugins.qt_plugin'
        - 'skimage.io._plugins.simpleitk_plugin'
        - 'skimage.io._plugins.skivi_plugin'
        - 'skimage.io._plugins.tifffile_plugin'
        - 'skimage.io._plugins.util'

- module-name: 'skimage.io._io' # checksum: 1d1c0f59
  implicit-imports:
    - depends:
        - 'skimage.exposure.exposure'

- module-name: 'skimage.measure' # checksum: 32204708
  implicit-imports:
    - depends:
        - 'skimage.measure._ccomp'
        - 'skimage.measure._find_contours_cy'
        - 'skimage.measure._marching_cubes_classic_cy'
        - 'skimage.measure._marching_cubes_lewiner_cy'
        - 'skimage.measure._moments_cy'
        - 'skimage.measure._pnpoly'

- module-name: 'skimage.morphology' # checksum: 188a6703
  data-files:
    - patterns:
        - '*.npy'

  implicit-imports:
    - depends:
        - 'skimage.morphology._convex_hull'
        - 'skimage.morphology._extrema_cy'
        - 'skimage.morphology._flood_fill_cy'
        - 'skimage.morphology._greyreconstruct'
        - 'skimage.morphology._max_tree'
        - 'skimage.morphology._skeletonize_3d_cy'
        - 'skimage.morphology._skeletonize_cy'
        - 'skimage.morphology._watershed'

- module-name: 'skimage.restoration' # checksum: fd820a1d
  implicit-imports:
    - depends:
        - 'skimage.restoration._denoise_cy'
        - 'skimage.restoration._nl_means_denoising'
        - 'skimage.restoration._unwrap_1d'
        - 'skimage.restoration._unwrap_2d'
        - 'skimage.restoration._unwrap_3d'

- module-name: 'skimage.restoration._cycle_spin' # checksum: 37f8ca49
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'import dask': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'skimage.segmentation' # checksum: e950c621
  implicit-imports:
    - depends:
        - 'skimage.segmentation._felzenszwalb_cy'
        - 'skimage.segmentation._quickshift_cy'
        - 'skimage.segmentation._slic'

- module-name: 'skimage.transform' # checksum: fdc67d7
  implicit-imports:
    - depends:
        - 'skimage.transform._hough_transform'
        - 'skimage.transform._radon_transform'
        - 'skimage.transform._warps_cy'

- module-name: 'skimage.transform._hough_transform' # checksum: 3b3932aa
  implicit-imports:
    - depends:
        - 'skimage.draw.draw'

- module-name: 'skimage.util.apply_parallel' # checksum: 72ef561b
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'import dask.array as da': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'sklearn' # checksum: acc187b0
  anti-bloat:
    - description: 'pandas check should not cause usage'
      no-auto-follow:
        'pandas': 'ignore'
    - description: 'matplotlib check should not cause usage'
      no-auto-follow:
        'matplotlib': 'ignore'
    - description: 'remove pytest testing framework'
      no-auto-follow:
        'pytest': 'ignore'
      when: 'not use_pytest'

- module-name: 'sklearn._distributor_init' # checksum: 8597e1a6
  dlls:
    - from_filenames:
        relative_path: '.libs'
        prefixes:
          - ''
  anti-bloat:
    - description: 'remove site module and distutils usage'
      replacements_plain:
        '_site.ENABLE_USER_SITE': 'False'
        'if _running_from_pip_package()': 'if False'
        'import distutils as _distutils': '_distutils = None'
        'import site as _site': '_site = None'
  implicit-imports:
    - depends:
        - '.python.keras'

- module-name: 'sklearn.cluster' # checksum: f5aadf01
  implicit-imports:
    - depends:
        - 'sklearn.cluster._dbscan_inner'
        - 'sklearn.cluster._hierarchical'
        - 'sklearn.cluster._k_means'
        - 'sklearn.cluster._k_means_elkan'

- module-name: 'sklearn.datasets' # checksum: 1868eae6
  data-files:
    - dirs:
        - 'data'
        - 'descr'
  implicit-imports:
    - depends:
        - 'sklearn.datasets._svmlight_format'
        - 'sklearn.datasets.data'
        - 'sklearn.datasets.descr'

- module-name: 'sklearn.datasets._base' # checksum: 521f4991
  anti-bloat:
    - description: 'avoid PIL dependency for sample image testing'
      change_function:
        'load_sample_images': 'un-callable'

- module-name: 'sklearn.decomposition' # checksum: 61c8cb5d
  implicit-imports:
    - depends:
        - 'sklearn.decomposition.cdnmf_fast'
        - 'sklearn.decomposition._online_lda'

- module-name: 'sklearn.ensemble' # checksum: 22d20aac
  implicit-imports:
    - depends:
        - 'sklearn.ensemble._gradient_boosting'

- module-name: 'sklearn.ensemble._hist_gradient_boosting' # checksum: 530838bc
  implicit-imports:
    - depends:
        - 'sklearn.ensemble._hist_gradient_boosting.histogram'
        - 'sklearn.ensemble._hist_gradient_boosting.splitting'
        - 'sklearn.ensemble._hist_gradient_boosting.types'
        - 'sklearn.ensemble._hist_gradient_boosting.utils'
        - 'sklearn.ensemble._hist_gradient_boosting._binning'
        - 'sklearn.ensemble._hist_gradient_boosting._gradient_boosting'
        - 'sklearn.ensemble._hist_gradient_boosting._loss'
        - 'sklearn.ensemble._hist_gradient_boosting._predictor'

- module-name: 'sklearn.externals' # checksum: 75fb09a8
  implicit-imports:
    - depends:
        - 'sklearn.externals.joblib'

- module-name: 'sklearn.externals.joblib' # checksum: d27fe3d7
  implicit-imports:
    - depends:
        - 'sklearn.externals.joblib.numpy_pickle'

- module-name: 'sklearn.feature_extraction' # checksum: defc6c9f
  implicit-imports:
    - depends:
        - 'sklearn.feature_extraction._hashing'

- module-name: 'sklearn.linear_model' # checksum: 653af0a9
  implicit-imports:
    - depends:
        - 'sklearn.linear_model.cd_fast'
        - 'sklearn.linear_model.sag_fast'
        - 'sklearn.linear_model.sgd_fast'

- module-name: 'sklearn.linear_model._sgd_fast' # checksum: 52ff905c
  implicit-imports:
    - depends:
        - 'sklearn.utils._weight_vector'

- module-name: 'sklearn.manifold' # checksum: 9c747b4c
  implicit-imports:
    - depends:
        - 'sklearn.manifold._barnes_hut_tsne'
        - 'sklearn.manifold._utils'

- module-name: 'sklearn.manifold._barnes_hut_tsne' # checksum: 327dee3d
  implicit-imports:
    - depends:
        - 'sklearn.neighbors._quad_tree'

- module-name: 'sklearn.metrics' # checksum: 311c41ca
  implicit-imports:
    - depends:
        - 'sklearn.metrics.pairwise_fast'

- module-name: 'sklearn.metrics._dist_metrics' # checksum: 4d2b7e03
  implicit-imports:
    - depends:
        - 'sklearn.utils._typedefs'

- module-name: 'sklearn.metrics._pairwise_distances_reduction._argkmin' # checksum: 12b0ab2b
  implicit-imports:
    - depends:
        - 'sklearn.metrics._pairwise_distances_reduction._middle_term_computer'

- module-name: 'sklearn.metrics._pairwise_distances_reduction._base' # checksum: 22841aae
  implicit-imports:
    - depends:
        - 'sklearn.metrics._pairwise_distances_reduction._datasets_pair'

- module-name: 'sklearn.metrics.cluster' # checksum: 386d575c
  implicit-imports:
    - depends:
        - 'sklearn.metrics.cluster.expected_mutual_info_fast'

- module-name: 'sklearn.neighbors' # checksum: d45018d2
  implicit-imports:
    - depends:
        - 'sklearn.neighbors.ball_tree'
        - 'sklearn.neighbors.dist_metrics'
        - 'sklearn.neighbors.kd_tree'
        - 'sklearn.neighbors.quad_tree'
        - 'sklearn.neighbors.typedefs'

- module-name: 'sklearn.neighbors._ball_tree' # checksum: b80297a4
  implicit-imports:
    - depends:
        - 'sklearn.neighbors._partition_nodes'

- module-name: 'sklearn.neighbors._dist_metrics' # checksum: 30f6e612
  implicit-imports:
    - depends:
        - 'sklearn.neighbors._typedefs'

- module-name: 'sklearn.neighbors._quad_tree' # checksum: 3e37d9f0
  implicit-imports:
    - depends:
        - 'sklearn.tree'

- module-name: 'sklearn.preprocessing' # checksum: db64027b
  implicit-imports:
    - depends:
        - 'sklearn.preprocessing._csr_polynomial_expansion'

- module-name: 'sklearn.random_projection' # checksum: b68af3f8
  anti-bloat:
    - description: 'remove numpy testing framework'
      replacements_plain:
        'from numpy.testing import assert_equal': 'assert_equal = (lambda actual, desired, err_msg=None, verbose=True: True)'

- module-name: 'sklearn.svm' # checksum: 1126ac74
  implicit-imports:
    - depends:
        - 'sklearn.svm.liblinear'
        - 'sklearn.svm.libsvm'
        - 'sklearn.svm.libsvm_sparse'

- module-name: 'sklearn.tree' # checksum: 74b20e12
  implicit-imports:
    - depends:
        - 'sklearn.tree._criterion'
        - 'sklearn.tree._splitter'
        - 'sklearn.tree._tree'
        - 'sklearn.tree._utils'

- module-name: 'sklearn.tree._splitter' # checksum: f99ea94
  implicit-imports:
    - depends:
        - 'sklearn.tree._partitioner'

- module-name: 'sklearn.tree._tree' # checksum: 327dee3d
  implicit-imports:
    - depends:
        - 'sklearn.neighbors._quad_tree'

- module-name: 'sklearn.utils' # checksum: cecf5599
  data-files:
    - patterns:
        - '*.css'
  implicit-imports:
    - depends:
        - 'sklearn.utils.arrayfuncs'
        - 'sklearn.utils.fast_dict'
        - 'sklearn.utils.graph_shortest_path'
        - 'sklearn.utils.lgamma'
        - 'sklearn.utils.murmurhash'
        - 'sklearn.utils.seq_dataset'
        - 'sklearn.utils.sparsefuncs_fast'
        - 'sklearn.utils.weight_vector'
        - 'sklearn.utils._cython_blas'
        - 'sklearn.utils._logistic_sigmoid'
        - 'sklearn.utils._weight_vector'
        - 'sklearn.utils._typedefs'
        - 'sklearn.utils._heap'
        - 'sklearn.utils._sorting'
        - 'sklearn.utils._vector_sentinel'
        - 'sklearn.utils._seq_dataset'
        - 'sklearn.utils._readonly_array_wrapper'
        - 'sklearn.utils._openmp_helpers'
        - 'sklearn.utils._fast_dict'
        - 'sklearn.utils._random'

- module-name: 'sklearn.utils._hough_transform' # checksum: fb2b10d5
  implicit-imports:
    - depends:
        - 'skimage.draw'

- module-name: 'sklearn.utils._testing' # checksum: 8b4de7fe
  anti-bloat:
    - description: 'remove pytest testing framework'
      replacements_plain:
        '_dummy = TestCase("__init__")': ''
        'assert_dict_equal = _dummy.assert_dict_equal': 'def assert_dict_equal(*args, **kwargs): pass'
        'assert_raises = _dummy.assertRaises': 'def assert_raises(*args, **kwargs): pass'
        'assert_raises_regex = _dummy.assert_raises_regex': 'def assert_raises_regex(*args, **kwargs): pass'
        'assert_dict_equal = _dummy.assertDictEqual': 'def assert_dict_equal(*args, **kwargs): pass'
        'assert_raises_regex = _dummy.assertRaisesRegex': 'def assert_raises_regex(*args, **kwargs): pass'
        'from unittest import TestCase': ''
        'import pytest': 'raise ImportError'
        'import unittest': ''
        'unittest.case.SkipTest': 'None'

- module-name: 'sklearn.utils.estimator_checks' # checksum: 1356b841
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'parametrize_with_checks': 'un-callable'
      when: 'not use_pytest'

- module-name: 'sklearn.utils.sparsetools' # checksum: 3780099b
  implicit-imports:
    - depends:
        - 'sklearn.utils.sparsetools._graph_validation'
        - 'sklearn.utils.sparsetools._graph_tools'

- module-name: 'smt' # checksum: dd3b5473
  anti-bloat:
    - no-auto-follow:
        'numba': 'ignore'
      when: 'not use_numba'

- module-name: 'sound_lib' # checksum: f8f59602
  dlls:
    - from_filenames:
        relative_path: 'lib/x64'
        prefixes:
          - ''
        suffixes:
          - 'dylib'
      when: 'macos'
    - from_filenames:
        relative_path: 'lib/x64'
        prefixes:
          - ''
        suffixes:
          - 'so'
      when: 'linux'
    - from_filenames:
        relative_path: 'lib/x64'
        prefixes:
          - ''
        suffixes:
          - 'dll'
      when: 'win32 and arch_amd64'
    - from_filenames:
        relative_path: 'lib/x86'
        prefixes:
          - ''
        suffixes:
          - 'dll'
      when: 'win32 and arch_x86'

- module-name: 'sounddevice' # checksum: 4bff6c5b
  dlls:
    - from_filenames:
        relative_path: '_sounddevice_data/portaudio-binaries'
        prefixes:
          - 'libportaudio64bit'
      when: 'win32 and arch_amd64'
    - from_filenames:
        relative_path: '_sounddevice_data/portaudio-binaries'
        prefixes:
          - 'libportaudio32bit'
      when: 'win32 and arch_x86'
    - from_filenames:
        relative_path: '_sounddevice_data/portaudio-binaries'
        prefixes:
          - 'libportaudio'
      when: 'macos'

- module-name: 'spacy' # checksum: 4943d3fa
  data-files:
    - dirs:
        - '.'
  anti-bloat:
    - description: 'do not follow tests'
      no-auto-follow:
        'spacy.tests': 'ignore'

- module-name: 'spacy.morphology' # checksum: fc4c0a90
  implicit-imports:
    - depends:
        - 'spacy.parts_of_speech'

- module-name: 'spacy.pipeline' # checksum: e8faf1dd
  implicit-imports:
    - depends:
        - 'spacy.pipeline.ner'

- module-name: 'spacy.pipeline._parser_internals._beam_utils' # checksum: 21162b2b
  implicit-imports:
    - depends:
        - 'thinc.extra.search'

- module-name: 'spacy.pipeline.dep_parser' # checksum: c61d0958
  implicit-imports:
    - depends:
        - 'spacy.pipeline.transition_parser'

- module-name: 'spacy.pipeline.transition_parser' # checksum: bbff8187
  implicit-imports:
    - depends:
        - 'spacy.pipeline._parser_internals.*'

- module-name: 'spacy.util' # checksum: 39e3b324
  implicit-imports:
    - depends:
        - 'spacy.lang.*'

- module-name: 'spacy.vocab' # checksum: d166d251
  implicit-imports:
    - depends:
        - 'spacy.lang.lex_attrs'
        - 'spacy.lang.norm_exceptions'
        - 'spacy.lexeme'

- module-name: 'sparse' # checksum: 7a56f74f
  options:
    checks:
      - description: "Numba (via sparse) is not yet working with Nuitka standalone, recommended to use '--noinclude-numba-mode'."
        support_info: 'warning'
        when: 'not use_numba and standalone'

- module-name: 'sparse._coo.core' # checksum: 6ac5aa4f
  anti-bloat:
    - description: 'remove dask reference'
      no-auto-follow:
        'dask': 'ignore'
      when: 'not use_dask'

- module-name: 'speech_recognition' # checksum: 1b8b6249
  dlls:
    - from_filenames:
        prefixes:
          - 'flac-win32'
        executable: 'yes'
      when: 'win32'
    - from_filenames:
        prefixes:
          - 'flac-linux'
        executable: 'yes'
      when: 'linux'

- module-name: 'sphinx.util.docutils' # checksum: 4870f54d
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        '__version_info__ = tuple(LooseVersion(docutils.__version__).version)': 'tuple(int(d) for d in docutils.__version__.split("."))'
        'from distutils.version import LooseVersion': ''

- module-name: 'sqlalchemy' # checksum: c3efbf7
  implicit-imports:
    - depends:
        - '.dialects.*'

- module-name: 'sqlalchemy.dialects.mssql' # checksum: 9755bd16
  implicit-imports:
    - depends:
        - 'sqlalchemy.dialects.mssql.pyodbc'

- module-name: 'sqlalchemy.orm' # checksum: 807b0764
  implicit-imports:
    - depends:
        - 'sqlalchemy.orm.*'
        - 'sqlalchemy.ext.*'

- module-name: 'sqlalchemy.orm.util' # checksum: 629852fa
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'randomize_unitofwork': 'un-callable'
      when: 'not use_pytest'

- module-name: 'sqlalchemy.sql' # checksum: 74d06e19
  implicit-imports:
    - depends:
        - 'sqlalchemy.sql.default_comparator'

- module-name: 'sqlalchemy.util.preloaded' # checksum: e2f7fa57
  implicit-imports:
    - depends:
        - 'sqlalchemy.orm.dependency'

- module-name: 'sqlfluff' # checksum: 3fc9a782
  data-files:
    - include-metadata:
      # TODO: Actually we should be able to include by
      # entry point names.
        - 'sqlfluff'

  anti-bloat:
    - description: 'remove pytest reference'
      replacements_plain:
        'import pytest': ''
        'pytest.register_assert_rewrite("sqlfluff.utils.testing")': ''

- module-name: 'sqlfluff.api.simple' # checksum: 55a3b9b2
  implicit-imports:
    - depends:
        - 'sqlfluff.dialects.*'

- module-name: 'sqlfluff.core' # checksum: b4550b8e
  data-files:
    - patterns:
        - '*.cfg'
  implicit-imports:
    - depends:
        - 'sqlfluff.core.plugin.*'
        - 'sqlfluff.rules.*'

- module-name: 'sqlglot.dialects' # checksum: b0ad4de6
  implicit-imports:
    - depends:
        - 'sqlglot.dialects.*'

- module-name: 'srsly.msgpack._packer' # checksum: ef9e3a96
  implicit-imports:
    - depends:
        - 'srsly.msgpack.util'

- module-name: 'sspilib.raw._credential' # checksum: 8f3d1cec
  implicit-imports:
    - depends:
        - 'sspilib.raw._text'

- module-name: 'statsmodels' # checksum: 9664932
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      change_function:
        'test': 'un-callable'
      when: 'not use_pytest'

- module-name: 'statsmodels.api' # checksum: 3eb995ba
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        # TODO: Should make an un-callable object that is not just None for non-deployment.
        'from .tools.print_version import show_versions': 'show_versions = None'
        'from .__init__ import test': 'from . import test'
      when: 'not use_pytest'

- module-name: 'statsmodels.base' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.compat' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.datasets' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.discrete' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.distributions' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.duration' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.emplike' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.formula' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.gam' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.genmod' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.genmod.families' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.graphics' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.imputation' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.iolib' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.miscmodels' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.multivariate' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.multivariate.factor_rotation' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.nonparametric' # checksum: 6e8ca713
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'
  implicit-imports:
    - depends:
        - 'statsmodels.nonparametric.linbin'
        - 'statsmodels.nonparametric._smoothers_lowess'

- module-name: 'statsmodels.othermod' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.regression' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.robust' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.sandbox' # checksum: b4023396
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      module_code: |
        class PytestTester:
          def __init__(self, package_path=None):
            pass
        test = PytestTester()
      when: 'not use_pytest'

- module-name: 'statsmodels.stats' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.stats.libqsturng' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.tools' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.tsa' # checksum: ae76895b
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'
  implicit-imports:
    - depends:
        - 'statsmodels.tsa._exponential_smoothers'

- module-name: 'statsmodels.tsa.base' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.tsa.filters' # checksum: f7235f0e
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

  implicit-imports:
    - depends:
        - 'statsmodels.tsa.regime_switching._hamilton_filter'
        - 'statsmodels.tsa.regime_switching._kim_smoother'

- module-name: 'statsmodels.tsa.innovations' # checksum: 986e010e
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'
  implicit-imports:
    - depends:
        - 'statsmodels.tsa.innovations._arma_innovations'

- module-name: 'statsmodels.tsa.interp' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.tsa.kalmanf' # checksum: 84951770
  implicit-imports:
    - depends:
        - 'statsmodels.tsa.kalmanf.kalman_loglike'

- module-name: 'statsmodels.tsa.regime_switching' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.tsa.statespace' # checksum: 7b4ee6b9
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'
  implicit-imports:
    - depends:
        - 'statsmodels.tsa.statespace._filters'
        - 'statsmodels.tsa.statespace._initialization'
        - 'statsmodels.tsa.statespace._kalman_filter'
        - 'statsmodels.tsa.statespace._kalman_smoother'
        - 'statsmodels.tsa.statespace._representation'
        - 'statsmodels.tsa.statespace._simulation_smoother'
        - 'statsmodels.tsa.statespace._smoothers'
        - 'statsmodels.tsa.statespace._tools'

- module-name: 'statsmodels.tsa.statespace._filters' # checksum: a38bd53f
  implicit-imports:
    - depends:
        - 'statsmodels.tsa.statespace._filters._conventional'
        - 'statsmodels.tsa.statespace._filters._inversions'
        - 'statsmodels.tsa.statespace._filters._univariate'
        - 'statsmodels.tsa.statespace._filters._univariate_diffuse'

- module-name: 'statsmodels.tsa.statespace._smoothers' # checksum: 6051e02c
  implicit-imports:
    - depends:
        - 'statsmodels.tsa.statespace._smoothers._alternative'
        - 'statsmodels.tsa.statespace._smoothers._classical'
        - 'statsmodels.tsa.statespace._smoothers._conventional'
        - 'statsmodels.tsa.statespace._smoothers._univariate'
        - 'statsmodels.tsa.statespace._smoothers._univariate_diffuse'

- module-name: 'statsmodels.tsa.stl' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'statsmodels.tsa.vector_ar' # checksum: 29fd834d
  anti-bloat:
    - description: 'remove statsmodels testing framework'
      replacements_plain:
        'from statsmodels.tools._testing import PytestTester': 'from statsmodels.sandbox import PytestTester'
      when: 'not use_pytest'

- module-name: 'strawberry' # checksum: 438c71c
  data-files:
    - dirs:
        - 'static'

- module-name: 'streamlit.runtime.caching.cache_utils' # checksum: e430da94
  anti-bloat:
    - description: 'remove warning for compiled functions'
      replacements_plain:
        '_LOGGER.debug(': '('

- module-name: 'sv_ttk' # checksum: 8b045092
  data-files:
    - dirs:
        - 'theme'
    - patterns:
        - 'sv.tcl'

- module-name: 'swagger_ui_bundle' # checksum: 71dadccf
  data-files:
    - dirs:
        - 'vendor'

- module-name: 'sympy' # checksum: 4f06976c
  anti-bloat:
    - description: 'remove sympy.testing reference'
      replacements_plain:
        "'test', 'doctest',": ''
        'from .testing import test, doctest': ''
      when: 'not use_pytest'
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'
    - description: 'remove pydoc reference'
      no-auto-follow:
        'pydoc': 'ignore'
      when: 'not use_pydoc'

- module-name: 'sympy.interactive.printing' # checksum: 8dbbac5
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        "'IPython' not in modules": 'True'
      when: 'not use_ipython'

- module-name: 'sympy.utilities.decorator' # checksum: f8ef59fe
  anti-bloat:
    - description: 'remove sympy.testing reference'
      replacements_plain:
        'from sympy.testing.runtests import DependencyError, SymPyDocTests, PyTestReporter': ''
      change_function:
        'skiptests': 'un-callable'
      when: 'not use_pytest'

- module-name: 'tables' # checksum: fc7f4f37
  dlls:
    - from_filenames:
        prefixes:
          - 'libblosc2'

  anti-bloat:
    - description: 'remove tables.tests usage'
      replacements_plain:
        'from .tests import print_versions, test': ''

- module-name: 'tables.filters' # checksum: 4cc95bab
  anti-bloat:
    - description: 'remove docutils usage'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'tables.flavor' # checksum: 4cc95bab
  anti-bloat:
    - description: 'remove docutils usage'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'tables.misc.enum' # checksum: 4cc95bab
  anti-bloat:
    - description: 'remove docutils usage'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'tables.path' # checksum: 4cc95bab
  anti-bloat:
    - description: 'remove docutils usage'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'tables.utils' # checksum: 4cc95bab
  anti-bloat:
    - description: 'remove docutils usage'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'telethon' # checksum: a76d75f1
  anti-bloat:
    - description: 'avoid annotations bloat'
      annotations: 'no'

- module-name: 'tenacity' # checksum: 64bd1802
  anti-bloat:
    # It would be nice
    - description: 'remove tornado dependency'
      replacements_plain:
        'import tornado': 'raise ImportError'
        'from tenacity.tornadoweb import TornadoRetrying': 'pass'
      when: 'not use_tornado'

- module-name: 'tendo.singleton' # checksum: ca7e6fc2
  options:
    checks:
      # TODO: Actually change the default to product name, file version, etc. from __compiled__ or from plugin variables
      - description: "need to use cached onefile mode with 'tendo.singleton' to work correctly"
        support_info: 'warning'
        when: 'onefile and not onefile_cached'

- module-name: 'tensorboard' # checksum: 5e3de759
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        'load_ipython_extension': "'(lambda ipython: None)'"
        'notebook': "'(lambda: None)'"
      when: 'not use_ipython'

- module-name: 'tensorflow' # checksum: 7bd997cd
  dlls:
    - from_filenames:
        relative_path: 'core/platform'
        prefixes:
          - '_cpu_feature_guard'
  anti-bloat:
    - description: 'allow unittest inside of tensorflow, too dependent to remove'
      bloat-mode-overrides:
        'unittest': 'allow'
    - description: 'remove useless distutils and test framework usage'
      replacements_plain:
        'import distutils as _distutils': '_distutils = None'
        'import site as _site': '_site = None'
        'if _running_from_pip_package()': 'if False'
        '_site.ENABLE_USER_SITE': 'False'
        '_site_packages_dirs += [_site.USER_SITE]': 'pass'
        # TODO: Have "unusable", something that triggers a telling "RuntimeError" when used.
        'from tensorflow._api.v1 import test': 'test = None'
        'from ._api.v2 import test': 'test = None'
        'from tensorflow._api.v2 import test': 'test = None'
      replacements:
        '_os.environ.get("TF_USE_LEGACY_KERAS", None)': 'repr(os.getenv("TF_USE_LEGACY_KERAS", None))'
      append_plain: |
        import sys
        sys.modules["tensorflow.compat"] = compat
        sys.modules["tensorflow.experimental"] = experimental
        sys.modules["tensorflow.compat.v1"] = compat.v1
        sys.modules["tensorflow.compat.v2"] = compat.v2
        sys.modules["tensorflow.compat.v1.logging"] = compat.v1.logging
        sys.modules["tensorflow.compat.v2.experimental"] = compat.v2.experimental
        sys.modules["tensorflow.keras"] = keras
      when: 'standalone'
    - description: 'remove kubernetes as default dependency'
      no-auto-follow:
        'kubernetes': 'tensorflow clustering with kubernetes not available'
      when: 'standalone'

  implicit-imports:
    - depends:
        - 'tensorboard'
        - 'tensorflow_estimator'
    - depends:
        - 'keras.api._v2.keras'
      when: 'version("keras") < (3,)'
    - depends:
        - 'keras._tf_keras.keras'
      when: 'version("keras") >= (3,)'

  options:
    checks:
      - description: "'tensorflow' is not fully supported before version 2.16"
        support_info: 'warning'
        when: 'version("tensorflow") < (2,16)'
      - description: "'tensorflow' is not fully supported with legacy keras"
        support_info: 'warning'
        when: 'os.getenv("TF_USE_LEGACY_KERAS", None) is not None'

- module-name: 'tensorflow._api.v1.compat.v1' # checksum: 3e84bd8b
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from . import test': 'test = None'
        'from tensorflow._api.v1.compat.v1 import test': 'test = None'

- module-name: 'tensorflow._api.v1.compat.v2' # checksum: 669a14a3
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from . import test': 'test = None'
        'from tensorflow._api.v1.compat.v2 import test': 'test = None'

- module-name: 'tensorflow._api.v2.__internal__' # checksum: 92facfb8
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from . import test': 'test = None'
        'from tensorflow._api.v2.__internal__ import test': 'test = None'

- module-name: 'tensorflow._api.v2.compat.v1' # checksum: 53024ed1
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from . import test': 'test = None'
        'from tensorflow._api.v2.compat.v1 import test': 'test = None'

- module-name: 'tensorflow._api.v2.compat.v1.compat.v1' # checksum: 31fdcb26
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from tensorflow._api.v2.compat.v1 import test': 'test = None'

- module-name: 'tensorflow._api.v2.compat.v1.compat.v2' # checksum: 69e3620e
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from tensorflow._api.v2.compat.v2 import test': 'test = None'

- module-name: 'tensorflow._api.v2.compat.v2' # checksum: b1ce7f9
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from . import test': 'test = None'
        'from tensorflow._api.v2.compat.v2 import test': 'test = None'

- module-name: 'tensorflow._api.v2.compat.v2.__internal__' # checksum: 970797b3
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from . import test': 'test = None'
        'from tensorflow._api.v2.compat.v2.__internal__ import test': 'test = None'

- module-name: 'tensorflow._api.v2.compat.v2.compat.v1' # checksum: 31fdcb26
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from tensorflow._api.v2.compat.v1 import test': 'test = None'

- module-name: 'tensorflow._api.v2.compat.v2.compat.v2' # checksum: 69e3620e
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from tensorflow._api.v2.compat.v2 import test': 'test = None'

- module-name: 'tensorflow.compiler.tf2tensorrt' # checksum: d9b49de3
  implicit-imports:
    - depends:
        - 'tensorflow.compiler.tf2tensorrt._wrap_py_utils'
      when: 'not win32'

- module-name: 'tensorflow.compiler.tf2tensorrt.python.ops' # checksum: f9f714ff
  implicit-imports:
    - depends:
        - 'tensorflow.compiler.tf2tensorrt.python.ops.libtftrt'
      when: 'not win32'

- module-name: 'tensorflow.compiler.tf2xla.ops' # checksum: 43cd50f5
  implicit-imports:
    - depends:
        - 'tensorflow.compiler.tf2xla.ops._xla_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib' # checksum: e2ec935f
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'from tensorflow.contrib import testing': 'testing=None'
      when: 'not use_unittest'

- module-name: 'tensorflow.contrib.bigtable.python.ops' # checksum: 549b8309
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.bigtable.python.ops._bigtable'
      when: 'not win32'

- module-name: 'tensorflow.contrib.boosted_trees.python.ops' # checksum: 7bbc7f0
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.boosted_trees.python.ops._boosted_trees_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.factorization.python.ops.factorization_ops' # checksum: 4ea2083
  dlls:
    - from_filenames:
        prefixes:
          - '_factorization_ops'

- module-name: 'tensorflow.contrib.factorization.python.opso' # checksum: 280169f9
  implicit-imports:
    - depends:
        - 'tensorflw.contrib.factorization.python.ops._factorization_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.ffmpeg' # checksum: 92e98eca
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.ffmpeg.ffmpeg'
      when: 'not win32'

- module-name: 'tensorflow.contrib.framework.python.ops' # checksum: baeae338
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.framework.python.ops._variable_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.fused_conv.python.ops' # checksum: 9eb93fc7
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.fused_conv.python.ops._fused_conv2d_bias_activation_op'
      when: 'not win32'

- module-name: 'tensorflow.contrib.hadoop' # checksum: 651d6357
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.hadoop._dataset_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.ignite' # checksum: ee2d74ad
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.ignite._ignite_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.image.python.ops' # checksum: e180afc5
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.image.python.ops._distort_image_ops'
        - 'tensorflow.contrib.image.python.ops._image_ops'
        - 'tensorflow.contrib.image.python.ops._single_image_random_dot_stereograms'
      when: 'not win32'

- module-name: 'tensorflow.contrib.input_pipeline.python.ops' # checksum: ee4d7566
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.input_pipeline.python.ops._input_pipeline_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.kafka' # checksum: b4138bd7
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.kafka._dataset_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.kinesis' # checksum: 42f2ac20
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.kinesis._dataset_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.layers.python.ops' # checksum: 74ec77d5
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.layers.python.ops._sparse_feature_cross_op'
      when: 'not win32'

- module-name: 'tensorflow.contrib.libsvm.python.ops' # checksum: cd37a313
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.libsvm.python.ops._libsvm_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.memory_stats.python.ops' # checksum: df529e66
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.memory_stats.python.ops._memory_stats_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.nearest_neighbor.python.ops' # checksum: 8fc80318
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.nearest_neighbor.python.ops._nearest_neighbor_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.periodic_resample.python.ops' # checksum: 667ca39b
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.periodic_resample.python.ops._periodic_resample_op'
      when: 'not win32'

- module-name: 'tensorflow.contrib.reduce_slice_ops.python.ops' # checksum: 90556f44
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.reduce_slice_ops.python.ops._reduce_slice_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.resampler.python.ops' # checksum: 12b7cc7a
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.resampler.python.ops._resampler_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.rnn.python.ops' # checksum: a7b9da5d
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.rnn.python.ops._gru_ops'
        - 'tensorflow.contrib.rnn.python.ops._lstm_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.rpc.python.kernel_tests' # checksum: 1805430
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.rpc.python.kernel_tests.libtestexample'
      when: 'not win32'

- module-name: 'tensorflow.contrib.seq2seq.python.ops' # checksum: aac3a043
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.seq2seq.python.ops._beam_search_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.tensor_forest' # checksum: f1216a35
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.tensor_forest.libforestprotos'
      when: 'not win32'

- module-name: 'tensorflow.contrib.tensor_forest.hybrid.python.ops' # checksum: 615056ec
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.tensor_forest.hybrid.python.ops._training.ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.tensor_forest.python.ops' # checksum: 24df0a8d
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.tensor_forest.python.ops._model_ops'
        - 'tensorflow.contrib.tensor_forest.python.ops._stats_ops'
        - 'tensorflow.contrib.tensor_forest.python.ops._tensor_forest_ops'
      when: 'not win32'

- module-name: 'tensorflow.contrib.text.python.ops' # checksum: 1ceed33b
  implicit-imports:
    - depends:
        - 'tensorflow.contrib.text.python.ops._skip_gram_ops'
      when: 'not win32'

- module-name: 'tensorflow.include.external.protobuf_archive.python.google.protobuf.internal' # checksum: aa796e3d
  implicit-imports:
    - depends:
        - 'tensorflow.include.external.protobuf_archive.python.google.protobuf.internal._api_implementation'
      when: 'not win32'

- module-name: 'tensorflow.include.external.protobuf_archive.python.google.protobuf.pyext' # checksum: 654de2d1
  implicit-imports:
    - depends:
        - 'tensorflow.include.external.protobuf_archive.python.google.protobuf.pyext._message'
      when: 'not win32'

- module-name: 'tensorflow.lite.experimental.microfrontend.python.ops.audio_microfrontend_op' # checksum: 78612df0
  dlls:
    - from_filenames:
        prefixes:
          - '_audio_microfrontend_op'

# TODO: Maybe not really necessary, this should be avoided by itself instead.
- module-name: 'tensorflow.lite.python.convert' # checksum: 326784fc
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'distutils.spawn.find_executable(_deprecated_conversion_binary) is None': 'False'
        'import distutils.spawn': ''

- module-name: 'tensorflow.lite.python.interpreter_wrapper' # checksum: 6742ae49
  implicit-imports:
    - depends:
        - 'tensorflow.lite.python.interpreter_wrapper._tensorflow_wrap_interpreter_wrapper'

- module-name: 'tensorflow.lite.python.optimize' # checksum: 9dbd14ab
  implicit-imports:
    - depends:
        - 'tensorflow.lite.python.optimize._tensorflow_lite_wrap_calibration_wrapper'

- module-name: 'tensorflow.lite.toco.python' # checksum: 20ccf7c9
  implicit-imports:
    - depends:
        - 'tensorflow.lite.toco.python._tensorflow_wrap_toco'

- module-name: 'tensorflow.python' # checksum: a6d5f88
  anti-bloat:
    - description: 'remove useless tensorflow testing usage'
      replacements_plain:
        'from tensorflow.python.platform import test': 'test = None'
  implicit-imports:
    - depends:
        - 'tensorflow.python._pywrap_tensorflow_internal'
        - 'tensorflow.python.ops'
        - 'tensorflow.python.ops.cond_v2'

- module-name: 'tensorflow.python.autograph.core.ag_ctx' # checksum: 680413fa
  anti-bloat:
    - description: 'remove useless no-source warning'
      replacements_plain:
        'ag_logging.warning(': 'if False: ag_logging.warning('

- module-name: 'tensorflow.python.autograph.pyct.inspect_utils' # checksum: 791d410a
  anti-bloat:
    - description: 'workaround to enable Tensorflow JIT support'
      append_plain: |
        orig_getimmediatesource = getimmediatesource
        def getimmediatesource(obj):
          module_name = getattr(obj, '__module__', None)
          if module_name:
            qualname = getattr(obj, '__qualname__', None)

            _uncompiled_function_sources_dict=getattr(
              builtins,
              "_uncompiled_function_sources_dict",
              {}
            )
            key = "%s.%s" % (module_name, qualname)
            source, line_number = _uncompiled_function_sources_dict.get(key, (None, None))

            if source is not None:
              return source

          return orig_getimmediatesource(obj)
      when: 'standalone'

- module-name: 'tensorflow.python.compiler.tensorrt.trt_convert' # checksum: 9471824c
  implicit-imports:
    - depends:
        - 'tensorflow.compiler.tf2tensorrt.ops.gen_trt_ops'
        - 'tensorflow.compiler.tf2tensorrt._pywrap_py_utils'

- module-name: 'tensorflow.python.compiler.tensorrt.utils' # checksum: 356eeadf
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'distutils_version.LooseVersion': 'str'
        ? "from distutils import version\n"
        : "\n"
        'from distutils import version as distutils_version': ''

- module-name: 'tensorflow.python.data.ops.dataset_ops' # checksum: 1077d47d
  implicit-imports:
    - depends:
        - 'tensorflow.python.data.ops.shuffle_op'

- module-name: 'tensorflow.python.framework' # checksum: 98664efa
  implicit-imports:
    - depends:
        - 'tensorflow.python.framework.fast_tensor_util'
      when: 'not win32'

- module-name: 'tensorflow.python.framework.dtypes' # checksum: 25144a68
  implicit-imports:
    - depends:
        - 'ml_dtypes'

- module-name: 'tensorflow.python.framework.meta_graph' # checksum: 13d13fb
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'distutils_version.LooseVersion': 'str'
        'from distutils import version as distutils_version': ''

- module-name: 'tensorflow.python.keras.utils.vis_utils' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'tensorflow.python.ops.distributions.distribution' # checksum: 4b7ea33
  anti-bloat:
    - description: 'remove useless function copying'
      replacements_plain:
        'class_attr_value.__doc__ = _update_docstring': 'class_attr_value___doc__ = _update_docstring'
      change_function:
        '_copy_fn': "'(lambda fn: fn)'"

- module-name: 'tensorflow.python.util.lazy_loader' # checksum: b554bf0
  anti-bloat:
    - description: 'reduce keras usage'
      replacements_re:
        '(package_name = "(.*?)")': '\1; import \2'
      replacements:
        'os.environ.get("TF_USE_LEGACY_KERAS", None)': 'repr(os.getenv("TF_USE_LEGACY_KERAS", None))'
        'keras.__version__': 'repr(version_str("keras"))'

- module-name: 'tensorflow.python.util.tf_inspect' # checksum: 8e3a3cbf
  anti-bloat:
    - description: 'workaround to enable Tensorflow JIT support'
      append_plain: |
        import builtins
        orig_getsourcelines = getsourcelines
        def getsourcelines(object):
          module_name = getattr(object, '__module__', None)
          if module_name:
            qualname = getattr(object, '__qualname__', None)

            _uncompiled_function_sources_dict=getattr(
              builtins,
              "_uncompiled_function_sources_dict",
              {}
            )
            key = "%s.%s" % (module_name, qualname)
            source, line_number = _uncompiled_function_sources_dict.get(key, (None, None))

            if source is not None:
              return source.splitlines(), line_number

          return orig_getsourcelines(obj)
      when: 'standalone'

- module-name: 'tensorflow_core' # checksum: e1ad340d
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'import distutils as _distutils': '_distutils = None'
        'import site as _site': '_site = None'
        'if _running_from_pip_package()': 'if False'

- module-name: 'tensorflow_probability.python' # checksum: 33557ce7
  anti-bloat:
    - description: 'remove useless distutils usage'
      replacements_plain:
        'distutils.version.LooseVersion': ''
        'import distutils.version': ''
  implicit-imports:
    - depends:
        - 'tensorflow_probability.python.experimental'

- module-name: 'tensorflow_probability.python.bijectors.glow' # checksum: 44c3c53b
  implicit-imports:
    - depends:
        - 'keras.api._v2'

- module-name: 'tensorrt_libs' # checksum: cb30dde5
  dlls:
    - from_filenames:
        prefixes:
          - 'libnv'
      dest_path: '.'

- module-name: 'text_unidecode' # checksum: db822b99
  data-files:
    - patterns:
        - 'data.bin'

- module-name: 'textual' # checksum: c965c84e
  implicit-imports:
    - depends:
        - 'pygments.lexers.css'

- module-name: 'textual.widgets' # checksum: a98bfa19
  implicit-imports:
    - depends:
        - '._*'

- module-name: 'tf_keras.src' # checksum: c552fd3e
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'from keras.src.testing_infra import test_utils': 'test_utils = None'
        'from tf_keras.src.testing_infra import test_utils': 'test_utils = None'
      when: 'not use_unittest'

- module-name: 'tf_keras.src.utils.vis_utils' # checksum: 7d07a2d1
  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'thinc' # checksum: 22203f3f
  anti-bloat:
    - description: 'do not follow tests'
      no-auto-follow:
        'thinc.tests': 'ignore'

- module-name: 'thinc.backends' # checksum: e1fd8f8
  data-files:
    - patterns:
        - '*.cu'

- module-name: 'thinc.backends.cblas' # checksum: 53774cbb
  implicit-imports:
    - depends:
        - 'blis'

- module-name: 'thinc.backends.numpy_ops' # checksum: 33d82a78
  implicit-imports:
    - depends:
        - 'thinc.backends.linalg'
        - 'preshed.maps'

- module-name: 'thinc.backends.numpy_ops.NumpyOps' # checksum: 53774cbb
  implicit-imports:
    - depends:
        - 'blis'

- module-name: 'thinc.layers.concatenate' # checksum: 90d2f7a2
  implicit-imports:
    - depends:
        - 'blis.py'

- module-name: 'tifffile.tifffile' # checksum: 9838c964
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': 'un-callable'

- module-name: 'tiktoken.registry' # checksum: 6350df24
  implicit-imports:
    - depends:
        - 'tiktoken_ext.*'

- module-name: 'time_machine' # checksum: b038bd8
  anti-bloat:
    - description: 'remove pytest reference'
      no-auto-follow:
        'pytest': 'ignore'
      when: 'not use_pytest'
    - description: 'allow unittest'
      bloat-mode-overrides:
        'unittest': 'allow'

- module-name: 'timm.models.hrnet' # checksum: fdbe0e63
  anti-bloat:
    - replacements_plain:
        '@torch.jit.interface': ''

- module-name: 'timm.models.layers.config' # checksum: cc2107ee
  anti-bloat:
    - description: 'remove torchscript JIT usage'
      replacements_plain:
        '_NO_JIT = False': '_NO_JIT = True'
        '_NO_ACTIVATION_JIT = False': '_NO_ACTIVATION_JIT = True'

- module-name: 'tinycss2' # checksum: 49521496
  data-files:
    - patterns:
        - 'VERSION'

- module-name: 'tinycudann.modules' # checksum: 2467d1b5
  implicit-imports:
    - depends:
        - 'tinycudann_bindings._*_C'

- module-name: 'tkextrafont' # checksum: 1bd90a67
  dlls:
    - from_filenames:
        prefixes:
          - 'libextrafont'
      when: 'win32'

- module-name: 'tkinterdnd2.TkinterDnD' # checksum: 401c1500
  data-files:
    - dirs:
        - 'tkdnd/win64'
      when: 'use_tkinter and win32 and version("tkinterdnd2") < (0,4)'
    - dirs:
        - 'tkdnd/linux64'
      when: 'use_tkinter and linux and version("tkinterdnd2") < (0,4)'
    - dirs:
        - 'tkdnd/osx64'
      when: 'use_tkinter and macos and version("tkinterdnd2") < (0,4)'
    - dirs:
        - 'tkdnd/win-arm64'
      when: 'use_tkinter and win32 and arch_arm64 and version("tkinterdnd2") >= (0,4)'
    - dirs:
        - 'tkdnd/win-x64'
      when: 'use_tkinter and win32 and arch_amd64 and version("tkinterdnd2") >= (0,4)'
    - dirs:
        - 'tkdnd/win-x86'
      when: 'use_tkinter and win32 and arch_x86 and version("tkinterdnd2") >= (0,4)'
    - dirs:
        - 'tkdnd/linux-arm64'
      when: 'use_tkinter and linux and arch_arm64 and version("tkinterdnd2") >= (0,4)'
    - dirs:
        - 'tkdnd/linux-x64'
      when: 'use_tkinter and linux and arch_amd64 and version("tkinterdnd2") >= (0,4)'
    - dirs:
        - 'tkdnd/osx-arm64'
      when: 'use_tkinter and macos and arch_arm64 and version("tkinterdnd2") >= (0,4)'
    - dirs:
        - 'tkdnd/osx-x64'
      when: 'use_tkinter and macos and arch_amd64 and version("tkinterdnd2") >= (0,4)'
  dlls:
    - from_filenames:
        relative_path: 'tkdnd/win64'
        prefixes:
          - ''
      when: 'use_tkinter and win32 and version("tkinterdnd2") < (0,4)'
    - from_filenames:
        relative_path: 'tkdnd/linux64'
        prefixes:
          - ''
      when: 'use_tkinter and linux and version("tkinterdnd2") < (0,4)'
    - from_filenames:
        relative_path: 'tkdnd/osx64'
        prefixes:
          - ''
      when: 'use_tkinter and macos and version("tkinterdnd2") < (0,4)'

    - from_filenames:
        relative_path: 'tkdnd/win-arm64'
        prefixes:
          - ''
      when: 'use_tkinter and win32 and arch_arm64 and version("tkinterdnd2") >= (0,4)'
    - from_filenames:
        relative_path: 'tkdnd/win-x64'
        prefixes:
          - ''
      when: 'use_tkinter and win32 and arch_amd64 and version("tkinterdnd2") >= (0,4)'
    - from_filenames:
        relative_path: 'tkdnd/win-x86'
        prefixes:
          - ''
      when: 'use_tkinter and win32 and arch_x86 and version("tkinterdnd2") >= (0,4)'
    - from_filenames:
        relative_path: 'tkdnd/linux-arm64'
        prefixes:
          - ''
      when: 'use_tkinter and linux and arch_arm64 and version("tkinterdnd2") >= (0,4)'
    - from_filenames:
        relative_path: 'tkdnd/linux-x64'
        prefixes:
          - ''
      when: 'use_tkinter and linux and arch_amd64 and version("tkinterdnd2") >= (0,4)'
    - from_filenames:
        relative_path: 'tkdnd/osx-arm64'
        prefixes:
          - ''
      when: 'use_tkinter and macos and arch_arm64 and version("tkinterdnd2") >= (0,4)'
    - from_filenames:
        relative_path: 'tkdnd/osx-x64'
        prefixes:
          - ''
      when: 'use_tkinter and macos and arch_amd64 and version("tkinterdnd2") >= (0,4)'

- module-name: 'tkinterweb' # checksum: 5de6ded8
  data-files:
    - dirs:
        - 'tkhtml'

  dlls:
    - from_filenames:
        relative_path: 'tkhtml/Windows/32-bit'
        prefixes:
          - 'Tkhtml'
      when: 'win32 and arch_x86'
    - from_filenames:
        relative_path: 'tkhtml/Windows/64-bit'
        prefixes:
          - 'Tkhtml'
      when: 'win32 and arch_amd64'

  anti-bloat:
    - description: 'remove "sys.path" hack'
      replacements_plain:
        'sys.path.append': ''
  import-hacks:
    - global-sys-path:
        # This package forces itself into "sys.path" and expects absolute
        # imports to be available.
        - ''

- module-name: 'tls_client.cffi' # checksum: 97b24774
  dlls:
    - by_code:
        setup_code: 'import tls_client.cffi'
        filename_code: 'tls_client.cffi.library._name'
      dest_path: 'tls_client/dependencies'

- module-name: 'toga' # checksum: e93c8871
  data-files:
    - include-metadata:
        - 'toga-core'
  anti-bloat:
    - description: 'remove setuptools usage'
      no-auto-follow:
        'setuptools_scm': 'ignore'
      when: 'not use_setuptools'

- module-name: 'toga.platform' # checksum: c708442a
  variables:
    setup_code:
      - 'import toga.platform'
    declarations:
      'toga_backend_module_name': 'toga.platform.get_platform_factory().__name__'
  anti-bloat:
    - change_function:
        'get_platform_factory': "'importlib.import_module(%r)' % get_variable('toga_backend_module_name')"

- module-name: 'toga_cocoa' # checksum: f4de9bcb
  data-files:
    - dirs:
        - 'resources'
    - include-metadata:
        - 'toga-cocoa'

- module-name: 'toga_gtk' # checksum: eb33d9fa
  data-files:
    - dirs:
        - 'resources'
    - include-metadata:
        - 'toga-gtk'

- module-name: 'toga_winforms' # checksum: 618ee1e0
  data-files:
    - dirs:
        - 'resources'
    - include-metadata:
        - 'toga-winforms'
  dlls:
    - from_filenames:
        relative_path: 'libs/WebView2/runtimes/win-arm64/native'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_arm64'
    - from_filenames: # older toga_winforms only
        relative_path: 'libs/WebView2/arm64'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_arm64'
    - from_filenames:
        relative_path: 'libs/WebView2/runtimes/win-x86/native'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_x86'
    - from_filenames: # older toga_winforms only
        relative_path: 'libs/WebView2/x86'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_x86'
    - from_filenames:
        relative_path: 'libs/WebView2/runtimes/win-x64/native'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_amd64'
    - from_filenames: # older toga_winforms only
        relative_path: 'libs/WebView2/x64'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_amd64'

    - from_filenames:
        relative_path: 'libs/WebView2'
        prefixes:
          - 'Microsoft.'
      when: 'win32'
  implicit-imports:
    - depends:
        - 'toga_winforms.libs.wrapper'
        - 'ctypes.wintypes'

- module-name: 'tokenizers.tools.visualizer' # checksum: afab16ac
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from IPython.core.display import HTML, display': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'torch' # checksum: 9b1bc67d
  parameters:
    - 'name': 'disable-jit'
      'values': 'value in ("yes", "no")'
  data-files:
    - dirs:
        - 'bin'
        - 'include'
    - patterns:
        - 'bin/*.h'
  dlls:
    - from_filenames:
        relative_path: 'lib'
        prefixes:
          - ''
  anti-bloat:
    - description: 'allow unittest inside of torch, too dependent to remove'
      bloat-mode-overrides:
        'unittest': 'allow'
      when: 'not use_unittest'
    - description: 'remove setuptools usage'
      no-auto-follow:
        'torch.utils.cpp_extension': 'ignore'
        'triton': 'ignore'
      when: 'not use_setuptools'
    - description: 'remove numba reference'
      no-auto-follow:
        'numba': 'ignore'
      when: 'not use_numba and standalone'
    - description: 'remove pytest reference'
      no-auto-follow:
        'pytest': 'ignore'
        '_pytest': 'ignore'
      when: 'not use_pytest'

  options:
    checks:
      - description: "Torch JIT is disabled by default in standalone mode, make a choice explicit with '--module-parameter=torch-disable-jit=yes|no'"
        support_info: 'parameter'
        when: 'standalone and get_parameter("disable-jit", None) is None'
  import-hacks:
    - force-environment-variables:
        'PYTORCH_JIT': '0'
      when: 'get_parameter("disable-jit", "yes" if standalone else "no") == "yes"'
    - find-dlls-near-module:
        - 'nvidia'

- module-name: 'torch._dynamo.polyfills.loader' # checksum: e5019356
  implicit-imports:
    - depends:
        - 'torch._dynamo.polyfills.*'

- module-name: 'torch._dynamo.skipfiles' # checksum: 120e0012
  anti-bloat:
    # When calling inspect.getfile() on Nuitka's loader, it will yield
    # "__module__" as "__nuitka__" but that's not in "sys.modules" and won't
    # have a "__file__" value, so make sure to ignore it.
    - description: "work around our '__loader__' failing check"
      replacements_plain:
        'if inspect.isclass(obj)': 'if inspect.isclass(obj) and obj is not __loader__'

- module-name: 'torch._dynamo.utils' # checksum: fd1c674
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'import_submodule': "'''(lambda mod: [importlib.import_module(mod.__name__ + '.' + p.name) for p in pkgutil.iter_modules(mod.__path__) if p.name[0] != '_'])'''"
      append_plain: 'import pkgutil'

- module-name: 'torch._dynamo.variables.builder' # checksum: 6ef7624b
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'has_triton()': 'False'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.codecache' # checksum: bb14e715
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'from torch.utils import cpp_extension': ''
      change_function:
        'get_include_and_linking_paths': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.codegen.common' # checksum: 5d438f8d
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'from .cpp import DTYPE_TO_CPP, INDEX_TYPE': 'raise ImportError'
        'from .cpp import DTYPE_TO_CPP': 'raise ImportError'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.coordinate_descent_tuner' # checksum: 3a49d1a
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'if has_triton()': 'if False'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.decomposition' # checksum: 54e7450c
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      change_function:
        'should_pad_bench': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.graph' # checksum: a09372f0
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'from .codegen.wrapper import CppWrapperCodeGen, WrapperCodeGen': ''
      when: 'not use_setuptools'

- module-name: 'torch._inductor.ir' # checksum: f4ea1725
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'from torch._inductor.codegen.wrapper import CppWrapperCodeGen': 'raise ImportError'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.scheduler' # checksum: 78900add
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'from .codegen.wrapper import buffer_reuse_key': 'raise ImportError'
        'from .codegen.cpp import CppScheduling': 'raise ImportError'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.sizevars' # checksum: bc71cf2e
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        # This could actually be done, by creating the "PythonPrinter().doprint"
        # locally
        'from .codegen.wrapper import pexpr': 'raise ImportError'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.triton_heuristics' # checksum: 3a49d1a
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'if has_triton()': 'if False'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.triton_ops.autotune' # checksum: f3c779b5
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'from ..codecache import cache_dir': ''
        'cache_dir()': '__nuitka_binary_dir, ".cache"'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.triton_ops.conv' # checksum: 6185b84c
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'has_triton()': 'False'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.triton_ops.conv1x1' # checksum: 6185b84c
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'has_triton()': 'False'
      when: 'not use_setuptools'

- module-name: 'torch._inductor.utils' # checksum: 7dd0005
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'if _has_triton()': 'if False'
      change_function:
        'has_triton': "'(lambda: False)'"
        'do_bench': 'un-callable'
        'get_device_tflops': 'un-callable'
        'get_gpu_dram_gbps': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'torch._jit_internal' # checksum: f49ee799
  anti-bloat:
    - description: 'disable jit warnings'
      replacements_plain:
        'warnings.warn(': 'if False: warnings.warn('

- module-name: 'torch.fx._symbolic_trace' # checksum: d4866b6d
  anti-bloat:
    - description: 'workaround incompatible check'
      replacements_plain:
        "f.f_code.co_name != '<module>'": "not f.f_code.co_name.startswith('<module')"
        'f.f_code.co_name != "<module>"': "not f.f_code.co_name.startswith('<module')"

- module-name: 'torch.onnx.symbolic_registry' # checksum: 836242c
  implicit-imports:
    - depends:
        - 'torch.onnx.symbolic_opset7'
        - 'torch.onnx.symbolic_opset12'
        - 'torch.onnx.symbolic_opset14'
        - 'torch.onnx.symbolic_opset15'

- module-name: 'torch.sparse._triton_ops' # checksum: 8eb5107d
  anti-bloat:
    - description: 'remove setuptools usage via triton'
      replacements_plain:
        'if _has_triton()': 'if False'
        'if has_triton()': 'if False'
      change_function:
        '_has_triton': "'(lambda: False)'"
      when: 'not use_setuptools'

- module-name: 'torch.testing._internal.common_utils' # checksum: c0669cf
  anti-bloat:
    - description: 'remove pytest reference'
      change_function:
        'run_tests': 'un-callable'
        'get_pytest_test_cases': 'un-callable'
      when: 'not use_pytest'

- module-name: 'torch.utils._config_module' # checksum: 1613f7c7
  constants:
    declarations:
      'torch_config_module_candidates': '[m for m in iterate_modules("torch") if m.split(".")[-1] in ("config", "_config")]'
  variables:
    setup_code:
      - 'import importlib'
    declarations:
      'torch_config_modules': 'dict((m,importlib.import_module(m)._compile_ignored_keys) for m in torch_config_module_candidates if hasattr(importlib.import_module(m), "_compile_ignored_keys"))'

  anti-bloat:
    - description: 'workaround for torch config modules'
      append_result: |
        """_static_compile_ignored_keys = %s

        def get_assignments_with_compile_ignored_comments(module):
          return _static_compile_ignored_keys[module.__name__]
        """ % repr(get_variable("torch_config_modules"))

- module-name: 'torch.utils.data._typing' # checksum: f6ea0eb5
  anti-bloat:
    - description: 'compatibility workaround'
      replacements_plain:
        ? "\ndef _dp_init_subclass"
        : |-
          @classmethod
          def _dp_init_subclass

- module-name: 'torch_scatter' # checksum: 81d9f6ec
  anti-bloat:
    - description: 'workaround finding DLLs as modules by wrong names'
      replacements_plain:
        "f'{library}_cuda', [osp.dirname(__file__)]": "f'torch_scatter.{library}_cuda'"
        "f'{library}_cpu', [osp.dirname(__file__)]": "f'torch_scatter.{library}_cpu'"
        'importlib.machinery.PathFinder()': 'importlib.util'
  implicit-imports:
    - depends:
        - '._version_cpu'
        - '._scatter_cpu'
        - '._segment_csr_cpu'
        - '._segment_coo_cpu'
        - '._version_cuda'
        - '._scatter_cuda'
        - '._segment_csr_cuda'
        - '._segment_coo_cuda'

- module-name: 'torchaudio' # checksum: e38b176e
  import-hacks:
    - find-dlls-near-module:
        - 'torch'
    - acceptable-missing-dlls:
        - '_torchaudio_ffmpeg'

- module-name: 'torchaudio.lib._torchaudio' # checksum: 7b940206
  implicit-imports:
    - depends:
        - 'torchaudio.lib.libtorchaudio'

- module-name: 'torchmetrics.utilities.checks' # checksum: b4f61938
  anti-bloat:
    - description: 'remove unittest reference'
      replacements_plain:
        'from unittest.mock import Mock': ''
        'isinstance(instance_attr, Mock)': 'False'

- module-name: 'torchvision' # checksum: 32aa7aff
  dlls:
    - from_filenames:
        prefixes:
          - 'zlib'
          - 'libjpeg'
          - 'libpng*'

- module-name: 'torchvision.extension' # checksum: 9de523e5
  implicit-imports:
    - depends:
        # This is really only a DLL, but named according to extension module rules.
        - '._C'

- module-name: 'torchvision.io.image' # checksum: 2f2fb05c
  implicit-imports:
    - depends:
        - 'torchvision.image'

- module-name: 'torchvision.ops._register_onnx_ops' # checksum: a35b139f
  implicit-imports:
    - depends:
        - 'torch.onnx.symbolic_opset8'

- module-name: 'torio.io._streaming_media_decoder' # checksum: a43ec55f
  anti-bloat:
    - description: 'remove docstring dependency'
      replacements_plain:
        'obj.__doc__ = obj.__doc__.format(**kwargs)': ''
      when: 'no_docstrings'

- module-name: 'torio.io._streaming_media_encoder' # checksum: a43ec55f
  anti-bloat:
    - description: 'remove docstring dependency'
      replacements_plain:
        'obj.__doc__ = obj.__doc__.format(**kwargs)': ''
      when: 'no_docstrings'

- module-name: 'tornado.httputil' # checksum: 4d59551a
  anti-bloat:
    - description: 'remove doctest usage'
      change_function:
        'doctests': "'(lambda: None)'"

- module-name: 'tornado.iostream' # checksum: 4d59551a
  anti-bloat:
    - description: 'remove doctest usage'
      change_function:
        'doctests': "'(lambda: None)'"

- module-name: 'tornado.util' # checksum: 4d59551a
  anti-bloat:
    - description: 'remove doctest usage'
      change_function:
        'doctests': "'(lambda: None)'"

- module-name: 'tqdm' # checksum: f48e841d
  anti-bloat:
    - description: 'remove IPython reference'
      change_function:
        'tnrange': "'(lambda *args, **kwargs: None)'"
        'tqdm_notebook': "'(lambda *args, **kwargs: None)'"
      when: 'not use_ipython'
    - description: 'remove matplotlib reference'
      no-auto-follow:
        'matplotlib': 'ignore'
    - description: 'remove setuptools reference'
      no-auto-follow:
        'setuptools_scm': 'ignore'
      when: 'not use_setuptools'
    - description: 'remove pandas reference'
      no-auto-follow:
        'pandas': 'ignore'

- module-name: 'tqdm.autonotebook' # checksum: 6ac095cb
  anti-bloat:
    - description: 'remove IPython reference'
      module_code: "from .std import tqdm, trange\n"
      when: 'not use_ipython'

- module-name: 'tracerite' # checksum: fe3c0d7b
  data-files:
    - patterns:
        - '*.css'

- module-name: 'transformers' # checksum: d013b95f
  data-files:
    - include-metadata:
        - 'accelerate'
        - 'bitsandbytes'
        - 'datasets'
        - 'flax'
        - 'ftfy'
        - 'jinja2'
        - 'pandas'
        - 'peft'
        - 'psutil'
        - 'tensorflow'
        - 'torch'
        - 'tokenizers'
        - 'torchvision'
        - 'scipy'
        - 'safetensors'
        - 'sentencepiece'
        - 'timm'
        - 'openai'
        - 'pygments'
        - 'soundfile'
        - 'tqdm'

  anti-bloat:
    - description: 'remove IPython reference'
      no-auto-follow:
        'IPython': 'ignore'
      when: 'not use_ipython'

- module-name: 'transformers.integrations' # checksum: ccb3fd3
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'from transformers.utils.notebook import NotebookProgressCallback': 'raise ImportError'
      when: 'not use_ipython'

- module-name: 'transformers.models.auto.configuration_auto' # checksum: d40efb44
  anti-bloat:
    - description: 'remove docstring dependency'
      change_function:
        'docstring_decorator': "'(lambda fn: fn)'"
      when: 'no_docstrings'

- module-name: 'transformers.models.deformable_detr.load_custom' # checksum: baab8e6b
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'load_cuda_kernels': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'transformers.models.deformable_detr.modeling_deformable_detr' # checksum: baab8e6b
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'load_cuda_kernels': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'transformers.models.deprecated.deta.modeling_deta' # checksum: baab8e6b
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'load_cuda_kernels': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'transformers.models.deta.modeling_deta' # checksum: baab8e6b
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'load_cuda_kernels': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'transformers.models.grounding_dino.modeling_grounding_dino' # checksum: baab8e6b
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'load_cuda_kernels': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'transformers.models.mra.modeling_mra' # checksum: a554c6d
  anti-bloat:
    - description: 'remove setuptools usage'
      replacements_plain:
        'from torch.utils.cpp_extension import load': ''
      change_function:
        'load_cuda_kernels': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'transformers.models.omdet_turbo.modeling_omdet_turbo' # checksum: baab8e6b
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'load_cuda_kernels': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'transformers.models.rt_detr.modeling_rt_detr' # checksum: baab8e6b
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'load_cuda_kernels': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'transformers.models.rwkv.modeling_rwkv' # checksum: f186b69e
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'load_wkv_cuda_kernel': "'(lambda : None)'"
      when: 'not use_setuptools'

- module-name: 'transformers.models.yoso.modeling_deta' # checksum: baab8e6b
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'load_cuda_kernels': 'un-callable'
      when: 'not use_setuptools'

- module-name: 'transformers.models.yoso.modeling_yoso' # checksum: 7f1a78b7
  anti-bloat:
    - description: 'remove setuptools usage'
      change_function:
        'load_cuda_kernels': "'(lambda : False)'"
      when: 'not use_setuptools'

- module-name: 'transformers.processing_utils' # checksum: cdb3f70d
  anti-bloat:
    - description: 'workaround manual import issue'
      replacements_plain:
        'transformers_module = direct_transformers_import(Path(__file__).parent)': 'import transformers as transformers_module'

- module-name: 'transformers.tokenization_utils_base' # checksum: 836ef550
  anti-bloat:
    - description: 'disable transformers warnings'
      replacements_plain:
        'warnings.warn(': 'if False: warnings.warn('

- module-name: 'transformers.trainer' # checksum: f71d17eb
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'is_in_notebook()': 'False'
      when: 'not use_ipython'

- module-name: 'transformers.utils.doc' # checksum: e7a27fa2
  anti-bloat:
    - description: 'add support for compiled functions'
      append_plain: |
        def copy_func(f):
            try:
                g = f.clone()
            except AttributeError:
                g = types.FunctionType(f.__code__, f.__globals__, name=f.__name__, argdefs=f.__defaults__, closure=f.__closure__)
                g = functools.update_wrapper(g, f)
                g.__kwdefaults__ = f.__kwdefaults__

            return g
    - description: 'remove docstring dependency'
      change_function:
        '_prepare_output_docstrings': "'(lambda *args, **kwargs: str())'"
        'docstring_decorator': "'(lambda fn: fn)'"
      when: 'no_docstrings'

- module-name: 'transformers.utils.hub' # checksum: 81f43bda
  implicit-imports:
    - depends:
        - 'huggingface_hub.hf_api'

- module-name: 'transformers.utils.import_utils' # checksum: 705fafc4
  anti-bloat:
    - description: 'workaround for metadata checks not recognized'
      replacements_plain:
        'from .versions import importlib_metadata': 'import importlib.metadata as importlib_metadata'
      when: 'python38_or_higher'
    - description: 'workaround for metadata checks not recognized'
      replacements_plain:
        'from .versions import importlib_metadata': 'import importlib_metadata'
      when: 'before_python38'
    - description: 'workaround for tensorflow checks preventing pytorch usage'
      replacements_plain:
        'importlib.util.find_spec("tensorflow") is not None': 'False'
    - description: 'more clear missing metadata error message'
      replacements_plain:
        'package_exists = False': "sys.exit('''Nuitka: Need to use this as an option to compile with '--include-distribution-metadata=%s'.''' % pkg_name)"
      when: 'not deployment'

- module-name: 'transformers.utils.versions' # checksum: 7a77f5b1
  anti-bloat:
    - description: 'disable runtime metadata check for core dependencies'
      change_function:
        # TODO: Forcing inline of this will be better solution.
        'require_version_core': "'(lambda requirement: None)'"

- module-name: 'travertino' # checksum: 4e5a6f7
  data-files:
    - include-metadata:
        - 'travertino'

  anti-bloat:
    - description: 'remove setuptools usage'
      no-auto-follow:
        'setuptools_scm': 'ignore'
      when: 'not use_setuptools'

- module-name: 'trimesh.viewer.notebook' # checksum: d055e332
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        'ipy = get_ipython()': 'raise ImportError'
      change_function:
        'scene_to_notebook': 'un-callable'
      when: 'not use_ipython'

- module-name: 'trimesh.viewer.windowed' # checksum: 8dcf1242
  anti-bloat:
    - description: 'remove IPython reference (used for profiling)'
      replacements_plain:
        'if self._profile:': 'if False:'
      when: 'not use_ipython'

- module-name: 'trimesh.voxel.ops' # checksum: 73918e24
  anti-bloat:
    - description: 'remove numba reference'
      change_function:
        'boolean_sparse': 'un-callable'
      when: 'not use_numba'

- module-name: 'trio._core._ki' # checksum: 40560a43
  anti-bloat:
    - description: 'workaround for trio compatibility'
      # The only incompatibility in Trio is the way it handles KeyboardInterrupt exceptions (ctrl+C):
      # https://github.com/Nuitka/Nuitka/issues/561
      # https://github.com/python-trio/trio/issues/1752
      # It does this to ensure that Trio's internal data structures stay consistent and that the
      # `finally` blocks in suspended coroutines are all run:
      # https://vorpus.org/blog/control-c-handling-in-python-and-trio/
      # So, be warned, when this plugin is enabled, your Trio code may not behave as expected when the
      # user hits CTRL+C. One option to cope with this is to run your Trio code in a separate thread,
      # listen for it in the main thread (with a try/except block), then notify the Trio thread to
      # shutdown (e.g. with `trio_token.run_sync_soon(cancel_scope.cancel())`).
      append_plain: |
        def enable_ki_protection(fn):
          return fn
        def disable_ki_protection(fn):
          return fn
  options:
    checks:
      - description: "Disabled careful handling of KeyboardInterrupt in 'trio'"
        support_info: 'info'

- module-name: 'trio._core._multierror' # checksum: 15c0e7fc
  anti-bloat:
    - description: 'remove IPython reference'
      replacements_plain:
        '"IPython" in sys.modules': 'False'
      when: 'not use_ipython'

- module-name: 'trio._core._run' # checksum: 8aed5b01
  anti-bloat:
    - description: 'workaround for trio compatibility'
      replacements_plain:
        'coro.cr_frame.f_locals.setdefault(LOCALS_KEY_KI_PROTECTION_ENABLED, system_task)': ''
    - description: 'workaround for trio compatibility'
      replacements_plain:
        'assert coro.cr_frame is not None, "Coroutine frame should exist"': ''
      when: 'version("trio") >= (0,26,1)'

- module-name: 'tsdownsample._rust' # checksum: d072aa03
  implicit-imports:
    - depends:
        - 'numpy.core.multiarray'

- module-name: 'tsfresh.feature_extraction.data' # checksum: 59682295
  anti-bloat:
    - description: 'remove dask reference via distributed module'
      replacements_plain:
        'from dask import dataframe as dd': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'tsfresh.feature_extraction.feature_calculators' # checksum: e0796376
  anti-bloat:
    - description: 'remove numba reference via stumpy module'
      replacements_plain:
        'import stumpy': ''
      when: 'not use_numba'

- module-name: 'tsfresh.utilities.distribution' # checksum: e497e012
  anti-bloat:
    - description: 'remove dask reference via distributed module'
      replacements_plain:
        'from distributed import Client, LocalCluster': 'raise ImportError'
        'from distributed import Client': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'tufup.repo' # checksum: 737f453f
  anti-bloat:
    - description: 'need to use setuptools'
      bloat-mode-overrides:
        'setuptools': 'allow'

- module-name: 'twofish' # checksum: c8ae9b1e
  implicit-imports:
    - depends:
        - '_twofish'

- module-name: 'tzdata' # checksum: c364567b
  data-files:
    - dirs:
        - 'zones'

- module-name: 'tzdata.zoneinfo' # checksum: 1786da2c
  data-files:
    - dirs:
        - '.'

  implicit-imports:
    - depends:
        - '.*'

- module-name: 'unstructured' # checksum: e17b7f4d
  data-files:
    - patterns:
        - 'nlp/english-words.txt'

- module-name: 'urllib3' # checksum: aed85e8e
  implicit-imports:
    - depends:
        - 'urllib3'
        - 'urllib3._collections'
        - 'urllib3.connection'
        - 'urllib3.connection.appengine'
        - 'urllib3.connectionpool'
        - 'urllib3.contrib'
        - 'urllib3.contrib.appengine'
        - 'urllib3.exceptions'
        - 'urllib3.fields'
        - 'urllib3.filepost'
        - 'urllib3.packages'
        - 'urllib3.packages.six'
        - 'urllib3.packages.ssl_match_hostname'
        - 'urllib3.poolmanager'
        - 'urllib3.request'
        - 'urllib3.response'
        - 'urllib3.util'
        - 'urllib3.util.connection'
        - 'urllib3.util.queue'
        - 'urllib3.util.request'
        - 'urllib3.util.response'
        - 'urllib3.util.retry'
        - 'urllib3.util.ssl_'
        - 'urllib3.util.timeout'
        - 'urllib3.util.url'
        - 'urllib3.util.wait'
        - 'urllib.error'
        - 'urllib.parse'
        - 'urllib.request'
        - 'urllib.response'

- module-name: 'urllib3.packages.six' # checksum: e62bccb7
  implicit-imports:
    - depends:
        - 'queue'

- module-name: 'ursina' # checksum: e8b5dd34
  data-files:
    - dirs:
        - 'models_compressed'

- module-name: 'usb1' # checksum: 5cbb45ba
  dlls:
    - from_filenames:
        prefixes:
          - 'libusb'

- module-name: 'uvicorn' # checksum: e24c61dd
  implicit-imports:
    - depends:
        - 'uvicorn.loops'
        - 'uvicorn.lifespan'
        - 'uvicorn.protocols'

- module-name: 'uvicorn.config' # checksum: 243a5175
  implicit-imports:
    - depends:
        - 'websockets.legacy.server'
        - 'uvicorn.logging'

- module-name: 'uvicorn.importer' # checksum: ada37bba
  anti-bloat:
    - description: 'better error message for string imports'
      replacements_plain:
        'Could not import module "{module_str}"': 'Nuitka: Could not import module "{module_str}" use "--include-module={module_str}" option during compilation'
      when: 'not deployment'

- module-name: 'uvicorn.lifespan' # checksum: f7b81412
  implicit-imports:
    - depends:
        - 'uvicorn.lifespan.off'
        - 'uvicorn.lifespan.on'

- module-name: 'uvicorn.loops' # checksum: bf1f093c
  implicit-imports:
    - depends:
        - 'uvicorn.loops.auto'
        - 'uvicorn.loops.uvloop'

- module-name: 'uvicorn.protocols' # checksum: 77f78781
  implicit-imports:
    - depends:
        - 'uvicorn.protocols.http'
        - 'uvicorn.protocols.websockets'

- module-name: 'uvicorn.protocols.http' # checksum: e25f6bce
  implicit-imports:
    - depends:
        - 'uvicorn.protocols.http.auto'
        - 'uvicorn.protocols.http.h11_impl'
        - 'uvicorn.protocols.http.httptools_impl'

- module-name: 'uvicorn.protocols.websockets' # checksum: 561c2732
  implicit-imports:
    - depends:
        - 'uvicorn.protocols.websockets.auto'
        - 'uvicorn.protocols.websockets.websockets_impl'
        - 'uvicorn.protocols.websockets.wsproto_impl'

- module-name: 'uvloop.loop' # checksum: d4d134f3
  implicit-imports:
    - depends:
        - 'uvloop._noop'

- module-name: 'vedo' # checksum: 35ad0fe8
  data-files:
    - dirs:
        - 'fonts'

- module-name: 'vibora' # checksum: 3e4e581a
  anti-bloat:
    - description: 'remove test code'
      replacements_plain:
        'from .tests import *': ''

- module-name: 'vibora.protocol.cprotocol' # checksum: 9b7c23b0
  implicit-imports:
    - depends:
        - 'vibora.protocol.cwebsocket'

- module-name: 'vibora.responses.responses' # checksum: e0de35c7
  implicit-imports:
    - depends:
        - 'vibora.constants'

- module-name: 'vibora.router.router' # checksum: ec60a103
  implicit-imports:
    - depends:
        - 'vibora.router.parser'

- module-name: 'vosk' # checksum: 77da6060
  dlls:
    - from_filenames:
        prefixes:
          - 'libvosk'

- module-name: 'vtk' # checksum: e1e59251
  import-hacks:
    # This module aliases itself to another package, for which we will
    # not find packages in unless added.
    - package-paths:
        - 'vtkmodules'

- module-name: 'vtkmodules' # checksum: 8db0fa0f
  implicit-imports:
    - depends:
        - 'vtkmodules.all'
        - 'vtkmodules.util'

- module-name: 'vtkmodules.qt' # checksum: e9590444
  implicit-imports:
    - depends:
        - 'vtkmodules.qt.QVTKRenderWindowInteractor'

- module-name: 'vtkmodules.tk' # checksum: 29689455
  implicit-imports:
    - depends:
        - 'vtkmodules.tk.vtkLoadPythonTkWidgets'
        - 'vtkmodules.tk.vtkTkImageViewerWidget'
        - 'vtkmodules.tk.vtkTkPhotoImage'
        - 'vtkmodules.tk.vtkTkRenderWidget'
        - 'vtkmodules.tk.vtkTkRenderWindowInteractor'

- module-name: 'vtkmodules.util' # checksum: 510a8c9e
  implicit-imports:
    - depends:
        - 'vtkmodules.util.misc'
        - 'vtkmodules.util.numpy_support'
        - 'vtkmodules.util.vtkAlgorithm'
        - 'vtkmodules.util.vtkConstants'
        - 'vtkmodules.util.vtkImageExportToArray'
        - 'vtkmodules.util.vtkImageImportFromArray'
        - 'vtkmodules.util.vtkMethodParser'
        - 'vtkmodules.util.vtkVariant'

- module-name: 'vtkmodules.wx' # checksum: cfc06181
  implicit-imports:
    - depends:
        - 'vtkmodules.wx.wxVTKRenderWindow'
        - 'vtkmodules.wx.wxVTKRenderWindowInteractor'

- module-name: 'weasyprint' # checksum: 19961a08
  data-files:
    - dirs:
        - 'css'
    - patterns:
        - 'VERSION'

- module-name: 'webview' # checksum: f4e8fc01
  data-files:
    - dirs:
        - 'js'
        - 'lib'
  dlls:
    - from_filenames:
        relative_path: 'lib/runtimes/win-x86/native'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_x86'
    - from_filenames:
        relative_path: 'lib/runtimes/win-x64/native'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_amd64'
    - from_filenames:
        relative_path: 'lib/runtimes/win-arm64/native'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_arm64'
    - from_filenames:
        relative_path: 'lib/x86'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_x86'
    - from_filenames:
        relative_path: 'lib/x64'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_amd64'
    - from_filenames:
        relative_path: 'lib/arm64'
        prefixes:
          - 'WebView2Loader'
      when: 'win32 and arch_arm64'
    - from_filenames:
        relative_path: 'lib'
        prefixes:
          - 'Microsoft.'
      when: 'win32'
    - from_filenames:
        relative_path: 'lib'
        prefixes:
          - 'WebBrowserInterop.x86'
      when: 'win32 and arch_x86'
    - from_filenames:
        relative_path: 'lib'
        prefixes:
          - 'WebBrowserInterop.x64'
      when: 'win32 and arch_amd64'

- module-name: 'webview.platforms.edgechromium' # checksum: e798fc8d
  anti-bloat:
    - description: 'workaround unused platform DLL checks'
      replacements_plain:
        "';' + interop_dll_path(platform)": "';' + os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'lib', platform))"

- module-name: 'webview.platforms.winforms' # checksum: 38f993f6
  anti-bloat:
    - replacements_plain:
        'ExtractIconW(handle, sys.executable, 0)': 'ExtractIconW(handle, sys.argv[0], 0)'
      when: 'standalone and win32'

- module-name: 'werkzeug.debug.repr' # checksum: f8cccd39
  anti-bloat:
    - no-auto-follow:
        'pydoc': 'ignore'

- module-name: 'werkzeug.serving' # checksum: fcfdb30e
  anti-bloat:
    - description: 'remove ability to run with reloader'
      replacements_plain:
        'if use_reloader:': 'if False:'

- module-name: 'wheel' # checksum: 737f453f
  anti-bloat:
    - description: 'need to use setuptools'
      bloat-mode-overrides:
        'setuptools': 'allow'

- module-name: 'win32com' # checksum: 8d4a0be8
  import-hacks:
    # This package adds another directory to the search path of itself,
    # which we will not find packages in unless added.
    - package-dirs:
        - 'win32comext'
      when: 'win32'

- module-name: 'win32com.server.register' # checksum: ee461289
  anti-bloat:
    - replacements_plain:
        'pythoncom.frozen': '1'
        'sys.frozen': 'True'
        'GetShortPathName(sys.executable)': 'GetShortPathName(sys.argv[0])'
      change_function:
        'ReExecuteElevated': "'''raise (RuntimeError('Nuitka: Needs to be elevated already, use --windows-uac-admin'))'''"
  implicit-imports:
    - depends:
        - 'win32timezone'

- module-name: 'win32file' # checksum: 2531fce0
  implicit-imports:
    - depends:
        - 'win32timezone'

- module-name: 'win32ui' # checksum: 2f1beca2
  implicit-imports:
    - pre-import-code:
        # That is a namespace module only, but it seems the extension modules needs it,
        # despite the DLLs being present.
        - 'import pywin32_system32'
      when: 'win32'

- module-name: 'winloop' # checksum: 9d1257f8
  implicit-imports:
    - depends:
        - '._noop'

- module-name: 'wx' # checksum: 9a4d4164
  options:
    checks:
      - description: 'wx will crash in console mode during startup'
        macos_bundle: 'yes'
        when: 'macos'
      - description: 'wx requires program to be in bundle form'
        macos_bundle: 'yes'
        when: 'macos'

- module-name: 'wx.html2' # checksum: bc8985c4
  dlls:
    - from_filenames:
        prefixes:
          - 'WebView2Loader'
      when: 'win32'

- module-name: 'xarray' # checksum: 438c71c
  data-files:
    - dirs:
        - 'static'

- module-name: 'xarray.backends.locks' # checksum: 3d3302db
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        # TODO: This should be something special, where imports for a name
        # are all forced to ImportError during building and/or optimization.
        'from dask.utils import SerializableLock': 'raise ImportError'
        'from dask.distributed import Lock as DistributedLock': 'raise ImportError'
        'import dask': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'xarray.coding.variables' # checksum: a79667c9
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'if is_duck_dask_array(array):': 'if False:'
      when: 'not use_dask'

- module-name: 'xarray.core.dask_array_ops' # checksum: dc644550
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'import dask.array as dask_array': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'xarray.core.dataset' # checksum: 1599cfc0
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'import dask.array as dask_array': 'raise ImportError'
        'if lazy_data:': 'if False:'
        'from dask.delayed import Delayed': 'raise ImportError'
      change_function:
        '__dask_tokenize__': 'un-callable'
        '__dask_graph__': 'un-callable'
        '__dask_keys__': 'un-callable'
        '__dask_layers__': 'un-callable'
        '__dask_optimize__': 'un-callable'
        '__dask_scheduler__': 'un-callable'
        '_dask_postcompute': 'un-callable'
        'to_dask_dataframe': 'un-callable'
        '_get_chunk': 'un-callable'
        '_maybe_chunk': 'un-callable'
        '_dask_postpersist': 'un-callable'
      when: 'not use_dask'

- module-name: 'xarray.core.duck_array_ops' # checksum: ad7bcf75
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'import dask.array as dask_array': 'raise ImportError'
        'dask_array_compat, dask_array_ops, ': ''
      when: 'not use_dask'

- module-name: 'xarray.core.indexing' # checksum: 5c2e5b3e
  anti-bloat:
    - description: 'remove optional dask usage'
      change_function:
        '_dask_array_with_chunks_hint': 'un-callable'
      when: 'not use_dask'

- module-name: 'xarray.core.missing' # checksum: c9a72fc3
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'if is_duck_dask_array(var):': 'if False:'
      when: 'not use_dask'

- module-name: 'xarray.core.nanops' # checksum: dc644550
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'import dask.array as dask_array': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'xarray.core.nputils' # checksum: 190174a0
  data-files:
    - include-metadata:
        - 'numpy'

- module-name: 'xarray.core.parallel' # checksum: 37f8ca49
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'import dask': 'raise ImportError'
      when: 'not use_dask'

- module-name: 'xarray.core.pycompat' # checksum: e171aa31
  anti-bloat:
    - description: 'remove optional dask usage'
      replacements_plain:
        'mod == "dask"': 'False'
        'dsk.available': 'False'
      when: 'not use_dask'

- module-name: 'xarray.core.utils' # checksum: ff47c8d1
  anti-bloat:
    - description: 'remove optional dask usage'
      change_function:
        # TODO: Rather than un-callable, these we may want to just skip
        '__dask_tokenize__': 'un-callable'
      when: 'not use_dask'

- module-name: 'xarray.core.variable' # checksum: df985c92
  anti-bloat:
    - description: 'remove optional dask usage'
      change_function:
        # TODO: Rather than un-callable, these we may want to just skip
        '__dask_tokenize__': 'un-callable'
        'chunk': 'un-callable'
      when: 'not use_dask'

- module-name: 'xgboost' # checksum: 49521496
  data-files:
    - patterns:
        - 'VERSION'

- module-name: 'xgboost.core' # checksum: 2dcd7df3
  dlls:
    - from_filenames:
        relative_path: 'lib'
        prefixes:
          - 'xgboost'
          - 'libxgboost'

- module-name: 'xgboost.sklearn' # checksum: d0a963f4
  anti-bloat:
    - description: 'remove docstring dependency'
      replacements_plain:
        'fit.__doc__ = XGBModel.fit.__doc__.replace(': 'fit.__doc__ = "Fit gradient boosting model".replace('
        'assert XGBModel.fit.__doc__ is not None': ''
      when: 'no_docstrings'

- module-name: 'Xlib.display' # checksum: 3b1d6e64
  implicit-imports:
    - depends:
        - 'Xlib.ext.*'

- module-name: 'Xlib.support.connect' # checksum: 60d321da
  implicit-imports:
    - depends:
        - 'Xlib.support.unix_connect'

- module-name: 'Xlib.XK' # checksum: 6b59ce94
  implicit-imports:
    - depends:
        - 'Xlib.keysymdef.*'

- module-name: 'xmlschema' # checksum: c1520175
  data-files:
    - dirs:
        - 'schemas'

- module-name: 'xyzservices' # checksum: 6aaa99cc
  data-files:
    - dirs:
        - 'data'

- module-name: 'yapf_third_party._ylib2to3' # checksum: a54cab2a
  data-files:
    - patterns:
        - 'Grammar.txt'
        - 'PatternGrammar.txt'

- module-name: 'yt_dlp' # checksum: 7c4ae87e
  implicit-imports:
    - depends:
        - 'yt_dlp.utils._deprecated'

- module-name: 'zaber_motion' # checksum: 11ddc56d
  implicit-imports:
    - depends:
        - 'zaber_motion_bindings_windows'
      when: 'win32 and version("zaber_motion") < (5,1,2)'
    - depends:
        - 'zaber_motion_bindings_linux'
      when: 'linux and version("zaber_motion") < (5,1,2)'
    - depends:
        - 'zaber_motion_bindings_darwin'
      when: 'macos and version("zaber_motion") < (5,1,2)'
    - depends:
        - 'bindings'
      when: 'version("zaber_motion") == (5,1,2)'
    - depends:
        - 'zaber_motion_bindings'
      when: 'version("zaber_motion") >= (5,1,3)'

- module-name: 'zaber_motion.bindings' # checksum: ebad5220
  anti-bloat:
    - description: 'normalize path for DLLs'
      replacements_plain:
        'os.path.join(os.path.dirname(__file__), "..", "bindings", lib_name)': 'os.path.normpath(os.path.join(os.path.dirname(__file__), "..", "bindings", lib_name))'

- module-name: 'zaber_motion_bindings' # checksum: 679f33dc
  dlls:
    - from_filenames:
        prefixes:
          - 'zaber-motion-lib'
      when: 'None is not version("zaber_motion") >= (5,1,3)'

- module-name: 'zaber_motion_bindings_darwin' # checksum: dbde56ea
  dlls:
    - from_filenames:
        prefixes:
          - 'zaber-motion-lib'

- module-name: 'zaber_motion_bindings_linux' # checksum: dbde56ea
  dlls:
    - from_filenames:
        prefixes:
          - 'zaber-motion-lib'

- module-name: 'zaber_motion_bindings_windows' # checksum: dbde56ea
  dlls:
    - from_filenames:
        prefixes:
          - 'zaber-motion-lib'

- module-name: 'zeroconf._listener' # checksum: a6a63ed5
  implicit-imports:
    - depends:
        - 'zeroconf._handlers.answers'

- module-name: 'zeroconf._services.info' # checksum: abf003b9
  implicit-imports:
    - depends:
        - 'zeroconf._utils.ipaddress'

- module-name: 'zmq' # checksum: b0320bcc
  dlls:
    # Do not look at this one, using dest_path and suffixes is not
    # needed at all normally, but this one is trange and for old PyZMQ
    # only.
    - from_filenames:
        prefixes:
          - 'libzmq'
        suffixes:
          - 'pyd'
      dest_path: '.'
      when: 'win32'

- module-name: 'zmq.backend' # checksum: 139febbd
  implicit-imports:
    - depends:
        - 'zmq.backend.cython'

- module-name: 'zope.interface._compat' # checksum: 5a8f0d92
  anti-bloat:
    - description: 'remove unittest reference'
      change_function:
        # TODO: For a decorator, this looks breaking
        '_skip_under_py2': "'(lambda test_method: None)'"
        '_skip_under_py3k': "'(lambda test_method: None)'"
      when: 'not use_unittest'
