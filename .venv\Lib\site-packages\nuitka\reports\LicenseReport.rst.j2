{#     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file #}

License Report
--------------

This software was compiled with Nuitka and contains parts of the following software

{% for distribution in all_distributions %}

{{ get_distribution_name(distribution) }}
=========================================

Version {{ get_distribution_version(distribution) }} of {{ get_distribution_name(distribution) }} was included and is
under license "{{ get_distribution_license(distribution) }}".

{% if distribution_modules[distribution] != [get_distribution_name(distribution)] %}
The contained {{"packages are" if len(distribution_modules[distribution]) > 1 else "package is"}} {{" ,".join(quoted(distribution_modules[distribution]))}}.
{% endif %}

{% if distribution.read_text("LICENSE.txt") %}
.. code::

{{ distribution.read_text("LICENSE.txt") | indent(4, True) }}
{% endif %}

{% endfor %}

{#     Part of "Nuitka", an optimizing Python compiler that is compatible and   #}
{#     integrates with CPython, but also works on its own.                      #}
{#                                                                              #}
{#     Licensed under the Apache License, Version 2.0 (the "License");          #}
{#     you may not use this file except in compliance with the License.         #}
{#     You may obtain a copy of the License at                                  #}
{#                                                                              #}
{#        http://www.apache.org/licenses/LICENSE-2.0                            #}
{#                                                                              #}
{#     Unless required by applicable law or agreed to in writing, software      #}
{#     distributed under the License is distributed on an "AS IS" BASIS,        #}
{#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. #}
{#     See the License for the specific language governing permissions and      #}
{#     limitations under the License.                                           #}
