pyusb-1.3.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyusb-1.3.1.dist-info/LICENSE,sha256=A-Of3O6cGPL50MNQCpk93qwFBpXrgQcOpBNHWHx2p_4,1513
pyusb-1.3.1.dist-info/METADATA,sha256=oNUqlML2CdMeCEJvFxZFQxKS_lD7qEkDmWTWQvdBV80,2469
pyusb-1.3.1.dist-info/RECORD,,
pyusb-1.3.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyusb-1.3.1.dist-info/WHEEL,sha256=OVMc5UfuAQiSplgO0_WdW7vXVGAt9Hdd6qtN4HotdyA,91
pyusb-1.3.1.dist-info/top_level.txt,sha256=vUA5f-j92oxPnUR_XbCoOoxppBW-dP8feOD7BmVMMtE,4
usb/__init__.py,sha256=Jztfuj3-tfkJIiTPzD-fTw4AX_m3tMVRBVjIGw89gl0,4018
usb/__pycache__/__init__.cpython-312.pyc,,
usb/__pycache__/_debug.cpython-312.pyc,,
usb/__pycache__/_interop.cpython-312.pyc,,
usb/__pycache__/_lookup.cpython-312.pyc,,
usb/__pycache__/_objfinalizer.cpython-312.pyc,,
usb/__pycache__/_version.cpython-312.pyc,,
usb/__pycache__/control.cpython-312.pyc,,
usb/__pycache__/core.cpython-312.pyc,,
usb/__pycache__/legacy.cpython-312.pyc,,
usb/__pycache__/libloader.cpython-312.pyc,,
usb/__pycache__/util.cpython-312.pyc,,
usb/_debug.py,sha256=d-KnbQLwlkJK3DqtLiOwl1mP7AS3xERZGkgaRuYYk7M,3297
usb/_interop.py,sha256=SiFvQb6_g9-m-gUWnbVYnynDQ8J0LrcTfqT0MZaIcMI,2726
usb/_lookup.py,sha256=630ZcVloLqlwZIUq1EZ_j_rEtGExPyMZjOU97LDoLkE,3364
usb/_objfinalizer.py,sha256=kyITXHHOWWgUA1KHM5atAQbSpwJ2cMvjtGIvuFno5o8,4804
usb/_version.py,sha256=cOVPCvD2h2G_2KB6G3ddreYkIQfAnS6WqgAkF_qgGOQ,411
usb/backend/__init__.py,sha256=mb-cuX6hbOMg-ni7_UjXS4oSRSWj3Fg-cWGqP7mF5Mw,16284
usb/backend/__pycache__/__init__.cpython-312.pyc,,
usb/backend/__pycache__/libusb0.cpython-312.pyc,,
usb/backend/__pycache__/libusb1.cpython-312.pyc,,
usb/backend/__pycache__/openusb.cpython-312.pyc,,
usb/backend/libusb0.py,sha256=tdm2KSLB_y3pMqTTuxZYwafOzwH4hx2SH7wqEkqokpE,27061
usb/backend/libusb1.py,sha256=6hPJnb6WZ5BK0dPzZKFHz3W_35gRGzQmzuwg0FXzID0,36215
usb/backend/openusb.py,sha256=6oOkCbr0Xtxwh0G0J-ybtuRCDDWqxWGbmOUO-0fHR9o,28529
usb/control.py,sha256=A34AMMPBujyxCPRdnXVP1LS54sDhlTz1ZgZFyEOW8Lo,8947
usb/core.py,sha256=qmCJrMtZKuyxjh64u6nqYrCh7uffTlZLjUtaCHe8fHw,48671
usb/legacy.py,sha256=XTxBjgxxKhhrHnaba3t6JK-IMBwoO8B869Yb97iAu-A,12840
usb/libloader.py,sha256=A0d4nOwwoLMlQKm8CXhaf0aoQQdHKlsw82APg1YYrQM,7536
usb/util.py,sha256=S-igPXJfuC93PtebiQujFneB6c7nIgFSKNNpwlzRkpQ,12094
