Metadata-Version: 2.1
Name: shiboken6
Version: 6.9.0
Summary: Python/C++ bindings helper module
Author-email: Qt for Python Team <<EMAIL>>
License: LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
Project-URL: Homepage, https://pyside.org
Project-URL: Documentation, https://doc.qt.io/qtforpython
Project-URL: Repository, https://code.qt.io/cgit/pyside/pyside-setup.git/
Project-URL: Changelog, https://code.qt.io/cgit/pyside/pyside-setup.git/tree/doc/changelogs
Project-URL: Tracker, https://bugreports.qt.io/projects/PYSIDE
Keywords: Qt
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X
Classifier: Environment :: X11 Applications :: Qt
Classifier: Environment :: Win32 (MS Windows)
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: License :: OSI Approved :: GNU General Public License v3 (GPLv3)
Classifier: License :: OSI Approved :: GNU General Public License v2 (GPLv2)
Classifier: License :: Other/Proprietary License
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: POSIX
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: C++
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Database
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Code Generators
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: User Interfaces
Classifier: Topic :: Software Development :: Widget Sets
Requires-Python: <3.14,>=3.9
Description-Content-Type: text/markdown
License-File: LicenseRef-Qt-Commercial.txt

# Shiboken6 module

The purpose of the **shiboken6 Python module**
is to access information related to the binding generation that could be used to integrate
C++ programs to Python, or even to get useful information to debug
an application.

Mostly the idea is to interact with Shiboken objects,
where one can check if it is valid, or if the generated Python wrapper
is invalid after the underlying C++ object has been destroyed.

More information on the available functions can be found
in our [official documentation](https://doc.qt.io/qtforpython/shiboken6/shibokenmodule.html)
