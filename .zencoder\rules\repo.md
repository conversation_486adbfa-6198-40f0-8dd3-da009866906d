---
description: Repository Information Overview
alwaysApply: true
---

# Gestion & Stock Information

## Summary
A comprehensive inventory and repair management system built with Python and PyQt6. The application provides functionality for managing repair orders, inventory, customers, and technicians with a desktop GUI interface and a REST API.

## Structure
- **app/**: Main application code with UI, controllers, and business logic
- **api/**: REST API implementation for remote access
- **core/**: Core business logic and domain models
- **database/**: Database models and connection management
- **utils/**: Utility functions and helpers
- **config/**: Application configuration
- **data/**: Database files and application data
- **backups/**: Database backup files
- **output/**: Generated PDF reports and receipts
- **tests/**: Test files for application components
- **scripts/**: Utility scripts for database operations and maintenance

## Language & Runtime
**Language**: Python
**Version**: 3.12.0
**Build System**: Nuitka (Python to C++ compiler)
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- PyQt6 6.9.0: GUI framework
- SQLAlchemy 2.0.40: ORM for database operations
- FastAPI 0.115.12: REST API framework
- APScheduler 3.11.0: Task scheduling
- Jinja2 3.1.6: Template engine
- Pillow 11.2.1: Image processing
- Reportlab 4.4.0: PDF generation
- python-barcode 0.15.1: Barcode generation
- pyzbar 0.1.9: Barcode scanning
- bcrypt 4.3.0: Password hashing
- PyJWT 2.10.1: JWT token handling

**Development Dependencies**:
- pytest 8.3.5: Testing framework
- Nuitka 2.7.2: Python to C++ compiler
- pyinstaller 6.13.0: Application packaging

## Build & Installation
```bash
# Create virtual environment
python -m venv .venv
# Activate virtual environment
.venv\Scripts\activate
# Install dependencies
pip install -r requirements.txt
# Initialize database
python init_db.py
# Run application
python main.py
```

## Build & Packaging
```bash
# Build with Nuitka (creates standalone executable)
python build_with_nuitka.py
# Output is in dist/main.dist/
```

## Testing
**Framework**: pytest
**Test Location**: tests/
**Run Command**:
```bash
pytest tests/
```

## Application Structure
**Main Entry Point**: main.py
**Database Models**: database/models.py, core/models/
**UI Components**: app/ui/
**Business Logic**: core/services/, app/controllers/
**API Endpoints**: api/routes/

## Features
- User authentication and role-based permissions
- Repair order management with status tracking
- Inventory management
- Customer management
- PDF report generation
- Barcode scanning and generation
- Database backup and restore
- Task scheduling
- Dark theme support