# Badges de Statut pour l'Inventaire

## Vue d'ensemble

Ce document décrit les nouvelles fonctionnalités de badges colorés et d'icônes pour les statuts critiques dans l'inventaire.

## Fonctionnalités Ajoutées

### 1. Badges de Statut Colorés

Les statuts d'inventaire sont maintenant affichés avec des badges colorés distinctifs :

- **✓ Disponible** : Vert - Article en stock normal
- **⚠ Stock bas** : Orange - Quantité proche du seuil minimum
- **✗ Rupture** : Rouge - Article en rupture de stock
- **⊘ Arrêté** : Gris - Article discontinué

### 2. Widget d'Alerte de Stock Amélioré

Le widget d'alerte de stock a été complètement refactorisé avec :

- **Interface moderne** : Design épuré avec bordures et couleurs attrayantes
- **Scroll area** : Possibilité de faire défiler les alertes si elles sont nombreuses
- **Cartes d'alerte** : Chaque alerte est affichée dans une carte dédiée avec :
  - Nom de l'article
  - Quantité actuelle vs minimum
  - Statut visuel (icône et couleur)
  - Actions rapides (commander, modifier)
- **Mise à jour automatique** : Les alertes se mettent à jour automatiquement
- **Gestion d'erreurs robuste** : Le widget continue de fonctionner même en cas d'erreur

### 3. Couleurs de Fond Subtiles

Les lignes du tableau ont maintenant des couleurs de fond subtiles selon le statut :
- Vert très clair pour les articles disponibles
- Jaune très clair pour les articles en stock bas
- Rouge très clair pour les articles en rupture
- Gris très clair pour les articles arrêtés

## Architecture Technique

### Modèle de Table (`inventory_table_model.py`)
- Utilise les rôles Qt pour afficher les couleurs de fond
- Gestion des badges de statut avec couleurs et icônes
- Support du tri et du filtrage

### Widget d'Alerte (`stock_alert_widget.py`)
- Widget personnalisé avec scroll area
- Cartes d'alerte interactives
- Mise à jour asynchrone des données
- Gestion robuste des erreurs

### Délégués Personnalisés (`status_badge_widget.py`)
- `StatusBadgeDelegate` : Affiche les badges de statut dans les cellules
- Support des couleurs et icônes personnalisées
- Gestion des événements de clic

## Utilisation

### Dans l'Interface
1. **Vue d'inventaire** : Les badges de statut sont visibles dans la colonne "Statut"
2. **Widget d'alerte** : Situé à droite de l'interface, affiche les alertes en temps réel
3. **Couleurs de fond** : Les lignes du tableau sont colorées selon le statut

### Actions Disponibles
- **Clic sur un badge** : Affiche les détails de l'article
- **Clic sur une alerte** : Ouvre le dialogue de modification
- **Bouton "Commander"** : Lance le processus de commande

## Configuration

### Couleurs Personnalisées
Les couleurs peuvent être modifiées dans `inventory_table_model.py` :

```python
self.status_colors = {
    'available': QColor(240, 255, 240),  # Vert très clair
    'low_stock': QColor(255, 255, 224),  # Jaune très clair
    'out_of_stock': QColor(255, 240, 240),  # Rouge très clair
    'discontinued': QColor(248, 248, 248),  # Gris très clair
}
```

### Icônes de Statut
Les icônes sont définies dans `status_badge_widget.py` :

```python
self.status_icons = {
    'available': '✓',
    'low_stock': '⚠',
    'out_of_stock': '✗',
    'discontinued': '⊘',
}
```

## Maintenance

### Ajout de Nouveaux Statuts
1. Ajouter le statut dans l'enum `ItemStatus`
2. Définir la couleur et l'icône dans les dictionnaires
3. Mettre à jour la logique de détermination du statut

### Personnalisation du Widget d'Alerte
1. Modifier `AlertCard` pour ajouter de nouvelles informations
2. Ajuster les styles CSS dans `setup_ui()`
3. Ajouter de nouvelles actions dans les cartes

## Dépannage

### Widget d'Alerte Ne S'Affiche Pas
1. Vérifier que la base de données contient des articles en alerte
2. Contrôler les logs pour détecter les erreurs
3. Vérifier que le service d'inventaire fonctionne correctement

### Badges Ne S'Affichent Pas
1. Vérifier que les délégués sont correctement assignés
2. Contrôler que les données de statut sont présentes
3. Vérifier les styles CSS

## Performance

- **Optimisation des requêtes** : Utilisation de requêtes optimisées pour les alertes
- **Mise à jour asynchrone** : Les alertes se mettent à jour sans bloquer l'interface
- **Gestion de la mémoire** : Nettoyage automatique des widgets non utilisés 