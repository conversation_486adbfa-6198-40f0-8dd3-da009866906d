# Guide de Compilation avec Nuitka

Ce document explique comment compiler l'application de gestion avec Nuitka pour créer un exécutable autonome.

## Prérequis

- Python 3.10 ou supérieur
- Nuitka (installé via `pip install nuitka`)
- Toutes les dépendances du projet installées (voir `requirements.txt`)

## Structure de l'Application

L'application est structurée comme suit :

- `main.py` : Point d'entrée de l'application
- `app/` : Contient les modules principaux de l'application
  - `app_manager.py` : Gère le flux de l'application
  - `controllers/` : Contrôleurs pour la logique métier
  - `core/` : Modèles et services principaux
  - `ui/` : Interface utilisateur PyQt6
  - `utils/` : Utilitaires divers
- `config/` : Fichiers de configuration
- `data/` : Stockage des données (base de données SQLite)
- `backups/` : Sauvegardes de la base de données
- `output/` : Fichiers générés par l'application

## Compilation avec Nuitka

Un script de compilation `build_with_nuitka.py` a été créé pour faciliter le processus. Ce script :

1. Compile l'application avec Nuitka en mode standalone
2. Inclut tous les packages nécessaires
3. Copie les ressources et fichiers de configuration requis
4. Crée les répertoires nécessaires dans le dossier de distribution

### Utilisation du Script de Compilation

Pour compiler l'application, exécutez simplement :

```bash
python build_with_nuitka.py
```

Le processus de compilation peut prendre plusieurs minutes. Une fois terminé, l'exécutable et tous les fichiers nécessaires seront disponibles dans le dossier `dist/main.dist/`.

### Options de Compilation

Le script utilise les options Nuitka suivantes :

- `--standalone` : Crée une application autonome
- `--follow-imports` : Suit tous les imports Python
- `--include-package=app,config,sqlalchemy,PyQt6,apscheduler` : Inclut les packages nécessaires
- `--include-data-dir=app/ui/resources=app/ui/resources` : Inclut les ressources UI
- `--windows-disable-console` : Désactive la console sous Windows
- `--windows-icon-from-ico=app/ui/resources/icon.ico` : Définit l'icône de l'application

## Structure du Dossier de Distribution

Après la compilation, le dossier de distribution contiendra :

- `main.exe` : L'exécutable principal
- `config/` : Fichiers de configuration
- `data/` : Dossier pour la base de données
- `backups/` : Dossier pour les sauvegardes
- `output/` : Dossier pour les fichiers générés
- Autres fichiers et dossiers nécessaires au fonctionnement de l'application

## Résolution des Problèmes Courants

### Problèmes de Dépendances

Si certaines dépendances ne sont pas correctement incluses, vous pouvez les ajouter manuellement avec l'option `--include-package` dans le script de compilation.

### Problèmes de Ressources

Si certaines ressources ne sont pas disponibles dans l'application compilée, vérifiez qu'elles sont correctement incluses avec l'option `--include-data-dir` ou `--include-data-files`.

### Problèmes de Base de Données

Assurez-vous que l'application compilée a les droits d'écriture dans le dossier où elle est exécutée pour pouvoir créer et modifier la base de données.

## Notes Importantes

- L'application compilée est autonome et ne nécessite pas d'installation de Python
- Tous les fichiers nécessaires sont inclus dans le dossier de distribution
- La base de données sera créée automatiquement au premier lancement si elle n'existe pas
- Les chemins relatifs dans l'application sont adaptés pour fonctionner avec l'exécutable compilé