# Guide de débogage pour l'exécutable compilé

Ce guide vous aidera à diagnostiquer et résoudre les problèmes liés à l'exécutable compilé avec Nuitka qui ne s'exécute pas correctement.

## Scripts de débogage disponibles

Plusieurs scripts ont été créés pour vous aider à diagnostiquer les problèmes :

1. **debug_executable.py** - Outil de diagnostic général qui vérifie l'environnement d'exécution
2. **main_debug.py** - Version améliorée du script principal avec journalisation détaillée
3. **build_with_nuitka_debug.py** - Script de compilation amélioré avec options optimisées pour PyQt6
4. **check_missing_dlls.py** - Outil pour vérifier les DLL manquantes dans l'exécutable compilé
5. **test_executable.py** - Outil complet pour tester l'exécutable et générer un rapport de diagnostic

## Procédure de débogage recommandée

### 1. Tester l'exécutable existant

Exécutez le script de test pour diagnostiquer les problèmes avec l'exécutable actuel :

```
python test_executable.py
```

Ce script va :
- Vérifier l'environnement système
- Tester l'exécutable en mode test et debug
- Vérifier les dépendances DLL manquantes
- Générer un rapport de diagnostic détaillé

### 2. Vérifier les DLL manquantes

Si le test indique des DLL manquantes, exécutez :

```
python check_missing_dlls.py
```

Ce script identifiera précisément quelles DLL sont manquantes et vous donnera des suggestions pour les installer.

### 3. Recompiler l'application avec le script amélioré

Utilisez le script de compilation amélioré pour reconstruire l'exécutable avec de meilleures options :

```
python build_with_nuitka_debug.py
```

Ce script :
- Vérifie que toutes les dépendances sont installées
- Utilise des options de compilation optimisées pour PyQt6
- Copie correctement toutes les ressources nécessaires
- Teste l'exécutable après la compilation

### 4. Exécuter le diagnostic détaillé

Si l'exécutable ne fonctionne toujours pas, exécutez :

```
python debug_executable.py
```

Ce script effectue un diagnostic approfondi de l'environnement d'exécution et génère un fichier de log détaillé.

## Problèmes courants et solutions

### DLL manquantes

- Installez les Visual C++ Redistributable les plus récents
- Assurez-vous que PyQt6 est correctement installé
- Utilisez l'option `--enable-plugin=pyqt6` lors de la compilation avec Nuitka

### Fichiers de configuration manquants

- Vérifiez que le fichier `config/settings.toml` existe et est correctement formaté
- Assurez-vous que les répertoires `data`, `backups` et `output` existent

### Problèmes d'initialisation de PyQt

- Vérifiez que toutes les ressources UI sont correctement copiées
- Assurez-vous que les plugins Qt sont présents dans le répertoire de l'exécutable

### Erreurs de base de données

- Vérifiez que le fichier de base de données existe dans le répertoire `data`
- Assurez-vous que les migrations sont correctement exécutées

## Utilisation du script main_debug.py

Si vous souhaitez recompiler l'application avec une meilleure gestion des erreurs, utilisez `main_debug.py` comme point d'entrée :

```
python build_with_nuitka_debug.py
```

Ce script inclut :
- Une journalisation détaillée
- Une meilleure gestion des exceptions
- Des vérifications d'environnement supplémentaires
- Des messages d'erreur plus explicites

## Logs et rapports

Tous les scripts de débogage génèrent des fichiers de log détaillés :

- `debug_executable.log` - Log du diagnostic général
- `build_debug.log` - Log de la compilation améliorée
- `missing_dlls.log` - Log de la vérification des DLL
- `logs/test_executable_*.log` - Log du test de l'exécutable
- `logs/rapport_diagnostic_*.txt` - Rapport de diagnostic complet

Consultez ces fichiers pour obtenir des informations détaillées sur les problèmes détectés.