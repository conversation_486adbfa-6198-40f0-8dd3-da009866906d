from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime

from ...core.schemas.inventory import (  # Using relative import
    InventoryItem, InventoryItemCreate, InventoryItemUpdate,
    InventoryMovement, InventoryMovementCreate
)
from app.core.services.inventory_service import InventoryService  # Ajout du préfixe app
from app.utils.database import get_db  # Using absolute import

router = APIRouter(prefix="/inventory", tags=["inventory"])

@router.post("/items/", response_model=InventoryItem)
async def create_inventory_item(
    item: InventoryItemCreate,
    db: Session = Depends(get_db)
):
    service = InventoryService(db)
    return await service.create_item(item.dict())

@router.get("/items/", response_model=List[InventoryItem])
async def list_inventory_items(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    service = InventoryService(db)
    return await service.get_items(skip=skip, limit=limit)

@router.get("/items/{item_id}", response_model=InventoryItem)
async def get_inventory_item(
    item_id: int,
    db: Session = Depends(get_db)
):
    service = InventoryService(db)
    item = await service.get_item(item_id)
    if not item:
        raise HTTPException(status_code=404, detail="Item not found")
    return item

@router.put("/items/{item_id}", response_model=InventoryItem)
async def update_inventory_item(
    item_id: int,
    item: InventoryItemUpdate,
    db: Session = Depends(get_db)
):
    service = InventoryService(db)
    try:
        return await service.update_item(item_id, item.dict(exclude_unset=True))
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.post("/movements/", response_model=InventoryMovement)
async def record_inventory_movement(
    movement: InventoryMovementCreate,
    db: Session = Depends(get_db)
):
    service = InventoryService(db)
    try:
        return await service.record_movement(movement.dict())
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/low-stock/", response_model=List[InventoryItem])
async def get_low_stock_items(db: Session = Depends(get_db)):
    service = InventoryService(db)
    return await service.get_low_stock_items()

@router.get("/items/{item_id}/movements/", response_model=List[InventoryMovement])
async def get_item_movements(
    item_id: int,
    start_date: datetime = None,
    end_date: datetime = None,
    db: Session = Depends(get_db)
):
    service = InventoryService(db)
    return await service.get_item_movements(item_id, start_date, end_date)






