from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.routes import inventory, equipment, notifications, reporting, auth_api, repairs, purchasing


def init_api():
    """Initialise l'API FastAPI avec tous les routers et middlewares nécessaires"""

    app = FastAPI(
        title="Logiciel de Gestion API",
        description="API pour la gestion d'inventaire, maintenance et achats",
        version="1.0.0"
    )

    # Configuration CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # En production, spécifiez les origines exactes
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Inclusion des routers
    app.include_router(inventory.router, prefix="/api")
    app.include_router(equipment.router, prefix="/api")
    app.include_router(notifications.router, prefix="/api")
    app.include_router(reporting.router, prefix="/api")
    app.include_router(auth_api.router, prefix="/api/auth")
    app.include_router(repairs.router, prefix="/api/repairs")
    app.include_router(purchasing.router, prefix="/api/purchasing")

    @app.get("/api")
    async def root():
        return {
            "message": "Bienvenue sur l'API du Logiciel de Gestion",
            "version": "1.0.0"
        }

    return app
