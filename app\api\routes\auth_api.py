from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import OAuth2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.core.services.auth_service import AuthService
from app.core.models.user import UserPydantic as User
from app.core.dependencies import get_current_user, get_db

router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Dependency provider for AuthService
async def get_auth_service(db: Session = Depends(get_db)) -> AuthService:
    return AuthService(db)

@router.post("/login")
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    totp_code: str | None = None,
    auth_service: AuthService = Depends(get_auth_service)
):
    success, result, _info = await auth_service.authenticate_user(
        form_data.username,
        form_data.password,
        totp_code
    )
    if not success:
        detail = result if isinstance(result, str) else "Invalid credentials"
        raise HTTPException(status_code=401, detail=detail)
    return {"access_token": result, "token_type": "bearer"}

@router.post("/2fa/setup")
async def setup_two_factor(
    current_user: User = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    return await auth_service.setup_two_factor(current_user.id)

@router.post("/2fa/enable")
async def enable_two_factor(
    totp_code: str,
    current_user: User = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    ok = await auth_service.enable_two_factor(current_user.id, totp_code)
    if not ok:
        raise HTTPException(status_code=400, detail="Invalid 2FA code")
    return {"status": "enabled"}