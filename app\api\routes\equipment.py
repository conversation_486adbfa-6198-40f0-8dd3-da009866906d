from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.models.equipment import EquipmentPydantic, EquipmentStatus
from app.core.services.equipment_service import EquipmentService
from app.core.dependencies import get_db, get_current_user
from app.core.models.user import UserPydantic

router = APIRouter(
    prefix="/equipment",
    tags=["equipment"],
    responses={404: {"description": "Not found"}}
)

@router.get("/", response_model=List[EquipmentPydantic])
async def get_equipment(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """Récupère la liste des équipements"""
    service = EquipmentService(db)
    
    if status:
        try:
            equipment_status = EquipmentStatus(status)
            return await service.get_equipment_by_status(equipment_status)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid status value")
    
    return await service.get_active_equipment(skip, limit)

@router.get("/{equipment_id}", response_model=EquipmentPydantic)
async def get_equipment_by_id(
    equipment_id: int,
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """Récupère un équipement par son ID"""
    service = EquipmentService(db)
    equipment = await service.get(equipment_id)
    
    if not equipment:
        raise HTTPException(status_code=404, detail="Equipment not found")
    
    return equipment

@router.post("/", response_model=EquipmentPydantic)
async def create_equipment(
    equipment: EquipmentPydantic,
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """Crée un nouvel équipement"""
    service = EquipmentService(db)
    return await service.create(equipment)

@router.put("/{equipment_id}", response_model=EquipmentPydantic)
async def update_equipment(
    equipment_id: int,
    equipment: EquipmentPydantic,
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """Met à jour un équipement existant"""
    service = EquipmentService(db)
    updated_equipment = await service.update(equipment_id, equipment)
    
    if not updated_equipment:
        raise HTTPException(status_code=404, detail="Equipment not found")
    
    return updated_equipment

@router.delete("/{equipment_id}", response_model=bool)
async def delete_equipment(
    equipment_id: int,
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """Désactive un équipement (suppression logique)"""
    service = EquipmentService(db)
    result = await service.deactivate_equipment(equipment_id)
    
    if not result:
        raise HTTPException(status_code=404, detail="Equipment not found")
    
    return result

@router.get("/search/", response_model=List[EquipmentPydantic])
async def search_equipment(
    query: str = Query(..., min_length=2),
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """Recherche des équipements"""
    service = EquipmentService(db)
    return await service.search_equipment(query, limit)

@router.get("/locations/", response_model=List[str])
async def get_equipment_locations(
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """Récupère la liste des emplacements d'équipements"""
    service = EquipmentService(db)
    return await service.get_equipment_locations()
