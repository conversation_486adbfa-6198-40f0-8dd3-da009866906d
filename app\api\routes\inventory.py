from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from app.core.dependencies import get_db, get_current_user
from app.core.services.inventory_service import InventoryService
from app.core.models.inventory import InventoryItemPydantic, InventoryMovementPydantic, ItemStatus, MovementType
from app.core.models.user import UserPydantic

router = APIRouter()

@router.get("/items/{item_id}", response_model=InventoryItemPydantic)
async def get_item(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    service = InventoryService(db)
    item = await service.get(item_id)
    if not item:
        raise HTTPException(status_code=404, detail="Item not found")
    return item

@router.post("/items/adjust-stock/{item_id}")
async def adjust_stock(
    item_id: int,
    quantity_change: int,
    reference: str,
    notes: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    service = InventoryService(db)
    success = await service.adjust_stock(
        item_id=item_id,
        quantity_change=quantity_change,
        user_id=current_user.id,
        reference=reference,
        notes=notes
    )
    if not success:
        raise HTTPException(status_code=400, detail="Stock adjustment failed")
    return {"message": "Stock adjusted successfully"}
