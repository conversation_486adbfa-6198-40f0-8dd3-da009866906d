from fastapi import APIRouter, Depends, HTTPException
from typing import List
from app.core.services.purchasing_service import PurchasingService
from app.core.models.purchasing import PurchaseOrder, SupplierQuote
from app.core.models.supplier import Supplier
from app.core.dependencies import get_current_user

router = APIRouter()

@router.post("/purchase-orders/", response_model=PurchaseOrder)
async def create_purchase_order(
    order_data: dict,
    current_user = Depends(get_current_user),
    purchasing_service: PurchasingService = Depends()
):
    try:
        order = await purchasing_service.create_purchase_order(
            supplier_id=order_data['supplier_id'],
            items=order_data['items']
        )
        return order
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/purchase-orders/{order_id}/send")
async def send_purchase_order(
    order_id: int,
    current_user = Depends(get_current_user),
    purchasing_service: PurchasingService = Depends()
):
    success = await purchasing_service.send_purchase_order(order_id)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to send purchase order")
    return {"status": "sent"}

@router.get("/suppliers/{supplier_id}/evaluate")
async def evaluate_supplier(
    supplier_id: int,
    evaluation_data: dict,
    current_user = Depends(get_current_user),
    purchasing_service: PurchasingService = Depends()
):
    supplier = await purchasing_service.evaluate_supplier(supplier_id, evaluation_data)
    return supplier

@router.get("/quotes/compare/{product_id}")
async def compare_quotes(
    product_id: int,
    current_user = Depends(get_current_user),
    purchasing_service: PurchasingService = Depends()
):
    quotes = await purchasing_service.compare_supplier_quotes(product_id)
    return quotes

@router.get("/reorder-check")
async def check_reorder_points(
    current_user = Depends(get_current_user),
    purchasing_service: PurchasingService = Depends()
):
    products = await purchasing_service.check_reorder_points()
    return products