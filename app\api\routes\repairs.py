from fastapi import APIRouter, Depends, HTTPException
from typing import List
from datetime import datetime
from app.core.services.repair_service import RepairService
from app.core.models.repair import RepairOrder
from app.core.dependencies import get_current_user

router = APIRouter()

@router.post("/repairs/", response_model=RepairOrder)
async def create_repair(
    repair_data: dict,
    current_user = Depends(get_current_user),
    repair_service: RepairService = Depends()
):
    try:
        repair = await repair_service.create_repair_order(repair_data)
        return repair
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/repairs/{repair_id}/diagnosis")
async def record_diagnosis(
    repair_id: int,
    diagnosis_data: dict,
    current_user = Depends(get_current_user),
    repair_service: RepairService = Depends()
):
    repair = await repair_service.record_diagnosis(repair_id, diagnosis_data)
    return repair

@router.post("/repairs/{repair_id}/start")
async def start_repair(
    repair_id: int,
    current_user = Depends(get_current_user),
    repair_service: RepairService = Depends()
):
    try:
        repair = await repair_service.start_repair(repair_id, current_user.id)
        return repair
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/repairs/{repair_id}/parts/use")
async def use_part(
    repair_id: int,
    part_data: dict,
    current_user = Depends(get_current_user),
    repair_service: RepairService = Depends()
):
    repair = await repair_service.use_part(repair_id, part_data)
    return repair

@router.post("/repairs/{repair_id}/complete")
async def complete_repair(
    repair_id: int,
    completion_data: dict,
    current_user = Depends(get_current_user),
    repair_service: RepairService = Depends()
):
    try:
        repair = await repair_service.complete_repair(repair_id, completion_data)
        return repair
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/technicians/{technician_id}/schedule")
async def get_schedule(
    technician_id: int,
    date: datetime,
    current_user = Depends(get_current_user),
    repair_service: RepairService = Depends()
):
    schedule = await repair_service.get_technician_schedule(technician_id, date)
    return schedule