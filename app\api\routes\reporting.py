from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from datetime import datetime, date

from app.core.services.reporting_service import ReportingService
from app.core.dependencies import get_db, get_current_user
from app.core.models.user import UserPydantic

router = APIRouter(
    prefix="/reporting",
    tags=["reporting"],
    responses={404: {"description": "Not found"}}
)

@router.get("/dashboard-kpis", response_model=Dict[str, Any])
async def get_dashboard_kpis(
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère les KPIs pour le tableau de bord
    """
    service = ReportingService(db)
    return await service.get_dashboard_kpis()

@router.get("/repair-trends", response_model=List[Dict[str, Any]])
async def get_repair_trends(
    months: int = Query(12, ge=1, le=60),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère les données de tendance des réparations
    
    Args:
        months: Nombre de mois à inclure dans les données de tendance
    """
    service = ReportingService(db)
    return await service.get_repair_trend_data(months)

@router.get("/inventory-trends", response_model=List[Dict[str, Any]])
async def get_inventory_trends(
    months: int = Query(12, ge=1, le=60),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère les données de tendance des mouvements d'inventaire
    
    Args:
        months: Nombre de mois à inclure dans les données de tendance
    """
    service = ReportingService(db)
    return await service.get_inventory_trend_data(months)

@router.get("/top-equipment", response_model=List[Dict[str, Any]])
async def get_top_equipment(
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère les équipements les plus réparés
    
    Args:
        limit: Nombre maximum d'équipements à récupérer
    """
    service = ReportingService(db)
    return await service.get_top_repaired_equipment(limit)

@router.get("/top-customers", response_model=List[Dict[str, Any]])
async def get_top_customers(
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère les clients avec le plus de réparations
    
    Args:
        limit: Nombre maximum de clients à récupérer
    """
    service = ReportingService(db)
    return await service.get_top_customers(limit)

@router.get("/repair-status-distribution", response_model=Dict[str, int])
async def get_repair_status_distribution(
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère la distribution des statuts de réparation
    """
    service = ReportingService(db)
    return await service.get_repair_status_distribution()

@router.get("/monthly-report", response_model=Dict[str, Any])
async def get_monthly_report(
    year: int = Query(..., ge=2000, le=2100),
    month: int = Query(..., ge=1, le=12),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Génère un rapport mensuel
    
    Args:
        year: Année du rapport
        month: Mois du rapport (1-12)
    """
    service = ReportingService(db)
    return await service.generate_monthly_report(year, month)

@router.get("/quarterly-report", response_model=Dict[str, Any])
async def get_quarterly_report(
    year: int = Query(..., ge=2000, le=2100),
    quarter: int = Query(..., ge=1, le=4),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Génère un rapport trimestriel
    
    Args:
        year: Année du rapport
        quarter: Trimestre du rapport (1-4)
    """
    service = ReportingService(db)
    return await service.generate_quarterly_report(year, quarter)

@router.get("/annual-report", response_model=Dict[str, Any])
async def get_annual_report(
    year: int = Query(..., ge=2000, le=2100),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Génère un rapport annuel
    
    Args:
        year: Année du rapport
    """
    service = ReportingService(db)
    return await service.generate_annual_report(year)

@router.get("/repair-statistics", response_model=Dict[str, Any])
async def get_repair_statistics(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère les statistiques de réparation pour une période donnée
    
    Args:
        start_date: Date de début de la période
        end_date: Date de fin de la période
    """
    # Convertir les dates en datetime
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())
    
    service = ReportingService(db)
    return await service.get_repair_statistics(start_datetime, end_datetime)

@router.get("/maintenance-compliance", response_model=Dict[str, Any])
async def get_maintenance_compliance(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère les statistiques de conformité de maintenance pour une période donnée
    
    Args:
        start_date: Date de début de la période
        end_date: Date de fin de la période
    """
    # Convertir les dates en datetime
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())
    
    service = ReportingService(db)
    return await service.get_maintenance_compliance(start_datetime, end_datetime)

@router.get("/technician-performance", response_model=List[Dict[str, Any]])
async def get_technician_performance(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère les performances des techniciens pour une période donnée
    
    Args:
        start_date: Date de début de la période
        end_date: Date de fin de la période
    """
    # Convertir les dates en datetime
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())
    
    service = ReportingService(db)
    return await service.get_technician_performance(start_datetime, end_datetime)

@router.get("/purchasing-analytics", response_model=Dict[str, Any])
async def get_purchasing_analytics(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère les statistiques d'achat pour une période donnée
    
    Args:
        start_date: Date de début de la période
        end_date: Date de fin de la période
    """
    # Convertir les dates en datetime
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())
    
    service = ReportingService(db)
    return await service.get_purchasing_analytics(start_datetime, end_datetime)

@router.get("/equipment-reliability", response_model=List[Dict[str, Any]])
async def get_equipment_reliability(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db),
    current_user: UserPydantic = Depends(get_current_user)
):
    """
    Récupère les statistiques de fiabilité des équipements pour une période donnée
    
    Args:
        start_date: Date de début de la période
        end_date: Date de fin de la période
    """
    # Convertir les dates en datetime
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())
    
    service = ReportingService(db)
    return await service.get_equipment_reliability(start_datetime, end_datetime)
