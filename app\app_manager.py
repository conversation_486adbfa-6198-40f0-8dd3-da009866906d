"""
Gestionnaire d'application pour contrôler le flux entre les fenêtres
"""
from PyQt6.QtCore import QObject, pyqtSlot
from PyQt6.QtWidgets import QApplication
from app.ui.window import MainWindow
from app.ui.views.auth.auth_window import AuthWindow
from app.controllers.auth_controller import AuthController

class AppManager(QObject):
    """
    Classe qui gère le flux de l'application entre les différentes fenêtres
    et maintient l'état global de l'application.
    """

    def __init__(self, app: QApplication):
        super().__init__()
        self.app = app
        self.auth_window = None
        self.main_window = None
        self.auth_controller = AuthController()

        # Connecter les signaux du contrôleur d'authentification
        self.auth_controller.loginSucceeded.connect(self.on_login_success)
        self.auth_controller.logoutSucceeded.connect(self.on_logout_success)

    def start(self):
        """
        Démarre l'application en affichant la fenêtre d'authentification
        """
        self.show_auth_window()

    def show_auth_window(self):
        """
        Affiche la fenêtre d'authentification
        """
        # Fermer la fenêtre principale si elle existe
        if self.main_window:
            self.main_window.hide()

        # Créer et afficher la fenêtre d'authentification
        if not self.auth_window:
            self.auth_window = AuthWindow()
            # Connecter le contrôleur d'authentification
            self.auth_window.auth_controller = self.auth_controller
            # Connecter les signaux
            self.auth_window.loginRequested.connect(self.handle_login_request)
        else:
            # Réinitialiser les champs de saisie
            self.auth_window.reset_fields()

        self.auth_window.show()

    @pyqtSlot(dict)
    def on_login_success(self, user_info):
        """
        Appelé lorsque l'authentification réussit
        """
        # Cacher la fenêtre d'authentification
        if self.auth_window:
            self.auth_window.hide()

        # Afficher la fenêtre principale
        self.show_main_window(user_info)

    def show_main_window(self, user_info):
        """
        Affiche la fenêtre principale avec les informations de l'utilisateur
        """
        # Si la fenêtre principale existe déjà, la mettre à jour
        if self.main_window:
            self.main_window.user_info = user_info
            self.main_window.auth_controller = self.auth_controller
            self.main_window.update_user_interface()
            self.main_window.show()
        else:
            # Sinon, créer une nouvelle fenêtre principale
            self.main_window = MainWindow(user_info=user_info)
            self.main_window.auth_controller = self.auth_controller

            # Connecter les signaux pour la déconnexion et le changement d'utilisateur
            self.main_window.logoutRequested.connect(self.handle_logout)
            self.main_window.switchUserRequested.connect(self.handle_switch_user)

            self.main_window.show()

    def handle_logout(self):
        """
        Gère la déconnexion de l'utilisateur
        """
        # Déconnecter l'utilisateur
        self.auth_controller.logout()

    def handle_switch_user(self):
        """
        Gère le changement d'utilisateur
        """
        # Déconnecter l'utilisateur actuel
        self.auth_controller.logout()

        # Afficher la fenêtre d'authentification
        self.show_auth_window()

    def handle_login_request(self, email: str, password: str, remember_me: bool):
        """
        Gère la demande de connexion
        """
        # Utiliser QTimer pour exécuter la méthode dans un thread séparé
        from PyQt6.QtCore import QTimer
        from functools import partial
        QTimer.singleShot(0, partial(self._direct_login, email, password, remember_me))

    def _direct_login(self, email: str, password: str, remember_me: bool):
        """
        Méthode interne pour exécuter la connexion directement
        """
        try:
            # Authentification directe avec SQLite
            import sqlite3
            import secrets

            db_path = "data/app.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Récupérer l'utilisateur
            cursor.execute("""
                SELECT id, email, hashed_password, full_name, status, is_active
                FROM users
                WHERE email = ?
            """, (email,))

            user = cursor.fetchone()

            if not user:
                self.auth_window.login_view.show_error(f"Utilisateur avec l'email {email} non trouvé.")
                conn.close()
                return

            user_id, user_email, hashed_password, full_name, status, is_active = user

            # Vérifier le mot de passe
            from passlib.context import CryptContext
            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
            is_password_valid = pwd_context.verify(password, hashed_password)

            if not is_password_valid:
                self.auth_window.login_view.show_error("Mot de passe incorrect.")
                conn.close()
                return

            if not is_active:
                self.auth_window.login_view.show_error("Ce compte n'est pas actif.")
                conn.close()
                return

            if status != "active":
                self.auth_window.login_view.show_error(f"Ce compte est {status}.")
                conn.close()
                return

            # Récupérer les rôles de l'utilisateur
            cursor.execute("""
                SELECT r.name
                FROM roles r
                JOIN user_roles ur ON r.id = ur.role_id
                WHERE ur.user_id = ?
            """, (user_id,))

            roles = [role[0] for role in cursor.fetchall()]

            # Créer les informations de l'utilisateur
            user_info = {
                "id": user_id,
                "email": user_email,
                "full_name": full_name,
                "permissions": roles
            }

            # Simuler un token
            token = secrets.token_hex(32)

            # Mettre à jour le contrôleur d'authentification
            self.auth_controller.current_user = user_info
            self.auth_controller.access_token = token

            # Émettre le signal de connexion réussie
            self.auth_controller.loginSucceeded.emit(user_info)

            conn.close()
        except Exception as e:
            self.auth_window.login_view.show_error(f"Erreur lors de la connexion: {str(e)}")

    @pyqtSlot()
    def on_logout_success(self):
        """
        Appelé lorsque la déconnexion réussit
        """
        # Cacher la fenêtre principale
        if self.main_window:
            self.main_window.hide()

        # Afficher la fenêtre d'authentification
        self.show_auth_window()
