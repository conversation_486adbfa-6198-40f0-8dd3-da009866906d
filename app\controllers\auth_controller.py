from typing import Optional
from datetime import datetime
from PyQt6.QtCore import QObject, pyqtSignal
from app.core.services.auth_service import AuthService
from app.core.services.user_service import UserService
from app.utils.database import get_db
from app.utils.security import TokenBlacklist

class AuthController(QObject):
    loginSucceeded = pyqtSignal(dict)  # Émet les informations de l'utilisateur
    loginFailed = pyqtSignal(str)      # Émet le message d'erreur
    logoutSucceeded = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.current_user = None
        self.access_token = None

    async def login(self, email: str, password: str, remember_me: bool = False):
        try:
            # Utiliser le gestionnaire de contexte de manière synchrone
            with get_db() as db:
                auth_service = AuthService(db)
                success, result, user_info = await auth_service.authenticate_user(
                    email=email,
                    password=password,
                    ip_address="127.0.0.1",  # À remplacer par l'IP réelle
                    user_agent="Desktop App"
                )

                if success:
                    self.current_user = user_info
                    self.access_token = result
                    print("[DEBUG] Utilisateur connecté:", self.current_user)
                    print("[DEBUG] roles:", self.current_user.get("roles"))
                    print("[DEBUG] permissions:", self.current_user.get("permissions"))
                    self.loginSucceeded.emit(user_info)
                else:
                    # Gérer le cas où result est None
                    error_message = "Identifiants invalides"
                    if result is not None and isinstance(result, dict):
                        error_message = result.get("error", "Erreur d'authentification")
                    elif user_info is not None and isinstance(user_info, dict):
                        error_message = user_info.get("error", "Erreur d'authentification")

                    self.loginFailed.emit(error_message)

        except Exception as e:
            self.loginFailed.emit(str(e))

    def logout(self):
        if self.access_token:
            TokenBlacklist.add(self.access_token)
            self.current_user = None
            self.access_token = None
            self.logoutSucceeded.emit()

    @property
    def is_authenticated(self) -> bool:
        return self.current_user is not None and self.access_token is not None

    @property
    def user_roles(self) -> list:
        return self.current_user.get("permissions", []) if self.current_user else []

    def has_permission(self, permission_code):
        """Vérifie si l'utilisateur actuel a une permission spécifique"""
        if not self.current_user:
            return False

        # Admin a toutes les permissions
        if self.current_user.get('is_admin') or self.current_user.get('is_superuser'):
            return True

        # Récupérer et normaliser les permissions de l'utilisateur
        user_permissions = self.current_user.get('permissions', [])

        # Gérer les différents formats de permissions
        if isinstance(user_permissions, str):
            # Essayer de parser comme JSON
            try:
                import json
                parsed = json.loads(user_permissions)
                if isinstance(parsed, list):
                    user_permissions = [str(p) for p in parsed]
                else:
                    user_permissions = [str(parsed)]
            except (json.JSONDecodeError, ValueError):
                # Fallback: séparer par virgule
                user_permissions = [p.strip() for p in user_permissions.split(',') if p.strip()]
        elif not isinstance(user_permissions, list):
            user_permissions = []

        # Vérifier les rôles pour les permissions d'administration
        roles = self.current_user.get('roles', [])
        if isinstance(roles, str):
            try:
                import json
                parsed_roles = json.loads(roles)
                if isinstance(parsed_roles, list):
                    roles = [str(r) for r in parsed_roles]
                else:
                    roles = [str(parsed_roles)]
            except (json.JSONDecodeError, ValueError):
                roles = [r.strip() for r in roles.split(',') if r.strip()]
        elif not isinstance(roles, list):
            roles = []

        # Vérifier si l'utilisateur a un rôle admin
        admin_roles = ['admin', 'administrator', 'administrateur', 'superadmin']
        if any(role.lower() in admin_roles for role in roles):
            return True

        # Vérifier si l'utilisateur a des permissions d'administration
        admin_permissions = ['admin', 'user.create', 'user.delete', 'user.manage_roles', 'system.settings']
        if any(perm in user_permissions for perm in admin_permissions):
            return True

        return permission_code in user_permissions

