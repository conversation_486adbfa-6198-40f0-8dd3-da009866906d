from typing import List, Optional, Dict, Any
from PyQt6.QtCore import QObject, pyqtSignal
from app.utils.database import get_db
from app.core.services.repair_service import RepairService
from app.core.services.finance_service import FinanceService
from app.core.services.treasury_service import TreasuryService
from app.core.models.repair import RepairPaymentPydantic, PaymentMethod
from app.core.models.treasury import CashRegister


class PaymentsController(QObject):
    paymentsLoaded = pyqtSignal(list)         # Liste[RepairPaymentPydantic as dict]
    paymentRecorded = pyqtSignal(dict)       # RepairOrder as dict minimal
    cashRegistersLoaded = pyqtSignal(list)   # Liste[{id, name, type}]
    errorOccurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()

    async def load_payments(self, repair_id: int):
        try:
            with get_db() as db:
                service = RepairService(db)
                payments: List[RepairPaymentPydantic] = await service.get_repair_payments(repair_id)
                # Convertir en dict pour signaux Qt (éviter objets Pydantic dans signaux)
                payload = [p.model_dump() for p in payments]
                self.paymentsLoaded.emit(payload)
        except Exception as e:
            self.errorOccurred.emit(str(e))

    async def load_cash_registers(self):
        try:
            with get_db() as db:
                treasury = TreasuryService(db)
                # Lister les caisses actives
                registers = (
                    db.query(CashRegister)
                    .filter(CashRegister.is_active == True)
                    .order_by(CashRegister.name.asc())
                    .all()
                )
                payload = [
                    {
                        "id": r.id,
                        "name": r.name,
                        "type": getattr(r.type, "value", str(r.type)),
                    }
                    for r in registers
                ]
                self.cashRegistersLoaded.emit(payload)
        except Exception as e:
            self.errorOccurred.emit(str(e))

    async def record_payment(self, repair_id: int, data: Dict[str, Any]):
        """
        data attend:
        - amount: float/Decimal
        - payment_method: PaymentMethod | str ("cash", "credit_card", "bank_transfer", "check", "credit")
        - reference_number?: str
        - notes?: str
        - processed_by?: int
        - payment_date?: datetime
        - cash_register_id?: int
        - send_receipt?: bool
        """
        try:
            with get_db() as db:
                finance_service = FinanceService(db)
                # Normaliser la méthode de paiement si string
                pm = data.get("payment_method")
                if isinstance(pm, str):
                    try:
                        payment_method = PaymentMethod(pm)
                    except Exception:
                        # fallback: espèces
                        payment_method = PaymentMethod.cash
                else:
                    payment_method = pm

                # Utiliser FinanceService pour créer le paiement avec cohérence trésorerie
                payment = await finance_service.pay_repair(
                    repair_id=repair_id,
                    amount=data.get("amount"),
                    method=payment_method,
                    processed_by=data.get("processed_by", 1),  # TODO: récupérer l'utilisateur connecté
                    reference_number=data.get("reference_number"),
                    cash_register_id=data.get("cash_register_id"),
                    credit_terms=data.get("credit_terms"),
                    payment_date=data.get("payment_date")
                )

                # Récupérer la réparation mise à jour pour l'UI
                repair_service = RepairService(db)
                repair = await repair_service.get(repair_id)

                # Émettre un résumé utile pour rafraîchir l'UI
                self.paymentRecorded.emit({
                    "repair_id": repair.id,
                    "total_paid": float(repair.total_paid or 0),
                    "payment_status": getattr(repair.payment_status, "value", repair.payment_status),
                    "status": getattr(repair.status, "value", repair.status),
                })
        except Exception as e:
            self.errorOccurred.emit(str(e))