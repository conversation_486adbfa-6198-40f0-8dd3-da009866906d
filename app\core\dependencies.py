from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.orm import Session
from jwt import decode, exceptions as JWTError
from datetime import datetime

from app.utils.database import get_db as database_session
from app.core.models.user import UserPydantic
from .services.user_service import UserService

# Configuration OAuth2
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token")

async def get_db():
    """Dépendance pour obtenir une session de base de données."""
    async with database_session() as db:
        yield db

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> UserPydantic:
    """Dépendance pour obtenir l'utilisateur actuellement authentifié."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Décodage du token JWT
        payload = decode(
            token,
            "your-secret-key",  # À remplacer par la clé secrète de config
            algorithms=["HS256"]
        )
        user_id: int = payload.get("sub")
        if user_id is None:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception

    # Récupération de l'utilisateur
    service = UserService(db)
    user = await service.get_user_by_id(user_id)
    if user is None:
        raise credentials_exception
        
    return user


