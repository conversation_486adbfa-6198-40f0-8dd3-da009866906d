"""
Package pour les modèles de données.
Ce fichier importe tous les modèles dans le bon ordre pour éviter les problèmes d'imports circulaires.
"""
# Importer d'abord les modèles de base
from .base import BaseDBModel, TimestampMixin, BaseModelTimestamp

# Importer ensuite les modèles qui ne dépendent pas d'autres modèles
from .user import User, UserPydantic, UserStatus
from .customer import Customer, CustomerPydantic
from .supplier import Supplier, SupplierPydantic
from .item_category import ItemCategory, ItemCategoryPydantic
from .inventory import InventoryItem, InventoryItemPydantic
from .equipment import Equipment, EquipmentPydantic

# Importer les modèles de réparation
from .repair import RepairStatus, RepairPriority, PaymentStatus, PaymentMethod
from .repair import RepairOrder, UsedPart, RepairPayment
from .repair import RepairOrderPydantic, UsedPartPydantic, RepairPaymentPydantic

# Importer les modèles qui dépendent des modèles de réparation
from .repair_photo import PhotoType, RepairPhoto, RepairPhotoPydantic
from .repair_note import NoteType, RepairNote, RepairNotePydantic
from .repair_status_history import RepairStatusHistory, RepairStatusHistoryPydantic

# Importer les autres modèles
from .treasury import CashRegister, CashTransaction, TransactionCategory, CashRegisterType
from .treasury import Expense, PaymentMethod

# Importer les modèles de clôture journalière
from .daily_closure import (
    DailyClosure, CashRegisterSnapshot, PeriodLock,
    ClosureValidation, ClosureAuditLog,
    ClosureStatus, ClosureType
)

# Exposer les classes importées
__all__ = [
    'BaseDBModel', 'TimestampMixin', 'BaseModelTimestamp',
    'User', 'UserPydantic', 'UserStatus',
    'Customer', 'CustomerPydantic',
    'Supplier', 'SupplierPydantic',
    'InventoryItem', 'InventoryItemPydantic', 'ItemCategory', 'ItemCategoryPydantic',
    'Equipment', 'EquipmentPydantic',
    'RepairStatus', 'RepairPriority', 'PaymentStatus', 'PaymentMethod',
    'RepairOrder', 'UsedPart', 'RepairPayment',
    'RepairOrderPydantic', 'UsedPartPydantic', 'RepairPaymentPydantic',
    'PhotoType', 'RepairPhoto', 'RepairPhotoPydantic',
    'NoteType', 'RepairNote', 'RepairNotePydantic',
    'RepairStatusHistory', 'RepairStatusHistoryPydantic',
    'CashRegister', 'CashTransaction', 'TransactionCategory', 'CashRegisterType',
    'Expense', 'PaymentMethod',
    'DailyClosure', 'CashRegisterSnapshot', 'PeriodLock',
    'ClosureValidation', 'ClosureAuditLog', 'ClosureStatus', 'ClosureType'
]
