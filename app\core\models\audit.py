from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, TYPE_CHECKING
from sqlalchemy import Column, Integer, String, DateTime, JSON, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field

from .base import BaseDBModel, BaseModelTimestamp

if TYPE_CHECKING:
    from .user import User

class AuditActionType(str, Enum):
    """Types d'actions pour le journal d'audit"""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    LOGIN = "login"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    PASSWORD_RESET = "password_reset"
    PERMISSION_CHANGE = "permission_change"
    EXPORT = "export"
    IMPORT = "import"

    # Actions spécifiques aux commandes
    ORDER_SUBMIT = "order_submit"
    ORDER_APPROVE = "order_approve"
    ORDER_CANCEL = "order_cancel"
    ORDER_RECEIVE = "order_receive"

    OTHER = "other"

class AuditLog(BaseDBModel):
    """Modèle pour le journal d'audit"""
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(), index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    action = Column(SQLEnum(AuditActionType), nullable=False)
    entity_type = Column(String, nullable=True)
    entity_id = Column(Integer, nullable=True)
    details = Column(JSON, nullable=True)
    ip_address = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)

    # Relations
    # Relation avec User définie sans back_populates pour éviter les références circulaires
    user = relationship("User", foreign_keys=[user_id])

# Modèles Pydantic
class AuditLogPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour le journal d'audit"""
    id: int
    timestamp: datetime
    user_id: Optional[int] = None
    action: AuditActionType
    entity_type: Optional[str] = None
    entity_id: Optional[int] = None
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

class AuditLogCreate(BaseModel):
    """Modèle Pydantic pour la création d'un journal d'audit"""
    user_id: Optional[int] = None
    action: AuditActionType
    entity_type: Optional[str] = None
    entity_id: Optional[int] = None
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
