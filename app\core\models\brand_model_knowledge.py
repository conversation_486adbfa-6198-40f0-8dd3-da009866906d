"""
Modèles pour la base de connaissances des marques et modèles
Système d'auto-complétion évolutif basé sur l'apprentissage
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean, Text, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import BaseDBModel, TimestampMixin
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel


class Brand(BaseDBModel, TimestampMixin):
    """
    Table des marques avec compteur d'utilisation pour l'apprentissage
    """
    __tablename__ = "brands"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)  # Nom de la marque
    normalized_name = Column(String, index=True)  # Nom normalisé pour la recherche
    usage_count = Column(Integer, default=1)  # Nombre d'utilisations
    last_used = Column(DateTime, default=func.now())  # Dernière utilisation
    is_verified = Column(Boolean, default=False)  # Marque vérifiée manuellement
    category = Column(String, nullable=True)  # Catégorie (smartphone, ordinateur, etc.)
    
    # Relations
    models = relationship("DeviceModel", back_populates="brand", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Brand(name='{self.name}', usage_count={self.usage_count})>"


class DeviceModel(BaseDBModel, TimestampMixin):
    """
    Table des modèles liés aux marques avec apprentissage
    """
    __tablename__ = "device_models"

    id = Column(Integer, primary_key=True, index=True)
    brand_id = Column(Integer, ForeignKey("brands.id"), nullable=False)
    name = Column(String, nullable=False)  # Nom du modèle
    normalized_name = Column(String, index=True)  # Nom normalisé pour la recherche
    full_name = Column(String)  # Nom complet (marque + modèle)
    usage_count = Column(Integer, default=1)  # Nombre d'utilisations
    last_used = Column(DateTime, default=func.now())  # Dernière utilisation
    is_verified = Column(Boolean, default=False)  # Modèle vérifié manuellement
    specifications = Column(Text, nullable=True)  # Spécifications techniques (JSON)
    
    # Relations
    brand = relationship("Brand", back_populates="models")
    
    # Index composé pour optimiser les recherches
    __table_args__ = (
        Index('idx_brand_model', 'brand_id', 'normalized_name'),
        Index('idx_model_usage', 'usage_count', 'last_used'),
    )
    
    def __repr__(self):
        return f"<DeviceModel(brand='{self.brand.name}', name='{self.name}', usage_count={self.usage_count})>"


class BrandModelSuggestion(BaseDBModel, TimestampMixin):
    """
    Table des suggestions d'auto-complétion avec scoring
    """
    __tablename__ = "brand_model_suggestions"

    id = Column(Integer, primary_key=True, index=True)
    brand_id = Column(Integer, ForeignKey("brands.id"), nullable=False)
    model_id = Column(Integer, ForeignKey("device_models.id"), nullable=False)
    suggestion_text = Column(String, index=True)  # Texte de suggestion
    search_pattern = Column(String, index=True)  # Pattern de recherche
    relevance_score = Column(Integer, default=0)  # Score de pertinence
    click_count = Column(Integer, default=0)  # Nombre de clics sur cette suggestion
    
    # Relations
    brand = relationship("Brand")
    model = relationship("DeviceModel")
    
    def __repr__(self):
        return f"<BrandModelSuggestion(text='{self.suggestion_text}', score={self.relevance_score})>"


# Modèles Pydantic pour l'API
class BrandCreate(BaseModel):
    name: str
    category: Optional[str] = None

class BrandResponse(BaseModel):
    id: int
    name: str
    normalized_name: str
    usage_count: int
    category: Optional[str] = None
    is_verified: bool
    
    class Config:
        from_attributes = True

class DeviceModelCreate(BaseModel):
    brand_id: int
    name: str
    specifications: Optional[str] = None

class DeviceModelResponse(BaseModel):
    id: int
    brand_id: int
    name: str
    normalized_name: str
    full_name: str
    usage_count: int
    is_verified: bool
    brand_name: Optional[str] = None
    
    class Config:
        from_attributes = True

class AutoCompleteSuggestion(BaseModel):
    """Suggestion d'auto-complétion"""
    text: str
    type: str  # 'brand' ou 'model'
    brand_id: Optional[int] = None
    model_id: Optional[int] = None
    score: int
    usage_count: int

class BrandModelPair(BaseModel):
    """Paire marque-modèle validée"""
    brand: str
    model: str
    brand_id: Optional[int] = None
    model_id: Optional[int] = None
    confidence: float  # Score de confiance (0.0 à 1.0)
