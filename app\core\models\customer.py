from typing import Optional, List
from sqlalchemy import Column, Integer, String, Boolean, Text, Float, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
from .base import BaseDBModel, TimestampMixin, BaseModelTimestamp

class Customer(BaseDBModel, TimestampMixin):
    """Modèle de données pour les clients"""
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    contact_person = Column(String, nullable=True)
    phone = Column(String)
    email = Column(String, nullable=True)
    address = Column(String, nullable=True)
    commune = Column(String, nullable=True)
    city = Column(String, nullable=True)
    postal_code = Column(String, nullable=True)
    notes = Column(Text, nullable=True)
    active = Column(Boolean, default=True)

    # Champs financiers
    credit_limit = Column(Float, default=0.0)  # Limite de crédit du client
    current_balance = Column(Float, default=0.0)  # Solde actuel du client
    default_payment_terms = Column(Integer, default=30)  # Conditions de paiement par défaut en jours

    # Relations
    repair_orders = relationship("RepairOrder", back_populates="customer", foreign_keys="[RepairOrder.customer_id]")
    transactions = relationship("CustomerTransaction", back_populates="customer", cascade="all, delete-orphan")

class CustomerTransaction(BaseDBModel, TimestampMixin):
    """Modèle pour les transactions financières des clients"""
    __tablename__ = "customer_transactions"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    repair_order_id = Column(Integer, ForeignKey("repair_orders.id"), nullable=True)
    sale_id = Column(Integer, ForeignKey("sales.id"), nullable=True)
    amount = Column(Float, nullable=False)  # Montant positif pour les paiements, négatif pour les dettes
    transaction_date = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    description = Column(Text, nullable=True)
    reference_number = Column(String, nullable=True)
    processed_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    transaction_type = Column(String, default="manual")  # manual, repair, sale, payment

    # Relations
    customer = relationship("Customer", back_populates="transactions")
    repair_order = relationship("RepairOrder", foreign_keys=[repair_order_id])
    sale = relationship("Sale", foreign_keys=[sale_id])
    processor = relationship("User", foreign_keys=[processed_by])

class CustomerPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les clients"""
    id: Optional[int] = None  # ID optionnel pour permettre la création sans ID
    name: str
    contact_person: Optional[str] = None
    phone: str
    email: Optional[str] = None
    address: Optional[str] = None
    commune: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    notes: Optional[str] = None
    active: bool = True
    credit_limit: float = 0.0
    current_balance: float = 0.0
    default_payment_terms: int = 30

class CustomerTransactionPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les transactions des clients"""
    id: Optional[int] = None
    customer_id: int
    repair_order_id: Optional[int] = None
    sale_id: Optional[int] = None
    amount: float
    transaction_date: datetime = datetime.now(timezone.utc)
    description: Optional[str] = None
    reference_number: Optional[str] = None
    processed_by: Optional[int] = None
    transaction_type: str = "manual"

    class Config:
        orm_mode = True
