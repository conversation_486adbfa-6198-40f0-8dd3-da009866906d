"""
Modèles pour la gestion des clôtures journalières de trésorerie.
"""
from datetime import datetime, date
from enum import Enum
from typing import Optional, List, Dict, Any
from decimal import Decimal
from sqlalchemy import (
    Column, Integer, String, DateTime, Date, ForeignKey, 
    Enum as SQLEnum, Boolean, Text, Numeric, UniqueConstraint,
    Index, CheckConstraint
)
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.core.models.base import BaseDBModel, TimestampMixin


class ClosureStatus(str, Enum):
    """Statuts de clôture"""
    PENDING = "pending"  # En attente
    IN_PROGRESS = "in_progress"  # En cours
    COMPLETED = "completed"  # Terminée
    FAILED = "failed"  # Échouée
    CANCELLED = "cancelled"  # Annulée


class ClosureType(str, Enum):
    """Types de clôture"""
    DAILY = "daily"  # Clôture journalière
    WEEKLY = "weekly"  # Clôture hebdomadaire
    MONTHLY = "monthly"  # Clôture mensuelle
    MANUAL = "manual"  # Clôture manuelle


class DailyClosure(BaseDBModel, TimestampMixin):
    """
    Modèle pour les clôtures journalières.
    Une clôture par jour avec snapshot des soldes de toutes les caisses.
    """
    __tablename__ = "daily_closures"

    id = Column(Integer, primary_key=True, index=True)
    closure_date = Column(Date, nullable=False, unique=True, index=True)
    closure_type = Column(SQLEnum(ClosureType), default=ClosureType.DAILY)
    status = Column(SQLEnum(ClosureStatus), default=ClosureStatus.PENDING, index=True)
    
    # Informations de clôture
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    started_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    completed_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Totaux calculés
    total_cash_registers = Column(Integer, default=0)
    total_balance = Column(Numeric(18, 2), default=Decimal("0.00"))
    total_transactions = Column(Integer, default=0)
    total_in = Column(Numeric(18, 2), default=Decimal("0.00"))
    total_out = Column(Numeric(18, 2), default=Decimal("0.00"))
    
    # Métadonnées
    notes = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    validation_hash = Column(String(64), nullable=True)  # Hash pour validation d'intégrité
    
    # Relations
    snapshots = relationship("CashRegisterSnapshot", back_populates="closure", cascade="all, delete-orphan")
    started_by = relationship("User", foreign_keys=[started_by_user_id])
    completed_by = relationship("User", foreign_keys=[completed_by_user_id])
    
    # Contraintes
    __table_args__ = (
        Index('idx_daily_closure_date_status', 'closure_date', 'status'),
        CheckConstraint('total_balance >= 0', name='check_positive_total_balance'),
        CheckConstraint('total_transactions >= 0', name='check_positive_total_transactions'),
    )
    
    def __repr__(self):
        return f"<DailyClosure(date={self.closure_date}, status={self.status})>"
    
    @property
    def is_locked(self) -> bool:
        """Vérifie si la clôture est verrouillée (terminée)"""
        return self.status == ClosureStatus.COMPLETED
    
    @property
    def duration_minutes(self) -> Optional[int]:
        """Durée de la clôture en minutes"""
        if self.started_at and self.completed_at:
            delta = self.completed_at - self.started_at
            return int(delta.total_seconds() / 60)
        return None


class CashRegisterSnapshot(BaseDBModel, TimestampMixin):
    """
    Snapshot des soldes d'une caisse à un moment donné.
    Créé lors des clôtures journalières.
    """
    __tablename__ = "cash_register_snapshots"

    id = Column(Integer, primary_key=True, index=True)
    closure_id = Column(Integer, ForeignKey("daily_closures.id"), nullable=False, index=True)
    cash_register_id = Column(Integer, ForeignKey("cash_registers.id"), nullable=False, index=True)
    
    # Soldes au moment de la clôture
    opening_balance = Column(Numeric(18, 2), nullable=False)  # Solde d'ouverture
    closing_balance = Column(Numeric(18, 2), nullable=False)  # Solde de clôture
    theoretical_balance = Column(Numeric(18, 2), nullable=False)  # Solde théorique calculé
    
    # Mouvements de la journée
    total_in = Column(Numeric(18, 2), default=Decimal("0.00"))  # Total des entrées
    total_out = Column(Numeric(18, 2), default=Decimal("0.00"))  # Total des sorties
    transaction_count = Column(Integer, default=0)  # Nombre de transactions
    
    # Détails par méthode de paiement
    cash_amount = Column(Numeric(18, 2), default=Decimal("0.00"))
    bank_transfer_amount = Column(Numeric(18, 2), default=Decimal("0.00"))
    check_amount = Column(Numeric(18, 2), default=Decimal("0.00"))
    credit_card_amount = Column(Numeric(18, 2), default=Decimal("0.00"))
    other_amount = Column(Numeric(18, 2), default=Decimal("0.00"))
    
    # Écarts et ajustements
    variance = Column(Numeric(18, 2), default=Decimal("0.00"))  # Écart constaté
    adjustment_amount = Column(Numeric(18, 2), default=Decimal("0.00"))  # Montant d'ajustement
    adjustment_reason = Column(Text, nullable=True)  # Raison de l'ajustement
    
    # Métadonnées
    notes = Column(Text, nullable=True)
    is_reconciled = Column(Boolean, default=False)  # Rapprochement effectué
    reconciled_at = Column(DateTime, nullable=True)
    reconciled_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relations
    closure = relationship("DailyClosure", back_populates="snapshots")
    cash_register = relationship("CashRegister")
    reconciled_by = relationship("User", foreign_keys=[reconciled_by_user_id])
    
    # Contraintes
    __table_args__ = (
        UniqueConstraint('closure_id', 'cash_register_id', name='uq_closure_register'),
        Index('idx_snapshot_closure_register', 'closure_id', 'cash_register_id'),
        Index('idx_snapshot_register_date', 'cash_register_id', 'created_at'),
        CheckConstraint('opening_balance >= 0', name='check_positive_opening_balance'),
        CheckConstraint('closing_balance >= 0', name='check_positive_closing_balance'),
        CheckConstraint('transaction_count >= 0', name='check_positive_transaction_count'),
    )
    
    def __repr__(self):
        return f"<CashRegisterSnapshot(closure_id={self.closure_id}, register_id={self.cash_register_id})>"
    
    @property
    def net_movement(self) -> Decimal:
        """Mouvement net de la journée"""
        return self.total_in - abs(self.total_out)
    
    @property
    def has_variance(self) -> bool:
        """Vérifie s'il y a un écart"""
        return abs(self.variance) > Decimal("0.01")
    
    @property
    def calculated_closing_balance(self) -> Decimal:
        """Solde de clôture calculé"""
        return self.opening_balance + self.net_movement


class PeriodLock(BaseDBModel, TimestampMixin):
    """
    Verrouillage des périodes pour empêcher les modifications.
    """
    __tablename__ = "period_locks"

    id = Column(Integer, primary_key=True, index=True)
    start_date = Column(Date, nullable=False, index=True)
    end_date = Column(Date, nullable=False, index=True)
    lock_type = Column(String(50), nullable=False)  # 'daily_closure', 'monthly_closure', etc.
    
    # Informations de verrouillage
    locked_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    locked_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    reason = Column(Text, nullable=True)
    
    # Contrôle d'accès
    is_active = Column(Boolean, default=True, index=True)
    can_unlock = Column(Boolean, default=False)  # Peut être déverrouillé
    unlock_requires_approval = Column(Boolean, default=True)  # Nécessite une approbation
    
    # Métadonnées
    reference_id = Column(Integer, nullable=True)  # ID de la clôture associée
    notes = Column(Text, nullable=True)
    
    # Relations
    locked_by = relationship("User")
    
    # Contraintes
    __table_args__ = (
        Index('idx_period_lock_dates', 'start_date', 'end_date'),
        Index('idx_period_lock_active', 'is_active', 'start_date', 'end_date'),
        CheckConstraint('start_date <= end_date', name='check_valid_date_range'),
    )
    
    def __repr__(self):
        return f"<PeriodLock(start={self.start_date}, end={self.end_date}, type={self.lock_type})>"
    
    def is_date_locked(self, check_date: date) -> bool:
        """Vérifie si une date est verrouillée"""
        if not self.is_active:
            return False
        return self.start_date <= check_date <= self.end_date
    
    def overlaps_with(self, start_date: date, end_date: date) -> bool:
        """Vérifie si la période chevauche avec une autre"""
        if not self.is_active:
            return False
        return not (end_date < self.start_date or start_date > self.end_date)


class ClosureValidation(BaseDBModel, TimestampMixin):
    """
    Validations effectuées lors des clôtures.
    """
    __tablename__ = "closure_validations"

    id = Column(Integer, primary_key=True, index=True)
    closure_id = Column(Integer, ForeignKey("daily_closures.id"), nullable=False, index=True)
    validation_type = Column(String(100), nullable=False)  # Type de validation
    
    # Résultat de la validation
    is_valid = Column(Boolean, nullable=False)
    error_message = Column(Text, nullable=True)
    warning_message = Column(Text, nullable=True)
    
    # Détails de la validation
    expected_value = Column(String(255), nullable=True)
    actual_value = Column(String(255), nullable=True)
    tolerance = Column(Numeric(18, 2), nullable=True)
    
    # Métadonnées
    validated_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    validated_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relations
    closure = relationship("DailyClosure")
    validated_by = relationship("User")
    
    # Contraintes
    __table_args__ = (
        Index('idx_validation_closure_type', 'closure_id', 'validation_type'),
        Index('idx_validation_type_valid', 'validation_type', 'is_valid'),
    )
    
    def __repr__(self):
        return f"<ClosureValidation(closure_id={self.closure_id}, type={self.validation_type}, valid={self.is_valid})>"


class ClosureAuditLog(BaseDBModel, TimestampMixin):
    """
    Journal d'audit pour les opérations de clôture.
    """
    __tablename__ = "closure_audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    closure_id = Column(Integer, ForeignKey("daily_closures.id"), nullable=True, index=True)
    action = Column(String(100), nullable=False, index=True)  # Action effectuée
    
    # Détails de l'action
    entity_type = Column(String(100), nullable=True)  # Type d'entité affectée
    entity_id = Column(Integer, nullable=True)  # ID de l'entité
    old_values = Column(Text, nullable=True)  # Anciennes valeurs (JSON)
    new_values = Column(Text, nullable=True)  # Nouvelles valeurs (JSON)
    
    # Informations utilisateur
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv4 ou IPv6
    user_agent = Column(Text, nullable=True)
    
    # Métadonnées
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow, index=True)
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    
    # Relations
    closure = relationship("DailyClosure")
    user = relationship("User")
    
    # Contraintes
    __table_args__ = (
        Index('idx_audit_closure_action', 'closure_id', 'action'),
        Index('idx_audit_timestamp', 'timestamp'),
        Index('idx_audit_user_action', 'user_id', 'action'),
    )
    
    def __repr__(self):
        return f"<ClosureAuditLog(action={self.action}, timestamp={self.timestamp})>"
