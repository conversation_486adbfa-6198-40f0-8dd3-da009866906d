from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any, TYPE_CHECKING
from sqlalchemy import <PERSON>umn, Integer, String, Float, Boolean, ForeignKey, Enum as SQLEnum, JSON, DateTime
from sqlalchemy.orm import relationship, Mapped, mapped_column
from pydantic import BaseModel, conint, confloat
from typing import Annotated
from app.core.models.base import BaseDBModel, TimestampMixin, BaseModelTimestamp
from app.core.models.user import User

if TYPE_CHECKING:
    from app.core.models.supplier import Supplier
    from app.core.models.item_category import ItemCategory

# Garder l'énumération pour la compatibilité avec le code existant
class LegacyItemCategory(str, Enum):
    PART = "part"
    TOOL = "tool"
    CONSUMABLE = "consumable"
    EQUIPMENT = "equipment"

class ItemStatus(str, Enum):
    AVAILABLE = "available"
    LOW_STOCK = "low_stock"
    OUT_OF_STOCK = "out_of_stock"
    DISCONTINUED = "discontinued"

class ItemCondition(str, Enum):
    NEW = "new"
    USED = "used"

class MovementType(str, Enum):
    IN = "in"
    OUT = "out"

# Modèles SQLAlchemy
class InventoryItem(BaseDBModel, TimestampMixin):
    __tablename__ = "inventory_items"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    sku: Mapped[str] = mapped_column(String, unique=True, index=True)
    name: Mapped[str] = mapped_column(String)
    description: Mapped[str] = mapped_column(String)
    # Garder la colonne category pour la compatibilité, mais ajouter une nouvelle colonne category_id
    category: Mapped[LegacyItemCategory] = mapped_column(SQLEnum(LegacyItemCategory), nullable=True)
    category_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("item_categories.id"), nullable=True)
    quantity: Mapped[int] = mapped_column(Integer, default=0)
    minimum_quantity: Mapped[int] = mapped_column(Integer, default=0)
    purchase_price: Mapped[float] = mapped_column(Float, default=0)  # Prix d'achat
    unit_price: Mapped[float] = mapped_column(Float)  # Prix de vente
    margin_percent: Mapped[float] = mapped_column(Float, default=30)  # Marge en pourcentage
    supplier_id: Mapped[int] = mapped_column(Integer, ForeignKey("suppliers.id"))
    location: Mapped[str] = mapped_column(String)
    status: Mapped[ItemStatus] = mapped_column(SQLEnum(ItemStatus))
    condition: Mapped[ItemCondition] = mapped_column(SQLEnum(ItemCondition), default=ItemCondition.NEW)  # État (neuf/occasion)
    specifications: Mapped[Dict[str, Any]] = mapped_column(JSON)
    barcode: Mapped[Optional[str]] = mapped_column(String, unique=True, nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    last_purchase_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)  # Date du dernier achat

    # Relations
    supplier: Mapped["Supplier"] = relationship("Supplier")
    movements: Mapped[List["InventoryMovement"]] = relationship("InventoryMovement", back_populates="item")
    category_relation: Mapped[Optional["ItemCategory"]] = relationship("ItemCategory", back_populates="items")

class InventoryMovement(BaseDBModel, TimestampMixin):
    __tablename__ = "inventory_movements"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    item_id: Mapped[int] = mapped_column(Integer, ForeignKey("inventory_items.id"))
    quantity: Mapped[int] = mapped_column(Integer)
    type: Mapped[str] = mapped_column(String)  # "in" ou "out"
    reference: Mapped[str] = mapped_column(String)  # Numéro de commande ou de réparation
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"))
    notes: Mapped[Optional[str]] = mapped_column(String, nullable=True)

    # Relations
    item: Mapped["InventoryItem"] = relationship("InventoryItem", back_populates="movements")
    user: Mapped["User"] = relationship("User")

# Modèles Pydantic
class InventoryItemPydantic(BaseModelTimestamp):
    id: Optional[int] = None
    sku: str
    name: str
    description: str
    category: Optional[LegacyItemCategory] = None
    category_id: Optional[int] = None
    quantity: Annotated[int, conint(ge=0)]
    minimum_quantity: Annotated[int, conint(ge=0)]
    purchase_price: Annotated[float, confloat(ge=0)] = 0
    unit_price: Annotated[float, confloat(gt=0)]
    margin_percent: Annotated[float, confloat(ge=0)] = 30
    supplier_id: int
    location: str
    status: ItemStatus
    condition: ItemCondition = ItemCondition.NEW
    specifications: Dict[str, Any]
    barcode: Optional[str] = None
    is_active: bool = True
    last_purchase_date: Optional[datetime] = None

    class Config:
        orm_mode = True

class InventoryMovementPydantic(BaseModelTimestamp):
    id: int
    item_id: int
    quantity: int
    type: str
    reference: str
    user_id: int
    notes: Optional[str]










