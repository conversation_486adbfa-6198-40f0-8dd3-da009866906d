"""
Modèle pour les catégories d'articles.
"""
from sqlalchemy import Column, Integer, String, Boolean, ForeignKey
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import List, Optional
from app.core.models.base import BaseDBModel, TimestampMixin, BaseModelTimestamp

class ItemCategory(BaseDBModel, TimestampMixin):
    """Modèle pour les catégories d'articles"""
    __tablename__ = "item_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    description = Column(String, nullable=True)
    code = Column(String, unique=True, index=True)
    parent_id = Column(Integer, ForeignKey("item_categories.id"), nullable=True)
    is_active = Column(Boolean, default=True)
    
    # Relations
    parent = relationship("ItemCategory", remote_side=[id], backref="subcategories")
    items = relationship("InventoryItem", back_populates="category_relation")

class ItemCategoryPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les catégories d'articles"""
    id: Optional[int] = None
    name: str
    description: Optional[str] = None
    code: str
    parent_id: Optional[int] = None
    is_active: bool = True
    
    class Config:
        orm_mode = True
