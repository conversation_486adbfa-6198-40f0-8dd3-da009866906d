from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, ForeignKey, Boolean, JSON, Enum as SQLEnum, Text
from sqlalchemy.orm import relationship
from pydantic import BaseModel
from .base import BaseDBModel, TimestampMixin, BaseModelTimestamp

class NotificationType(str, Enum):
    """Types de notifications"""
    SYSTEM = "system"           # Notifications système
    REPAIR = "repair"           # Notifications liées aux réparations
    INVENTORY = "inventory"     # Notifications liées à l'inventaire
    EQUIPMENT = "equipment"     # Notifications liées aux équipements
    SUPPLIER = "supplier"       # Notifications liées aux fournisseurs
    USER = "user"               # Notifications liées aux utilisateurs
    CUSTOMER = "customer"       # Notifications liées aux clients
    FINANCE = "finance"         # Notifications liées aux finances

class NotificationPriority(str, Enum):
    """Priorités des notifications"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class NotificationStatus(str, Enum):
    """Statuts des notifications"""
    UNREAD = "unread"
    READ = "read"
    ARCHIVED = "archived"

class NotificationChannel(str, Enum):
    """Canaux de notification"""
    UI = "ui"                   # Interface utilisateur
    EMAIL = "email"             # Email
    SMS = "sms"                 # SMS
    PUSH = "push"               # Notification push

class Notification(BaseDBModel, TimestampMixin):
    """Modèle de notification"""
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    type = Column(SQLEnum(NotificationType), nullable=False)
    priority = Column(SQLEnum(NotificationPriority), default=NotificationPriority.MEDIUM)
    title = Column(String, nullable=False)
    message = Column(Text, nullable=False)
    status = Column(SQLEnum(NotificationStatus), default=NotificationStatus.UNREAD)
    data = Column(JSON, nullable=True)  # Données supplémentaires au format JSON
    action_url = Column(String, nullable=True)  # URL d'action (optionnelle)
    icon = Column(String, nullable=True)  # Icône (optionnelle)
    expiry_date = Column(DateTime, nullable=True)  # Date d'expiration (optionnelle)

    # Relations
    user = relationship("User", backref="notifications")

class NotificationDelivery(BaseDBModel, TimestampMixin):
    """Modèle de livraison de notification"""
    __tablename__ = "notification_deliveries"

    id = Column(Integer, primary_key=True, index=True)
    notification_id = Column(Integer, ForeignKey("notifications.id"), nullable=False)
    channel = Column(SQLEnum(NotificationChannel), nullable=False)
    recipient = Column(String, nullable=False)  # Email, numéro de téléphone, ID utilisateur, etc.
    sent_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)
    success = Column(Boolean, default=False)
    error_message = Column(String, nullable=True)

    # Relations
    notification = relationship("Notification", backref="deliveries")

# Modèles Pydantic
class NotificationPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les notifications"""
    id: int
    user_id: Optional[int]
    type: NotificationType
    priority: NotificationPriority
    title: str
    message: str
    status: NotificationStatus
    data: Optional[Dict[str, Any]]
    action_url: Optional[str]
    icon: Optional[str]
    expiry_date: Optional[datetime]

class NotificationDeliveryPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les livraisons de notification"""
    id: int
    notification_id: int
    channel: NotificationChannel
    recipient: str
    sent_at: Optional[datetime]
    delivered_at: Optional[datetime]
    success: bool
    error_message: Optional[str]

class NotificationCreate(BaseModel):
    """Modèle pour la création de notification"""
    user_id: Optional[int] = None
    type: NotificationType
    priority: NotificationPriority = NotificationPriority.MEDIUM
    title: str
    message: str
    data: Optional[Dict[str, Any]] = None
    action_url: Optional[str] = None
    icon: Optional[str] = None
    expiry_date: Optional[datetime] = None
    channels: list[NotificationChannel] = [NotificationChannel.UI]
    recipients: Optional[list[str]] = None  # Liste des destinataires (emails, numéros, etc.)
