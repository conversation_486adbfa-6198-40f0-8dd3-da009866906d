from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Enum as SQLEnum, Float, JSON
from sqlalchemy.orm import relationship
from .config import BaseDBModel
from app.core.enums.purchasing import OrderStatus, SupplierRating

class PurchaseOrder(BaseDBModel):
    """Modèle pour les commandes d'achat"""
    __tablename__ = "purchase_orders"

    id = Column(Integer, primary_key=True)
    po_number = Column(String, unique=True)
    supplier_id = Column(Integer, ForeignKey("suppliers.id"))
    status = Column(SQLEnum(OrderStatus))
    order_date = Column(DateTime)
    expected_delivery = Column(DateTime, nullable=True)
    delivery_date = Column(DateTime, nullable=True)

    # Montants financiers
    subtotal_amount = Column(Float, default=0)  # Montant HT avant remise
    discount_percent = Column(Float, default=0)  # Pourcentage de remise globale
    discount_amount = Column(Float, default=0)  # Montant de la remise globale
    tax_percent = Column(Float, default=0)  # Pourcentage de TVA (configuré dans les paramètres)
    tax_amount = Column(Float, default=0)  # Montant de la TVA
    shipping_amount = Column(Float, default=0)  # Frais de livraison
    total_amount = Column(Float, default=0)  # Montant total TTC

    currency = Column(String, default="DA")
    payment_terms = Column(String, nullable=True)
    shipping_terms = Column(String, nullable=True)
    notes = Column(String, nullable=True)
    delivery_note = Column(String, nullable=True)  # Numéro de bon de livraison

    # Champs pour le suivi des paiements
    payment_status = Column(String, default="unpaid")  # unpaid, partial, paid
    payment_due_date = Column(DateTime, nullable=True)  # Date d'échéance calculée
    advance_payment_amount = Column(Float, default=0)  # Montant de l'acompte versé

    # Champs pour le suivi du workflow
    created_by = Column(Integer, ForeignKey("users.id"))
    approved_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    submitted_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    submitted_at = Column(DateTime, nullable=True)
    approved_at = Column(DateTime, nullable=True)  # Date d'approbation
    ordered_at = Column(DateTime, nullable=True)  # Date de commande effective
    received_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    received_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now())
    updated_at = Column(DateTime, default=lambda: datetime.now(), onupdate=lambda: datetime.now())

    supplier = relationship("Supplier", foreign_keys=[supplier_id])
    items = relationship("PurchaseOrderItem", back_populates="purchase_order")
    creator = relationship("User", foreign_keys=[created_by], primaryjoin="PurchaseOrder.created_by == User.id")
    approver = relationship("User", foreign_keys=[approved_by], primaryjoin="PurchaseOrder.approved_by == User.id")
    submitter = relationship("User", foreign_keys=[submitted_by], primaryjoin="PurchaseOrder.submitted_by == User.id")
    receiver = relationship("User", foreign_keys=[received_by], primaryjoin="PurchaseOrder.received_by == User.id")

class PurchaseOrderItem(BaseDBModel):
    """Modèle pour les articles de commande d'achat"""
    __tablename__ = "purchase_order_items"

    id = Column(Integer, primary_key=True)
    po_id = Column(Integer, ForeignKey("purchase_orders.id"))
    product_id = Column(Integer, ForeignKey("inventory_items.id"))
    quantity = Column(Float)
    purchase_unit_price = Column(Float)

    # Champs financiers
    discount_percent = Column(Float, default=0)  # Pourcentage de remise sur l'article
    discount_amount = Column(Float, default=0)  # Montant de la remise sur l'article
    tax_percent = Column(Float, default=0)  # Pourcentage de TVA (configuré dans les paramètres)
    tax_amount = Column(Float, default=0)  # Montant de la TVA
    subtotal = Column(Float, default=0)  # Sous-total HT (quantité * prix unitaire)
    total_price = Column(Float, default=0)  # Prix total TTC

    # Champs pour la réception
    received_quantity = Column(Float, default=0)  # Quantité reçue
    remaining_quantity = Column(Float, default=0)  # Quantité restante à recevoir
    return_quantity = Column(Float, default=0)  # Quantité retournée

    # Autres champs
    unit_of_measure = Column(String, default="pcs")  # Unité de mesure (pièces par défaut)
    specifications = Column(JSON, nullable=True)
    delivery_date = Column(DateTime, nullable=True)
    received_at = Column(DateTime, nullable=True)

    purchase_order = relationship("PurchaseOrder", back_populates="items")
    product = relationship("InventoryItem")

# La classe Supplier est maintenant importée depuis supplier.py

class SupplierQuote(BaseDBModel):
    """Modèle pour les devis fournisseur"""
    __tablename__ = "supplier_quotes"

    id = Column(Integer, primary_key=True)
    supplier_id = Column(Integer, ForeignKey("suppliers.id"))
    product_id = Column(Integer, ForeignKey("inventory_items.id"))
    quantity = Column(Float)
    purchase_unit_price = Column(Float)
    total_amount = Column(Float)
    delivery_time = Column(Integer)  # en jours
    validity_period = Column(Integer)  # en jours
    payment_terms = Column(String)
    notes = Column(String, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now())
    valid_until = Column(DateTime)

    supplier = relationship("Supplier", foreign_keys=[supplier_id])
    product = relationship("InventoryItem")



