"""
Module pour les notes techniques de réparation.
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Enum, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel

from app.core.models.base import BaseDBModel, TimestampMixin, BaseModelTimestamp

class NoteType(str, PyEnum):
    """Types de notes techniques"""
    DIAGNOSTIC = "diagnostic"  # Note de diagnostic
    REPAIR = "repair"          # Note de réparation
    CUSTOMER = "customer"      # Note client
    INTERNAL = "internal"      # Note interne
    FOLLOW_UP = "follow_up"    # Note de suivi
    OTHER = "other"            # Autre type de note

class RepairNote(BaseDBModel, TimestampMixin):
    """Modèle pour les notes techniques de réparation"""
    __tablename__ = "repair_notes"

    id = Column(Integer, primary_key=True, index=True)
    repair_id = Column(Integer, ForeignKey("repair_orders.id"), nullable=False)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    note_type = Column(Enum(NoteType), nullable=False, default=NoteType.OTHER)
    is_private = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    updated_at = Column(DateTime, nullable=True, onupdate=datetime.utcnow)
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relations
    repair = relationship("RepairOrder", back_populates="technical_notes")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])

class RepairNotePydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les notes techniques de réparation"""
    id: Optional[int] = None
    repair_id: int
    title: str
    content: str
    created_at: datetime = datetime.utcnow()
    created_by: Optional[int] = None
    updated_at: Optional[datetime] = None
    updated_by: Optional[int] = None

    class Config:
        from_attributes = True  # Anciennement orm_mode = True
