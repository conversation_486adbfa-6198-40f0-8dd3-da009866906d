"""
Module pour les photos de réparation.
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel

from app.core.models.base import BaseDBModel, TimestampMixin, BaseModelTimestamp

class PhotoType(str, PyEnum):
    """Types de photos de réparation"""
    BEFORE = "before"  # Photo avant réparation
    DURING = "during"  # Photo pendant la réparation
    AFTER = "after"    # Photo après réparation
    DAMAGE = "damage"  # Photo des dommages
    PART = "part"      # Photo des pièces
    OTHER = "other"    # Autre type de photo

class RepairPhoto(BaseDBModel, TimestampMixin):
    """Modèle pour les photos de réparation"""
    __tablename__ = "repair_photos"

    id = Column(Integer, primary_key=True, index=True)
    repair_id = Column(Integer, ForeignKey("repair_orders.id"), nullable=False)
    file_path = Column(String, nullable=False)
    thumbnail_path = Column(String, nullable=True)
    photo_type = Column(Enum(PhotoType), default=PhotoType.OTHER)
    title = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    taken_at = Column(DateTime, default=func.now())
    taken_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relations
    repair = relationship("RepairOrder")
    technician = relationship("User", foreign_keys=[taken_by])

class RepairPhotoPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les photos de réparation"""
    id: Optional[int] = None
    repair_id: int
    file_path: str
    thumbnail_path: Optional[str] = None
    photo_type: PhotoType = PhotoType.OTHER
    title: Optional[str] = None
    description: Optional[str] = None
    taken_at: datetime = datetime.utcnow()
    taken_by: Optional[int] = None

    class Config:
        orm_mode = True
