"""
Module pour l'historique des statuts des réparations.
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel

from app.core.models.base import BaseDBModel, TimestampMixin, BaseModelTimestamp
from app.core.models.repair import RepairStatus

class RepairStatusHistory(BaseDBModel, TimestampMixin):
    """Modèle pour l'historique des statuts des réparations"""
    __tablename__ = "repair_status_history"

    id = Column(Integer, primary_key=True, index=True)
    repair_id = Column(Integer, ForeignKey("repair_orders.id"), nullable=False)
    status = Column(Enum(RepairStatus), nullable=False)
    changed_at = Column(DateTime, default=func.now(), nullable=False)
    changed_by = Column(Integer, Foreign<PERSON>ey("users.id"), nullable=True)
    notes = Column(Text, nullable=True)

    # Relations
    repair = relationship("RepairOrder")
    user = relationship("User", foreign_keys=[changed_by])

class RepairStatusHistoryPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour l'historique des statuts des réparations"""
    id: Optional[int] = None
    repair_id: int
    status: RepairStatus
    changed_at: datetime
    changed_by: Optional[int] = None
    notes: Optional[str] = None

    class Config:
        orm_mode = True
