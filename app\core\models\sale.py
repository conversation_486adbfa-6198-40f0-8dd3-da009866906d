from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, ForeignKey, Text, Enum as SQLEnum
from sqlalchemy.orm import relationship
from enum import Enum
from pydantic import BaseModel, Field, conint, confloat

from .base import BaseDBModel, TimestampMixin
from .base import BaseModelTimestamp

class SaleStatus(str, Enum):
    """Statut d'une vente"""
    DRAFT = "draft"  # Brouillon
    COMPLETED = "completed"  # Terminée
    CANCELLED = "cancelled"  # Annulée
    REFUNDED = "refunded"  # Remboursée

class PaymentMethod(str, Enum):
    """Méthode de paiement"""
    cash = "cash"  # Espèces
    card = "card"  # Carte bancaire
    transfer = "transfer"  # Virement
    mobile = "mobile"  # Paiement mobile
    credit = "credit"  # Crédit
    mixed = "mixed"  # Mixte (plusieurs méthodes)

class PaymentStatus(str, Enum):
    """Statut de paiement"""
    PENDING = "pending"  # En attente
    PARTIAL = "partial"  # Partiellement payé
    PAID = "paid"  # Payé
    OVERDUE = "overdue"  # En retard
    REFUNDED = "refunded"  # Remboursé

class QuoteStatus(str, Enum):
    """Statut d'un devis"""
    DRAFT = "draft"  # Brouillon
    SENT = "sent"  # Envoyé
    ACCEPTED = "accepted"  # Accepté
    REJECTED = "rejected"  # Rejeté
    EXPIRED = "expired"  # Expiré
    CONVERTED = "converted"  # Converti en vente

class CustomerType(str, Enum):
    """Type de client"""
    REGULAR = "regular"  # Régulier
    PROFESSIONAL = "professional"  # Professionnel
    OCCASIONAL = "occasional"  # Occasionnel

class Sale(BaseDBModel, TimestampMixin):
    """Modèle pour les ventes"""
    __tablename__ = "sales"

    id = Column(Integer, primary_key=True, index=True)
    number = Column(String, unique=True, index=True)
    date = Column(DateTime, default=datetime.utcnow)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    status = Column(SQLEnum(SaleStatus), default=SaleStatus.DRAFT)
    payment_method = Column(SQLEnum(PaymentMethod), nullable=True)
    payment_status = Column(SQLEnum(PaymentStatus), default=PaymentStatus.PENDING)
    subtotal = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    discount_amount = Column(Float, default=0.0)
    final_amount = Column(Float, default=0.0)
    total_paid = Column(Float, default=0.0)
    due_date = Column(DateTime, nullable=True)
    notes = Column(Text, nullable=True)
    is_invoice = Column(Boolean, default=False)
    invoice_number = Column(String, nullable=True)
    invoice_date = Column(DateTime, nullable=True)

    # Relations
    customer = relationship("Customer", foreign_keys=[customer_id])
    user = relationship("User", foreign_keys=[user_id])
    items = relationship("SaleItem", back_populates="sale", cascade="all, delete-orphan")
    payments = relationship("Payment", back_populates="sale", cascade="all, delete-orphan")

class SaleItem(BaseDBModel, TimestampMixin):
    """Modèle pour les articles de vente"""
    __tablename__ = "sale_items"

    id = Column(Integer, primary_key=True, index=True)
    sale_id = Column(Integer, ForeignKey("sales.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("inventory_items.id"), nullable=False)
    quantity = Column(Float, default=1.0)
    purchase_unit_price = Column(Float, default=0.0)
    discount_percent = Column(Float, default=0.0)
    discount_amount = Column(Float, default=0.0)
    tax_percent = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    total_amount = Column(Float, default=0.0)

    # Relations
    sale = relationship("Sale", back_populates="items")
    product = relationship("InventoryItem", foreign_keys=[product_id])

class Payment(BaseDBModel, TimestampMixin):
    """Modèle pour les paiements"""
    __tablename__ = "payments"

    id = Column(Integer, primary_key=True, index=True)
    sale_id = Column(Integer, ForeignKey("sales.id"), nullable=False)
    amount = Column(Float, default=0.0)
    payment_method = Column(SQLEnum(PaymentMethod), nullable=False)
    payment_date = Column(DateTime, default=datetime.utcnow)
    reference = Column(String, nullable=True)
    notes = Column(Text, nullable=True)
    processed_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relations
    sale = relationship("Sale", back_populates="payments")
    processor = relationship("User", foreign_keys=[processed_by])

class Quote(BaseDBModel, TimestampMixin):
    """Modèle pour les devis"""
    __tablename__ = "quotes"

    id = Column(Integer, primary_key=True, index=True)
    number = Column(String, unique=True, index=True)
    date = Column(DateTime, default=datetime.utcnow)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"))
    status = Column(SQLEnum(QuoteStatus), default=QuoteStatus.DRAFT)
    validity_days = Column(Integer, default=30)
    valid_until = Column(DateTime)
    subtotal = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    discount_amount = Column(Float, default=0.0)
    final_amount = Column(Float, default=0.0)
    notes = Column(Text, nullable=True)
    sale_id = Column(Integer, ForeignKey("sales.id"), nullable=True)

    # Relations
    customer = relationship("Customer", foreign_keys=[customer_id])
    user = relationship("User", foreign_keys=[user_id])
    sale = relationship("Sale", foreign_keys=[sale_id])
    items = relationship("QuoteItem", back_populates="quote", cascade="all, delete-orphan")

class QuoteItem(BaseDBModel, TimestampMixin):
    """Modèle pour les articles de devis"""
    __tablename__ = "quote_items"

    id = Column(Integer, primary_key=True, index=True)
    quote_id = Column(Integer, ForeignKey("quotes.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("inventory_items.id"), nullable=False)
    quantity = Column(Float, default=1.0)
    purchase_unit_price = Column(Float, default=0.0)
    discount_percent = Column(Float, default=0.0)
    discount_amount = Column(Float, default=0.0)
    tax_percent = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    total_amount = Column(Float, default=0.0)

    # Relations
    quote = relationship("Quote", back_populates="items")
    product = relationship("InventoryItem", foreign_keys=[product_id])

class CustomerLoyalty(BaseDBModel, TimestampMixin):
    """Modèle pour la fidélité client"""
    __tablename__ = "customer_loyalty"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    points = Column(Integer, default=0)
    level = Column(String, default="Standard")
    discount_percent = Column(Float, default=0.0)
    last_activity = Column(DateTime, default=datetime.utcnow)

    # Relations
    customer = relationship("Customer", foreign_keys=[customer_id])

# Modèles Pydantic
class SalePydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les ventes"""
    id: Optional[int] = None
    number: str
    date: datetime = datetime.utcnow()
    customer_id: Optional[int] = None
    user_id: int
    status: SaleStatus = SaleStatus.DRAFT
    payment_method: Optional[PaymentMethod] = None
    payment_status: PaymentStatus = PaymentStatus.PENDING
    subtotal: float = 0.0
    tax_amount: float = 0.0
    discount_amount: float = 0.0
    final_amount: float = 0.0
    total_paid: float = 0.0
    due_date: Optional[datetime] = None
    notes: Optional[str] = None
    is_invoice: bool = False
    invoice_number: Optional[str] = None
    invoice_date: Optional[datetime] = None

class SaleItemPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les articles de vente"""
    id: Optional[int] = None
    sale_id: int
    product_id: int
    quantity: float = 1.0
    purchase_unit_price: float = Field(0.0, alias="unit_price")
    discount_percent: float = 0.0
    discount_amount: float = 0.0
    tax_percent: float = 0.0
    tax_amount: float = 0.0
    total_amount: float = 0.0

class PaymentPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les paiements"""
    id: Optional[int] = None
    sale_id: int
    amount: float
    payment_method: PaymentMethod
    payment_date: datetime = datetime.utcnow()
    reference: Optional[str] = None
    notes: Optional[str] = None
    processed_by: Optional[int] = None

class QuotePydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les devis"""
    id: Optional[int] = None
    number: str
    date: datetime = datetime.utcnow()
    customer_id: int
    user_id: int
    status: QuoteStatus = QuoteStatus.DRAFT
    validity_days: int = 30
    valid_until: Optional[datetime] = None
    subtotal: float = 0.0
    tax_amount: float = 0.0
    discount_amount: float = 0.0
    final_amount: float = 0.0
    notes: Optional[str] = None
    sale_id: Optional[int] = None

class QuoteItemPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les articles de devis"""
    id: Optional[int] = None
    quote_id: int
    product_id: int
    quantity: float = 1.0
    purchase_unit_price: float = Field(0.0, alias="unit_price")
    discount_percent: float = 0.0
    discount_amount: float = 0.0
    tax_percent: float = 0.0
    tax_amount: float = 0.0
    total_amount: float = 0.0

class CustomerLoyaltyPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour la fidélité client"""
    id: Optional[int] = None
    customer_id: int
    points: int = 0
    level: str = "Standard"
    discount_percent: float = 0.0
    last_activity: datetime = datetime.utcnow()
