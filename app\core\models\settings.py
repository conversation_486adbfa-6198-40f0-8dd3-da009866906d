"""
Modèles pour les paramètres de l'application.
"""
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, Boolean, DateTime, JSON, Text
from sqlalchemy.orm import relationship
from pydantic import BaseModel

from app.core.models.base import BaseDBModel, TimestampMixin, BaseModelTimestamp

class SettingCategory(str, Enum):
    """Catégories de paramètres"""
    GENERAL = "general"  # Paramètres généraux
    APPEARANCE = "appearance"  # Apparence de l'application
    DATABASE = "database"  # Paramètres de base de données
    BACKUP = "backup"  # Paramètres de sauvegarde
    SECURITY = "security"  # Paramètres de sécurité
    NOTIFICATIONS = "notifications"  # Paramètres de notifications
    PRINTING = "printing"  # Paramètres d'impression
    ADVANCED = "advanced"  # Paramètres avancés

class BackupFrequency(str, Enum):
    """Fréquence des sauvegardes automatiques"""
    DAILY = "daily"  # Quotidienne
    WEEKLY = "weekly"  # Hebdomadaire
    MONTHLY = "monthly"  # Mensuelle
    NEVER = "never"  # Jamais

class AppSetting(BaseDBModel, TimestampMixin):
    """Modèle pour les paramètres de l'application"""
    __tablename__ = "app_settings"

    id = Column(Integer, primary_key=True, index=True)
    key = Column(String, unique=True, index=True)
    value = Column(Text)
    category = Column(String)
    description = Column(String, nullable=True)
    is_system = Column(Boolean, default=False)  # Paramètre système (non modifiable par l'utilisateur)

class BackupInfo(BaseDBModel, TimestampMixin):
    """Modèle pour les informations de sauvegarde"""
    __tablename__ = "backup_info"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String)
    path = Column(String)
    size = Column(Integer)  # Taille en octets
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, nullable=True)  # ID de l'utilisateur qui a créé la sauvegarde
    description = Column(String, nullable=True)
    version = Column(String, nullable=True)  # Version de l'application
    is_auto = Column(Boolean, default=False)  # Sauvegarde automatique
    backup_metadata = Column(JSON, nullable=True)  # Métadonnées supplémentaires

# Modèles Pydantic
class AppSettingPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les paramètres de l'application"""
    id: Optional[int] = None
    key: str
    value: str
    category: str
    description: Optional[str] = None
    is_system: bool = False

class BackupInfoPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les informations de sauvegarde"""
    id: Optional[int] = None
    filename: str
    path: str
    size: int
    created_at: datetime
    created_by: Optional[int] = None
    description: Optional[str] = None
    version: Optional[str] = None
    is_auto: bool = False
    backup_metadata: Optional[Dict[str, Any]] = None

class AppSettingsUpdate(BaseModel):
    """Modèle pour la mise à jour des paramètres de l'application"""
    general: Optional[Dict[str, Any]] = None
    appearance: Optional[Dict[str, Any]] = None
    database: Optional[Dict[str, Any]] = None
    backup: Optional[Dict[str, Any]] = None
    security: Optional[Dict[str, Any]] = None
    notifications: Optional[Dict[str, Any]] = None
    printing: Optional[Dict[str, Any]] = None
    advanced: Optional[Dict[str, Any]] = None

class BackupSettings(BaseModel):
    """Paramètres de sauvegarde"""
    auto_backup: bool = False
    backup_frequency: BackupFrequency = BackupFrequency.WEEKLY
    backup_path: str = "./backups"
    max_backups: int = 10
    include_attachments: bool = True
    compress_backup: bool = True
    encrypt_backup: bool = False
    encryption_key: Optional[str] = None

class AppearanceSettings(BaseModel):
    """Paramètres d'apparence"""
    theme: str = "light"
    font_size: int = 12
    language: str = "fr"
    show_welcome_screen: bool = True
    compact_mode: bool = False
    enable_animations: bool = True

class GeneralSettings(BaseModel):
    """Paramètres généraux"""
    company_name: str = ""
    company_address: str = ""
    company_phone: str = ""
    company_email: str = ""
    company_website: str = ""
    company_logo: str = ""
    default_currency: str = "DA"
    tax_rate: float = 19.0
    fiscal_year_start: str = "01-01"

class DatabaseSettings(BaseModel):
    """Paramètres de base de données"""
    auto_optimize: bool = True
    optimize_frequency: BackupFrequency = BackupFrequency.WEEKLY
    connection_timeout: int = 30
    max_connections: int = 10

class SecuritySettings(BaseModel):
    """Paramètres de sécurité"""
    session_timeout: int = 30  # minutes
    password_expiry: int = 90  # jours
    min_password_length: int = 8
    require_special_chars: bool = True
    require_numbers: bool = True
    require_uppercase: bool = True
    max_login_attempts: int = 5
    enable_two_factor: bool = False
    audit_log_retention: int = 90  # jours

class NotificationSettings(BaseModel):
    """Paramètres de notifications"""
    enable_notifications: bool = True
    enable_email_notifications: bool = False
    enable_desktop_notifications: bool = True
    notification_sound: bool = True
    notification_retention: int = 30  # jours

class PrintingSettings(BaseModel):
    """Paramètres d'impression"""
    default_printer: str = ""
    paper_size: str = "A4"
    orientation: str = "portrait"
    margin_top: int = 20
    margin_bottom: int = 20
    margin_left: int = 20
    margin_right: int = 20
    header_text: str = ""
    footer_text: str = ""
    show_logo: bool = True

class AdvancedSettings(BaseModel):
    """Paramètres avancés"""
    debug_mode: bool = False
    log_level: str = "INFO"
    enable_api: bool = False
    api_port: int = 8000
    enable_remote_access: bool = False
    remote_access_port: int = 8080
    enable_auto_update: bool = True
