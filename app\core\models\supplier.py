from datetime import datetime
from typing import Optional, Dict, List
from sqlalchemy import Column, Integer, String, Boolean, JSON, Float
from sqlalchemy.types import Enum as SQLEnum
from sqlalchemy.orm import Mapped, relationship
from enum import Enum
from app.core.models.base import BaseDBModel, TimestampMixin, BaseModelTimestamp

# Forward references pour éviter les imports circulaires
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from app.core.models.inventory import InventoryItem
    from app.core.models.purchasing import PurchaseOrder

class SupplierRating(int, Enum):
    EXCELLENT = 5
    GOOD = 4
    AVERAGE = 3
    FAIR = 2
    POOR = 1

class Supplier(BaseDBModel, TimestampMixin):
    __tablename__ = "suppliers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    contact_person = Column(String, nullable=True)
    email = Column(String)
    phone = Column(String, nullable=True)
    address = Column(String)
    commune = Column(String, nullable=True)
    city = Column(String, nullable=True)
    postal_code = Column(String, nullable=True)
    tax_id = Column(String, nullable=True)
    rating = Column(SQLEnum(SupplierRating))
    payment_terms = Column(String, nullable=True)
    active = Column(Boolean, default=True)
    evaluation_scores = Column(JSON, default=dict)

    # Nouveaux champs pour les délais de livraison
    avg_delivery_time = Column(Integer, nullable=True)  # Délai moyen de livraison en jours
    min_delivery_time = Column(Integer, nullable=True)  # Délai minimum de livraison en jours
    max_delivery_time = Column(Integer, nullable=True)  # Délai maximum de livraison en jours
    delivery_reliability = Column(Float, nullable=True)  # Fiabilité de livraison (0-100%)

    # Relations définies avec string pour éviter les imports circulaires
    # Commenté pour éviter les erreurs de référence circulaire
    # items: Mapped[List["InventoryItem"]] = relationship("InventoryItem", back_populates="supplier")
    # purchase_orders: Mapped[List["PurchaseOrder"]] = relationship("PurchaseOrder", back_populates="supplier", foreign_keys="[PurchaseOrder.supplier_id]")

class SupplierPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les fournisseurs"""
    id: Optional[int] = None
    name: str
    contact_person: Optional[str] = None
    phone: Optional[str] = None
    email: str
    address: str
    commune: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    tax_id: Optional[str] = None
    rating: SupplierRating
    payment_terms: Optional[str] = None
    active: bool = True
    evaluation_scores: Dict = {}

    # Nouveaux champs pour les délais de livraison
    avg_delivery_time: Optional[int] = None
    min_delivery_time: Optional[int] = None
    max_delivery_time: Optional[int] = None
    delivery_reliability: Optional[float] = None



