"""
Modèles pour la gestion financière des fournisseurs.
"""
from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Enum as SQLEnum, Boolean
from sqlalchemy.orm import relationship, Mapped, mapped_column
from pydantic import BaseModel, conint, confloat
from typing import Annotated

from app.core.models.base import BaseDBModel, TimestampMixin, BaseModelTimestamp
from app.core.models.purchasing import PurchaseOrder

class InvoiceStatus(str, Enum):
    """Statut d'une facture fournisseur"""
    PENDING = "pending"  # En attente de paiement
    PARTIAL = "partial"  # Partiellement payée
    PAID = "paid"  # Entièrement payée
    CANCELLED = "cancelled"  # Annulée
    DISPUTED = "disputed"  # Contestée

class PaymentMethod(str, Enum):
    """Méthode de paiement"""
    cash = "cash"  # Espèces
    bank_transfer = "bank_transfer"  # Virement bancaire
    check = "check"  # Chèque
    credit_card = "credit_card"  # Carte de crédit
    other = "other"  # Autre

class SupplierInvoice(BaseDBModel, TimestampMixin):
    """Modèle pour les factures fournisseurs"""
    __tablename__ = "supplier_invoices"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    invoice_number: Mapped[str] = mapped_column(String, unique=True, index=True)
    supplier_id: Mapped[int] = mapped_column(Integer, ForeignKey("suppliers.id"))
    purchase_order_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("purchase_orders.id"), nullable=True)
    invoice_date: Mapped[datetime] = mapped_column(DateTime)
    due_date: Mapped[datetime] = mapped_column(DateTime)
    total_amount: Mapped[float] = mapped_column(Float)
    tax_amount: Mapped[float] = mapped_column(Float, default=0)
    currency: Mapped[str] = mapped_column(String, default="DA")
    status: Mapped[InvoiceStatus] = mapped_column(SQLEnum(InvoiceStatus), default=InvoiceStatus.PENDING)
    notes: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)

    # Relations
    supplier = relationship("Supplier", foreign_keys=[supplier_id])
    purchase_order = relationship("PurchaseOrder", foreign_keys=[purchase_order_id])
    payments = relationship("SupplierPayment", back_populates="invoice")

class SupplierPayment(BaseDBModel, TimestampMixin):
    """Modèle pour les paiements aux fournisseurs"""
    __tablename__ = "supplier_payments"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    supplier_id: Mapped[int] = mapped_column(Integer, ForeignKey("suppliers.id"))
    invoice_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("supplier_invoices.id"), nullable=True)
    purchase_order_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("purchase_orders.id"), nullable=True)
    payment_date: Mapped[datetime] = mapped_column(DateTime)
    amount: Mapped[float] = mapped_column(Float)
    payment_method: Mapped[PaymentMethod] = mapped_column(SQLEnum(PaymentMethod))
    reference: Mapped[Optional[str]] = mapped_column(String, nullable=True)  # Référence du paiement (numéro de chèque, etc.)
    notes: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    processed_by: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"))

    # Relations
    supplier = relationship("Supplier", foreign_keys=[supplier_id])
    invoice = relationship("SupplierInvoice", back_populates="payments")
    purchase_order = relationship("PurchaseOrder", foreign_keys=[purchase_order_id])
    processor = relationship("User", foreign_keys=[processed_by])

class PriceHistory(BaseDBModel, TimestampMixin):
    """Modèle pour l'historique des prix"""
    __tablename__ = "price_history"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    item_id: Mapped[int] = mapped_column(Integer, ForeignKey("inventory_items.id"))
    old_purchase_price: Mapped[float] = mapped_column(Float)
    new_purchase_price: Mapped[float] = mapped_column(Float)
    old_unit_price: Mapped[float] = mapped_column(Float)
    new_unit_price: Mapped[float] = mapped_column(Float)
    change_date: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now())
    change_reason: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    purchase_order_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("purchase_orders.id"), nullable=True)
    changed_by: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"))

    # Relations
    item = relationship("InventoryItem")
    purchase_order = relationship("PurchaseOrder")
    user = relationship("User", foreign_keys=[changed_by])

# Modèles Pydantic
class SupplierInvoicePydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les factures fournisseurs"""
    id: Optional[int] = None
    invoice_number: str
    supplier_id: int
    purchase_order_id: Optional[int] = None
    invoice_date: datetime
    due_date: datetime
    total_amount: float
    tax_amount: float = 0
    currency: str = "DA"
    status: InvoiceStatus = InvoiceStatus.PENDING
    notes: Optional[str] = None
    is_active: bool = True

class SupplierPaymentPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les paiements aux fournisseurs"""
    id: Optional[int] = None
    supplier_id: int
    invoice_id: Optional[int] = None
    purchase_order_id: Optional[int] = None
    payment_date: datetime
    amount: float
    payment_method: PaymentMethod
    reference: Optional[str] = None
    notes: Optional[str] = None
    processed_by: int

class PriceHistoryPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour l'historique des prix"""
    id: Optional[int] = None
    item_id: int
    old_purchase_price: float
    new_purchase_price: float
    old_unit_price: float
    new_unit_price: float
    change_date: datetime
    change_reason: Optional[str] = None
    purchase_order_id: Optional[int] = None
    changed_by: int
