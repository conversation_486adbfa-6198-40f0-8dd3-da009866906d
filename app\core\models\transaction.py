"""
Modèles pour les transactions financières.
"""
from enum import Enum, auto

class TransactionType(str, Enum):
    """Types de transactions financières"""
    PAYMENT = "payment"  # Paiement reçu
    INVOICE = "invoice"  # Facture émise
    REFUND = "refund"    # Remboursement
    CREDIT = "credit"    # Crédit accordé
    DEBIT = "debit"      # Débit
    ADJUSTMENT = "adjustment"  # Ajustement
    OTHER = "other"      # Autre
