"""
Modèles pour la gestion de la trésorerie.
"""
from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any
from decimal import Decimal
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Enum as SQLEnum, Boolean, Text, Numeric
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.core.models.base import BaseDBModel, TimestampMixin, BaseModelTimestamp

class CashRegisterType(str, Enum):
    """Types de caisses"""
    MAIN = "main"  # Caisse principale
    REPAIR = "repair"  # Caisse des réparations
    SALES = "sales"  # Caisse des ventes
    PURCHASE = "purchase"  # Caisse des achats
    EXPENSE = "expense"  # Caisse des dépenses

class TransactionCategory(str, Enum):
    """Catégories de transactions"""
    SALE = "sale"  # Vente
    REPAIR = "repair"  # Réparation
    PURCHASE = "purchase"  # Achat
    EXPENSE = "expense"  # Dépense
    TRANSFER = "transfer"  # Transfert entre caisses
    DEPOSIT = "deposit"  # Dépôt
    WITHDRAWAL = "withdrawal"  # Retrait
    ADJUSTMENT = "adjustment"  # Ajustement
    OTHER = "other"  # Autre

class PaymentMethod(str, Enum):
    """Méthodes de paiement"""
    cash = "cash"  # Espèces
    bank_transfer = "bank_transfer"  # Virement bancaire
    check = "check"  # Chèque
    credit_card = "credit_card"  # Carte de crédit
    other = "other"  # Autre

class CashRegister(BaseDBModel, TimestampMixin):
    """Modèle pour les caisses"""
    __tablename__ = "cash_registers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True)
    type = Column(SQLEnum(CashRegisterType))
    initial_balance = Column(Numeric(18, 2), default=Decimal("0.00"))
    current_balance = Column(Numeric(18, 2), default=Decimal("0.00"))
    last_reconciliation = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)
    notes = Column(Text, nullable=True)

    # Relations
    transactions = relationship("CashTransaction", back_populates="cash_register")

class CashTransaction(BaseDBModel, TimestampMixin):
    """Modèle pour les transactions de caisse"""
    __tablename__ = "cash_transactions"

    id = Column(Integer, primary_key=True, index=True)
    cash_register_id = Column(Integer, ForeignKey("cash_registers.id"))
    amount = Column(Numeric(18, 2))
    transaction_date = Column(DateTime, default=datetime.utcnow)
    category = Column(SQLEnum(TransactionCategory))
    payment_method = Column(SQLEnum(PaymentMethod))
    reference_number = Column(String, nullable=True)
    description = Column(Text, nullable=True)

    # Références optionnelles vers d'autres entités
    sale_id = Column(Integer, ForeignKey("sales.id"), nullable=True)
    repair_id = Column(Integer, ForeignKey("repair_orders.id"), nullable=True)
    purchase_id = Column(Integer, ForeignKey("purchase_orders.id"), nullable=True)
    supplier_payment_id = Column(Integer, ForeignKey("supplier_payments.id"), nullable=True)
    customer_transaction_id = Column(Integer, ForeignKey("customer_transactions.id"), nullable=True)
    expense_id = Column(Integer, ForeignKey("expenses.id"), nullable=True)

    # Utilisateur qui a effectué la transaction
    user_id = Column(Integer, ForeignKey("users.id"))

    # Relations
    cash_register = relationship("CashRegister", back_populates="transactions")
    # Commenté pour éviter l'erreur de référence à la classe Sale qui n'existe pas encore
    # sale = relationship("Sale", foreign_keys=[sale_id])
    # Commenté pour éviter l'erreur de référence à la classe RepairOrder qui n'existe pas encore
    # repair = relationship("RepairOrder", foreign_keys=[repair_id])
    # Commenté pour éviter l'erreur de référence à la classe PurchaseOrder qui n'existe pas encore
    # purchase = relationship("PurchaseOrder", foreign_keys=[purchase_id])
    # Commenté pour éviter l'erreur de référence à la classe SupplierPayment qui n'existe pas encore
    # supplier_payment = relationship("SupplierPayment", foreign_keys=[supplier_payment_id])
    # Commenté pour éviter l'erreur de référence à la classe CustomerTransaction qui n'existe pas encore
    # customer_transaction = relationship("CustomerTransaction", foreign_keys=[customer_transaction_id])
    expense = relationship("Expense", foreign_keys=[expense_id], back_populates="transaction")
    # Commenté pour éviter l'erreur de référence à la classe User qui n'existe pas encore
    # user = relationship("User", foreign_keys=[user_id])

class Expense(BaseDBModel, TimestampMixin):
    """Modèle pour les dépenses"""
    __tablename__ = "expenses"

    id = Column(Integer, primary_key=True, index=True)
    amount = Column(Numeric(18, 2))
    expense_date = Column(DateTime, default=datetime.utcnow)
    category = Column(String)
    payment_method = Column(SQLEnum(PaymentMethod))
    reference_number = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    receipt_image = Column(String, nullable=True)  # Chemin vers l'image du reçu

    # Caisse utilisée pour la dépense
    cash_register_id = Column(Integer, ForeignKey("cash_registers.id"))

    # Utilisateur qui a enregistré la dépense
    user_id = Column(Integer, ForeignKey("users.id"))

    # Relations
    cash_register = relationship("CashRegister")
    # Commenté pour éviter l'erreur de référence à la classe User qui n'existe pas encore
    # user = relationship("User", foreign_keys=[user_id])
    transaction = relationship("CashTransaction", uselist=False, back_populates="expense")

# Modèles Pydantic
class CashRegisterPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les caisses"""
    id: Optional[int] = None
    name: str
    type: CashRegisterType
    initial_balance: Decimal = Decimal("0.00")
    current_balance: Decimal = Decimal("0.00")
    last_reconciliation: Optional[datetime] = None
    is_active: bool = True
    notes: Optional[str] = None

class CashTransactionPydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les transactions de caisse"""
    id: Optional[int] = None
    cash_register_id: int
    amount: Decimal
    transaction_date: datetime = datetime.utcnow()
    category: TransactionCategory
    payment_method: PaymentMethod
    reference_number: Optional[str] = None
    description: Optional[str] = None
    sale_id: Optional[int] = None
    repair_id: Optional[int] = None
    purchase_id: Optional[int] = None
    supplier_payment_id: Optional[int] = None
    customer_transaction_id: Optional[int] = None
    expense_id: Optional[int] = None
    user_id: int

class ExpensePydantic(BaseModelTimestamp):
    """Modèle Pydantic pour les dépenses"""
    id: Optional[int] = None
    amount: Decimal
    expense_date: datetime = datetime.utcnow()
    category: str
    payment_method: PaymentMethod
    reference_number: Optional[str] = None
    description: Optional[str] = None
    receipt_image: Optional[str] = None
    cash_register_id: int
    user_id: int
