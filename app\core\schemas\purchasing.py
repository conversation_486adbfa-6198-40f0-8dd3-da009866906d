from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic.v1 import BaseModel, Field
from app.core.enums.purchasing import OrderStatus, SupplierRating


# -------------------------
# Purchase Order Item
# -------------------------
class PurchaseOrderItemBase(BaseModel):
    product_id: int
    quantity: float = Field(gt=0)
    purchase_unit_price: float = Field(ge=0)

    # Optional financial fields to align with ORM (defaults keep create payloads minimal)
    discount_percent: float = 0
    discount_amount: float = 0
    tax_percent: float = 19
    tax_amount: float = 0

    # Other fields
    unit_of_measure: str = "pcs"
    specifications: Optional[Dict[str, Any]] = None
    delivery_date: Optional[datetime] = None

    class Config:
        allow_population_by_field_name = True


class PurchaseOrderItemCreate(PurchaseOrderItemBase):
    pass


class PurchaseOrderItem(PurchaseOrderItemBase):
    id: int
    po_id: int

    # Calculated/aggregated values
    subtotal: float = 0
    total_price: float = 0

    # Receiving info
    received_quantity: float = 0
    remaining_quantity: float = 0
    return_quantity: float = 0
    received_at: Optional[datetime] = None

    class Config:
        orm_mode = True
        allow_population_by_field_name = True


# -------------------------
# Purchase Order
# -------------------------
class PurchaseOrderBase(BaseModel):
    supplier_id: int
    currency: str = "DA"
    payment_terms: Optional[str] = None
    shipping_terms: Optional[str] = None
    notes: Optional[str] = None
    delivery_note: Optional[str] = None


class PurchaseOrderCreate(PurchaseOrderBase):
    items: List[PurchaseOrderItemCreate]


class PurchaseOrderUpdate(BaseModel):
    status: Optional[OrderStatus] = None
    expected_delivery: Optional[datetime] = None
    payment_terms: Optional[str] = None
    shipping_terms: Optional[str] = None
    notes: Optional[str] = None


class PurchaseOrder(PurchaseOrderBase):
    id: int
    po_number: Optional[str] = None
    status: OrderStatus
    order_date: Optional[datetime] = None
    expected_delivery: Optional[datetime] = None
    delivery_date: Optional[datetime] = None

    # Financial amounts (aligned with ORM defaults)
    subtotal_amount: float = 0
    discount_percent: float = 0
    discount_amount: float = 0
    tax_percent: float = 19
    tax_amount: float = 0
    shipping_amount: float = 0
    total_amount: float = 0

    # Payment tracking
    payment_status: str = "unpaid"  # unpaid, partial, paid
    payment_due_date: Optional[datetime] = None
    advance_payment_amount: float = 0

    # Workflow tracking
    created_by: Optional[int] = None
    approved_by: Optional[int] = None
    submitted_by: Optional[int] = None
    submitted_at: Optional[datetime] = None
    approved_at: Optional[datetime] = None
    ordered_at: Optional[datetime] = None
    received_by: Optional[int] = None
    received_at: Optional[datetime] = None

    items: List[PurchaseOrderItem] = []
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


# -------------------------
# Supplier
# -------------------------
class SupplierBase(BaseModel):
    name: str
    contact_person: Optional[str] = None
    email: str
    phone: Optional[str] = None
    address: str
    tax_id: Optional[str] = None
    payment_terms: Optional[str] = None
    edi_enabled: bool = False
    evaluation_scores: Dict[str, float] = Field(default_factory=dict)


class SupplierCreate(SupplierBase):
    pass


class SupplierUpdate(BaseModel):
    contact_person: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    payment_terms: Optional[str] = None
    edi_enabled: Optional[bool] = None
    evaluation_scores: Optional[Dict[str, float]] = None


class Supplier(SupplierBase):
    id: int
    rating: SupplierRating
    active: bool = True
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


# -------------------------
# Supplier Quote
# -------------------------
class SupplierQuoteBase(BaseModel):
    supplier_id: int
    product_id: int
    quantity: float = Field(gt=0)
    purchase_unit_price: float = Field(ge=0)
    delivery_time: int  # en jours
    validity_period: int  # en jours
    payment_terms: str
    notes: Optional[str] = None


class SupplierQuoteCreate(SupplierQuoteBase):
    pass


class SupplierQuote(SupplierQuoteBase):
    id: int
    total_amount: float = Field(ge=0)
    created_at: datetime
    valid_until: datetime

    class Config:
        orm_mode = True


# -------------------------
# Order Receipt (auxiliary)
# -------------------------
class OrderReceiptBase(BaseModel):
    order_id: int
    received_items: List[Dict[str, Any]]
    receipt_date: datetime = Field(default_factory=datetime.utcnow)
    received_by: int
    notes: Optional[str] = None


class OrderReceiptCreate(OrderReceiptBase):
    pass


class OrderReceipt(OrderReceiptBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True
