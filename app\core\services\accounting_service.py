"""
Service pour la gestion de la comptabilité.
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.core.models.supplier_finance import SupplierInvoice, SupplierPayment
from app.core.models.notification import NotificationType, NotificationPriority, NotificationChannel
from app.core.services.notification_service import NotificationService

class AccountingService:
    """Service pour la gestion de la comptabilité"""
    
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService(db)
        
    async def record_supplier_invoice(self, invoice: SupplierInvoice) -> Dict[str, Any]:
        """
        Enregistre une facture fournisseur dans le système comptable
        
        Args:
            invoice: La facture fournisseur à enregistrer
            
        Returns:
            Dictionnaire contenant les informations sur l'opération
        """
        # Dans une implémentation réelle, cette méthode créerait une écriture comptable
        # pour enregistrer la facture fournisseur dans le grand livre
        
        # Pour l'instant, nous simulons l'opération
        accounting_entry = {
            "date": datetime.now(),
            "type": "supplier_invoice",
            "reference": invoice.invoice_number,
            "description": f"Facture fournisseur {invoice.invoice_number}",
            "amount": invoice.total_amount,
            "account_debit": "Achats",  # Compte de charge
            "account_credit": "Fournisseurs",  # Compte de passif
            "supplier_id": invoice.supplier_id,
            "invoice_id": invoice.id
        }
        
        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": None,  # Notification système
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.LOW,
            "title": "Écriture comptable créée",
            "message": f"Une écriture comptable a été créée pour la facture {invoice.invoice_number}.",
            "data": {"invoice_id": invoice.id, "supplier_id": invoice.supplier_id},
            "action_url": f"/accounting/entries",
            "icon": "accounting",
            "channels": [NotificationChannel.UI]
        })
        
        return accounting_entry
        
    async def record_supplier_payment(self, payment: SupplierPayment) -> Dict[str, Any]:
        """
        Enregistre un paiement fournisseur dans le système comptable
        
        Args:
            payment: Le paiement fournisseur à enregistrer
            
        Returns:
            Dictionnaire contenant les informations sur l'opération
        """
        # Dans une implémentation réelle, cette méthode créerait une écriture comptable
        # pour enregistrer le paiement fournisseur dans le grand livre
        
        # Pour l'instant, nous simulons l'opération
        reference = payment.reference or f"PAY-{payment.id}"
        
        accounting_entry = {
            "date": datetime.now(),
            "type": "supplier_payment",
            "reference": reference,
            "description": f"Paiement fournisseur {reference}",
            "amount": payment.amount,
            "account_debit": "Fournisseurs",  # Compte de passif
            "account_credit": self._get_payment_account(payment.payment_method),  # Compte de trésorerie
            "supplier_id": payment.supplier_id,
            "payment_id": payment.id
        }
        
        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": None,  # Notification système
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.LOW,
            "title": "Écriture comptable créée",
            "message": f"Une écriture comptable a été créée pour le paiement {reference}.",
            "data": {"payment_id": payment.id, "supplier_id": payment.supplier_id},
            "action_url": f"/accounting/entries",
            "icon": "accounting",
            "channels": [NotificationChannel.UI]
        })
        
        return accounting_entry
        
    def _get_payment_account(self, payment_method: str) -> str:
        """
        Retourne le compte comptable correspondant à la méthode de paiement
        
        Args:
            payment_method: La méthode de paiement
            
        Returns:
            Le nom du compte comptable
        """
        accounts = {
            "cash": "Caisse",
            "bank_transfer": "Banque",
            "check": "Banque",
            "credit_card": "Carte de crédit",
            "other": "Autres moyens de paiement"
        }
        
        return accounts.get(payment_method, "Banque")
        
    async def generate_supplier_balance_report(self, supplier_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Génère un rapport de solde fournisseur
        
        Args:
            supplier_id: ID du fournisseur (optionnel, si None, génère pour tous les fournisseurs)
            
        Returns:
            Dictionnaire contenant les informations du rapport
        """
        # Dans une implémentation réelle, cette méthode générerait un rapport détaillé
        # des soldes fournisseurs à partir des écritures comptables
        
        # Pour l'instant, nous simulons le rapport
        report = {
            "date": datetime.now(),
            "title": "Rapport de solde fournisseur",
            "supplier_id": supplier_id,
            "suppliers": []
        }
        
        # Récupérer les factures
        query = self.db.query(SupplierInvoice)
        if supplier_id:
            query = query.filter(SupplierInvoice.supplier_id == supplier_id)
        invoices = query.all()
        
        # Récupérer les paiements
        query = self.db.query(SupplierPayment)
        if supplier_id:
            query = query.filter(SupplierPayment.supplier_id == supplier_id)
        payments = query.all()
        
        # Organiser les données par fournisseur
        supplier_data = {}
        
        # Traiter les factures
        for invoice in invoices:
            if invoice.supplier_id not in supplier_data:
                supplier_data[invoice.supplier_id] = {
                    "supplier_id": invoice.supplier_id,
                    "supplier_name": invoice.supplier.name if invoice.supplier else "N/A",
                    "total_due": 0,
                    "total_paid": 0,
                    "invoices": [],
                    "payments": []
                }
                
            supplier_data[invoice.supplier_id]["total_due"] += invoice.total_amount
            supplier_data[invoice.supplier_id]["invoices"].append({
                "id": invoice.id,
                "invoice_number": invoice.invoice_number,
                "date": invoice.invoice_date,
                "due_date": invoice.due_date,
                "amount": invoice.total_amount,
                "status": invoice.status.value
            })
            
        # Traiter les paiements
        for payment in payments:
            if payment.supplier_id not in supplier_data:
                supplier_data[payment.supplier_id] = {
                    "supplier_id": payment.supplier_id,
                    "supplier_name": payment.supplier.name if payment.supplier else "N/A",
                    "total_due": 0,
                    "total_paid": 0,
                    "invoices": [],
                    "payments": []
                }
                
            supplier_data[payment.supplier_id]["total_paid"] += payment.amount
            supplier_data[payment.supplier_id]["payments"].append({
                "id": payment.id,
                "date": payment.payment_date,
                "amount": payment.amount,
                "method": payment.payment_method.value,
                "reference": payment.reference
            })
            
        # Ajouter les données au rapport
        report["suppliers"] = list(supplier_data.values())
        
        return report
