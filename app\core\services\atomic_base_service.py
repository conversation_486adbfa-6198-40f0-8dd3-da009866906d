"""
Service de base avec support des transactions atomiques.
"""
import logging
from typing import TypeVar, Generic, Type, Dict, Any, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.core.models.base import BaseDBModel
from app.utils.transaction_manager import atomic_operation, TransactionError, TransactionManager

logger = logging.getLogger(__name__)

ModelType = TypeVar("ModelType", bound=BaseDBModel)
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")


class AtomicBaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    Service de base avec support des transactions atomiques et validation stricte.
    """
    
    def __init__(self, db: Session, model: Type[ModelType]):
        """
        Initialise le service
        
        Args:
            db: Session SQLAlchemy
            model: Classe du modèle SQLAlchemy
        """
        self.db = db
        self.model = model
        self.transaction_manager = TransactionManager(db)
    
    def create(self, obj_in: CreateSchemaType, **kwargs) -> ModelType:
        """
        Crée un nouvel objet avec transaction atomique
        
        Args:
            obj_in: Données d'entrée
            **kwargs: Arguments supplémentaires
            
        Returns:
            Objet créé
            
        Raises:
            TransactionError: En cas d'erreur de transaction
        """
        try:
            with atomic_operation(self.db, f"Création {self.model.__name__}") as session:
                # Convertir les données Pydantic en dict si nécessaire
                if hasattr(obj_in, 'dict'):
                    obj_data = obj_in.dict()
                elif hasattr(obj_in, 'model_dump'):
                    obj_data = obj_in.model_dump()
                else:
                    obj_data = obj_in
                
                # Ajouter les arguments supplémentaires
                obj_data.update(kwargs)
                
                # Créer l'objet
                db_obj = self.model(**obj_data)
                session.add(db_obj)
                session.flush()
                session.refresh(db_obj)
                
                logger.info(f"{self.model.__name__} créé avec ID {db_obj.id}")
                return db_obj
                
        except TransactionError:
            raise
        except Exception as e:
            raise TransactionError(f"Erreur lors de la création de {self.model.__name__}: {e}")
    
    def get(self, id: Any) -> Optional[ModelType]:
        """
        Récupère un objet par son ID
        
        Args:
            id: ID de l'objet
            
        Returns:
            Objet trouvé ou None
        """
        try:
            return self.db.query(self.model).filter(self.model.id == id).first()
        except SQLAlchemyError as e:
            logger.error(f"Erreur lors de la récupération de {self.model.__name__}#{id}: {e}")
            return None
    
    def get_multi(self, skip: int = 0, limit: int = 100, **filters) -> List[ModelType]:
        """
        Récupère plusieurs objets avec pagination et filtres
        
        Args:
            skip: Nombre d'éléments à ignorer
            limit: Nombre maximum d'éléments à retourner
            **filters: Filtres à appliquer
            
        Returns:
            Liste d'objets
        """
        try:
            query = self.db.query(self.model)
            
            # Appliquer les filtres
            for field, value in filters.items():
                if hasattr(self.model, field) and value is not None:
                    query = query.filter(getattr(self.model, field) == value)
            
            return query.offset(skip).limit(limit).all()
            
        except SQLAlchemyError as e:
            logger.error(f"Erreur lors de la récupération multiple de {self.model.__name__}: {e}")
            return []
    
    def update(self, db_obj: ModelType, obj_in: UpdateSchemaType, **kwargs) -> ModelType:
        """
        Met à jour un objet avec transaction atomique
        
        Args:
            db_obj: Objet à mettre à jour
            obj_in: Nouvelles données
            **kwargs: Arguments supplémentaires
            
        Returns:
            Objet mis à jour
            
        Raises:
            TransactionError: En cas d'erreur de transaction
        """
        try:
            with atomic_operation(self.db, f"Mise à jour {self.model.__name__}#{db_obj.id}") as session:
                # Convertir les données Pydantic en dict si nécessaire
                if hasattr(obj_in, 'dict'):
                    update_data = obj_in.dict(exclude_unset=True)
                elif hasattr(obj_in, 'model_dump'):
                    update_data = obj_in.model_dump(exclude_unset=True)
                else:
                    update_data = obj_in
                
                # Ajouter les arguments supplémentaires
                update_data.update(kwargs)
                
                # Mettre à jour les champs
                for field, value in update_data.items():
                    if hasattr(db_obj, field):
                        setattr(db_obj, field, value)
                
                session.flush()
                session.refresh(db_obj)
                
                logger.info(f"{self.model.__name__}#{db_obj.id} mis à jour")
                return db_obj
                
        except TransactionError:
            raise
        except Exception as e:
            raise TransactionError(f"Erreur lors de la mise à jour de {self.model.__name__}#{db_obj.id}: {e}")
    
    def delete(self, id: Any) -> bool:
        """
        Supprime un objet avec transaction atomique
        
        Args:
            id: ID de l'objet à supprimer
            
        Returns:
            True si supprimé, False sinon
            
        Raises:
            TransactionError: En cas d'erreur de transaction
        """
        try:
            with atomic_operation(self.db, f"Suppression {self.model.__name__}#{id}") as session:
                db_obj = session.query(self.model).filter(self.model.id == id).first()
                if not db_obj:
                    return False
                
                session.delete(db_obj)
                session.flush()
                
                logger.info(f"{self.model.__name__}#{id} supprimé")
                return True
                
        except TransactionError:
            raise
        except Exception as e:
            raise TransactionError(f"Erreur lors de la suppression de {self.model.__name__}#{id}: {e}")
    
    def count(self, **filters) -> int:
        """
        Compte le nombre d'objets avec filtres optionnels
        
        Args:
            **filters: Filtres à appliquer
            
        Returns:
            Nombre d'objets
        """
        try:
            query = self.db.query(self.model)
            
            # Appliquer les filtres
            for field, value in filters.items():
                if hasattr(self.model, field) and value is not None:
                    query = query.filter(getattr(self.model, field) == value)
            
            return query.count()
            
        except SQLAlchemyError as e:
            logger.error(f"Erreur lors du comptage de {self.model.__name__}: {e}")
            return 0
    
    def exists(self, id: Any) -> bool:
        """
        Vérifie si un objet existe
        
        Args:
            id: ID de l'objet
            
        Returns:
            True si l'objet existe, False sinon
        """
        try:
            return self.db.query(self.model).filter(self.model.id == id).first() is not None
        except SQLAlchemyError as e:
            logger.error(f"Erreur lors de la vérification d'existence de {self.model.__name__}#{id}: {e}")
            return False
    
    def bulk_create(self, objects_in: List[CreateSchemaType], **kwargs) -> List[ModelType]:
        """
        Crée plusieurs objets en une seule transaction atomique
        
        Args:
            objects_in: Liste des données d'entrée
            **kwargs: Arguments supplémentaires pour tous les objets
            
        Returns:
            Liste des objets créés
            
        Raises:
            TransactionError: En cas d'erreur de transaction
        """
        try:
            with atomic_operation(self.db, f"Création en lot de {len(objects_in)} {self.model.__name__}") as session:
                created_objects = []
                
                for obj_in in objects_in:
                    # Convertir les données Pydantic en dict si nécessaire
                    if hasattr(obj_in, 'dict'):
                        obj_data = obj_in.dict()
                    elif hasattr(obj_in, 'model_dump'):
                        obj_data = obj_in.model_dump()
                    else:
                        obj_data = obj_in
                    
                    # Ajouter les arguments supplémentaires
                    obj_data.update(kwargs)
                    
                    # Créer l'objet
                    db_obj = self.model(**obj_data)
                    session.add(db_obj)
                    created_objects.append(db_obj)
                
                session.flush()
                
                # Rafraîchir tous les objets
                for obj in created_objects:
                    session.refresh(obj)
                
                logger.info(f"{len(created_objects)} {self.model.__name__} créés en lot")
                return created_objects
                
        except TransactionError:
            raise
        except Exception as e:
            raise TransactionError(f"Erreur lors de la création en lot de {self.model.__name__}: {e}")
    
    def validate_before_save(self, obj_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Valide les données avant sauvegarde (à surcharger dans les classes filles)
        
        Args:
            obj_data: Données à valider
            
        Returns:
            Données validées
            
        Raises:
            ValueError: Si la validation échoue
        """
        return obj_data
    
    def after_create(self, db_obj: ModelType) -> None:
        """
        Hook appelé après création (à surcharger dans les classes filles)
        
        Args:
            db_obj: Objet créé
        """
        pass
    
    def after_update(self, db_obj: ModelType) -> None:
        """
        Hook appelé après mise à jour (à surcharger dans les classes filles)
        
        Args:
            db_obj: Objet mis à jour
        """
        pass
    
    def after_delete(self, obj_id: Any) -> None:
        """
        Hook appelé après suppression (à surcharger dans les classes filles)
        
        Args:
            obj_id: ID de l'objet supprimé
        """
        pass
