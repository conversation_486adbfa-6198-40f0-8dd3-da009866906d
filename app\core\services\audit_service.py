from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.core.models.audit import AuditLog, AuditLogPydantic, AuditActionType
from app.core.services.base_service import BaseService

class AuditService(BaseService[AuditLog, AuditLogPydantic, AuditLogPydantic]):
    """Service pour la gestion du journal d'audit"""
    
    def __init__(self, db: Session):
        super().__init__(db, AuditLog)
    
    async def log_action(
        self,
        action: AuditActionType,
        user_id: Optional[int] = None,
        entity_type: Optional[str] = None,
        entity_id: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> AuditLog:
        """
        Enregistre une action dans le journal d'audit
        
        Args:
            action: Type d'action
            user_id: ID de l'utilisateur qui a effectué l'action
            entity_type: Type d'entité concernée
            entity_id: ID de l'entité concernée
            details: Détails supplémentaires
            ip_address: Adresse IP de l'utilisateur
            user_agent: User-Agent du navigateur
            
        Returns:
            L'entrée du journal d'audit créée
        """
        audit_log = AuditLog(
            timestamp=datetime.utcnow(),
            user_id=user_id,
            action=action,
            entity_type=entity_type,
            entity_id=entity_id,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.add(audit_log)
        self.db.commit()
        self.db.refresh(audit_log)
        
        return audit_log
    
    async def get_logs(
        self,
        user_id: Optional[int] = None,
        action: Optional[AuditActionType] = None,
        entity_type: Optional[str] = None,
        entity_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AuditLog]:
        """
        Récupère les entrées du journal d'audit avec filtres
        
        Args:
            user_id: Filtrer par utilisateur
            action: Filtrer par type d'action
            entity_type: Filtrer par type d'entité
            entity_id: Filtrer par ID d'entité
            start_date: Date de début
            end_date: Date de fin
            skip: Nombre d'entrées à sauter
            limit: Nombre maximum d'entrées à retourner
            
        Returns:
            Liste des entrées du journal d'audit
        """
        query = self.db.query(self.model)
        
        if user_id is not None:
            query = query.filter(self.model.user_id == user_id)
        
        if action is not None:
            query = query.filter(self.model.action == action)
        
        if entity_type is not None:
            query = query.filter(self.model.entity_type == entity_type)
        
        if entity_id is not None:
            query = query.filter(self.model.entity_id == entity_id)
        
        if start_date is not None:
            query = query.filter(self.model.timestamp >= start_date)
        
        if end_date is not None:
            query = query.filter(self.model.timestamp <= end_date)
        
        return query.order_by(desc(self.model.timestamp)).offset(skip).limit(limit).all()
    
    async def get_user_activity(self, user_id: int, days: int = 30) -> List[AuditLog]:
        """
        Récupère l'activité récente d'un utilisateur
        
        Args:
            user_id: ID de l'utilisateur
            days: Nombre de jours à considérer
            
        Returns:
            Liste des entrées du journal d'audit pour cet utilisateur
        """
        start_date = datetime.utcnow() - timedelta(days=days)
        
        return self.db.query(self.model).filter(
            self.model.user_id == user_id,
            self.model.timestamp >= start_date
        ).order_by(desc(self.model.timestamp)).all()
    
    async def get_recent_activity(self, limit: int = 50) -> List[AuditLog]:
        """
        Récupère l'activité récente de tous les utilisateurs
        
        Args:
            limit: Nombre maximum d'entrées à retourner
            
        Returns:
            Liste des entrées récentes du journal d'audit
        """
        return self.db.query(self.model).order_by(
            desc(self.model.timestamp)
        ).limit(limit).all()
    
    async def purge_old_logs(self, days: int = 365) -> int:
        """
        Supprime les anciennes entrées du journal d'audit
        
        Args:
            days: Âge en jours des entrées à supprimer
            
        Returns:
            Nombre d'entrées supprimées
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        result = self.db.query(self.model).filter(
            self.model.timestamp < cutoff_date
        ).delete()
        
        self.db.commit()
        
        return result
