from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Dict, Any
import secrets
import string
from sqlalchemy.orm import Session
import pyotp
import qrcode
import io
import base64

from app.core.models.user import User, UserStatus
from app.core.services.user_service import UserService
from app.core.services.audit_service import AuditService
from app.core.models.audit import AuditActionType
from app.utils.security import verify_password, get_password_hash, create_access_token

class AuthService:
    """Service pour l'authentification"""

    def __init__(self, db: Session):
        self.db = db
        self.user_service = UserService(db)
        self.audit_service = AuditService(db)

    async def authenticate_user(
        self,
        email: str,
        password: str,
        totp_code: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """
        Authentifie un utilisateur

        Args:
            email: Email de l'utilisateur
            password: Mot de passe
            totp_code: Code TOTP pour l'authentification à deux facteurs
            ip_address: Adresse IP de l'utilisateur
            user_agent: User-Agent du navigateur

        Returns:
            Tuple (succès, token, informations utilisateur)
        """
        user = await self.user_service.get_user_by_email(email)

        # Vérifier si l'utilisateur existe et si le mot de passe est correct
        if not user or not verify_password(password, user.hashed_password):
            # Enregistrer la tentative échouée
            if user:
                await self.user_service.record_login_attempt(email, False)

                # Journaliser l'action
                await self.audit_service.log_action(
                    action=AuditActionType.LOGIN,
                    user_id=user.id,
                    details={"success": False, "reason": "invalid_credentials"},
                    ip_address=ip_address,
                    user_agent=user_agent
                )

            return False, None, {"error": "Invalid credentials"}

        # Vérifier si l'utilisateur est actif
        if not user.is_active or user.status != UserStatus.ACTIVE:
            # Journaliser l'action
            await self.audit_service.log_action(
                action=AuditActionType.LOGIN,
                user_id=user.id,
                details={"success": False, "reason": "account_inactive"},
                ip_address=ip_address,
                user_agent=user_agent
            )

            return False, None, {"error": "Account is inactive or suspended"}

        # Vérifier l'authentification à deux facteurs si activée
        if user.two_factor_enabled:
            if not totp_code:
                return False, None, {"error": "2FA code required", "require_2fa": True}

            if not self._verify_totp(user.totp_secret, totp_code):
                # Journaliser l'action
                await self.audit_service.log_action(
                    action=AuditActionType.LOGIN,
                    user_id=user.id,
                    details={"success": False, "reason": "invalid_2fa"},
                    ip_address=ip_address,
                    user_agent=user_agent
                )

                return False, None, {"error": "Invalid 2FA code"}

        # Enregistrer la tentative réussie directement dans la base de données
        try:
            # Mettre à jour directement la base de données
            now = datetime.utcnow()

            # Première tentative : mise à jour via query
            updated_rows = self.db.query(User).filter(User.email == email).update({
                "failed_login_attempts": 0,
                "last_login": now
            })
            self.db.commit()
            print(f"Mise à jour de la dernière connexion pour {email} à {now} ({updated_rows} ligne(s) affectée(s))")

            # Vérifier que la mise à jour a bien été effectuée
            user_check = self.db.query(User).filter(User.email == email).first()
            if user_check and user_check.last_login:
                print(f"Vérification: dernière connexion pour {email} mise à jour avec succès: {user_check.last_login}")
            else:
                print(f"Erreur: la dernière connexion pour {email} n'a pas été mise à jour correctement")
                # Tentative de correction avec l'objet user directement
                user.last_login = now
                user.failed_login_attempts = 0
                self.db.add(user)
                self.db.commit()
                print(f"Correction directe effectuée pour {email}")

        except Exception as e:
            print(f"Erreur lors de la mise à jour de la dernière connexion: {e}")
            self.db.rollback()
            # Essayer une approche alternative avec l'objet user
            try:
                now = datetime.utcnow()
                user.last_login = now
                user.failed_login_attempts = 0
                self.db.add(user)
                self.db.commit()
                print(f"Mise à jour alternative réussie pour {email}")
            except Exception as e2:
                print(f"Erreur lors de la mise à jour alternative: {e2}")
                self.db.rollback()

        # Appeler également la méthode originale pour la journalisation
        await self.user_service.record_login_attempt(email, True)

        # Récupérer les permissions de l'utilisateur
        permissions = await self.user_service.get_user_permissions(user.id)

        # Créer le token JWT
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "permissions": permissions
        }

        token = create_access_token(
            data=token_data,
            secret_key="your-secret-key",  # À remplacer par la clé secrète de config
            expires_delta=timedelta(hours=24)
        )

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.LOGIN,
            user_id=user.id,
            details={"success": True},
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Retourner les informations de l'utilisateur
        user_info = {
            "id": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "permissions": permissions
        }

        return True, token, user_info

    async def logout(
        self,
        user_id: int,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        Déconnecte un utilisateur

        Args:
            user_id: ID de l'utilisateur
            ip_address: Adresse IP de l'utilisateur
            user_agent: User-Agent du navigateur

        Returns:
            True si la déconnexion a réussi, False sinon
        """
        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.LOGOUT,
            user_id=user_id,
            details={"success": True},
            ip_address=ip_address,
            user_agent=user_agent
        )

        return True

    async def setup_two_factor(self, user_id: int) -> Dict[str, Any]:
        """
        Configure l'authentification à deux facteurs pour un utilisateur

        Args:
            user_id: ID de l'utilisateur

        Returns:
            Informations pour configurer l'authentification à deux facteurs
        """
        user = await self.user_service.get_user_by_id(user_id)
        if not user:
            raise ValueError("User not found")

        # Générer une clé secrète TOTP
        totp_secret = pyotp.random_base32()

        # Créer l'URI pour le QR code
        totp = pyotp.TOTP(totp_secret)
        uri = totp.provisioning_uri(user.email, issuer_name="YourApp")

        # Générer le QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(uri)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # Convertir l'image en base64
        buffer = io.BytesIO()
        img.save(buffer, format="PNG")
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")

        # Mettre à jour l'utilisateur avec la clé secrète
        user.totp_secret = totp_secret
        self.db.commit()

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.UPDATE,
            user_id=user_id,
            entity_type="user",
            entity_id=user_id,
            details={"action": "setup_2fa"}
        )

        return {
            "secret": totp_secret,
            "qr_code": qr_code_base64,
            "uri": uri
        }

    async def verify_two_factor(self, user_id: int, totp_code: str) -> bool:
        """
        Vérifie un code TOTP pour l'authentification à deux facteurs

        Args:
            user_id: ID de l'utilisateur
            totp_code: Code TOTP

        Returns:
            True si le code est valide, False sinon
        """
        user = await self.user_service.get_user_by_id(user_id)
        if not user or not user.totp_secret:
            return False

        return self._verify_totp(user.totp_secret, totp_code)

    async def enable_two_factor(self, user_id: int, totp_code: str) -> bool:
        """
        Active l'authentification à deux facteurs pour un utilisateur

        Args:
            user_id: ID de l'utilisateur
            totp_code: Code TOTP pour vérification

        Returns:
            True si l'activation a réussi, False sinon
        """
        user = await self.user_service.get_user_by_id(user_id)
        if not user or not user.totp_secret:
            return False

        # Vérifier le code TOTP
        if not self._verify_totp(user.totp_secret, totp_code):
            return False

        # Activer l'authentification à deux facteurs
        user.two_factor_enabled = True
        self.db.commit()

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.UPDATE,
            user_id=user_id,
            entity_type="user",
            entity_id=user_id,
            details={"action": "enable_2fa"}
        )

        return True

    async def disable_two_factor(self, user_id: int, password: str) -> bool:
        """
        Désactive l'authentification à deux facteurs pour un utilisateur

        Args:
            user_id: ID de l'utilisateur
            password: Mot de passe pour vérification

        Returns:
            True si la désactivation a réussi, False sinon
        """
        user = await self.user_service.get_user_by_id(user_id)
        if not user:
            return False

        # Vérifier le mot de passe
        if not verify_password(password, user.hashed_password):
            return False

        # Désactiver l'authentification à deux facteurs
        user.two_factor_enabled = False
        user.totp_secret = None
        self.db.commit()

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.UPDATE,
            user_id=user_id,
            entity_type="user",
            entity_id=user_id,
            details={"action": "disable_2fa"}
        )

        return True

    async def request_password_reset(self, email: str) -> Optional[str]:
        """
        Demande une réinitialisation de mot de passe

        Args:
            email: Email de l'utilisateur

        Returns:
            Token de réinitialisation ou None si l'utilisateur n'existe pas
        """
        return await self.user_service.request_password_reset(email)

    async def reset_password(self, token: str, new_password: str, confirm_password: str) -> bool:
        """
        Réinitialise le mot de passe avec un token

        Args:
            token: Token de réinitialisation
            new_password: Nouveau mot de passe
            confirm_password: Confirmation du nouveau mot de passe

        Returns:
            True si la réinitialisation a réussi, False sinon
        """
        if new_password != confirm_password:
            raise ValueError("Passwords do not match")

        # Rechercher l'utilisateur avec ce token
        user = self.db.query(User).filter(
            User.password_reset_token == token,
            User.password_reset_expires > datetime.now(),
            User.is_active == True
        ).first()

        if not user:
            raise ValueError("Invalid or expired token")

        # Réinitialiser le mot de passe
        user.hashed_password = get_password_hash(new_password)
        user.password_reset_token = None
        user.password_reset_expires = None
        user.failed_login_attempts = 0
        self.db.commit()

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.PASSWORD_RESET,
            user_id=user.id,
            entity_type="user",
            entity_id=user.id,
            details={"action": "reset"}
        )

        return True

    def _verify_totp(self, secret: str, code: str) -> bool:
        """
        Vérifie un code TOTP

        Args:
            secret: Clé secrète TOTP
            code: Code TOTP

        Returns:
            True si le code est valide, False sinon
        """
        totp = pyotp.TOTP(secret)
        return totp.verify(code)



