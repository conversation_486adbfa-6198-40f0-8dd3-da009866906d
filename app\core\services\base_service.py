from typing import TypeVar, Generic, Type, Optional, List
from sqlalchemy.orm import Session
from pydantic import BaseModel
from ..models.base import BaseDBModel

ModelType = TypeVar("ModelType", bound=BaseDBModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)

class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, db: Session, model: Type[ModelType]):
        self.db = db
        self.model = model
        print(f"BaseService initialisé avec modèle: {model.__name__}")

    async def get(self, id: int) -> Optional[ModelType]:
        return self.db.query(self.model).filter(self.model.id == id).first()

    async def get_all(self, skip: int = 0, limit: int = 100, joined_relations: list = None) -> List[ModelType]:
        """Récupère tous les éléments (version asynchrone), avec possibilité de jointure sur des relations."""
        return self.get_all_sync(skip, limit, joined_relations=joined_relations)

    def get_all_sync(self, skip: int = 0, limit: int = 100, joined_relations: list = None) -> List[ModelType]:
        """Récupère tous les éléments (version synchrone), avec possibilité de jointure sur des relations."""
        query = self.db.query(self.model)
        if joined_relations:
            from sqlalchemy.orm import joinedload
            for rel in joined_relations:
                query = query.options(joinedload(rel))
        return query.offset(skip).limit(limit).all()

    async def create(self, schema: CreateSchemaType) -> ModelType:
        # Handle both Pydantic v1 and v2 models
        try:
            # Try Pydantic v2 method first
            data = schema.model_dump(exclude_unset=True)
        except AttributeError:
            try:
                # Fall back to Pydantic v1 method
                data = schema.dict(exclude_unset=True)
            except AttributeError:
                # If all else fails, convert to dict directly
                data = dict(schema)

        # Supprimer l'ID s'il est None ou 0 pour laisser la base de données le générer
        if 'id' in data and (data['id'] is None or data['id'] == 0):
            del data['id']

        print(f"Données pour la création: {data}")

        db_item = self.model(**data)
        self.db.add(db_item)
        self.db.commit()
        self.db.refresh(db_item)
        return db_item

    async def update(self, id: int, schema: UpdateSchemaType) -> Optional[ModelType]:
        db_item = await self.get(id)
        if db_item:
            # Handle both Pydantic v1 and v2 models
            try:
                # Try Pydantic v2 method first
                update_data = schema.model_dump(exclude_unset=True)
            except AttributeError:
                try:
                    # Fall back to Pydantic v1 method
                    update_data = schema.dict(exclude_unset=True)
                except AttributeError:
                    # If all else fails, convert to dict directly
                    update_data = dict(schema)

            for key, value in update_data.items():
                setattr(db_item, key, value)
            self.db.commit()
            self.db.refresh(db_item)
        return db_item

    async def delete(self, id: int) -> bool:
        db_item = await self.get(id)
        if db_item:
            self.db.delete(db_item)
            self.db.commit()
            return True
        return False