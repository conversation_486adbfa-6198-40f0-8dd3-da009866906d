"""
Service pour la gestion intelligente des marques et modèles
Système d'auto-complétion avec apprentissage automatique
"""

import re
from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, or_, and_, desc
from ..models.brand_model_knowledge import (
    Brand, DeviceModel, BrandModelSuggestion,
    BrandCreate, DeviceModelCreate, AutoCompleteSuggestion, BrandModelPair
)
from .base_service import BaseService
from datetime import datetime
import unicodedata


class BrandModelService(BaseService):
    """Service pour la gestion intelligente des marques et modèles"""
    
    def __init__(self, db: Session):
        super().__init__(db, Brand)
        self.db = db
    
    def normalize_text(self, text: str) -> str:
        """Normalise le texte pour la recherche (supprime accents, minuscules, etc.)"""
        if not text:
            return ""
        
        # Supprimer les accents
        text = unicodedata.normalize('NFD', text)
        text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')
        
        # Minuscules et suppression des caractères spéciaux
        text = re.sub(r'[^\w\s]', '', text.lower())
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def extract_brand_model(self, input_text: str) -> Tuple[str, str]:
        """
        Extrait la marque et le modèle d'un texte d'entrée
        Utilise des heuristiques et la base de connaissances
        """
        if not input_text:
            return "", ""
        
        normalized = self.normalize_text(input_text)
        words = normalized.split()
        
        if len(words) == 1:
            # Un seul mot : probablement une marque
            return words[0], ""
        
        # Rechercher des marques connues dans le texte
        known_brands = self.get_known_brands()
        
        for brand in known_brands:
            brand_normalized = self.normalize_text(brand.name)
            if brand_normalized in normalized:
                # Marque trouvée, extraire le modèle
                brand_part = brand_normalized
                model_part = normalized.replace(brand_part, "").strip()
                return brand.name, model_part
        
        # Heuristique : premier mot = marque, reste = modèle
        return words[0], " ".join(words[1:]) if len(words) > 1 else ""
    
    def get_known_brands(self) -> List[Brand]:
        """Récupère toutes les marques connues triées par usage"""
        return self.db.query(Brand).order_by(desc(Brand.usage_count)).all()
    
    def get_models_for_brand(self, brand_id: int) -> List[DeviceModel]:
        """Récupère tous les modèles pour une marque donnée"""
        return self.db.query(DeviceModel).filter(
            DeviceModel.brand_id == brand_id
        ).order_by(desc(DeviceModel.usage_count)).all()
    
    def search_brands(self, query: str, limit: int = 10) -> List[AutoCompleteSuggestion]:
        """Recherche de marques avec auto-complétion"""
        if not query:
            return []
        
        normalized_query = self.normalize_text(query)
        
        brands = self.db.query(Brand).filter(
            or_(
                Brand.normalized_name.like(f"{normalized_query}%"),
                Brand.normalized_name.contains(normalized_query)
            )
        ).order_by(desc(Brand.usage_count)).limit(limit).all()
        
        suggestions = []
        for brand in brands:
            suggestions.append(AutoCompleteSuggestion(
                text=brand.name,
                type="brand",
                brand_id=brand.id,
                score=brand.usage_count,
                usage_count=brand.usage_count
            ))
        
        return suggestions
    
    def search_models(self, brand_id: int, query: str, limit: int = 10) -> List[AutoCompleteSuggestion]:
        """Recherche de modèles pour une marque avec auto-complétion"""
        if not query:
            # Retourner les modèles les plus utilisés pour cette marque
            models = self.get_models_for_brand(brand_id)[:limit]
        else:
            normalized_query = self.normalize_text(query)
            models = self.db.query(DeviceModel).filter(
                and_(
                    DeviceModel.brand_id == brand_id,
                    or_(
                        DeviceModel.normalized_name.like(f"{normalized_query}%"),
                        DeviceModel.normalized_name.contains(normalized_query)
                    )
                )
            ).order_by(desc(DeviceModel.usage_count)).limit(limit).all()
        
        suggestions = []
        for model in models:
            suggestions.append(AutoCompleteSuggestion(
                text=model.name,
                type="model",
                brand_id=brand_id,
                model_id=model.id,
                score=model.usage_count,
                usage_count=model.usage_count
            ))
        
        return suggestions
    
    def learn_from_input(self, brand_name: str, model_name: str) -> BrandModelPair:
        """
        Apprend d'une nouvelle saisie utilisateur
        Crée ou met à jour les entrées dans la base de connaissances
        """
        if not brand_name:
            raise ValueError("Le nom de la marque est requis")
        
        # Normaliser les entrées
        brand_normalized = self.normalize_text(brand_name)
        model_normalized = self.normalize_text(model_name) if model_name else ""
        
        # Gérer la marque
        brand = self.db.query(Brand).filter(
            Brand.normalized_name == brand_normalized
        ).first()
        
        if brand:
            # Marque existante : incrémenter l'usage
            brand.usage_count += 1
            brand.last_used = datetime.now()
        else:
            # Nouvelle marque
            brand = Brand(
                name=brand_name.strip(),
                normalized_name=brand_normalized,
                usage_count=1,
                last_used=datetime.now()
            )
            self.db.add(brand)
            self.db.flush()  # Pour obtenir l'ID
        
        # Gérer le modèle si fourni
        model = None
        if model_name and model_name.strip():
            model = self.db.query(DeviceModel).filter(
                and_(
                    DeviceModel.brand_id == brand.id,
                    DeviceModel.normalized_name == model_normalized
                )
            ).first()
            
            if model:
                # Modèle existant : incrémenter l'usage
                model.usage_count += 1
                model.last_used = datetime.now()
            else:
                # Nouveau modèle
                full_name = f"{brand_name} {model_name}".strip()
                model = DeviceModel(
                    brand_id=brand.id,
                    name=model_name.strip(),
                    normalized_name=model_normalized,
                    full_name=full_name,
                    usage_count=1,
                    last_used=datetime.now()
                )
                self.db.add(model)
        
        self.db.commit()
        
        return BrandModelPair(
            brand=brand.name,
            model=model.name if model else "",
            brand_id=brand.id,
            model_id=model.id if model else None,
            confidence=1.0
        )
    
    def get_smart_suggestions(self, partial_input: str, limit: int = 10) -> List[AutoCompleteSuggestion]:
        """
        Suggestions intelligentes basées sur l'entrée partielle
        Combine recherche de marques et de modèles
        """
        if not partial_input:
            # Retourner les marques les plus populaires
            return self.search_brands("", limit)
        
        suggestions = []
        
        # Essayer d'extraire marque et modèle
        potential_brand, potential_model = self.extract_brand_model(partial_input)
        
        if potential_model:
            # L'utilisateur semble saisir un modèle
            # Chercher la marque correspondante
            brand_suggestions = self.search_brands(potential_brand, 5)
            if brand_suggestions:
                brand_id = brand_suggestions[0].brand_id
                model_suggestions = self.search_models(brand_id, potential_model, limit)
                suggestions.extend(model_suggestions)
        
        # Ajouter des suggestions de marques
        brand_suggestions = self.search_brands(partial_input, limit - len(suggestions))
        suggestions.extend(brand_suggestions)
        
        # Trier par score de pertinence
        suggestions.sort(key=lambda x: x.score, reverse=True)
        
        return suggestions[:limit]
    
    def validate_brand_model_pair(self, brand_name: str, model_name: str) -> BrandModelPair:
        """
        Valide et normalise une paire marque-modèle
        Retourne la paire avec un score de confiance
        """
        if not brand_name:
            return BrandModelPair(brand="", model="", confidence=0.0)
        
        # Rechercher des correspondances exactes ou proches
        brand_suggestions = self.search_brands(brand_name, 1)
        
        if brand_suggestions:
            brand = brand_suggestions[0]
            confidence = 0.9  # Haute confiance pour marque connue
            
            if model_name:
                model_suggestions = self.search_models(brand.brand_id, model_name, 1)
                if model_suggestions:
                    model = model_suggestions[0]
                    return BrandModelPair(
                        brand=brand.text,
                        model=model.text,
                        brand_id=brand.brand_id,
                        model_id=model.model_id,
                        confidence=0.95  # Très haute confiance
                    )
                else:
                    return BrandModelPair(
                        brand=brand.text,
                        model=model_name,
                        brand_id=brand.brand_id,
                        confidence=0.7  # Marque connue, modèle nouveau
                    )
            else:
                return BrandModelPair(
                    brand=brand.text,
                    model="",
                    brand_id=brand.brand_id,
                    confidence=confidence
                )
        else:
            # Marque inconnue
            return BrandModelPair(
                brand=brand_name,
                model=model_name or "",
                confidence=0.3  # Faible confiance
            )
