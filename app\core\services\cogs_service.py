"""
Service pour la gestion du coût des marchandises vendues (COGS - Cost of Goods Sold).
Ce service fait le lien entre les achats et les ventes pour suivre les coûts et calculer les marges.
"""
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_

from app.core.models.inventory import InventoryItem
from app.core.models.sale import Sale, SaleItem, SaleStatus
from app.core.models.purchasing import PurchaseOrder, PurchaseOrderItem, OrderStatus
from app.core.services.inventory_service import InventoryService
from app.core.services.sale_service import SaleService
from app.core.services.purchasing_service import PurchasingService
from app.utils.database import SessionLocal

class COGSService:
    """Service pour la gestion du coût des marchandises vendues"""

    def __init__(self, db: Session = None):
        """Initialise le service avec une session de base de données"""
        if db is None:
            db = SessionLocal()
        self.db = db
        self.inventory_service = InventoryService(db)
        self.sale_service = SaleService(db)
        self.purchasing_service = PurchasingService(db)

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    async def calculate_sale_margins(self, sale_id: int) -> Dict[str, Any]:
        """
        Calcule les marges pour une vente spécifique

        Args:
            sale_id: ID de la vente

        Returns:
            Dictionnaire contenant les informations de marge
        """
        # Récupérer la vente
        sale = await self.sale_service.get(sale_id)
        if not sale:
            raise ValueError(f"Vente avec ID {sale_id} non trouvée")

        # Initialiser les totaux
        total_cost = 0.0
        total_revenue = sale.final_amount

        # Calculer le coût total des articles vendus
        for item in sale.items:
            # Récupérer le produit
            product = await self.inventory_service.get(item.product_id)
            if not product:
                continue

            # Calculer le coût de l'article
            item_cost = product.purchase_price * item.quantity
            total_cost += item_cost

        # Calculer la marge brute
        gross_margin = total_revenue - total_cost

        # Calculer le pourcentage de marge
        margin_percent = (gross_margin / total_revenue * 100) if total_revenue > 0 else 0

        return {
            "sale_id": sale_id,
            "sale_number": sale.number,
            "date": sale.date,
            "total_revenue": total_revenue,
            "total_cost": total_cost,
            "gross_margin": gross_margin,
            "margin_percent": margin_percent
        }

    async def calculate_product_margins(self, product_id: int, period_days: int = 30) -> Dict[str, Any]:
        """
        Calcule les marges pour un produit spécifique sur une période donnée

        Args:
            product_id: ID du produit
            period_days: Nombre de jours à considérer (par défaut 30 jours)

        Returns:
            Dictionnaire contenant les informations de marge
        """
        # Récupérer le produit
        product = await self.inventory_service.get(product_id)
        if not product:
            raise ValueError(f"Produit avec ID {product_id} non trouvé")

        # Calculer la date de début de la période
        start_date = datetime.now() - timedelta(days=period_days)

        # Récupérer les ventes du produit sur la période
        sales_items = (
            self.db.query(SaleItem)
            .join(Sale)
            .filter(
                SaleItem.product_id == product_id,
                Sale.date >= start_date,
                Sale.status == SaleStatus.COMPLETED
            )
            .all()
        )

        # Initialiser les totaux
        total_quantity_sold = 0
        total_revenue = 0.0

        # Calculer les totaux
        for item in sales_items:
            total_quantity_sold += item.quantity
            total_revenue += item.total_amount

        # Calculer le coût total
        total_cost = product.purchase_price * total_quantity_sold

        # Calculer la marge brute
        gross_margin = total_revenue - total_cost

        # Calculer le pourcentage de marge
        margin_percent = (gross_margin / total_revenue * 100) if total_revenue > 0 else 0

        return {
            "product_id": product_id,
            "product_name": product.name,
            "period_days": period_days,
            "total_quantity_sold": total_quantity_sold,
            "total_revenue": total_revenue,
            "total_cost": total_cost,
            "gross_margin": gross_margin,
            "margin_percent": margin_percent,
            "current_purchase_price": product.purchase_price,
            "current_selling_price": product.unit_price,
            "current_margin_percent": product.margin_percent
        }

    async def get_product_purchase_history(self, product_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Récupère l'historique des achats d'un produit

        Args:
            product_id: ID du produit
            limit: Nombre maximum d'entrées à récupérer

        Returns:
            Liste des achats du produit
        """
        # Récupérer le produit
        product = await self.inventory_service.get(product_id)
        if not product:
            raise ValueError(f"Produit avec ID {product_id} non trouvé")

        # Récupérer les articles de commande pour ce produit
        order_items = (
            self.db.query(PurchaseOrderItem)
            .join(PurchaseOrder)
            .filter(
                PurchaseOrderItem.product_id == product_id,
                PurchaseOrder.status.in_([OrderStatus.COMPLETED, OrderStatus.PARTIALLY_RECEIVED])
            )
            .order_by(PurchaseOrder.order_date.desc())
            .limit(limit)
            .all()
        )

        # Préparer les résultats
        results = []
        for item in order_items:
            order = item.purchase_order
            results.append({
                "order_id": order.id,
                "order_number": order.po_number,
                "order_date": order.order_date,
                "received_date": item.received_at,
                "supplier_id": order.supplier_id,
                "supplier_name": order.supplier.name if order.supplier else "N/A",
                "quantity": item.quantity,
                "received_quantity": item.received_quantity,
                "unit_price": item.unit_price,
                "total_price": item.total_price
            })

        return results

    async def get_product_sales_history(self, product_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Récupère l'historique des ventes d'un produit

        Args:
            product_id: ID du produit
            limit: Nombre maximum d'entrées à récupérer

        Returns:
            Liste des ventes du produit
        """
        # Récupérer le produit
        product = await self.inventory_service.get(product_id)
        if not product:
            raise ValueError(f"Produit avec ID {product_id} non trouvé")

        # Récupérer les articles de vente pour ce produit
        sale_items = (
            self.db.query(SaleItem)
            .join(Sale)
            .filter(
                SaleItem.product_id == product_id,
                Sale.status == SaleStatus.COMPLETED
            )
            .order_by(Sale.date.desc())
            .limit(limit)
            .all()
        )

        # Préparer les résultats
        results = []
        for item in sale_items:
            sale = item.sale
            results.append({
                "sale_id": sale.id,
                "sale_number": sale.number,
                "sale_date": sale.date,
                "customer_id": sale.customer_id,
                "customer_name": sale.customer.name if sale.customer else "N/A",
                "quantity": item.quantity,
                "unit_price": item.unit_price,
                "discount_percent": item.discount_percent,
                "discount_amount": item.discount_amount,
                "tax_percent": item.tax_percent,
                "tax_amount": item.tax_amount,
                "total_amount": item.total_amount
            })

        return results

    async def calculate_overall_margins(self, period_days: int = 30) -> Dict[str, Any]:
        """
        Calcule les marges globales sur une période donnée (version asynchrone)

        Args:
            period_days: Nombre de jours à considérer (par défaut 30 jours)

        Returns:
            Dictionnaire contenant les informations de marge
        """
        return self.calculate_overall_margins_sync(period_days)

    def calculate_overall_margins_sync(self, period_days: int = 30) -> Dict[str, Any]:
        """
        Calcule les marges globales sur une période donnée (version synchrone)

        Args:
            period_days: Nombre de jours à considérer (par défaut 30 jours)

        Returns:
            Dictionnaire contenant les informations de marge
        """
        try:
            # Calculer la date de début de la période
            start_date = datetime.now() - timedelta(days=period_days)

            # Récupérer les ventes complétées sur la période
            try:
                sales = (
                    self.db.query(Sale)
                    .filter(
                        Sale.date >= start_date,
                        Sale.status == SaleStatus.COMPLETED
                    )
                    .all()
                )

                # Initialiser les totaux
                total_sales = len(sales)
                total_revenue = sum(sale.final_amount for sale in sales)
                total_cost = 0.0

                # Calculer le coût total des articles vendus
                for sale in sales:
                    for item in sale.items:
                        # Récupérer le produit
                        product = self.db.query(InventoryItem).get(item.product_id)
                        if not product:
                            continue

                        # Calculer le coût de l'article
                        item_cost = product.purchase_price * item.quantity
                        total_cost += item_cost

                # Calculer la marge brute
                gross_margin = total_revenue - total_cost

                # Calculer le pourcentage de marge
                margin_percent = (gross_margin / total_revenue * 100) if total_revenue > 0 else 0

                return {
                    "period_days": period_days,
                    "start_date": start_date,
                    "end_date": datetime.now(),
                    "total_sales": total_sales,
                    "total_revenue": total_revenue,
                    "total_cost": total_cost,
                    "gross_margin": gross_margin,
                    "margin_percent": margin_percent
                }

            except Exception as e:
                print(f"Erreur lors du calcul des marges globales: {e}")
                # En cas d'erreur, retourner des données vides
                return self._create_empty_margins(period_days)

        except Exception as e:
            print(f"Erreur générale lors du calcul des marges globales: {e}")
            import traceback
            traceback.print_exc()

            # En cas d'erreur, retourner des données vides
            return self._create_empty_margins(period_days)

    def _create_empty_margins(self, period_days: int) -> Dict[str, Any]:
        """
        Crée un dictionnaire de marges vide

        Args:
            period_days: Nombre de jours de la période

        Returns:
            Dictionnaire contenant des informations de marge vides
        """
        return {
            "period_days": period_days,
            "start_date": datetime.now() - timedelta(days=period_days),
            "end_date": datetime.now(),
            "total_sales": 0,
            "total_revenue": 0.0,
            "total_cost": 0.0,
            "gross_margin": 0.0,
            "margin_percent": 0.0
        }

    async def get_top_margin_products(self, limit: int = 10, period_days: int = 30) -> List[Dict[str, Any]]:
        """
        Récupère les produits avec les meilleures marges sur une période donnée (version asynchrone)

        Args:
            limit: Nombre maximum de produits à récupérer
            period_days: Nombre de jours à considérer (par défaut 30 jours)

        Returns:
            Liste des produits avec les meilleures marges
        """
        return self.get_top_margin_products_sync(limit, period_days)

    def get_top_margin_products_sync(self, limit: int = 10, period_days: int = 30) -> List[Dict[str, Any]]:
        """
        Récupère les produits avec les meilleures marges sur une période donnée (version synchrone)

        Args:
            limit: Nombre maximum de produits à récupérer
            period_days: Nombre de jours à considérer (par défaut 30 jours)

        Returns:
            Liste des produits avec les meilleures marges
        """
        try:
            # Calculer la date de début de la période
            start_date = datetime.now() - timedelta(days=period_days)

            # Récupérer les produits vendus sur la période
            try:
                # Récupérer tous les articles vendus sur la période
                sale_items = (
                    self.db.query(
                        SaleItem.product_id,
                        func.sum(SaleItem.quantity).label('total_quantity'),
                        func.sum(SaleItem.total_amount).label('total_revenue')
                    )
                    .join(Sale)
                    .filter(
                        Sale.date >= start_date,
                        Sale.status == SaleStatus.COMPLETED
                    )
                    .group_by(SaleItem.product_id)
                    .all()
                )

                # Préparer les résultats
                results = []
                for item in sale_items:
                    # Récupérer le produit
                    product = self.db.query(InventoryItem).get(item.product_id)
                    if not product:
                        continue

                    # Calculer le coût total
                    total_cost = product.purchase_price * item.total_quantity

                    # Calculer la marge brute
                    gross_margin = item.total_revenue - total_cost

                    # Calculer le pourcentage de marge
                    margin_percent = (gross_margin / item.total_revenue * 100) if item.total_revenue > 0 else 0

                    results.append({
                        "product_id": item.product_id,
                        "product_name": product.name,
                        "total_quantity_sold": item.total_quantity,
                        "total_revenue": item.total_revenue,
                        "total_cost": total_cost,
                        "gross_margin": gross_margin,
                        "margin_percent": margin_percent
                    })

                # Trier les résultats par pourcentage de marge décroissant
                results.sort(key=lambda x: x["margin_percent"], reverse=True)

                # Limiter le nombre de résultats
                return results[:limit]

            except Exception as e:
                print(f"Erreur lors de la récupération des produits avec les meilleures marges: {e}")
                # En cas d'erreur, retourner une liste vide
                return []

        except Exception as e:
            print(f"Erreur générale lors de la récupération des produits avec les meilleures marges: {e}")
            import traceback
            traceback.print_exc()

            # En cas d'erreur, retourner une liste vide
            return []

    async def get_low_margin_products(self, threshold: float = 10.0, period_days: int = 30) -> List[Dict[str, Any]]:
        """
        Récupère les produits avec des marges inférieures à un seuil donné (version asynchrone)

        Args:
            threshold: Seuil de marge en pourcentage
            period_days: Nombre de jours à considérer (par défaut 30 jours)

        Returns:
            Liste des produits avec des marges faibles
        """
        return self.get_low_margin_products_sync(threshold, period_days)

    def get_low_margin_products_sync(self, threshold: float = 10.0, period_days: int = 30) -> List[Dict[str, Any]]:
        """
        Récupère les produits avec des marges inférieures à un seuil donné (version synchrone)

        Args:
            threshold: Seuil de marge en pourcentage
            period_days: Nombre de jours à considérer (par défaut 30 jours)

        Returns:
            Liste des produits avec des marges faibles
        """
        try:
            # Calculer la date de début de la période
            start_date = datetime.now() - timedelta(days=period_days)

            # Récupérer les produits vendus sur la période
            try:
                # Récupérer tous les articles vendus sur la période
                sale_items = (
                    self.db.query(
                        SaleItem.product_id,
                        func.sum(SaleItem.quantity).label('total_quantity'),
                        func.sum(SaleItem.total_amount).label('total_revenue')
                    )
                    .join(Sale)
                    .filter(
                        Sale.date >= start_date,
                        Sale.status == SaleStatus.COMPLETED
                    )
                    .group_by(SaleItem.product_id)
                    .all()
                )

                # Préparer les résultats
                results = []
                for item in sale_items:
                    # Récupérer le produit
                    product = self.db.query(InventoryItem).get(item.product_id)
                    if not product:
                        continue

                    # Calculer le coût total
                    total_cost = product.purchase_price * item.total_quantity

                    # Calculer la marge brute
                    gross_margin = item.total_revenue - total_cost

                    # Calculer le pourcentage de marge
                    margin_percent = (gross_margin / item.total_revenue * 100) if item.total_revenue > 0 else 0

                    # Ajouter le produit s'il a une marge inférieure au seuil
                    if margin_percent <= threshold:
                        results.append({
                            "product_id": item.product_id,
                            "product_name": product.name,
                            "total_quantity_sold": item.total_quantity,
                            "total_revenue": item.total_revenue,
                            "total_cost": total_cost,
                            "gross_margin": gross_margin,
                            "margin_percent": margin_percent
                        })

                # Trier les résultats par pourcentage de marge croissant
                results.sort(key=lambda x: x["margin_percent"])

                return results

            except Exception as e:
                print(f"Erreur lors de la récupération des produits avec des marges faibles: {e}")
                # En cas d'erreur, retourner une liste vide
                return []

        except Exception as e:
            print(f"Erreur générale lors de la récupération des produits avec des marges faibles: {e}")
            import traceback
            traceback.print_exc()

            # En cas d'erreur, retourner une liste vide
            return []
