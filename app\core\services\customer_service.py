from typing import List, Optional, Dict, Any
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, and_, or_
from ..models.customer import Customer, CustomerPydantic, CustomerTransaction, CustomerTransactionPydantic
from ..models.repair import RepairOrder, PaymentStatus as RepairPaymentStatus
from ..models.sale import Sale, PaymentStatus as SalePaymentStatus
from .base_service import BaseService

class CustomerService(BaseService[Customer, CustomerPydantic, CustomerPydantic]):
    """Service pour la gestion des clients"""

    def __init__(self, db: Session = None):
        """Initialise le service avec une session de base de données"""
        from app.utils.database import SessionLocal
        if db is None:
            db = SessionLocal()
            print("CustomerService: Nouvelle session de base de données créée")
        else:
            print("CustomerService: Session de base de données existante utilisée")
        super().__init__(db, Customer)

    async def search_customers(self, query: str, limit: int = 50) -> List[Customer]:
        """Recherche des clients par nom, contact, email ou téléphone"""
        search = f"%{query}%"
        return (
            self.db.query(self.model)
            .filter(
                (self.model.name.ilike(search)) |
                (self.model.contact_person.ilike(search)) |
                (self.model.email.ilike(search)) |
                (self.model.phone.ilike(search))
            )
            .filter(self.model.active == True)
            .limit(limit)
            .all()
        )

    async def get_active_customers(self, skip: int = 0, limit: int = 100) -> List[Customer]:
        """Récupère tous les clients actifs"""
        try:
            result = (
                self.db.query(self.model)
                .filter(self.model.active == True)
                .offset(skip)
                .limit(limit)
                .all()
            )
            print(f"Service client: {len(result)} clients actifs trouvés")
            return result
        except Exception as e:
            print(f"Erreur dans get_active_customers: {e}")
            return []

    async def deactivate_customer(self, customer_id: int) -> bool:
        """Désactive un client au lieu de le supprimer"""
        customer = await self.get(customer_id)
        if not customer:
            return False

        customer.active = False
        self.db.commit()
        return True

    async def get_customer_credit_status(self, customer_id: int) -> Dict[str, Any]:
        """Récupère le statut de crédit d'un client"""
        customer = await self.get(customer_id)
        if not customer:
            return {
                "credit_limit": 0.0,
                "current_balance": 0.0,
                "available_credit": 0.0,
                "overdue_amount": 0.0,
                "pending_amount": 0.0
            }

        # Calculer le montant en retard des réparations
        repair_overdue_amount = self.db.query(func.sum(RepairOrder.final_amount - RepairOrder.total_paid))\
            .filter(
                RepairOrder.customer_id == customer_id,
                RepairOrder.payment_status == RepairPaymentStatus.OVERDUE
            ).scalar() or 0.0

        # Calculer le montant en attente des réparations
        repair_pending_amount = self.db.query(func.sum(RepairOrder.final_amount - RepairOrder.total_paid))\
            .filter(
                RepairOrder.customer_id == customer_id,
                RepairOrder.payment_status.in_([RepairPaymentStatus.PENDING, RepairPaymentStatus.PARTIAL])
            ).scalar() or 0.0

        # Calculer le montant en retard des ventes
        sale_overdue_amount = self.db.query(func.sum(Sale.final_amount - Sale.total_paid))\
            .filter(
                Sale.customer_id == customer_id,
                Sale.payment_status == SalePaymentStatus.OVERDUE
            ).scalar() or 0.0

        # Calculer le montant en attente des ventes
        sale_pending_amount = self.db.query(func.sum(Sale.final_amount - Sale.total_paid))\
            .filter(
                Sale.customer_id == customer_id,
                Sale.payment_status.in_([SalePaymentStatus.PENDING, SalePaymentStatus.PARTIAL])
            ).scalar() or 0.0

        # Calculer les montants totaux
        overdue_amount = repair_overdue_amount + sale_overdue_amount
        pending_amount = repair_pending_amount + sale_pending_amount

        # Calculer le crédit disponible
        available_credit = max(0.0, customer.credit_limit - customer.current_balance)

        return {
            "credit_limit": customer.credit_limit,
            "current_balance": customer.current_balance,
            "available_credit": available_credit,
            "overdue_amount": overdue_amount,
            "pending_amount": pending_amount
        }

    async def update_customer_balance(self, customer_id: int, amount: float) -> bool:
        """Met à jour le solde d'un client"""
        customer = await self.get(customer_id)
        if not customer:
            return False

        customer.current_balance += amount
        self.db.commit()
        return True

    async def record_transaction(self, transaction_data: CustomerTransactionPydantic) -> CustomerTransaction:
        """
        Enregistre une transaction pour un client via FinanceService.
        DEPRECATED: Utilisez directement FinanceService.record_customer_transaction() pour les nouveaux développements.
        """
        # Import différé pour éviter la dépendance circulaire
        from .finance_service import FinanceService

        finance_service = FinanceService(self.db)

        # Utiliser FinanceService pour enregistrer la transaction avec cohérence
        transaction = await finance_service.record_customer_transaction(
            customer_id=transaction_data.customer_id,
            amount=transaction_data.amount,
            description=transaction_data.description or "Transaction client",
            transaction_type=transaction_data.transaction_type,
            reference_number=transaction_data.reference_number,
            repair_order_id=transaction_data.repair_order_id,
            sale_id=transaction_data.sale_id,
            processed_by=transaction_data.processed_by or 1,
            transaction_date=transaction_data.transaction_date,
            # Pas d'écriture trésorerie automatique ici pour maintenir la compatibilité
        )

        return transaction

    async def get_customer_transactions(self, customer_id: int, skip: int = 0, limit: int = 100) -> List[CustomerTransaction]:
        """Récupère les transactions d'un client"""
        return (
            self.db.query(CustomerTransaction)
            .filter(CustomerTransaction.customer_id == customer_id)
            .order_by(desc(CustomerTransaction.transaction_date))
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def get_all_transactions(self, skip: int = 0, limit: int = 1000) -> List[CustomerTransaction]:
        """Récupère toutes les transactions"""
        return (
            self.db.query(CustomerTransaction)
            .order_by(desc(CustomerTransaction.transaction_date))
            .options(
                joinedload(CustomerTransaction.customer),
                joinedload(CustomerTransaction.repair_order),
                joinedload(CustomerTransaction.sale)
            )
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def get_customer_sales(self, customer_id: int, skip: int = 0, limit: int = 100) -> List[Sale]:
        """Récupère les ventes d'un client"""
        return (
            self.db.query(Sale)
            .filter(Sale.customer_id == customer_id)
            .order_by(desc(Sale.date))
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def get_customer_repair_orders(self, customer_id: int, skip: int = 0, limit: int = 100) -> List[RepairOrder]:
        """Récupère les réparations d'un client"""
        return (
            self.db.query(RepairOrder)
            .filter(RepairOrder.customer_id == customer_id)
            .order_by(desc(RepairOrder.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def get_customer_balance(self, customer_id: int) -> Dict[str, Any]:
        """Récupère le solde d'un client avec des informations détaillées"""
        customer = await self.get(customer_id)
        if not customer:
            return {
                "customer": None,
                "balance": 0.0,
                "available_credit": 0.0,
                "total_due": 0.0,
                "total_paid": 0.0,
                "pending_invoices": [],
                "overdue_invoices": []
            }

        # Récupérer les factures en attente (réparations)
        pending_repairs = self.db.query(RepairOrder).filter(
            RepairOrder.customer_id == customer_id,
            RepairOrder.payment_status.in_([RepairPaymentStatus.PENDING, RepairPaymentStatus.PARTIAL])
        ).all()

        # Récupérer les factures en retard (réparations)
        overdue_repairs = self.db.query(RepairOrder).filter(
            RepairOrder.customer_id == customer_id,
            RepairOrder.payment_status == RepairPaymentStatus.OVERDUE
        ).all()

        # Récupérer les factures en attente (ventes)
        pending_sales = self.db.query(Sale).filter(
            Sale.customer_id == customer_id,
            Sale.payment_status.in_([SalePaymentStatus.PENDING, SalePaymentStatus.PARTIAL])
        ).all()

        # Récupérer les factures en retard (ventes)
        overdue_sales = self.db.query(Sale).filter(
            Sale.customer_id == customer_id,
            Sale.payment_status == SalePaymentStatus.OVERDUE
        ).all()

        # Calculer le montant total dû (normaliser en float pour éviter Decimal vs float)
        total_due = 0.0
        for repair in pending_repairs + overdue_repairs:
            ra = float(repair.final_amount or 0)
            rp = float(repair.total_paid or 0)
            total_due += ra - rp

        for sale in pending_sales + overdue_sales:
            sa = float(sale.final_amount or 0)
            sp = float(sale.total_paid or 0)
            total_due += sa - sp

        # Calculer le montant total payé (normaliser en float)
        total_paid_val = self.db.query(func.sum(CustomerTransaction.amount)).filter(
            CustomerTransaction.customer_id == customer_id,
            CustomerTransaction.amount > 0
        ).scalar() or 0.0
        try:
            total_paid = float(total_paid_val)
        except Exception:
            total_paid = 0.0

        # Calculer le crédit disponible
        available_credit = max(0.0, customer.credit_limit - customer.current_balance)

        return {
            "customer": customer,
            "balance": customer.current_balance,
            "available_credit": available_credit,
            "total_due": total_due,
            "total_paid": total_paid,
            "pending_invoices": pending_repairs + pending_sales,
            "overdue_invoices": overdue_repairs + overdue_sales
        }

    async def get_customer_debt_summary(self, customer_id: int) -> Dict[str, Any]:
        """Récupère un résumé des dettes d'un client"""
        customer = await self.get(customer_id)
        if not customer:
            return {
                "total_debt": 0.0,
                "overdue_debt": 0.0,
                "pending_debt": 0.0,
                "repair_count": 0,
                "unpaid_repair_count": 0
            }

        # Calculer le nombre total de réparations
        repair_count = self.db.query(func.count(RepairOrder.id))\
            .filter(RepairOrder.customer_id == customer_id)\
            .scalar() or 0

        # Calculer le nombre de réparations impayées
        unpaid_repair_count = self.db.query(func.count(RepairOrder.id))\
            .filter(
                RepairOrder.customer_id == customer_id,
                RepairOrder.payment_status.in_([RepairPaymentStatus.PENDING, RepairPaymentStatus.PARTIAL, RepairPaymentStatus.OVERDUE])
            ).scalar() or 0

        # Calculer le nombre de ventes impayées
        unpaid_sale_count = self.db.query(func.count(Sale.id))\
            .filter(
                Sale.customer_id == customer_id,
                Sale.payment_status.in_([SalePaymentStatus.PENDING, SalePaymentStatus.PARTIAL, SalePaymentStatus.OVERDUE])
            ).scalar() or 0

        # Calculer la dette totale des réparations
        repair_total_debt = self.db.query(func.sum(RepairOrder.final_amount - RepairOrder.total_paid))\
            .filter(
                RepairOrder.customer_id == customer_id,
                RepairOrder.payment_status.in_([RepairPaymentStatus.PENDING, RepairPaymentStatus.PARTIAL, RepairPaymentStatus.OVERDUE])
            ).scalar() or 0.0

        # Calculer la dette totale des ventes
        sale_total_debt = self.db.query(func.sum(Sale.final_amount - Sale.total_paid))\
            .filter(
                Sale.customer_id == customer_id,
                Sale.payment_status.in_([SalePaymentStatus.PENDING, SalePaymentStatus.PARTIAL, SalePaymentStatus.OVERDUE])
            ).scalar() or 0.0

        # Calculer la dette en retard des réparations
        repair_overdue_debt = self.db.query(func.sum(RepairOrder.final_amount - RepairOrder.total_paid))\
            .filter(
                RepairOrder.customer_id == customer_id,
                RepairOrder.payment_status == RepairPaymentStatus.OVERDUE
            ).scalar() or 0.0

        # Calculer la dette en retard des ventes
        sale_overdue_debt = self.db.query(func.sum(Sale.final_amount - Sale.total_paid))\
            .filter(
                Sale.customer_id == customer_id,
                Sale.payment_status == SalePaymentStatus.OVERDUE
            ).scalar() or 0.0

        # Calculer la dette en attente des réparations
        repair_pending_debt = self.db.query(func.sum(RepairOrder.final_amount - RepairOrder.total_paid))\
            .filter(
                RepairOrder.customer_id == customer_id,
                RepairOrder.payment_status.in_([RepairPaymentStatus.PENDING, RepairPaymentStatus.PARTIAL])
            ).scalar() or 0.0

        # Calculer la dette en attente des ventes
        sale_pending_debt = self.db.query(func.sum(Sale.final_amount - Sale.total_paid))\
            .filter(
                Sale.customer_id == customer_id,
                Sale.payment_status.in_([SalePaymentStatus.PENDING, SalePaymentStatus.PARTIAL])
            ).scalar() or 0.0

        # Calculer les totaux
        total_debt = repair_total_debt + sale_total_debt
        overdue_debt = repair_overdue_debt + sale_overdue_debt
        pending_debt = repair_pending_debt + sale_pending_debt

        return {
            "total_debt": total_debt,
            "overdue_debt": overdue_debt,
            "pending_debt": pending_debt,
            "repair_count": repair_count,
            "unpaid_repair_count": unpaid_repair_count,
            "unpaid_sale_count": unpaid_sale_count,
            "repair_total_debt": repair_total_debt,
            "sale_total_debt": sale_total_debt
        }
