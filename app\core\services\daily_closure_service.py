"""
Service pour la gestion des clôtures journalières de trésorerie.
"""
import logging
import hashlib
import json
from datetime import datetime, date, timedelta
from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func, text

from app.core.models.daily_closure import (
    DailyClosure, CashRegisterSnapshot, PeriodLock, 
    ClosureValidation, ClosureAuditLog,
    ClosureStatus, ClosureType
)
from app.core.models.treasury import CashRegister, CashTransaction, PaymentMethod
from app.core.services.base_service import BaseService
from app.utils.decimal_utils import validate_amount, safe_decimal_sum
from app.utils.transaction_manager import atomic_operation, TransactionError
from app.utils.event_bus import event_bus

logger = logging.getLogger(__name__)


class ClosureValidationError(Exception):
    """Exception levée lors d'erreurs de validation de clôture"""
    pass


class PeriodLockError(Exception):
    """Exception levée lors d'erreurs de verrouillage de période"""
    pass


class DailyClosureService(BaseService):
    """Service pour la gestion des clôtures journalières"""
    
    def __init__(self, db: Session):
        """
        Initialise le service de clôture journalière
        
        Args:
            db: Session de base de données
        """
        super().__init__(db, DailyClosure)
        self.audit_enabled = True
    
    def can_close_date(self, closure_date: date) -> Tuple[bool, str]:
        """
        Vérifie si une date peut être clôturée
        
        Args:
            closure_date: Date à vérifier
            
        Returns:
            Tuple (peut_cloturer, raison)
        """
        # Vérifier si la date n'est pas dans le futur
        if closure_date > date.today():
            return False, "Impossible de clôturer une date future"
        
        # Vérifier si la clôture existe déjà
        existing_closure = self.db.query(DailyClosure).filter(
            DailyClosure.closure_date == closure_date
        ).first()
        
        if existing_closure:
            if existing_closure.status == ClosureStatus.COMPLETED:
                return False, f"La date {closure_date} est déjà clôturée"
            elif existing_closure.status == ClosureStatus.IN_PROGRESS:
                return False, f"Une clôture est déjà en cours pour la date {closure_date}"
        
        # Vérifier si la période est verrouillée
        if self.is_period_locked(closure_date):
            return False, f"La période contenant la date {closure_date} est verrouillée"
        
        # Vérifier s'il y a des caisses actives
        active_registers = self.db.query(CashRegister).filter(
            CashRegister.is_active == True
        ).count()
        
        if active_registers == 0:
            return False, "Aucune caisse active trouvée"
        
        return True, "La date peut être clôturée"
    
    def start_daily_closure(self, closure_date: date, user_id: int, 
                           notes: str = None) -> DailyClosure:
        """
        Démarre une clôture journalière
        
        Args:
            closure_date: Date de clôture
            user_id: ID de l'utilisateur qui démarre la clôture
            notes: Notes optionnelles
            
        Returns:
            Objet DailyClosure créé
            
        Raises:
            ClosureValidationError: Si la clôture ne peut pas être démarrée
            TransactionError: En cas d'erreur de transaction
        """
        # Vérifier si la clôture peut être démarrée
        can_close, reason = self.can_close_date(closure_date)
        if not can_close:
            raise ClosureValidationError(reason)
        
        try:
            with atomic_operation(self.db, f"Démarrage clôture {closure_date}") as session:
                # Créer ou récupérer la clôture
                closure = session.query(DailyClosure).filter(
                    DailyClosure.closure_date == closure_date
                ).first()
                
                if not closure:
                    closure = DailyClosure(
                        closure_date=closure_date,
                        closure_type=ClosureType.DAILY,
                        notes=notes
                    )
                    session.add(closure)
                    session.flush()
                
                # Mettre à jour le statut
                closure.status = ClosureStatus.IN_PROGRESS
                closure.started_at = datetime.utcnow()
                closure.started_by_user_id = user_id
                closure.error_message = None
                
                session.flush()
                
                # Audit
                self._log_audit(session, closure.id, "CLOSURE_STARTED", user_id=user_id)
                
                logger.info(f"Clôture journalière démarrée pour {closure_date} par utilisateur {user_id}")
                
                return closure
                
        except TransactionError:
            raise
        except Exception as e:
            raise TransactionError(f"Erreur lors du démarrage de la clôture: {e}")
    
    def calculate_register_snapshot(self, cash_register_id: int, 
                                  closure_date: date) -> Dict[str, Any]:
        """
        Calcule le snapshot d'une caisse pour une date donnée
        
        Args:
            cash_register_id: ID de la caisse
            closure_date: Date de clôture
            
        Returns:
            Dictionnaire avec les données du snapshot
        """
        # Récupérer la caisse
        cash_register = self.db.query(CashRegister).get(cash_register_id)
        if not cash_register:
            raise ValueError(f"Caisse {cash_register_id} non trouvée")
        
        # Définir la plage de dates pour la journée
        start_datetime = datetime.combine(closure_date, datetime.min.time())
        end_datetime = datetime.combine(closure_date, datetime.max.time())
        
        # Récupérer les transactions de la journée
        transactions = self.db.query(CashTransaction).filter(
            and_(
                CashTransaction.cash_register_id == cash_register_id,
                CashTransaction.transaction_date >= start_datetime,
                CashTransaction.transaction_date <= end_datetime
            )
        ).all()
        
        # Calculer le solde d'ouverture (solde à la fin de la journée précédente)
        previous_date = closure_date - timedelta(days=1)
        previous_snapshot = self.db.query(CashRegisterSnapshot).join(DailyClosure).filter(
            and_(
                CashRegisterSnapshot.cash_register_id == cash_register_id,
                DailyClosure.closure_date == previous_date,
                DailyClosure.status == ClosureStatus.COMPLETED
            )
        ).first()
        
        opening_balance = previous_snapshot.closing_balance if previous_snapshot else cash_register.initial_balance
        
        # Calculer les totaux
        total_in = safe_decimal_sum(t.amount for t in transactions if t.amount > 0)
        total_out = safe_decimal_sum(abs(t.amount) for t in transactions if t.amount < 0)
        transaction_count = len(transactions)
        
        # Calculer les montants par méthode de paiement
        payment_amounts = {method: Decimal("0.00") for method in PaymentMethod}
        for transaction in transactions:
            if transaction.payment_method in payment_amounts:
                payment_amounts[transaction.payment_method] += abs(transaction.amount)
        
        # Calculer le solde théorique
        theoretical_balance = validate_amount(opening_balance) + total_in - total_out
        
        # Le solde de clôture est le solde actuel de la caisse
        closing_balance = validate_amount(cash_register.current_balance)
        
        # Calculer l'écart
        variance = closing_balance - theoretical_balance
        
        return {
            'cash_register_id': cash_register_id,
            'opening_balance': opening_balance,
            'closing_balance': closing_balance,
            'theoretical_balance': theoretical_balance,
            'total_in': total_in,
            'total_out': total_out,
            'transaction_count': transaction_count,
            'cash_amount': payment_amounts.get(PaymentMethod.cash, Decimal("0.00")),
            'bank_transfer_amount': payment_amounts.get(PaymentMethod.bank_transfer, Decimal("0.00")),
            'check_amount': payment_amounts.get(PaymentMethod.check, Decimal("0.00")),
            'credit_card_amount': payment_amounts.get(PaymentMethod.credit_card, Decimal("0.00")),
            'other_amount': payment_amounts.get(PaymentMethod.other, Decimal("0.00")),
            'variance': variance,
            'adjustment_amount': Decimal("0.00"),
            'adjustment_reason': None,
            'notes': None,
            'is_reconciled': abs(variance) <= Decimal("0.01")  # Tolérance de 1 centime
        }
    
    def create_snapshots(self, closure: DailyClosure) -> List[CashRegisterSnapshot]:
        """
        Crée les snapshots pour toutes les caisses actives
        
        Args:
            closure: Objet DailyClosure
            
        Returns:
            Liste des snapshots créés
        """
        snapshots = []
        
        # Récupérer toutes les caisses actives
        active_registers = self.db.query(CashRegister).filter(
            CashRegister.is_active == True
        ).all()
        
        for register in active_registers:
            # Calculer les données du snapshot
            snapshot_data = self.calculate_register_snapshot(register.id, closure.closure_date)
            
            # Créer le snapshot
            snapshot = CashRegisterSnapshot(
                closure_id=closure.id,
                **snapshot_data
            )
            
            self.db.add(snapshot)
            snapshots.append(snapshot)
        
        return snapshots
    
    def validate_closure(self, closure: DailyClosure) -> List[ClosureValidation]:
        """
        Effectue les validations de la clôture
        
        Args:
            closure: Objet DailyClosure
            
        Returns:
            Liste des validations effectuées
        """
        validations = []
        
        # Validation 1: Vérifier que tous les snapshots sont cohérents
        validation = self._validate_snapshots_consistency(closure)
        validations.append(validation)
        
        # Validation 2: Vérifier les écarts
        validation = self._validate_variances(closure)
        validations.append(validation)
        
        # Validation 3: Vérifier l'intégrité des données
        validation = self._validate_data_integrity(closure)
        validations.append(validation)
        
        # Sauvegarder les validations
        for validation in validations:
            self.db.add(validation)
        
        return validations
    
    def _validate_snapshots_consistency(self, closure: DailyClosure) -> ClosureValidation:
        """Valide la cohérence des snapshots"""
        try:
            snapshots = closure.snapshots
            total_calculated = sum(s.closing_balance for s in snapshots)
            total_expected = closure.total_balance
            
            is_valid = abs(total_calculated - total_expected) <= Decimal("0.01")
            
            return ClosureValidation(
                closure_id=closure.id,
                validation_type="SNAPSHOTS_CONSISTENCY",
                is_valid=is_valid,
                expected_value=str(total_expected),
                actual_value=str(total_calculated),
                tolerance=Decimal("0.01"),
                error_message=None if is_valid else "Incohérence dans les totaux des snapshots"
            )
        except Exception as e:
            return ClosureValidation(
                closure_id=closure.id,
                validation_type="SNAPSHOTS_CONSISTENCY",
                is_valid=False,
                error_message=f"Erreur lors de la validation: {e}"
            )
    
    def _validate_variances(self, closure: DailyClosure) -> ClosureValidation:
        """Valide les écarts des snapshots"""
        try:
            snapshots = closure.snapshots
            max_variance = max(abs(s.variance) for s in snapshots) if snapshots else Decimal("0.00")
            tolerance = Decimal("10.00")  # Tolérance de 10 DA
            
            is_valid = max_variance <= tolerance
            
            return ClosureValidation(
                closure_id=closure.id,
                validation_type="VARIANCE_CHECK",
                is_valid=is_valid,
                expected_value="0.00",
                actual_value=str(max_variance),
                tolerance=tolerance,
                warning_message=None if is_valid else f"Écart maximum de {max_variance} DA détecté"
            )
        except Exception as e:
            return ClosureValidation(
                closure_id=closure.id,
                validation_type="VARIANCE_CHECK",
                is_valid=False,
                error_message=f"Erreur lors de la validation des écarts: {e}"
            )
    
    def _validate_data_integrity(self, closure: DailyClosure) -> ClosureValidation:
        """Valide l'intégrité des données"""
        try:
            # Vérifier que toutes les caisses actives ont un snapshot
            active_registers_count = self.db.query(CashRegister).filter(
                CashRegister.is_active == True
            ).count()
            
            snapshots_count = len(closure.snapshots)
            
            is_valid = active_registers_count == snapshots_count
            
            return ClosureValidation(
                closure_id=closure.id,
                validation_type="DATA_INTEGRITY",
                is_valid=is_valid,
                expected_value=str(active_registers_count),
                actual_value=str(snapshots_count),
                error_message=None if is_valid else "Nombre de snapshots incohérent avec les caisses actives"
            )
        except Exception as e:
            return ClosureValidation(
                closure_id=closure.id,
                validation_type="DATA_INTEGRITY",
                is_valid=False,
                error_message=f"Erreur lors de la validation d'intégrité: {e}"
            )

    def complete_daily_closure(self, closure_id: int, user_id: int,
                              force: bool = False) -> DailyClosure:
        """
        Termine une clôture journalière

        Args:
            closure_id: ID de la clôture
            user_id: ID de l'utilisateur qui termine la clôture
            force: Forcer la clôture même en cas d'erreurs de validation

        Returns:
            Objet DailyClosure mis à jour

        Raises:
            ClosureValidationError: Si la clôture ne peut pas être terminée
            TransactionError: En cas d'erreur de transaction
        """
        try:
            with atomic_operation(self.db, f"Finalisation clôture {closure_id}") as session:
                closure = session.query(DailyClosure).get(closure_id)
                if not closure:
                    raise ClosureValidationError(f"Clôture {closure_id} non trouvée")

                if closure.status != ClosureStatus.IN_PROGRESS:
                    raise ClosureValidationError(f"La clôture n'est pas en cours")

                # Créer les snapshots si pas encore fait
                if not closure.snapshots:
                    snapshots = self.create_snapshots(closure)
                    session.flush()

                # Calculer les totaux
                self._calculate_closure_totals(closure)

                # Effectuer les validations
                validations = self.validate_closure(closure)
                session.flush()

                # Vérifier si toutes les validations sont passées
                failed_validations = [v for v in validations if not v.is_valid]
                if failed_validations and not force:
                    error_messages = [v.error_message for v in failed_validations if v.error_message]
                    raise ClosureValidationError(f"Validations échouées: {'; '.join(error_messages)}")

                # Générer le hash de validation
                closure.validation_hash = self._generate_validation_hash(closure)

                # Marquer comme terminée
                closure.status = ClosureStatus.COMPLETED
                closure.completed_at = datetime.utcnow()
                closure.completed_by_user_id = user_id

                # Créer le verrouillage de période
                self._create_period_lock(closure, user_id)

                session.flush()

                # Audit
                self._log_audit(session, closure.id, "CLOSURE_COMPLETED", user_id=user_id)

                logger.info(f"Clôture journalière terminée pour {closure.closure_date}")

                # Notifier via l'event bus
                event_bus.show_success(f"Clôture journalière du {closure.closure_date} terminée avec succès")

                return closure

        except TransactionError:
            raise
        except Exception as e:
            raise TransactionError(f"Erreur lors de la finalisation de la clôture: {e}")

    def _calculate_closure_totals(self, closure: DailyClosure):
        """Calcule les totaux de la clôture"""
        snapshots = closure.snapshots

        closure.total_cash_registers = len(snapshots)
        closure.total_balance = sum(s.closing_balance for s in snapshots)
        closure.total_transactions = sum(s.transaction_count for s in snapshots)
        closure.total_in = sum(s.total_in for s in snapshots)
        closure.total_out = sum(s.total_out for s in snapshots)

    def _generate_validation_hash(self, closure: DailyClosure) -> str:
        """Génère un hash de validation pour la clôture"""
        data = {
            'closure_date': closure.closure_date.isoformat(),
            'total_balance': str(closure.total_balance),
            'total_transactions': closure.total_transactions,
            'snapshots': [
                {
                    'register_id': s.cash_register_id,
                    'opening_balance': str(s.opening_balance),
                    'closing_balance': str(s.closing_balance),
                    'transaction_count': s.transaction_count
                }
                for s in sorted(closure.snapshots, key=lambda x: x.cash_register_id)
            ]
        }

        json_str = json.dumps(data, sort_keys=True)
        return hashlib.sha256(json_str.encode()).hexdigest()

    def _create_period_lock(self, closure: DailyClosure, user_id: int):
        """Crée un verrouillage de période pour la clôture"""
        period_lock = PeriodLock(
            start_date=closure.closure_date,
            end_date=closure.closure_date,
            lock_type="daily_closure",
            locked_by_user_id=user_id,
            reason=f"Clôture journalière du {closure.closure_date}",
            reference_id=closure.id,
            is_active=True,
            can_unlock=False,
            unlock_requires_approval=True
        )

        self.db.add(period_lock)

    def is_period_locked(self, check_date: date) -> bool:
        """
        Vérifie si une date est verrouillée

        Args:
            check_date: Date à vérifier

        Returns:
            True si la date est verrouillée
        """
        lock = self.db.query(PeriodLock).filter(
            and_(
                PeriodLock.is_active == True,
                PeriodLock.start_date <= check_date,
                PeriodLock.end_date >= check_date
            )
        ).first()

        return lock is not None

    def get_period_locks(self, start_date: date = None,
                        end_date: date = None) -> List[PeriodLock]:
        """
        Récupère les verrouillages de période

        Args:
            start_date: Date de début (optionnelle)
            end_date: Date de fin (optionnelle)

        Returns:
            Liste des verrouillages
        """
        query = self.db.query(PeriodLock).filter(PeriodLock.is_active == True)

        if start_date:
            query = query.filter(PeriodLock.end_date >= start_date)

        if end_date:
            query = query.filter(PeriodLock.start_date <= end_date)

        return query.order_by(PeriodLock.start_date).all()

    def cancel_closure(self, closure_id: int, user_id: int, reason: str) -> DailyClosure:
        """
        Annule une clôture en cours

        Args:
            closure_id: ID de la clôture
            user_id: ID de l'utilisateur qui annule
            reason: Raison de l'annulation

        Returns:
            Objet DailyClosure mis à jour
        """
        try:
            with atomic_operation(self.db, f"Annulation clôture {closure_id}") as session:
                closure = session.query(DailyClosure).get(closure_id)
                if not closure:
                    raise ClosureValidationError(f"Clôture {closure_id} non trouvée")

                if closure.status not in [ClosureStatus.PENDING, ClosureStatus.IN_PROGRESS]:
                    raise ClosureValidationError("Seules les clôtures en attente ou en cours peuvent être annulées")

                closure.status = ClosureStatus.CANCELLED
                closure.error_message = reason
                closure.completed_at = datetime.utcnow()
                closure.completed_by_user_id = user_id

                # Supprimer les snapshots créés
                for snapshot in closure.snapshots:
                    session.delete(snapshot)

                session.flush()

                # Audit
                self._log_audit(session, closure.id, "CLOSURE_CANCELLED", user_id=user_id,
                              details={'reason': reason})

                logger.info(f"Clôture journalière annulée pour {closure.closure_date}: {reason}")

                return closure

        except TransactionError:
            raise
        except Exception as e:
            raise TransactionError(f"Erreur lors de l'annulation de la clôture: {e}")

    def get_closure_history(self, limit: int = 50, offset: int = 0) -> List[DailyClosure]:
        """
        Récupère l'historique des clôtures

        Args:
            limit: Nombre maximum de résultats
            offset: Décalage pour la pagination

        Returns:
            Liste des clôtures
        """
        return (self.db.query(DailyClosure)
                .order_by(desc(DailyClosure.closure_date))
                .limit(limit)
                .offset(offset)
                .all())

    def _log_audit(self, session: Session, closure_id: int, action: str,
                   user_id: int = None, details: Dict[str, Any] = None):
        """Enregistre une entrée d'audit"""
        if not self.audit_enabled:
            return

        audit_log = ClosureAuditLog(
            closure_id=closure_id,
            action=action,
            user_id=user_id,
            new_values=json.dumps(details) if details else None,
            timestamp=datetime.utcnow(),
            success=True
        )

        session.add(audit_log)
