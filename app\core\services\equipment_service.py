from typing import List, Optional
from sqlalchemy.orm import Session
from ..models.equipment import Equipment, EquipmentPydantic, EquipmentStatus
from .base_service import BaseService

class EquipmentService(BaseService[Equipment, EquipmentPydantic, EquipmentPydantic]):
    """Service pour la gestion des équipements"""
    
    def __init__(self, db: Session = None):
        """Initialise le service avec une session de base de données"""
        from app.utils.database import SessionLocal
        if db is None:
            db = SessionLocal()
        super().__init__(db, Equipment)
    
    async def search_equipment(self, query: str, limit: int = 50) -> List[Equipment]:
        """Recherche des équipements par nom, marque, modèle ou numéro de série"""
        search = f"%{query}%"
        return (
            self.db.query(self.model)
            .filter(
                (self.model.name.ilike(search)) |
                (self.model.brand.ilike(search)) |
                (self.model.model.ilike(search)) |
                (self.model.serial_number.ilike(search))
            )
            .filter(self.model.is_active == True)
            .limit(limit)
            .all()
        )
    
    async def get_active_equipment(self, skip: int = 0, limit: int = 100) -> List[Equipment]:
        """Récupère tous les équipements actifs"""
        return (
            self.db.query(self.model)
            .filter(self.model.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    async def get_equipment_by_status(self, status: EquipmentStatus) -> List[Equipment]:
        """Récupère les équipements par statut"""
        return (
            self.db.query(self.model)
            .filter(self.model.status == status)
            .filter(self.model.is_active == True)
            .all()
        )
    
    async def deactivate_equipment(self, equipment_id: int) -> bool:
        """Désactive un équipement au lieu de le supprimer"""
        equipment = await self.get(equipment_id)
        if not equipment:
            return False

        equipment.is_active = False
        self.db.commit()
        return True
    
    async def get_equipment_with_repairs(self, equipment_id: int) -> Optional[Equipment]:
        """Récupère un équipement avec ses réparations"""
        return (
            self.db.query(self.model)
            .filter(self.model.id == equipment_id)
            .first()
        )
    
    async def get_equipment_locations(self) -> List[str]:
        """Récupère la liste des emplacements d'équipements"""
        locations = (
            self.db.query(self.model.location)
            .filter(self.model.location.isnot(None))
            .distinct()
            .all()
        )
        return [location[0] for location in locations if location[0]]
