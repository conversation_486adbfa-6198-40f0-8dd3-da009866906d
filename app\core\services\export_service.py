"""
Service d'export pour les rapports de trésorerie (PDF, Excel, CSV).
"""
import logging
import io
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
from decimal import Decimal
from pathlib import Path

# Imports pour PDF
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

# Imports pour Excel
try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.chart import BarChart, Reference
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

# Import pour CSV
import csv

from app.core.services.treasury_report_service import ReportData

logger = logging.getLogger(__name__)


class ExportService:
    """Service d'export pour les rapports"""
    
    def __init__(self, output_dir: str = "exports"):
        """
        Initialise le service d'export
        
        Args:
            output_dir: Répertoire de sortie pour les exports
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def export_to_pdf(self, report_data: ReportData, filename: str = None) -> str:
        """
        Exporte un rapport en PDF
        
        Args:
            report_data: Données du rapport
            filename: Nom du fichier (optionnel)
            
        Returns:
            Chemin du fichier généré
            
        Raises:
            ImportError: Si reportlab n'est pas disponible
            Exception: En cas d'erreur d'export
        """
        if not REPORTLAB_AVAILABLE:
            raise ImportError("reportlab n'est pas installé. Installez-le avec: pip install reportlab")
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"rapport_tresorerie_{timestamp}.pdf"
        
        filepath = self.output_dir / filename
        
        try:
            # Créer le document PDF
            doc = SimpleDocTemplate(str(filepath), pagesize=A4)
            story = []
            styles = getSampleStyleSheet()
            
            # Style personnalisé pour le titre
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=TA_CENTER
            )
            
            # Titre
            story.append(Paragraph(report_data.title, title_style))
            story.append(Paragraph(report_data.subtitle, styles['Heading2']))
            story.append(Spacer(1, 12))
            
            # Informations de génération
            info_text = f"Généré le {report_data.generated_at.strftime('%d/%m/%Y à %H:%M')}"
            story.append(Paragraph(info_text, styles['Normal']))
            story.append(Spacer(1, 20))
            
            # Résumé
            if report_data.summary:
                story.append(Paragraph("Résumé", styles['Heading3']))
                for key, value in report_data.summary.items():
                    if isinstance(value, (int, float, Decimal)):
                        if 'amount' in key.lower() or 'balance' in key.lower():
                            value_str = f"{value:,.2f} DA"
                        else:
                            value_str = f"{value:,}"
                    else:
                        value_str = str(value)
                    
                    story.append(Paragraph(f"<b>{key.replace('_', ' ').title()}:</b> {value_str}", styles['Normal']))
                
                story.append(Spacer(1, 20))
            
            # Données du tableau
            if report_data.data:
                story.append(Paragraph("Détails", styles['Heading3']))
                
                # Préparer les données du tableau
                if report_data.data:
                    headers = list(report_data.data[0].keys())
                    table_data = [headers]
                    
                    for row in report_data.data:
                        formatted_row = []
                        for key, value in row.items():
                            if isinstance(value, datetime):
                                formatted_row.append(value.strftime('%d/%m/%Y %H:%M'))
                            elif isinstance(value, (int, float, Decimal)):
                                if 'amount' in key.lower() or 'balance' in key.lower():
                                    formatted_row.append(f"{value:,.2f}")
                                else:
                                    formatted_row.append(str(value))
                            else:
                                formatted_row.append(str(value) if value is not None else '')
                        table_data.append(formatted_row)
                    
                    # Créer le tableau
                    table = Table(table_data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('FONTSIZE', (0, 1), (-1, -1), 8),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    
                    story.append(table)
            
            # Pagination
            if report_data.pagination.total_pages > 1:
                story.append(Spacer(1, 20))
                pagination_text = (f"Page {report_data.pagination.page} sur {report_data.pagination.total_pages} "
                                 f"({report_data.pagination.total_items} éléments au total)")
                story.append(Paragraph(pagination_text, styles['Normal']))
            
            # Construire le PDF
            doc.build(story)
            
            logger.info(f"Rapport PDF généré: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération du PDF: {e}")
            raise
    
    def export_to_excel(self, report_data: ReportData, filename: str = None) -> str:
        """
        Exporte un rapport en Excel
        
        Args:
            report_data: Données du rapport
            filename: Nom du fichier (optionnel)
            
        Returns:
            Chemin du fichier généré
            
        Raises:
            ImportError: Si openpyxl n'est pas disponible
            Exception: En cas d'erreur d'export
        """
        if not OPENPYXL_AVAILABLE:
            raise ImportError("openpyxl n'est pas installé. Installez-le avec: pip install openpyxl")
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"rapport_tresorerie_{timestamp}.xlsx"
        
        filepath = self.output_dir / filename
        
        try:
            # Créer le classeur
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Rapport"
            
            # Styles
            title_font = Font(size=16, bold=True)
            header_font = Font(size=12, bold=True)
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            border = Border(left=Side(style='thin'), right=Side(style='thin'), 
                          top=Side(style='thin'), bottom=Side(style='thin'))
            
            row = 1
            
            # Titre
            ws.cell(row=row, column=1, value=report_data.title).font = title_font
            row += 1
            
            # Sous-titre
            ws.cell(row=row, column=1, value=report_data.subtitle).font = header_font
            row += 2
            
            # Informations de génération
            ws.cell(row=row, column=1, value=f"Généré le {report_data.generated_at.strftime('%d/%m/%Y à %H:%M')}")
            row += 2
            
            # Résumé
            if report_data.summary:
                ws.cell(row=row, column=1, value="RÉSUMÉ").font = header_font
                row += 1
                
                for key, value in report_data.summary.items():
                    ws.cell(row=row, column=1, value=key.replace('_', ' ').title())
                    ws.cell(row=row, column=2, value=value)
                    row += 1
                
                row += 1
            
            # Données
            if report_data.data:
                ws.cell(row=row, column=1, value="DONNÉES").font = header_font
                row += 1
                
                # En-têtes
                headers = list(report_data.data[0].keys())
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=row, column=col, value=header.replace('_', ' ').title())
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = header_fill
                    cell.border = border
                    cell.alignment = Alignment(horizontal="center")
                
                row += 1
                
                # Données
                for data_row in report_data.data:
                    for col, (key, value) in enumerate(data_row.items(), 1):
                        if isinstance(value, datetime):
                            cell_value = value.strftime('%d/%m/%Y %H:%M')
                        elif isinstance(value, (int, float, Decimal)):
                            cell_value = float(value) if isinstance(value, Decimal) else value
                        else:
                            cell_value = str(value) if value is not None else ''
                        
                        cell = ws.cell(row=row, column=col, value=cell_value)
                        cell.border = border
                        
                        # Alignement selon le type
                        if isinstance(cell_value, (int, float)):
                            cell.alignment = Alignment(horizontal="right")
                        else:
                            cell.alignment = Alignment(horizontal="left")
                    
                    row += 1
            
            # Ajuster la largeur des colonnes
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # Sauvegarder
            wb.save(filepath)
            
            logger.info(f"Rapport Excel généré: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération du fichier Excel: {e}")
            raise
    
    def export_to_csv(self, report_data: ReportData, filename: str = None) -> str:
        """
        Exporte un rapport en CSV
        
        Args:
            report_data: Données du rapport
            filename: Nom du fichier (optionnel)
            
        Returns:
            Chemin du fichier généré
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"rapport_tresorerie_{timestamp}.csv"
        
        filepath = self.output_dir / filename
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                if not report_data.data:
                    return str(filepath)
                
                # En-têtes
                headers = list(report_data.data[0].keys())
                writer = csv.DictWriter(csvfile, fieldnames=headers)
                
                # Écrire les métadonnées en commentaire
                csvfile.write(f"# {report_data.title}\n")
                csvfile.write(f"# {report_data.subtitle}\n")
                csvfile.write(f"# Généré le {report_data.generated_at.strftime('%d/%m/%Y à %H:%M')}\n")
                csvfile.write("#\n")
                
                # Écrire les en-têtes
                writer.writeheader()
                
                # Écrire les données
                for row in report_data.data:
                    formatted_row = {}
                    for key, value in row.items():
                        if isinstance(value, datetime):
                            formatted_row[key] = value.strftime('%d/%m/%Y %H:%M')
                        elif isinstance(value, Decimal):
                            formatted_row[key] = float(value)
                        else:
                            formatted_row[key] = value
                    
                    writer.writerow(formatted_row)
            
            logger.info(f"Rapport CSV généré: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération du fichier CSV: {e}")
            raise
    
    def get_available_formats(self) -> List[str]:
        """
        Retourne la liste des formats d'export disponibles
        
        Returns:
            Liste des formats disponibles
        """
        formats = ["CSV"]
        
        if REPORTLAB_AVAILABLE:
            formats.append("PDF")
        
        if OPENPYXL_AVAILABLE:
            formats.append("Excel")
        
        return formats
