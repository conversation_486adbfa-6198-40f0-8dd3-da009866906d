from __future__ import annotations
from typing import Optional, Dict, Any
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP

from sqlalchemy.orm import Session

# Models & enums
from app.core.models.repair import (
    RepairOrder,
    RepairPayment,
    PaymentMethod as RepairPaymentMethod,
    PaymentStatus as RepairPaymentStatus,
    PaymentRecordType as RepairPaymentRecordType,
)
from app.core.models.sale import (
    Sale,
    Payment,
    PaymentMethod as SalePaymentMethod,
    PaymentStatus as SalePaymentStatus,
)
from app.core.models.treasury import (
    CashRegister,
    CashRegisterType,
    TransactionCategory,
    PaymentMethod as TreasuryPaymentMethod,
)
from app.core.models.customer import (
    Customer,
    CustomerTransaction,
)
from .reference_service import ReferenceService, ReferenceType

# Services existants
from app.core.services.treasury_service import TreasuryService
from app.core.services.repair_payment_service import RepairPaymentService
from app.core.services.sale_service import SaleService


class FinanceService:
    """
    Service centralisé pour la synchronisation en temps réel des paiements
    (source de vérité) avec Réparations et Ventes, et écriture en Trésorerie.

    - Les écritures de trésorerie sont créées au même moment que le paiement.
    - Les statuts de paiement sont recalculés depuis les écritures Paiements.
    - Idempotence optionnelle via idempotency_key quand disponible.
    """

    def __init__(self, db: Session):
        self.db = db
        self.treasury = TreasuryService(db)
        self.repair_payments = RepairPaymentService(db)
        self.sale_service = SaleService(db)
        self.reference_service = ReferenceService(db)

    # ------------------------
    # Helpers
    # ------------------------
    def _d(self, v) -> Decimal:
        return Decimal(str(v)).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    def _pick_register(self, preferred_type: Optional[CashRegisterType]) -> Optional[CashRegister]:
        q = self.db.query(CashRegister).filter(CashRegister.is_active == True)
        if preferred_type:
            reg = q.filter(CashRegister.type == preferred_type).first()
            if reg:
                return reg
        # fallback chain
        return (
            q.filter(CashRegister.type == CashRegisterType.MAIN).first()
            or q.first()
        )

    # ------------------------
    # Réparations
    # ------------------------
    async def pay_repair(
        self,
        *,
        repair_id: int,
        amount: float | Decimal,
        method: RepairPaymentMethod | str,
        processed_by: int,
        reference_number: Optional[str] = None,
        cash_register_id: Optional[int] = None,
        credit_terms: Optional[int] = None,
        idempotency_key: Optional[str] = None,
        payment_date: Optional[datetime] = None,
        auto_generate_reference: bool = True,
    ) -> RepairPayment:
        """Crée un paiement réparation et écrit la transaction de trésorerie associée."""

        # Générer une référence unifiée si nécessaire
        if not reference_number and auto_generate_reference:
            reference_number = self.reference_service.generate_payment_reference(
                reference_type=ReferenceType.REPAIR_PAYMENT,
                entity_id=repair_id,
                user_id=processed_by
            )

        # Vérifier les doublons si une référence est fournie
        if reference_number and self.reference_service.reference_exists(reference_number):
            existing_payment = self.reference_service.find_payment_by_reference(reference_number)
            if existing_payment and existing_payment['type'] == 'repair_payment':
                print(f"⚠️  Paiement avec référence {reference_number} existe déjà")
                return existing_payment['payment']

        # Déléguer la logique métier détaillée existante au service dédié
        payment = await self.repair_payments.create_payment(
            repair_id=repair_id,
            amount=amount,
            payment_method=method,
            processed_by=processed_by,
            payment_date=payment_date,
            reference_number=reference_number,
            cash_register_id=cash_register_id,
            credit_terms=credit_terms,
            idempotency_key=idempotency_key,
        )

        # Synchroniser automatiquement le statut de paiement
        await self.sync_payment_status('repair', repair_id)

        return payment

    async def refund_repair(
        self,
        *,
        original_payment_id: int,
        amount: float | Decimal,
        reason: str,
        processed_by: int,
        idempotency_key: Optional[str] = None,
        refund_date: Optional[datetime] = None,
    ) -> RepairPayment:
        """Crée un remboursement réparation (trésorerie écrite négative)."""
        refund = await self.repair_payments.refund_payment(
            original_payment_id=original_payment_id,
            amount=amount,
            reason=reason,
            processed_by=processed_by,
            idempotency_key=idempotency_key,
            refund_date=refund_date,
        )

        # Synchroniser automatiquement le statut de paiement après remboursement
        if refund and refund.repair_order_id:
            await self.sync_payment_status('repair', refund.repair_order_id)

        return refund

    # ------------------------
    # Ventes
    # ------------------------
    async def pay_sale(
        self,
        *,
        sale_id: int,
        amount: float | Decimal,
        method: SalePaymentMethod | str,
        processed_by: int,
        reference: Optional[str] = None,
        cash_register_id: Optional[int] = None,
        idempotency_key: Optional[str] = None,  # réservé si le modèle évolue
        payment_date: Optional[datetime] = None,
        auto_generate_reference: bool = True,
    ) -> Payment:
        """
        Enregistre un paiement pour une vente existante, écrit en trésorerie,
        recalcule le statut de paiement de la vente.
        """
        sale: Sale | None = self.db.query(Sale).get(sale_id)
        if not sale:
            raise ValueError("Vente introuvable")

        amt = self._d(amount)
        if amt <= 0:
            raise ValueError("Le montant doit être > 0")

        # Générer une référence unifiée si nécessaire
        if not reference and auto_generate_reference:
            reference = self.reference_service.generate_payment_reference(
                reference_type=ReferenceType.SALE_PAYMENT,
                entity_id=sale_id,
                user_id=processed_by
            )

        # Vérifier les doublons si une référence est fournie
        if reference and self.reference_service.reference_exists(reference):
            existing_payment = self.reference_service.find_payment_by_reference(reference)
            if existing_payment and existing_payment['type'] == 'sale_payment':
                print(f"⚠️  Paiement avec référence {reference} existe déjà")
                return existing_payment['payment']

        # Normaliser la méthode
        if isinstance(method, str):
            try:
                method = SalePaymentMethod(method)
            except Exception:
                method = SalePaymentMethod.cash

        pay_dt = payment_date or datetime.now(timezone.utc)

        # 1) Créer l'écriture de paiement
        payment = Payment(
            sale_id=sale.id,
            amount=float(amt),
            payment_method=method,
            payment_date=pay_dt,
            reference=reference,
            processed_by=processed_by,
        )
        self.db.add(payment)

        # 2) Écriture de trésorerie (si non crédit)
        try:
            if method != SalePaymentMethod.credit:
                # Choisir caisse : utiliser celle spécifiée ou caisse ventes par défaut
                if cash_register_id:
                    sales_register = self.db.query(CashRegister).filter(
                        CashRegister.id == cash_register_id,
                        CashRegister.is_active == True
                    ).first()
                    if not sales_register:
                        raise ValueError(f"Caisse avec ID {cash_register_id} non trouvée ou inactive")
                else:
                    # Caisse par défaut : caisse ventes ou principale
                    sales_register = (
                        self.db.query(CashRegister)
                        .filter(CashRegister.is_active == True, CashRegister.type == CashRegisterType.SALES)
                        .first()
                        or self._pick_register(CashRegisterType.MAIN)
                    )
                if sales_register:
                    try:
                        treas_method = TreasuryPaymentMethod(method.value)
                    except Exception:
                        treas_method = TreasuryPaymentMethod.other

                    await self.treasury.add_transaction({
                        "cash_register_id": sales_register.id,
                        "amount": float(amt),
                        "transaction_date": pay_dt,
                        "category": TransactionCategory.SALE,
                        "payment_method": treas_method,
                        "reference_number": reference or f"SALE-{sale.number}",
                        "description": f"Paiement pour vente #{sale.number}",
                        "sale_id": sale.id,
                        "repair_id": None,
                        "purchase_id": None,
                        "supplier_payment_id": None,
                        "customer_transaction_id": None,
                        "expense_id": None,
                        "user_id": processed_by,
                    })
        except Exception as e:
            # ne pas bloquer le paiement si la trésorerie échoue
            print(f"Erreur écriture trésorerie (vente): {e}")

        # 3) Recalcul statut payé
        sale.total_paid = float(self._d((sale.total_paid or 0) + float(amt)))
        final_amount = self._d(sale.final_amount or 0)
        if final_amount > 0 and Decimal(str(sale.total_paid)) >= final_amount:
            sale.payment_status = SalePaymentStatus.PAID
        elif sale.total_paid > 0:
            sale.payment_status = SalePaymentStatus.PARTIAL
        else:
            sale.payment_status = SalePaymentStatus.PENDING

        if sale.payment_status == SalePaymentStatus.PAID:
            sale.payment_date = pay_dt

        # 4) Commit
        self.db.commit()
        self.db.refresh(payment)
        self.db.refresh(sale)

        # 5) Synchroniser automatiquement le statut de paiement
        await self.sync_payment_status('sale', sale_id)

        return payment

    async def refund_sale(
        self,
        *,
        sale_payment_id: int,
        amount: float | Decimal,
        reason: Optional[str],
        processed_by: int,
        refund_date: Optional[datetime] = None,
    ) -> Payment:
        """Crée un remboursement (écriture négative) pour une vente et met à jour la trésorerie."""
        orig: Payment | None = self.db.query(Payment).get(sale_payment_id)
        if not orig:
            raise ValueError("Paiement d'origine introuvable")
        sale: Sale | None = self.db.query(Sale).get(orig.sale_id)
        if not sale:
            raise ValueError("Vente introuvable")

        amt = self._d(amount)
        if amt <= 0:
            raise ValueError("Le montant du remboursement doit être > 0")

        refund_dt = refund_date or datetime.now(timezone.utc)
        refund = Payment(
            sale_id=sale.id,
            amount=float(-amt),  # négatif
            payment_method=orig.payment_method,
            payment_date=refund_dt,
            reference=f"Remboursement {orig.id}",
            notes=reason,
            processed_by=processed_by,
        )
        self.db.add(refund)

        # Trésorerie écriture négative
        try:
            sales_register = (
                self.db.query(CashRegister)
                .filter(CashRegister.is_active == True, CashRegister.type == CashRegisterType.SALES)
                .first()
                or self._pick_register(CashRegisterType.MAIN)
            )
            if sales_register:
                try:
                    treas_method = TreasuryPaymentMethod(orig.payment_method.value)
                except Exception:
                    treas_method = TreasuryPaymentMethod.other

                await self.treasury.add_transaction({
                    "cash_register_id": sales_register.id,
                    "amount": float(-amt),
                    "transaction_date": refund_dt,
                    "category": TransactionCategory.SALE,
                    "payment_method": treas_method,
                    "reference_number": f"REFUND-{orig.id}",
                    "description": f"Remboursement vente #{sale.number}",
                    "sale_id": sale.id,
                    "repair_id": None,
                    "purchase_id": None,
                    "supplier_payment_id": None,
                    "customer_transaction_id": None,
                    "expense_id": None,
                    "user_id": processed_by,
                })
        except Exception as e:
            print(f"Erreur trésorerie (remboursement vente): {e}")

        # Recalc statut
        sale.total_paid = float(self._d((sale.total_paid or 0) - float(amt)))
        final_amount = self._d(sale.final_amount or 0)
        if final_amount > 0 and Decimal(str(sale.total_paid)) >= final_amount:
            sale.payment_status = SalePaymentStatus.PAID
        elif sale.total_paid > 0:
            sale.payment_status = SalePaymentStatus.PARTIAL
        else:
            sale.payment_status = SalePaymentStatus.PENDING

        self.db.commit()
        self.db.refresh(refund)
        self.db.refresh(sale)

        # Synchroniser automatiquement le statut de paiement après remboursement
        await self.sync_payment_status('sale', sale.id)

        return refund

    # ------------------------
    # Transactions Clients
    # ------------------------
    async def record_customer_transaction(
        self,
        *,
        customer_id: int,
        amount: float | Decimal,
        description: str,
        transaction_type: str = "manual",
        reference_number: Optional[str] = None,
        repair_order_id: Optional[int] = None,
        sale_id: Optional[int] = None,
        processed_by: int,
        transaction_date: Optional[datetime] = None,
        cash_register_id: Optional[int] = None,
        payment_method: Optional[TreasuryPaymentMethod] = None,
        auto_generate_reference: bool = True,
    ) -> CustomerTransaction:
        """
        Enregistre une transaction client avec écriture optionnelle en trésorerie.

        Args:
            customer_id: ID du client
            amount: Montant (positif = paiement/crédit, négatif = dette/débit)
            description: Description de la transaction
            transaction_type: Type de transaction (manual, repair, sale, payment)
            reference_number: Numéro de référence
            repair_order_id: ID de la réparation liée (optionnel)
            sale_id: ID de la vente liée (optionnel)
            processed_by: ID de l'utilisateur qui traite
            transaction_date: Date de la transaction
            cash_register_id: ID de la caisse pour écriture trésorerie (optionnel)
            payment_method: Méthode de paiement pour trésorerie (optionnel)
        """
        # Vérifier que le client existe
        customer: Customer | None = self.db.query(Customer).get(customer_id)
        if not customer:
            raise ValueError("Client introuvable")

        # Générer une référence unifiée si nécessaire
        if not reference_number and auto_generate_reference:
            reference_number = self.reference_service.generate_payment_reference(
                reference_type=ReferenceType.CUSTOMER_TRANSACTION,
                entity_id=customer_id,
                user_id=processed_by
            )

        # Vérifier les doublons si une référence est fournie
        if reference_number and self.reference_service.reference_exists(reference_number):
            existing_transaction = self.reference_service.find_payment_by_reference(reference_number)
            if existing_transaction and existing_transaction['type'] == 'customer_transaction':
                print(f"⚠️  Transaction avec référence {reference_number} existe déjà")
                return existing_transaction['payment']

        amt = self._d(amount)
        trans_dt = transaction_date or datetime.now(timezone.utc)

        # 1) Créer la transaction client
        transaction = CustomerTransaction(
            customer_id=customer_id,
            repair_order_id=repair_order_id,
            sale_id=sale_id,
            amount=float(amt),
            transaction_date=trans_dt,
            description=description,
            reference_number=reference_number,
            processed_by=processed_by,
            transaction_type=transaction_type,
        )
        self.db.add(transaction)

        # 2) Mettre à jour le solde client
        customer.current_balance = float(self._d(customer.current_balance or 0) + amt)

        # 3) Écriture optionnelle en trésorerie (si caisse et méthode spécifiées)
        if cash_register_id and payment_method and amt != 0:
            try:
                # Déterminer la catégorie de transaction
                if transaction_type == "payment":
                    category = TransactionCategory.DEPOSIT
                elif transaction_type == "refund":
                    category = TransactionCategory.EXPENSE
                else:
                    category = TransactionCategory.DEPOSIT if amt > 0 else TransactionCategory.EXPENSE

                await self.treasury.add_transaction({
                    "cash_register_id": cash_register_id,
                    "amount": float(amt),
                    "transaction_date": trans_dt,
                    "category": category,
                    "payment_method": payment_method,
                    "reference_number": reference_number or f"CLIENT-{customer_id}",
                    "description": description,
                    "sale_id": sale_id,
                    "repair_id": repair_order_id,
                    "purchase_id": None,
                    "supplier_payment_id": None,
                    "customer_transaction_id": None,  # Sera mis à jour après commit
                    "expense_id": None,
                    "user_id": processed_by,
                })
            except Exception as e:
                print(f"Erreur écriture trésorerie (transaction client): {e}")

        # 4) Commit
        self.db.commit()
        self.db.refresh(transaction)
        self.db.refresh(customer)
        return transaction

    async def pay_customer_repair(
        self,
        *,
        customer_id: int,
        repair_id: int,
        amount: float | Decimal,
        method: TreasuryPaymentMethod,
        processed_by: int,
        reference_number: Optional[str] = None,
        cash_register_id: Optional[int] = None,
        payment_date: Optional[datetime] = None,
    ) -> tuple[CustomerTransaction, object]:  # object sera RepairPayment
        """
        Enregistre un paiement de réparation via l'interface client.
        Combine transaction client + paiement réparation via FinanceService.

        Returns:
            Tuple (CustomerTransaction, RepairPayment)
        """
        # 1) Paiement de réparation via FinanceService
        from app.core.models.repair import PaymentMethod as RepairPaymentMethod

        # Mapper les méthodes de paiement
        repair_method_map = {
            TreasuryPaymentMethod.cash: RepairPaymentMethod.cash,
            TreasuryPaymentMethod.credit_card: RepairPaymentMethod.credit_card,
            TreasuryPaymentMethod.bank_transfer: RepairPaymentMethod.bank_transfer,
            TreasuryPaymentMethod.check: RepairPaymentMethod.check,
            TreasuryPaymentMethod.other: RepairPaymentMethod.cash,
        }

        repair_payment = await self.pay_repair(
            repair_id=repair_id,
            amount=amount,
            method=repair_method_map.get(method, RepairPaymentMethod.cash),
            processed_by=processed_by,
            reference_number=reference_number,
            cash_register_id=cash_register_id,
            payment_date=payment_date,
        )

        # 2) Transaction client (crédit - diminue la dette)
        customer_transaction = await self.record_customer_transaction(
            customer_id=customer_id,
            amount=float(amount),  # Positif = paiement
            description=f"Paiement réparation #{repair_payment.repair_order.number if hasattr(repair_payment, 'repair_order') else repair_id}",
            transaction_type="payment",
            reference_number=reference_number,
            repair_order_id=repair_id,
            processed_by=processed_by,
            transaction_date=payment_date,
            # Pas d'écriture trésorerie ici car déjà faite par pay_repair
        )

        return customer_transaction, repair_payment

    # ------------------------
    # Gestion des références et traçabilité
    # ------------------------
    def find_payment_by_reference(self, reference: str) -> Optional[Dict[str, Any]]:
        """Trouve un paiement par sa référence dans tous les systèmes"""
        return self.reference_service.find_payment_by_reference(reference)

    def get_related_payments(self, reference: str) -> List[Dict[str, Any]]:
        """Trouve tous les paiements liés à une référence"""
        return self.reference_service.get_related_payments(reference)

    async def sync_payment_status(self, entity_type: str, entity_id: int) -> bool:
        """
        Synchronise le statut de paiement d'une entité avec ses paiements réels.

        Args:
            entity_type: 'repair' ou 'sale'
            entity_id: ID de l'entité

        Returns:
            True si synchronisation réussie, False sinon
        """
        try:
            if entity_type == 'repair':
                from app.core.models.repair import RepairOrder, RepairPayment

                # Récupérer la réparation
                repair = self.db.query(RepairOrder).get(entity_id)
                if not repair:
                    print(f"❌ Réparation {entity_id} introuvable")
                    return False

                # Calculer le total payé
                payments = self.db.query(RepairPayment).filter(
                    RepairPayment.repair_order_id == entity_id
                ).all()

                total_paid = sum(float(p.amount) for p in payments)
                expected_amount = float(repair.final_amount or 0)

                # Déterminer le statut calculé
                if total_paid >= expected_amount and expected_amount > 0:
                    calculated_status = RepairPaymentStatus.PAID
                elif total_paid > 0:
                    calculated_status = RepairPaymentStatus.PARTIAL
                else:
                    calculated_status = RepairPaymentStatus.PENDING

                # Mettre à jour si nécessaire
                if repair.payment_status != calculated_status or repair.total_paid != total_paid:
                    repair.payment_status = calculated_status
                    repair.total_paid = total_paid
                    self.db.commit()
                    print(f"✅ Statut réparation {entity_id} synchronisé: {calculated_status.value}")

                return True

            elif entity_type == 'sale':
                from app.core.models.sale import Sale, Payment

                # Récupérer la vente
                sale = self.db.query(Sale).get(entity_id)
                if not sale:
                    print(f"❌ Vente {entity_id} introuvable")
                    return False

                # Calculer le total payé
                payments = self.db.query(Payment).filter(
                    Payment.sale_id == entity_id
                ).all()

                total_paid = sum(float(p.amount) for p in payments)
                expected_amount = float(sale.final_amount or 0)

                # Déterminer le statut calculé
                if total_paid >= expected_amount and expected_amount > 0:
                    calculated_status = SalePaymentStatus.PAID
                elif total_paid > 0:
                    calculated_status = SalePaymentStatus.PARTIAL
                else:
                    calculated_status = SalePaymentStatus.PENDING

                # Mettre à jour si nécessaire
                if sale.payment_status != calculated_status or sale.total_paid != total_paid:
                    sale.payment_status = calculated_status
                    sale.total_paid = total_paid
                    self.db.commit()
                    print(f"✅ Statut vente {entity_id} synchronisé: {calculated_status.value}")

                return True

        except Exception as e:
            print(f"❌ Erreur lors de la synchronisation du statut: {e}")
            return False

        return False