from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from ..models.inventory import (
    InventoryItem, InventoryItemPydantic,
    InventoryMovement, InventoryMovementPydantic,
    ItemStatus, MovementType
)
from ..models.supplier_finance import PriceHistory, PriceHistoryPydantic
from .base_service import BaseService
from app.utils.event_manager import event_manager, EventType

class InventoryService(BaseService[InventoryItem, InventoryItemPydantic, InventoryItemPydantic]):

    async def get_all(self, skip: int = 0, limit: int = 100) -> List[InventoryItem]:
        # Charger la relation category_relation dès le départ
        from app.core.models.inventory import InventoryItem
        return self.get_all_sync(skip, limit, joined_relations=[InventoryItem.category_relation])
    def __init__(self, db: Session = None):
        # Utiliser une session indépendante si aucune n'est fournie
        if db is None:
            from app.core.models.config import session_factory
            db = session_factory()
        super().__init__(db, InventoryItem)

        # S'abonner aux événements
        self._subscribe_to_events()

    def _subscribe_to_events(self):
        """S'abonne aux événements pertinents pour le service d'inventaire"""
        # S'abonner à l'événement de réception d'un article
        event_manager.subscribe(EventType.PURCHASE_ITEM_RECEIVED, self._handle_purchase_item_received)

        # S'abonner à l'événement de mise à jour du prix d'un article
        event_manager.subscribe(EventType.INVENTORY_PRICE_UPDATED, self._handle_price_updated)

    def _handle_purchase_item_received(self, **kwargs):
        """Gère l'événement de réception d'un article"""
        item_id = kwargs.get('item_id')
        # Utiliser purchase_unit_price en priorité, unit_price comme fallback
        purchase_unit_price = kwargs.get('purchase_unit_price', kwargs.get('unit_price'))

        if item_id and purchase_unit_price:
            # Mettre à jour le prix d'achat de l'article de manière synchrone
            item = self.db.query(InventoryItem).filter(InventoryItem.id == item_id).first()
            if item:
                # Vérifier si le prix a changé
                if item.purchase_price != purchase_unit_price:
                    print(f"Prix d'achat mis à jour pour l'article {item.id} ({item.name}): {item.purchase_price} -> {purchase_unit_price}")
                    item.purchase_price = purchase_unit_price
                    self.db.commit()

    def _handle_price_updated(self, **kwargs):
        """Gère l'événement de mise à jour du prix d'un article"""
        item_id = kwargs.get('item_id')
        new_unit_price = kwargs.get('new_unit_price')

        if item_id and new_unit_price:
            # Mettre à jour le prix de vente de l'article de manière synchrone
            item = self.db.query(InventoryItem).filter(InventoryItem.id == item_id).first()
            if item:
                # Vérifier si le prix a changé
                if item.unit_price != new_unit_price:
                    print(f"Prix de vente mis à jour pour l'article {item.id} ({item.name}): {item.unit_price} -> {new_unit_price}")
                    item.unit_price = new_unit_price
                    self.db.commit()

    async def adjust_stock(self, item_id: int, quantity_change: int, user_id: int, reference: str, notes: Optional[str] = None) -> bool:
        from decimal import Decimal

        item = await self.get(item_id)
        if not item:
            return False

        # Convertir quantity_change en Decimal pour éviter les erreurs de type
        quantity_change_decimal = Decimal(str(quantity_change))
        new_quantity = item.quantity + quantity_change_decimal
        if new_quantity < 0:
            return False

        # Créer le mouvement d'inventaire
        movement = InventoryMovement(
            item_id=item_id,
            quantity=abs(quantity_change),
            type="out" if quantity_change < 0 else "in",
            reference=reference,
            user_id=user_id,
            notes=notes
        )

        # Mettre à jour le stock (convertir en float pour SQLite)
        item.quantity = float(new_quantity)

        # Mettre à jour le statut
        if new_quantity == 0:
            item.status = ItemStatus.OUT_OF_STOCK
        elif new_quantity <= item.minimum_quantity:
            item.status = ItemStatus.LOW_STOCK
        else:
            item.status = ItemStatus.AVAILABLE

        self.db.add(movement)
        self.db.commit()
        return True

    async def get_low_stock_items(self) -> List[InventoryItem]:
        return (
            self.db.query(self.model)
            .filter(
                (self.model.status == ItemStatus.LOW_STOCK) |
                (self.model.status == ItemStatus.OUT_OF_STOCK)
            )
            .all()
        )

    async def get_movements(self, item_id: int, limit: int = 50) -> List[InventoryMovement]:
        return (
            self.db.query(InventoryMovement)
            .filter(InventoryMovement.item_id == item_id)
            .order_by(InventoryMovement.created_at.desc())
            .limit(limit)
            .all()
        )

    async def get_unique_locations(self) -> List[str]:
        """Récupère la liste des emplacements uniques utilisés dans l'inventaire"""
        from sqlalchemy import distinct

        locations = (
            self.db.query(distinct(self.model.location))
            .filter(self.model.is_active == True)
            .all()
        )

        # Convertir les tuples en liste de chaînes
        return [location[0] for location in locations if location[0]]

    async def check_stock_availability(self, product_id: int, quantity: float) -> Dict[str, Any]:
        """
        Vérifie si un produit est disponible en stock en quantité suffisante

        Args:
            product_id: ID du produit à vérifier
            quantity: Quantité demandée

        Returns:
            Un dictionnaire contenant:
            - available (bool): True si le stock est suffisant
            - current_stock (float): Quantité actuellement en stock
            - product (InventoryItem): L'objet produit
            - message (str): Message d'information ou d'erreur
        """
        product = await self.get(product_id)
        if not product:
            return {
                "available": False,
                "current_stock": 0,
                "product": None,
                "message": f"Produit avec ID {product_id} non trouvé"
            }

        # Convertir quantity en Decimal pour éviter les erreurs de type
        from decimal import Decimal
        quantity_decimal = Decimal(str(quantity))

        is_available = product.quantity >= quantity_decimal

        # Déterminer le message en fonction de la disponibilité
        if is_available:
            if product.quantity == quantity_decimal:
                message = "Attention: Cette vente épuisera le stock de ce produit"
            elif product.quantity <= product.minimum_quantity + quantity_decimal:
                remaining = product.quantity - quantity_decimal
                message = f"Attention: Stock bas après cette vente ({remaining} restants)"
            else:
                message = f"Produit disponible ({product.quantity} en stock)"
        else:
            message = f"Stock insuffisant ({product.quantity} disponibles pour {quantity} demandés)"

        return {
            "available": is_available,
            "current_stock": product.quantity,
            "product": product,
            "message": message
        }

    async def reserve_stock(self, product_id: int, quantity: float) -> bool:
        """
        Réserve temporairement du stock pour un produit
        Utile pour les ventes en cours de création

        Args:
            product_id: ID du produit
            quantity: Quantité à réserver

        Returns:
            True si la réservation a réussi, False sinon
        """
        product = await self.get(product_id)
        if not product or product.quantity < quantity:
            return False

        # On pourrait ajouter un champ "reserved_quantity" au modèle InventoryItem
        # Pour l'instant, on ne fait rien de spécial
        return True

    def get_sync(self, id: int) -> Optional[InventoryItem]:
        """Récupère un élément d'inventaire de manière synchrone"""
        return self.db.query(self.model).filter(self.model.id == id).first()

    async def get_by_sku(self, sku: str) -> Optional[InventoryItem]:
        """Récupère un élément d'inventaire par son SKU"""
        return self.db.query(self.model).filter(self.model.sku == sku).first()

    async def get_by_barcode(self, barcode: str) -> Optional[InventoryItem]:
        """Récupère un élément d'inventaire par son code-barres"""
        if not barcode:
            return None
        return self.db.query(self.model).filter(self.model.barcode == barcode).first()

    async def get_by_name(self, name: str) -> Optional[InventoryItem]:
        """Récupère un élément d'inventaire par son nom (recherche exacte)"""
        if not name:
            return None
        return self.db.query(self.model).filter(self.model.name == name).first()

    async def update_purchase_price(
        self,
        item_id: int,
        new_price: float,
        user_id: int,
        purchase_order_id: Optional[int] = None,
        reason: Optional[str] = None,
        update_selling_price: bool = False
    ) -> Dict[str, Any]:
        """
        Met à jour le prix d'achat d'un produit et enregistre l'historique

        Args:
            item_id: ID du produit
            new_price: Nouveau prix d'achat
            user_id: ID de l'utilisateur qui effectue la modification
            purchase_order_id: ID de la commande d'achat associée (optionnel)
            reason: Raison de la modification (optionnel)
            update_selling_price: Si True, met à jour le prix de vente en fonction de la marge

        Returns:
            Dictionnaire contenant les informations sur la mise à jour
        """
        item = await self.get(item_id)
        if not item:
            return {
                "success": False,
                "message": f"Produit avec ID {item_id} non trouvé"
            }

        # Enregistrer les anciens prix
        old_purchase_price = item.purchase_price
        old_unit_price = item.unit_price

        # Mettre à jour le prix d'achat
        item.purchase_price = new_price
        item.last_purchase_date = datetime.now()

        # Mettre à jour le prix de vente si demandé
        new_unit_price = old_unit_price
        if update_selling_price and new_price > 0:
            # Calculer le nouveau prix de vente en fonction de la marge
            new_unit_price = new_price * (1 + item.margin_percent / 100)
            item.unit_price = new_unit_price

        # Créer l'historique des prix
        price_history = PriceHistory(
            item_id=item_id,
            old_purchase_price=old_purchase_price,
            new_purchase_price=new_price,
            old_unit_price=old_unit_price,
            new_unit_price=new_unit_price,
            change_date=datetime.now(),
            change_reason=reason,
            purchase_order_id=purchase_order_id,
            changed_by=user_id
        )

        self.db.add(price_history)
        self.db.commit()

        return {
            "success": True,
            "message": "Prix mis à jour avec succès",
            "old_purchase_price": old_purchase_price,
            "new_purchase_price": new_price,
            "old_unit_price": old_unit_price,
            "new_unit_price": new_unit_price,
            "price_history_id": price_history.id
        }

    async def update_selling_price(
        self,
        item_id: int,
        new_price: float,
        user_id: int,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Met à jour le prix de vente d'un produit et enregistre l'historique

        Args:
            item_id: ID du produit
            new_price: Nouveau prix de vente
            user_id: ID de l'utilisateur qui effectue la modification
            reason: Raison de la modification (optionnel)

        Returns:
            Dictionnaire contenant les informations sur la mise à jour
        """
        item = await self.get(item_id)
        if not item:
            return {
                "success": False,
                "message": f"Produit avec ID {item_id} non trouvé"
            }

        # Enregistrer les anciens prix
        old_purchase_price = item.purchase_price
        old_unit_price = item.unit_price

        # Mettre à jour le prix de vente
        item.unit_price = new_price

        # Mettre à jour la marge si le prix d'achat est supérieur à 0
        if item.purchase_price > 0:
            item.margin_percent = ((new_price - item.purchase_price) / item.purchase_price) * 100

        # Créer l'historique des prix
        price_history = PriceHistory(
            item_id=item_id,
            old_purchase_price=old_purchase_price,
            new_purchase_price=old_purchase_price,  # Pas de changement du prix d'achat
            old_unit_price=old_unit_price,
            new_unit_price=new_price,
            change_date=datetime.now(),
            change_reason=reason,
            changed_by=user_id
        )

        self.db.add(price_history)
        self.db.commit()

        return {
            "success": True,
            "message": "Prix de vente mis à jour avec succès",
            "old_unit_price": old_unit_price,
            "new_unit_price": new_price,
            "margin_percent": item.margin_percent,
            "price_history_id": price_history.id
        }

    async def get_price_history(self, item_id: int, limit: int = 10) -> List[PriceHistory]:
        """
        Récupère l'historique des prix d'un produit

        Args:
            item_id: ID du produit
            limit: Nombre maximum d'entrées à récupérer

        Returns:
            Liste des entrées d'historique des prix
        """
        return (
            self.db.query(PriceHistory)
            .filter(PriceHistory.item_id == item_id)
            .order_by(PriceHistory.change_date.desc())
            .limit(limit)
            .all()
        )

    async def record_movement(
        self,
        item_id: int,
        quantity: float,
        movement_type: MovementType,
        reference: str,
        user_id: int,
        notes: Optional[str] = None
    ) -> bool:
        """
        Enregistre un mouvement d'inventaire et met à jour le stock

        Args:
            item_id: ID de l'article
            quantity: Quantité du mouvement
            movement_type: Type de mouvement (in/out)
            reference: Référence du mouvement (ex: numéro de vente)
            user_id: ID de l'utilisateur effectuant le mouvement
            notes: Notes optionnelles

        Returns:
            True si le mouvement a été enregistré avec succès, False sinon
        """
        try:
            from decimal import Decimal

            # Récupérer l'article
            item = await self.get(item_id)
            if not item:
                return False

            # Convertir quantity en Decimal pour éviter les erreurs de type
            quantity_decimal = Decimal(str(quantity))

            # Vérifier la disponibilité du stock pour les sorties
            if movement_type == MovementType.OUT and item.quantity < quantity_decimal:
                return False

            # Créer le mouvement
            movement = InventoryMovement(
                item_id=item_id,
                quantity=quantity,  # Garder le float original pour le mouvement
                type=movement_type,
                reference=reference,
                user_id=user_id,
                notes=notes
            )

            # Mettre à jour le stock avec des Decimal, puis convertir en float pour SQLite
            current_quantity = Decimal(str(item.quantity))
            if movement_type == MovementType.IN:
                new_quantity = current_quantity + quantity_decimal
            else:  # MovementType.OUT
                new_quantity = current_quantity - quantity_decimal

            item.quantity = float(new_quantity)

            # Mettre à jour le statut
            if item.quantity <= 0:
                item.status = ItemStatus.OUT_OF_STOCK
            elif item.quantity <= item.minimum_quantity:
                item.status = ItemStatus.LOW_STOCK
            else:
                item.status = ItemStatus.AVAILABLE

            # Enregistrer les changements
            self.db.add(movement)
            self.db.commit()
            return True

        except Exception as e:
            print(f"Erreur lors de l'enregistrement du mouvement: {str(e)}")
            self.db.rollback()
            return False
