"""
Service pour la gestion de la valorisation des stocks.
Ce service fait le lien entre les achats et l'inventaire pour suivre les coûts et calculer les valorisations.
"""
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_

from app.core.models.inventory import InventoryItem, ItemStatus, InventoryMovement, MovementType
from app.core.models.purchasing import PurchaseOrder, PurchaseOrderItem, OrderStatus
from app.core.models.supplier_finance import PriceHistory
from app.core.services.inventory_service import InventoryService
from app.core.services.purchasing_service import PurchasingService
from app.utils.database import SessionLocal

class InventoryValuationService:
    """Service pour la gestion de la valorisation des stocks"""

    def __init__(self, db: Session = None):
        """Initialise le service avec une session de base de données"""
        if db is None:
            db = SessionLocal()
        self.db = db
        self.inventory_service = InventoryService(db)
        self.purchasing_service = PurchasingService(db)

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def calculate_inventory_value_sync(self) -> Dict[str, Any]:
        """
        Calcule la valeur totale de l'inventaire (version synchrone)

        Returns:
            Dictionnaire contenant les informations de valorisation
        """
        try:
            # Récupérer tous les articles actifs
            items = (
                self.db.query(InventoryItem)
                .filter(InventoryItem.is_active == True)
                .all()
            )

            # Calculer les totaux
            total_value = 0.0
            total_quantity = 0

            for item in items:
                item_value = item.quantity * item.purchase_price
                total_value += item_value
                total_quantity += item.quantity

            return {
                "total_value": total_value,
                "total_items": len(items),
                "total_quantity": total_quantity,
                "date": datetime.now()
            }
        except Exception as e:
            print(f"Erreur lors du calcul de la valeur de l'inventaire: {e}")
            # En cas d'erreur, retourner des valeurs par défaut à zéro
            return {
                "total_value": 0.0,
                "total_items": 0,
                "total_quantity": 0,
                "date": datetime.now()
            }

    async def calculate_inventory_value(self) -> Dict[str, Any]:
        """
        Calcule la valeur totale de l'inventaire (version asynchrone)

        Returns:
            Dictionnaire contenant les informations de valorisation
        """
        return self.calculate_inventory_value_sync()

    def calculate_category_values_sync(self) -> List[Dict[str, Any]]:
        """
        Calcule la valeur de l'inventaire par catégorie (version synchrone)

        Returns:
            Liste des valorisations par catégorie
        """
        # Récupérer tous les articles actifs
        items = (
            self.db.query(InventoryItem)
            .filter(InventoryItem.is_active == True)
            .all()
        )

        # Initialiser les résultats par catégorie
        category_values = {}

        # Calculer la valeur par catégorie
        for item in items:
            category = item.category.value
            item_value = item.quantity * item.purchase_price

            if category not in category_values:
                category_values[category] = {
                    "category": category,
                    "total_value": 0.0,
                    "total_items": 0,
                    "total_quantity": 0
                }

            category_values[category]["total_value"] += item_value
            category_values[category]["total_items"] += 1
            category_values[category]["total_quantity"] += item.quantity

        # Convertir le dictionnaire en liste
        results = list(category_values.values())

        # Trier les résultats par valeur décroissante
        results.sort(key=lambda x: x["total_value"], reverse=True)

        return results

    async def calculate_category_values(self) -> List[Dict[str, Any]]:
        """
        Calcule la valeur de l'inventaire par catégorie (version asynchrone)

        Returns:
            Liste des valorisations par catégorie
        """
        return self.calculate_category_values_sync()

    def calculate_supplier_values_sync(self) -> List[Dict[str, Any]]:
        """
        Calcule la valeur de l'inventaire par fournisseur (version synchrone)

        Returns:
            Liste des valorisations par fournisseur
        """
        # Récupérer tous les articles actifs
        items = (
            self.db.query(InventoryItem)
            .filter(InventoryItem.is_active == True)
            .all()
        )

        # Initialiser les résultats par fournisseur
        supplier_values = {}

        # Calculer la valeur par fournisseur
        for item in items:
            supplier_id = item.supplier_id
            item_value = item.quantity * item.purchase_price

            if supplier_id not in supplier_values:
                supplier = item.supplier
                supplier_values[supplier_id] = {
                    "supplier_id": supplier_id,
                    "supplier_name": supplier.name if supplier else "N/A",
                    "total_value": 0.0,
                    "total_items": 0,
                    "total_quantity": 0
                }

            supplier_values[supplier_id]["total_value"] += item_value
            supplier_values[supplier_id]["total_items"] += 1
            supplier_values[supplier_id]["total_quantity"] += item.quantity

        # Convertir le dictionnaire en liste
        results = list(supplier_values.values())

        # Trier les résultats par valeur décroissante
        results.sort(key=lambda x: x["total_value"], reverse=True)

        return results

    async def calculate_supplier_values(self) -> List[Dict[str, Any]]:
        """
        Calcule la valeur de l'inventaire par fournisseur (version asynchrone)

        Returns:
            Liste des valorisations par fournisseur
        """
        return self.calculate_supplier_values_sync()

    async def get_price_history(self, product_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Récupère l'historique des prix d'un produit

        Args:
            product_id: ID du produit
            limit: Nombre maximum d'entrées à récupérer

        Returns:
            Liste des changements de prix du produit
        """
        # Récupérer le produit
        product = await self.inventory_service.get(product_id)
        if not product:
            raise ValueError(f"Produit avec ID {product_id} non trouvé")

        # Récupérer l'historique des prix
        price_history = (
            self.db.query(PriceHistory)
            .filter(PriceHistory.product_id == product_id)
            .order_by(PriceHistory.date.desc())
            .limit(limit)
            .all()
        )

        # Préparer les résultats
        results = []
        for entry in price_history:
            results.append({
                "id": entry.id,
                "date": entry.date,
                "old_price": entry.old_price,
                "new_price": entry.new_price,
                "purchase_order_id": entry.purchase_order_id,
                "reason": entry.reason,
                "user_id": entry.user_id,
                "user_name": entry.user.username if entry.user else "N/A"
            })

        return results

    async def calculate_inventory_turnover(self, period_days: int = 365) -> Dict[str, Any]:
        """
        Calcule la rotation des stocks (version asynchrone)

        Args:
            period_days: Nombre de jours à considérer (par défaut 365 jours)

        Returns:
            Dictionnaire contenant les informations de rotation des stocks
        """
        try:
            start_date = datetime.now() - timedelta(days=period_days)

            # Calculer la valeur actuelle de l'inventaire
            inventory_value = await self.calculate_inventory_value()
            current_inventory_value = inventory_value["total_value"]

            # Récupérer les mouvements de sortie pour calculer le coût des marchandises vendues
            movements = (
                self.db.query(InventoryMovement)
                .filter(
                    InventoryMovement.type == "out",
                    InventoryMovement.created_at >= start_date
                )
                .all()
            )

            # Calculer le coût des marchandises vendues (COGS)
            cogs = 0.0
            for movement in movements:
                # Récupérer l'article associé au mouvement
                item = self.db.query(InventoryItem).get(movement.item_id)
                if item:
                    # Utiliser le prix d'achat de l'article pour calculer le coût
                    cogs += movement.quantity * item.purchase_price

            # Calculer le ratio de rotation des stocks et les jours en inventaire
            if current_inventory_value > 0:
                turnover_ratio = cogs / current_inventory_value
                days_in_inventory = period_days / turnover_ratio if turnover_ratio > 0 else 0
            else:
                turnover_ratio = 0
                days_in_inventory = 0

            return {
                "period_days": period_days,
                "start_date": start_date,
                "end_date": datetime.now(),
                "average_inventory_value": current_inventory_value,
                "cost_of_goods_sold": cogs,
                "turnover_ratio": turnover_ratio,
                "days_in_inventory": days_in_inventory
            }
        except Exception as e:
            print(f"Erreur lors du calcul de la rotation des stocks: {e}")
            # En cas d'erreur, retourner des valeurs par défaut à zéro
            return {
                "period_days": period_days,
                "start_date": start_date,
                "end_date": datetime.now(),
                "average_inventory_value": 0.0,
                "cost_of_goods_sold": 0.0,
                "turnover_ratio": 0.0,
                "days_in_inventory": 0.0
            }

    def get_slow_moving_items_sync(self, threshold_days: int = 90, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Récupère les articles à faible rotation (version synchrone)

        Args:
            threshold_days: Nombre de jours sans mouvement pour considérer un article comme à faible rotation
            limit: Nombre maximum d'articles à récupérer

        Returns:
            Liste des articles à faible rotation
        """
        # Calculer la date seuil
        threshold_date = datetime.now() - timedelta(days=threshold_days)

        # Récupérer tous les articles actifs
        items = (
            self.db.query(InventoryItem)
            .filter(
                InventoryItem.is_active == True,
                InventoryItem.quantity > 0
            )
            .all()
        )

        # Préparer les résultats
        results = []
        for item in items:
            # Récupérer le dernier mouvement de sortie
            last_movement = (
                self.db.query(InventoryMovement)
                .filter(
                    InventoryMovement.item_id == item.id,
                    InventoryMovement.type == "out"
                )
                .order_by(InventoryMovement.created_at.desc())
                .first()
            )

            # Calculer le nombre de jours depuis le dernier mouvement
            if last_movement:
                days_since_last_movement = (datetime.now() - last_movement.created_at).days
            else:
                # Si aucun mouvement, utiliser la date de création de l'article
                days_since_last_movement = (datetime.now() - item.created_at).days

            # Calculer la valeur de l'article
            item_value = item.quantity * item.purchase_price

            # Ajouter l'article aux résultats s'il dépasse le seuil
            if days_since_last_movement >= threshold_days:
                results.append({
                    "product_id": item.id,
                    "product_name": item.name,
                    "sku": item.sku,
                    "quantity": item.quantity,
                    "purchase_price": item.purchase_price,
                    "item_value": item_value,
                    "days_since_last_movement": days_since_last_movement,
                    "last_movement_date": last_movement.created_at if last_movement else None
                })

        # Trier les résultats par nombre de jours décroissant
        results.sort(key=lambda x: x["days_since_last_movement"], reverse=True)

        # Limiter le nombre de résultats
        return results[:limit]

    async def get_slow_moving_items(self, threshold_days: int = 90, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Récupère les articles à faible rotation (version asynchrone)

        Args:
            threshold_days: Nombre de jours sans mouvement pour considérer un article comme à faible rotation
            limit: Nombre maximum d'articles à récupérer

        Returns:
            Liste des articles à faible rotation
        """
        return self.get_slow_moving_items_sync(threshold_days, limit)

    def get_high_value_items_sync(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Récupère les articles de haute valeur (version synchrone)

        Args:
            limit: Nombre maximum d'articles à récupérer

        Returns:
            Liste des articles de haute valeur
        """
        # Récupérer tous les articles actifs
        items = (
            self.db.query(InventoryItem)
            .filter(
                InventoryItem.is_active == True,
                InventoryItem.quantity > 0
            )
            .all()
        )

        # Préparer les résultats
        results = []
        for item in items:
            # Calculer la valeur de l'article
            item_value = item.quantity * item.purchase_price

            results.append({
                "product_id": item.id,
                "product_name": item.name,
                "sku": item.sku,
                "quantity": item.quantity,
                "purchase_price": item.purchase_price,
                "item_value": item_value
            })

        # Trier les résultats par valeur décroissante
        results.sort(key=lambda x: x["item_value"], reverse=True)

        # Limiter le nombre de résultats
        return results[:limit]

    async def get_high_value_items(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Récupère les articles de haute valeur (version asynchrone)

        Args:
            limit: Nombre maximum d'articles à récupérer

        Returns:
            Liste des articles de haute valeur
        """
        return self.get_high_value_items_sync(limit)

    def calculate_abc_analysis_sync(self) -> Dict[str, Any]:
        """
        Effectue une analyse ABC de l'inventaire (version synchrone)

        Returns:
            Dictionnaire contenant les résultats de l'analyse ABC
        """
        # Récupérer tous les articles actifs
        items = (
            self.db.query(InventoryItem)
            .filter(
                InventoryItem.is_active == True,
                InventoryItem.quantity > 0
            )
            .all()
        )

        # Calculer la valeur totale de l'inventaire
        total_value = 0.0
        item_values = []

        for item in items:
            item_value = item.quantity * item.purchase_price
            total_value += item_value

            item_values.append({
                "product_id": item.id,
                "product_name": item.name,
                "sku": item.sku,
                "value": item_value
            })

        # Trier les articles par valeur décroissante
        item_values.sort(key=lambda x: x["value"], reverse=True)

        # Initialiser les catégories
        category_a = []
        category_b = []
        category_c = []

        # Calculer les valeurs cumulées
        cumulative_value = 0.0

        for item in item_values:
            cumulative_value += item["value"]
            cumulative_percent = (cumulative_value / total_value) * 100

            # Ajouter l'article à la catégorie appropriée
            if cumulative_percent <= 80:
                category_a.append(item)
            elif cumulative_percent <= 95:
                category_b.append(item)
            else:
                category_c.append(item)

        # Calculer les totaux par catégorie
        category_a_value = sum(item["value"] for item in category_a)
        category_b_value = sum(item["value"] for item in category_b)
        category_c_value = sum(item["value"] for item in category_c)

        return {
            "total_value": total_value,
            "total_items": len(items),
            "category_a": {
                "items": category_a,
                "count": len(category_a),
                "value": category_a_value,
                "percent_of_items": (len(category_a) / len(items)) * 100 if items else 0,
                "percent_of_value": (category_a_value / total_value) * 100 if total_value > 0 else 0
            },
            "category_b": {
                "items": category_b,
                "count": len(category_b),
                "value": category_b_value,
                "percent_of_items": (len(category_b) / len(items)) * 100 if items else 0,
                "percent_of_value": (category_b_value / total_value) * 100 if total_value > 0 else 0
            },
            "category_c": {
                "items": category_c,
                "count": len(category_c),
                "value": category_c_value,
                "percent_of_items": (len(category_c) / len(items)) * 100 if items else 0,
                "percent_of_value": (category_c_value / total_value) * 100 if total_value > 0 else 0
            }
        }

    async def calculate_abc_analysis(self) -> Dict[str, Any]:
        """
        Effectue une analyse ABC de l'inventaire (version asynchrone)

        Returns:
            Dictionnaire contenant les résultats de l'analyse ABC
        """
        return self.calculate_abc_analysis_sync()
