from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import or_
from pydantic import BaseModel

from app.core.models.permission import (
    Permission, DBRole, PermissionCategory,
    PermissionPydantic
)
from app.core.models.user_role import UserRole, UserRolePydantic
from app.core.services.base_service import BaseService
from app.core.services.audit_service import AuditService
from app.core.models.audit import AuditActionType

# Pydantic models for validation
class PermissionCreate(BaseModel):
    code: str
    name: str
    description: Optional[str] = None
    category: str

class RoleCreate(BaseModel):
    name: str
    description: Optional[str] = None
    parent_id: Optional[int] = None
    permission_ids: List[int] = []

class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    parent_id: Optional[int] = None
    permission_ids: Optional[List[int]] = None

class PermissionService(BaseService[Permission, PermissionPydantic, PermissionPydantic]):
    """Service pour la gestion des permissions"""
    
    def __init__(self, db: Session):
        super().__init__(db, Permission)
        self.audit_service = AuditService(db)
    
    async def create_permission(
        self,
        permission_data: PermissionCreate,
        created_by_id: Optional[int] = None
    ) -> Permission:
        """
        Créer une nouvelle permission
        
        Args:
            permission_data: Données de la permission
            created_by_id: ID de l'utilisateur qui crée la permission
            
        Returns:
            La permission créée
        """
        # Vérifier si le code existe déjà
        existing = self.db.query(self.model).filter(
            self.model.code == permission_data.code
        ).first()
        
        if existing:
            raise ValueError(f"Permission with code '{permission_data.code}' already exists")
        
        # Créer la permission
        permission = Permission(
            code=permission_data.code,
            name=permission_data.name,
            description=permission_data.description,
            category=permission_data.category
        )
        
        self.db.add(permission)
        self.db.commit()
        self.db.refresh(permission)
        
        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.CREATE,
            user_id=created_by_id,
            entity_type="permission",
            entity_id=permission.id,
            details={"code": permission.code}
        )
        
        return permission
    
    async def get_permissions_by_category(self, category: PermissionCategory) -> List[Permission]:
        """
        Récupérer les permissions par catégorie
        
        Args:
            category: Catégorie de permissions
            
        Returns:
            Liste des permissions dans cette catégorie
        """
        return self.db.query(self.model).filter(
            self.model.category == category
        ).all()
    
    async def search_permissions(
        self,
        search_term: Optional[str] = None,
        category: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Permission]:
        """
        Rechercher des permissions avec filtres
        
        Args:
            search_term: Terme de recherche (code, nom)
            category: Catégorie de permissions
            skip: Nombre d'entrées à sauter
            limit: Nombre maximum d'entrées à retourner
            
        Returns:
            Liste des permissions correspondantes
        """
        query = self.db.query(self.model)
        
        if search_term:
            query = query.filter(
                or_(
                    self.model.code.ilike(f"%{search_term}%"),
                    self.model.name.ilike(f"%{search_term}%"),
                    self.model.description.ilike(f"%{search_term}%")
                )
            )
        
        if category:
            query = query.filter(self.model.category == category)
        
        return query.order_by(self.model.category, self.model.code).offset(skip).limit(limit).all()

class RoleService(BaseService[DBRole, UserRolePydantic, UserRolePydantic]):
    """Service pour la gestion des rôles"""
    
    def __init__(self, db: Session):
        super().__init__(db, DBRole)
        self.audit_service = AuditService(db)
    
    async def create_role(
        self,
        role_data: RoleCreate,
        created_by_id: Optional[int] = None
    ) -> DBRole:
        """
        Créer un nouveau rôle
        
        Args:
            role_data: Données du rôle
            created_by_id: ID de l'utilisateur qui crée le rôle
            
        Returns:
            Le rôle créé
        """
        # Vérifier si le nom existe déjà
        existing = self.db.query(self.model).filter(
            self.model.name == role_data.name
        ).first()
        
        if existing:
            raise ValueError(f"Role with name '{role_data.name}' already exists")
        
        # Créer le rôle
        role = DBRole(
            name=role_data.name,
            description=role_data.description,
            parent_id=role_data.parent_id,
            is_system=False
        )
        
        self.db.add(role)
        self.db.flush()  # Pour obtenir l'ID du rôle
        
        # Assigner les permissions
        for permission_id in role_data.permission_ids:
            permission = self.db.query(Permission).get(permission_id)
            if permission:
                role.permissions.append(permission)
        
        self.db.commit()
        self.db.refresh(role)
        
        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.CREATE,
            user_id=created_by_id,
            entity_type="role",
            entity_id=role.id,
            details={"name": role.name}
        )
        
        return role
    
    async def update_role(
        self,
        role_id: int,
        role_data: RoleUpdate,
        updated_by_id: Optional[int] = None
    ) -> DBRole:
        """
        Mettre à jour un rôle
        
        Args:
            role_id: ID du rôle à mettre à jour
            role_data: Données à mettre à jour
            updated_by_id: ID de l'utilisateur qui effectue la mise à jour
            
        Returns:
            Le rôle mis à jour
        """
        role = await self.get(role_id)
        if not role:
            raise ValueError("Role not found")
        
        # Vérifier si c'est un rôle système
        if role.is_system:
            raise ValueError("System roles cannot be modified")
        
        # Sauvegarder les anciennes valeurs pour l'audit
        old_values = {
            "name": role.name,
            "description": role.description,
            "parent_id": role.parent_id,
            "permission_ids": [p.id for p in role.permissions]
        }
        
        # Mettre à jour les champs
        if role_data.name is not None and role_data.name != role.name:
            # Vérifier si le nouveau nom existe déjà
            existing = self.db.query(self.model).filter(
                self.model.name == role_data.name,
                self.model.id != role_id
            ).first()
            
            if existing:
                raise ValueError(f"Role with name '{role_data.name}' already exists")
            
            role.name = role_data.name
        
        if role_data.description is not None:
            role.description = role_data.description
        
        if role_data.parent_id is not None:
            # Vérifier que le parent existe
            if role_data.parent_id > 0:
                parent = self.db.query(self.model).get(role_data.parent_id)
                if not parent:
                    raise ValueError("Parent role not found")
                
                # Vérifier qu'il n'y a pas de cycle
                if await self._would_create_cycle(role_id, role_data.parent_id):
                    raise ValueError("This would create a cycle in the role hierarchy")
            
            role.parent_id = role_data.parent_id
        
        # Mettre à jour les permissions si nécessaire
        if role_data.permission_ids is not None:
            # Supprimer les permissions existantes
            role.permissions = []
            
            # Ajouter les nouvelles permissions
            for permission_id in role_data.permission_ids:
                permission = self.db.query(Permission).get(permission_id)
                if permission:
                    role.permissions.append(permission)
        
        self.db.commit()
        self.db.refresh(role)
        
        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.UPDATE,
            user_id=updated_by_id,
            entity_type="role",
            entity_id=role.id,
            details={
                "old_values": old_values,
                "new_values": {
                    "name": role.name,
                    "description": role.description,
                    "parent_id": role.parent_id,
                    "permission_ids": [p.id for p in role.permissions]
                }
            }
        )
        
        return role
    
    async def delete_role(
        self,
        role_id: int,
        deleted_by_id: Optional[int] = None
    ) -> bool:
        """
        Supprimer un rôle
        
        Args:
            role_id: ID du rôle à supprimer
            deleted_by_id: ID de l'utilisateur qui effectue la suppression
            
        Returns:
            True si la suppression a réussi, False sinon
        """
        role = await self.get(role_id)
        if not role:
            return False
        
        # Vérifier si c'est un rôle système
        if role.is_system:
            raise ValueError("System roles cannot be deleted")
        
        # Vérifier si le rôle est utilisé
        user_roles = self.db.query(UserRole).filter(UserRole.role_id == role_id).count()
        if user_roles > 0:
            raise ValueError("Role is assigned to users and cannot be deleted")
        
        # Vérifier si le rôle a des enfants
        children = self.db.query(self.model).filter(self.model.parent_id == role_id).count()
        if children > 0:
            raise ValueError("Role has child roles and cannot be deleted")
        
        # Journaliser l'action avant la suppression
        await self.audit_service.log_action(
            action=AuditActionType.DELETE,
            user_id=deleted_by_id,
            entity_type="role",
            entity_id=role.id,
            details={"name": role.name}
        )
        
        # Supprimer le rôle
        self.db.delete(role)
        self.db.commit()
        
        return True
    
    async def get_role_with_permissions(self, role_id: int) -> Optional[DBRole]:
        """
        Récupérer un rôle avec ses permissions
        
        Args:
            role_id: ID du rôle
            
        Returns:
            Le rôle avec ses permissions ou None
        """
        return self.db.query(self.model).filter(self.model.id == role_id).first()
    
    async def get_roles_hierarchy(self) -> List[Dict[str, Any]]:
        """
        Récupérer la hiérarchie des rôles
        
        Returns:
            Liste des rôles avec leur hiérarchie
        """
        # Récupérer tous les rôles
        roles = self.db.query(self.model).all()
        
        # Construire la hiérarchie
        roles_dict = {role.id: {
            "id": role.id,
            "name": role.name,
            "description": role.description,
            "is_system": role.is_system,
            "parent_id": role.parent_id,
            "children": []
        } for role in roles}
        
        # Organiser les rôles en hiérarchie
        root_roles = []
        for role_id, role_data in roles_dict.items():
            if role_data["parent_id"] is None:
                root_roles.append(role_data)
            else:
                parent_id = role_data["parent_id"]
                if parent_id in roles_dict:
                    roles_dict[parent_id]["children"].append(role_data)
        
        return root_roles
    
    async def search_roles(
        self,
        search_term: Optional[str] = None,
        include_system: bool = True,
        skip: int = 0,
        limit: int = 100
    ) -> List[DBRole]:
        """
        Rechercher des rôles avec filtres
        
        Args:
            search_term: Terme de recherche (nom, description)
            include_system: Inclure les rôles système
            skip: Nombre d'entrées à sauter
            limit: Nombre maximum d'entrées à retourner
            
        Returns:
            Liste des rôles correspondants
        """
        query = self.db.query(self.model)
        
        if search_term:
            query = query.filter(
                or_(
                    self.model.name.ilike(f"%{search_term}%"),
                    self.model.description.ilike(f"%{search_term}%")
                )
            )
        
        if not include_system:
            query = query.filter(self.model.is_system == False)
        
        return query.order_by(self.model.name).offset(skip).limit(limit).all()
    
    async def _would_create_cycle(self, role_id: int, new_parent_id: int) -> bool:
        """
        Vérifie si l'ajout d'un parent créerait un cycle dans la hiérarchie
        
        Args:
            role_id: ID du rôle
            new_parent_id: ID du nouveau parent
            
        Returns:
            True si un cycle serait créé, False sinon
        """
        # Si le nouveau parent est le rôle lui-même, c'est un cycle
        if role_id == new_parent_id:
            return True
        
        # Vérifier si le rôle est un ancêtre du nouveau parent
        current_id = new_parent_id
        visited = set()
        
        while current_id is not None:
            if current_id in visited:
                # Cycle détecté dans la hiérarchie existante
                return True
            
            visited.add(current_id)
            
            if current_id == role_id:
                # Le rôle est un ancêtre du nouveau parent
                return True
            
            # Passer au parent suivant
            parent = self.db.query(self.model).get(current_id)
            if not parent:
                break
            
            current_id = parent.parent_id
        
        return False
