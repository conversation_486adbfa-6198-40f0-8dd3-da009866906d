"""
Service pour la gestion des produits.
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from ..models.inventory import InventoryItem
from .base_service import BaseService

class ProductService(BaseService):
    """Service pour la gestion des produits"""
    
    def __init__(self, db: Session):
        """Initialise le service avec une session de base de données"""
        super().__init__(db, InventoryItem)
        
    async def get_all_products(self) -> List[InventoryItem]:
        """
        Récupère tous les produits.
        
        Returns:
            Liste des produits
        """
        return self.db.query(self.model).all()
        
    async def get_product(self, product_id: int) -> Optional[InventoryItem]:
        """
        Récupère un produit par son ID.
        
        Args:
            product_id: ID du produit
            
        Returns:
            Le produit ou None s'il n'existe pas
        """
        return self.db.query(self.model).filter(self.model.id == product_id).first()
        
    async def search_products(self, query: str) -> List[InventoryItem]:
        """
        Recherche des produits par nom, référence ou description.
        
        Args:
            query: Texte de recherche
            
        Returns:
            Liste des produits correspondants
        """
        search = f"%{query}%"
        return (
            self.db.query(self.model)
            .filter(
                (self.model.name.ilike(search)) |
                (self.model.sku.ilike(search)) |
                (self.model.description.ilike(search))
            )
            .all()
        )
        
    async def get_products_by_category(self, category_id: int) -> List[InventoryItem]:
        """
        Récupère les produits d'une catégorie.
        
        Args:
            category_id: ID de la catégorie
            
        Returns:
            Liste des produits de la catégorie
        """
        return (
            self.db.query(self.model)
            .filter(self.model.category_id == category_id)
            .all()
        )
        
    async def get_products_in_stock(self) -> List[InventoryItem]:
        """
        Récupère les produits en stock.
        
        Returns:
            Liste des produits en stock
        """
        return (
            self.db.query(self.model)
            .filter(self.model.quantity > 0)
            .all()
        )
        
    async def get_products_out_of_stock(self) -> List[InventoryItem]:
        """
        Récupère les produits en rupture de stock.
        
        Returns:
            Liste des produits en rupture de stock
        """
        return (
            self.db.query(self.model)
            .filter(self.model.quantity <= 0)
            .all()
        )
        
    async def get_products_below_threshold(self) -> List[InventoryItem]:
        """
        Récupère les produits dont le stock est inférieur au seuil d'alerte.
        
        Returns:
            Liste des produits dont le stock est inférieur au seuil d'alerte
        """
        return (
            self.db.query(self.model)
            .filter(self.model.quantity <= self.model.alert_threshold)
            .all()
        )
