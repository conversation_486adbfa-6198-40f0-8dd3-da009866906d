from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from app.core.models.purchasing import (
    PurchaseOrder, PurchaseOrderItem
)
from app.core.models.supplier import Supplier
from app.core.schemas.purchasing import (
    PurchaseOrder as PurchaseOrderSchema, PurchaseOrderCreate, PurchaseOrderUpdate
)
from app.core.enums.purchasing import OrderStatus
from app.core.models.inventory import InventoryItem, ItemStatus
from app.core.models.notification import NotificationType, NotificationPriority, NotificationChannel
from app.core.models.supplier_finance import SupplierInvoice, SupplierPayment, InvoiceStatus, PaymentMethod
from app.core.services.base_service import BaseService
from app.core.services.inventory_service import InventoryService
from app.core.services.notification_service import NotificationService
from app.core.services.supplier_finance_service import SupplierFinanceService
from app.core.services.accounting_service import AccountingService
from app.utils.event_manager import event_manager, EventType

class PurchasingService(BaseService[PurchaseOrder, PurchaseOrderCreate, PurchaseOrderUpdate]):
    def __init__(self, db: Session):
        super().__init__(db, PurchaseOrder)
        self.inventory_service = InventoryService(db)
        self.notification_service = NotificationService(db)
        self.supplier_finance_service = SupplierFinanceService(db)
        self.accounting_service = AccountingService(db)

    def _get_tax_rate(self) -> float:
        """Récupère le taux de TVA depuis les paramètres de l'application"""
        try:
            from app.core.models.settings import Setting
            setting = self.db.query(Setting).filter(Setting.key == "general.tax_rate").first()
            if setting:
                return float(setting.value)
            else:
                # Valeur par défaut si le paramètre n'existe pas
                return 0.0
        except Exception as e:
            print(f"Erreur lors de la récupération du taux de TVA: {e}")
            return 0.0

    def get_sync(self, id: int, joined_relations: list | None = None) -> PurchaseOrder | None:
        """Version synchrone de get() avec possibilité de charger des relations."""
        from sqlalchemy.orm import joinedload
        query = self.db.query(PurchaseOrder)
        if joined_relations:
            for rel in joined_relations:
                query = query.options(joinedload(rel))
        return query.filter(PurchaseOrder.id == id).first()

    async def get_all_with_items(self) -> list[PurchaseOrder]:
        """Récupère toutes les commandes avec les relations utiles (supplier, items, users)."""
        from sqlalchemy.orm import joinedload
        query = (
            self.db.query(PurchaseOrder)
            .options(
                joinedload(PurchaseOrder.supplier),
                joinedload(PurchaseOrder.items),
                joinedload(PurchaseOrder.creator),
                joinedload(PurchaseOrder.approver),
                joinedload(PurchaseOrder.submitter),
            )
            .order_by(PurchaseOrder.id.desc())
        )
        return query.all()

    async def create_purchase_order(
        self,
        supplier_id: int,
        items: List[dict],
        user_id: int
    ) -> PurchaseOrder:
        # Vérifier le fournisseur
        supplier = self.db.query(Supplier).get(supplier_id)
        if not supplier:
            raise ValueError("Supplier not found")

        # Créer la commande
        order = PurchaseOrder(
            supplier_id=supplier_id,
            status=OrderStatus.DRAFT,
            created_by=user_id,
            created_at=datetime.now(),
            order_date=datetime.now()
        )

        # Ajouter les articles
        subtotal_amount = 0
        for item in items:
            product = await self.inventory_service.get(item["product_id"])
            if not product:
                raise ValueError(f"Product {item['product_id']} not found")

            # Récupérer les données de l'article
            quantity = item["quantity"]
            purchase_unit_price = item.get("purchase_unit_price", product.purchase_price)

            # Calculer les montants financiers
            subtotal = quantity * purchase_unit_price
            discount_percent = item.get("discount_percent", 0)
            discount_amount = subtotal * (discount_percent / 100)
            # Récupérer le taux de TVA depuis les paramètres de l'application
            default_tax_rate = self._get_tax_rate()
            tax_percent = item.get("tax_percent", default_tax_rate)
            tax_amount = (subtotal - discount_amount) * (tax_percent / 100)
            total_price = subtotal - discount_amount + tax_amount

            # Créer l'article de commande
            order_item = PurchaseOrderItem(
                product_id=item["product_id"],
                quantity=quantity,
                purchase_unit_price=purchase_unit_price,
                discount_percent=discount_percent,
                discount_amount=discount_amount,
                tax_percent=tax_percent,
                tax_amount=tax_amount,
                subtotal=subtotal,
                total_price=total_price,
                remaining_quantity=quantity,
                unit_of_measure=item.get("unit_of_measure", "pcs")
            )

            # Ajouts facultatifs
            if "delivery_date" in item:
                order_item.delivery_date = item["delivery_date"]
            if "specifications" in item:
                order_item.specifications = item["specifications"]
            if "received_quantity" in item:
                order_item.received_quantity = item["received_quantity"]

            # Ajouter l'article à la commande
            order.items.append(order_item)
            subtotal_amount += subtotal

        # Calculer les montants financiers de la commande (alignés sur les articles)
        order.subtotal_amount = sum(i.subtotal for i in order.items)
        order.discount_percent = 0  # Pas de remise globale par défaut
        order.discount_amount = sum(i.discount_amount for i in order.items)
        # TVA globale déduite des items; si vide, utiliser le taux configuré
        items_tax = sum(i.tax_amount for i in order.items)
        default_tax_rate = self._get_tax_rate()
        order.tax_percent = default_tax_rate
        order.tax_amount = items_tax if items_tax is not None else order.subtotal_amount * (default_tax_rate / 100)
        order.shipping_amount = 0  # Pas de frais de livraison par défaut
        order.total_amount = sum(i.total_price for i in order.items) + order.shipping_amount

        # Ajouter la commande à la base de données
        self.db.add(order)
        self.db.commit()

        return order

    async def create_with_items(self, order_data: Dict[str, Any], items_data: List[Dict[str, Any]]) -> PurchaseOrder:
        """Crée une commande d'achat avec ses articles en calculant les montants.

        - N'insère PAS de doublons: crée de nouveaux objets d'articles à partir des données fournies.
        - Calcule subtotal, remises, TVA et total TTC.
        """
        # Extraire champs ordre
        supplier_id = order_data.get("supplier_id")
        if not supplier_id:
            raise ValueError("supplier_id est requis")

        order = PurchaseOrder(
            supplier_id=supplier_id,
            po_number=order_data.get("po_number"),
            status=order_data.get("status", OrderStatus.DRAFT),
            order_date=order_data.get("order_date", datetime.now()),
            expected_delivery=order_data.get("expected_delivery"),
            currency=order_data.get("currency", "DA"),
            payment_terms=order_data.get("payment_terms"),
            shipping_terms=order_data.get("shipping_terms"),
            notes=order_data.get("notes"),
        )

        shipping_amount = float(order_data.get("shipping_amount", 0) or 0)
        discount_percent = float(order_data.get("discount_percent", 0) or 0)
        # Utiliser le taux de TVA configuré dans les paramètres
        default_tax_rate = self._get_tax_rate()
        tax_percent = float(order_data.get("tax_percent", default_tax_rate) or 0)

        subtotal_amount = 0.0
        # Créer les items à partir de dicts pour éviter toute réutilisation d'objets
        for it in items_data:
            product_id = it.get("product_id")
            quantity = float(it.get("quantity", 0) or 0)
            purchase_unit_price = float(it.get("purchase_unit_price", 0) or 0)
            if not product_id or quantity <= 0:
                raise ValueError("Chaque article doit avoir product_id et quantity > 0")

            subtotal = quantity * purchase_unit_price
            # Remise au niveau article (si fournie)
            item_discount_percent = float(it.get("discount_percent", 0) or 0)
            item_discount_amount = subtotal * (item_discount_percent / 100.0)
            # TVA au niveau article (si fournie) sinon TVA globale
            item_tax_percent = float(it.get("tax_percent", tax_percent) or 0)
            item_tax_amount = (subtotal - item_discount_amount) * (item_tax_percent / 100.0)
            total_price = subtotal - item_discount_amount + item_tax_amount

            order_item = PurchaseOrderItem(
                product_id=product_id,
                quantity=quantity,
                purchase_unit_price=purchase_unit_price,
                discount_percent=item_discount_percent,
                discount_amount=item_discount_amount,
                tax_percent=item_tax_percent,
                tax_amount=item_tax_amount,
                subtotal=subtotal,
                total_price=total_price,
                remaining_quantity=quantity,
                unit_of_measure=it.get("unit_of_measure", "pcs")
            )
            if it.get("delivery_date"):
                order_item.delivery_date = it.get("delivery_date")
            if it.get("specifications") is not None:
                order_item.specifications = it.get("specifications")
            if it.get("received_quantity") is not None:
                order_item.received_quantity = it.get("received_quantity")

            order.items.append(order_item)
            subtotal_amount += subtotal

        # Calcul des montants globaux
        order.subtotal_amount = subtotal_amount
        order.discount_percent = discount_percent
        order.discount_amount = subtotal_amount * (discount_percent / 100.0)
        taxable_base = max(0.0, subtotal_amount - order.discount_amount)
        order.tax_percent = tax_percent
        order.tax_amount = taxable_base * (tax_percent / 100.0)
        order.shipping_amount = shipping_amount
        order.total_amount = taxable_base + order.tax_amount + shipping_amount

        self.db.add(order)
        self.db.commit()
        self.db.refresh(order)
        return order

    async def submit_order(self, order_id: int, user_id: int) -> PurchaseOrder:
        """
        Soumet une commande d'achat pour approbation (version unifiée).
        - Valide l'état, le contenu, le fournisseur et le montant total
        - Génère po_number si absent et calcule payment_due_date
        - Ajoute une entrée d'historique et notifie les approbateurs
        """
        order = await self.get(order_id)
        if not order:
            raise ValueError("Commande non trouvée")

        # Vérifier que la commande est en état brouillon
        if order.status != OrderStatus.DRAFT:
            raise ValueError("Seules les commandes en brouillon peuvent être soumises")

        # Vérifier que la commande contient au moins un article
        if not order.items or len(order.items) == 0:
            raise ValueError("La commande doit contenir au moins un article pour être soumise")

        # Vérifier que le fournisseur est spécifié
        if not order.supplier_id:
            raise ValueError("La commande doit avoir un fournisseur spécifié")

        # Vérifier que le montant total est valide
        if order.total_amount is None or order.total_amount <= 0:
            raise ValueError("Le montant total de la commande doit être supérieur à zéro")

        # Enregistrer l'ancien statut pour l'historique
        old_status = order.status

        # Mettre à jour le statut de la commande
        order.status = OrderStatus.SUBMITTED
        order.submitted_by = user_id
        order.submitted_at = datetime.now()

        # Générer un numéro de commande s'il n'existe pas
        if not order.po_number:
            order.po_number = f"PO-{datetime.now().strftime('%Y%m%d')}-{order.id}"

        # Calculer la date d'échéance de paiement (30 jours par défaut)
        if order.payment_terms:
            try:
                terms = order.payment_terms.lower()
                if "jours" in terms:
                    days = int(terms.split("jours")[0].strip())
                    order.payment_due_date = order.order_date + timedelta(days=days)
                else:
                    order.payment_due_date = order.order_date + timedelta(days=30)
            except Exception:
                order.payment_due_date = order.order_date + timedelta(days=30)
        else:
            order.payment_due_date = order.order_date + timedelta(days=30)

        # Ajouter une entrée dans l'historique des commandes
        from app.core.models.order_history import OrderHistory
        history_entry = OrderHistory(
            order_id=order.id,
            user_id=user_id,
            action="submit",
            old_status=old_status.value if hasattr(old_status, 'value') else str(old_status),
            new_status=OrderStatus.SUBMITTED.value,
            details=f"Commande soumise pour approbation par l'utilisateur {user_id}"
        )
        self.db.add(history_entry)

        # Notifier les personnes concernées
        await self.notification_service.create_notification({
            "user_id": None,  # Notification système pour les approbateurs
            "type": NotificationType.SUPPLIER,
            "priority": NotificationPriority.MEDIUM,
            "title": "Nouvelle commande à approuver",
            "message": f"La commande {order.po_number or f'#PO-{order.id}'} a été soumise et est en attente d'approbation.",
            "data": {"order_id": order.id, "supplier_id": order.supplier_id},
            "action_url": f"/purchasing/orders/{order.id}",
            "icon": "purchase",
            "channels": [NotificationChannel.UI]
        })

        self.db.commit()
        return order

    async def approve_order(self, order_id: int, user_id: int) -> PurchaseOrder:
        """
        Approuve une commande soumise

        Args:
            order_id: ID de la commande
            user_id: ID de l'utilisateur qui approuve la commande

        Returns:
            La commande mise à jour
        """
        order = await self.get(order_id)
        if not order:
            raise ValueError("Commande non trouvée")

        # Vérifier que la commande est en état soumis
        if order.status != OrderStatus.SUBMITTED:
            raise ValueError("Seules les commandes soumises peuvent être approuvées")

        # Enregistrer l'ancien statut pour l'historique
        old_status = order.status

        # Mettre à jour le statut de la commande
        order.status = OrderStatus.APPROVED
        order.approved_by = user_id
        order.approved_at = datetime.now()

        # Ajouter une entrée dans l'historique des commandes
        from app.core.models.order_history import OrderHistory
        history_entry = OrderHistory(
            order_id=order.id,
            user_id=user_id,
            action="approve",
            old_status=old_status.value if hasattr(old_status, 'value') else str(old_status),
            new_status=OrderStatus.APPROVED.value,
            details=f"Commande approuvée par l'utilisateur {user_id}"
        )
        self.db.add(history_entry)

        # Notifier les personnes concernées
        await self.notification_service.create_notification({
            "user_id": order.created_by,  # Notifier la personne qui a créé la commande
            "type": NotificationType.SUPPLIER,
            "priority": NotificationPriority.MEDIUM,
            "title": "Commande approuvée",
            "message": f"La commande {order.po_number} a été approuvée et peut maintenant être envoyée au fournisseur.",
            "data": {"order_id": order.id, "supplier_id": order.supplier_id},
            "action_url": f"/purchasing/orders/{order.id}",
            "icon": "purchase",
            "channels": [NotificationChannel.UI, NotificationChannel.EMAIL]
        })

        self.db.commit()
        return order

    async def place_order(self, order_id: int, user_id: int) -> PurchaseOrder:
        """
        Envoie une commande approuvée au fournisseur

        Args:
            order_id: ID de la commande
            user_id: ID de l'utilisateur qui envoie la commande

        Returns:
            La commande mise à jour
        """
        order = await self.get(order_id)
        if not order:
            raise ValueError("Commande non trouvée")

        # Vérifier que la commande est en état approuvé
        if order.status != OrderStatus.APPROVED:
            raise ValueError("Seules les commandes approuvées peuvent être envoyées")

        # Mettre à jour le statut de la commande
        order.status = OrderStatus.ORDERED
        order.ordered_at = datetime.now()

        # Notifier les personnes concernées
        await self.notification_service.create_notification({
            "user_id": order.created_by,  # Notifier la personne qui a créé la commande
            "type": NotificationType.SUPPLIER,
            "priority": NotificationPriority.MEDIUM,
            "title": "Commande envoyée",
            "message": f"La commande {order.po_number} a été envoyée au fournisseur {order.supplier.name}.",
            "data": {"order_id": order.id, "supplier_id": order.supplier_id},
            "action_url": f"/purchasing/orders/{order.id}",
            "icon": "purchase",
            "channels": [NotificationChannel.UI, NotificationChannel.EMAIL]
        })

        self.db.commit()
        return order

    async def receive_order(
        self,
        order_id: int,
        received_items: List[dict],
        user_id: int
    ) -> PurchaseOrder:
        order = await self.get(order_id)
        if not order:
            raise ValueError("Commande non trouvée")

        # Vérifier que la commande est dans un état valide pour la réception
        valid_statuses = [OrderStatus.APPROVED, OrderStatus.ORDERED, OrderStatus.PARTIALLY_RECEIVED]
        if order.status not in valid_statuses:
            raise ValueError(f"La commande doit être dans l'un des états suivants pour être réceptionnée: {', '.join([s.value for s in valid_statuses])}")

        # Vérifier et mettre à jour le stock
        for received_item in received_items:
            order_item = next(
                (item for item in order.items if item.id == received_item["item_id"]),
                None
            )
            if not order_item:
                raise ValueError(f"Item {received_item['item_id']} not in order")

            # Quantité à recevoir pour cet item
            qty = float(received_item.get("quantity", 0) or 0)
            if qty <= 0:
                raise ValueError("La quantité reçue doit être > 0")

            already_received = float(order_item.received_quantity or 0)
            max_receivable = max(0.0, float(order_item.quantity) - already_received)
            qty_actual = min(qty, max_receivable)
            if qty_actual <= 0:
                continue  # rien à recevoir pour cet item

            # Ajuster le stock avec la quantité réellement recevable
            await self.inventory_service.adjust_stock(
                item_id=order_item.product_id,
                quantity_change=qty_actual,
                user_id=user_id,
                reference=f"PO-{order.po_number or order.id}",
                notes=f"Réception partielle de la commande #{order.po_number or order.id} (item {order_item.id})"
            )

            # Émettre un événement pour mise à jour éventuelle du prix d'achat
            try:
                event_manager.emit(
                    EventType.PURCHASE_ITEM_RECEIVED,
                    item_id=order_item.product_id,
                    purchase_unit_price=order_item.purchase_unit_price,
                )
            except Exception:
                pass

            order_item.received_quantity = already_received + qty_actual
            order_item.remaining_quantity = max(0.0, float(order_item.quantity) - float(order_item.received_quantity))
            order_item.received_at = datetime.now()

        # Mettre à jour le statut de la commande
        all_items_received = all(item.received_quantity == item.quantity for item in order.items)
        order.status = OrderStatus.COMPLETED if all_items_received else OrderStatus.PARTIALLY_RECEIVED
        order.received_by = user_id
        order.received_at = datetime.now()

        # Créer une notification pour informer de la réception
        await self.notification_service.create_notification({
            "user_id": order.created_by,  # Notifier la personne qui a créé la commande
            "type": NotificationType.INVENTORY,
            "priority": NotificationPriority.MEDIUM,
            "title": "Réception de commande",
            "message": f"La commande {order.po_number or f'#PO-{order.id}'} a été " +
                      ("entièrement reçue" if all_items_received else "partiellement reçue"),
            "data": {"order_id": order.id, "supplier_id": order.supplier_id},
            "action_url": f"/purchasing/orders/{order.id}",
            "icon": "delivery",
            "channels": [NotificationChannel.UI, NotificationChannel.EMAIL]
        })

        self.db.commit()
        return order

    async def receive_items(
        self,
        order_id: int,
        receipt_data: Dict[str, Any]
    ) -> PurchaseOrder:
        """
        Réceptionne les articles d'une commande d'achat avec des informations supplémentaires

        Args:
            order_id: ID de la commande
            receipt_data: Données de réception contenant:
                - received_items: Liste des articles reçus avec leur quantité
                - receipt_date: Date de réception
                - delivery_note: Numéro de bon de livraison
                - notes: Notes supplémentaires

        Returns:
            La commande mise à jour
        """
        order = await self.get(order_id)
        if not order:
            raise ValueError("Commande non trouvée")

        # Vérifier que la commande est dans un état valide pour la réception
        valid_statuses = [OrderStatus.APPROVED, OrderStatus.ORDERED, OrderStatus.PARTIALLY_RECEIVED]
        if order.status not in valid_statuses:
            raise ValueError(f"La commande doit être dans l'un des états suivants pour être réceptionnée: {', '.join([s.value for s in valid_statuses])}")

        # Extraire les données
        received_items = receipt_data.get("received_items", [])
        receipt_date = receipt_data.get("receipt_date", datetime.now())
        delivery_note = receipt_data.get("delivery_note")
        notes = receipt_data.get("notes")

        # Vérifier qu'il y a des articles à réceptionner
        if not received_items:
            raise ValueError("Aucun article à réceptionner")

        # Récupérer l'ID de l'utilisateur (à partir de la session ou des paramètres)
        user_id = receipt_data.get("user_id", 1)  # TODO: Récupérer l'ID de l'utilisateur connecté

        # Vérifier et mettre à jour le stock
        for received_item in received_items:
            order_item = next(
                (item for item in order.items if item.id == received_item["item_id"]),
                None
            )
            if not order_item:
                raise ValueError(f"Article {received_item['item_id']} non trouvé dans la commande")

            # Récupérer le produit
            product = await self.inventory_service.get(order_item.product_id)
            if not product:
                raise ValueError(f"Produit {order_item.product_id} non trouvé")

            # Récupérer la quantité à recevoir
            quantity_to_receive = received_item["quantity"]

            # Vérifier si la quantité à recevoir est valide
            if quantity_to_receive <= 0:
                raise ValueError(f"La quantité à recevoir doit être supérieure à 0")

            # Vérifier si la quantité à recevoir est supérieure à la quantité restante
            remaining = order_item.quantity - (order_item.received_quantity or 0)
            if quantity_to_receive > remaining and not received_item.get("allow_excess", False):
                raise ValueError(f"La quantité à recevoir ({quantity_to_receive}) est supérieure à la quantité restante ({remaining})")

            # Vérifier si le prix d'achat a changé
            update_purchase_price = False
            new_purchase_price = received_item.get("unit_price", getattr(order_item, "purchase_unit_price", 0))

            # Si le prix d'achat a changé et que l'option est activée, mettre à jour le prix
            update_price = received_item.get("update_price", False)
            if update_price and new_purchase_price != product.purchase_price:
                update_purchase_price = True

            # Ajuster le stock
            stock_adjustment_result = await self.inventory_service.adjust_stock(
                item_id=order_item.product_id,
                quantity_change=quantity_to_receive,
                user_id=user_id,
                reference=f"PO-{order.po_number}",
                notes=f"Reçu de la commande {order.po_number}"
            )

            # Émettre un événement pour informer qu'un article a été reçu
            if stock_adjustment_result:
                event_manager.emit(
                    EventType.PURCHASE_ITEM_RECEIVED,
                    item_id=order_item.product_id,
                    quantity=quantity_to_receive,
                    purchase_order_id=order.id,
                    purchase_order_item_id=order_item.id,
                    user_id=user_id,
                    purchase_unit_price=new_purchase_price
                )

            # Mettre à jour les informations de réception
            order_item.received_quantity = (order_item.received_quantity or 0) + quantity_to_receive
            order_item.remaining_quantity = max(0, order_item.quantity - order_item.received_quantity)
            order_item.received_at = receipt_date

            # Mettre à jour le prix d'achat si nécessaire
            if update_purchase_price:
                # Mettre à jour le prix d'achat et éventuellement le prix de vente
                update_selling_price = received_item.get("update_selling_price", False)
                price_update_result = await self.inventory_service.update_purchase_price(
                    item_id=order_item.product_id,
                    new_price=new_purchase_price,
                    user_id=user_id,
                    purchase_order_id=order.id,
                    reason=f"Mise à jour lors de la réception de la commande {order.po_number}",
                    update_selling_price=update_selling_price
                )

                # Émettre un événement pour informer que le prix a été mis à jour
                if price_update_result.get("success", False):
                    event_manager.emit(
                        EventType.INVENTORY_PRICE_UPDATED,
                        item_id=order_item.product_id,
                        old_purchase_price=price_update_result.get("old_purchase_price"),
                        new_purchase_price=price_update_result.get("new_purchase_price"),
                        old_unit_price=price_update_result.get("old_unit_price"),
                        new_unit_price=price_update_result.get("new_unit_price"),
                        user_id=user_id,
                        purchase_order_id=order.id
                    )

        # Mettre à jour le statut de la commande
        all_items_received = all(
            (item.received_quantity or 0) >= item.quantity
            for item in order.items
        )
        order.status = OrderStatus.COMPLETED if all_items_received else OrderStatus.PARTIALLY_RECEIVED
        order.received_by = user_id
        order.received_at = receipt_date

        # Si la commande est terminée, mettre à jour la date de livraison
        if order.status == OrderStatus.COMPLETED:
            order.delivery_date = receipt_date

        # Mettre à jour les informations supplémentaires
        if delivery_note:
            order.delivery_note = delivery_note
        if notes:
            order.notes = (order.notes + "\n" + notes) if order.notes else notes

        # Créer une notification pour informer de la réception
        await self.notification_service.create_notification({
            "user_id": order.created_by,  # Notifier la personne qui a créé la commande
            "type": NotificationType.INVENTORY,
            "priority": NotificationPriority.MEDIUM,
            "title": "Réception de commande",
            "message": f"La commande {order.po_number} a été " +
                      ("entièrement reçue" if all_items_received else "partiellement reçue"),
            "data": {"order_id": order.id, "supplier_id": order.supplier_id},
            "action_url": f"/purchasing/orders/{order.id}",
            "icon": "delivery",
            "channels": [NotificationChannel.UI, NotificationChannel.EMAIL]
        })

        # Si la commande est terminée, proposer de créer une facture
        invoice_result = None
        if order.status == OrderStatus.COMPLETED and receipt_data.get("create_invoice", False):
            invoice_data = {
                "invoice_number": receipt_data.get("invoice_number"),
                "invoice_date": receipt_date,
                "due_date": order.payment_due_date,
                "notes": f"Facture créée automatiquement lors de la réception de la commande {order.po_number}",
                "tax_amount": receipt_data.get("tax_amount", order.tax_amount)
            }

            try:
                invoice_result = await self.create_invoice_from_order(order.id, invoice_data)

                # Mettre à jour le statut de paiement de la commande
                await self.update_payment_status(order.id)
            except Exception as e:
                # Ne pas bloquer la réception en cas d'erreur lors de la création de la facture
                print(f"Erreur lors de la création de la facture: {str(e)}")
                import traceback
                traceback.print_exc()

        self.db.commit()

        # Préparer la réponse
        result = {
            "order": {
                "id": order.id,
                "po_number": order.po_number,
                "supplier_id": order.supplier_id,
                "status": order.status.value if hasattr(order.status, 'value') else order.status,
                "total_amount": order.total_amount,
                "payment_status": order.payment_status,
                "received_at": order.received_at,
                "delivery_date": order.delivery_date,
                "delivery_note": order.delivery_note
            },
            "received_items": [
                {
                    "id": item.id,
                    "product_id": item.product_id,
                    "quantity": item.quantity,
                    "received_quantity": item.received_quantity,
                    "remaining_quantity": item.remaining_quantity
                }
                for item in order.items
            ],
            "all_items_received": order.status == OrderStatus.COMPLETED
        }

        # Ajouter les informations de facture si une facture a été créée
        if invoice_result:
            result["invoice"] = invoice_result["invoice"]
            result["payment_status"] = invoice_result["payment_status"]

        return result

    async def get_low_stock_items(self) -> List[Dict[str, Any]]:
        """
        Récupère les articles à faible stock qui nécessitent une commande

        Returns:
            Liste des articles à commander avec les informations sur les fournisseurs
        """
        # Récupérer les articles à faible stock
        low_stock_items = await self.inventory_service.get_low_stock_items()

        result = []
        for item in low_stock_items:
            # Récupérer les commandes en cours pour cet article
            pending_orders = self.db.query(PurchaseOrderItem).join(
                PurchaseOrder, PurchaseOrderItem.po_id == PurchaseOrder.id
            ).filter(
                PurchaseOrderItem.product_id == item.id,
                PurchaseOrder.status.in_([
                    OrderStatus.PENDING,
                    OrderStatus.APPROVED,
                    OrderStatus.ORDERED
                ])
            ).all()

            # Calculer la quantité déjà commandée
            ordered_quantity = sum(order.quantity for order in pending_orders)

            # Calculer la quantité à commander
            quantity_to_order = max(0, item.minimum_quantity - item.quantity - ordered_quantity)

            if quantity_to_order > 0:
                # Récupérer le fournisseur principal pour cet article
                # TODO: Implémenter la logique pour déterminer le meilleur fournisseur
                # Pour l'instant, on prend le premier fournisseur trouvé
                supplier = self.db.query(Supplier).first()

                result.append({
                    "item": item,
                    "quantity_to_order": quantity_to_order,
                    "supplier": supplier,
                    "pending_orders": pending_orders
                })

        return result

    async def generate_purchase_orders_from_low_stock(self, user_id: int) -> List[PurchaseOrder]:
        """
        Génère automatiquement des commandes d'achat pour les articles à faible stock

        Args:
            user_id: ID de l'utilisateur qui génère les commandes

        Returns:
            Liste des commandes générées
        """
        low_stock_items = await self.get_low_stock_items()

        # Regrouper les articles par fournisseur
        items_by_supplier = {}
        for item_data in low_stock_items:
            supplier_id = item_data["supplier"].id
            if supplier_id not in items_by_supplier:
                items_by_supplier[supplier_id] = []

            items_by_supplier[supplier_id].append({
                "product_id": item_data["item"].id,
                "quantity": item_data["quantity_to_order"]
            })

        # Créer une commande par fournisseur
        orders = []
        for supplier_id, items in items_by_supplier.items():
            try:
                order = await self.create_purchase_order(
                    supplier_id=supplier_id,
                    items=items,
                    user_id=user_id
                )
                orders.append(order)

                # Créer une notification pour informer de la création de la commande
                await self.notification_service.create_notification({
                    "user_id": user_id,
                    "type": NotificationType.INVENTORY,
                    "priority": NotificationPriority.MEDIUM,
                    "title": "Commande automatique créée",
                    "message": f"Une commande automatique a été créée pour {len(items)} article(s) à faible stock.",
                    "data": {"order_id": order.id, "supplier_id": supplier_id},
                    "action_url": f"/purchasing/orders/{order.id}",
                    "icon": "purchase",
                    "channels": [NotificationChannel.UI]
                })
            except Exception as e:
                # Continuer avec le prochain fournisseur en cas d'erreur
                print(f"Erreur lors de la création de la commande pour le fournisseur {supplier_id}: {str(e)}")

        return orders

    async def create_invoice_from_order(
        self,
        order_id: int,
        invoice_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Crée une facture fournisseur à partir d'une commande d'achat

        Args:
            order_id: ID de la commande
            invoice_data: Données supplémentaires pour la facture

        Returns:
            Informations sur la facture créée et le statut de paiement mis à jour
        """
        order = await self.get(order_id)
        if not order:
            raise ValueError(f"Commande avec ID {order_id} non trouvée")

        # Vérifier que la commande est dans un état valide
        valid_statuses = [OrderStatus.PARTIALLY_RECEIVED, OrderStatus.COMPLETED]
        if order.status not in valid_statuses:
            raise ValueError(f"La commande doit être au moins partiellement reçue pour créer une facture")

        # Préparer les données de la facture
        invoice_number = invoice_data.get("invoice_number")
        if not invoice_number:
            # Générer un numéro de facture automatique en utilisant le service de finances fournisseurs
            invoice_number = await self.supplier_finance_service.generate_invoice_number(order_id)

        # Calculer la date d'échéance si non spécifiée
        due_date = invoice_data.get("due_date")
        if not due_date and order.payment_terms:
            # Utiliser les conditions de paiement de la commande pour calculer la date d'échéance
            invoice_date = invoice_data.get("invoice_date", datetime.now())
            if isinstance(invoice_date, str):
                invoice_date = datetime.strptime(invoice_date, "%Y-%m-%d")
            due_date = invoice_date + timedelta(days=order.payment_terms)

        # Créer la facture
        invoice_data = {
            "invoice_number": invoice_number,
            "supplier_id": order.supplier_id,
            "purchase_order_id": order_id,
            "invoice_date": invoice_data.get("invoice_date", datetime.now()),
            "due_date": due_date,
            "total_amount": order.total_amount,
            "tax_amount": invoice_data.get("tax_amount", 0),
            "currency": order.currency,
            "notes": invoice_data.get("notes"),
            "status": InvoiceStatus.PENDING
        }

        # Créer la facture via le service de finances fournisseurs
        invoice = await self.supplier_finance_service.create_invoice(invoice_data)

        # Mettre à jour le statut de paiement de la commande
        payment_status = await self.update_payment_status(order_id)

        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": None,  # Notification système
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.MEDIUM,
            "title": "Nouvelle facture fournisseur",
            "message": f"Une facture ({invoice_number}) a été créée pour la commande {order.po_number}.",
            "data": {"order_id": order.id, "supplier_id": order.supplier_id, "invoice_id": invoice.id},
            "action_url": f"/purchasing/orders/{order.id}",
            "icon": "invoice",
            "channels": [NotificationChannel.UI]
        })

        # Appliquer les acomptes à la facture si nécessaire
        if order.advance_payment_amount and order.advance_payment_amount > 0:
            # Récupérer les paiements d'acompte
            advance_payments = self.db.query(SupplierPayment).filter(
                SupplierPayment.purchase_order_id == order_id,
                SupplierPayment.invoice_id.is_(None)
            ).all()

            # Associer les acomptes à la facture
            for payment in advance_payments:
                payment.invoice_id = invoice.id
                self.db.add(payment)

            # Mettre à jour le statut de la facture
            total_advance = sum(payment.amount for payment in advance_payments)
            if total_advance >= invoice.total_amount:
                invoice.status = InvoiceStatus.PAID
            elif total_advance > 0:
                invoice.status = InvoiceStatus.PARTIALLY_PAID
            self.db.add(invoice)

            self.db.commit()

        return {
            "invoice": {
                "id": invoice.id,
                "invoice_number": invoice.invoice_number,
                "supplier_id": invoice.supplier_id,
                "purchase_order_id": invoice.purchase_order_id,
                "invoice_date": invoice.invoice_date,
                "due_date": invoice.due_date,
                "total_amount": invoice.total_amount,
                "status": invoice.status.value if hasattr(invoice.status, 'value') else invoice.status
            },
            "payment_status": payment_status
        }

    async def check_overdue_orders(self) -> List[PurchaseOrder]:
        """
        Vérifie les commandes en retard de livraison

        Returns:
            Liste des commandes en retard
        """
        today = datetime.now().date()

        # Récupérer les commandes dont la date de livraison prévue est dépassée
        overdue_orders = self.db.query(self.model).filter(
            self.model.status.in_([OrderStatus.ORDERED, OrderStatus.PARTIALLY_RECEIVED]),
            self.model.expected_delivery < today
        ).all()

        # Créer des notifications pour les commandes en retard
        for order in overdue_orders:
            days_overdue = (today - order.expected_delivery.date()).days

            await self.notification_service.create_notification({
                "user_id": order.created_by,
                "type": NotificationType.SUPPLIER,
                "priority": NotificationPriority.HIGH,
                "title": "Commande en retard",
                "message": f"La commande {order.po_number or f'#PO-{order.id}'} est en retard de {days_overdue} jour(s).",
                "data": {"order_id": order.id, "supplier_id": order.supplier_id, "days_overdue": days_overdue},
                "action_url": f"/purchasing/orders/{order.id}",
                "icon": "warning",
                "channels": [NotificationChannel.UI, NotificationChannel.EMAIL]
            })

        return overdue_orders

    async def record_advance_payment(self, order_id: int, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enregistre un acompte pour une commande d'achat

        Args:
            order_id: ID de la commande
            payment_data: Données du paiement contenant:
                - amount: Montant du paiement
                - payment_method: Méthode de paiement
                - payment_date: Date du paiement
                - reference: Référence du paiement
                - notes: Notes supplémentaires

        Returns:
            Informations sur le paiement enregistré
        """
        # Récupérer la commande
        order = await self.get(order_id)
        if not order:
            raise ValueError(f"Commande avec ID {order_id} non trouvée")

        # Vérifier que la commande est dans un état valide pour un acompte
        valid_statuses = [OrderStatus.APPROVED, OrderStatus.ORDERED, OrderStatus.PARTIALLY_RECEIVED]
        if order.status not in valid_statuses:
            raise ValueError(f"La commande doit être dans l'un des états suivants pour enregistrer un acompte: {', '.join([s.value for s in valid_statuses])}")

        # Extraire les données du paiement
        amount = payment_data.get("amount")
        if not amount or amount <= 0:
            raise ValueError("Le montant du paiement doit être supérieur à zéro")

        payment_method = payment_data.get("payment_method", PaymentMethod.bank_transfer)
        payment_date = payment_data.get("payment_date", datetime.now())
        reference = payment_data.get("reference", f"ADV-{order.po_number}-{datetime.now().strftime('%Y%m%d')}")
        notes = payment_data.get("notes", f"Acompte pour la commande {order.po_number}")

        # Vérifier que le montant de l'acompte ne dépasse pas le montant total de la commande
        if amount > order.total_amount:
            raise ValueError(f"Le montant de l'acompte ({amount}) ne peut pas dépasser le montant total de la commande ({order.total_amount})")

        # Mettre à jour l'acompte dans la commande
        order.advance_payment_amount = (order.advance_payment_amount or 0) + amount

        # Mettre à jour le statut de paiement
        if order.advance_payment_amount >= order.total_amount:
            order.payment_status = "paid"
        elif order.advance_payment_amount > 0:
            order.payment_status = "partial"
        else:
            order.payment_status = "unpaid"

        # Enregistrer le paiement via le service financier fournisseur
        payment_result = await self.supplier_finance_service.record_payment({
            "supplier_id": order.supplier_id,
            "amount": amount,
            "payment_method": payment_method,
            "payment_date": payment_date,
            "reference": reference,
            "notes": notes,
            "purchase_order_id": order.id
        })

        # Enregistrer dans le système comptable
        await self.accounting_service.record_supplier_payment(payment_result)

        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": None,  # Notification système
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.MEDIUM,
            "title": "Acompte enregistré",
            "message": f"Un acompte de {amount} DA a été enregistré pour la commande {order.po_number}.",
            "data": {"order_id": order.id, "supplier_id": order.supplier_id, "payment_id": payment_result.id},
            "action_url": f"/purchasing/orders/{order.id}",
            "icon": "payment",
            "channels": [NotificationChannel.UI]
        })

        self.db.commit()

        return {
            "order_id": order.id,
            "po_number": order.po_number,
            "payment_id": payment_result.id,
            "amount": amount,
            "payment_method": payment_method.value if hasattr(payment_method, 'value') else payment_method,
            "payment_date": payment_date,
            "reference": reference,
            "total_advance_payment": order.advance_payment_amount,
            "payment_status": order.payment_status,
            "remaining_amount": order.total_amount - order.advance_payment_amount
        }

    async def update_payment_status(self, order_id: int) -> Dict[str, Any]:
        """
        Met à jour le statut de paiement d'une commande en fonction des factures et paiements associés

        Args:
            order_id: ID de la commande

        Returns:
            Informations sur le statut de paiement mis à jour
        """
        # Récupérer la commande
        order = await self.get(order_id)
        if not order:
            raise ValueError(f"Commande avec ID {order_id} non trouvée")

        # Récupérer les factures associées à cette commande
        invoices = self.db.query(SupplierInvoice).filter(
            SupplierInvoice.purchase_order_id == order_id
        ).all()

        # Récupérer les paiements associés à ces factures
        invoice_ids = [invoice.id for invoice in invoices]
        payments = []
        if invoice_ids:
            payments = self.db.query(SupplierPayment).filter(
                SupplierPayment.invoice_id.in_(invoice_ids)
            ).all()

        # Récupérer les paiements directement associés à la commande (acomptes)
        direct_payments = self.db.query(SupplierPayment).filter(
            SupplierPayment.purchase_order_id == order_id,
            SupplierPayment.invoice_id.is_(None)
        ).all()

        # Calculer le montant total des factures
        total_invoiced = sum(invoice.total_amount for invoice in invoices)

        # Calculer le montant total payé
        total_paid = sum(payment.amount for payment in payments)

        # Ajouter les acomptes
        total_advance = sum(payment.amount for payment in direct_payments)

        # Mettre à jour l'acompte dans la commande
        order.advance_payment_amount = total_advance

        # Déterminer le statut de paiement
        if total_invoiced == 0:
            # Pas de facture, on se base sur les acomptes
            if total_advance >= order.total_amount:
                order.payment_status = "paid"
            elif total_advance > 0:
                order.payment_status = "partial"
            else:
                order.payment_status = "unpaid"
        else:
            # Il y a des factures, on se base sur le paiement des factures
            if total_paid + total_advance >= total_invoiced:
                order.payment_status = "paid"
            elif total_paid + total_advance > 0:
                order.payment_status = "partial"
            else:
                order.payment_status = "unpaid"

        self.db.commit()

        return {
            "order_id": order.id,
            "po_number": order.po_number,
            "total_amount": order.total_amount,
            "total_invoiced": total_invoiced,
            "total_paid": total_paid,
            "total_advance": total_advance,
            "payment_status": order.payment_status,
            "remaining_amount": max(0, order.total_amount - total_paid - total_advance),
            "invoices": [
                {
                    "id": invoice.id,
                    "number": invoice.invoice_number,
                    "invoice_number": invoice.invoice_number,
                    "date": invoice.invoice_date,
                    "due_date": invoice.due_date,
                    "amount": invoice.total_amount,
                    "tax_amount": invoice.tax_amount,
                    "status": invoice.status.value if hasattr(invoice.status, 'value') else invoice.status,
                    "notes": invoice.notes
                }
                for invoice in invoices
            ],
            "payments": [
                {
                    "id": payment.id,
                    "amount": payment.amount,
                    "payment_date": payment.payment_date,
                    "payment_method": payment.payment_method.value if hasattr(payment.payment_method, 'value') else payment.payment_method,
                    "invoice_id": payment.invoice_id
                }
                for payment in payments + direct_payments
            ]
        }

    async def get_payment_history(self, order_id: int) -> Dict[str, Any]:
        """
        Récupère l'historique des paiements pour une commande

        Args:
            order_id: ID de la commande

        Returns:
            Historique des paiements
        """
        # Récupérer la commande
        order = await self.get(order_id)
        if not order:
            raise ValueError(f"Commande avec ID {order_id} non trouvée")

        # Récupérer les factures associées à cette commande
        invoices = self.db.query(SupplierInvoice).filter(
            SupplierInvoice.purchase_order_id == order_id
        ).all()

        # Récupérer les paiements associés à ces factures
        invoice_ids = [invoice.id for invoice in invoices]
        invoice_payments = []
        if invoice_ids:
            invoice_payments = self.db.query(SupplierPayment).filter(
                SupplierPayment.invoice_id.in_(invoice_ids)
            ).all()

        # Récupérer les paiements directement associés à la commande (acomptes)
        direct_payments = self.db.query(SupplierPayment).filter(
            SupplierPayment.purchase_order_id == order_id,
            SupplierPayment.invoice_id.is_(None)
        ).all()

        # Combiner tous les paiements et les trier par date
        all_payments = invoice_payments + direct_payments
        all_payments.sort(key=lambda p: p.payment_date)

        return {
            "order_id": order.id,
            "po_number": order.po_number,
            "supplier_id": order.supplier_id,
            "supplier_name": order.supplier.name if order.supplier else "N/A",
            "total_amount": order.total_amount,
            "payment_status": order.payment_status,
            "invoices": [
                {
                    "id": invoice.id,
                    "invoice_number": invoice.invoice_number,
                    "invoice_date": invoice.invoice_date,
                    "due_date": invoice.due_date,
                    "amount": invoice.total_amount,
                    "status": invoice.status.value if hasattr(invoice.status, 'value') else invoice.status,
                    "payments": [
                        {
                            "id": payment.id,
                            "amount": payment.amount,
                            "payment_date": payment.payment_date,
                            "payment_method": payment.payment_method.value if hasattr(payment.payment_method, 'value') else payment.payment_method,
                            "reference": payment.reference
                        }
                        for payment in invoice_payments if payment.invoice_id == invoice.id
                    ]
                }
                for invoice in invoices
            ],
            "advance_payments": [
                {
                    "id": payment.id,
                    "amount": payment.amount,
                    "payment_date": payment.payment_date,
                    "payment_method": payment.payment_method.value if hasattr(payment.payment_method, 'value') else payment.payment_method,
                    "reference": payment.reference,
                    "notes": payment.notes
                }
                for payment in direct_payments
            ],
            "payment_timeline": [
                {
                    "date": payment.payment_date,
                    "type": "advance_payment" if payment.invoice_id is None else "invoice_payment",
                    "amount": payment.amount,
                    "payment_method": payment.payment_method.value if hasattr(payment.payment_method, 'value') else payment.payment_method,
                    "reference": payment.reference,
                    "invoice_id": payment.invoice_id,
                    "invoice_number": next((inv.invoice_number for inv in invoices if inv.id == payment.invoice_id), None)
                }
                for payment in all_payments
            ]
        }

    async def get_all_with_items(self, skip: int = 0, limit: int = 100) -> List[PurchaseOrder]:
        """
        Récupère toutes les commandes avec leurs articles

        Args:
            skip: Nombre d'éléments à sauter
            limit: Nombre maximum d'éléments à retourner

        Returns:
            Liste des commandes avec leurs articles
        """
        from sqlalchemy.orm import joinedload, subqueryload
        # Utiliser subqueryload pour charger les articles et leurs produits associés
        orders = self.db.query(self.model).options(
            subqueryload(self.model.items).joinedload(PurchaseOrderItem.product),
            joinedload(self.model.supplier),
            joinedload(self.model.creator),
            joinedload(self.model.submitter),
            joinedload(self.model.approver)
        ).offset(skip).limit(limit).all()

        # Calculer le montant total pour chaque commande si nécessaire
        for order in orders:
            if order.total_amount is None or order.total_amount == 0:
                total = 0
                if hasattr(order, 'items') and order.items:
                    for item in order.items:
                        # Utiliser purchase_unit_price uniquement
                        price = getattr(item, 'purchase_unit_price', 0)
                        if price > 0 and hasattr(item, 'quantity'):
                            total += price * item.quantity
                    order.total_amount = total

        return orders

    def get_sync(self, id: int) -> Optional[PurchaseOrder]:
        """
        Récupère une commande d'achat par son ID de manière synchrone

        Args:
            id: ID de la commande

        Returns:
            La commande d'achat ou None si elle n'existe pas
        """
        from sqlalchemy.orm import joinedload, subqueryload

        # Récupérer la commande avec ses relations
        order = (
            self.db.query(self.model)
            .options(
                subqueryload(self.model.items).joinedload(PurchaseOrderItem.product),
                joinedload(self.model.supplier),
                joinedload(self.model.creator),
                joinedload(self.model.submitter),
                joinedload(self.model.approver)
            )
            .filter(self.model.id == id)
            .first()
        )

        return order

    def get_all_sync(self) -> List[PurchaseOrder]:
        """
        Récupère toutes les commandes d'achat de manière synchrone

        Returns:
            Liste des commandes d'achat
        """
        from sqlalchemy.orm import joinedload, subqueryload

        # Récupérer toutes les commandes avec leurs relations
        orders = (
            self.db.query(self.model)
            .options(
                subqueryload(self.model.items).joinedload(PurchaseOrderItem.product),
                joinedload(self.model.supplier),
                joinedload(self.model.creator),
                joinedload(self.model.submitter),
                joinedload(self.model.approver)
            )
            .order_by(self.model.created_at.desc())
            .all()
        )

        return orders

    async def generate_financial_report(self, start_date: datetime = None, end_date: datetime = None) -> Dict[str, Any]:
        """
        Génère un rapport financier pour les achats

        Args:
            start_date: Date de début (optionnelle)
            end_date: Date de fin (optionnelle)

        Returns:
            Rapport financier
        """
        # Définir les dates par défaut si non spécifiées
        if not start_date:
            # Par défaut, début du mois en cours
            start_date = datetime(datetime.now().year, datetime.now().month, 1)

        if not end_date:
            # Par défaut, date actuelle
            end_date = datetime.now()

        # Récupérer les commandes dans la période
        orders = self.db.query(self.model).filter(
            self.model.order_date >= start_date,
            self.model.order_date <= end_date
        ).all()

        # Récupérer les factures dans la période
        invoices = self.db.query(SupplierInvoice).filter(
            SupplierInvoice.invoice_date >= start_date,
            SupplierInvoice.invoice_date <= end_date
        ).all()

        # Récupérer les paiements dans la période
        payments = self.db.query(SupplierPayment).filter(
            SupplierPayment.payment_date >= start_date,
            SupplierPayment.payment_date <= end_date
        ).all()

        # Calculer les totaux
        total_ordered = sum(order.total_amount for order in orders)
        total_invoiced = sum(invoice.total_amount for invoice in invoices)
        total_paid = sum(payment.amount for payment in payments)

        # Regrouper par fournisseur
        supplier_data = {}
        for order in orders:
            if order.supplier_id not in supplier_data:
                supplier_data[order.supplier_id] = {
                    "supplier_id": order.supplier_id,
                    "supplier_name": order.supplier.name if order.supplier else "N/A",
                    "total_ordered": 0,
                    "total_invoiced": 0,
                    "total_paid": 0,
                    "orders": []
                }

            supplier_data[order.supplier_id]["total_ordered"] += order.total_amount
            supplier_data[order.supplier_id]["orders"].append({
                "id": order.id,
                "po_number": order.po_number,
                "order_date": order.order_date,
                "status": order.status.value if hasattr(order.status, 'value') else order.status,
                "total_amount": order.total_amount,
                "payment_status": order.payment_status
            })

        for invoice in invoices:
            if invoice.supplier_id in supplier_data:
                supplier_data[invoice.supplier_id]["total_invoiced"] += invoice.total_amount

        for payment in payments:
            if payment.supplier_id in supplier_data:
                supplier_data[invoice.supplier_id]["total_paid"] += payment.amount

        # Calculer les indicateurs financiers
        payment_efficiency = (total_paid / total_invoiced) * 100 if total_invoiced > 0 else 0
        average_payment_delay = 0  # TODO: Calculer le délai moyen de paiement

        return {
            "period": {
                "start_date": start_date,
                "end_date": end_date
            },
            "summary": {
                "total_ordered": total_ordered,
                "total_invoiced": total_invoiced,
                "total_paid": total_paid,
                "payment_efficiency": payment_efficiency,
                "average_payment_delay": average_payment_delay,
                "orders_count": len(orders),
                "invoices_count": len(invoices),
                "payments_count": len(payments)
            },
            "suppliers": list(supplier_data.values()),
            "payment_methods": {
                payment_method.value: sum(p.amount for p in payments if p.payment_method == payment_method)
                for payment_method in PaymentMethod
            },
            "monthly_data": {
                # Regrouper les données par mois
                # TODO: Implémenter cette partie
            }
        }

    async def get_supplier_orders(self, supplier_id: int) -> List[PurchaseOrder]:
        """
        Récupère toutes les commandes d'achat d'un fournisseur

        Args:
            supplier_id: ID du fournisseur

        Returns:
            Liste des commandes d'achat du fournisseur
        """
        from sqlalchemy.orm import joinedload, subqueryload
        return self.db.query(self.model).options(
            subqueryload(self.model.items).joinedload(PurchaseOrderItem.product),
            joinedload(self.model.supplier),
            joinedload(self.model.creator)
        ).filter(
            self.model.supplier_id == supplier_id
        ).order_by(self.model.order_date.desc()).all()