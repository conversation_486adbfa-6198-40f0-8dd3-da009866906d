"""
Service de génération et gestion des références unifiées pour les paiements
"""

import uuid
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import text, or_

from app.core.models.repair import RepairPayment
from app.core.models.sale import Payment
from app.core.models.customer import CustomerTransaction
from app.core.models.treasury import CashTransaction


class ReferenceType:
    """Types de références pour les opérations de paiement"""
    REPAIR_PAYMENT = "REP"      # Paiement de réparation
    SALE_PAYMENT = "SAL"        # Paiement de vente
    CUSTOMER_TRANSACTION = "CUS" # Transaction client
    TREASURY_TRANSACTION = "TRE" # Transaction trésorerie
    REFUND = "REF"              # Remboursement
    TRANSFER = "TRF"            # Transfert


class ReferenceService:
    """Service de génération et gestion des références unifiées"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def generate_payment_reference(
        self,
        reference_type: str,
        entity_id: Optional[int] = None,
        user_id: Optional[int] = None,
        custom_suffix: Optional[str] = None
    ) -> str:
        """
        Génère une référence unique pour un paiement.
        
        Format: {TYPE}-{YYYYMMDD}-{HHMMSS}-{ENTITY_ID}-{UNIQUE_ID}
        Exemple: REP-20250910-143022-123-A1B2C3
        
        Args:
            reference_type: Type de référence (REP, SAL, CUS, etc.)
            entity_id: ID de l'entité liée (repair_id, sale_id, etc.)
            user_id: ID de l'utilisateur qui traite
            custom_suffix: Suffixe personnalisé (optionnel)
        """
        now = datetime.now(timezone.utc)
        date_part = now.strftime("%Y%m%d")
        time_part = now.strftime("%H%M%S")
        
        # Générer un identifiant unique court
        unique_id = str(uuid.uuid4())[:6].upper()
        
        # Construire la référence
        parts = [reference_type, date_part, time_part]
        
        if entity_id:
            parts.append(str(entity_id))
        
        if custom_suffix:
            parts.append(custom_suffix)
        else:
            parts.append(unique_id)
        
        reference = "-".join(parts)
        
        # Vérifier l'unicité et ajuster si nécessaire
        counter = 1
        original_reference = reference
        while self.reference_exists(reference):
            # Générer un nouveau suffixe unique
            new_unique_id = str(uuid.uuid4())[:6].upper()
            reference = f"{original_reference[:-6]}{new_unique_id}"
            counter += 1
            # Éviter les boucles infinies
            if counter > 10:
                reference = f"{original_reference}-{counter:02d}"
                break
        
        return reference
    
    def reference_exists(self, reference: str) -> bool:
        """Vérifie si une référence existe déjà dans le système"""
        try:
            # Vérifier dans RepairPayment
            repair_payment = self.db.query(RepairPayment).filter(
                RepairPayment.reference_number == reference
            ).first()
            if repair_payment:
                return True
            
            # Vérifier dans Payment (ventes)
            sale_payment = self.db.query(Payment).filter(
                Payment.reference == reference
            ).first()
            if sale_payment:
                return True
            
            # Vérifier dans CustomerTransaction
            customer_transaction = self.db.query(CustomerTransaction).filter(
                CustomerTransaction.reference_number == reference
            ).first()
            if customer_transaction:
                return True
            
            # Vérifier dans CashTransaction
            treasury_transaction = self.db.query(CashTransaction).filter(
                CashTransaction.reference_number == reference
            ).first()
            if treasury_transaction:
                return True
            
            return False
            
        except Exception as e:
            print(f"Erreur lors de la vérification de référence: {e}")
            return True  # En cas d'erreur, considérer comme existante pour éviter les doublons
    
    def find_payment_by_reference(self, reference: str) -> Optional[Dict[str, Any]]:
        """
        Trouve un paiement par sa référence dans tous les systèmes.
        
        Returns:
            Dict avec 'type', 'payment', 'related_entities' ou None si non trouvé
        """
        try:
            # Chercher dans RepairPayment
            repair_payment = self.db.query(RepairPayment).filter(
                RepairPayment.reference_number == reference
            ).first()
            if repair_payment:
                return {
                    'type': 'repair_payment',
                    'payment': repair_payment,
                    'related_entities': {
                        'repair_id': repair_payment.repair_order_id,
                        'customer_id': repair_payment.repair_order.customer_id if repair_payment.repair_order else None
                    }
                }
            
            # Chercher dans Payment (ventes)
            sale_payment = self.db.query(Payment).filter(
                Payment.reference == reference
            ).first()
            if sale_payment:
                return {
                    'type': 'sale_payment',
                    'payment': sale_payment,
                    'related_entities': {
                        'sale_id': sale_payment.sale_id,
                        'customer_id': sale_payment.sale.customer_id if sale_payment.sale else None
                    }
                }
            
            # Chercher dans CustomerTransaction
            customer_transaction = self.db.query(CustomerTransaction).filter(
                CustomerTransaction.reference_number == reference
            ).first()
            if customer_transaction:
                return {
                    'type': 'customer_transaction',
                    'payment': customer_transaction,
                    'related_entities': {
                        'customer_id': customer_transaction.customer_id,
                        'repair_id': customer_transaction.repair_order_id,
                        'sale_id': customer_transaction.sale_id
                    }
                }
            
            # Chercher dans CashTransaction
            treasury_transaction = self.db.query(CashTransaction).filter(
                CashTransaction.reference_number == reference
            ).first()
            if treasury_transaction:
                return {
                    'type': 'treasury_transaction',
                    'payment': treasury_transaction,
                    'related_entities': {
                        'cash_register_id': treasury_transaction.cash_register_id,
                        'repair_id': treasury_transaction.repair_id,
                        'sale_id': treasury_transaction.sale_id,
                        'customer_transaction_id': treasury_transaction.customer_transaction_id
                    }
                }
            
            return None
            
        except Exception as e:
            print(f"Erreur lors de la recherche de paiement par référence: {e}")
            return None
    
    def get_related_payments(self, reference: str) -> List[Dict[str, Any]]:
        """
        Trouve tous les paiements liés à une référence (même entité).
        Utile pour tracer toutes les opérations liées à une réparation ou vente.
        """
        related_payments = []
        
        # Trouver le paiement principal
        main_payment = self.find_payment_by_reference(reference)
        if not main_payment:
            return related_payments
        
        related_payments.append(main_payment)
        
        # Chercher les paiements liés selon le type
        try:
            entities = main_payment['related_entities']
            
            # Si c'est lié à une réparation
            if entities.get('repair_id'):
                repair_id = entities['repair_id']
                
                # Autres paiements de réparation
                other_repair_payments = self.db.query(RepairPayment).filter(
                    RepairPayment.repair_order_id == repair_id,
                    RepairPayment.reference_number != reference
                ).all()
                
                for payment in other_repair_payments:
                    related_payments.append({
                        'type': 'repair_payment',
                        'payment': payment,
                        'related_entities': {'repair_id': repair_id}
                    })
                
                # Transactions clients liées
                customer_transactions = self.db.query(CustomerTransaction).filter(
                    CustomerTransaction.repair_order_id == repair_id,
                    CustomerTransaction.reference_number != reference
                ).all()
                
                for tx in customer_transactions:
                    related_payments.append({
                        'type': 'customer_transaction',
                        'payment': tx,
                        'related_entities': {'repair_id': repair_id, 'customer_id': tx.customer_id}
                    })
            
            # Si c'est lié à une vente
            if entities.get('sale_id'):
                sale_id = entities['sale_id']
                
                # Autres paiements de vente
                other_sale_payments = self.db.query(Payment).filter(
                    Payment.sale_id == sale_id,
                    Payment.reference != reference
                ).all()
                
                for payment in other_sale_payments:
                    related_payments.append({
                        'type': 'sale_payment',
                        'payment': payment,
                        'related_entities': {'sale_id': sale_id}
                    })
                
                # Transactions clients liées
                customer_transactions = self.db.query(CustomerTransaction).filter(
                    CustomerTransaction.sale_id == sale_id,
                    CustomerTransaction.reference_number != reference
                ).all()
                
                for tx in customer_transactions:
                    related_payments.append({
                        'type': 'customer_transaction',
                        'payment': tx,
                        'related_entities': {'sale_id': sale_id, 'customer_id': tx.customer_id}
                    })
            
        except Exception as e:
            print(f"Erreur lors de la recherche de paiements liés: {e}")
        
        return related_payments
    
    def generate_linked_reference(self, base_reference: str, operation_type: str) -> str:
        """
        Génère une référence liée à une référence existante.
        Utile pour les remboursements, ajustements, etc.
        
        Args:
            base_reference: Référence de base
            operation_type: Type d'opération (REFUND, ADJUST, etc.)
        """
        # Extraire les parties de la référence de base
        parts = base_reference.split('-')
        if len(parts) >= 3:
            base_type = parts[0]
            date_part = parts[1]
            time_part = parts[2]
            
            # Créer une nouvelle référence liée
            new_time = datetime.now(timezone.utc).strftime("%H%M%S")
            unique_id = str(uuid.uuid4())[:4].upper()
            
            linked_reference = f"{operation_type}-{date_part}-{new_time}-{base_type}-{unique_id}"
            
            # Vérifier l'unicité
            counter = 1
            original_reference = linked_reference
            while self.reference_exists(linked_reference):
                linked_reference = f"{original_reference}-{counter:02d}"
                counter += 1
            
            return linked_reference
        else:
            # Fallback: générer une nouvelle référence
            return self.generate_payment_reference(operation_type)
