"""
Service pour la gestion de la documentation des réparations (photos et notes techniques).
"""
import os
import shutil
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from PIL import Image
import logging

from ..models.repair_photo import RepairPhoto, RepairPhotoPydantic, PhotoType
from ..models.repair_note import RepairNote, RepairNotePydantic, NoteType

class RepairDocsService:
    """Service pour la gestion de la documentation des réparations"""

    def __init__(self, db: Session):
        """Initialise le service avec une session de base de données"""
        self.db = db
        self.photos_dir = os.path.join(os.getcwd(), "data", "photos")
        self.thumbnails_dir = os.path.join(os.getcwd(), "data", "thumbnails")

        # Créer les répertoires s'ils n'existent pas
        os.makedirs(self.photos_dir, exist_ok=True)
        os.makedirs(self.thumbnails_dir, exist_ok=True)

        # Ajouter un logger
        import logging
        self.logger = logging.getLogger(__name__)

    # Méthodes pour les photos

    async def add_photo(self, repair_id: int, file_path: str, photo_type: PhotoType = PhotoType.OTHER,
                        title: Optional[str] = None, description: Optional[str] = None,
                        taken_by: Optional[int] = None) -> RepairPhotoPydantic:
        """
        Ajoute une photo à une réparation.

        Args:
            repair_id: ID de la réparation
            file_path: Chemin du fichier photo
            photo_type: Type de photo
            title: Titre de la photo
            description: Description de la photo
            taken_by: ID du technicien qui a pris la photo

        Returns:
            La photo ajoutée
        """
        # Vérifier que le fichier existe
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Le fichier {file_path} n'existe pas")

        # Créer le répertoire pour les photos de cette réparation
        repair_photos_dir = os.path.join(self.photos_dir, f"repair_{repair_id}")
        repair_thumbnails_dir = os.path.join(self.thumbnails_dir, f"repair_{repair_id}")
        os.makedirs(repair_photos_dir, exist_ok=True)
        os.makedirs(repair_thumbnails_dir, exist_ok=True)

        # Copier le fichier dans le répertoire des photos
        file_name = os.path.basename(file_path)
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        new_file_name = f"{timestamp}_{file_name}"
        new_file_path = os.path.join(repair_photos_dir, new_file_name)
        shutil.copy2(file_path, new_file_path)

        # Créer une miniature
        thumbnail_path = os.path.join(repair_thumbnails_dir, new_file_name)
        self._create_thumbnail(new_file_path, thumbnail_path)

        # Créer l'enregistrement dans la base de données
        photo = RepairPhoto(
            repair_id=repair_id,
            file_path=new_file_path,
            thumbnail_path=thumbnail_path,
            photo_type=photo_type,
            title=title,
            description=description,
            taken_by=taken_by
        )

        self.db.add(photo)
        self.db.commit()
        self.db.refresh(photo)

        return RepairPhotoPydantic.from_orm(photo)

    def _create_thumbnail(self, source_path: str, target_path: str, size: tuple = (200, 200)) -> None:
        """
        Crée une miniature d'une image.

        Args:
            source_path: Chemin de l'image source
            target_path: Chemin de la miniature
            size: Taille de la miniature (largeur, hauteur)
        """
        try:
            with Image.open(source_path) as img:
                img.thumbnail(size)
                img.save(target_path)
        except Exception as e:
            self.logger.error(f"Erreur lors de la création de la miniature: {str(e)}")
            # Copier l'image originale si la création de la miniature échoue
            shutil.copy2(source_path, target_path)

    async def get_photos(self, repair_id: int) -> List[RepairPhotoPydantic]:
        """
        Récupère les photos d'une réparation.

        Args:
            repair_id: ID de la réparation

        Returns:
            Liste des photos
        """
        photos = self.db.query(RepairPhoto).filter(RepairPhoto.repair_id == repair_id).all()
        return [RepairPhotoPydantic.from_orm(photo) for photo in photos]

    async def get_photo(self, photo_id: int) -> Optional[RepairPhotoPydantic]:
        """
        Récupère une photo par son ID.

        Args:
            photo_id: ID de la photo

        Returns:
            La photo ou None si elle n'existe pas
        """
        photo = self.db.query(RepairPhoto).filter(RepairPhoto.id == photo_id).first()
        return RepairPhotoPydantic.from_orm(photo) if photo else None

    async def update_photo(self, photo_id: int, data: Dict[str, Any]) -> Optional[RepairPhotoPydantic]:
        """
        Met à jour une photo.

        Args:
            photo_id: ID de la photo
            data: Données à mettre à jour

        Returns:
            La photo mise à jour ou None si elle n'existe pas
        """
        photo = self.db.query(RepairPhoto).filter(RepairPhoto.id == photo_id).first()
        if not photo:
            return None

        # Mettre à jour les champs
        for key, value in data.items():
            if hasattr(photo, key):
                setattr(photo, key, value)

        self.db.commit()
        self.db.refresh(photo)

        return RepairPhotoPydantic.from_orm(photo)

    async def delete_photo(self, photo_id: int) -> bool:
        """
        Supprime une photo.

        Args:
            photo_id: ID de la photo

        Returns:
            True si la suppression a réussi, False sinon
        """
        photo = self.db.query(RepairPhoto).filter(RepairPhoto.id == photo_id).first()
        if not photo:
            return False

        # Supprimer les fichiers
        try:
            if os.path.exists(photo.file_path):
                os.remove(photo.file_path)
            if photo.thumbnail_path and os.path.exists(photo.thumbnail_path):
                os.remove(photo.thumbnail_path)
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression des fichiers: {str(e)}")

        # Supprimer l'enregistrement
        self.db.delete(photo)
        self.db.commit()

        return True

    # Méthodes pour les notes techniques

    async def add_note(self, repair_id: int, content: str, note_type: NoteType = NoteType.OTHER,
                       title: Optional[str] = None, is_private: bool = False,
                       created_by: Optional[int] = None) -> RepairNotePydantic:
        """
        Ajoute une note technique à une réparation.

        Args:
            repair_id: ID de la réparation
            content: Contenu de la note
            note_type: Type de note
            title: Titre de la note
            is_private: Si la note est privée
            created_by: ID de l'utilisateur qui a créé la note

        Returns:
            La note ajoutée
        """
        note = RepairNote(
            repair_id=repair_id,
            content=content,
            note_type=note_type,
            title=title,
            is_private=is_private,
            created_by=created_by
        )

        self.db.add(note)
        self.db.commit()
        self.db.refresh(note)

        return RepairNotePydantic.from_orm(note)

    async def get_notes(self, repair_id: int, include_private: bool = True) -> List[RepairNotePydantic]:
        """
        Récupère les notes techniques d'une réparation.

        Args:
            repair_id: ID de la réparation
            include_private: Si les notes privées doivent être incluses

        Returns:
            Liste des notes
        """
        query = self.db.query(RepairNote).filter(RepairNote.repair_id == repair_id)
        if not include_private:
            query = query.filter(RepairNote.is_private == False)

        notes = query.all()
        return [RepairNotePydantic.from_orm(note) for note in notes]

    async def get_note(self, note_id: int) -> Optional[RepairNotePydantic]:
        """
        Récupère une note technique par son ID.

        Args:
            note_id: ID de la note

        Returns:
            La note ou None si elle n'existe pas
        """
        note = self.db.query(RepairNote).filter(RepairNote.id == note_id).first()
        return RepairNotePydantic.from_orm(note) if note else None

    async def update_note(self, note_id: int, data: Dict[str, Any]) -> Optional[RepairNotePydantic]:
        """
        Met à jour une note technique.

        Args:
            note_id: ID de la note
            data: Données à mettre à jour

        Returns:
            La note mise à jour ou None si elle n'existe pas
        """
        note = self.db.query(RepairNote).filter(RepairNote.id == note_id).first()
        if not note:
            return None

        # Mettre à jour les champs
        for key, value in data.items():
            if hasattr(note, key):
                setattr(note, key, value)

        self.db.commit()
        self.db.refresh(note)

        return RepairNotePydantic.from_orm(note)

    async def delete_note(self, note_id: int) -> bool:
        """
        Supprime une note technique.

        Args:
            note_id: ID de la note

        Returns:
            True si la suppression a réussi, False sinon
        """
        note = self.db.query(RepairNote).filter(RepairNote.id == note_id).first()
        if not note:
            return False

        self.db.delete(note)
        self.db.commit()

        return True
