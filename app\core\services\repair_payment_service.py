from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, timedelta, timezone
from typing import Optional, List
from sqlalchemy.orm import Session

from app.core.models.repair import (
    RepairOrder,
    RepairPayment,
    PaymentStatus,
    PaymentMethod,
    PaymentRecordType,
)
from app.core.models.treasury import (
    CashRegister,
    CashRegisterType,
    TransactionCategory,
    PaymentMethod as TreasuryPaymentMethod,
)
from app.core.services.treasury_service import TreasuryService


class RepairPaymentService:
    """Service dédié aux paiements de réparations (paiement, remboursement, annulation)."""

    def __init__(self, db: Session):
        self.db = db
        self.treasury = TreasuryService(db)

    def _d(self, v) -> Decimal:
        return Decimal(str(v)).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    async def list_payments(self, repair_id: int) -> List[RepairPayment]:
        return (
            self.db.query(RepairPayment)
            .filter(RepairPayment.repair_order_id == repair_id)
            .order_by(RepairPayment.payment_date.asc())
            .all()
        )

    async def _compute_total_paid(self, repair_id: int) -> Decimal:
        rows = (
            self.db.query(RepairPayment)
            .filter(
                RepairPayment.repair_order_id == repair_id,
                RepairPayment.is_voided == False,
            )
            .all()
        )
        paid = sum(self._d(r.amount) for r in rows if r.record_type == PaymentRecordType.PAYMENT)
        refunded = sum(self._d(r.amount) for r in rows if r.record_type == PaymentRecordType.REFUND)
        return self._d(paid - refunded)

    async def recalc_payment_status(self, repair: RepairOrder) -> PaymentStatus:
        total_paid = await self._compute_total_paid(repair.id)
        repair.total_paid = total_paid
        final_amount = self._d(repair.final_amount or 0)
        if final_amount > 0 and total_paid >= final_amount:
            repair.payment_status = PaymentStatus.PAID
        elif total_paid > 0:
            repair.payment_status = PaymentStatus.PARTIAL
        else:
            repair.payment_status = PaymentStatus.PENDING
        return repair.payment_status

    async def _pick_register(self, provided_id: Optional[int]) -> Optional[CashRegister]:
        if provided_id:
            reg = self.db.query(CashRegister).get(provided_id)
            if reg and getattr(reg, "is_active", True):
                return reg
        return (
            self.db.query(CashRegister)
            .filter(CashRegister.is_active == True, CashRegister.type == CashRegisterType.REPAIR)
            .first()
            or self.db.query(CashRegister)
            .filter(CashRegister.is_active == True, CashRegister.type == CashRegisterType.MAIN)
            .first()
            or self.db.query(CashRegister).filter(CashRegister.is_active == True).first()
        )

    async def create_payment(
        self,
        repair_id: int,
        amount,
        payment_method: PaymentMethod | str,
        processed_by: int,
        payment_date: Optional[datetime] = None,
        reference_number: Optional[str] = None,
        cash_register_id: Optional[int] = None,
        credit_terms: Optional[int] = None,
        idempotency_key: Optional[str] = None,
    ) -> RepairPayment:
        # Idempotence
        if idempotency_key:
            existing = (
                self.db.query(RepairPayment)
                .filter(RepairPayment.idempotency_key == idempotency_key)
                .first()
            )
            if existing:
                return existing

        amt = self._d(amount)
        if amt <= 0:
            raise ValueError("Amount must be > 0")

        if isinstance(payment_method, str):
            try:
                payment_method = PaymentMethod(payment_method)
            except Exception:
                payment_method = PaymentMethod.cash

        repair = self.db.query(RepairOrder).get(repair_id)
        if not repair:
            raise ValueError("Repair order not found")

        pay_dt = payment_date or datetime.now(timezone.utc)
        payment = RepairPayment(
            repair_order_id=repair_id,
            amount=amt,
            payment_method=payment_method,
            payment_date=pay_dt,
            reference_number=reference_number,
            processed_by=processed_by,
            idempotency_key=idempotency_key,
            record_type=PaymentRecordType.PAYMENT,
        )
        self.db.add(payment)

        # Crédit: due_date
        if payment_method == PaymentMethod.credit and credit_terms is not None:
            repair.credit_terms = int(credit_terms)
            repair.due_date = pay_dt + timedelta(days=repair.credit_terms)

        # Trésorerie (hors crédit)
        treasury_success = False
        if payment_method != PaymentMethod.credit:
            try:
                register = await self._pick_register(cash_register_id)
                if register and processed_by:
                    try:
                        treas_method = TreasuryPaymentMethod(payment_method.value)
                    except Exception:
                        treas_method = TreasuryPaymentMethod.other

                    await self.treasury.add_transaction({
                        "cash_register_id": register.id,
                        "amount": float(amt),
                        "transaction_date": pay_dt,
                        "category": TransactionCategory.REPAIR,
                        "payment_method": treas_method,
                        "reference_number": reference_number,
                        "description": f"Paiement réparation #{repair.number}",
                        "sale_id": None,
                        "repair_id": repair_id,
                        "purchase_id": None,
                        "supplier_payment_id": None,
                        "customer_transaction_id": None,
                        "expense_id": None,
                        "user_id": processed_by,
                    })
                    treasury_success = True
                    print(f"Transaction de trésorerie créée avec succès pour le paiement de réparation #{repair.number}")
                else:
                    print(f"Avertissement: Caisse non trouvée ou utilisateur non spécifié pour le paiement de réparation #{repair.number}")
            except Exception as e:
                # Log l'erreur mais ne pas bloquer le paiement
                print(f"Erreur lors de l'écriture en trésorerie: {e}")
                import traceback
                traceback.print_exc()
        else:
            # Pour les paiements à crédit, pas de transaction de trésorerie
            treasury_success = True

        # Maj statut/total
        await self.recalc_payment_status(repair)
        if repair.payment_status == PaymentStatus.PAID:
            repair.payment_date = pay_dt

        # Commit de la transaction
        try:
            self.db.commit()
            self.db.refresh(payment)
            self.db.refresh(repair)
            print(f"Paiement enregistré avec succès: ID {payment.id}, Montant {amt} DA, Statut réparation: {repair.payment_status.value}")
        except Exception as e:
            self.db.rollback()
            print(f"Erreur lors du commit: {e}")
            raise

        # Émettre le signal payment_processed
        try:
            from app.utils.event_bus import event_bus
            event_bus.payment_processed.emit(payment.id)
            print(f"Signal payment_processed émis pour le paiement {payment.id}")
        except Exception as e:
            print(f"Erreur lors de l'émission du signal payment_processed: {e}")

        return payment

    async def refund_payment(
        self,
        original_payment_id: int,
        amount,
        reason: str,
        processed_by: int,
        idempotency_key: Optional[str] = None,
        refund_date: Optional[datetime] = None,
    ) -> RepairPayment:
        # Idempotence
        if idempotency_key:
            existing = (
                self.db.query(RepairPayment)
                .filter(RepairPayment.idempotency_key == idempotency_key)
                .first()
            )
            if existing:
                return existing

        orig = self.db.query(RepairPayment).get(original_payment_id)
        if not orig or orig.is_voided:
            raise ValueError("Original payment not found or voided")

        amt = self._d(amount)
        if amt <= 0:
            raise ValueError("Refund amount must be > 0")

        repair = self.db.query(RepairOrder).get(orig.repair_order_id)
        if not repair:
            raise ValueError("Repair order not found")

        refund_dt = refund_date or datetime.now(timezone.utc)
        refund = RepairPayment(
            repair_order_id=repair.id,
            amount=amt,
            payment_method=orig.payment_method,
            payment_date=refund_dt,
            reference_number=orig.reference_number,
            notes=reason,
            processed_by=processed_by,
            idempotency_key=idempotency_key,
            record_type=PaymentRecordType.REFUND,
            original_payment_id=orig.id,
        )
        self.db.add(refund)

        # Trésorerie: écriture négative
        try:
            register = await self._pick_register(None)
            if register and processed_by:
                try:
                    treas_method = TreasuryPaymentMethod(orig.payment_method.value)
                except Exception:
                    treas_method = TreasuryPaymentMethod.other
                await self.treasury.add_transaction({
                    "cash_register_id": register.id,
                    "amount": float(-amt),
                    "transaction_date": refund_dt,
                    "category": TransactionCategory.REPAIR,
                    "payment_method": treas_method,
                    "reference_number": orig.reference_number,
                    "description": f"Remboursement réparation #{repair.number}",
                    "sale_id": None,
                    "repair_id": repair.id,
                    "purchase_id": None,
                    "supplier_payment_id": None,
                    "customer_transaction_id": None,
                    "expense_id": None,
                    "user_id": processed_by,
                })
        except Exception as e:
            print(f"Treasury refund write failed: {e}")

        await self.recalc_payment_status(repair)
        self.db.commit()
        self.db.refresh(refund)
        return refund

    async def void_payment(self, payment_id: int, reason: str, voided_by: int) -> bool:
        pay = self.db.query(RepairPayment).get(payment_id)
        if not pay or pay.is_voided:
            return False
        pay.is_voided = True
        pay.void_reason = reason
        pay.voided_by = voided_by
        pay.voided_at = datetime.now(timezone.utc)

        repair = self.db.query(RepairOrder).get(pay.repair_order_id)
        await self.recalc_payment_status(repair)

        self.db.commit()
        return True