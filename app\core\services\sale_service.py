from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta, timezone
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_
from ..models.sale import (
    Sale, SalePydantic, SaleItem, SaleItemPydantic,
    Payment, PaymentPydantic, Quote, QuotePydantic,
    QuoteItem, QuoteItemPydantic, CustomerLoyalty, CustomerLoyaltyPydantic,
    SaleStatus, PaymentStatus, PaymentMethod, QuoteStatus
)
from ..models.inventory import InventoryItem, MovementType
from ..models.customer import Customer
from .base_service import BaseService
from .inventory_service import InventoryService
from .customer_service import CustomerService
from .treasury_service import TreasuryService
from ..models.treasury import CashRegister, CashRegisterType, TransactionCategory

class SaleService(BaseService[Sale, SalePydantic, SalePydantic]):
    """Service pour la gestion des ventes"""

    def __init__(self, db: Session = None):
        """Initialise le service avec une session de base de données"""
        from app.utils.database import SessionLocal
        if db is None:
            db = SessionLocal()
            print("SaleService: Nouvelle session de base de données créée")
        else:
            print("SaleService: Session de base de données existante utilisée")
        super().__init__(db, Sale)
        self.inventory_service = InventoryService(db)
        self.customer_service = CustomerService(db)
        self.treasury_service = TreasuryService(db)

    def _get_tax_rate(self) -> float:
        """Récupère le taux de TVA depuis les paramètres de l'application"""
        try:
            from app.core.models.settings import Setting
            setting = self.db.query(Setting).filter(Setting.key == "general.tax_rate").first()
            if setting:
                return float(setting.value)
            else:
                # Valeur par défaut si le paramètre n'existe pas
                return 0.0
        except Exception as e:
            print(f"Erreur lors de la récupération du taux de TVA: {e}")
            return 0.0

    async def create_sale(self, sale_data: Dict[str, Any], items: List[Dict[str, Any]], check_stock: bool = True) -> Sale:
        """
        Crée une nouvelle vente avec ses articles

        Args:
            sale_data: Données de la vente
            items: Liste des articles à vendre
            check_stock: Si True, vérifie la disponibilité du stock avant de créer la vente

        Returns:
            L'objet Sale créé

        Raises:
            ValueError: Si un produit n'est pas trouvé ou si le stock est insuffisant
        """
        # Vérifier la disponibilité du stock si nécessaire
        if check_stock and sale_data.get('status') == SaleStatus.COMPLETED:
            # Vérifier le stock pour chaque article
            for item_data in items:
                product_id = item_data.get('product_id')
                quantity = item_data.get('quantity', 1.0)

                # Vérifier la disponibilité
                stock_check = await self.inventory_service.check_stock_availability(product_id, quantity)
                if not stock_check["available"]:
                    # Si le stock est insuffisant, lever une exception
                    product_name = stock_check["product"].name if stock_check["product"] else f"Produit {product_id}"
                    raise ValueError(f"Stock insuffisant pour {product_name}: {stock_check['message']}")

        # Générer un numéro de vente unique
        sale_number = await self._generate_sale_number()

        # Créer la vente
        sale = Sale(
            number=sale_number,
            date=datetime.now(timezone.utc),
            customer_id=sale_data.get('customer_id'),
            user_id=sale_data.get('user_id'),
            status=sale_data.get('status', SaleStatus.DRAFT),
            payment_method=sale_data.get('payment_method'),
            payment_status=sale_data.get('payment_status', PaymentStatus.PENDING),
            notes=sale_data.get('notes'),
            is_invoice=sale_data.get('is_invoice', False)
        )

        if sale.is_invoice:
            sale.invoice_number = await self._generate_invoice_number()
            sale.invoice_date = datetime.now(timezone.utc)

        # Ajouter la vente à la base de données
        self.db.add(sale)
        self.db.flush()  # Pour obtenir l'ID de la vente

        # Ajouter les articles
        total_subtotal = 0.0
        total_tax = 0.0
        total_discount = 0.0

        for item_data in items:
            # Récupérer le produit
            product_id = item_data.get('product_id')
            product = await self.inventory_service.get(product_id)
            if not product:
                raise ValueError(f"Produit avec ID {product_id} non trouvé")

            quantity = item_data.get('quantity', 1.0)
            unit_price = item_data.get('unit_price', product.unit_price)
            discount_percent = item_data.get('discount_percent', 0.0)
            # Utiliser le taux de TVA configuré dans les paramètres
            default_tax_rate = self._get_tax_rate()
            tax_percent = item_data.get('tax_percent', default_tax_rate)

            # Calculer les montants
            subtotal = quantity * unit_price
            discount_amount = subtotal * (discount_percent / 100)
            tax_amount = (subtotal - discount_amount) * (tax_percent / 100)
            total_amount = subtotal - discount_amount + tax_amount

            # Créer l'article de vente
            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=product_id,
                quantity=quantity,
                unit_price=unit_price,
                discount_percent=discount_percent,
                discount_amount=discount_amount,
                tax_percent=tax_percent,
                tax_amount=tax_amount,
                total_amount=total_amount
            )

            self.db.add(sale_item)

            # Mettre à jour les totaux
            total_subtotal += subtotal
            total_tax += tax_amount
            total_discount += discount_amount

            # Mettre à jour le stock si la vente est complétée
            if sale.status == SaleStatus.COMPLETED:
                try:
                    await self.inventory_service.record_movement(
                        item_id=product_id,
                        quantity=quantity,  # La quantité est positive car record_movement gère le signe
                        movement_type=MovementType.OUT,
                        reference=f"Vente #{sale.number}",
                        user_id=sale_data.get('user_id')
                    )
                except Exception as e:
                    # En cas d'erreur, annuler la transaction et relancer l'exception
                    self.db.rollback()
                    raise ValueError(f"Erreur lors de la mise à jour du stock: {str(e)}")

        # Mettre à jour les montants de la vente
        sale.subtotal = total_subtotal
        sale.tax_amount = total_tax
        sale.discount_amount = total_discount
        sale.final_amount = total_subtotal - total_discount + total_tax

        # Enregistrer les paiements si fournis via FinanceService
        payment_data = sale_data.get('payment')
        if payment_data and sale.status == SaleStatus.COMPLETED:
            payment_amount = payment_data.get('amount', sale.final_amount)
            payment_method = payment_data.get('payment_method', PaymentMethod.cash)

            # Import différé pour éviter la dépendance circulaire
            from .finance_service import FinanceService
            finance_service = FinanceService(self.db)

            # Utiliser FinanceService pour créer le paiement avec cohérence trésorerie
            await finance_service.pay_sale(
                sale_id=sale.id,
                amount=payment_amount,
                method=payment_method,
                processed_by=sale_data.get('user_id', 1),
                reference=payment_data.get('reference'),
                payment_date=datetime.now(timezone.utc)
            )

            # Si c'est un paiement à crédit, définir la date d'échéance
            if payment_method == PaymentMethod.credit:
                # Récupérer les conditions de paiement du client
                if sale.customer_id:
                    customer = await self.customer_service.get(sale.customer_id)
                    if customer:
                        payment_terms = customer.default_payment_terms or 30
                        sale.due_date = datetime.now(timezone.utc) + timedelta(days=payment_terms)
                # Ne pas bloquer la vente si l'enregistrement de la transaction échoue

        # Mettre à jour les points de fidélité du client si applicable
        if sale.customer_id and sale.status == SaleStatus.COMPLETED:
            await self._update_customer_loyalty(
                customer_id=sale.customer_id,
                amount=sale.final_amount
            )

        self.db.commit()
        return sale

    async def update_sale_status(self, sale_id: int, status: SaleStatus) -> Sale:
        """Met à jour le statut d'une vente"""
        sale = await self.get(sale_id)
        if not sale:
            raise ValueError(f"Vente avec ID {sale_id} non trouvée")

        old_status = sale.status
        sale.status = status

        # Si la vente passe de brouillon à complétée, mettre à jour le stock
        if old_status == SaleStatus.DRAFT and status == SaleStatus.COMPLETED:
            for item in sale.items:
                await self.inventory_service.record_movement(
                    item_id=item.product_id,
                    quantity=item.quantity,  # La quantité est positive car record_movement gère le signe
                    movement_type=MovementType.OUT,
                    reference=f"Vente #{sale.number}",
                    user_id=sale.user_id
                )

        # Si la vente est annulée, remettre les produits en stock
        if status == SaleStatus.CANCELLED and old_status == SaleStatus.COMPLETED:
            for item in sale.items:
                await self.inventory_service.record_movement(
                    item_id=item.product_id,
                    quantity=item.quantity,  # La quantité est positive car record_movement gère le signe
                    movement_type=MovementType.IN,
                    reference=f"Annulation vente #{sale.number}",
                    user_id=sale.user_id
                )

            # Si le client a payé, créer un remboursement via FinanceService
            if sale.total_paid > 0:
                # Import différé pour éviter la dépendance circulaire
                from .finance_service import FinanceService
                finance_service = FinanceService(self.db)

                # Trouver le dernier paiement pour le remboursement
                last_payment = self.db.query(Payment).filter(
                    Payment.sale_id == sale.id,
                    Payment.amount > 0
                ).order_by(Payment.payment_date.desc()).first()

                if last_payment:
                    await finance_service.refund_sale(
                        sale_payment_id=last_payment.id,
                        amount=sale.total_paid,
                        reason="Remboursement suite à annulation",
                        processed_by=sale.user_id or 1
                    )

                # Mettre à jour le solde du client si nécessaire
                if sale.customer_id and sale.payment_status in [PaymentStatus.PARTIAL, PaymentStatus.OVERDUE]:
                    remaining_amount = sale.final_amount - sale.total_paid
                    await self.customer_service.update_customer_balance(
                        customer_id=sale.customer_id,
                        amount=-remaining_amount
                    )

            sale.payment_status = PaymentStatus.REFUNDED
            sale.total_paid = 0

        self.db.commit()
        return sale

    async def record_payment(self, payment_data: PaymentPydantic) -> Payment:
        """
        Enregistre un paiement pour une vente en utilisant FinanceService pour la cohérence.
        DEPRECATED: Utilisez directement FinanceService.pay_sale() pour les nouveaux développements.
        """
        # Import différé pour éviter la dépendance circulaire
        from .finance_service import FinanceService

        finance_service = FinanceService(self.db)

        # Convertir PaymentPydantic vers les paramètres de FinanceService
        payment = await finance_service.pay_sale(
            sale_id=payment_data.sale_id,
            amount=payment_data.amount,
            method=payment_data.payment_method,
            processed_by=payment_data.processed_by or 1,
            reference=payment_data.reference,
            payment_date=payment_data.payment_date
        )

        # Mettre à jour le solde du client si nécessaire
        sale = await self.get(payment_data.sale_id)
        if sale and sale.customer_id and payment_data.amount > 0:
            await self.customer_service.update_customer_balance(
                customer_id=sale.customer_id,
                amount=-payment_data.amount
            )

        # Émettre le signal payment_processed
        try:
            from app.utils.event_bus import event_bus
            event_bus.payment_processed.emit(payment.id)
        except Exception as e:
            print(f"Erreur lors de l'émission du signal payment_processed: {e}")

        return payment

    async def get_sale_payments(self, sale_id: int) -> List[Payment]:
        """Récupère les paiements d'une vente"""
        return (
            self.db.query(Payment)
            .filter(Payment.sale_id == sale_id)
            .order_by(Payment.payment_date)
            .all()
        )

    async def create_quote(self, quote_data: Dict[str, Any], items: List[Dict[str, Any]]) -> Quote:
        """Crée un nouveau devis avec ses articles"""
        # Générer un numéro de devis unique
        quote_number = await self._generate_quote_number()

        # Calculer la date de validité
        validity_days = quote_data.get('validity_days', 30)
        valid_until = datetime.now(timezone.utc) + timedelta(days=validity_days)

        # Créer le devis
        quote = Quote(
            number=quote_number,
            date=datetime.now(timezone.utc),
            customer_id=quote_data.get('customer_id'),
            user_id=quote_data.get('user_id'),
            status=QuoteStatus.DRAFT,
            validity_days=validity_days,
            valid_until=valid_until,
            notes=quote_data.get('notes')
        )

        # Ajouter le devis à la base de données
        self.db.add(quote)
        self.db.flush()  # Pour obtenir l'ID du devis

        # Ajouter les articles
        total_subtotal = 0.0
        total_tax = 0.0
        total_discount = 0.0

        for item_data in items:
            # Récupérer le produit
            product_id = item_data.get('product_id')
            product = await self.inventory_service.get(product_id)
            if not product:
                raise ValueError(f"Produit avec ID {product_id} non trouvé")

            quantity = item_data.get('quantity', 1.0)
            unit_price = item_data.get('unit_price', product.unit_price)
            discount_percent = item_data.get('discount_percent', 0.0)
            # Utiliser le taux de TVA configuré dans les paramètres
            default_tax_rate = self._get_tax_rate()
            tax_percent = item_data.get('tax_percent', default_tax_rate)

            # Calculer les montants
            subtotal = quantity * unit_price
            discount_amount = subtotal * (discount_percent / 100)
            tax_amount = (subtotal - discount_amount) * (tax_percent / 100)
            total_amount = subtotal - discount_amount + tax_amount

            # Créer l'article de devis
            quote_item = QuoteItem(
                quote_id=quote.id,
                product_id=product_id,
                quantity=quantity,
                unit_price=unit_price,
                discount_percent=discount_percent,
                discount_amount=discount_amount,
                tax_percent=tax_percent,
                tax_amount=tax_amount,
                total_amount=total_amount
            )

            self.db.add(quote_item)

            # Mettre à jour les totaux
            total_subtotal += subtotal
            total_tax += tax_amount
            total_discount += discount_amount

        # Mettre à jour les montants du devis
        quote.subtotal = total_subtotal
        quote.tax_amount = total_tax
        quote.discount_amount = total_discount
        quote.final_amount = total_subtotal - total_discount + total_tax

        self.db.commit()
        return quote

    async def convert_quote_to_sale(self, quote_id: int, user_id: int, payment_data: Optional[Dict[str, Any]] = None) -> Sale:
        """Convertit un devis en vente"""
        quote = self.db.query(Quote).filter(Quote.id == quote_id).first()
        if not quote:
            raise ValueError(f"Devis avec ID {quote_id} non trouvé")

        # Vérifier si le devis est valide
        if quote.status != QuoteStatus.ACCEPTED:
            raise ValueError(f"Le devis doit être accepté pour être converti en vente")

        if quote.valid_until and quote.valid_until < datetime.now(timezone.utc):
            raise ValueError(f"Le devis a expiré")

        # Créer la vente
        sale_data = {
            'customer_id': quote.customer_id,
            'user_id': user_id,
            'status': SaleStatus.COMPLETED,
            'notes': f"Converti depuis le devis #{quote.number}",
            'is_invoice': True
        }

        # Ajouter les informations de paiement si fournies
        if payment_data:
            sale_data['payment'] = payment_data
            sale_data['payment_method'] = payment_data.get('payment_method')

        # Récupérer les articles du devis
        items = []
        for quote_item in quote.items:
            items.append({
                'product_id': quote_item.product_id,
                'quantity': quote_item.quantity,
                'purchase_unit_price': getattr(quote_item, 'purchase_unit_price', getattr(quote_item, 'unit_price', 0)),
                'discount_percent': quote_item.discount_percent,
                'tax_percent': quote_item.tax_percent
            })

        # Créer la vente
        sale = await self.create_sale(sale_data, items)

        # Mettre à jour le statut du devis
        quote.status = QuoteStatus.CONVERTED
        quote.sale_id = sale.id

        self.db.commit()
        return sale

    async def get_sales_by_date_range(self, start_date: datetime, end_date: datetime) -> List[Sale]:
        """Récupère les ventes dans une plage de dates"""
        return (
            self.db.query(Sale)
            .filter(Sale.date >= start_date, Sale.date <= end_date)
            .order_by(desc(Sale.date))
            .all()
        )

    async def get_sales_by_customer(self, customer_id: int) -> List[Sale]:
        """Récupère les ventes d'un client"""
        return (
            self.db.query(Sale)
            .filter(Sale.customer_id == customer_id)
            .order_by(desc(Sale.date))
            .all()
        )

    async def get_sales_by_user(self, user_id: int) -> List[Sale]:
        """Récupère les ventes d'un utilisateur"""
        return (
            self.db.query(Sale)
            .filter(Sale.user_id == user_id)
            .order_by(desc(Sale.date))
            .all()
        )

    async def get_daily_sales_summary(self) -> Dict[str, Any]:
        """Récupère un résumé des ventes du jour"""
        try:
            # Obtenir la date du jour au format YYYY-MM-DD
            today_str = datetime.now().strftime('%Y-%m-%d')
            today_date = datetime.now().date()

            # Vérifier si la table Sale existe
            try:
                # Utiliser une requête SQL directe pour filtrer par date
                # SQLite stocke les dates au format ISO, donc on peut filtrer avec LIKE
                completed_sales = (
                    self.db.query(Sale)
                    .filter(
                        Sale.date.like(f"{today_str}%"),  # Filtre sur la date du jour
                        Sale.status == SaleStatus.COMPLETED
                    )
                    .all()
                )

                # Calculer les statistiques
                total_sales = len(completed_sales)
                total_amount = sum(sale.final_amount for sale in completed_sales)
                total_paid = sum(sale.total_paid for sale in completed_sales)

                # Répartition par méthode de paiement
                payment_methods = {}
                for sale in completed_sales:
                    if sale.payment_method:
                        method = sale.payment_method.value
                        if method not in payment_methods:
                            payment_methods[method] = 0
                        payment_methods[method] += sale.total_paid

                # Meilleures ventes
                try:
                    top_products = (
                        self.db.query(
                            InventoryItem.id,
                            InventoryItem.name,
                            func.sum(SaleItem.quantity).label('total_quantity'),
                            func.sum(SaleItem.total_amount).label('total_amount')
                        )
                        .join(SaleItem, SaleItem.product_id == InventoryItem.id)
                        .join(Sale, Sale.id == SaleItem.sale_id)
                        .filter(
                            Sale.date.like(f"{today_str}%"),  # Filtre sur la date du jour
                            Sale.status == SaleStatus.COMPLETED
                        )
                        .group_by(InventoryItem.id, InventoryItem.name)
                        .order_by(desc('total_amount'))
                        .limit(5)
                        .all()
                    )
                except Exception as e:
                    print(f"Erreur lors de la récupération des meilleures ventes: {e}")
                    top_products = []

                # Meilleurs clients
                try:
                    top_customers = (
                        self.db.query(
                            Customer.id,
                            Customer.name,
                            func.count(Sale.id).label('total_sales'),
                            func.sum(Sale.final_amount).label('total_amount')
                        )
                        .join(Sale, Sale.customer_id == Customer.id)
                        .filter(
                            Sale.date.like(f"{today_str}%"),  # Filtre sur la date du jour
                            Sale.status == SaleStatus.COMPLETED
                        )
                        .group_by(Customer.id, Customer.name)
                        .order_by(desc('total_amount'))
                        .limit(5)
                        .all()
                    )
                except Exception as e:
                    print(f"Erreur lors de la récupération des meilleurs clients: {e}")
                    top_customers = []

            except Exception as e:
                print(f"Erreur lors de la récupération des ventes: {e}")
                # En cas d'erreur (par exemple, table inexistante), retourner des données vides
                return {
                    'date': today_date.strftime('%Y-%m-%d'),
                    'total_sales': 0,
                    'total_amount': 0,
                    'total_paid': 0,
                    'payment_methods': {},
                    'top_products': [],
                    'top_customers': []
                }

            return {
                'date': today_date.strftime('%Y-%m-%d'),
                'total_sales': total_sales,
                'total_amount': total_amount,
                'total_paid': total_paid,
                'payment_methods': payment_methods,
                'top_products': [
                    {
                        'id': p.id,
                        'name': p.name,
                        'quantity': p.total_quantity,
                        'amount': p.total_amount
                    }
                    for p in top_products
                ],
                'top_customers': [
                    {
                        'id': c.id,
                        'name': c.name,
                        'sales': c.total_sales,
                        'amount': c.total_amount
                    }
                    for c in top_customers
                ]
            }
        except Exception as e:
            print(f"Erreur générale lors de la récupération du résumé des ventes: {e}")
            import traceback
            traceback.print_exc()

            # En cas d'erreur, retourner des données vides
            return {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'total_sales': 0,
                'total_amount': 0,
                'total_paid': 0,
                'payment_methods': {},
                'top_products': [],
                'top_customers': []
            }

    async def get_sales_report(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Génère un rapport de ventes pour une période donnée"""
        # Récupérer les ventes de la période
        sales = await self.get_sales_by_date_range(start_date, end_date)

        # Calculer les statistiques
        total_sales = len(sales)
        completed_sales = len([s for s in sales if s.status == SaleStatus.COMPLETED])
        cancelled_sales = len([s for s in sales if s.status == SaleStatus.CANCELLED])

        total_amount = sum(sale.final_amount for sale in sales if sale.status == SaleStatus.COMPLETED)
        total_paid = sum(sale.total_paid for sale in sales if sale.status == SaleStatus.COMPLETED)
        total_due = total_amount - total_paid

        # Répartition par méthode de paiement
        payment_methods = {}
        for sale in sales:
            if sale.status == SaleStatus.COMPLETED and sale.payment_method:
                method = sale.payment_method.value
                if method not in payment_methods:
                    payment_methods[method] = 0
                payment_methods[method] += sale.total_paid

        # Répartition par statut de paiement
        payment_statuses = {}
        for sale in sales:
            if sale.status == SaleStatus.COMPLETED:
                status = sale.payment_status.value
                if status not in payment_statuses:
                    payment_statuses[status] = 0
                payment_statuses[status] += 1

        # Ventes par jour
        sales_by_day = {}
        for sale in sales:
            if sale.status == SaleStatus.COMPLETED:
                day = sale.date.date().strftime('%Y-%m-%d')
                if day not in sales_by_day:
                    sales_by_day[day] = {
                        'count': 0,
                        'amount': 0
                    }
                sales_by_day[day]['count'] += 1
                sales_by_day[day]['amount'] += sale.final_amount

        return {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'total_sales': total_sales,
            'completed_sales': completed_sales,
            'cancelled_sales': cancelled_sales,
            'total_amount': total_amount,
            'total_paid': total_paid,
            'total_due': total_due,
            'payment_methods': payment_methods,
            'payment_statuses': payment_statuses,
            'sales_by_day': sales_by_day
        }

    async def get_customer_debt_summary(self) -> List[Dict[str, Any]]:
        """Récupère un résumé des dettes clients"""
        # Récupérer les clients avec des dettes
        customers_with_debt = (
            self.db.query(Customer)
            .filter(Customer.current_balance > 0)
            .all()
        )

        result = []
        for customer in customers_with_debt:
            # Récupérer les ventes impayées
            unpaid_sales = (
                self.db.query(Sale)
                .filter(
                    Sale.customer_id == customer.id,
                    Sale.status == SaleStatus.COMPLETED,
                    Sale.payment_status.in_([PaymentStatus.PENDING, PaymentStatus.PARTIAL, PaymentStatus.OVERDUE])
                )
                .order_by(Sale.date)
                .all()
            )

            # Calculer les montants
            total_debt = sum(sale.final_amount - sale.total_paid for sale in unpaid_sales)
            overdue_debt = sum(
                sale.final_amount - sale.total_paid
                for sale in unpaid_sales
                if sale.payment_status == PaymentStatus.OVERDUE
            )

            # Calculer l'ancienneté de la dette
            oldest_sale = unpaid_sales[0] if unpaid_sales else None
            debt_age = None
            if oldest_sale:
                debt_age = (datetime.now(timezone.utc) - oldest_sale.date).days

            result.append({
                'customer_id': customer.id,
                'customer_name': customer.name,
                'total_debt': total_debt,
                'overdue_debt': overdue_debt,
                'debt_age': debt_age,
                'credit_limit': customer.credit_limit,
                'available_credit': max(0, customer.credit_limit - customer.current_balance),
                'unpaid_sales': len(unpaid_sales)
            })

        return result

    async def get_customer_unpaid_sales(self, customer_id: int) -> List[Sale]:
        """Récupère les ventes impayées d'un client

        Args:
            customer_id: ID du client

        Returns:
            Liste des ventes impayées
        """
        return (
            self.db.query(Sale)
            .filter(
                Sale.customer_id == customer_id,
                Sale.status == SaleStatus.COMPLETED,
                Sale.payment_status.in_([PaymentStatus.PENDING, PaymentStatus.PARTIAL])
            )
            .order_by(desc(Sale.date))
            .all()
        )

    async def get_customer_loyalty(self, customer_id: int) -> Optional[CustomerLoyalty]:
        """Récupère les informations de fidélité d'un client"""
        return (
            self.db.query(CustomerLoyalty)
            .filter(CustomerLoyalty.customer_id == customer_id)
            .first()
        )

    async def get_product_sales_stats(self, product_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Récupère les statistiques de vente d'un produit sur une période donnée

        Args:
            product_id: ID du produit
            start_date: Date de début de la période
            end_date: Date de fin de la période

        Returns:
            Dictionnaire contenant les statistiques de vente du produit
        """
        # Récupérer le produit
        product = await self.inventory_service.get(product_id)
        if not product:
            raise ValueError(f"Produit avec ID {product_id} non trouvé")

        # Récupérer les ventes du produit sur la période
        sale_items = (
            self.db.query(
                func.sum(SaleItem.quantity).label('total_quantity'),
                func.sum(SaleItem.total_amount).label('total_amount'),
                func.count(SaleItem.id).label('sale_count')
            )
            .join(Sale)
            .filter(
                SaleItem.product_id == product_id,
                Sale.date.between(start_date, end_date),
                Sale.status == SaleStatus.COMPLETED
            )
            .first()
        )

        # Initialiser les totaux
        quantity_sold = sale_items.total_quantity or 0
        revenue = sale_items.total_amount or 0
        sale_count = sale_items.sale_count or 0

        # Calculer le coût total
        cost = product.purchase_price * quantity_sold

        # Calculer la marge
        margin = revenue - cost

        # Calculer le pourcentage de marge
        margin_percent = (margin / revenue * 100) if revenue > 0 else 0

        # Récupérer les dernières ventes
        recent_sales = (
            self.db.query(
                Sale.id,
                Sale.number,
                Sale.date,
                SaleItem.quantity,
                SaleItem.purchase_unit_price,
                SaleItem.total_amount
            )
            .join(SaleItem)
            .filter(
                SaleItem.product_id == product_id,
                Sale.date.between(start_date, end_date),
                Sale.status == SaleStatus.COMPLETED
            )
            .order_by(desc(Sale.date))
            .limit(5)
            .all()
        )

        # Récupérer l'évolution des ventes par jour
        sales_by_day = (
            self.db.query(
                func.date(Sale.date).label('day'),
                func.sum(SaleItem.quantity).label('quantity'),
                func.sum(SaleItem.total_amount).label('amount')
            )
            .join(SaleItem)
            .filter(
                SaleItem.product_id == product_id,
                Sale.date.between(start_date, end_date),
                Sale.status == SaleStatus.COMPLETED
            )
            .group_by('day')
            .order_by('day')
            .all()
        )

        return {
            'product_id': product_id,
            'product_name': product.name,
            'sku': getattr(product, 'sku', None),
            'purchase_price': product.purchase_price,
            'selling_price': product.unit_price,
            'current_stock': product.quantity,
            'quantity_sold': quantity_sold,
            'revenue': revenue,
            'cost': cost,
            'margin': margin,
            'margin_percent': margin_percent,
            'sale_count': sale_count,
            'recent_sales': [
                {
                    'sale_id': sale.id,
                    'sale_number': sale.number,
                    'date': sale.date.strftime('%Y-%m-%d %H:%M'),
                    'quantity': sale.quantity,
                    'unit_price': sale.unit_price,
                    'total_amount': sale.total_amount
                }
                for sale in recent_sales
            ],
            'sales_by_day': [
                {
                    'day': day.day.strftime('%Y-%m-%d'),
                    'quantity': day.quantity,
                    'amount': day.amount
                }
                for day in sales_by_day
            ]
        }

    async def _update_customer_loyalty(self, customer_id: int, amount: float) -> None:
        """Met à jour les points de fidélité d'un client"""
        # Récupérer ou créer l'enregistrement de fidélité
        loyalty = await self.get_customer_loyalty(customer_id)
        if not loyalty:
            loyalty = CustomerLoyalty(
                customer_id=customer_id,
                points=0,
                level="Standard",
                discount_percent=0.0,
                last_activity=datetime.now(timezone.utc)
            )
            self.db.add(loyalty)

        # Calculer les points à ajouter (1 point pour chaque 100 DA)
        points_to_add = int(amount / 100)
        loyalty.points += points_to_add
        loyalty.last_activity = datetime.now(timezone.utc)

        # Mettre à jour le niveau et la remise en fonction des points
        if loyalty.points >= 1000:
            loyalty.level = "Platinum"
            loyalty.discount_percent = 10.0
        elif loyalty.points >= 500:
            loyalty.level = "Gold"
            loyalty.discount_percent = 5.0
        elif loyalty.points >= 200:
            loyalty.level = "Silver"
            loyalty.discount_percent = 2.0
        else:
            loyalty.level = "Standard"
            loyalty.discount_percent = 0.0

        self.db.commit()

    async def _generate_sale_number(self) -> str:
        """Génère un numéro de vente unique"""
        # Format: S-YYYYMMDD-XXXX où XXXX est un numéro séquentiel
        today = datetime.now(timezone.utc).strftime('%Y%m%d')

        # Récupérer le dernier numéro de vente pour aujourd'hui
        last_sale = (
            self.db.query(Sale)
            .filter(Sale.number.like(f"S-{today}-%"))
            .order_by(desc(Sale.number))
            .first()
        )

        if last_sale:
            # Extraire le numéro séquentiel et l'incrémenter
            seq_num = int(last_sale.number.split('-')[-1]) + 1
        else:
            seq_num = 1

        return f"S-{today}-{seq_num:04d}"

    async def _generate_invoice_number(self) -> str:
        """Génère un numéro de facture unique"""
        # Format: INV-YYYYMMDD-XXXX où XXXX est un numéro séquentiel
        today = datetime.now(timezone.utc).strftime('%Y%m%d')

        # Récupérer la dernière facture pour aujourd'hui
        last_invoice = (
            self.db.query(Sale)
            .filter(Sale.invoice_number.like(f"INV-{today}-%"))
            .order_by(desc(Sale.invoice_number))
            .first()
        )

        if last_invoice:
            # Extraire le numéro séquentiel et l'incrémenter
            seq_num = int(last_invoice.invoice_number.split('-')[-1]) + 1
        else:
            seq_num = 1

        return f"INV-{today}-{seq_num:04d}"

    async def _generate_quote_number(self) -> str:
        """Génère un numéro de devis unique"""
        # Format: Q-YYYYMMDD-XXXX où XXXX est un numéro séquentiel
        today = datetime.now(timezone.utc).strftime('%Y%m%d')

        # Récupérer le dernier devis pour aujourd'hui
        last_quote = (
            self.db.query(Quote)
            .filter(Quote.number.like(f"Q-{today}-%"))
            .order_by(desc(Quote.number))
            .first()
        )

        if last_quote:
            # Extraire le numéro séquentiel et l'incrémenter
            seq_num = int(last_quote.number.split('-')[-1]) + 1
        else:
            seq_num = 1

        return f"Q-{today}-{seq_num:04d}"

    async def update_sale(self, sale_id: int, sale_data: Dict[str, Any], items: List[Dict[str, Any]]) -> Sale:
        """
        Met à jour une vente existante et ses articles

        Args:
            sale_id: ID de la vente à mettre à jour
            sale_data: Nouvelles données de la vente
            items: Nouvelle liste des articles

        Returns:
            L'objet Sale mis à jour

        Raises:
            ValueError: Si la vente n'est pas trouvée ou si le stock est insuffisant
        """
        # Récupérer la vente existante
        sale = await self.get(sale_id)
        if not sale:
            raise ValueError(f"Vente avec ID {sale_id} non trouvée")

        # Vérifier si la vente peut être modifiée
        if sale.status == SaleStatus.COMPLETED:
            raise ValueError("Impossible de modifier une vente complétée")

        # Sauvegarder l'ancien statut
        old_status = sale.status

        # Mettre à jour les informations de base
        sale.customer_id = sale_data.get('customer_id', sale.customer_id)
        sale.user_id = sale_data.get('user_id', sale.user_id)
        sale.status = sale_data.get('status', sale.status)
        sale.payment_method = sale_data.get('payment_method', sale.payment_method)
        sale.notes = sale_data.get('notes', sale.notes)
        sale.is_invoice = sale_data.get('is_invoice', sale.is_invoice)

        # Si la vente devient une facture, générer un numéro de facture
        if sale.is_invoice and not sale.invoice_number:
            sale.invoice_number = await self._generate_invoice_number()
            sale.invoice_date = datetime.now(timezone.utc)

        # Supprimer les anciens articles
        for item in sale.items:
            self.db.delete(item)

        # Ajouter les nouveaux articles
        total_subtotal = 0.0
        total_tax = 0.0
        total_discount = 0.0

        for item_data in items:
            # Récupérer le produit
            product_id = item_data.get('product_id')
            product = await self.inventory_service.get(product_id)
            if not product:
                raise ValueError(f"Produit avec ID {product_id} non trouvé")

            quantity = item_data.get('quantity', 1.0)
            unit_price = item_data.get('unit_price', product.unit_price)
            discount_percent = item_data.get('discount_percent', 0.0)
            # Utiliser le taux de TVA configuré dans les paramètres
            default_tax_rate = self._get_tax_rate()
            tax_percent = item_data.get('tax_percent', default_tax_rate)

            # Calculer les montants
            subtotal = quantity * unit_price
            discount_amount = subtotal * (discount_percent / 100)
            tax_amount = (subtotal - discount_amount) * (tax_percent / 100)
            total_amount = subtotal - discount_amount + tax_amount

            # Créer l'article de vente
            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=product_id,
                quantity=quantity,
                unit_price=unit_price,
                discount_percent=discount_percent,
                discount_amount=discount_amount,
                tax_percent=tax_percent,
                tax_amount=tax_amount,
                total_amount=total_amount
            )

            self.db.add(sale_item)

            # Mettre à jour les totaux
            total_subtotal += subtotal
            total_tax += tax_amount
            total_discount += discount_amount

        # Mettre à jour les montants de la vente
        sale.subtotal = total_subtotal
        sale.tax_amount = total_tax
        sale.discount_amount = total_discount
        sale.final_amount = total_subtotal - total_discount + total_tax

        # Si la vente passe de brouillon à complétée, mettre à jour le stock
        if old_status == SaleStatus.DRAFT and sale.status == SaleStatus.COMPLETED:
            for item in sale.items:
                await self.inventory_service.record_movement(
                    item_id=item.product_id,
                    quantity=item.quantity,  # La quantité est positive car record_movement gère le signe
                    movement_type=MovementType.OUT,
                    reference=f"Vente #{sale.number}",
                    user_id=sale.user_id
                )

        # Enregistrer les paiements si fournis via FinanceService
        payment_data = sale_data.get('payment')
        if payment_data and sale.status == SaleStatus.COMPLETED:
            payment_amount = payment_data.get('amount', sale.final_amount)
            payment_method = payment_data.get('payment_method', PaymentMethod.cash)

            # Import différé pour éviter la dépendance circulaire
            from .finance_service import FinanceService
            finance_service = FinanceService(self.db)

            # Utiliser FinanceService pour créer le paiement avec cohérence trésorerie
            await finance_service.pay_sale(
                sale_id=sale.id,
                amount=payment_amount,
                method=payment_method,
                processed_by=sale.user_id or 1,
                reference=payment_data.get('reference'),
                payment_date=datetime.now(timezone.utc)
            )

            # Si c'est un paiement à crédit, définir la date d'échéance
            if payment_method == PaymentMethod.credit:
                    # Récupérer les conditions de paiement du client
                    if sale.customer_id:
                        customer = await self.customer_service.get(sale.customer_id)
                        if customer:
                            payment_terms = customer.default_payment_terms or 30
                            sale.due_date = datetime.now(timezone.utc) + timedelta(days=payment_terms)

                            # Mettre à jour le solde du client
                            remaining_amount = sale.final_amount - payment_amount
                            await self.customer_service.update_customer_balance(
                                customer_id=sale.customer_id,
                                amount=remaining_amount
                            )

            # Enregistrer la transaction dans la caisse des ventes
            try:
                # Trouver la caisse des ventes
                sales_cash_register = self.db.query(CashRegister).filter(
                    CashRegister.type == CashRegisterType.SALES,
                    CashRegister.is_active == True
                ).first()

                if sales_cash_register:
                    # Créer une transaction dans la caisse
                    transaction_data = {
                        "cash_register_id": sales_cash_register.id,
                        "amount": payment_amount,
                        "transaction_date": datetime.now(timezone.utc),
                        "category": TransactionCategory.SALE,
                        "payment_method": payment_method,
                        "reference_number": f"SALE-{sale.number}",
                        "description": f"Paiement pour vente #{sale.number}",
                        "sale_id": sale.id,
                        "user_id": sale_data.get('user_id', 1)  # Utiliser l'ID 1 par défaut si non spécifié
                    }

                    # Ajouter la transaction à la caisse
                    await self.treasury_service.add_transaction(transaction_data)

                    # Notifier la mise à jour de la trésorerie
                    try:
                        from app.utils.treasury_updater import notify_sale_payment
                        notify_sale_payment(sale.id, payment_amount, sales_cash_register.id)
                    except Exception as notify_error:
                        print(f"Erreur lors de la notification de mise à jour de la trésorerie: {notify_error}")

            except Exception as e:
                print(f"Erreur lors de l'enregistrement de la transaction dans la caisse: {str(e)}")
                # Ne pas bloquer la vente si l'enregistrement de la transaction échoue

        # Mettre à jour les points de fidélité du client si applicable
        if sale.customer_id and sale.status == SaleStatus.COMPLETED:
            await self._update_customer_loyalty(
                customer_id=sale.customer_id,
                amount=sale.final_amount
            )

        self.db.commit()
        return sale
