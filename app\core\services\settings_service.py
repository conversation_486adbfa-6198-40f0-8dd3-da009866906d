"""
Service pour la gestion des paramètres de l'application.
"""
import os
import json
import shutil
import sqlite3
import zipfile
import datetime
import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app.core.models.settings import (
    AppSetting, BackupInfo, SettingCategory,
    AppSettingPydantic, BackupInfoPydantic,
    BackupFrequency, AppSettingsUpdate
)
from app.core.services.base_service import BaseService
from app.core.services.notification_service import NotificationService
from app.core.models.notification import NotificationType, NotificationPriority, NotificationChannel
from app.utils.config import get_settings, get_db_path
from config.settings import get_settings as get_app_settings

logger = logging.getLogger(__name__)

class SettingsService(BaseService[AppSetting, AppSettingPydantic, AppSettingPydantic]):
    """Service pour la gestion des paramètres de l'application"""

    def __init__(self, db: Session = None):
        """Initialise le service avec une session de base de données"""
        from app.utils.database import SessionLocal
        if db is None:
            db = SessionLocal()
            print("SettingsService: Nouvelle session de base de données créée")
        else:
            print("SettingsService: Session de base de données existante utilisée")
        super().__init__(db, AppSetting)
        self.notification_service = NotificationService(db)

    async def get_setting(self, key: str) -> Optional[AppSetting]:
        """
        Récupère un paramètre par sa clé

        Args:
            key: Clé du paramètre

        Returns:
            Le paramètre ou None s'il n'existe pas
        """
        return self.db.query(self.model).filter(self.model.key == key).first()

    async def get_setting_value(self, key: str, default: Any = None) -> Any:
        """
        Récupère la valeur d'un paramètre par sa clé

        Args:
            key: Clé du paramètre
            default: Valeur par défaut si le paramètre n'existe pas

        Returns:
            La valeur du paramètre ou la valeur par défaut
        """
        setting = await self.get_setting(key)
        if not setting:
            return default

        # Convertir la valeur en fonction du type attendu
        value = setting.value

        # Booléen
        if value.lower() in ["true", "false"]:
            return value.lower() == "true"

        # Nombre entier
        try:
            if "." not in value:
                return int(value)
        except ValueError:
            pass

        # Nombre à virgule flottante
        try:
            if "." in value:
                return float(value)
        except ValueError:
            pass

        # Chaîne de caractères
        return value

    async def set_setting(self, key: str, value: Any, category: str = None, description: str = None) -> AppSetting:
        """
        Définit la valeur d'un paramètre

        Args:
            key: Clé du paramètre
            value: Valeur du paramètre
            category: Catégorie du paramètre (si création)
            description: Description du paramètre (si création)

        Returns:
            Le paramètre mis à jour ou créé
        """
        # Convertir la valeur en chaîne de caractères
        if isinstance(value, bool):
            value_str = str(value).lower()
        else:
            value_str = str(value)

        # Vérifier si le paramètre existe déjà
        setting = await self.get_setting(key)
        if setting:
            # Mettre à jour le paramètre existant
            setting.value = value_str
            if category:
                setting.category = category
            if description:
                setting.description = description
            self.db.commit()
            self.db.refresh(setting)
            return setting
        else:
            # Créer un nouveau paramètre
            if not category:
                # Déterminer la catégorie à partir de la clé
                category_prefix = key.split('.')[0]
                try:
                    category = SettingCategory(category_prefix)
                except ValueError:
                    category = SettingCategory.GENERAL

            # Créer le paramètre
            new_setting = AppSetting(
                key=key,
                value=value_str,
                category=category,
                description=description or "",
                is_system=False
            )
            self.db.add(new_setting)
            self.db.commit()
            self.db.refresh(new_setting)
            return new_setting

    async def get_settings_by_category(self, category: Union[str, SettingCategory]) -> List[AppSetting]:
        """
        Récupère tous les paramètres d'une catégorie

        Args:
            category: Catégorie des paramètres

        Returns:
            Liste des paramètres de la catégorie
        """
        if isinstance(category, SettingCategory):
            category = category.value

        return self.db.query(self.model).filter(self.model.category == category).all()

    async def get_settings_dict_by_category(self, category: Union[str, SettingCategory]) -> Dict[str, Any]:
        """
        Récupère tous les paramètres d'une catégorie sous forme de dictionnaire

        Args:
            category: Catégorie des paramètres

        Returns:
            Dictionnaire des paramètres de la catégorie
        """
        settings = await self.get_settings_by_category(category)
        result = {}

        for setting in settings:
            # Extraire le nom du paramètre sans le préfixe de catégorie
            key_parts = setting.key.split('.')
            if len(key_parts) > 1:
                param_name = key_parts[1]
            else:
                param_name = key_parts[0]

            # Convertir la valeur en fonction du type attendu
            value = setting.value

            # Booléen
            if value.lower() in ["true", "false"]:
                result[param_name] = value.lower() == "true"
                continue

            # Nombre entier
            try:
                if "." not in value:
                    result[param_name] = int(value)
                    continue
            except ValueError:
                pass

            # Nombre à virgule flottante
            try:
                if "." in value:
                    result[param_name] = float(value)
                    continue
            except ValueError:
                pass

            # Chaîne de caractères
            result[param_name] = value

        return result

    async def get_all_settings_dict(self) -> Dict[str, Dict[str, Any]]:
        """
        Récupère tous les paramètres sous forme de dictionnaire organisé par catégorie

        Returns:
            Dictionnaire des paramètres organisé par catégorie
        """
        result = {}

        for category in SettingCategory:
            result[category.value] = await self.get_settings_dict_by_category(category)

        return result

    async def update_settings(self, settings_update: AppSettingsUpdate, user_id: int = None) -> Dict[str, Any]:
        """
        Met à jour plusieurs paramètres à la fois

        Args:
            settings_update: Paramètres à mettre à jour
            user_id: ID de l'utilisateur effectuant la mise à jour

        Returns:
            Dictionnaire des paramètres mis à jour
        """
        updated_settings = {}

        # Parcourir toutes les catégories de paramètres
        for category_name, category_settings in settings_update.dict(exclude_unset=True).items():
            if not category_settings:
                continue

            # Mettre à jour chaque paramètre de la catégorie
            for key, value in category_settings.items():
                setting_key = f"{category_name}.{key}"
                await self.set_setting(setting_key, value, category_name)
                updated_settings[setting_key] = value

        # Créer une notification
        if user_id and updated_settings:
            await self.notification_service.create_notification({
                "user_id": user_id,
                "type": NotificationType.SYSTEM,
                "priority": NotificationPriority.LOW,
                "title": "Paramètres mis à jour",
                "message": f"{len(updated_settings)} paramètres ont été mis à jour.",
                "data": {"updated_settings": list(updated_settings.keys())},
                "action_url": "/settings",
                "icon": "settings",
                "channels": [NotificationChannel.UI]
            })

        return updated_settings

    async def reset_settings_to_default(self, category: Optional[Union[str, SettingCategory]] = None) -> int:
        """
        Réinitialise les paramètres aux valeurs par défaut

        Args:
            category: Catégorie des paramètres à réinitialiser (None pour tous)

        Returns:
            Nombre de paramètres réinitialisés
        """
        # Exécuter la migration pour réinitialiser les paramètres
        from app.utils.migrations.add_settings_tables import run_migration
        run_migration()

        # Compter le nombre de paramètres réinitialisés
        if category:
            if isinstance(category, SettingCategory):
                category = category.value
            count = self.db.query(self.model).filter(self.model.category == category).count()
        else:
            count = self.db.query(self.model).count()

        return count

class BackupService(BaseService[BackupInfo, BackupInfoPydantic, BackupInfoPydantic]):
    """Service pour la gestion des sauvegardes"""

    def __init__(self, db: Session = None):
        """Initialise le service avec une session de base de données"""
        from app.utils.database import SessionLocal
        if db is None:
            db = SessionLocal()
            print("BackupService: Nouvelle session de base de données créée")
        else:
            print("BackupService: Session de base de données existante utilisée")
        super().__init__(db, BackupInfo)
        self.settings_service = SettingsService(db)
        self.notification_service = NotificationService(db)

    async def create_backup(self, description: str = None, user_id: int = None, is_auto: bool = False) -> BackupInfo:
        """
        Crée une sauvegarde de la base de données

        Args:
            description: Description de la sauvegarde
            user_id: ID de l'utilisateur effectuant la sauvegarde
            is_auto: Indique si la sauvegarde est automatique

        Returns:
            Informations sur la sauvegarde créée
        """
        try:
            # Récupérer les paramètres de sauvegarde
            backup_path = await self.settings_service.get_setting_value("backup.backup_path", "./backups")
            compress_backup = await self.settings_service.get_setting_value("backup.compress_backup", True)
            include_attachments = await self.settings_service.get_setting_value("backup.include_attachments", True)

            # Créer le répertoire de sauvegarde s'il n'existe pas
            os.makedirs(backup_path, exist_ok=True)

            # Générer le nom de fichier de sauvegarde
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            app_settings = get_app_settings()
            # Supprimer les guillemets et autres caractères non valides pour un nom de fichier
            version = str(app_settings.app_version).replace('"', '').replace("'", "").strip()

            if compress_backup:
                filename = f"backup_{timestamp}_v{version}.zip"
            else:
                filename = f"backup_{timestamp}_v{version}.db"

            backup_file_path = os.path.join(backup_path, filename)

            # Récupérer le chemin de la base de données
            db_path = get_db_path()

            # Fermer la connexion à la base de données
            self.db.close()

            if compress_backup:
                # Créer une archive ZIP
                with zipfile.ZipFile(backup_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # Ajouter la base de données
                    zipf.write(db_path, os.path.basename(db_path))

                    # Ajouter les pièces jointes si demandé
                    if include_attachments:
                        attachments_dir = os.path.join(os.path.dirname(db_path), "attachments")
                        if os.path.exists(attachments_dir):
                            for root, _, files in os.walk(attachments_dir):
                                for file in files:
                                    file_path = os.path.join(root, file)
                                    zipf.write(file_path, os.path.relpath(file_path, os.path.dirname(db_path)))
            else:
                # Copier simplement le fichier de base de données
                shutil.copy2(db_path, backup_file_path)

            # Récupérer la taille du fichier de sauvegarde
            size = os.path.getsize(backup_file_path)

            # Reconnecter à la base de données
            from app.utils.database import SessionLocal
            self.db = SessionLocal()

            # Créer l'entrée de sauvegarde dans la base de données
            backup_info = BackupInfo(
                filename=filename,
                path=backup_file_path,
                size=size,
                created_by=user_id,
                description=description,
                version=version,
                is_auto=is_auto,
                backup_metadata={
                    "compress": compress_backup,
                    "include_attachments": include_attachments,
                    "db_path": db_path
                }
            )
            self.db.add(backup_info)
            self.db.commit()
            self.db.refresh(backup_info)

            # Nettoyer les anciennes sauvegardes
            await self.cleanup_old_backups()

            # Notification désactivée pour éviter les doublons
            pass

            return backup_info

        except Exception as e:
            logger.error(f"Erreur lors de la création de la sauvegarde: {str(e)}")

            # Reconnecter à la base de données en cas d'erreur
            from app.utils.database import SessionLocal
            self.db = SessionLocal()

            # Créer une notification d'erreur
            if not is_auto and user_id:
                await self.notification_service.create_notification({
                    "user_id": user_id,
                    "type": NotificationType.SYSTEM,
                    "priority": NotificationPriority.HIGH,
                    "title": "Erreur de sauvegarde",
                    "message": f"Une erreur est survenue lors de la création de la sauvegarde: {str(e)}",
                    "action_url": "/settings/backups",
                    "icon": "error",
                    "channels": [NotificationChannel.UI]
                })

            raise

    async def restore_backup(self, backup_id: int, user_id: int = None) -> bool:
        """
        Restaure une sauvegarde

        Args:
            backup_id: ID de la sauvegarde à restaurer
            user_id: ID de l'utilisateur effectuant la restauration

        Returns:
            True si la restauration a réussi, False sinon
        """
        try:
            # Récupérer les informations de la sauvegarde
            backup_info = self.db.query(BackupInfo).get(backup_id)

            if not backup_info:
                print(f"Sauvegarde avec ID {backup_id} non trouvée dans la base de données, recherche par nom de fichier...")

                # Essayer de trouver la sauvegarde dans le répertoire de sauvegarde
                backup_dir = os.path.join(os.getcwd(), "backups")
                if os.path.exists(backup_dir):
                    # Parcourir tous les fichiers de sauvegarde
                    for filename in os.listdir(backup_dir):
                        if filename.startswith("backup_") and (filename.endswith(".zip") or filename.endswith(".db")):
                            file_path = os.path.join(backup_dir, filename)

                            # Vérifier si ce fichier correspond à l'ID demandé
                            try:
                                # Créer un objet BackupInfo temporaire
                                file_size = os.path.getsize(file_path)

                                # Extraire la date de création à partir du nom de fichier
                                import re
                                from datetime import datetime as dt
                                match = re.search(r'backup_(\d{8}_\d{6})', filename)
                                if match:
                                    timestamp_str = match.group(1)
                                    try:
                                        created_at = dt.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                                    except ValueError:
                                        created_at = dt.now()
                                else:
                                    created_at = dt.now()

                                # Extraire la version à partir du nom de fichier
                                match = re.search(r'_v([^\.]+\.[^\.]+\.[^\.]+)', filename)
                                version = match.group(1) if match else "1.0.0"
                                # Supprimer les guillemets et autres caractères non valides pour un nom de fichier
                                version = str(version).replace('"', '').replace("'", "").strip()

                                # Créer un nouvel objet BackupInfo et l'enregistrer dans la base de données
                                new_backup = BackupInfo(
                                    filename=filename,
                                    path=file_path,
                                    size=file_size,
                                    created_at=created_at,
                                    description="Sauvegarde importée automatiquement",
                                    version=version,
                                    is_auto=False,
                                    backup_metadata={
                                        "compress": filename.endswith(".zip"),
                                        "include_attachments": True,
                                        "db_path": get_db_path()
                                    }
                                )

                                self.db.add(new_backup)
                                self.db.commit()

                                # Vérifier si l'ID correspond
                                if new_backup.id == backup_id:
                                    print(f"Sauvegarde trouvée et enregistrée avec ID {new_backup.id}")
                                    backup_info = new_backup
                                    break
                            except Exception as e:
                                print(f"Erreur lors de l'enregistrement de la sauvegarde {filename}: {e}")
                                self.db.rollback()

            if not backup_info:
                raise ValueError(f"Sauvegarde avec ID {backup_id} non trouvée")

            # Vérifier que le fichier de sauvegarde existe
            if not os.path.exists(backup_info.path):
                raise FileNotFoundError(f"Fichier de sauvegarde non trouvé: {backup_info.path}")

            # Récupérer le chemin de la base de données
            db_path = get_db_path()

            # Créer une sauvegarde de la base de données actuelle avant restauration
            await self.create_backup("Sauvegarde automatique avant restauration", user_id, True)

            # Fermer la connexion à la base de données
            self.db.close()

            # Restaurer la base de données
            if backup_info.path.endswith(".zip"):
                # Extraire l'archive ZIP
                with zipfile.ZipFile(backup_info.path, 'r') as zipf:
                    # Extraire la base de données
                    db_filename = os.path.basename(db_path)
                    with zipf.open(db_filename) as source, open(db_path, 'wb') as target:
                        shutil.copyfileobj(source, target)

                    # Extraire les pièces jointes si présentes
                    attachments_dir = os.path.join(os.path.dirname(db_path), "attachments")
                    for file_info in zipf.infolist():
                        if file_info.filename.startswith("attachments/"):
                            zipf.extract(file_info, os.path.dirname(db_path))
            else:
                # Copier simplement le fichier de base de données
                shutil.copy2(backup_info.path, db_path)

            # Reconnecter à la base de données
            from app.utils.database import SessionLocal
            self.db = SessionLocal()

            # Exécuter les migrations pour s'assurer que la base de données est à jour
            from app.utils.db_migration import run_migrations
            run_migrations()

            # Créer une notification
            if user_id:
                await self.notification_service.create_notification({
                    "user_id": user_id,
                    "type": NotificationType.SYSTEM,
                    "priority": NotificationPriority.MEDIUM,
                    "title": "Sauvegarde restaurée",
                    "message": f"La sauvegarde '{backup_info.filename}' a été restaurée avec succès.",
                    "data": {"backup_id": backup_id},
                    "action_url": "/settings/backups",
                    "icon": "restore",
                    "channels": [NotificationChannel.UI]
                })

            return True

        except Exception as e:
            logger.error(f"Erreur lors de la restauration de la sauvegarde: {str(e)}")

            # Reconnecter à la base de données en cas d'erreur
            from app.utils.database import SessionLocal
            self.db = SessionLocal()

            # Créer une notification d'erreur
            if user_id:
                await self.notification_service.create_notification({
                    "user_id": user_id,
                    "type": NotificationType.SYSTEM,
                    "priority": NotificationPriority.HIGH,
                    "title": "Erreur de restauration",
                    "message": f"Une erreur est survenue lors de la restauration de la sauvegarde: {str(e)}",
                    "action_url": "/settings/backups",
                    "icon": "error",
                    "channels": [NotificationChannel.UI]
                })

            raise

    async def delete_backup(self, backup_id: int, user_id: int = None) -> bool:
        """
        Supprime une sauvegarde

        Args:
            backup_id: ID de la sauvegarde à supprimer
            user_id: ID de l'utilisateur effectuant la suppression

        Returns:
            True si la suppression a réussi, False sinon
        """
        try:
            # Récupérer les informations de la sauvegarde
            backup_info = self.db.query(BackupInfo).get(backup_id)
            if not backup_info:
                raise ValueError(f"Sauvegarde avec ID {backup_id} non trouvée")

            # Supprimer le fichier de sauvegarde
            if os.path.exists(backup_info.path):
                os.remove(backup_info.path)

            # Supprimer l'entrée de la base de données
            self.db.delete(backup_info)
            self.db.commit()

            # Notification désactivée pour éviter les doublons
            # Nous n'affichons pas de message ici car il sera affiché dans l'interface utilisateur

            return True

        except Exception as e:
            logger.error(f"Erreur lors de la suppression de la sauvegarde: {str(e)}")

            # Désactivation des notifications d'erreur pour éviter les doublons
            # L'erreur sera affichée dans l'interface utilisateur

            raise

    async def get_backups(self, skip: int = 0, limit: int = 100) -> List[BackupInfo]:
        """
        Récupère la liste des sauvegardes

        Args:
            skip: Nombre d'entrées à sauter
            limit: Nombre maximum d'entrées à retourner

        Returns:
            Liste des sauvegardes
        """
        return self.db.query(BackupInfo).order_by(desc(BackupInfo.created_at)).offset(skip).limit(limit).all()

    async def cleanup_old_backups(self) -> int:
        """
        Nettoie les anciennes sauvegardes en fonction du nombre maximum configuré

        Returns:
            Nombre de sauvegardes supprimées
        """
        try:
            # Récupérer le nombre maximum de sauvegardes
            max_backups = await self.settings_service.get_setting_value("backup.max_backups", 10)

            # Récupérer toutes les sauvegardes
            backups = self.db.query(BackupInfo).order_by(desc(BackupInfo.created_at)).all()

            # Si le nombre de sauvegardes est inférieur ou égal au maximum, ne rien faire
            if len(backups) <= max_backups:
                return 0

            # Supprimer les sauvegardes excédentaires
            backups_to_delete = backups[max_backups:]
            count = 0

            for backup in backups_to_delete:
                # Supprimer le fichier de sauvegarde
                if os.path.exists(backup.path):
                    os.remove(backup.path)

                # Supprimer l'entrée de la base de données
                self.db.delete(backup)
                count += 1

            self.db.commit()

            return count

        except Exception as e:
            logger.error(f"Erreur lors du nettoyage des anciennes sauvegardes: {str(e)}")
            return 0

    async def optimize_database(self, user_id: int = None) -> bool:
        """
        Optimise la base de données

        Args:
            user_id: ID de l'utilisateur effectuant l'optimisation

        Returns:
            True si l'optimisation a réussi, False sinon
        """
        try:
            # Récupérer le chemin de la base de données
            db_path = get_db_path()

            # Fermer la connexion à la base de données
            self.db.close()

            # Optimiser la base de données
            conn = sqlite3.connect(db_path)
            conn.execute("VACUUM")
            conn.execute("ANALYZE")
            conn.close()

            # Reconnecter à la base de données
            from app.utils.database import SessionLocal
            self.db = SessionLocal()

            # Créer une notification
            if user_id:
                await self.notification_service.create_notification({
                    "user_id": user_id,
                    "type": NotificationType.SYSTEM,
                    "priority": NotificationPriority.LOW,
                    "title": "Base de données optimisée",
                    "message": "La base de données a été optimisée avec succès.",
                    "action_url": "/settings/database",
                    "icon": "database",
                    "channels": [NotificationChannel.UI]
                })

            return True

        except Exception as e:
            logger.error(f"Erreur lors de l'optimisation de la base de données: {str(e)}")

            # Reconnecter à la base de données en cas d'erreur
            from app.utils.database import SessionLocal
            self.db = SessionLocal()

            # Créer une notification d'erreur
            if user_id:
                await self.notification_service.create_notification({
                    "user_id": user_id,
                    "type": NotificationType.SYSTEM,
                    "priority": NotificationPriority.MEDIUM,
                    "title": "Erreur d'optimisation",
                    "message": f"Une erreur est survenue lors de l'optimisation de la base de données: {str(e)}",
                    "action_url": "/settings/database",
                    "icon": "error",
                    "channels": [NotificationChannel.UI]
                })

            raise

    async def reset_database(self, user_id: int = None) -> bool:
        """
        Réinitialise la base de données

        Args:
            user_id: ID de l'utilisateur effectuant la réinitialisation

        Returns:
            True si la réinitialisation a réussi, False sinon
        """
        try:
            # Créer une sauvegarde avant réinitialisation
            await self.create_backup("Sauvegarde automatique avant réinitialisation", user_id, True)

            # Fermer la connexion à la base de données
            self.db.close()

            # Récupérer le chemin de la base de données
            db_path = get_db_path()

            # Supprimer la base de données
            if os.path.exists(db_path):
                os.remove(db_path)

            # Reconnecter à la base de données (cela va créer une nouvelle base de données vide)
            from app.utils.database import SessionLocal, init_db
            self.db = SessionLocal()

            # Initialiser la base de données
            init_db()

            # Exécuter les migrations
            from app.utils.db_migration import run_migrations
            run_migrations()

            # Créer une notification
            if user_id:
                await self.notification_service.create_notification({
                    "user_id": user_id,
                    "type": NotificationType.SYSTEM,
                    "priority": NotificationPriority.HIGH,
                    "title": "Base de données réinitialisée",
                    "message": "La base de données a été réinitialisée avec succès.",
                    "action_url": "/settings/database",
                    "icon": "database",
                    "channels": [NotificationChannel.UI]
                })

            return True

        except Exception as e:
            logger.error(f"Erreur lors de la réinitialisation de la base de données: {str(e)}")

            # Reconnecter à la base de données en cas d'erreur
            from app.utils.database import SessionLocal
            self.db = SessionLocal()

            # Créer une notification d'erreur
            if user_id:
                await self.notification_service.create_notification({
                    "user_id": user_id,
                    "type": NotificationType.SYSTEM,
                    "priority": NotificationPriority.HIGH,
                    "title": "Erreur de réinitialisation",
                    "message": f"Une erreur est survenue lors de la réinitialisation de la base de données: {str(e)}",
                    "action_url": "/settings/database",
                    "icon": "error",
                    "channels": [NotificationChannel.UI]
                })

            raise
