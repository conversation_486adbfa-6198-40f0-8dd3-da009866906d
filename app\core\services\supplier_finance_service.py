"""
Service pour la gestion financière des fournisseurs.
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app.core.models.supplier_finance import (
    SupplierInvoice, SupplierInvoicePydantic,
    SupplierPayment, SupplierPaymentPydantic,
    InvoiceStatus, PaymentMethod
)
from app.core.models.supplier import Supplier
from app.core.models.purchasing import PurchaseOrder
from app.core.services.base_service import BaseService
from app.core.services.notification_service import NotificationService
from app.core.services.accounting_service import AccountingService
from app.core.models.notification import NotificationType, NotificationPriority, NotificationChannel

class SupplierFinanceService(BaseService[SupplierInvoice, SupplierInvoicePydantic, SupplierInvoicePydantic]):
    """Service pour la gestion financière des fournisseurs"""

    def __init__(self, db: Session):
        super().__init__(db, SupplierInvoice)
        self.notification_service = NotificationService(db)
        self.accounting_service = AccountingService(db)

    async def generate_invoice_number(self, purchase_order_id: Optional[int] = None) -> str:
        """
        Génère un numéro de facture unique pour les factures fournisseurs

        Args:
            purchase_order_id: ID de la commande associée (optionnel)

        Returns:
            Un numéro de facture unique
        """
        # Format de base: SINV-YYYYMMDD-XXXX où XXXX est un numéro séquentiel ou l'ID de la commande
        today = datetime.now().strftime('%Y%m%d')

        # Base du numéro de facture
        base_number = f"SINV-{today}-"

        if purchase_order_id:
            # Si une commande est fournie, utiliser son ID comme base
            base_invoice_number = f"{base_number}{purchase_order_id}"
        else:
            base_invoice_number = f"{base_number}1"

        # Vérifier si ce numéro existe déjà
        existing_invoice = self.db.query(SupplierInvoice).filter(
            SupplierInvoice.invoice_number.like(f"{base_number}%")
        ).order_by(desc(SupplierInvoice.invoice_number)).first()

        if not existing_invoice:
            # Si aucune facture n'existe avec ce préfixe, utiliser le numéro de base
            return base_invoice_number

        # Si une facture existe déjà avec ce numéro, ajouter un suffixe incrémental
        try:
            # Extraire le dernier numéro et l'incrémenter
            last_number = existing_invoice.invoice_number
            suffix = last_number.split('-')[-1]

            # Si le suffixe est numérique, l'incrémenter
            if suffix.isdigit():
                new_suffix = int(suffix) + 1
            else:
                # Si le suffixe n'est pas numérique, ajouter -1
                new_suffix = 1

            return f"{base_number}{new_suffix}"
        except Exception as e:
            # En cas d'erreur, utiliser un timestamp comme suffixe pour garantir l'unicité
            timestamp = int(datetime.now().timestamp())
            return f"{base_number}{timestamp}"

    async def create_invoice(self, invoice_data: Dict[str, Any]) -> SupplierInvoice:
        """
        Crée une nouvelle facture fournisseur

        Args:
            invoice_data: Données de la facture

        Returns:
            La facture créée
        """
        # Vérifier le fournisseur
        supplier_id = invoice_data.get("supplier_id")
        supplier = self.db.query(Supplier).get(supplier_id)
        if not supplier:
            raise ValueError(f"Fournisseur avec ID {supplier_id} non trouvé")

        # Vérifier la commande si fournie
        purchase_order_id = invoice_data.get("purchase_order_id")
        if purchase_order_id:
            purchase_order = self.db.query(PurchaseOrder).get(purchase_order_id)
            if not purchase_order:
                raise ValueError(f"Commande avec ID {purchase_order_id} non trouvée")

            # Si la commande est fournie, utiliser son montant total comme montant de la facture
            if "total_amount" not in invoice_data or invoice_data["total_amount"] == 0:
                # Si le montant total de la commande est défini et non nul, l'utiliser
                if purchase_order.total_amount is not None and purchase_order.total_amount > 0:
                    invoice_data["total_amount"] = purchase_order.total_amount
                # Sinon, calculer le montant total à partir des articles de la commande
                elif hasattr(purchase_order, 'items') and purchase_order.items:
                    total = 0
                    for item in purchase_order.items:
                        if hasattr(item, 'quantity'):
                            price = getattr(item, 'purchase_unit_price', getattr(item, 'unit_price', 0))
                            total += price * item.quantity
                    if total > 0:
                        invoice_data["total_amount"] = total

        # Générer un numéro de facture unique si non fourni
        invoice_number = invoice_data.get("invoice_number")
        if not invoice_number:
            invoice_number = await self.generate_invoice_number(purchase_order_id)
        else:
            # Vérifier si le numéro de facture existe déjà
            existing_invoice = self.db.query(SupplierInvoice).filter(
                SupplierInvoice.invoice_number == invoice_number
            ).first()

            if existing_invoice:
                # Si le numéro existe déjà, générer un nouveau numéro
                invoice_number = await self.generate_invoice_number(purchase_order_id)

        # Créer la facture
        invoice = SupplierInvoice(
            invoice_number=invoice_number,
            supplier_id=supplier_id,
            purchase_order_id=purchase_order_id,
            invoice_date=invoice_data.get("invoice_date", datetime.now()),
            due_date=invoice_data.get("due_date"),
            total_amount=invoice_data.get("total_amount"),
            tax_amount=invoice_data.get("tax_amount", 0),
            currency=invoice_data.get("currency", "DA"),
            status=InvoiceStatus.PENDING,
            notes=invoice_data.get("notes")
        )

        self.db.add(invoice)
        self.db.commit()

        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": None,  # Notification système
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.MEDIUM,
            "title": "Nouvelle facture fournisseur",
            "message": f"Une nouvelle facture ({invoice.invoice_number}) a été enregistrée pour le fournisseur {supplier.name}.",
            "data": {"invoice_id": invoice.id, "supplier_id": supplier_id},
            "action_url": f"/suppliers/invoices/{invoice.id}",
            "icon": "invoice",
            "channels": [NotificationChannel.UI]
        })

        # Enregistrer dans le système comptable
        try:
            accounting_entry = await self.accounting_service.record_supplier_invoice(invoice)
            print(f"Écriture comptable créée: {accounting_entry}")
        except Exception as e:
            print(f"Erreur lors de l'enregistrement comptable: {str(e)}")
            # Ne pas bloquer le processus en cas d'erreur comptable

        return invoice

    async def record_payment(self, payment_data: Dict[str, Any]) -> SupplierPayment:
        """
        Enregistre un paiement à un fournisseur

        Args:
            payment_data: Données du paiement

        Returns:
            Le paiement créé
        """
        # Vérifier le fournisseur
        supplier_id = payment_data.get("supplier_id")
        supplier = self.db.query(Supplier).get(supplier_id)
        if not supplier:
            raise ValueError(f"Fournisseur avec ID {supplier_id} non trouvé")

        # Vérifier la facture si fournie
        invoice_id = payment_data.get("invoice_id")
        invoice = None
        if invoice_id:
            invoice = self.db.query(SupplierInvoice).get(invoice_id)
            if not invoice:
                raise ValueError(f"Facture avec ID {invoice_id} non trouvée")

        # Vérifier la commande si fournie
        purchase_order_id = payment_data.get("purchase_order_id")
        purchase_order = None
        if purchase_order_id:
            purchase_order = self.db.query(PurchaseOrder).get(purchase_order_id)
            if not purchase_order:
                raise ValueError(f"Commande avec ID {purchase_order_id} non trouvée")

        # Normaliser la méthode de paiement en Enum
        pm = payment_data.get("payment_method")
        try:
            if isinstance(pm, str):
                pm = PaymentMethod(pm)
        except Exception:
            pm = PaymentMethod.other

        # Créer le paiement
        payment = SupplierPayment(
            supplier_id=supplier_id,
            invoice_id=invoice_id,
            purchase_order_id=purchase_order_id,
            payment_date=payment_data.get("payment_date", datetime.now()),
            amount=payment_data.get("amount"),
            payment_method=pm,
            reference=payment_data.get("reference"),
            notes=payment_data.get("notes"),
            processed_by=payment_data.get("processed_by")
        )

        self.db.add(payment)

        # Mettre à jour le statut de la facture si nécessaire
        if invoice:
            # Récupérer tous les paiements pour cette facture
            payments = self.db.query(SupplierPayment).filter(
                SupplierPayment.invoice_id == invoice_id
            ).all()

            # Calculer le montant total payé
            total_paid = sum(p.amount for p in payments) + payment.amount

            # Mettre à jour le statut de la facture
            if total_paid >= invoice.total_amount:
                invoice.status = InvoiceStatus.PAID
                print(f"Facture {invoice.invoice_number} marquée comme PAYÉE")
            elif total_paid > 0:
                invoice.status = InvoiceStatus.PARTIAL
                print(f"Facture {invoice.invoice_number} marquée comme PARTIELLEMENT PAYÉE")

        self.db.commit()

        # Créer une notification
        await self.notification_service.create_notification({
            "user_id": None,  # Notification système
            "type": NotificationType.FINANCE,
            "priority": NotificationPriority.MEDIUM,
            "title": "Paiement fournisseur enregistré",
            "message": f"Un paiement de {payment.amount} {payment.invoice.currency if invoice else 'DA'} a été enregistré pour le fournisseur {supplier.name}.",
            "data": {"payment_id": payment.id, "supplier_id": supplier_id, "invoice_id": invoice_id},
            "action_url": f"/suppliers/payments/{payment.id}",
            "icon": "payment",
            "channels": [NotificationChannel.UI]
        })

        # Enregistrer dans le système comptable
        try:
            accounting_entry = await self.accounting_service.record_supplier_payment(payment)
            print(f"Écriture comptable créée: {accounting_entry}")
        except Exception as e:
            print(f"Erreur lors de l'enregistrement comptable: {str(e)}")
            # Ne pas bloquer le processus en cas d'erreur comptable
            
        # Émettre le signal payment_processed
        try:
            from app.utils.event_bus import event_bus
            event_bus.payment_processed.emit(payment.id)
            print(f"Signal payment_processed émis pour le paiement fournisseur {payment.id}")
        except Exception as e:
            print(f"Erreur lors de l'émission du signal payment_processed: {str(e)}")
            # Ne pas bloquer le processus en cas d'erreur d'émission du signal

        return payment

    async def get_supplier_balance(self, supplier_id: int) -> Dict[str, Any]:
        """
        Calcule le solde d'un fournisseur (montant dû)

        Args:
            supplier_id: ID du fournisseur

        Returns:
            Dictionnaire contenant le solde et les détails
        """
        # Vérifier le fournisseur
        supplier = self.db.query(Supplier).get(supplier_id)
        if not supplier:
            raise ValueError(f"Fournisseur avec ID {supplier_id} non trouvé")

        # Récupérer toutes les factures non payées
        invoices = self.db.query(SupplierInvoice).filter(
            SupplierInvoice.supplier_id == supplier_id,
            SupplierInvoice.status.in_([InvoiceStatus.PENDING, InvoiceStatus.PARTIAL])
        ).all()

        # Calculer le montant total dû
        total_due = sum(invoice.total_amount for invoice in invoices)

        # Récupérer tous les paiements pour les factures en attente ou partiellement payées
        invoice_payments = []
        if invoices:
            invoice_payments = self.db.query(SupplierPayment).filter(
                SupplierPayment.supplier_id == supplier_id,
                SupplierPayment.invoice_id.in_([invoice.id for invoice in invoices])
            ).all()

        # Récupérer tous les paiements pour les factures déjà payées
        paid_invoices = self.db.query(SupplierInvoice).filter(
            SupplierInvoice.supplier_id == supplier_id,
            SupplierInvoice.status == InvoiceStatus.PAID
        ).all()

        paid_invoice_payments = []
        if paid_invoices:
            paid_invoice_payments = self.db.query(SupplierPayment).filter(
                SupplierPayment.supplier_id == supplier_id,
                SupplierPayment.invoice_id.in_([invoice.id for invoice in paid_invoices])
            ).all()

        # Récupérer les paiements directs (acomptes)
        direct_payments = self.db.query(SupplierPayment).filter(
            SupplierPayment.supplier_id == supplier_id,
            SupplierPayment.invoice_id.is_(None)
        ).all()

        # Calculer le montant total payé (tous les paiements)
        all_payments = invoice_payments + paid_invoice_payments + direct_payments
        total_paid = sum(payment.amount for payment in all_payments)

        # Calculer le solde
        balance = total_due - total_paid

        return {
            "supplier_id": supplier_id,
            "supplier_name": supplier.name,
            "total_due": total_due,
            "total_paid": total_paid,
            "balance": balance,
            "invoices": invoices,
            "payments": all_payments
        }

    async def get_overdue_invoices(self) -> List[SupplierInvoice]:
        """
        Récupère les factures en retard de paiement

        Returns:
            Liste des factures en retard
        """
        today = datetime.now().date()

        # Récupérer les factures dont la date d'échéance est dépassée
        overdue_invoices = self.db.query(self.model).filter(
            self.model.status.in_([InvoiceStatus.PENDING, InvoiceStatus.PARTIAL]),
            self.model.due_date < today
        ).all()

        return overdue_invoices

    async def generate_balance_report(self, supplier_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Génère un rapport de solde fournisseur

        Args:
            supplier_id: ID du fournisseur (optionnel, si None, génère pour tous les fournisseurs)

        Returns:
            Dictionnaire contenant les informations du rapport
        """
        # Utiliser le service de comptabilité pour générer le rapport
        return await self.accounting_service.generate_supplier_balance_report(supplier_id)

    async def get_supplier_invoices(self, supplier_id: int) -> List[SupplierInvoice]:
        """
        Récupère les factures d'un fournisseur

        Args:
            supplier_id: ID du fournisseur

        Returns:
            Liste des factures du fournisseur
        """
        from sqlalchemy.orm import joinedload
        return self.db.query(self.model).options(
            joinedload(self.model.purchase_order)
        ).filter(
            self.model.supplier_id == supplier_id
        ).all()

    async def get_all_with_orders(self) -> List[SupplierInvoice]:
        """
        Récupère toutes les factures avec leurs commandes associées

        Returns:
            Liste des factures avec leurs commandes associées
        """
        from sqlalchemy.orm import joinedload
        return self.db.query(self.model).options(
            joinedload(self.model.purchase_order),
            joinedload(self.model.supplier)
        ).all()

    async def get_all_payments(self) -> List[SupplierPayment]:
        """
        Récupère tous les paiements

        Returns:
            Liste de tous les paiements
        """
        return self.db.query(SupplierPayment).all()

    async def get_supplier_payments(self, supplier_id: int) -> List[SupplierPayment]:
        """
        Récupère tous les paiements d'un fournisseur

        Args:
            supplier_id: ID du fournisseur

        Returns:
            Liste des paiements du fournisseur
        """
        from sqlalchemy.orm import joinedload
        payments = self.db.query(SupplierPayment).options(
            joinedload(SupplierPayment.invoice)
        ).filter(
            SupplierPayment.supplier_id == supplier_id
        ).order_by(SupplierPayment.payment_date.desc()).all()

        # Normaliser en lecture (au cas où d’anciennes données seraient en str)
        for p in payments:
            pm = getattr(p, "payment_method", None)
            if isinstance(pm, str):
                try:
                    p.payment_method = PaymentMethod(pm)
                except Exception:
                    p.payment_method = PaymentMethod.other
        return payments

    async def get_invoice_balance(self, invoice_id: int) -> Dict[str, Any]:
        """
        Calcule le solde d'une facture (montant restant à payer)

        Args:
            invoice_id: ID de la facture

        Returns:
            Dictionnaire contenant le solde et les détails
        """
        # Récupérer la facture
        invoice = self.db.query(self.model).get(invoice_id)
        if not invoice:
            raise ValueError(f"Facture avec ID {invoice_id} non trouvée")

        # Récupérer les paiements pour cette facture
        payments = self.db.query(SupplierPayment).filter(
            SupplierPayment.invoice_id == invoice_id
        ).all()

        # Calculer le montant total payé
        total_paid = sum(payment.amount for payment in payments)

        # Calculer le solde
        balance = invoice.total_amount - total_paid

        return {
            "invoice_id": invoice_id,
            "invoice_number": invoice.invoice_number,
            "total_amount": invoice.total_amount,
            "total_paid": total_paid,
            "balance": balance,
            "status": invoice.status.value,
            "payments": payments
        }
