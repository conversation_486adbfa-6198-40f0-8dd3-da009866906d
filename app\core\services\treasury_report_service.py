"""
Service pour la génération de rapports de trésorerie avec exports et pagination.
"""
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func, text
from dataclasses import dataclass

from app.core.models.treasury import CashRegister, CashTransaction, Expense, TransactionCategory, PaymentMethod
from app.core.models.daily_closure import DailyClosure, CashRegisterSnapshot, PeriodLock, ClosureStatus
from app.utils.decimal_utils import safe_decimal_sum, validate_amount
from app.utils.transaction_manager import atomic_operation

logger = logging.getLogger(__name__)


@dataclass
class ReportFilter:
    """Filtre pour les rapports"""
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    cash_register_ids: Optional[List[int]] = None
    transaction_categories: Optional[List[TransactionCategory]] = None
    payment_methods: Optional[List[PaymentMethod]] = None
    min_amount: Optional[Decimal] = None
    max_amount: Optional[Decimal] = None
    search_text: Optional[str] = None


@dataclass
class PaginationInfo:
    """Information de pagination"""
    page: int = 1
    page_size: int = 50
    total_items: int = 0
    total_pages: int = 0
    has_next: bool = False
    has_previous: bool = False


@dataclass
class ReportData:
    """Données d'un rapport"""
    title: str
    subtitle: str
    generated_at: datetime
    filters: ReportFilter
    pagination: PaginationInfo
    summary: Dict[str, Any]
    data: List[Dict[str, Any]]
    charts_data: Optional[Dict[str, Any]] = None


class TreasuryReportService:
    """Service pour la génération de rapports de trésorerie"""
    
    def __init__(self, db: Session):
        """
        Initialise le service de rapports
        
        Args:
            db: Session de base de données
        """
        self.db = db
    
    def get_transactions_report(self, filters: ReportFilter, page: int = 1, page_size: int = 50) -> ReportData:
        """
        Génère un rapport des transactions avec pagination
        
        Args:
            filters: Filtres à appliquer
            page: Numéro de page
            page_size: Taille de la page
            
        Returns:
            Données du rapport
        """
        # Construire la requête de base
        query = self.db.query(CashTransaction).join(CashRegister)
        
        # Appliquer les filtres
        query = self._apply_transaction_filters(query, filters)
        
        # Compter le total
        total_items = query.count()
        
        # Calculer la pagination
        pagination = self._calculate_pagination(page, page_size, total_items)
        
        # Appliquer la pagination et l'ordre
        transactions = (query
                       .order_by(desc(CashTransaction.transaction_date))
                       .offset((page - 1) * page_size)
                       .limit(page_size)
                       .all())
        
        # Préparer les données
        data = []
        for transaction in transactions:
            data.append({
                'id': transaction.id,
                'date': transaction.transaction_date,
                'cash_register': transaction.cash_register.name,
                'amount': float(transaction.amount),
                'category': transaction.category.value if hasattr(transaction.category, 'value') else str(transaction.category),
                'payment_method': transaction.payment_method.value if hasattr(transaction.payment_method, 'value') else str(transaction.payment_method),
                'description': transaction.description or '',
                'reference': transaction.reference_number or ''
            })
        
        # Calculer le résumé
        summary = self._calculate_transactions_summary(filters)
        
        # Générer les données de graphiques
        charts_data = self._generate_transactions_charts(filters)
        
        return ReportData(
            title="Rapport des Transactions",
            subtitle=self._format_filter_subtitle(filters),
            generated_at=datetime.now(),
            filters=filters,
            pagination=pagination,
            summary=summary,
            data=data,
            charts_data=charts_data
        )
    
    def get_cash_registers_report(self, filters: ReportFilter) -> ReportData:
        """
        Génère un rapport des caisses
        
        Args:
            filters: Filtres à appliquer
            
        Returns:
            Données du rapport
        """
        # Récupérer toutes les caisses actives
        query = self.db.query(CashRegister).filter(CashRegister.is_active == True)
        
        if filters.cash_register_ids:
            query = query.filter(CashRegister.id.in_(filters.cash_register_ids))
        
        cash_registers = query.all()
        
        # Préparer les données
        data = []
        total_balance = Decimal("0.00")
        
        for register in cash_registers:
            # Calculer les statistiques pour cette caisse
            stats = self._calculate_register_stats(register.id, filters)
            
            register_data = {
                'id': register.id,
                'name': register.name,
                'type': register.type.value if hasattr(register.type, 'value') else str(register.type),
                'current_balance': float(register.current_balance),
                'initial_balance': float(register.initial_balance),
                'total_in': float(stats['total_in']),
                'total_out': float(stats['total_out']),
                'net_change': float(stats['net_change']),
                'transaction_count': stats['transaction_count'],
                'last_transaction': stats['last_transaction']
            }
            
            data.append(register_data)
            total_balance += validate_amount(register.current_balance)
        
        # Résumé
        summary = {
            'total_registers': len(cash_registers),
            'total_balance': float(total_balance),
            'active_registers': len([r for r in cash_registers if r.is_active])
        }
        
        return ReportData(
            title="Rapport des Caisses",
            subtitle=self._format_filter_subtitle(filters),
            generated_at=datetime.now(),
            filters=filters,
            pagination=PaginationInfo(page=1, page_size=len(data), total_items=len(data), total_pages=1),
            summary=summary,
            data=data
        )
    
    def get_expenses_report(self, filters: ReportFilter, page: int = 1, page_size: int = 50) -> ReportData:
        """
        Génère un rapport des dépenses avec pagination
        
        Args:
            filters: Filtres à appliquer
            page: Numéro de page
            page_size: Taille de la page
            
        Returns:
            Données du rapport
        """
        # Construire la requête de base
        query = self.db.query(Expense).join(CashRegister)
        
        # Appliquer les filtres
        query = self._apply_expense_filters(query, filters)
        
        # Compter le total
        total_items = query.count()
        
        # Calculer la pagination
        pagination = self._calculate_pagination(page, page_size, total_items)
        
        # Appliquer la pagination et l'ordre
        expenses = (query
                   .order_by(desc(Expense.expense_date))
                   .offset((page - 1) * page_size)
                   .limit(page_size)
                   .all())
        
        # Préparer les données
        data = []
        for expense in expenses:
            data.append({
                'id': expense.id,
                'date': expense.expense_date,
                'cash_register': expense.cash_register.name,
                'amount': float(expense.amount),
                'category': expense.category,
                'payment_method': expense.payment_method.value if hasattr(expense.payment_method, 'value') else str(expense.payment_method),
                'description': expense.description or '',
                'reference': expense.reference_number or '',
                'receipt_image': expense.receipt_image or ''
            })
        
        # Calculer le résumé
        summary = self._calculate_expenses_summary(filters)
        
        return ReportData(
            title="Rapport des Dépenses",
            subtitle=self._format_filter_subtitle(filters),
            generated_at=datetime.now(),
            filters=filters,
            pagination=pagination,
            summary=summary,
            data=data
        )
    
    def _apply_transaction_filters(self, query, filters: ReportFilter):
        """Applique les filtres aux transactions"""
        if filters.start_date:
            query = query.filter(CashTransaction.transaction_date >= filters.start_date)
        
        if filters.end_date:
            query = query.filter(CashTransaction.transaction_date <= filters.end_date)
        
        if filters.cash_register_ids:
            query = query.filter(CashTransaction.cash_register_id.in_(filters.cash_register_ids))
        
        if filters.transaction_categories:
            query = query.filter(CashTransaction.category.in_(filters.transaction_categories))
        
        if filters.payment_methods:
            query = query.filter(CashTransaction.payment_method.in_(filters.payment_methods))
        
        if filters.min_amount is not None:
            query = query.filter(CashTransaction.amount >= filters.min_amount)
        
        if filters.max_amount is not None:
            query = query.filter(CashTransaction.amount <= filters.max_amount)
        
        if filters.search_text:
            search_pattern = f"%{filters.search_text}%"
            query = query.filter(
                or_(
                    CashTransaction.description.ilike(search_pattern),
                    CashTransaction.reference_number.ilike(search_pattern),
                    CashRegister.name.ilike(search_pattern)
                )
            )
        
        return query
    
    def _apply_expense_filters(self, query, filters: ReportFilter):
        """Applique les filtres aux dépenses"""
        if filters.start_date:
            query = query.filter(Expense.expense_date >= filters.start_date)
        
        if filters.end_date:
            query = query.filter(Expense.expense_date <= filters.end_date)
        
        if filters.cash_register_ids:
            query = query.filter(Expense.cash_register_id.in_(filters.cash_register_ids))
        
        if filters.payment_methods:
            query = query.filter(Expense.payment_method.in_(filters.payment_methods))
        
        if filters.min_amount is not None:
            query = query.filter(Expense.amount >= filters.min_amount)
        
        if filters.max_amount is not None:
            query = query.filter(Expense.amount <= filters.max_amount)
        
        if filters.search_text:
            search_pattern = f"%{filters.search_text}%"
            query = query.filter(
                or_(
                    Expense.description.ilike(search_pattern),
                    Expense.reference_number.ilike(search_pattern),
                    Expense.category.ilike(search_pattern),
                    CashRegister.name.ilike(search_pattern)
                )
            )
        
        return query
    
    def _calculate_pagination(self, page: int, page_size: int, total_items: int) -> PaginationInfo:
        """Calcule les informations de pagination"""
        total_pages = (total_items + page_size - 1) // page_size
        
        return PaginationInfo(
            page=page,
            page_size=page_size,
            total_items=total_items,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_previous=page > 1
        )
    
    def _calculate_transactions_summary(self, filters: ReportFilter) -> Dict[str, Any]:
        """Calcule le résumé des transactions"""
        query = self.db.query(CashTransaction).join(CashRegister)
        query = self._apply_transaction_filters(query, filters)
        
        transactions = query.all()
        
        total_in = safe_decimal_sum(t.amount for t in transactions if t.amount > 0)
        total_out = safe_decimal_sum(t.amount for t in transactions if t.amount < 0)
        
        return {
            'total_transactions': len(transactions),
            'total_in': float(total_in),
            'total_out': float(abs(total_out)),
            'net_change': float(total_in + total_out),
            'average_transaction': float(safe_decimal_sum(t.amount for t in transactions) / len(transactions)) if transactions else 0
        }
    
    def _calculate_expenses_summary(self, filters: ReportFilter) -> Dict[str, Any]:
        """Calcule le résumé des dépenses"""
        query = self.db.query(Expense).join(CashRegister)
        query = self._apply_expense_filters(query, filters)
        
        expenses = query.all()
        
        total_amount = safe_decimal_sum(e.amount for e in expenses)
        
        return {
            'total_expenses': len(expenses),
            'total_amount': float(total_amount),
            'average_expense': float(total_amount / len(expenses)) if expenses else 0
        }
    
    def _calculate_register_stats(self, register_id: int, filters: ReportFilter) -> Dict[str, Any]:
        """Calcule les statistiques pour une caisse"""
        query = self.db.query(CashTransaction).filter(CashTransaction.cash_register_id == register_id)
        
        if filters.start_date:
            query = query.filter(CashTransaction.transaction_date >= filters.start_date)
        if filters.end_date:
            query = query.filter(CashTransaction.transaction_date <= filters.end_date)
        
        transactions = query.all()
        
        total_in = safe_decimal_sum(t.amount for t in transactions if t.amount > 0)
        total_out = safe_decimal_sum(t.amount for t in transactions if t.amount < 0)
        
        last_transaction = None
        if transactions:
            last_transaction = max(transactions, key=lambda t: t.transaction_date).transaction_date
        
        return {
            'total_in': total_in,
            'total_out': abs(total_out),
            'net_change': total_in + total_out,
            'transaction_count': len(transactions),
            'last_transaction': last_transaction
        }
    
    def _generate_transactions_charts(self, filters: ReportFilter) -> Dict[str, Any]:
        """Génère les données pour les graphiques des transactions"""
        # Données par catégorie
        category_data = {}
        
        # Données par méthode de paiement
        payment_method_data = {}
        
        # Données par jour (pour les 30 derniers jours)
        daily_data = {}
        
        # TODO: Implémenter la génération des données de graphiques
        
        return {
            'categories': category_data,
            'payment_methods': payment_method_data,
            'daily_trends': daily_data
        }
    
    def _format_filter_subtitle(self, filters: ReportFilter) -> str:
        """Formate le sous-titre basé sur les filtres"""
        parts = []
        
        if filters.start_date and filters.end_date:
            parts.append(f"Du {filters.start_date.strftime('%d/%m/%Y')} au {filters.end_date.strftime('%d/%m/%Y')}")
        elif filters.start_date:
            parts.append(f"À partir du {filters.start_date.strftime('%d/%m/%Y')}")
        elif filters.end_date:
            parts.append(f"Jusqu'au {filters.end_date.strftime('%d/%m/%Y')}")
        
        if filters.cash_register_ids:
            parts.append(f"{len(filters.cash_register_ids)} caisse(s) sélectionnée(s)")
        
        return " - ".join(parts) if parts else "Toutes les données"

    def get_daily_closures_report(self, filters: ReportFilter, page: int = 1, page_size: int = 50) -> ReportData:
        """
        Génère un rapport des clôtures journalières

        Args:
            filters: Filtres à appliquer
            page: Numéro de page
            page_size: Taille de la page

        Returns:
            Données du rapport
        """
        # Construire la requête de base
        query = self.db.query(DailyClosure)

        # Appliquer les filtres de date
        if filters.start_date:
            query = query.filter(DailyClosure.closure_date >= filters.start_date.date())

        if filters.end_date:
            query = query.filter(DailyClosure.closure_date <= filters.end_date.date())

        # Compter le total
        total_items = query.count()

        # Calculer la pagination
        pagination = self._calculate_pagination(page, page_size, total_items)

        # Appliquer la pagination et l'ordre
        closures = (query
                   .order_by(desc(DailyClosure.closure_date))
                   .offset((page - 1) * page_size)
                   .limit(page_size)
                   .all())

        # Préparer les données
        data = []
        for closure in closures:
            data.append({
                'id': closure.id,
                'date': closure.closure_date,
                'status': closure.status.value,
                'total_registers': closure.total_cash_registers,
                'total_balance': float(closure.total_balance),
                'total_transactions': closure.total_transactions,
                'total_in': float(closure.total_in),
                'total_out': float(closure.total_out),
                'started_at': closure.started_at,
                'completed_at': closure.completed_at,
                'duration_minutes': closure.duration_minutes,
                'validation_hash': closure.validation_hash[:8] + '...' if closure.validation_hash else None
            })

        # Calculer le résumé
        summary = self._calculate_closures_summary(filters)

        return ReportData(
            title="Rapport des Clôtures Journalières",
            subtitle=self._format_filter_subtitle(filters),
            generated_at=datetime.now(),
            filters=filters,
            pagination=pagination,
            summary=summary,
            data=data
        )

    def get_closure_snapshots_report(self, closure_id: int) -> ReportData:
        """
        Génère un rapport détaillé des snapshots d'une clôture

        Args:
            closure_id: ID de la clôture

        Returns:
            Données du rapport
        """
        # Récupérer la clôture
        closure = self.db.query(DailyClosure).get(closure_id)
        if not closure:
            raise ValueError(f"Clôture {closure_id} non trouvée")

        # Récupérer les snapshots avec les informations des caisses
        snapshots = (self.db.query(CashRegisterSnapshot)
                    .join(CashRegister)
                    .filter(CashRegisterSnapshot.closure_id == closure_id)
                    .all())

        # Préparer les données
        data = []
        total_variance = Decimal("0.00")

        for snapshot in snapshots:
            variance_abs = abs(snapshot.variance)
            total_variance += variance_abs

            data.append({
                'register_name': snapshot.cash_register.name,
                'register_type': snapshot.cash_register.type.value if hasattr(snapshot.cash_register.type, 'value') else str(snapshot.cash_register.type),
                'opening_balance': float(snapshot.opening_balance),
                'closing_balance': float(snapshot.closing_balance),
                'theoretical_balance': float(snapshot.theoretical_balance),
                'total_in': float(snapshot.total_in),
                'total_out': float(snapshot.total_out),
                'transaction_count': snapshot.transaction_count,
                'variance': float(snapshot.variance),
                'variance_abs': float(variance_abs),
                'cash_amount': float(snapshot.cash_amount),
                'bank_transfer_amount': float(snapshot.bank_transfer_amount),
                'check_amount': float(snapshot.check_amount),
                'credit_card_amount': float(snapshot.credit_card_amount),
                'other_amount': float(snapshot.other_amount),
                'is_reconciled': snapshot.is_reconciled,
                'adjustment_amount': float(snapshot.adjustment_amount),
                'adjustment_reason': snapshot.adjustment_reason
            })

        # Calculer le résumé
        summary = {
            'closure_date': closure.closure_date.isoformat(),
            'closure_status': closure.status.value,
            'total_registers': len(snapshots),
            'total_balance': float(closure.total_balance),
            'total_variance': float(total_variance),
            'max_variance': float(max(abs(s.variance) for s in snapshots)) if snapshots else 0,
            'reconciled_count': sum(1 for s in snapshots if s.is_reconciled),
            'unreconciled_count': sum(1 for s in snapshots if not s.is_reconciled)
        }

        return ReportData(
            title=f"Détail de la Clôture du {closure.closure_date}",
            subtitle=f"Statut: {closure.status.value} - {len(snapshots)} caisses",
            generated_at=datetime.now(),
            filters=None,
            pagination=PaginationInfo(page=1, page_size=len(data), total_items=len(data), total_pages=1),
            summary=summary,
            data=data
        )

    def get_period_locks_report(self, filters: ReportFilter = None) -> ReportData:
        """
        Génère un rapport des verrouillages de période

        Args:
            filters: Filtres à appliquer (optionnel)

        Returns:
            Données du rapport
        """
        # Construire la requête de base
        query = self.db.query(PeriodLock).filter(PeriodLock.is_active == True)

        if filters:
            if filters.start_date:
                query = query.filter(PeriodLock.end_date >= filters.start_date.date())

            if filters.end_date:
                query = query.filter(PeriodLock.start_date <= filters.end_date.date())

        # Récupérer les verrouillages
        locks = query.order_by(desc(PeriodLock.start_date)).all()

        # Préparer les données
        data = []
        for lock in locks:
            duration_days = (lock.end_date - lock.start_date).days + 1

            data.append({
                'id': lock.id,
                'start_date': lock.start_date,
                'end_date': lock.end_date,
                'duration_days': duration_days,
                'lock_type': lock.lock_type,
                'reason': lock.reason or '',
                'locked_by_user_id': lock.locked_by_user_id,
                'locked_at': lock.locked_at,
                'can_unlock': lock.can_unlock,
                'unlock_requires_approval': lock.unlock_requires_approval,
                'reference_id': lock.reference_id
            })

        # Calculer le résumé
        total_days_locked = sum(d['duration_days'] for d in data)

        summary = {
            'total_locks': len(locks),
            'total_days_locked': total_days_locked,
            'active_locks': len([l for l in locks if l.is_active]),
            'closure_locks': len([l for l in locks if l.lock_type == 'daily_closure']),
            'manual_locks': len([l for l in locks if l.lock_type != 'daily_closure'])
        }

        return ReportData(
            title="Rapport des Verrouillages de Période",
            subtitle=f"{len(locks)} verrouillages actifs",
            generated_at=datetime.now(),
            filters=filters,
            pagination=PaginationInfo(page=1, page_size=len(data), total_items=len(data), total_pages=1),
            summary=summary,
            data=data
        )

    def get_variance_analysis_report(self, filters: ReportFilter) -> ReportData:
        """
        Génère un rapport d'analyse des écarts

        Args:
            filters: Filtres à appliquer

        Returns:
            Données du rapport
        """
        # Construire la requête pour les snapshots
        query = (self.db.query(CashRegisterSnapshot)
                .join(DailyClosure)
                .join(CashRegister))

        # Appliquer les filtres
        if filters.start_date:
            query = query.filter(DailyClosure.closure_date >= filters.start_date.date())

        if filters.end_date:
            query = query.filter(DailyClosure.closure_date <= filters.end_date.date())

        if filters.cash_register_ids:
            query = query.filter(CashRegisterSnapshot.cash_register_id.in_(filters.cash_register_ids))

        # Récupérer les snapshots
        snapshots = query.order_by(desc(DailyClosure.closure_date)).all()

        # Préparer les données
        data = []
        for snapshot in snapshots:
            variance_abs = abs(snapshot.variance)

            # Catégoriser l'écart
            if variance_abs == 0:
                variance_category = "Aucun écart"
            elif variance_abs <= Decimal("1.00"):
                variance_category = "Écart mineur (≤ 1 DA)"
            elif variance_abs <= Decimal("10.00"):
                variance_category = "Écart modéré (≤ 10 DA)"
            else:
                variance_category = "Écart important (> 10 DA)"

            data.append({
                'closure_date': snapshot.closure.closure_date,
                'register_name': snapshot.cash_register.name,
                'register_type': snapshot.cash_register.type.value if hasattr(snapshot.cash_register.type, 'value') else str(snapshot.cash_register.type),
                'theoretical_balance': float(snapshot.theoretical_balance),
                'closing_balance': float(snapshot.closing_balance),
                'variance': float(snapshot.variance),
                'variance_abs': float(variance_abs),
                'variance_category': variance_category,
                'transaction_count': snapshot.transaction_count,
                'is_reconciled': snapshot.is_reconciled,
                'adjustment_amount': float(snapshot.adjustment_amount),
                'adjustment_reason': snapshot.adjustment_reason or ''
            })

        # Calculer le résumé
        total_variance = sum(d['variance_abs'] for d in data)
        avg_variance = total_variance / len(data) if data else 0

        variance_categories = {}
        for d in data:
            cat = d['variance_category']
            variance_categories[cat] = variance_categories.get(cat, 0) + 1

        summary = {
            'total_snapshots': len(data),
            'total_variance': total_variance,
            'average_variance': avg_variance,
            'max_variance': max(d['variance_abs'] for d in data) if data else 0,
            'reconciled_count': sum(1 for d in data if d['is_reconciled']),
            'variance_categories': variance_categories
        }

        return ReportData(
            title="Analyse des Écarts de Clôture",
            subtitle=self._format_filter_subtitle(filters),
            generated_at=datetime.now(),
            filters=filters,
            pagination=PaginationInfo(page=1, page_size=len(data), total_items=len(data), total_pages=1),
            summary=summary,
            data=data
        )

    def _calculate_closures_summary(self, filters: ReportFilter) -> Dict[str, Any]:
        """Calcule le résumé des clôtures"""
        query = self.db.query(DailyClosure)

        if filters.start_date:
            query = query.filter(DailyClosure.closure_date >= filters.start_date.date())

        if filters.end_date:
            query = query.filter(DailyClosure.closure_date <= filters.end_date.date())

        closures = query.all()

        # Compter par statut
        status_counts = {}
        for closure in closures:
            status = closure.status.value
            status_counts[status] = status_counts.get(status, 0) + 1

        # Calculer les totaux
        completed_closures = [c for c in closures if c.status == ClosureStatus.COMPLETED]

        total_balance = sum(c.total_balance for c in completed_closures)
        total_transactions = sum(c.total_transactions for c in completed_closures)

        return {
            'total_closures': len(closures),
            'completed_closures': len(completed_closures),
            'failed_closures': status_counts.get('failed', 0),
            'in_progress_closures': status_counts.get('in_progress', 0),
            'total_balance': float(total_balance),
            'total_transactions': total_transactions,
            'average_balance': float(total_balance / len(completed_closures)) if completed_closures else 0
        }
