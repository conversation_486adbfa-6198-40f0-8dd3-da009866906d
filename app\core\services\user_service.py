from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import secrets
import string
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from app.core.models.user import (
    User, UserStatus, UserPydantic, UserCreate, UserUpdate,
    PasswordChange, PasswordReset, PasswordResetRequest
)
from app.core.models.permission import DBRole, Permission
from app.core.models.user_role import UserRole
from app.core.services.base_service import BaseService
from app.core.services.audit_service import AuditService
from app.core.models.audit import AuditActionType
from app.utils.security import get_password_hash, verify_password

class UserService(BaseService[User, UserPydantic, UserPydantic]):
    def __init__(self, db: Session):
        super().__init__(db, User)
        self.audit_service = AuditService(db)

    async def create_user(self, user_data: UserCreate, created_by_id: Optional[int] = None) -> User:
        """
        Créer un nouvel utilisateur avec mot de passe hashé

        Args:
            user_data: Données de l'utilisateur
            created_by_id: ID de l'utilisateur qui crée le compte

        Returns:
            L'utilisateur créé
        """
        # Vérifier si l'email existe déjà
        if await self.get_user_by_email(user_data.email):
            raise ValueError("Email already registered")

        # Créer l'utilisateur
        # Note: last_login reste None jusqu'à la première connexion
        user = User(
            email=user_data.email,
            hashed_password=get_password_hash(user_data.password),
            full_name=user_data.full_name,
            status=UserStatus.ACTIVE,
            is_active=True,
            phone=user_data.phone,
            position=user_data.position,
            department=user_data.department
        )

        self.db.add(user)
        self.db.flush()  # Pour obtenir l'ID de l'utilisateur

        # Assigner les rôles
        for role_id in user_data.role_ids:
            role = self.db.query(DBRole).get(role_id)
            if role:
                user_role = UserRole(user_id=user.id, role_id=role_id)
                self.db.add(user_role)

        self.db.commit()
        self.db.refresh(user)

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.CREATE,
            user_id=created_by_id,
            entity_type="user",
            entity_id=user.id,
            details={"email": user.email}
        )

        return user

    async def update_user(
        self,
        user_id: int,
        user_data: UserUpdate,
        updated_by_id: Optional[int] = None
    ) -> User:
        """
        Mettre à jour les informations d'un utilisateur

        Args:
            user_id: ID de l'utilisateur à mettre à jour
            user_data: Données à mettre à jour
            updated_by_id: ID de l'utilisateur qui effectue la mise à jour

        Returns:
            L'utilisateur mis à jour
        """
        user = await self.get(user_id)
        if not user:
            raise ValueError("User not found")

        # Sauvegarder les anciennes valeurs pour l'audit
        old_values = {
            "email": user.email,
            "full_name": user.full_name,
            "status": user.status,
            "is_active": user.is_active,
            "phone": user.phone,
            "position": user.position,
            "department": user.department,
            "two_factor_enabled": user.two_factor_enabled
        }

        # Mettre à jour les champs
        if user_data.email is not None and user_data.email != user.email:
            # Vérifier si le nouvel email existe déjà
            existing_user = await self.get_user_by_email(user_data.email)
            if existing_user and existing_user.id != user_id:
                raise ValueError("Email already registered")
            user.email = user_data.email

        if user_data.full_name is not None:
            user.full_name = user_data.full_name

        if user_data.status is not None:
            user.status = user_data.status

        if user_data.is_active is not None:
            user.is_active = user_data.is_active

        if user_data.phone is not None:
            user.phone = user_data.phone

        if user_data.position is not None:
            user.position = user_data.position

        if user_data.department is not None:
            user.department = user_data.department

        if user_data.profile_image is not None:
            user.profile_image = user_data.profile_image

        if user_data.two_factor_enabled is not None:
            user.two_factor_enabled = user_data.two_factor_enabled

        # Mettre à jour les rôles si nécessaire
        if user_data.role_ids is not None:
            # Supprimer les rôles existants
            self.db.query(UserRole).filter(UserRole.user_id == user_id).delete()

            # Ajouter les nouveaux rôles
            for role_id in user_data.role_ids:
                role = self.db.query(DBRole).get(role_id)
                if role:
                    user_role = UserRole(user_id=user.id, role_id=role_id)
                    self.db.add(user_role)

        self.db.commit()
        self.db.refresh(user)

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.UPDATE,
            user_id=updated_by_id,
            entity_type="user",
            entity_id=user.id,
            details={
                "old_values": old_values,
                "new_values": {
                    "email": user.email,
                    "full_name": user.full_name,
                    "status": user.status,
                    "is_active": user.is_active,
                    "phone": user.phone,
                    "position": user.position,
                    "department": user.department,
                    "two_factor_enabled": user.two_factor_enabled
                }
            }
        )

        return user

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Récupérer un utilisateur par son email

        Args:
            email: Email de l'utilisateur

        Returns:
            L'utilisateur correspondant ou None
        """
        return self.db.query(User).filter(User.email == email).first()

    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """
        Récupérer un utilisateur par son ID

        Args:
            user_id: ID de l'utilisateur

        Returns:
            L'utilisateur correspondant ou None
        """
        return await self.get(user_id)

    async def change_password(
        self,
        user_id: int,
        password_data: PasswordChange,
        updated_by_id: Optional[int] = None
    ) -> bool:
        """
        Changer le mot de passe d'un utilisateur

        Args:
            user_id: ID de l'utilisateur
            password_data: Données de changement de mot de passe
            updated_by_id: ID de l'utilisateur qui effectue le changement

        Returns:
            True si le changement a réussi, False sinon
        """
        user = await self.get(user_id)
        if not user:
            raise ValueError("User not found")

        # Vérifier le mot de passe actuel
        if not verify_password(password_data.current_password, user.hashed_password):
            raise ValueError("Current password is incorrect")

        # Vérifier que les nouveaux mots de passe correspondent
        if password_data.new_password != password_data.confirm_password:
            raise ValueError("New passwords do not match")

        # Mettre à jour le mot de passe
        user.hashed_password = get_password_hash(password_data.new_password)
        self.db.commit()

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.PASSWORD_CHANGE,
            user_id=updated_by_id or user_id,
            entity_type="user",
            entity_id=user.id,
            details={"changed_by": "self" if updated_by_id is None else "admin"}
        )

        return True

    async def request_password_reset(self, email: str) -> Optional[str]:
        """
        Demander une réinitialisation de mot de passe

        Args:
            email: Email de l'utilisateur

        Returns:
            Token de réinitialisation ou None si l'utilisateur n'existe pas
        """
        user = await self.get_user_by_email(email)
        if not user or not user.is_active:
            return None

        # Générer un token aléatoire
        token = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(64))

        # Enregistrer le token et sa date d'expiration
        user.password_reset_token = token
        user.password_reset_expires = datetime.now() + timedelta(hours=24)
        self.db.commit()

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.PASSWORD_RESET,
            user_id=None,
            entity_type="user",
            entity_id=user.id,
            details={"action": "request"}
        )

        return token

    async def reset_password(self, reset_data: PasswordReset) -> bool:
        """
        Réinitialiser le mot de passe avec un token

        Args:
            reset_data: Données de réinitialisation

        Returns:
            True si la réinitialisation a réussi, False sinon
        """
        # Vérifier que les mots de passe correspondent
        if reset_data.new_password != reset_data.confirm_password:
            raise ValueError("Passwords do not match")

        # Rechercher l'utilisateur avec ce token
        user = self.db.query(User).filter(
            User.password_reset_token == reset_data.token,
            User.password_reset_expires > datetime.now(),
            User.is_active == True
        ).first()

        if not user:
            raise ValueError("Invalid or expired token")

        # Réinitialiser le mot de passe
        user.hashed_password = get_password_hash(reset_data.new_password)
        user.password_reset_token = None
        user.password_reset_expires = None
        user.failed_login_attempts = 0
        self.db.commit()

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.PASSWORD_RESET,
            user_id=user.id,
            entity_type="user",
            entity_id=user.id,
            details={"action": "reset"}
        )

        return True

    async def deactivate_user(self, user_id: int, deactivated_by_id: Optional[int] = None) -> bool:
        """
        Désactiver un utilisateur

        Args:
            user_id: ID de l'utilisateur à désactiver
            deactivated_by_id: ID de l'utilisateur qui effectue la désactivation

        Returns:
            True si la désactivation a réussi, False sinon
        """
        user = await self.get(user_id)
        if not user:
            return False

        user.is_active = False
        user.status = UserStatus.INACTIVE
        self.db.commit()

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.UPDATE,
            user_id=deactivated_by_id,
            entity_type="user",
            entity_id=user.id,
            details={"action": "deactivate"}
        )

        return True

    async def activate_user(self, user_id: int, activated_by_id: Optional[int] = None) -> bool:
        """
        Activer un utilisateur

        Args:
            user_id: ID de l'utilisateur à activer
            activated_by_id: ID de l'utilisateur qui effectue l'activation

        Returns:
            True si l'activation a réussi, False sinon
        """
        user = await self.get(user_id)
        if not user:
            return False

        user.is_active = True
        user.status = UserStatus.ACTIVE
        self.db.commit()

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.UPDATE,
            user_id=activated_by_id,
            entity_type="user",
            entity_id=user.id,
            details={"action": "activate"}
        )

        return True

    async def delete_user(self, user_id: int, deleted_by_id: Optional[int] = None) -> bool:
        """
        Supprime un utilisateur

        Args:
            user_id: ID de l'utilisateur à supprimer
            deleted_by_id: ID de l'utilisateur qui effectue la suppression

        Returns:
            True si la suppression a réussi, False sinon
        """
        user = await self.get(user_id)
        if not user:
            return False

        # Sauvegarder les informations de l'utilisateur pour l'audit
        user_info = {
            "email": user.email,
            "full_name": user.full_name,
            "status": user.status,
            "is_active": user.is_active,
            "department": user.department
        }

        # Supprimer les rôles de l'utilisateur
        self.db.query(UserRole).filter(UserRole.user_id == user_id).delete()

        # Supprimer l'utilisateur
        self.db.delete(user)
        self.db.commit()

        # Journaliser l'action
        await self.audit_service.log_action(
            action=AuditActionType.DELETE,
            user_id=deleted_by_id,
            entity_type="user",
            entity_id=user_id,
            details={"deleted_user": user_info}
        )

        return True

    async def search_users(
        self,
        search_term: Optional[str] = None,
        status: Optional[UserStatus] = None,
        role_id: Optional[int] = None,
        department: Optional[str] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """
        Rechercher des utilisateurs avec filtres

        Args:
            search_term: Terme de recherche (email, nom)
            status: Statut des utilisateurs
            role_id: ID du rôle
            department: Département
            is_active: Statut d'activité
            skip: Nombre d'entrées à sauter
            limit: Nombre maximum d'entrées à retourner

        Returns:
            Liste des utilisateurs correspondants
        """
        query = self.db.query(User)

        # Appliquer les filtres
        if search_term:
            query = query.filter(
                or_(
                    User.email.ilike(f"%{search_term}%"),
                    User.full_name.ilike(f"%{search_term}%"),
                    User.phone.ilike(f"%{search_term}%")
                )
            )

        if status:
            query = query.filter(User.status == status)

        if is_active is not None:
            query = query.filter(User.is_active == is_active)

        if department:
            query = query.filter(User.department == department)

        if role_id:
            query = query.join(UserRole).filter(UserRole.role_id == role_id)

        return query.order_by(User.full_name).offset(skip).limit(limit).all()

    async def get_user_permissions(self, user_id: int) -> List[str]:
        """
        Récupère tous les codes de permissions d'un utilisateur via ses rôles (incluant les rôles parents).
        Implémentation optimisée: utilise les relations déjà définies au lieu de jointures incorrectes.
        """
        user = await self.get(user_id)
        if not user:
            return []

        permission_codes = set()

        # Rôles directs de l'utilisateur
        user_roles = self.db.query(UserRole).filter(UserRole.user_id == user_id).all()

        for ur in user_roles:
            role = self.db.query(DBRole).get(ur.role_id)
            if not role:
                continue

            # Permissions du rôle courant
            for p in role.permissions:
                permission_codes.add(p.code)

            # Permissions héritées des rôles parents
            parent_id = role.parent_id
            while parent_id:
                parent = self.db.query(DBRole).get(parent_id)
                if not parent:
                    break
                for p in parent.permissions:
                    permission_codes.add(p.code)
                parent_id = parent.parent_id

        return list(permission_codes)

    async def check_permission(self, user_id: int, permission_code: str) -> bool:
        """
        Vérifie si un utilisateur possède une permission donnée.
        """
        permissions = await self.get_user_permissions(user_id)
        return permission_code in permissions

    async def assign_roles(self, user_id: int, role_ids: List[int], updated_by_id: Optional[int] = None) -> bool:
        """
        Assigne une liste de rôles à un utilisateur (remplace les assignations existantes).
        """
        user = await self.get(user_id)
        if not user:
            raise ValueError("User not found")

        # Supprimer les rôles existants
        self.db.query(UserRole).filter(UserRole.user_id == user_id).delete()

        # Ajouter les nouveaux rôles (ignorer les IDs inexistants)
        for role_id in role_ids or []:
            role = self.db.query(DBRole).get(role_id)
            if role:
                self.db.add(UserRole(user_id=user_id, role_id=role_id))

        self.db.commit()

        # Audit
        await self.audit_service.log_action(
            action=AuditActionType.PERMISSION_CHANGE,
            user_id=updated_by_id,
            entity_type="user",
            entity_id=user_id,
            details={"action": "assign_roles", "role_ids": role_ids}
        )
        return True

    async def get_user_roles(self, user_id: int) -> List[DBRole]:
        """
        Retourne les rôles (DBRole) assignés à l'utilisateur.
        """
        return [
            self.db.query(DBRole).get(ur.role_id)
            for ur in self.db.query(UserRole).filter(UserRole.user_id == user_id).all()
            if self.db.query(DBRole).get(ur.role_id) is not None
        ]

    async def get_users_by_role(self, role_name: Optional[str] = None, role_id: Optional[int] = None) -> List[User]:
        """
        Retourne les utilisateurs qui possèdent un rôle donné (par nom ou ID).
        """
        if not role_id and not role_name:
            return []
        if role_id is None and role_name is not None:
            role = self.db.query(DBRole).filter(DBRole.name == role_name).first()
            if not role:
                return []
            role_id = role.id

        from app.core.models.user import User  # import local pour éviter les cycles
        return (
            self.db.query(User)
            .join(UserRole, User.id == UserRole.user_id)
            .filter(UserRole.role_id == role_id)
            .all()
        )

    async def has_permission(self, user_id: int, permission_code: str) -> bool:
        """
        Alias pratique de check_permission.
        """
        return await self.check_permission(user_id, permission_code)

    def get_current_user(self) -> Optional[User]:
        """
        Récupère l'utilisateur actuellement connecté.
        Note: Cette méthode est une solution temporaire.
        Dans une implémentation complète, elle devrait récupérer l'utilisateur
        depuis le contrôleur d'authentification.

        Returns:
            L'utilisateur actuellement connecté ou None
        """
        # Pour l'instant, retourner l'utilisateur avec l'ID 1 (administrateur par défaut)
        return self.db.query(User).get(1)

    async def record_login_attempt(self, email: str, success: bool) -> None:
        """
        Enregistrer une tentative de connexion

        Args:
            email: Email de l'utilisateur
            success: True si la connexion a réussi, False sinon
        """
        user = await self.get_user_by_email(email)
        if not user:
            return

        if success:
            # Réinitialiser les tentatives échouées et mettre à jour la dernière connexion
            now = datetime.utcnow()

            # Mettre à jour via l'objet user pour assurer la cohérence
            user.failed_login_attempts = 0
            user.last_login = now
            self.db.add(user)
            print(f"Service utilisateur: Mise à jour de la dernière connexion pour {email} à {now}")
        else:
            # Incrémenter les tentatives échouées
            user.failed_login_attempts += 1
            user.last_failed_login = datetime.now()

            # Désactiver le compte après 5 tentatives échouées
            if user.failed_login_attempts >= 5:
                user.status = UserStatus.SUSPENDED

                # Journaliser l'action
                await self.audit_service.log_action(
                    action=AuditActionType.UPDATE,
                    user_id=None,
                    entity_type="user",
                    entity_id=user.id,
                    details={"action": "suspend", "reason": "too_many_failed_attempts"}
                )

        # S'assurer que les modifications sont bien enregistrées
        try:
            self.db.commit()

            # Vérifier que les modifications ont bien été enregistrées
            if success:
                # Rafraîchir l'objet user pour vérifier la mise à jour
                self.db.refresh(user)
                if user.last_login:
                    print(f"Service utilisateur: Vérification: dernière connexion pour {email} mise à jour avec succès: {user.last_login}")
                else:
                    print(f"Service utilisateur: Erreur: la dernière connexion pour {email} n'a pas été mise à jour correctement")

        except Exception as e:
            print(f"Service utilisateur: Erreur lors de l'enregistrement de la tentative de connexion: {e}")
            self.db.rollback()
            raise
