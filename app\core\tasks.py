"""
Tâches planifiées pour l'application.
"""
import asyncio
import logging
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from app.utils.database import SessionLocal
from app.core.services.notification_service import NotificationService
from app.core.services.financial_reporting_service import FinancialReportingService

logger = logging.getLogger(__name__)

async def check_alerts():
    """Vérifie les alertes et crée des notifications"""
    logger.info("Vérification des alertes...")
    try:
        db = SessionLocal()
        notification_service = NotificationService(db)
        
        # Vérifier les alertes de stock faible
        low_stock_count = await notification_service.notify_low_stock_items()
        logger.info(f"Alertes de stock faible: {low_stock_count}")
        
        # Vérifier les alertes de factures fournisseurs
        supplier_invoices_count = await notification_service.notify_supplier_invoices_due()
        logger.info(f"Alertes de factures fournisseurs: {supplier_invoices_count}")
        
        # Vérifier les alertes de factures clients
        customer_invoices_count = await notification_service.notify_customer_invoices_due()
        logger.info(f"Alertes de factures clients: {customer_invoices_count}")
        
        # Vérifier les alertes de marge faible
        low_margin_count = await notification_service.notify_low_margin_sales()
        logger.info(f"Alertes de marge faible: {low_margin_count}")
        
        # Nettoyer les notifications expirées
        expired_count = await notification_service.clean_expired_notifications()
        logger.info(f"Notifications expirées supprimées: {expired_count}")
        
    except Exception as e:
        logger.error(f"Erreur lors de la vérification des alertes: {str(e)}")
    finally:
        db.close()

async def generate_daily_reports():
    """Génère les rapports quotidiens"""
    logger.info("Génération des rapports quotidiens...")
    try:
        db = SessionLocal()
        financial_service = FinancialReportingService(db)
        
        # Générer le rapport de profits et pertes
        today = datetime.now()
        start_date = datetime(today.year, today.month, 1)
        await financial_service.generate_profit_loss_report(start_date, today)
        logger.info("Rapport de profits et pertes généré")
        
        # Générer le rapport de flux de trésorerie
        await financial_service.generate_cash_flow_report(start_date, today)
        logger.info("Rapport de flux de trésorerie généré")
        
        # Générer le rapport d'inventaire
        await financial_service.generate_inventory_financial_report()
        logger.info("Rapport d'inventaire généré")
        
    except Exception as e:
        logger.error(f"Erreur lors de la génération des rapports quotidiens: {str(e)}")
    finally:
        db.close()

def run_async_task(task_func):
    """Exécute une tâche asynchrone"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(task_func())
    finally:
        loop.close()

def schedule_tasks(scheduler: BackgroundScheduler):
    """Configure les tâches planifiées"""
    # Vérification des alertes toutes les heures
    scheduler.add_job(
        lambda: run_async_task(check_alerts),
        IntervalTrigger(hours=1),
        id='check_alerts',
        replace_existing=True
    )
    
    # Génération des rapports quotidiens à minuit
    scheduler.add_job(
        lambda: run_async_task(generate_daily_reports),
        CronTrigger(hour=0, minute=0),
        id='generate_daily_reports',
        replace_existing=True
    )
    
    logger.info("Tâches planifiées configurées")
