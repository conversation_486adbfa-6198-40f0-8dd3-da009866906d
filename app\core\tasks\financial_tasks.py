"""
Tâches planifiées pour les rapports financiers et les alertes.
"""
import asyncio
import logging
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from app.utils.database import SessionLocal
from app.core.services.notification_service import NotificationService
from app.core.services.financial_reporting_service import FinancialReportingService
from app.core.services.purchasing_service import PurchasingService
from app.core.models.notification import NotificationType, NotificationPriority, NotificationChannel

logger = logging.getLogger(__name__)

async def check_financial_alerts():
    """Vérifie les alertes financières et crée des notifications"""
    logger.info("Vérification des alertes financières...")
    db = SessionLocal()

    try:
        notification_service = NotificationService(db)

        # Vérifier les factures fournisseurs à échéance
        supplier_invoices_due = await notification_service.check_supplier_invoices_due()
        if supplier_invoices_due > 0:
            logger.info(f"{supplier_invoices_due} factures fournisseurs à échéance détectées")

        # Vérifier les factures clients à échéance
        customer_invoices_due = await notification_service.check_customer_invoices_due()
        if customer_invoices_due > 0:
            logger.info(f"{customer_invoices_due} factures clients à échéance détectées")

        # Vérifier les alertes de marge faible
        low_margin_sales = await notification_service.check_sales_margin_alerts()
        if low_margin_sales > 0:
            logger.info(f"{low_margin_sales} ventes à marge faible détectées")

    except Exception as e:
        logger.error(f"Erreur lors de la vérification des alertes financières: {str(e)}")
    finally:
        db.close()

async def update_purchase_payment_statuses():
    """Met à jour les statuts de paiement des commandes d'achat"""
    logger.info("Mise à jour des statuts de paiement des commandes d'achat...")

    db = SessionLocal()

    try:
        purchasing_service = PurchasingService(db)

        # Récupérer les commandes actives
        orders = db.query(purchasing_service.model).filter(
            purchasing_service.model.status.in_(["ordered", "partially_received", "completed"])
        ).all()

        updated_count = 0
        for order in orders:
            try:
                # Mettre à jour le statut de paiement
                await purchasing_service.update_payment_status(order.id)
                updated_count += 1
            except Exception as e:
                logger.error(f"Erreur lors de la mise à jour du statut de paiement de la commande {order.id}: {str(e)}")

        logger.info(f"Statuts de paiement mis à jour pour {updated_count} commandes")

    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des statuts de paiement: {str(e)}")
    finally:
        db.close()

async def generate_daily_reports():
    """Génère les rapports financiers quotidiens"""
    logger.info("Génération des rapports financiers quotidiens...")
    db = SessionLocal()

    try:
        financial_service = FinancialReportingService(db)

        # Générer le rapport de profits et pertes
        today = datetime.now()
        start_date = datetime(today.year, today.month, 1)
        await financial_service.generate_profit_loss_report(start_date, today)
        logger.info("Rapport de profits et pertes généré")

        # Générer le rapport de flux de trésorerie
        await financial_service.generate_cash_flow_report(start_date, today)
        logger.info("Rapport de flux de trésorerie généré")

        # Générer le rapport d'inventaire
        await financial_service.generate_inventory_financial_report()
        logger.info("Rapport d'inventaire généré")

    except Exception as e:
        logger.error(f"Erreur lors de la génération des rapports quotidiens: {str(e)}")
    finally:
        db.close()

def schedule_financial_tasks(scheduler):
    """
    Planifie les tâches financières

    Args:
        scheduler: Le planificateur de tâches
    """
    # Vérification des alertes financières toutes les heures
    scheduler.add_job(
        lambda: asyncio.run(check_financial_alerts()),
        IntervalTrigger(hours=1),
        id='check_financial_alerts',
        replace_existing=True
    )

    # Mise à jour des statuts de paiement des commandes d'achat toutes les 4 heures
    scheduler.add_job(
        lambda: asyncio.run(update_purchase_payment_statuses()),
        IntervalTrigger(hours=4),
        id='update_purchase_payment_statuses',
        replace_existing=True
    )

    # Génération des rapports quotidiens à minuit
    scheduler.add_job(
        lambda: asyncio.run(generate_daily_reports()),
        CronTrigger(hour=0, minute=0),
        id='generate_daily_reports',
        replace_existing=True
    )
