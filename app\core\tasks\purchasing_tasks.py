import asyncio
import logging
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.core.services.purchasing_service import PurchasingService
from app.core.services.notification_service import NotificationService
from app.core.models.notification import NotificationType, NotificationPriority, NotificationChannel
from app.utils.database import SessionLocal

logger = logging.getLogger(__name__)

async def check_low_stock_items():
    """
    Vérifie les articles à faible stock et envoie des notifications
    """
    logger.info("Vérification des articles à faible stock")
    
    db = SessionLocal()
    try:
        purchasing_service = PurchasingService(db)
        notification_service = NotificationService(db)
        
        # Récupérer les articles à faible stock
        low_stock_items = await purchasing_service.get_low_stock_items()
        
        if low_stock_items:
            # Créer une notification pour les administrateurs
            await notification_service.create_notification({
                "user_id": None,  # Notification système
                "type": NotificationType.INVENTORY,
                "priority": NotificationPriority.MEDIUM,
                "title": "Articles à faible stock",
                "message": f"{len(low_stock_items)} article(s) sont à faible stock et nécessitent une commande.",
                "data": {"low_stock_count": len(low_stock_items)},
                "action_url": "/purchasing/low-stock",
                "icon": "warning",
                "channels": [NotificationChannel.UI]
            })
            
            logger.info(f"{len(low_stock_items)} articles à faible stock détectés")
        else:
            logger.info("Aucun article à faible stock détecté")
    
    except Exception as e:
        logger.error(f"Erreur lors de la vérification des articles à faible stock: {str(e)}")
    
    finally:
        db.close()

async def check_overdue_orders():
    """
    Vérifie les commandes en retard et envoie des notifications
    """
    logger.info("Vérification des commandes en retard")
    
    db = SessionLocal()
    try:
        purchasing_service = PurchasingService(db)
        
        # Récupérer les commandes en retard
        overdue_orders = await purchasing_service.check_overdue_orders()
        
        if overdue_orders:
            logger.info(f"{len(overdue_orders)} commandes en retard détectées")
        else:
            logger.info("Aucune commande en retard détectée")
    
    except Exception as e:
        logger.error(f"Erreur lors de la vérification des commandes en retard: {str(e)}")
    
    finally:
        db.close()

async def run_purchasing_tasks():
    """
    Exécute toutes les tâches liées aux achats
    """
    logger.info("Exécution des tâches d'achat")
    
    try:
        # Vérifier les articles à faible stock
        await check_low_stock_items()
        
        # Vérifier les commandes en retard
        await check_overdue_orders()
        
    except Exception as e:
        logger.error(f"Erreur lors de l'exécution des tâches d'achat: {str(e)}")

def schedule_purchasing_tasks(scheduler):
    """
    Planifie les tâches liées aux achats
    
    Args:
        scheduler: Le planificateur de tâches
    """
    # Vérifier les articles à faible stock tous les jours à 8h00
    scheduler.add_job(
        lambda: asyncio.run(check_low_stock_items()),
        'cron',
        hour=8,
        minute=0,
        id='check_low_stock_items'
    )
    
    # Vérifier les commandes en retard tous les jours à 9h00
    scheduler.add_job(
        lambda: asyncio.run(check_overdue_orders()),
        'cron',
        hour=9,
        minute=0,
        id='check_overdue_orders'
    )
