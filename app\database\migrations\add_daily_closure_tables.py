"""
Migration pour ajouter les tables de clôture journalière.
"""
from sqlalchemy import text
from sqlalchemy.orm import Session
import logging

logger = logging.getLogger(__name__)


def upgrade(db: Session):
    """Applique la migration - ajoute les tables de clôture journalière"""
    
    logger.info("Début de la migration : ajout des tables de clôture journalière")
    
    try:
        # 1. Table daily_closures
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS daily_closures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                closure_date DATE NOT NULL UNIQUE,
                closure_type VARCHAR(20) DEFAULT 'daily',
                status VARCHAR(20) DEFAULT 'pending',
                started_at TIMESTAMP NULL,
                completed_at TIMESTAMP NULL,
                started_by_user_id INTEGER NULL,
                completed_by_user_id INTEGER NULL,
                total_cash_registers INTEGER DEFAULT 0,
                total_balance DECIMAL(18,2) DEFAULT 0.00,
                total_transactions INTEGER DEFAULT 0,
                total_in DECIMAL(18,2) DEFAULT 0.00,
                total_out DECIMAL(18,2) DEFAULT 0.00,
                notes TEXT NULL,
                error_message TEXT NULL,
                validation_hash VARCHAR(64) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (started_by_user_id) REFERENCES users(id),
                FOREIGN KEY (completed_by_user_id) REFERENCES users(id),
                CHECK (total_balance >= 0),
                CHECK (total_transactions >= 0)
            )
        """))
        
        # Index pour daily_closures
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_daily_closure_date ON daily_closures(closure_date)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_daily_closure_status ON daily_closures(status)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_daily_closure_date_status ON daily_closures(closure_date, status)"))
        
        # 2. Table cash_register_snapshots
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS cash_register_snapshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                closure_id INTEGER NOT NULL,
                cash_register_id INTEGER NOT NULL,
                opening_balance DECIMAL(18,2) NOT NULL,
                closing_balance DECIMAL(18,2) NOT NULL,
                theoretical_balance DECIMAL(18,2) NOT NULL,
                total_in DECIMAL(18,2) DEFAULT 0.00,
                total_out DECIMAL(18,2) DEFAULT 0.00,
                transaction_count INTEGER DEFAULT 0,
                cash_amount DECIMAL(18,2) DEFAULT 0.00,
                bank_transfer_amount DECIMAL(18,2) DEFAULT 0.00,
                check_amount DECIMAL(18,2) DEFAULT 0.00,
                credit_card_amount DECIMAL(18,2) DEFAULT 0.00,
                other_amount DECIMAL(18,2) DEFAULT 0.00,
                variance DECIMAL(18,2) DEFAULT 0.00,
                adjustment_amount DECIMAL(18,2) DEFAULT 0.00,
                adjustment_reason TEXT NULL,
                notes TEXT NULL,
                is_reconciled BOOLEAN DEFAULT FALSE,
                reconciled_at TIMESTAMP NULL,
                reconciled_by_user_id INTEGER NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (closure_id) REFERENCES daily_closures(id) ON DELETE CASCADE,
                FOREIGN KEY (cash_register_id) REFERENCES cash_registers(id),
                FOREIGN KEY (reconciled_by_user_id) REFERENCES users(id),
                UNIQUE(closure_id, cash_register_id),
                CHECK (opening_balance >= 0),
                CHECK (closing_balance >= 0),
                CHECK (transaction_count >= 0)
            )
        """))
        
        # Index pour cash_register_snapshots
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_snapshot_closure_id ON cash_register_snapshots(closure_id)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_snapshot_register_id ON cash_register_snapshots(cash_register_id)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_snapshot_closure_register ON cash_register_snapshots(closure_id, cash_register_id)"))
        
        # 3. Table period_locks
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS period_locks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                lock_type VARCHAR(50) NOT NULL,
                locked_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                locked_by_user_id INTEGER NOT NULL,
                reason TEXT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                can_unlock BOOLEAN DEFAULT FALSE,
                unlock_requires_approval BOOLEAN DEFAULT TRUE,
                reference_id INTEGER NULL,
                notes TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (locked_by_user_id) REFERENCES users(id),
                CHECK (start_date <= end_date)
            )
        """))
        
        # Index pour period_locks
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_period_lock_dates ON period_locks(start_date, end_date)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_period_lock_active ON period_locks(is_active, start_date, end_date)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_period_lock_type ON period_locks(lock_type)"))
        
        # 4. Table closure_validations
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS closure_validations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                closure_id INTEGER NOT NULL,
                validation_type VARCHAR(100) NOT NULL,
                is_valid BOOLEAN NOT NULL,
                error_message TEXT NULL,
                warning_message TEXT NULL,
                expected_value VARCHAR(255) NULL,
                actual_value VARCHAR(255) NULL,
                tolerance DECIMAL(18,2) NULL,
                validated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                validated_by_user_id INTEGER NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (closure_id) REFERENCES daily_closures(id) ON DELETE CASCADE,
                FOREIGN KEY (validated_by_user_id) REFERENCES users(id)
            )
        """))
        
        # Index pour closure_validations
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_validation_closure_id ON closure_validations(closure_id)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_validation_type ON closure_validations(validation_type)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_validation_closure_type ON closure_validations(closure_id, validation_type)"))
        
        # 5. Table closure_audit_logs
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS closure_audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                closure_id INTEGER NULL,
                action VARCHAR(100) NOT NULL,
                entity_type VARCHAR(100) NULL,
                entity_id INTEGER NULL,
                old_values TEXT NULL,
                new_values TEXT NULL,
                user_id INTEGER NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                success BOOLEAN DEFAULT TRUE,
                error_message TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (closure_id) REFERENCES daily_closures(id) ON DELETE SET NULL,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """))
        
        # Index pour closure_audit_logs
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_audit_closure_id ON closure_audit_logs(closure_id)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_audit_action ON closure_audit_logs(action)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON closure_audit_logs(timestamp)"))
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_audit_user_id ON closure_audit_logs(user_id)"))
        
        # Commit des changements
        db.commit()
        
        logger.info("Migration terminée avec succès : tables de clôture journalière créées")
        return True
        
    except Exception as e:
        logger.error(f"Erreur lors de la migration : {e}")
        db.rollback()
        raise


def downgrade(db: Session):
    """Annule la migration - supprime les tables de clôture journalière"""
    
    logger.info("Début du rollback : suppression des tables de clôture journalière")
    
    try:
        # Supprimer les tables dans l'ordre inverse (contraintes de clés étrangères)
        tables_to_drop = [
            'closure_audit_logs',
            'closure_validations', 
            'cash_register_snapshots',
            'period_locks',
            'daily_closures'
        ]
        
        for table in tables_to_drop:
            db.execute(text(f"DROP TABLE IF EXISTS {table}"))
            logger.info(f"Table {table} supprimée")
        
        db.commit()
        
        logger.info("Rollback terminé avec succès : tables de clôture journalière supprimées")
        return True
        
    except Exception as e:
        logger.error(f"Erreur lors du rollback : {e}")
        db.rollback()
        raise


def run_migration(db_session: Session, direction: str = "upgrade"):
    """
    Lance la migration dans la direction spécifiée
    
    Args:
        db_session: Session de base de données
        direction: 'upgrade' ou 'downgrade'
    """
    if direction == "upgrade":
        return upgrade(db_session)
    elif direction == "downgrade":
        return downgrade(db_session)
    else:
        raise ValueError(f"Direction invalide: {direction}. Utilisez 'upgrade' ou 'downgrade'.")


if __name__ == "__main__":
    # Test de la migration (nécessite une session de base de données)
    print("Script de migration des tables de clôture journalière")
    print("Ce script doit être exécuté avec une session de base de données valide")
    print("Utilisez run_migration(db_session, 'upgrade') pour appliquer la migration")
    print("Utilisez run_migration(db_session, 'downgrade') pour annuler la migration")
