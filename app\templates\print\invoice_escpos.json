{"name": "invoice_escpos", "template_type": "escpos", "content": "\n# Commandes ESC/POS pour la facture\n# Utiliser {{ variable }} pour les variables\n\n# Initialiser l'imprimante\nESC @\n\n# Centrer le texte\nESC a 1\n\n# Double largeur et hauteur\nESC ! 0x30\nFACTURE\nESC ! 0x00\n#{{ invoice.number }}\n\n# Taille normale\nESC ! 0x00\nDate: {{ invoice.date.strftime('%d/%m/%Y') }}\n\n# Aligner à gauche\nESC a 0\nClient: {{ customer.name }}\nAdresse: {{ customer.address }}\nTelephone: {{ customer.phone }}\n\n--------------------------------\nDETAILS\n--------------------------------\n{% for item in items %}\n{{ item.description }}\n{{ item.quantity }} x {{ item.unit_price }} = {{ item.total }}\n{% endfor %}\n--------------------------------\n\nSous-total: {{ invoice.subtotal }}\n{% if invoice.discount > 0 %}\nRemise: {{ invoice.discount }}\n{% endif %}\nTVA: {{ invoice.tax }}\nTOTAL: {{ invoice.total }}\n\nMode de paiement: {{ invoice.payment_method }}\n\n# Centrer le texte\nESC a 1\nMerci pour votre achat!\n\n# Couper le papier\nGS V A\n", "metadata": {"description": "Modèle de facture pour imprimante ESC/POS", "paper_width": 80, "paper_height": 0}}