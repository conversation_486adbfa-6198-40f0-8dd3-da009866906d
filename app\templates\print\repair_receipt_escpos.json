{"name": "repair_receipt_escpos", "template_type": "escpos", "content": "\n# Commandes ESC/POS pour le reçu de réparation\n# Utiliser {{ variable }} pour les variables\n\n# Initialiser l'imprimante\nESC @\n\n# Centrer le texte\nESC a 1\n\n# Double largeur et hauteur\nESC ! 0x30\nRECU DE REPARATION\nESC ! 0x00\n#{{ repair.number }}\n\n# Taille normale\nESC ! 0x00\nDate: {{ repair.created_at.strftime('%d/%m/%Y') }}\n\n# Aligner à gauche\nESC a 0\nClient: {{ customer.name }}\nTelephone: {{ customer.phone }}\n\nAppareil: {{ repair.brand }} {{ repair.model }}\nN° de serie: {{ repair.serial_number }}\n\n--------------------------------\nDETAILS DE LA REPARATION\n--------------------------------\nProbleme: {{ repair.description }}\n\n{% if repair.diagnosis %}\nDiagnostic: {{ repair.diagnosis }}\n{% endif %}\n\n{% if repair.work_performed %}\nTravaux effectues: {{ repair.work_performed }}\n{% endif %}\n\n--------------------------------\nPIECES UTILISEES\n--------------------------------\n{% for part in used_parts %}\n{{ part.name }} x{{ part.quantity }} = {{ part.total_price }} €\n{% endfor %}\n\n--------------------------------\nCOUT\n--------------------------------\nPieces: {{ repair.parts_cost }} €\nMain d'oeuvre: {{ repair.labor_cost }} €\n{% if repair.tax_amount > 0 %}\nTVA: {{ repair.tax_amount }} €\n{% endif %}\n{% if repair.discount_amount > 0 %}\nRemise: -{{ repair.discount_amount }} €\n{% endif %}\nTOTAL: {{ repair.final_amount }} €\n\nStatut: {{ repair.status }}\nDate de fin: {{ repair.completion_date.strftime('%d/%m/%Y') if repair.completion_date else 'N/A' }}\n\n# Centrer le texte\nESC a 1\nMerci de votre confiance!\n\n# Couper le papier\nGS V A\n", "metadata": {"description": "Modèle de reçu de réparation pour imprimante ESC/POS", "paper_width": 80, "paper_height": 0}}