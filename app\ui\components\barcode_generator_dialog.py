"""
Boîte de dialogue pour générer et imprimer des codes-barres
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QLineEdit, QFormLayout, QGroupBox,
    QRadioButton, QButtonGroup, QFileDialog, QMessageBox,
    QScrollArea, QWidget, QGridLayout, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize, QBuffer, QByteArray, QIODevice
from PyQt6.QtGui import QImage, QPixmap, QIcon
from PyQt6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

import os
import tempfile
from PIL import Image
from typing import List, Dict, Any, Optional, Tuple

from app.utils.barcode_utils import BarcodeGenerator
from app.core.services.inventory_service import InventoryService
from app.utils.database import SessionLocal

class BarcodeGeneratorDialog(QDialog):
    """Boîte de dialogue pour générer et imprimer des codes-barres"""
    
    def __init__(self, parent=None, product_id=None):
        super().__init__(parent)
        
        # Configuration de la boîte de dialogue
        self.setWindowTitle("Générateur de codes-barres")
        self.setMinimumSize(800, 600)
        
        # Services
        self.db = SessionLocal()
        self.inventory_service = InventoryService(self.db)
        
        # Variables
        self.product_id = product_id
        self.products = []
        self.selected_products = []
        self.generated_barcodes = []
        
        # Configuration de l'interface
        self.setup_ui()
        
        # Connexions
        self.setup_connections()
        
        # Charger les données
        self.load_data()
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Options de génération
        options_group = QGroupBox("Options de génération")
        options_layout = QFormLayout(options_group)
        
        # Type de code-barres
        self.barcode_type_combo = QComboBox()
        self.barcode_type_combo.addItem("Code EAN-13", "ean13")
        self.barcode_type_combo.addItem("Code EAN-8", "ean8")
        self.barcode_type_combo.addItem("Code 128", "code128")
        self.barcode_type_combo.addItem("Code 39", "code39")
        self.barcode_type_combo.addItem("QR Code", "qrcode")
        options_layout.addRow("Type de code:", self.barcode_type_combo)
        
        # Taille
        size_layout = QHBoxLayout()
        
        self.width_spin = QSpinBox()
        self.width_spin.setRange(100, 1000)
        self.width_spin.setValue(300)
        self.width_spin.setSuffix(" px")
        size_layout.addWidget(QLabel("Largeur:"))
        size_layout.addWidget(self.width_spin)
        
        self.height_spin = QSpinBox()
        self.height_spin.setRange(50, 500)
        self.height_spin.setValue(100)
        self.height_spin.setSuffix(" px")
        size_layout.addWidget(QLabel("Hauteur:"))
        size_layout.addWidget(self.height_spin)
        
        options_layout.addRow("Dimensions:", size_layout)
        
        # Contenu du code-barres
        content_group = QGroupBox("Contenu du code-barres")
        content_layout = QVBoxLayout(content_group)
        
        self.content_group = QButtonGroup(self)
        
        self.sku_radio = QRadioButton("Référence (SKU)")
        self.sku_radio.setChecked(True)
        self.content_group.addButton(self.sku_radio)
        content_layout.addWidget(self.sku_radio)
        
        self.barcode_radio = QRadioButton("Code-barres existant")
        self.content_group.addButton(self.barcode_radio)
        content_layout.addWidget(self.barcode_radio)
        
        self.id_radio = QRadioButton("ID du produit")
        self.content_group.addButton(self.id_radio)
        content_layout.addWidget(self.id_radio)
        
        self.custom_radio = QRadioButton("Personnalisé")
        self.content_group.addButton(self.custom_radio)
        content_layout.addWidget(self.custom_radio)
        
        self.custom_input = QLineEdit()
        self.custom_input.setPlaceholderText("Saisir un code personnalisé")
        self.custom_input.setEnabled(False)
        content_layout.addWidget(self.custom_input)
        
        options_layout.addRow(content_group)
        
        # Options d'étiquette
        label_group = QGroupBox("Options d'étiquette")
        label_layout = QVBoxLayout(label_group)
        
        self.include_name_check = QCheckBox("Inclure le nom du produit")
        self.include_name_check.setChecked(True)
        label_layout.addWidget(self.include_name_check)
        
        self.include_price_check = QCheckBox("Inclure le prix")
        self.include_price_check.setChecked(True)
        label_layout.addWidget(self.include_price_check)
        
        self.include_sku_check = QCheckBox("Inclure la référence")
        self.include_sku_check.setChecked(True)
        label_layout.addWidget(self.include_sku_check)
        
        options_layout.addRow(label_group)
        
        main_layout.addWidget(options_group)
        
        # Sélection des produits
        products_group = QGroupBox("Sélection des produits")
        products_layout = QVBoxLayout(products_group)
        
        # Recherche
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher un produit...")
        search_layout.addWidget(self.search_input)
        
        self.search_button = QPushButton("Rechercher")
        self.search_button.setIcon(QIcon("app/ui/resources/icons/search.svg"))
        search_layout.addWidget(self.search_button)
        
        products_layout.addLayout(search_layout)
        
        # Liste des produits
        self.products_combo = QComboBox()
        self.products_combo.setMinimumWidth(400)
        products_layout.addWidget(self.products_combo)
        
        # Boutons d'action
        actions_layout = QHBoxLayout()
        
        self.add_button = QPushButton("Ajouter")
        self.add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        actions_layout.addWidget(self.add_button)
        
        self.remove_button = QPushButton("Supprimer")
        self.remove_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
        self.remove_button.setEnabled(False)
        actions_layout.addWidget(self.remove_button)
        
        self.clear_button = QPushButton("Tout effacer")
        self.clear_button.setIcon(QIcon("app/ui/resources/icons/clear.svg"))
        self.clear_button.setEnabled(False)
        actions_layout.addWidget(self.clear_button)
        
        products_layout.addLayout(actions_layout)
        
        # Liste des produits sélectionnés
        self.selected_list = QComboBox()
        self.selected_list.setMinimumWidth(400)
        products_layout.addWidget(self.selected_list)
        
        main_layout.addWidget(products_group)
        
        # Aperçu
        preview_group = QGroupBox("Aperçu")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_scroll = QScrollArea()
        self.preview_scroll.setWidgetResizable(True)
        self.preview_scroll.setMinimumHeight(200)
        
        self.preview_widget = QWidget()
        self.preview_layout = QGridLayout(self.preview_widget)
        
        self.preview_scroll.setWidget(self.preview_widget)
        
        preview_layout.addWidget(self.preview_scroll)
        
        main_layout.addWidget(preview_group)
        
        # Boutons de dialogue
        buttons_layout = QHBoxLayout()
        
        self.generate_button = QPushButton("Générer")
        self.generate_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        buttons_layout.addWidget(self.generate_button)
        
        self.save_button = QPushButton("Enregistrer")
        self.save_button.setIcon(QIcon("app/ui/resources/icons/save.svg"))
        self.save_button.setEnabled(False)
        buttons_layout.addWidget(self.save_button)
        
        self.print_button = QPushButton("Imprimer")
        self.print_button.setIcon(QIcon("app/ui/resources/icons/print.svg"))
        self.print_button.setEnabled(False)
        buttons_layout.addWidget(self.print_button)
        
        buttons_layout.addStretch()
        
        self.close_button = QPushButton("Fermer")
        self.close_button.setIcon(QIcon("app/ui/resources/icons/close.svg"))
        buttons_layout.addWidget(self.close_button)
        
        main_layout.addLayout(buttons_layout)
        
    def setup_connections(self):
        """Configure les connexions des signaux"""
        # Boutons
        self.search_button.clicked.connect(self.search_products)
        self.add_button.clicked.connect(self.add_product)
        self.remove_button.clicked.connect(self.remove_product)
        self.clear_button.clicked.connect(self.clear_products)
        self.generate_button.clicked.connect(self.generate_barcodes)
        self.save_button.clicked.connect(self.save_barcodes)
        self.print_button.clicked.connect(self.print_barcodes)
        self.close_button.clicked.connect(self.reject)
        
        # Champs
        self.search_input.returnPressed.connect(self.search_products)
        self.custom_radio.toggled.connect(self.custom_input.setEnabled)
        self.selected_list.currentIndexChanged.connect(self.update_remove_button)
        
        # Mise à jour de l'aperçu
        self.barcode_type_combo.currentIndexChanged.connect(self.update_size_controls)
        
    def update_size_controls(self):
        """Met à jour les contrôles de taille en fonction du type de code-barres"""
        barcode_type = self.barcode_type_combo.currentData()
        
        if barcode_type == "qrcode":
            # Pour les QR codes, la largeur et la hauteur sont identiques
            self.height_spin.setValue(self.width_spin.value())
            self.height_spin.setEnabled(False)
        else:
            self.height_spin.setEnabled(True)
            
    def load_data(self):
        """Charge les données"""
        try:
            # Charger tous les produits
            import asyncio
            self.products = asyncio.run(self.inventory_service.get_all())
            
            # Si un produit spécifique est demandé, le sélectionner
            if self.product_id:
                for product in self.products:
                    if product.id == self.product_id:
                        self.selected_products.append(product)
                        self.update_selected_list()
                        break
                        
            # Mettre à jour la liste des produits
            self.update_products_combo()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des produits: {str(e)}"
            )
            
    def search_products(self):
        """Recherche des produits"""
        search_text = self.search_input.text().lower()
        
        if not search_text:
            self.update_products_combo()
            return
            
        # Filtrer les produits
        filtered_products = []
        for product in self.products:
            if (search_text in product.name.lower() or 
                (hasattr(product, 'sku') and product.sku and search_text in product.sku.lower()) or
                (hasattr(product, 'barcode') and product.barcode and search_text in product.barcode.lower())):
                filtered_products.append(product)
                
        # Mettre à jour la liste des produits
        self.update_products_combo(filtered_products)
        
    def update_products_combo(self, products=None):
        """Met à jour la liste déroulante des produits"""
        self.products_combo.clear()
        
        products_to_show = products if products is not None else self.products
        
        for product in products_to_show:
            display_text = f"{product.name}"
            if hasattr(product, 'sku') and product.sku:
                display_text += f" ({product.sku})"
                
            self.products_combo.addItem(display_text, product.id)
            
    def add_product(self):
        """Ajoute un produit à la liste des produits sélectionnés"""
        if self.products_combo.count() == 0:
            return
            
        product_id = self.products_combo.currentData()
        
        # Vérifier si le produit est déjà dans la liste
        for product in self.selected_products:
            if product.id == product_id:
                return
                
        # Trouver le produit
        for product in self.products:
            if product.id == product_id:
                self.selected_products.append(product)
                self.update_selected_list()
                break
                
    def remove_product(self):
        """Supprime un produit de la liste des produits sélectionnés"""
        if self.selected_list.count() == 0:
            return
            
        index = self.selected_list.currentIndex()
        if index >= 0:
            self.selected_products.pop(index)
            self.update_selected_list()
            
    def clear_products(self):
        """Efface la liste des produits sélectionnés"""
        self.selected_products.clear()
        self.update_selected_list()
        
    def update_selected_list(self):
        """Met à jour la liste des produits sélectionnés"""
        self.selected_list.clear()
        
        for product in self.selected_products:
            display_text = f"{product.name}"
            if hasattr(product, 'sku') and product.sku:
                display_text += f" ({product.sku})"
                
            self.selected_list.addItem(display_text, product.id)
            
        # Mettre à jour les boutons
        self.clear_button.setEnabled(len(self.selected_products) > 0)
        self.update_remove_button()
        
    def update_remove_button(self):
        """Met à jour le bouton de suppression"""
        self.remove_button.setEnabled(self.selected_list.count() > 0 and self.selected_list.currentIndex() >= 0)
        
    def generate_barcodes(self):
        """Génère les codes-barres"""
        if not self.selected_products:
            QMessageBox.warning(
                self,
                "Attention",
                "Veuillez sélectionner au moins un produit."
            )
            return
            
        # Récupérer les options
        barcode_type = self.barcode_type_combo.currentData()
        width = self.width_spin.value()
        height = self.height_spin.value()
        
        # Vider l'aperçu
        self.clear_preview()
        
        # Générer les codes-barres
        self.generated_barcodes = []
        
        for i, product in enumerate(self.selected_products):
            # Déterminer le contenu du code-barres
            if self.sku_radio.isChecked():
                if not hasattr(product, 'sku') or not product.sku:
                    QMessageBox.warning(
                        self,
                        "Attention",
                        f"Le produit '{product.name}' n'a pas de référence (SKU)."
                    )
                    continue
                    
                barcode_data = product.sku
                
            elif self.barcode_radio.isChecked():
                if not hasattr(product, 'barcode') or not product.barcode:
                    QMessageBox.warning(
                        self,
                        "Attention",
                        f"Le produit '{product.name}' n'a pas de code-barres."
                    )
                    continue
                    
                barcode_data = product.barcode
                
            elif self.id_radio.isChecked():
                barcode_data = str(product.id)
                
            elif self.custom_radio.isChecked():
                barcode_data = self.custom_input.text().strip()
                if not barcode_data:
                    QMessageBox.warning(
                        self,
                        "Attention",
                        "Veuillez saisir un code personnalisé."
                    )
                    return
            
            # Générer le code-barres ou l'étiquette
            try:
                if barcode_type == "qrcode":
                    # Générer un QR code
                    img = BarcodeGenerator.generate_qrcode(
                        barcode_data,
                        size=width,
                        text=product.name if self.include_name_check.isChecked() else None
                    )
                elif self.include_name_check.isChecked() or self.include_price_check.isChecked() or self.include_sku_check.isChecked():
                    # Générer une étiquette complète
                    product_dict = {
                        'id': product.id,
                        'name': product.name,
                        'sku': getattr(product, 'sku', ''),
                        'barcode': getattr(product, 'barcode', ''),
                        'unit_price': getattr(product, 'unit_price', 0)
                    }
                    
                    img = BarcodeGenerator.generate_product_label(
                        product_dict,
                        width=width,
                        height=height * 2  # Plus d'espace pour l'étiquette
                    )
                else:
                    # Générer un simple code-barres
                    img = BarcodeGenerator.generate_barcode(
                        barcode_data,
                        barcode_type=barcode_type,
                        width=width,
                        height=height,
                        text=None
                    )
                    
                # Ajouter à la liste des codes-barres générés
                self.generated_barcodes.append({
                    'product': product,
                    'image': img,
                    'data': barcode_data,
                    'type': barcode_type
                })
                
                # Ajouter à l'aperçu
                self.add_to_preview(img, i)
                
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Erreur",
                    f"Erreur lors de la génération du code-barres pour '{product.name}': {str(e)}"
                )
                
        # Mettre à jour les boutons
        self.save_button.setEnabled(len(self.generated_barcodes) > 0)
        self.print_button.setEnabled(len(self.generated_barcodes) > 0)
        
    def clear_preview(self):
        """Efface l'aperçu"""
        # Supprimer tous les widgets de l'aperçu
        while self.preview_layout.count():
            item = self.preview_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
                
    def add_to_preview(self, img: Image.Image, index: int):
        """Ajoute une image à l'aperçu"""
        # Convertir l'image PIL en QPixmap
        img_array = QByteArray()
        buffer = QBuffer(img_array)
        buffer.open(QIODevice.OpenModeFlag.WriteOnly)
        img.save(buffer, "PNG")
        buffer.close()
        
        pixmap = QPixmap()
        pixmap.loadFromData(img_array)
        
        # Créer un label pour afficher l'image
        label = QLabel()
        label.setPixmap(pixmap)
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("background-color: white; border: 1px solid #ccc; padding: 5px;")
        
        # Ajouter le label à l'aperçu
        row = index // 3
        col = index % 3
        self.preview_layout.addWidget(label, row, col)
        
    def save_barcodes(self):
        """Enregistre les codes-barres générés"""
        if not self.generated_barcodes:
            return
            
        # Demander le dossier de destination
        directory = QFileDialog.getExistingDirectory(
            self,
            "Sélectionner un dossier",
            os.path.expanduser("~"),
            QFileDialog.Option.ShowDirsOnly
        )
        
        if not directory:
            return
            
        # Enregistrer les images
        for i, barcode in enumerate(self.generated_barcodes):
            product = barcode['product']
            img = barcode['image']
            
            # Créer un nom de fichier
            filename = f"{product.name.replace(' ', '_')}_{i+1}.png"
            filepath = os.path.join(directory, filename)
            
            try:
                img.save(filepath)
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Erreur",
                    f"Erreur lors de l'enregistrement de l'image '{filename}': {str(e)}"
                )
                
        QMessageBox.information(
            self,
            "Succès",
            f"{len(self.generated_barcodes)} image(s) enregistrée(s) dans {directory}"
        )
        
    def print_barcodes(self):
        """Imprime les codes-barres générés"""
        if not self.generated_barcodes:
            return
            
        # Créer un dossier temporaire pour les images
        temp_dir = tempfile.mkdtemp()
        temp_files = []
        
        try:
            # Enregistrer les images dans le dossier temporaire
            for i, barcode in enumerate(self.generated_barcodes):
                img = barcode['image']
                
                # Créer un nom de fichier
                filename = os.path.join(temp_dir, f"barcode_{i+1}.png")
                img.save(filename)
                temp_files.append(filename)
                
            # Créer un document HTML pour l'impression
            html = "<html><body>"
            html += "<style>body { font-family: Arial; } .barcode { text-align: center; margin: 10px; }</style>"
            html += "<h1>Codes-barres</h1>"
            
            for filename in temp_files:
                html += f'<div class="barcode"><img src="{filename}" /></div>'
                
            html += "</body></html>"
            
            # Créer un fichier HTML temporaire
            html_file = os.path.join(temp_dir, "barcodes.html")
            with open(html_file, "w") as f:
                f.write(html)
                
            # Ouvrir la boîte de dialogue d'impression
            printer = QPrinter()
            dialog = QPrintPreviewDialog(printer, self)
            dialog.paintRequested.connect(lambda p: self.print_preview(p, html_file))
            dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors de l'impression: {str(e)}"
            )
            
        finally:
            # Supprimer les fichiers temporaires
            for filename in temp_files:
                try:
                    os.remove(filename)
                except:
                    pass
                    
            try:
                os.remove(html_file)
                os.rmdir(temp_dir)
            except:
                pass
                
    def print_preview(self, printer, html_file):
        """Affiche l'aperçu d'impression"""
        from PyQt6.QtWebEngineWidgets import QWebEngineView
        from PyQt6.QtCore import QUrl
        
        view = QWebEngineView()
        view.load(QUrl.fromLocalFile(html_file))
        
        def print_document():
            view.page().print(printer, lambda success: None)
            
        view.loadFinished.connect(print_document)
        
    def closeEvent(self, event):
        """Gère l'événement de fermeture de la boîte de dialogue"""
        # Fermer la session de base de données
        if hasattr(self, 'db') and self.db:
            self.db.close()
            
        super().closeEvent(event)
