"""
Boîte de dialogue pour scanner des codes-barres avec la caméra
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QMessageBox, QApplication, QLineEdit
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QSize
from PyQt6.QtGui import QImage, QPixmap, QIcon

import cv2
import numpy as np
from typing import List, Dict, Any, Optional

from app.utils.barcode_utils import BarcodeScanner

class BarcodeScannerDialog(QDialog):
    """Boîte de dialogue pour scanner des codes-barres avec la caméra"""
    
    # Signal émis lorsqu'un code-barres est détecté
    barcode_detected = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Configuration de la boîte de dialogue
        self.setWindowTitle("Scanner de codes-barres")
        self.setMinimumSize(640, 480)
        
        # Scanner de codes-barres
        self.scanner = BarcodeScanner()
        
        # Timer pour la mise à jour de l'image
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        
        # Variables
        self.last_detected_barcode = None
        self.detection_count = 0  # Pour éviter les faux positifs
        
        # Configuration de l'interface
        self.setup_ui()
        
        # Connexions
        self.setup_connections()
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Sélection de la caméra
        camera_layout = QHBoxLayout()
        
        camera_label = QLabel("Caméra:")
        camera_layout.addWidget(camera_label)
        
        self.camera_combo = QComboBox()
        self.camera_combo.addItem("Caméra par défaut", 0)
        
        # Ajouter les caméras disponibles
        for i in range(1, 5):  # Essayer jusqu'à 5 caméras
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                self.camera_combo.addItem(f"Caméra {i}", i)
                cap.release()
                
        camera_layout.addWidget(self.camera_combo)
        
        self.start_button = QPushButton("Démarrer")
        self.start_button.setIcon(QIcon("app/ui/resources/icons/play.svg"))
        camera_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("Arrêter")
        self.stop_button.setIcon(QIcon("app/ui/resources/icons/stop.svg"))
        self.stop_button.setEnabled(False)
        camera_layout.addWidget(self.stop_button)
        
        main_layout.addLayout(camera_layout)
        
        # Affichage de l'image
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setMinimumSize(640, 360)
        self.image_label.setStyleSheet("background-color: black;")
        main_layout.addWidget(self.image_label)
        
        # Saisie manuelle
        manual_layout = QHBoxLayout()
        
        manual_label = QLabel("Code-barres:")
        manual_layout.addWidget(manual_label)
        
        self.barcode_input = QLineEdit()
        self.barcode_input.setPlaceholderText("Saisir un code-barres manuellement")
        manual_layout.addWidget(self.barcode_input)
        
        self.validate_button = QPushButton("Valider")
        self.validate_button.setIcon(QIcon("app/ui/resources/icons/check.svg"))
        manual_layout.addWidget(self.validate_button)
        
        main_layout.addLayout(manual_layout)
        
        # Boutons de dialogue
        buttons_layout = QHBoxLayout()
        
        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.setIcon(QIcon("app/ui/resources/icons/cancel.svg"))
        buttons_layout.addWidget(self.cancel_button)
        
        buttons_layout.addStretch()
        
        main_layout.addLayout(buttons_layout)
        
    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.start_button.clicked.connect(self.start_camera)
        self.stop_button.clicked.connect(self.stop_camera)
        self.cancel_button.clicked.connect(self.reject)
        self.validate_button.clicked.connect(self.validate_manual_input)
        self.barcode_input.returnPressed.connect(self.validate_manual_input)
        
    def start_camera(self):
        """Démarre la caméra"""
        camera_id = self.camera_combo.currentData()
        
        if self.scanner.start(camera_id):
            self.timer.start(30)  # 30ms = ~33 FPS
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.camera_combo.setEnabled(False)
        else:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Impossible d'ouvrir la caméra {camera_id}."
            )
            
    def stop_camera(self):
        """Arrête la caméra"""
        self.timer.stop()
        self.scanner.stop()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.camera_combo.setEnabled(True)
        
        # Afficher une image noire
        black_image = np.zeros((360, 640, 3), dtype=np.uint8)
        self.display_image(black_image)
        
    def update_frame(self):
        """Met à jour l'image de la caméra"""
        ret, frame = self.scanner.read_frame()
        if not ret:
            return
            
        # Scanner l'image pour détecter des codes-barres
        barcodes = self.scanner.scan_frame(frame)
        
        # Dessiner les codes-barres détectés
        frame = self.scanner.draw_barcodes(frame, barcodes)
        
        # Afficher l'image
        self.display_image(frame)
        
        # Vérifier si un code-barres a été détecté
        if barcodes:
            barcode_data = barcodes[0]['data']
            
            # Vérifier si c'est le même code-barres que précédemment
            if barcode_data == self.last_detected_barcode:
                self.detection_count += 1
                
                # Si le code-barres a été détecté plusieurs fois de suite, l'émettre
                if self.detection_count >= 5:  # 5 détections consécutives pour éviter les faux positifs
                    self.barcode_detected.emit(barcode_data)
                    self.accept()
            else:
                self.last_detected_barcode = barcode_data
                self.detection_count = 1
                
    def display_image(self, frame: np.ndarray):
        """Affiche une image dans le label"""
        # Convertir l'image OpenCV en QImage
        height, width, channel = frame.shape
        bytes_per_line = 3 * width
        q_img = QImage(frame.data, width, height, bytes_per_line, QImage.Format.Format_RGB888).rgbSwapped()
        
        # Redimensionner l'image pour qu'elle tienne dans le label
        pixmap = QPixmap.fromImage(q_img)
        pixmap = pixmap.scaled(self.image_label.width(), self.image_label.height(), 
                              Qt.AspectRatioMode.KeepAspectRatio)
        
        # Afficher l'image
        self.image_label.setPixmap(pixmap)
        
    def validate_manual_input(self):
        """Valide la saisie manuelle d'un code-barres"""
        barcode = self.barcode_input.text().strip()
        if barcode:
            self.barcode_detected.emit(barcode)
            self.accept()
            
    def closeEvent(self, event):
        """Gère l'événement de fermeture de la boîte de dialogue"""
        self.stop_camera()
        super().closeEvent(event)
        
    def get_barcode(self) -> Optional[str]:
        """Retourne le code-barres détecté"""
        return self.last_detected_barcode
