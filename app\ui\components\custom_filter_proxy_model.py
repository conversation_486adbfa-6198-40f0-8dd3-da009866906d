from PyQt6.QtCore import QSortFilterProxyModel, Qt, QModelIndex

class CustomFilterProxyModel(QSortFilterProxyModel):
    """Modèle proxy personnalisé pour le filtrage avancé"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.filters = {}
        self.filter_text = ""
        self.filter_category = None
        self.filter_status = None
        self.filter_location = None
        self.filter_condition = None

    def set_filters(self, filters):
        """Définit les filtres à appliquer"""
        self.filters = filters

        # Extraire le texte de recherche s'il est présent
        if 'search' in filters:
            self.filter_text = filters.pop('search', "")

        self.invalidateFilter()

    def set_filter_text(self, text):
        """Définit le texte de recherche"""
        self.filter_text = text
        self.invalidateFilter()

    def set_filter_category(self, category):
        """Définit la catégorie de filtre"""
        self.filter_category = category
        self.invalidateFilter()

    def set_filter_status(self, status):
        """Définit le statut de filtre"""
        self.filter_status = status
        self.invalidateFilter()

    def set_filter_location(self, location):
        """Définit l'emplacement de filtre"""
        self.filter_location = location
        self.invalidateFilter()

    def set_filter_condition(self, condition):
        """Définit la condition (état) de filtre"""
        self.filter_condition = condition
        self.invalidateFilter()

    def filterAcceptsRow(self, source_row, source_parent):
        """Détermine si une ligne doit être affichée selon les filtres"""
        source_model = self.sourceModel()

        # Filtre textuel (toutes colonnes)
        if self.filter_text:
            match_found = False
            for column in range(source_model.columnCount()):
                index = source_model.index(source_row, column, source_parent)
                data = source_model.data(index)
                if data and self.filter_text.lower() in str(data).lower():
                    match_found = True
                    break
            if not match_found:
                return False

        # Filtres spécifiques existants (pour inventaire)
        if self.filter_category and self.filter_category != "":
            category_column = 2
            index = source_model.index(source_row, category_column, source_parent)
            data = source_model.data(index)
            if not data or self.filter_category not in str(data):
                return False

        if self.filter_status and self.filter_status != "":
            item = getattr(source_model, 'items', None)
            if item is not None and source_row < len(source_model.items):
                obj = source_model.items[source_row]
                status_value = getattr(obj, 'status', None)
                if self.filter_status == "available":
                    if not status_value or status_value.value not in ("available", "low_stock"):
                        return False
                else:
                    if not status_value or status_value.value != self.filter_status:
                        return False

        if self.filter_location and self.filter_location != "":
            location_column = 4
            index = source_model.index(source_row, location_column, source_parent)
            data = source_model.data(index)
            if not data or self.filter_location not in str(data):
                return False

        if self.filter_condition and self.filter_condition != "":
            item = getattr(source_model, 'items', None)
            if item is not None and source_row < len(source_model.items):
                obj = source_model.items[source_row]
                condition_value = getattr(obj, 'condition', None)
                if not condition_value or condition_value.value != self.filter_condition:
                    return False

        # Filtres dynamiques passés via set_filters
        if self.filters:
            for filter_key, filter_value in self.filters.items():
                if filter_value is None or filter_value == "":
                    continue

                # Cas spécial: filtre statut client (basé sur current_balance du modèle clients)
                if filter_key == 'customer_status':
                    # On récupère la valeur de la colonne "Solde actuel" (index 9 dans CustomerTableModel)
                    balance_col = 9
                    try:
                        index_balance = source_model.index(source_row, balance_col, source_parent)
                        display_text = source_model.data(index_balance)
                        # Exemple affiché: "123.45 DA" => on extrait le float
                        balance_value = 0.0
                        if isinstance(display_text, str):
                            num = display_text.replace(' DA', '').replace('\u202f', '').replace(' ', '').replace(',', '.')
                            balance_value = float(num) if num else 0.0
                        else:
                            # Si le modèle retourne déjà un nombre, on utilise directement
                            balance_value = float(display_text or 0.0)

                        if filter_value == 'up_to_date' and balance_value > 0:
                            return False
                        if filter_value == 'not_up_to_date' and balance_value <= 0:
                            return False
                    except Exception:
                        # En cas d'erreur d'extraction, ne pas filtrer la ligne
                        pass
                    continue

                # Essaie via dict consolidé (si fourni par d'autres modèles)
                index0 = source_model.index(source_row, 0, source_parent)
                consolidated = source_model.data(index0, Qt.ItemDataRole.UserRole)
                if isinstance(consolidated, dict) and filter_key in consolidated:
                    current_value = consolidated.get(filter_key)
                    if isinstance(filter_value, (tuple, list, set)):
                        if current_value not in filter_value:
                            return False
                    else:
                        if current_value != filter_value:
                            return False
                    continue

                # Fallback par colonne mappée
                column_index = self._get_column_index(filter_key)
                if column_index >= 0:
                    index = source_model.index(source_row, column_index, source_parent)
                    # Try UserRole first, then fall back to DisplayRole
                    data_user = source_model.data(index, Qt.ItemDataRole.UserRole)
                    data_display = source_model.data(index, Qt.ItemDataRole.DisplayRole)
                    data_value = data_user if data_user is not None else data_display
                    # If filter_value is an Enum or object, compare its value/name when applicable
                    try:
                        from enum import Enum
                        if isinstance(filter_value, Enum):
                            filter_cmp = getattr(filter_value, 'value', str(filter_value))
                        else:
                            filter_cmp = filter_value
                    except Exception:
                        filter_cmp = filter_value
                    if data_value != filter_cmp:
                        return False

        return True

    def _get_column_index(self, filter_key):
        """Obtient l'index de colonne correspondant à une clé de filtre"""
        # Mapper les clés de filtre aux indices de colonne
        column_map = {
            'status': 8,       # Colonne 8 pour le statut (inventaire)
            'category': 2,     # Colonne 2 pour la catégorie (inventaire)
            'location': 4,     # Colonne 4 pour l'emplacement (inventaire)
            'condition': 9,    # Colonne 9 pour l'état (inventaire)
            'supplier_rating': 8,  # Colonne 8 pour la note (fournisseurs)
        }
        return column_map.get(filter_key, -1)
