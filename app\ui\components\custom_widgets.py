from PyQt6.QtWidgets import QWidget, QLabel, QVBoxLayout, QLineEdit, QComboBox, QProgressBar
from PyQt6.QtCore import Qt, QTimer, QSize
from PyQt6.QtGui import QPainter, QColor, QIcon, QPen, QBrush
import re

class SearchBar(QLineEdit):
    def __init__(self, placeholder_text="", parent=None):
        super().__init__(parent)
        self.setPlaceholderText(placeholder_text)
        self.setClearButtonEnabled(True)
        self.setMinimumHeight(30)
        self.setObjectName("searchBar")

        # Ajouter une icône de recherche
        search_icon = QIcon("app/ui/resources/icons/search.svg")
        self.addAction(search_icon, QLineEdit.ActionPosition.LeadingPosition)

class FilterComboBox(QComboBox):
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.setObjectName("filterComboBox")
        self.setMinimumHeight(30)

        # Ajouter un texte par défaut
        self.addItem(f"Tous les {title.lower()}")

        # Option pour réinitialiser le filtre
        self.setInsertPolicy(QComboBox.InsertPolicy.InsertAtBottom)

class LoadingOverlay(QWidget):
    """Widget d'overlay de chargement"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Configurer le widget
        self.setObjectName("loadingOverlay")
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # Créer le layout
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Ajouter un label
        self.label = QLabel("Chargement...")
        self.label.setObjectName("loadingLabel")
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.label)

        # Ajouter une barre de progression
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("loadingProgressBar")
        self.progress_bar.setRange(0, 0)  # Barre de progression indéterminée
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setFixedSize(200, 10)
        layout.addWidget(self.progress_bar)

        # Configurer le timer pour l'animation
        self.angle = 0
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update)
        self.timer.start(50)

        # Masquer par défaut
        self.hide()

    def showEvent(self, event):
        """Gère l'événement d'affichage"""
        super().showEvent(event)

        # Ajuster la taille et la position
        if self.parentWidget():
            self.resize(self.parentWidget().size())

    def paintEvent(self, event):
        """Dessine l'overlay"""
        super().paintEvent(event)

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Dessiner le fond semi-transparent
        painter.fillRect(self.rect(), QColor(0, 0, 0, 128))

        # Dessiner un cercle de chargement
        center_x = self.width() / 2
        center_y = self.height() / 2 - 50
        radius = 30

        painter.setPen(Qt.PenStyle.NoPen)

        # Dessiner 8 points avec des opacités différentes
        for i in range(8):
            angle = self.angle + i * 45
            x = center_x + radius * 1.5 * (0.5 * (1 + 0.5 * (i % 2))) * (1 if i < 4 else -1)
            y = center_y + radius * 1.5 * (0.5 * (1 + 0.5 * (i % 2))) * (1 if i == 0 or i == 3 or i == 4 or i == 7 else -1)

            opacity = 255 - (i * 32)
            painter.setBrush(QBrush(QColor(255, 255, 255, opacity)))
            painter.drawEllipse(int(x - radius / 4), int(y - radius / 4), int(radius / 2), int(radius / 2))

        # Mettre à jour l'angle pour l'animation
        self.angle = (self.angle + 5) % 360

class CircularProgressBar(QWidget):
    """Widget de barre de progression circulaire"""

    def __init__(self, parent=None):
        super().__init__(parent)

        self.value = 0
        self.max_value = 100
        self.setMinimumSize(100, 100)

    def set_value(self, value):
        """Définit la valeur de la barre de progression"""
        self.value = max(0, min(value, self.max_value))
        self.update()

    def set_max_value(self, max_value):
        """Définit la valeur maximale de la barre de progression"""
        self.max_value = max(1, max_value)
        self.update()

    def paintEvent(self, event):
        """Dessine la barre de progression circulaire"""
        super().paintEvent(event)

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Dimensions
        width = self.width()
        height = self.height()
        size = min(width, height)

        # Calculer le rectangle
        rect_size = size - 20
        rect_x = (width - rect_size) / 2
        rect_y = (height - rect_size) / 2

        # Dessiner le cercle de fond
        painter.setPen(QPen(QColor("#E0E0E0"), 10))
        painter.drawArc(int(rect_x), int(rect_y), int(rect_size), int(rect_size), 0, 360 * 16)

        # Dessiner l'arc de progression
        if self.value > 0:
            angle = 360 * self.value / self.max_value
            painter.setPen(QPen(QColor("#1976D2"), 10))
            painter.drawArc(int(rect_x), int(rect_y), int(rect_size), int(rect_size), 90 * 16, -angle * 16)

        # Dessiner le texte
        painter.setPen(QColor("#333333"))
        painter.setFont(self.font())
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, f"{int(self.value)}%")

class ColoredProgressBar(QProgressBar):
    """Barre de progression colorée selon la valeur"""

    def __init__(self, parent=None):
        super().__init__(parent)

        self.setTextVisible(True)
        self.setRange(0, 100)
        self.setValue(0)

        # Définir les couleurs
        self.low_color = QColor("#F44336")  # Rouge
        self.medium_color = QColor("#FFC107")  # Jaune
        self.high_color = QColor("#4CAF50")  # Vert

    def paintEvent(self, event):
        """Dessine la barre de progression avec la couleur appropriée"""
        # Définir la couleur en fonction de la valeur
        value = self.value()
        if value < 33:
            color = self.low_color
        elif value < 66:
            color = self.medium_color
        else:
            color = self.high_color

        # Définir la feuille de style
        self.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                text-align: center;
            }}

            QProgressBar::chunk {{
                background-color: {color.name()};
                border-radius: 5px;
            }}
        """)

        # Appeler la méthode de la classe parente
        super().paintEvent(event)


class PasswordStrengthBar(ColoredProgressBar):
    """Barre de progression pour la force du mot de passe"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setTextVisible(False)
        self.setFixedHeight(10)

    def update_strength(self, password):
        """Met à jour la force du mot de passe"""
        strength = self._calculate_password_strength(password)
        self.setValue(strength)

    def _calculate_password_strength(self, password):
        """Calcule la force du mot de passe (0-100)"""
        if not password:
            return 0

        # Longueur de base (40 points max)
        length_score = min(40, len(password) * 4)

        # Complexité (60 points max)
        complexity_score = 0

        # Présence de lettres minuscules
        if re.search(r'[a-z]', password):
            complexity_score += 10

        # Présence de lettres majuscules
        if re.search(r'[A-Z]', password):
            complexity_score += 10

        # Présence de chiffres
        if re.search(r'[0-9]', password):
            complexity_score += 10

        # Présence de caractères spéciaux
        if re.search(r'[^a-zA-Z0-9]', password):
            complexity_score += 15

        # Mélange de types de caractères
        if len(set(re.findall(r'[a-z]|[A-Z]|[0-9]|[^a-zA-Z0-9]', password))) > 2:
            complexity_score += 15

        return length_score + complexity_score