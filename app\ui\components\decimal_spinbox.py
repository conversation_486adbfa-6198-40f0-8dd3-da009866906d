"""
Widget personnalisé pour la saisie de montants en Decimal avec validation stricte.
"""
from PyQt6.QtWidgets import QLineEdit, QHBoxLayout, QWidget, QLabel
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtGui import QValidator, QDoubleValidator
from decimal import Decimal, InvalidOperation, ROUND_HALF_UP
from typing import Optional


class DecimalValidator(QValidator):
    """Validateur personnalisé pour les valeurs Decimal"""
    
    def __init__(self, minimum: Decimal = None, maximum: Decimal = None, decimals: int = 2):
        super().__init__()
        self.minimum = minimum or Decimal("-999999999.99")
        self.maximum = maximum or Decimal("999999999.99")
        self.decimals = decimals
    
    def validate(self, input_str: str, pos: int) -> tuple:
        """Valide l'entrée utilisateur"""
        if not input_str or input_str == "-":
            return QValidator.State.Intermediate, input_str, pos
        
        # Remplacer la virgule par un point
        input_str = input_str.replace(',', '.')
        
        try:
            value = Decimal(input_str)
            
            # Vérifier les limites
            if value < self.minimum or value > self.maximum:
                return QValidator.State.Invalid, input_str, pos
            
            # Vérifier le nombre de décimales
            if value.as_tuple().exponent < -self.decimals:
                return QValidator.State.Invalid, input_str, pos
            
            return QValidator.State.Acceptable, input_str, pos
            
        except (InvalidOperation, ValueError):
            return QValidator.State.Invalid, input_str, pos
    
    def fixup(self, input_str: str) -> str:
        """Corrige l'entrée si possible"""
        try:
            input_str = input_str.replace(',', '.')
            value = Decimal(input_str)
            
            # Appliquer les limites
            if value < self.minimum:
                value = self.minimum
            elif value > self.maximum:
                value = self.maximum
            
            # Arrondir au nombre de décimales approprié
            value = value.quantize(Decimal('0.' + '0' * self.decimals), rounding=ROUND_HALF_UP)
            
            return str(value)
        except (InvalidOperation, ValueError):
            return "0.00"


class DecimalSpinBox(QWidget):
    """Widget de saisie pour les montants en Decimal"""
    
    # Signaux
    valueChanged = pyqtSignal(Decimal)
    editingFinished = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Propriétés
        self._minimum = Decimal("0.00")
        self._maximum = Decimal("999999.99")
        self._decimals = 2
        self._value = Decimal("0.00")
        self._suffix = ""
        self._prefix = ""
        
        # Interface
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Label préfixe
        self.prefix_label = QLabel()
        self.prefix_label.hide()
        layout.addWidget(self.prefix_label)
        
        # Champ de saisie
        self.line_edit = QLineEdit()
        self.line_edit.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.line_edit.setText("0.00")
        layout.addWidget(self.line_edit)
        
        # Label suffixe
        self.suffix_label = QLabel()
        self.suffix_label.hide()
        layout.addWidget(self.suffix_label)
        
        # Validateur
        self._update_validator()
    
    def setup_connections(self):
        """Configure les connexions de signaux"""
        self.line_edit.textChanged.connect(self._on_text_changed)
        self.line_edit.editingFinished.connect(self._on_editing_finished)
    
    def _update_validator(self):
        """Met à jour le validateur"""
        validator = DecimalValidator(self._minimum, self._maximum, self._decimals)
        self.line_edit.setValidator(validator)
    
    def _on_text_changed(self, text: str):
        """Gère le changement de texte"""
        try:
            # Remplacer la virgule par un point
            text = text.replace(',', '.')
            value = Decimal(text) if text else Decimal("0.00")
            
            if value != self._value:
                self._value = value
                self.valueChanged.emit(self._value)
        except (InvalidOperation, ValueError):
            pass
    
    def _on_editing_finished(self):
        """Gère la fin d'édition"""
        try:
            text = self.line_edit.text().replace(',', '.')
            value = Decimal(text) if text else Decimal("0.00")
            
            # Appliquer les limites
            if value < self._minimum:
                value = self._minimum
            elif value > self._maximum:
                value = self._maximum
            
            # Arrondir
            value = value.quantize(Decimal('0.' + '0' * self._decimals), rounding=ROUND_HALF_UP)
            
            # Mettre à jour l'affichage
            self.setValue(value)
            self.editingFinished.emit()
            
        except (InvalidOperation, ValueError):
            self.setValue(Decimal("0.00"))
    
    # Propriétés publiques
    def value(self) -> Decimal:
        """Retourne la valeur actuelle"""
        return self._value
    
    def setValue(self, value: Decimal):
        """Définit la valeur"""
        if isinstance(value, (int, float)):
            value = Decimal(str(value))
        elif not isinstance(value, Decimal):
            raise TypeError("La valeur doit être un Decimal")
        
        # Appliquer les limites
        if value < self._minimum:
            value = self._minimum
        elif value > self._maximum:
            value = self._maximum
        
        # Arrondir
        value = value.quantize(Decimal('0.' + '0' * self._decimals), rounding=ROUND_HALF_UP)
        
        if value != self._value:
            self._value = value
            self.line_edit.setText(str(value))
            self.valueChanged.emit(self._value)
    
    def minimum(self) -> Decimal:
        """Retourne la valeur minimum"""
        return self._minimum
    
    def setMinimum(self, minimum: Decimal):
        """Définit la valeur minimum"""
        if isinstance(minimum, (int, float)):
            minimum = Decimal(str(minimum))
        self._minimum = minimum
        self._update_validator()
        
        # Vérifier la valeur actuelle
        if self._value < self._minimum:
            self.setValue(self._minimum)
    
    def maximum(self) -> Decimal:
        """Retourne la valeur maximum"""
        return self._maximum
    
    def setMaximum(self, maximum: Decimal):
        """Définit la valeur maximum"""
        if isinstance(maximum, (int, float)):
            maximum = Decimal(str(maximum))
        self._maximum = maximum
        self._update_validator()
        
        # Vérifier la valeur actuelle
        if self._value > self._maximum:
            self.setValue(self._maximum)
    
    def setRange(self, minimum: Decimal, maximum: Decimal):
        """Définit la plage de valeurs"""
        self.setMinimum(minimum)
        self.setMaximum(maximum)
    
    def decimals(self) -> int:
        """Retourne le nombre de décimales"""
        return self._decimals
    
    def setDecimals(self, decimals: int):
        """Définit le nombre de décimales"""
        self._decimals = max(0, decimals)
        self._update_validator()
        
        # Reformater la valeur actuelle
        self.setValue(self._value)
    
    def suffix(self) -> str:
        """Retourne le suffixe"""
        return self._suffix
    
    def setSuffix(self, suffix: str):
        """Définit le suffixe"""
        self._suffix = suffix
        if suffix:
            self.suffix_label.setText(suffix)
            self.suffix_label.show()
        else:
            self.suffix_label.hide()
    
    def prefix(self) -> str:
        """Retourne le préfixe"""
        return self._prefix
    
    def setPrefix(self, prefix: str):
        """Définit le préfixe"""
        self._prefix = prefix
        if prefix:
            self.prefix_label.setText(prefix)
            self.prefix_label.show()
        else:
            self.prefix_label.hide()
    
    def clear(self):
        """Efface le contenu"""
        self.setValue(Decimal("0.00"))
    
    def selectAll(self):
        """Sélectionne tout le texte"""
        self.line_edit.selectAll()
    
    def setFocus(self):
        """Donne le focus au widget"""
        self.line_edit.setFocus()
