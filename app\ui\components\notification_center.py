from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QScrollArea, QFrame, QMenu, QToolButton, QSizePolicy,
    QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal, QTimer, QRect
from PyQt6.QtGui import QIcon, QAction, QPainter, QColor, QFont

# Importer les icônes Unicode
from app.ui.components.unicode_icons import UnicodeIcons
import asyncio
from datetime import datetime, timezone

from app.core.services.notification_service import NotificationService
from app.core.models.notification import NotificationStatus, NotificationPriority, NotificationType

class NotificationItem(QFrame):
    """Widget représentant une notification individuelle"""

    markAsRead = pyqtSignal(int)
    delete = pyqtSignal(int)
    clicked = pyqtSignal(int, str)  # ID de la notification, URL d'action

    def __init__(self, notification, parent=None):
        super().__init__(parent)
        self.notification = notification
        self.notification_id = notification.id
        self.action_url = notification.action_url

        self.setObjectName("notificationItem")
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)
        self.setCursor(Qt.CursorShape.PointingHandCursor)

        # Appliquer un style selon la priorité
        if notification.priority == NotificationPriority.HIGH:
            self.setProperty("priority", "high")
        elif notification.priority == NotificationPriority.CRITICAL:
            self.setProperty("priority", "critical")
        else:
            self.setProperty("priority", "normal")

        # Appliquer un style selon le statut
        if notification.status == NotificationStatus.READ:
            self.setProperty("status", "read")
        else:
            self.setProperty("status", "unread")

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur de l'élément de notification"""
        layout = QVBoxLayout(self)

        # En-tête avec titre et date
        header_layout = QHBoxLayout()

        # Icône selon le type
        icon_name = self.notification.icon or self._get_default_icon()
        icon_label = QLabel()
        icon_label.setPixmap(QIcon(f":/icons/icons/{icon_name}.svg").pixmap(QSize(16, 16)))
        header_layout.addWidget(icon_label)

        # Titre
        title_label = QLabel(self.notification.title)
        title_label.setObjectName("notificationTitle")
        title_label.setWordWrap(True)
        header_layout.addWidget(title_label, 1)

        # Date
        date_label = QLabel(self._format_date(self.notification.created_at))
        date_label.setObjectName("notificationDate")
        header_layout.addWidget(date_label)

        # Bouton de menu
        menu_button = QToolButton()
        menu_button.setIcon(QIcon("app/ui/resources/icons/more.svg"))
        menu_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)

        # Menu contextuel
        menu = QMenu(menu_button)

        if self.notification.status == NotificationStatus.UNREAD:
            mark_read_action = QAction("Marquer comme lu", menu)
            mark_read_action.triggered.connect(lambda: self.markAsRead.emit(self.notification_id))
            menu.addAction(mark_read_action)

        delete_action = QAction("Supprimer", menu)
        delete_action.triggered.connect(lambda: self.delete.emit(self.notification_id))
        menu.addAction(delete_action)

        menu_button.setMenu(menu)
        header_layout.addWidget(menu_button)

        layout.addLayout(header_layout)

        # Message
        message_label = QLabel(self.notification.message)
        message_label.setObjectName("notificationMessage")
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        self.setLayout(layout)

    def _format_date(self, date):
        """Formate la date de la notification"""
        # Convertir la date en datetime avec fuseau horaire si ce n'est pas déjà le cas
        if date.tzinfo is None:
            date = date.replace(tzinfo=timezone.utc)

        now = datetime.now(timezone.utc)
        delta = now - date

        if delta.days == 0:
            if delta.seconds < 60:
                return "À l'instant"
            elif delta.seconds < 3600:
                minutes = delta.seconds // 60
                return f"Il y a {minutes} minute{'s' if minutes > 1 else ''}"
            else:
                hours = delta.seconds // 3600
                return f"Il y a {hours} heure{'s' if hours > 1 else ''}"
        elif delta.days == 1:
            return "Hier"
        elif delta.days < 7:
            return f"Il y a {delta.days} jour{'s' if delta.days > 1 else ''}"
        else:
            return date.strftime("%d/%m/%Y")

    def _get_default_icon(self):
        """Retourne l'icône par défaut selon le type de notification"""
        icon_map = {
            NotificationType.SYSTEM: "system",
            NotificationType.REPAIR: "repair",
            NotificationType.INVENTORY: "inventory",
            NotificationType.EQUIPMENT: "equipment",
            NotificationType.SUPPLIER: "supplier",
            NotificationType.USER: "user",
            NotificationType.CUSTOMER: "customer",
            NotificationType.FINANCE: "invoice"
        }
        return icon_map.get(self.notification.type, "notification")

    def mousePressEvent(self, event):
        """Gère le clic sur la notification"""
        super().mousePressEvent(event)
        self.clicked.emit(self.notification_id, self.action_url or "")

class NotificationCenter(QWidget):
    """Widget central pour afficher et gérer les notifications"""

    notificationClicked = pyqtSignal(int, str)  # ID de la notification, URL d'action
    unreadCountChanged = pyqtSignal(int)  # Nombre de notifications non lues

    def __init__(self, parent=None):
        super().__init__(parent)
        self.service = NotificationService()
        self.notifications = []
        self.unread_count = 0

        self.setup_ui()

        # Configurer un timer pour rafraîchir les notifications (désactivé pour éviter les doublons)
        # self.refresh_timer = QTimer(self)
        # self.refresh_timer.timeout.connect(self.refresh_notifications)
        # self.refresh_timer.start(60000)  # Rafraîchir toutes les minutes

        # Charger les notifications au démarrage
        QTimer.singleShot(0, self.refresh_notifications)

    def setup_ui(self):
        """Configure l'interface utilisateur du centre de notifications"""
        layout = QVBoxLayout(self)

        # En-tête
        header_layout = QHBoxLayout()

        title = QLabel("Notifications")
        title.setObjectName("notificationCenterTitle")
        header_layout.addWidget(title)

        # Boutons d'action
        self.mark_all_read_button = QPushButton("Tout marquer comme lu")
        self.mark_all_read_button.clicked.connect(self.mark_all_as_read)
        header_layout.addWidget(self.mark_all_read_button)

        layout.addLayout(header_layout)

        # Liste des notifications
        self.notification_list = QListWidget()
        self.notification_list.setObjectName("notificationList")
        self.notification_list.setFrameShape(QFrame.Shape.NoFrame)
        self.notification_list.setVerticalScrollMode(QListWidget.ScrollMode.ScrollPerPixel)

        # Scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.notification_list)

        layout.addWidget(scroll_area)

        # Message si pas de notifications
        self.empty_label = QLabel("Aucune notification")
        self.empty_label.setObjectName("emptyNotifications")
        self.empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.empty_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.empty_label.hide()

        layout.addWidget(self.empty_label)

        self.setLayout(layout)

    def refresh_notifications(self):
        """Rafraîchit la liste des notifications"""
        try:
            # Utiliser l'event loop existant ou en créer un nouveau de manière sûre
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            if loop.is_running():
                asyncio.create_task(self._load_notifications())
            else:
                loop.run_until_complete(self._load_notifications())
        except Exception as e:
            print(f"Erreur lors du rafraîchissement des notifications: {e}")

    async def _load_notifications(self):
        """Charge les notifications depuis le service"""
        try:
            # Récupérer les notifications non archivées
            self.notifications = await self.service.get_user_notifications(
                user_id=1,  # TODO: Récupérer l'ID de l'utilisateur connecté
                status=None,  # Toutes les notifications non archivées
                limit=50
            )

            # Récupérer le nombre de notifications non lues
            self.unread_count = await self.service.get_unread_count(1)  # TODO: Récupérer l'ID de l'utilisateur connecté

            # Mettre à jour l'interface
            self._update_ui()

            # Émettre le signal de changement du nombre de notifications non lues
            self.unreadCountChanged.emit(self.unread_count)

        except Exception as e:
            print(f"Erreur lors du chargement des notifications: {str(e)}")

    def _update_ui(self):
        """Met à jour l'interface utilisateur avec les notifications chargées"""
        # Vider la liste
        self.notification_list.clear()

        if not self.notifications:
            self.notification_list.hide()
            self.empty_label.show()
            return

        self.notification_list.show()
        self.empty_label.hide()

        # Ajouter les notifications à la liste
        for notification in self.notifications:
            item = QListWidgetItem(self.notification_list)

            # Créer le widget de notification
            notification_widget = NotificationItem(notification)

            # Connecter les signaux
            notification_widget.markAsRead.connect(self.mark_as_read)
            notification_widget.delete.connect(self.delete_notification)
            notification_widget.clicked.connect(self.handle_notification_click)

            # Définir la taille de l'élément
            item.setSizeHint(notification_widget.sizeHint())

            # Ajouter le widget à l'élément
            self.notification_list.addItem(item)
            self.notification_list.setItemWidget(item, notification_widget)

    def mark_as_read(self, notification_id):
        """Marque une notification comme lue"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            if loop.is_running():
                asyncio.create_task(self._mark_as_read(notification_id))
            else:
                loop.run_until_complete(self._mark_as_read(notification_id))
        except Exception as e:
            print(f"Erreur lors du marquage de la notification comme lue: {e}")

    async def _mark_as_read(self, notification_id):
        """Marque une notification comme lue de manière asynchrone"""
        try:
            result = await self.service.mark_as_read(notification_id, 1)  # TODO: Récupérer l'ID de l'utilisateur connecté
            if result:
                # Rafraîchir les notifications
                await self._load_notifications()
        except Exception as e:
            print(f"Erreur lors du marquage de la notification comme lue: {str(e)}")

    def mark_all_as_read(self):
        """Marque toutes les notifications comme lues"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            if loop.is_running():
                asyncio.create_task(self._mark_all_as_read())
            else:
                loop.run_until_complete(self._mark_all_as_read())
        except Exception as e:
            print(f"Erreur lors du marquage de toutes les notifications comme lues: {e}")

    async def _mark_all_as_read(self):
        """Marque toutes les notifications comme lues de manière asynchrone"""
        try:
            count = await self.service.mark_all_as_read(1)  # TODO: Récupérer l'ID de l'utilisateur connecté
            if count > 0:
                # Rafraîchir les notifications
                await self._load_notifications()
        except Exception as e:
            print(f"Erreur lors du marquage de toutes les notifications comme lues: {str(e)}")

    def delete_notification(self, notification_id):
        """Supprime une notification"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            if loop.is_running():
                asyncio.create_task(self._delete_notification(notification_id))
            else:
                loop.run_until_complete(self._delete_notification(notification_id))
        except Exception as e:
            print(f"Erreur lors de la suppression de la notification: {e}")

    async def _delete_notification(self, notification_id):
        """Supprime une notification de manière asynchrone"""
        try:
            result = await self.service.delete_notification(notification_id, 1)  # TODO: Récupérer l'ID de l'utilisateur connecté
            if result:
                # Rafraîchir les notifications
                await self._load_notifications()
        except Exception as e:
            print(f"Erreur lors de la suppression de la notification: {str(e)}")

    def handle_notification_click(self, notification_id, action_url):
        """Gère le clic sur une notification"""
        # Marquer la notification comme lue
        self.mark_as_read(notification_id)

        # Émettre le signal de clic sur la notification
        self.notificationClicked.emit(notification_id, action_url)

class NotificationButton(QToolButton):
    """Bouton de notification avec badge pour le nombre de notifications non lues"""

    clicked = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.unread_count = 0

        # Utiliser un style personnalisé pour le bouton
        self.setStyleSheet("""
            QToolButton {
                background-color: transparent;
                border: none;
                color: #2196F3;
                padding: 4px;
                margin: 0px;
            }
            QToolButton:hover {
                background-color: rgba(33, 150, 243, 0.1);
            }
        """)

        # Utiliser l'icône SVG
        self.setIcon(QIcon("app/ui/resources/icons/notification_new.svg"))
        self.setIconSize(QSize(24, 24))
        self.setToolTip("Notifications")
        self.setCursor(Qt.CursorShape.PointingHandCursor)

        # Définir une taille fixe pour le bouton
        self.setFixedSize(32, 32)

        self.setObjectName("notificationButton")

        # Connecter le signal de clic
        self.clicked.connect(self.clicked)

    def set_unread_count(self, count):
        """Définit le nombre de notifications non lues"""
        self.unread_count = count
        self.update()

        # Mettre à jour le tooltip
        if count > 0:
            self.setToolTip(f"{count} notification{'s' if count > 1 else ''} non lue{'s' if count > 1 else ''}")
        else:
            self.setToolTip("Notifications")

    def paintEvent(self, event):
        """Dessine le bouton avec le badge"""
        super().paintEvent(event)

        if self.unread_count > 0:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # Dessiner le badge
            badge_size = 16
            badge_x = self.width() - badge_size
            badge_y = 0

            painter.setBrush(QColor("#FF4560"))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawEllipse(badge_x, badge_y, badge_size, badge_size)

            # Dessiner le nombre
            painter.setPen(Qt.GlobalColor.white)
            painter.setFont(QFont("Arial", 8, QFont.Weight.Bold))

            text = str(self.unread_count) if self.unread_count < 10 else "9+"
            painter.drawText(
                QRect(badge_x, badge_y, badge_size, badge_size),
                Qt.AlignmentFlag.AlignCenter,
                text
            )
