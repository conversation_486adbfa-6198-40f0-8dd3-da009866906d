"""
Module pour la prévisualisation des PDF.
"""
import os
import subprocess
import platform
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QDialogButtonBox, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon

class PDFPreviewDialog(QDialog):
    """Boîte de dialogue pour prévisualiser un PDF."""

    # Signal émis lorsque l'utilisateur clique sur le bouton d'impression
    printRequested = pyqtSignal(str)

    def __init__(self, pdf_path, parent=None, title="Prévisualisation du PDF"):
        """
        Initialise la boîte de dialogue de prévisualisation.

        Args:
            pdf_path: Chemin vers le fichier PDF à prévisualiser
            parent: Widget parent
            title: Titre de la boîte de dialogue
        """
        super().__init__(parent)
        self.pdf_path = pdf_path
        self.setWindowTitle(title)
        self.resize(400, 200)
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur."""
        # Layout principal
        main_layout = QVBoxLayout(self)

        # Message d'information
        info_label = QLabel(f"Le PDF a été généré avec succès à l'emplacement:\n{self.pdf_path}")
        info_label.setWordWrap(True)
        main_layout.addWidget(info_label)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        # Bouton d'ouverture
        self.open_button = QPushButton("Ouvrir")
        self.open_button.setIcon(QIcon("app/ui/resources/icons/open.svg"))
        self.open_button.clicked.connect(self.open_pdf)
        buttons_layout.addWidget(self.open_button)

        # Bouton d'enregistrement
        self.save_button = QPushButton("Enregistrer sous...")
        self.save_button.setIcon(QIcon("app/ui/resources/icons/save.svg"))
        self.save_button.clicked.connect(self.save_pdf)
        buttons_layout.addWidget(self.save_button)

        # Bouton d'impression
        self.print_button = QPushButton("Imprimer")
        self.print_button.setIcon(QIcon("app/ui/resources/icons/print.svg"))
        self.print_button.clicked.connect(self.print_pdf)
        buttons_layout.addWidget(self.print_button)

        main_layout.addLayout(buttons_layout)

        # Boutons de dialogue
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

        # Vérifier si le fichier existe
        if not os.path.exists(self.pdf_path):
            QMessageBox.critical(self, "Erreur", f"Le fichier PDF n'existe pas: {self.pdf_path}")
            self.reject()
            return

        # Ouvrir automatiquement le PDF
        self.open_pdf()

    def open_pdf(self):
        """Ouvre le PDF avec l'application par défaut."""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.pdf_path)
            elif os.name == 'posix':  # macOS et Linux
                subprocess.call(('xdg-open', self.pdf_path))
            else:
                QMessageBox.warning(self, "Avertissement", "Système d'exploitation non pris en charge.")
                return False
            return True
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ouverture du PDF: {str(e)}")
            return False

    def print_pdf(self):
        """Imprime le PDF en l'ouvrant avec l'application par défaut."""
        if self.open_pdf():
            QMessageBox.information(
                self,
                "Impression",
                "Le PDF a été ouvert avec l'application par défaut. Utilisez la fonction d'impression de cette application."
            )

    def save_pdf(self):
        """Enregistre le PDF à un autre emplacement."""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Enregistrer le PDF",
            os.path.basename(self.pdf_path),
            "Fichiers PDF (*.pdf)"
        )

        if file_path:
            try:
                # Copier le fichier
                import shutil
                shutil.copy2(self.pdf_path, file_path)
                QMessageBox.information(
                    self,
                    "Enregistrement",
                    f"Le PDF a été enregistré avec succès à l'emplacement:\n{file_path}"
                )
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Erreur",
                    f"Une erreur est survenue lors de l'enregistrement du PDF:\n{str(e)}"
                )
