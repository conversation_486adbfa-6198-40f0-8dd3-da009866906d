from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QListWidget,
    QListWidgetItem, QLabel, QPushButton, QFrame, QCompleter,
    QStyledItemDelegate, QStyle, QComboBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QStringListModel, QTimer, QSize, QRect
from PyQt6.QtGui import QIcon, QColor, QPainter, QFont, QBrush

class ProductItemDelegate(QStyledItemDelegate):
    """Délégué personnalisé pour l'affichage des produits dans la liste"""

    def __init__(self, parent=None):
        super().__init__(parent)

    def paint(self, painter, option, index):
        """Dessine l'élément avec des informations supplémentaires"""
        # Récupérer le produit
        product = index.data(Qt.ItemDataRole.UserRole)
        if not product:
            super().paint(painter, option, index)
            return

        # Dessiner le fond
        if option.state & QStyle.StateFlag.State_Selected:
            painter.fillRect(option.rect, option.palette.highlight())
            text_color = option.palette.highlightedText().color()
        else:
            painter.fillRect(option.rect, option.palette.base())
            text_color = option.palette.text().color()

        # Configurer le pinceau
        painter.save()

        # Dessiner le nom du produit
        font = QFont(option.font)
        font.setBold(True)
        painter.setFont(font)
        painter.setPen(text_color)

        name_rect = QRect(option.rect)
        name_rect.setLeft(name_rect.left() + 5)
        name_rect.setTop(name_rect.top() + 5)
        name_rect.setHeight(20)

        painter.drawText(name_rect, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter, product.name)

        # Dessiner la référence
        font.setBold(False)
        font.setItalic(True)
        painter.setFont(font)

        ref_rect = QRect(option.rect)
        ref_rect.setLeft(ref_rect.left() + 5)
        ref_rect.setTop(name_rect.bottom())
        ref_rect.setHeight(15)

        if hasattr(product, 'sku') and product.sku:
            painter.drawText(ref_rect, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter, f"Réf: {product.sku}")

        # Dessiner le prix
        price_rect = QRect(option.rect)
        price_rect.setRight(price_rect.right() - 5)
        price_rect.setTop(name_rect.top())
        price_rect.setHeight(20)

        # Utiliser purchase_price en priorité, sinon unit_price comme fallback pour l'affichage
        price = getattr(product, 'purchase_price', getattr(product, 'unit_price', 0))
        if price > 0:
            price_text = f"{price:.2f} DA"
            painter.setPen(QColor("#2196F3"))  # Bleu
            painter.drawText(price_rect, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter, price_text)

        # Dessiner le stock
        stock_rect = QRect(option.rect)
        stock_rect.setRight(stock_rect.right() - 5)
        stock_rect.setTop(ref_rect.top())
        stock_rect.setHeight(15)

        if hasattr(product, 'quantity'):
            if product.quantity <= 0:
                stock_color = QColor("#F44336")  # Rouge
                stock_text = f"Rupture"
            elif product.quantity < 5:
                stock_color = QColor("#FF9800")  # Orange
                stock_text = f"Stock: {product.quantity} (Faible)"
            else:
                stock_color = QColor("#4CAF50")  # Vert
                stock_text = f"Stock: {product.quantity}"

            painter.setPen(stock_color)
            painter.drawText(stock_rect, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter, stock_text)

        painter.restore()

    def sizeHint(self, option, index):
        """Retourne la taille suggérée pour l'élément"""
        size = super().sizeHint(option, index)
        size.setHeight(45)  # Hauteur fixe pour tous les éléments
        return size

class ProductSearchWidget(QWidget):
    """Widget de recherche de produits avec auto-complétion"""

    # Signal émis lorsqu'un produit est sélectionné
    product_selected = pyqtSignal(object)  # Produit sélectionné
    create_new_product = pyqtSignal()  # Signal pour créer un nouveau produit

    def __init__(self, products=None, parent=None):
        super().__init__(parent)
        self.products = products or []
        self.filtered_products = []
        self.db = None

        # Créer une session de base de données si nécessaire
        try:
            from app.utils.database import SessionLocal
            self.db = SessionLocal()
            print("ProductSearchWidget: Nouvelle session de base de données créée")
        except Exception as e:
            print(f"Erreur lors de la création de la session: {e}")

        self.setup_ui()
        self.setup_connections()
        self.update_products(self.products)

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Barre de recherche
        search_layout = QHBoxLayout()

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher un produit (nom, référence, code-barres...)")
        self.search_input.setClearButtonEnabled(True)
        search_layout.addWidget(self.search_input, 1)

        # Bouton pour créer un nouveau produit
        self.new_product_button = QPushButton("Nouveau")
        self.new_product_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.new_product_button.setToolTip("Créer un nouveau produit")
        search_layout.addWidget(self.new_product_button)

        main_layout.addLayout(search_layout)

        # Filtres avancés
        filters_layout = QHBoxLayout()

        # Filtre par catégorie
        self.category_combo = QComboBox()
        self.category_combo.addItem("Toutes les catégories", None)
        self.category_combo.setMinimumWidth(150)
        filters_layout.addWidget(self.category_combo)

        # Filtre par fournisseur
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItem("Tous les fournisseurs", None)
        self.supplier_combo.setMinimumWidth(150)
        filters_layout.addWidget(self.supplier_combo)

        # Filtre par stock
        self.stock_combo = QComboBox()
        self.stock_combo.addItem("Tout le stock", None)
        self.stock_combo.addItem("En stock", "in_stock")
        self.stock_combo.addItem("Stock faible", "low_stock")
        self.stock_combo.addItem("Rupture de stock", "out_of_stock")
        self.stock_combo.setMinimumWidth(120)
        filters_layout.addWidget(self.stock_combo)

        # Bouton pour scanner un code-barres
        self.barcode_button = QPushButton()
        self.barcode_button.setIcon(QIcon("app/ui/resources/icons/barcode.svg"))
        self.barcode_button.setToolTip("Scanner un code-barres")
        self.barcode_button.setFixedWidth(40)
        filters_layout.addWidget(self.barcode_button)

        main_layout.addLayout(filters_layout)

        # Liste des résultats
        self.results_frame = QFrame()
        self.results_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.results_frame.setFrameShadow(QFrame.Shadow.Sunken)
        self.results_frame.setMaximumHeight(250)

        results_layout = QVBoxLayout(self.results_frame)
        results_layout.setContentsMargins(0, 0, 0, 0)

        self.results_list = QListWidget()
        self.results_list.setAlternatingRowColors(True)

        # Utiliser notre délégué personnalisé
        self.item_delegate = ProductItemDelegate(self.results_list)
        self.results_list.setItemDelegate(self.item_delegate)

        results_layout.addWidget(self.results_list)

        main_layout.addWidget(self.results_frame)

        # Cacher la liste des résultats par défaut
        self.results_frame.setVisible(False)

        # Informations sur le produit sélectionné
        self.info_frame = QFrame()
        self.info_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.info_frame.setFrameShadow(QFrame.Shadow.Raised)

        info_layout = QVBoxLayout(self.info_frame)

        self.product_name_label = QLabel()
        self.product_name_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        info_layout.addWidget(self.product_name_label)

        details_layout = QHBoxLayout()

        self.product_sku_label = QLabel()
        details_layout.addWidget(self.product_sku_label)

        details_layout.addStretch()

        self.product_price_label = QLabel()
        self.product_price_label.setStyleSheet("font-weight: bold; color: #2196F3; font-size: 13px;")
        details_layout.addWidget(self.product_price_label)

        self.product_stock_label = QLabel()
        details_layout.addWidget(self.product_stock_label)

        info_layout.addLayout(details_layout)

        # Ajouter des informations supplémentaires
        extra_layout = QHBoxLayout()

        self.product_category_label = QLabel()
        self.product_category_label.setStyleSheet("font-style: italic;")
        extra_layout.addWidget(self.product_category_label)

        extra_layout.addStretch()

        self.product_supplier_label = QLabel()
        extra_layout.addWidget(self.product_supplier_label)

        info_layout.addLayout(extra_layout)

        main_layout.addWidget(self.info_frame)

        # Cacher les informations par défaut
        self.info_frame.setVisible(False)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.search_input.textChanged.connect(self.filter_products)
        self.results_list.itemClicked.connect(self.on_item_clicked)
        self.new_product_button.clicked.connect(self.create_new_product.emit)

        # Connecter les filtres avancés
        self.category_combo.currentIndexChanged.connect(lambda: self.filter_products(self.search_input.text()))
        self.supplier_combo.currentIndexChanged.connect(lambda: self.filter_products(self.search_input.text()))
        self.stock_combo.currentIndexChanged.connect(lambda: self.filter_products(self.search_input.text()))

        # Connecter le bouton de code-barres
        self.barcode_button.clicked.connect(self.scan_barcode)

    def update_products(self, products):
        """Met à jour la liste des produits"""
        self.products = products
        self.filtered_products = []

        # S'assurer que les produits sont attachés à la session
        if hasattr(self, 'db') and self.db:
            try:
                # Vérifier si la session est active
                if not self.db.is_active:
                    self.refresh_session()

                # Attacher les produits à la session
                for product in self.products:
                    if hasattr(product, 'id'):
                        try:
                            # Vérifier si l'objet est détaché
                            product_name = self._safe_get_attr(product, 'name', None)
                            if product_name is None:
                                # Essayer de récupérer l'objet frais depuis la base de données
                                from app.core.services.inventory_service import InventoryService
                                service = InventoryService(self.db)
                                fresh_product = service.get_sync(product.id)
                                if fresh_product:
                                    # Remplacer l'objet détaché par l'objet frais
                                    index = self.products.index(product)
                                    self.products[index] = fresh_product
                        except Exception as e:
                            print(f"Erreur lors de la vérification du produit {product.id}: {e}")
            except Exception as e:
                print(f"Erreur lors de l'attachement des produits à la session: {e}")

        # Créer une liste de noms de produits pour l'auto-complétion
        product_names = []
        product_skus = []

        # Récupérer les catégories et fournisseurs uniques
        categories = set()
        suppliers = set()
        supplier_ids = set()

        for product in self.products:
            try:
                # Noms et SKUs pour l'auto-complétion
                product_name = self._safe_get_attr(product, 'name', '')
                if product_name:
                    product_names.append(product_name)

                product_sku = self._safe_get_attr(product, 'sku', '')
                if product_sku:
                    product_skus.append(product_sku)

                # Catégories
                product_category = self._safe_get_attr(product, 'category', None)
                if product_category:
                    if hasattr(product_category, 'value'):
                        categories.add(product_category.value)
                    else:
                        categories.add(str(product_category))

                # Fournisseurs
                product_supplier_id = self._safe_get_attr(product, 'supplier_id', None)
                if product_supplier_id:
                    supplier_ids.add(product_supplier_id)

                product_supplier_name = self._safe_get_attr(product, 'supplier_name', None)
                if product_supplier_name and product_supplier_id:
                    suppliers.add((product_supplier_id, product_supplier_name))
            except Exception as e:
                print(f"Erreur lors du traitement du produit: {e}")

        # Mettre à jour les filtres de catégorie
        self.category_combo.clear()
        self.category_combo.addItem("Toutes les catégories", None)
        for category in sorted(categories):
            self.category_combo.addItem(category, category)

        # Mettre à jour les filtres de fournisseur
        self.supplier_combo.clear()
        self.supplier_combo.addItem("Tous les fournisseurs", None)
        for supplier_id, supplier_name in sorted(suppliers, key=lambda x: x[1]):
            self.supplier_combo.addItem(supplier_name, supplier_id)

        # Créer un modèle pour l'auto-complétion
        completer_model = QStringListModel()
        completer_model.setStringList(product_names + product_skus)

        # Créer un completer
        completer = QCompleter()
        completer.setModel(completer_model)
        completer.setCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        completer.setFilterMode(Qt.MatchFlag.MatchContains)

        # Appliquer le completer à la barre de recherche
        self.search_input.setCompleter(completer)

    def filter_products(self, text):
        """Filtre les produits selon les critères de recherche"""
        # Récupérer les valeurs des filtres
        search_text = text.lower() if isinstance(text, str) else self.search_input.text().lower()
        category = self.category_combo.currentData()
        supplier_id = self.supplier_combo.currentData()
        stock_filter = self.stock_combo.currentData()

        # Filtrer les produits
        self.filtered_products = []

        for product in self.products:
            try:
                # Filtrer par texte de recherche
                if search_text:
                    text_match = False
                    # Utiliser getattr avec des valeurs par défaut pour éviter les erreurs
                    product_name = self._safe_get_attr(product, 'name', '').lower()
                    product_sku = self._safe_get_attr(product, 'sku', '').lower()
                    product_description = self._safe_get_attr(product, 'description', '').lower()
                    product_barcode = self._safe_get_attr(product, 'barcode', '').lower()

                    # Vérifier si le texte correspond au nom, à la référence, au code-barres ou à la description
                    if (search_text in product_name or
                        (product_sku and search_text in product_sku) or
                        (product_description and search_text in product_description) or
                        (product_barcode and search_text in product_barcode)):
                        text_match = True

                    if not text_match:
                        continue

                # Filtrer par catégorie
                if category:
                    category_match = False
                    product_category = self._safe_get_attr(product, 'category', None)

                    if product_category:
                        if hasattr(product_category, 'value'):
                            if product_category.value == category:
                                category_match = True
                        elif str(product_category) == category:
                            category_match = True

                    if not category_match:
                        continue

                # Filtrer par fournisseur
                product_supplier_id = self._safe_get_attr(product, 'supplier_id', None)
                if supplier_id and product_supplier_id != supplier_id:
                    continue

                # Filtrer par stock
                if stock_filter:
                    product_quantity = self._safe_get_attr(product, 'quantity', 0)

                    if stock_filter == "in_stock" and product_quantity <= 0:
                        continue
                    elif stock_filter == "low_stock" and (product_quantity <= 0 or product_quantity >= 5):
                        continue
                    elif stock_filter == "out_of_stock" and product_quantity > 0:
                        continue

                # Ajouter le produit aux résultats filtrés
                self.filtered_products.append(product)

            except Exception as e:
                print(f"Erreur lors du filtrage du produit: {e}")
                # Si une erreur se produit, essayer de rafraîchir la session
                if hasattr(self, 'db') and self.db:
                    try:
                        # Essayer de rafraîchir l'objet
                        if hasattr(product, 'id'):
                            from sqlalchemy.orm.exc import DetachedInstanceError
                            if isinstance(e, DetachedInstanceError):
                                print(f"Tentative de rafraîchissement de l'objet détaché (ID: {product.id})")
                                # Essayer de récupérer l'objet frais depuis la base de données
                                from app.core.services.inventory_service import InventoryService
                                service = InventoryService(self.db)
                                fresh_product = service.get_sync(product.id)
                                if fresh_product:
                                    # Remplacer l'objet détaché par l'objet frais
                                    index = self.products.index(product)
                                    self.products[index] = fresh_product
                                    self.filtered_products.append(fresh_product)
                    except Exception as refresh_error:
                        print(f"Erreur lors du rafraîchissement de l'objet: {refresh_error}")

        # Mettre à jour la liste des résultats
        self.update_results_list()

        # Afficher ou cacher la liste des résultats
        if search_text or category or supplier_id or stock_filter:
            self.results_frame.setVisible(True)
        else:
            self.results_frame.setVisible(False)

    def _safe_get_attr(self, obj, attr_name, default=None):
        """Récupère un attribut de manière sécurisée

        Args:
            obj: L'objet dont on veut récupérer l'attribut
            attr_name: Le nom de l'attribut
            default: La valeur par défaut si l'attribut n'existe pas ou si une erreur se produit

        Returns:
            La valeur de l'attribut ou la valeur par défaut
        """
        try:
            if hasattr(obj, attr_name):
                return getattr(obj, attr_name)
            return default
        except Exception as e:
            print(f"Erreur lors de la récupération de l'attribut {attr_name}: {e}")
            return default

    def update_results_list(self):
        """Met à jour la liste des résultats"""
        self.results_list.clear()

        if not self.filtered_products:
            self.results_frame.setVisible(False)
            return

        for product in self.filtered_products:
            item = QListWidgetItem()

            # Créer un texte avec le nom et le prix
            display_text = f"{product.name}"
            if hasattr(product, 'sku') and product.sku:
                display_text += f" ({product.sku})"

            item.setText(display_text)

            # Stocker le produit dans les données de l'item
            item.setData(Qt.ItemDataRole.UserRole, product)

            self.results_list.addItem(item)

        # Afficher la liste des résultats
        self.results_frame.setVisible(True)

    def on_item_clicked(self, item):
        """Gère le clic sur un élément de la liste"""
        # Récupérer le produit
        product = item.data(Qt.ItemDataRole.UserRole)

        # Mettre à jour les informations
        self.update_product_info(product)

        # Cacher la liste des résultats
        self.results_frame.setVisible(False)

        # Émettre le signal
        self.product_selected.emit(product)

    def update_product_info(self, product):
        """Met à jour les informations sur le produit sélectionné"""
        if not product:
            self.info_frame.setVisible(False)
            return

        try:
            # Vérifier si l'objet est détaché et le rafraîchir si nécessaire
            if hasattr(self, 'db') and self.db and hasattr(product, 'id'):
                try:
                    # Essayer d'accéder au nom pour voir si l'objet est détaché
                    product_name = self._safe_get_attr(product, 'name', None)
                    if product_name is None:
                        # L'objet est probablement détaché, essayer de le rafraîchir
                        from app.core.services.inventory_service import InventoryService
                        service = InventoryService(self.db)
                        fresh_product = service.get_sync(product.id)
                        if fresh_product:
                            product = fresh_product
                except Exception as e:
                    print(f"Erreur lors de la vérification du produit: {e}")

            # Mettre à jour les labels
            product_name = self._safe_get_attr(product, 'name', "Produit inconnu")
            self.product_name_label.setText(product_name)

            product_sku = self._safe_get_attr(product, 'sku', "")
            if product_sku:
                self.product_sku_label.setText(f"Réf: {product_sku}")
            else:
                self.product_sku_label.setText("")

            # Utiliser purchase_price en priorité, sinon unit_price comme fallback
            purchase_price = self._safe_get_attr(product, 'purchase_price',
                                               self._safe_get_attr(product, 'unit_price', 0.0))
            if purchase_price is not None and purchase_price > 0:
                self.product_price_label.setText(f"Prix d'achat: {purchase_price:.2f} DA")
            else:
                self.product_price_label.setText("")

            product_quantity = self._safe_get_attr(product, 'quantity', 0)
            if product_quantity is not None:
                stock_text = f"Stock: {product_quantity}"
                if product_quantity <= 0:
                    stock_text += " (Rupture)"
                    self.product_stock_label.setStyleSheet("color: #F44336;")  # Rouge
                elif product_quantity < 5:
                    stock_text += " (Faible)"
                    self.product_stock_label.setStyleSheet("color: #FF9800;")  # Orange
                else:
                    self.product_stock_label.setStyleSheet("color: #4CAF50;")  # Vert

                self.product_stock_label.setText(stock_text)
            else:
                self.product_stock_label.setText("")

            # Informations supplémentaires
            product_category = self._safe_get_attr(product, 'category', None)
            if product_category:
                if hasattr(product_category, 'value'):
                    self.product_category_label.setText(f"Catégorie: {product_category.value}")
                else:
                    self.product_category_label.setText(f"Catégorie: {product_category}")
            else:
                self.product_category_label.setText("")

            product_supplier_name = self._safe_get_attr(product, 'supplier_name', "")
            product_supplier_id = self._safe_get_attr(product, 'supplier_id', None)

            if product_supplier_name:
                self.product_supplier_label.setText(f"Fournisseur: {product_supplier_name}")
            elif product_supplier_id:
                self.product_supplier_label.setText(f"Fournisseur ID: {product_supplier_id}")
            else:
                self.product_supplier_label.setText("")

            # Afficher les informations
            self.info_frame.setVisible(True)

        except Exception as e:
            print(f"Erreur lors de la mise à jour des informations du produit: {e}")
            self.info_frame.setVisible(False)

    def scan_barcode(self):
        """Ouvre une boîte de dialogue pour scanner un code-barres"""
        from app.ui.components.barcode_scanner_dialog import BarcodeScannerDialog

        dialog = BarcodeScannerDialog(self)
        dialog.barcode_detected.connect(self.process_scanned_barcode)

        dialog.exec()

    def process_scanned_barcode(self, barcode):
        """Traite un code-barres scanné"""
        if not barcode:
            return

        # Rechercher le produit par code-barres ou SKU
        found = False
        for product in self.products:
            try:
                # Vérifier le code-barres
                product_barcode = self._safe_get_attr(product, 'barcode', "").lower()
                if product_barcode and product_barcode == barcode.lower():
                    # Sélectionner le produit
                    product_name = self._safe_get_attr(product, 'name', "")
                    self.search_input.setText(product_name)
                    self.update_product_info(product)
                    self.product_selected.emit(product)
                    found = True
                    break

                # Vérifier le SKU
                product_sku = self._safe_get_attr(product, 'sku', "").lower()
                if product_sku and product_sku == barcode.lower():
                    # Sélectionner le produit
                    product_name = self._safe_get_attr(product, 'name', "")
                    self.search_input.setText(product_name)
                    self.update_product_info(product)
                    self.product_selected.emit(product)
                    found = True
                    break
            except Exception as e:
                print(f"Erreur lors de la vérification du produit pour le code-barres: {e}")

        if not found:
            from PyQt6.QtWidgets import QMessageBox

            # Demander à l'utilisateur s'il souhaite créer un nouveau produit
            reply = QMessageBox.question(
                self,
                "Produit non trouvé",
                f"Aucun produit trouvé avec le code-barres '{barcode}'.\n\nVoulez-vous créer un nouveau produit?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Émettre le signal pour créer un nouveau produit
                self.create_new_product.emit()

    def get_selected_product(self):
        """Retourne le produit sélectionné"""
        # Vérifier si un produit est sélectionné dans la liste
        if self.results_list.currentItem():
            return self.results_list.currentItem().data(Qt.ItemDataRole.UserRole)

        # Sinon, rechercher le produit par le texte de recherche
        search_text = self.search_input.text().strip()
        if not search_text:
            return None

        # Rechercher un produit correspondant exactement au texte
        for product in self.products:
            if (product.name.lower() == search_text.lower() or
                (hasattr(product, 'sku') and product.sku and product.sku.lower() == search_text.lower())):
                return product

        return None

    def clear(self):
        """Réinitialise le widget"""
        self.search_input.clear()
        self.results_list.clear()
        self.results_frame.setVisible(False)
        self.info_frame.setVisible(False)

        # Réinitialiser les filtres
        self.category_combo.setCurrentIndex(0)
        self.supplier_combo.setCurrentIndex(0)
        self.stock_combo.setCurrentIndex(0)

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            try:
                self.db.close()
                print("ProductSearchWidget: Session de base de données fermée")
            except Exception as e:
                print(f"Erreur lors de la fermeture de la session: {e}")

    def refresh_session(self):
        """Rafraîchit la session de base de données"""
        if hasattr(self, 'db') and self.db:
            try:
                self.db.close()
                print("ProductSearchWidget: Ancienne session fermée")
            except Exception as e:
                print(f"Erreur lors de la fermeture de la session: {e}")

        try:
            from app.utils.database import SessionLocal
            self.db = SessionLocal()
            print("ProductSearchWidget: Nouvelle session créée")
        except Exception as e:
            print(f"Erreur lors de la création de la session: {e}")
            self.db = None
