from PyQt6.QtWidgets import QLabel
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import Q<PERSON>ainter, QColor, QPainterPath

class RoleBadge(QLabel):
    """Badge personnalisé pour afficher le rôle d'un utilisateur"""
    
    ROLE_COLORS = {
        "admin": "#FF4560",      # Rouge
        "manager": "#008FFB",    # <PERSON><PERSON>u
        "technician": "#00E396", # Vert
        "sales": "#FEB019",      # Orange
        "inventory": "#775DD0"   # Violet
    }

    def __init__(self, role_name: str, parent=None):
        super().__init__(parent)
        self.role_name = role_name.lower()
        self.bg_color = QColor(self.ROLE_COLORS.get(self.role_name, "#999999"))
        
        # Configuration du style
        self.setText(role_name.upper())
        self.setFixedHeight(24)
        self.setMinimumWidth(80)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet(f"""
            QLabel {{
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: bold;
            }}
        """)

    def paintEvent(self, event):
        """Surcharge pour dessiner le fond arrondi"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Création du chemin pour le fond arrondi
        path = QPainterPath()
        path.addRoundedRect(0, 0, self.width(), self.height(), 12, 12)

        # Remplissage avec la couleur du rôle
        painter.fillPath(path, self.bg_color)

        # Appel de la méthode parente pour dessiner le texte
        super().paintEvent(event)

    def sizeHint(self):
        """Suggestion de taille par défaut"""
        return QSize(80, 24)