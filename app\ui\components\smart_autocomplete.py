"""
Widgets d'auto-complétion intelligente pour marques et modèles
Utilise la base de connaissances évolutive
"""

from PyQt6.QtWidgets import (
    QLineEdit, QCompleter, QListWidget, QListWidgetItem,
    QVBoxLayout, QWidget, QLabel, QHBoxLayout
)
from PyQt6.QtCore import Qt, QStringListModel, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QKeyEvent
from typing import List, Optional, Callable
import asyncio


class SmartAutoCompleteLineEdit(QLineEdit):
    """
    QLineEdit avec auto-complétion intelligente inline (validation par Tab)
    """

    # Signaux
    suggestion_selected = pyqtSignal(str, dict)  # texte, données
    learning_requested = pyqtSignal(str)  # texte à apprendre

    def __init__(self, service, suggestion_type: str = "brand", parent=None):
        super().__init__(parent)
        self.service = service
        self.suggestion_type = suggestion_type  # "brand" ou "model"
        self.brand_id = None  # Pour les modèles
        self.suggestions = []
        self.current_suggestions = []
        self.current_suggestion_index = -1
        self.original_text = ""
        self.suggestion_text = ""
        self.is_completing = False

        # Timer pour éviter trop de requêtes
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._perform_search)

        # Configuration
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """Configure l'interface"""
        self.setPlaceholderText(
            "Tapez une marque (Tab pour compléter)..." if self.suggestion_type == "brand"
            else "Tapez un modèle (Tab pour compléter)..."
        )

        # Style pour indiquer l'auto-complétion inline
        self.setStyleSheet("""
            QLineEdit {
                border: 2px solid #3498db;
                border-radius: 5px;
                padding: 5px;
                font-size: 12px;
                font-family: monospace;
            }
            QLineEdit:focus {
                border-color: #2980b9;
                background-color: #f8f9fa;
            }
        """)
    
    def setup_connections(self):
        """Configure les connexions"""
        self.textChanged.connect(self._on_text_changed)
        self.editingFinished.connect(self._on_editing_finished)

    def keyPressEvent(self, event: QKeyEvent):
        """Gestion des événements clavier pour l'auto-complétion inline"""
        key = event.key()

        if key == Qt.Key.Key_Tab:
            # Tab : accepter la suggestion actuelle ou passer au suivant
            if self.current_suggestions and not self.is_completing:
                self._accept_current_suggestion()
                return
            # Si pas de suggestions et que c'est le champ marque,
            # s'assurer que le focus va au champ modèle s'il est activé
            if self.suggestion_type == "brand":
                parent_widget = self.parent()
                if hasattr(parent_widget, 'model_edit') and parent_widget.model_edit.isEnabled():
                    parent_widget.model_edit.setFocus()
                    return

        elif key == Qt.Key.Key_Escape:
            # Escape : annuler l'auto-complétion
            self._cancel_completion()
            return

        elif key == Qt.Key.Key_Up:
            # Flèche haut : suggestion précédente
            if self.current_suggestions:
                self._navigate_suggestions(-1)
                return

        elif key == Qt.Key.Key_Down:
            # Flèche bas : suggestion suivante
            if self.current_suggestions:
                self._navigate_suggestions(1)
                return

        elif key in (Qt.Key.Key_Return, Qt.Key.Key_Enter):
            # Entrée : accepter la suggestion ou valider
            if self.current_suggestions and not self.is_completing:
                self._accept_current_suggestion()
                return

        # Traitement normal pour les autres touches
        super().keyPressEvent(event)
    
    def set_brand_id(self, brand_id: int):
        """Définit l'ID de la marque pour la recherche de modèles"""
        self.brand_id = brand_id
        if self.suggestion_type == "model":
            self.clear()
            self.setEnabled(brand_id is not None)
            self._clear_suggestions()

    def get_suggestion_status(self) -> str:
        """Retourne le statut de la suggestion actuelle"""
        if self.current_suggestions:
            return f"{len(self.current_suggestions)} suggestion(s) - Tab pour compléter, ↑↓ pour naviguer"
        return "Tapez pour voir les suggestions"
    
    def _on_text_changed(self, text: str):
        """Appelé quand le texte change"""
        if not self.is_completing:
            self.original_text = text
            if len(text) >= 2:  # Commencer la recherche après 2 caractères
                self.search_timer.stop()
                self.search_timer.start(200)  # Délai de 200ms pour plus de réactivité
            else:
                self.search_timer.stop()
                self._clear_suggestions()
    
    def _perform_search(self):
        """Effectue la recherche d'auto-complétion"""
        text = self.original_text
        if not text or len(text) < 2:
            return

        try:
            if self.suggestion_type == "brand":
                suggestions = self.service.search_brands(text, 5)  # Moins de suggestions pour l'inline
            else:
                if self.brand_id:
                    suggestions = self.service.search_models(self.brand_id, text, 5)
                else:
                    suggestions = []

            self._update_suggestions(suggestions)

        except Exception as e:
            print(f"Erreur lors de la recherche: {e}")

    def _update_suggestions(self, suggestions: List):
        """Met à jour les suggestions d'auto-complétion inline"""
        self.current_suggestions = suggestions
        self.current_suggestion_index = -1

        if suggestions:
            # Afficher la première suggestion inline
            self._show_inline_suggestion(0)
        else:
            self._clear_suggestions()

    def _show_inline_suggestion(self, index: int):
        """Affiche une suggestion inline dans le champ"""
        if 0 <= index < len(self.current_suggestions):
            suggestion = self.current_suggestions[index]
            self.current_suggestion_index = index

            # Construire le texte avec la suggestion
            original_len = len(self.original_text)
            suggestion_text = suggestion.text

            if suggestion_text.lower().startswith(self.original_text.lower()):
                # Complétion directe
                completion = suggestion_text[original_len:]
                full_text = self.original_text + completion

                # Mettre à jour le texte avec sélection de la partie complétée
                self.is_completing = True
                self.setText(full_text)
                self.setSelection(original_len, len(completion))
                self.is_completing = False

                self.suggestion_text = suggestion_text
            else:
                # Suggestion de remplacement
                self.is_completing = True
                self.setText(suggestion_text)
                self.setSelection(len(self.original_text), len(suggestion_text) - len(self.original_text))
                self.is_completing = False

                self.suggestion_text = suggestion_text

    def _navigate_suggestions(self, direction: int):
        """Navigue dans les suggestions (direction: -1 pour haut, 1 pour bas)"""
        if not self.current_suggestions:
            return

        new_index = self.current_suggestion_index + direction

        if new_index < 0:
            # Revenir au texte original
            self.current_suggestion_index = -1
            self.is_completing = True
            self.setText(self.original_text)
            self.is_completing = False
            self.suggestion_text = ""
        elif new_index >= len(self.current_suggestions):
            # Aller à la première suggestion
            self._show_inline_suggestion(0)
        else:
            # Afficher la suggestion suivante/précédente
            self._show_inline_suggestion(new_index)

    def _accept_current_suggestion(self):
        """Accepte la suggestion actuelle"""
        if self.suggestion_text:
            suggestion = self.current_suggestions[self.current_suggestion_index]
            suggestion_data = {
                'type': suggestion.type,
                'brand_id': suggestion.brand_id,
                'model_id': suggestion.model_id,
                'score': suggestion.score
            }

            self.is_completing = True
            self.setText(self.suggestion_text)
            self.setCursorPosition(len(self.suggestion_text))
            self.is_completing = False

            self._clear_suggestions()
            self.suggestion_selected.emit(self.suggestion_text, suggestion_data)

    def _cancel_completion(self):
        """Annule l'auto-complétion en cours"""
        if self.original_text != self.text():
            self.is_completing = True
            self.setText(self.original_text)
            self.setCursorPosition(len(self.original_text))
            self.is_completing = False
        self._clear_suggestions()

    def _clear_suggestions(self):
        """Efface les suggestions"""
        self.current_suggestions = []
        self.current_suggestion_index = -1
        self.suggestion_text = ""
    

    
    def _on_editing_finished(self):
        """Appelé quand l'édition est terminée"""
        text = self.text().strip()
        if text and text != self.original_text:
            # Demander l'apprentissage de cette entrée
            self.learning_requested.emit(text)
        self._clear_suggestions()


class BrandModelWidget(QWidget):
    """
    Widget combiné pour la saisie intelligente de marque et modèle
    """
    
    # Signaux
    brand_model_changed = pyqtSignal(str, str)  # marque, modèle
    learning_completed = pyqtSignal(str, str)  # marque, modèle
    
    def __init__(self, service, parent=None):
        super().__init__(parent)
        self.service = service
        self.current_brand_id = None
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """Configure l'interface"""
        layout = QVBoxLayout(self)
        
        # Marque
        brand_layout = QHBoxLayout()
        brand_label = QLabel("Marque:")
        brand_label.setMinimumWidth(80)
        self.brand_edit = SmartAutoCompleteLineEdit(self.service, "brand")
        brand_layout.addWidget(brand_label)
        brand_layout.addWidget(self.brand_edit)
        layout.addLayout(brand_layout)
        
        # Modèle
        model_layout = QHBoxLayout()
        model_label = QLabel("Modèle:")
        model_label.setMinimumWidth(80)
        self.model_edit = SmartAutoCompleteLineEdit(self.service, "model")
        self.model_edit.setEnabled(False)  # Désactivé jusqu'à ce qu'une marque soit sélectionnée
        model_layout.addWidget(model_label)
        model_layout.addWidget(self.model_edit)
        layout.addLayout(model_layout)
        
        # Indicateur de confiance
        self.confidence_label = QLabel("")
        self.confidence_label.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        layout.addWidget(self.confidence_label)

        # Instructions d'utilisation
        instructions = QLabel("💡 Instructions: Tapez pour voir les suggestions, Tab pour compléter, ↑↓ pour naviguer, Esc pour annuler")
        instructions.setStyleSheet("color: #95a5a6; font-size: 9px; font-style: italic; margin-top: 5px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
    
    def setup_connections(self):
        """Configure les connexions"""
        self.brand_edit.suggestion_selected.connect(self._on_brand_selected)
        self.brand_edit.learning_requested.connect(self._on_brand_learning)
        self.brand_edit.textChanged.connect(self._on_brand_text_changed)

        self.model_edit.suggestion_selected.connect(self._on_model_selected)
        self.model_edit.learning_requested.connect(self._on_model_learning)
        self.model_edit.textChanged.connect(self._on_model_text_changed)

        # Configurer l'ordre de tabulation
        self.setTabOrder(self.brand_edit, self.model_edit)
    
    def _on_brand_selected(self, text: str, data: dict):
        """Appelé quand une marque est sélectionnée"""
        self.current_brand_id = data.get('brand_id')
        self.model_edit.set_brand_id(self.current_brand_id)
        self.model_edit.setEnabled(True)
        self.model_edit.setFocus()
        self._update_confidence()
        self.brand_model_changed.emit(text, self.model_edit.text())
    
    def _on_model_selected(self, text: str, data: dict):
        """Appelé quand un modèle est sélectionné"""
        self._update_confidence()
        self.brand_model_changed.emit(self.brand_edit.text(), text)
    
    def _on_brand_learning(self, text: str):
        """Apprentissage d'une nouvelle marque"""
        try:
            result = self.service.learn_from_input(text, "")
            self.current_brand_id = result.brand_id
            self.model_edit.set_brand_id(self.current_brand_id)
            self.model_edit.setEnabled(True)
            self._update_confidence()
            self.learning_completed.emit(result.brand, "")
        except Exception as e:
            print(f"Erreur lors de l'apprentissage de la marque: {e}")
    
    def _on_model_learning(self, text: str):
        """Apprentissage d'un nouveau modèle"""
        brand_text = self.brand_edit.text().strip()
        if brand_text and text:
            try:
                result = self.service.learn_from_input(brand_text, text)
                self._update_confidence()
                self.learning_completed.emit(result.brand, result.model)
            except Exception as e:
                print(f"Erreur lors de l'apprentissage du modèle: {e}")
    
    def _on_brand_text_changed(self, text: str):
        """Appelé quand le texte de la marque change"""
        if not text:
            # Pas de texte : désactiver le champ modèle
            self.current_brand_id = None
            self.model_edit.set_brand_id(None)
            self.model_edit.clear()
            self.model_edit.setEnabled(False)
        else:
            # Il y a du texte : activer le champ modèle pour permettre la saisie
            self.model_edit.setEnabled(True)
        self._update_confidence()
    
    def _on_model_text_changed(self, text: str):
        """Appelé quand le texte du modèle change"""
        self._update_confidence()
        self.brand_model_changed.emit(self.brand_edit.text(), text)
    
    def _update_confidence(self):
        """Met à jour l'indicateur de confiance"""
        brand_text = self.brand_edit.text().strip()
        model_text = self.model_edit.text().strip()
        
        if not brand_text:
            self.confidence_label.setText("")
            return
        
        try:
            validation = self.service.validate_brand_model_pair(brand_text, model_text)
            confidence_pct = int(validation.confidence * 100)
            
            if confidence_pct >= 90:
                color = "#27ae60"  # Vert
                icon = "✓"
            elif confidence_pct >= 70:
                color = "#f39c12"  # Orange
                icon = "⚠"
            else:
                color = "#e74c3c"  # Rouge
                icon = "?"
            
            self.confidence_label.setText(
                f'<span style="color: {color};">{icon} Confiance: {confidence_pct}%</span>'
            )
            
        except Exception as e:
            self.confidence_label.setText(f'<span style="color: #e74c3c;">Erreur: {str(e)}</span>')
    
    def get_brand_model(self) -> tuple:
        """Retourne la paire marque-modèle actuelle"""
        return self.brand_edit.text().strip(), self.model_edit.text().strip()
    
    def set_brand_model(self, brand: str, model: str):
        """Définit la paire marque-modèle"""
        self.brand_edit.setText(brand)
        self.model_edit.setText(model)
        
        # Essayer de trouver l'ID de la marque
        if brand:
            suggestions = self.service.search_brands(brand, 1)
            if suggestions:
                self.current_brand_id = suggestions[0].brand_id
                self.model_edit.set_brand_id(self.current_brand_id)
                self.model_edit.setEnabled(True)
        
        self._update_confidence()
    
    def clear(self):
        """Vide les champs"""
        self.brand_edit.clear()
        self.model_edit.clear()
        self.current_brand_id = None
        self.model_edit.set_brand_id(None)
        self.confidence_label.setText("")
