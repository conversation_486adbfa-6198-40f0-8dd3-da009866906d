"""
Widget de sélection de thème avec prévisualisation
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QButtonGroup, QRadioButton, QFrame, QGroupBox, QGridLayout,
    QScrollArea, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QPixmap, QPainter, QColor, QFont

from app.ui.theme.theme_manager import ThemeManager

class ThemePreviewCard(QFrame):
    """Carte de prévisualisation d'un thème"""
    
    clicked = pyqtSignal(str)
    
    def __init__(self, theme_name: str, theme_manager: ThemeManager, parent=None):
        super().__init__(parent)
        self.theme_name = theme_name
        self.theme_manager = theme_manager
        self.is_selected = False
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface de la carte"""
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        self.setFixedSize(200, 150)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        
        # Titre du thème
        title_label = QLabel(self.theme_name.title())
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # Zone de prévisualisation
        preview_frame = QFrame()
        preview_frame.setFixedHeight(80)
        preview_layout = QVBoxLayout(preview_frame)
        preview_layout.setSpacing(2)
        
        # Simuler des éléments d'interface
        for i in range(3):
            element = QFrame()
            element.setFixedHeight(20)
            element.setFrameStyle(QFrame.Shape.StyledPanel)
            preview_layout.addWidget(element)
        
        layout.addWidget(preview_frame)
        
        # Bouton de sélection
        self.select_button = QPushButton("Sélectionner")
        self.select_button.clicked.connect(lambda: self.clicked.emit(self.theme_name))
        layout.addWidget(self.select_button)
        
        # Appliquer le style initial
        self.update_style()
    
    def update_style(self):
        """Met à jour le style de la carte"""
        if self.theme_name == "dark":
            # Style pour thème sombre
            self.setStyleSheet("""
                ThemePreviewCard {
                    background-color: #1E1E1E;
                    border: 2px solid #333333;
                    border-radius: 8px;
                    color: #FFFFFF;
                }
                ThemePreviewCard:hover {
                    border-color: #2196F3;
                }
                QLabel {
                    color: #FFFFFF;
                }
                QFrame {
                    background-color: #121212;
                    border: 1px solid #333333;
                }
                QPushButton {
                    background-color: #2196F3;
                    color: #FFFFFF;
                    border: none;
                    border-radius: 4px;
                    padding: 6px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)
        else:
            # Style pour thème clair
            self.setStyleSheet("""
                ThemePreviewCard {
                    background-color: #FFFFFF;
                    border: 2px solid #E0E0E0;
                    border-radius: 8px;
                    color: #000000;
                }
                ThemePreviewCard:hover {
                    border-color: #2196F3;
                }
                QLabel {
                    color: #000000;
                }
                QFrame {
                    background-color: #F5F5F5;
                    border: 1px solid #E0E0E0;
                }
                QPushButton {
                    background-color: #2196F3;
                    color: #FFFFFF;
                    border: none;
                    border-radius: 4px;
                    padding: 6px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)
    
    def set_selected(self, selected: bool):
        """Définit l'état de sélection"""
        self.is_selected = selected
        if selected:
            self.setStyleSheet(self.styleSheet() + """
                ThemePreviewCard {
                    border-color: #4CAF50;
                    border-width: 3px;
                }
            """)
            self.select_button.setText("✓ Sélectionné")
            self.select_button.setEnabled(False)
        else:
            self.update_style()
            self.select_button.setText("Sélectionner")
            self.select_button.setEnabled(True)
    
    def mousePressEvent(self, event):
        """Gère le clic sur la carte"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.theme_name)
        super().mousePressEvent(event)

class ThemeSelector(QWidget):
    """Widget de sélection de thème"""
    
    themeChanged = pyqtSignal(str)
    
    def __init__(self, theme_manager: ThemeManager = None, parent=None):
        super().__init__(parent)
        self.theme_manager = theme_manager or ThemeManager()
        self.preview_cards = {}
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # Titre
        title_label = QLabel("Sélection du Thème")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel("Choisissez le thème qui vous convient le mieux")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #666666; font-size: 14px;")
        main_layout.addWidget(desc_label)
        
        # Zone de sélection rapide
        quick_select_group = QGroupBox("Sélection Rapide")
        quick_layout = QHBoxLayout(quick_select_group)
        
        self.light_radio = QRadioButton("Thème Clair")
        self.dark_radio = QRadioButton("Thème Sombre")
        self.auto_radio = QRadioButton("Automatique (Système)")
        
        self.theme_radio_group = QButtonGroup()
        self.theme_radio_group.addButton(self.light_radio, 0)
        self.theme_radio_group.addButton(self.dark_radio, 1)
        self.theme_radio_group.addButton(self.auto_radio, 2)
        
        quick_layout.addWidget(self.light_radio)
        quick_layout.addWidget(self.dark_radio)
        quick_layout.addWidget(self.auto_radio)
        quick_layout.addStretch()
        
        main_layout.addWidget(quick_select_group)
        
        # Zone de prévisualisation
        preview_group = QGroupBox("Prévisualisation des Thèmes")
        preview_layout = QHBoxLayout(preview_group)
        preview_layout.setSpacing(20)
        
        # Créer les cartes de prévisualisation
        for theme_name in self.theme_manager.get_available_themes():
            card = ThemePreviewCard(theme_name, self.theme_manager)
            card.clicked.connect(self.on_theme_selected)
            self.preview_cards[theme_name] = card
            preview_layout.addWidget(card)
        
        preview_layout.addStretch()
        main_layout.addWidget(preview_group)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        self.apply_button = QPushButton("Appliquer")
        self.apply_button.setIcon(QIcon("app/ui/resources/icons/check.svg"))
        self.apply_button.clicked.connect(self.apply_theme)
        
        self.reset_button = QPushButton("Réinitialiser")
        self.reset_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.reset_button.clicked.connect(self.reset_theme)
        
        self.preview_button = QPushButton("Aperçu")
        self.preview_button.setIcon(QIcon("app/ui/resources/icons/eye.svg"))
        self.preview_button.clicked.connect(self.preview_theme)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.preview_button)
        buttons_layout.addWidget(self.reset_button)
        buttons_layout.addWidget(self.apply_button)
        
        main_layout.addLayout(buttons_layout)
        
        # Mettre à jour l'état initial
        self.update_selection()
    
    def connect_signals(self):
        """Connecte les signaux"""
        self.theme_radio_group.buttonClicked.connect(self.on_radio_changed)
        self.theme_manager.themeChanged.connect(self.on_theme_manager_changed)
    
    def on_radio_changed(self, button):
        """Gère le changement de sélection radio"""
        if button == self.light_radio:
            self.select_theme("light")
        elif button == self.dark_radio:
            self.select_theme("dark")
        elif button == self.auto_radio:
            # Détecter le thème système (simplifié)
            self.select_theme("dark")  # Par défaut sombre pour l'auto
    
    def on_theme_selected(self, theme_name: str):
        """Gère la sélection d'un thème via les cartes"""
        self.select_theme(theme_name)
    
    def select_theme(self, theme_name: str):
        """Sélectionne un thème"""
        if theme_name in self.preview_cards:
            # Mettre à jour les cartes
            for name, card in self.preview_cards.items():
                card.set_selected(name == theme_name)
            
            # Mettre à jour les boutons radio
            if theme_name == "light":
                self.light_radio.setChecked(True)
            elif theme_name == "dark":
                self.dark_radio.setChecked(True)
            
            # Émettre le signal
            self.themeChanged.emit(theme_name)
    
    def apply_theme(self):
        """Applique le thème sélectionné"""
        for theme_name, card in self.preview_cards.items():
            if card.is_selected:
                self.theme_manager.switch_theme(theme_name)
                break
    
    def preview_theme(self):
        """Prévisualise temporairement le thème sélectionné"""
        for theme_name, card in self.preview_cards.items():
            if card.is_selected:
                # Appliquer temporairement
                app = QApplication.instance()
                if app:
                    stylesheet = self.theme_manager.get_all_stylesheets(theme_name)
                    app.setStyleSheet(stylesheet)
                
                # Revenir au thème original après 3 secondes
                QTimer.singleShot(3000, lambda: self.theme_manager.apply_theme_to_app())
                break
    
    def reset_theme(self):
        """Remet le thème par défaut"""
        self.theme_manager.switch_theme("light")
        self.update_selection()
    
    def update_selection(self):
        """Met à jour la sélection actuelle"""
        current_theme = self.theme_manager.current_theme
        self.select_theme(current_theme)
    
    def on_theme_manager_changed(self, theme_name: str):
        """Gère le changement de thème du gestionnaire"""
        self.update_selection()

# Instance globale du gestionnaire de thèmes
theme_manager = ThemeManager()
