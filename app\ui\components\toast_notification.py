"""
Système de notifications toast temporaires basé sur l'event bus.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGraphicsOpacityEffect, QFrame, QApplication
)
from PyQt6.QtCore import (
    Qt, QTimer, QPropertyAnimation, QEasingCurve, 
    pyqtSignal, QRect, QPoint, QSize
)
from PyQt6.QtGui import QFont, QPalette, QColor, QPainter, QBrush
from enum import Enum
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class ToastType(Enum):
    """Types de notifications toast"""
    SUCCESS = "success"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


class ToastNotification(QFrame):
    """Widget de notification toast temporaire"""
    
    # Signal émis quand la notification est fermée
    closed = pyqtSignal()
    
    def __init__(self, message: str, toast_type: ToastType = ToastType.INFO, 
                 duration: int = 3000, parent=None):
        """
        Initialise une notification toast
        
        Args:
            message: Message à afficher
            toast_type: Type de notification
            duration: Durée d'affichage en millisecondes
            parent: Widget parent
        """
        super().__init__(parent)
        
        self.message = message
        self.toast_type = toast_type
        self.duration = duration
        
        # Configuration du widget
        self.setFixedSize(350, 80)
        self.setFrameStyle(QFrame.Shape.Box)
        self.setLineWidth(1)
        
        # Effet d'opacité pour les animations
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        # Configuration de l'interface
        self.setup_ui()
        self.apply_style()
        
        # Timer pour la fermeture automatique
        self.close_timer = QTimer()
        self.close_timer.timeout.connect(self.start_fade_out)
        
        # Animations
        self.fade_in_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_out_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_out_animation.finished.connect(self.close)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(10)
        
        # Icône selon le type
        icon_label = QLabel()
        icon_label.setFixedSize(24, 24)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setText(self._get_icon())
        icon_label.setObjectName("toastIcon")
        layout.addWidget(icon_label)
        
        # Message
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setObjectName("toastMessage")
        layout.addWidget(message_label, 1)
        
        # Bouton de fermeture
        close_button = QPushButton("×")
        close_button.setFixedSize(20, 20)
        close_button.setObjectName("toastCloseButton")
        close_button.clicked.connect(self.start_fade_out)
        layout.addWidget(close_button)
    
    def _get_icon(self) -> str:
        """Retourne l'icône selon le type"""
        icons = {
            ToastType.SUCCESS: "✓",
            ToastType.INFO: "ℹ",
            ToastType.WARNING: "⚠",
            ToastType.ERROR: "✗"
        }
        return icons.get(self.toast_type, "ℹ")
    
    def apply_style(self):
        """Applique le style selon le type"""
        colors = {
            ToastType.SUCCESS: ("#d4edda", "#155724", "#c3e6cb"),
            ToastType.INFO: ("#d1ecf1", "#0c5460", "#bee5eb"),
            ToastType.WARNING: ("#fff3cd", "#856404", "#ffeaa7"),
            ToastType.ERROR: ("#f8d7da", "#721c24", "#f5c6cb")
        }
        
        bg_color, text_color, border_color = colors.get(
            self.toast_type, 
            colors[ToastType.INFO]
        )
        
        self.setStyleSheet(f"""
            ToastNotification {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 8px;
                color: {text_color};
            }}
            
            #toastIcon {{
                font-size: 16px;
                font-weight: bold;
                color: {text_color};
            }}
            
            #toastMessage {{
                font-size: 13px;
                color: {text_color};
            }}
            
            #toastCloseButton {{
                background-color: transparent;
                border: none;
                font-size: 16px;
                font-weight: bold;
                color: {text_color};
                border-radius: 10px;
            }}
            
            #toastCloseButton:hover {{
                background-color: rgba(0, 0, 0, 0.1);
            }}
        """)
    
    def show_animated(self):
        """Affiche la notification avec animation"""
        self.show()
        
        # Animation d'apparition
        self.fade_in_animation.setDuration(300)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.fade_in_animation.start()
        
        # Démarrer le timer de fermeture
        if self.duration > 0:
            self.close_timer.start(self.duration)
    
    def start_fade_out(self):
        """Démarre l'animation de disparition"""
        self.close_timer.stop()
        
        self.fade_out_animation.setDuration(300)
        self.fade_out_animation.setStartValue(1.0)
        self.fade_out_animation.setEndValue(0.0)
        self.fade_out_animation.setEasingCurve(QEasingCurve.Type.InCubic)
        self.fade_out_animation.start()
    
    def close(self):
        """Ferme la notification"""
        super().close()
        self.closed.emit()


class ToastManager(QWidget):
    """Gestionnaire des notifications toast"""
    
    def __init__(self, parent=None):
        """
        Initialise le gestionnaire de toast
        
        Args:
            parent: Widget parent (généralement la fenêtre principale)
        """
        super().__init__(parent)
        
        self.parent_widget = parent
        self.active_toasts = []
        self.max_toasts = 5
        self.toast_spacing = 10
        
        # Configuration du widget
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, False)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating, True)
        
        # Layout vertical pour empiler les toasts
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(self.toast_spacing)
        self.layout.addStretch()
        
        # Positionner le gestionnaire
        self.update_position()
    
    def show_toast(self, message: str, toast_type: ToastType = ToastType.INFO, 
                   duration: int = 3000) -> ToastNotification:
        """
        Affiche une nouvelle notification toast
        
        Args:
            message: Message à afficher
            toast_type: Type de notification
            duration: Durée d'affichage en millisecondes
            
        Returns:
            La notification créée
        """
        # Limiter le nombre de toasts actifs
        while len(self.active_toasts) >= self.max_toasts:
            oldest_toast = self.active_toasts[0]
            oldest_toast.start_fade_out()
        
        # Créer la nouvelle notification
        toast = ToastNotification(message, toast_type, duration, self)
        toast.closed.connect(lambda: self._remove_toast(toast))
        
        # Ajouter au layout (avant le stretch)
        self.layout.insertWidget(self.layout.count() - 1, toast)
        self.active_toasts.append(toast)
        
        # Afficher avec animation
        toast.show_animated()
        
        # Mettre à jour la position et la taille
        self.update_geometry()
        
        logger.debug(f"Toast affiché: {message} ({toast_type.value})")
        return toast
    
    def _remove_toast(self, toast: ToastNotification):
        """Supprime une notification de la liste"""
        if toast in self.active_toasts:
            self.active_toasts.remove(toast)
            self.layout.removeWidget(toast)
            toast.deleteLater()
            
            # Mettre à jour la géométrie
            self.update_geometry()
    
    def update_position(self):
        """Met à jour la position du gestionnaire"""
        if not self.parent_widget:
            return
        
        parent_rect = self.parent_widget.geometry()
        
        # Positionner en haut à droite avec une marge
        margin = 20
        self.move(
            parent_rect.right() - self.width() - margin,
            parent_rect.top() + margin
        )
    
    def update_geometry(self):
        """Met à jour la géométrie du gestionnaire"""
        if not self.active_toasts:
            self.hide()
            return
        
        # Calculer la hauteur nécessaire
        total_height = 0
        for toast in self.active_toasts:
            total_height += toast.height() + self.toast_spacing
        
        # Ajuster la taille
        max_width = max(toast.width() for toast in self.active_toasts) if self.active_toasts else 350
        self.setFixedSize(max_width, total_height)
        
        # Mettre à jour la position
        self.update_position()
        
        # Afficher si nécessaire
        if not self.isVisible():
            self.show()
    
    def clear_all(self):
        """Ferme toutes les notifications"""
        for toast in self.active_toasts.copy():
            toast.start_fade_out()


# Instance globale du gestionnaire de toast
_toast_manager = None


def get_toast_manager(parent=None) -> ToastManager:
    """
    Retourne l'instance globale du gestionnaire de toast
    
    Args:
        parent: Widget parent pour créer le gestionnaire si nécessaire
        
    Returns:
        Instance du gestionnaire de toast
    """
    global _toast_manager
    
    if _toast_manager is None and parent is not None:
        _toast_manager = ToastManager(parent)
    
    return _toast_manager


def show_success(message: str, duration: int = 3000):
    """Affiche une notification de succès"""
    manager = get_toast_manager()
    if manager:
        manager.show_toast(message, ToastType.SUCCESS, duration)


def show_info(message: str, duration: int = 3000):
    """Affiche une notification d'information"""
    manager = get_toast_manager()
    if manager:
        manager.show_toast(message, ToastType.INFO, duration)


def show_warning(message: str, duration: int = 4000):
    """Affiche une notification d'avertissement"""
    manager = get_toast_manager()
    if manager:
        manager.show_toast(message, ToastType.WARNING, duration)


def show_error(message: str, duration: int = 5000):
    """Affiche une notification d'erreur"""
    manager = get_toast_manager()
    if manager:
        manager.show_toast(message, ToastType.ERROR, duration)
