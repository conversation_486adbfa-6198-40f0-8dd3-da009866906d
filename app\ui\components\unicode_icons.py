"""
Module contenant des icônes Unicode pour l'application.
Ces icônes sont utilisées comme solution de secours lorsque les icônes SVG ne sont pas disponibles.
"""

class UnicodeIcons:
    """Classe contenant des icônes Unicode pour l'application."""

    # Icônes de navigation
    DASHBOARD = "📊"
    INVENTORY = "📦"
    REPAIR = "🔧"
    CUSTOMER = "👥"
    SUPPLIER = "🏭"
    PURCHASE = "🛒"
    EQUIPMENT = "⚙️"
    FINANCE = "💰"
    SALE = "💵"
    TREASURY = "💼"
    CHART = "📈"
    USER = "👤"
    SETTINGS = "⚙️"

    # Icônes d'action
    ADD = "➕"
    EDIT = "✏️"
    DELETE = "🗑️"
    SEARCH = "🔍"
    PRINT = "🖨️"
    EXPORT = "📤"
    IMPORT = "📥"
    REFRESH = "🔄"

    # Icônes de statut
    SUCCESS = "✅"
    ERROR = "❌"
    WARNING = "⚠️"
    INFO = "ℹ️"

    # Icônes de notification
    NOTIFICATION = "🔔"  # Alternative: "N"
    NOTIFICATION_UNREAD = "🔔"  # Alternative: "N"
    NOTIFICATION_READ = "🔕"  # Alternative: "n"

    # Icônes de thème
    LIGHT_MODE = "☀️"  # Alternative: "L"
    DARK_MODE = "🌙"  # Alternative: "D"

    # Icônes alternatives (plus petites)
    SETTINGS_ALT = "S"
    NOTIFICATION_ALT = "N"

    @staticmethod
    def get_icon(name):
        """Retourne l'icône Unicode correspondant au nom."""
        if hasattr(UnicodeIcons, name.upper()):
            return getattr(UnicodeIcons, name.upper())
        return "❓"  # Icône par défaut si le nom n'existe pas
