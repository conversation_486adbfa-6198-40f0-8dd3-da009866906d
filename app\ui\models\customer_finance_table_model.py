"""
Modèles de table pour la gestion financière des clients.
"""
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant, QDate
from PyQt6.QtGui import QColor, QBrush
from datetime import datetime, date

from app.core.models.customer import CustomerTransaction
from app.core.models.sale import Sale, PaymentStatus

class CustomerTransactionTableModel(QAbstractTableModel):
    """Modèle de table pour les transactions des clients"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.transactions = []
        self.filtered_transactions = []
        self.headers = [
            "Date", "Montant", "Description", "Référence", "Réparation", "Vente", "Traité par"
        ]
        
        # Filtres
        self.customer_filter = None
        self.date_filter = None
        
    def set_data(self, transactions):
        """Définit les données du modèle"""
        self.transactions = transactions
        self.apply_filters()
        
    def apply_filters(self):
        """Applique les filtres aux données"""
        # Commencer avec toutes les transactions
        filtered = self.transactions
        
        # Filtrer par client
        if self.customer_filter is not None:
            filtered = [t for t in filtered if t.customer_id == self.customer_filter]
            
        # Filtrer par date
        if self.date_filter is not None:
            filtered = [t for t in filtered if t.transaction_date.date() == self.date_filter]
            
        # Mettre à jour les transactions filtrées
        self.filtered_transactions = filtered
        
        # Notifier le modèle que les données ont changé
        self.layoutChanged.emit()
        
    def filter_by_customer(self, customer_id):
        """Filtre les transactions par client"""
        self.customer_filter = customer_id
        self.apply_filters()
        
    def filter_by_date(self, date_filter):
        """Filtre les transactions par date"""
        self.date_filter = date_filter
        self.apply_filters()
        
    def rowCount(self, parent=QModelIndex()):
        """Retourne le nombre de lignes"""
        return len(self.filtered_transactions)
        
    def columnCount(self, parent=QModelIndex()):
        """Retourne le nombre de colonnes"""
        return len(self.headers)
        
    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        """Retourne les en-têtes du tableau"""
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return QVariant()
        
    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        """Retourne les données à afficher"""
        if not index.isValid() or index.row() >= len(self.filtered_transactions):
            return QVariant()
            
        transaction = self.filtered_transactions[index.row()]
        column = index.column()
        
        if role == Qt.ItemDataRole.DisplayRole:
            # Données à afficher
            if column == 0:  # Date
                return transaction.transaction_date.strftime("%d/%m/%Y %H:%M")
            elif column == 1:  # Montant
                return f"{transaction.amount:.2f} DA"
            elif column == 2:  # Description
                return transaction.description or ""
            elif column == 3:  # Référence
                return transaction.reference_number or ""
            elif column == 4:  # Réparation
                return str(transaction.repair_order_id or "")
            elif column == 5:  # Vente
                return str(transaction.sale_id or "")
            elif column == 6:  # Traité par
                return str(transaction.processed_by or "")
                
        elif role == Qt.ItemDataRole.BackgroundRole:
            # Couleur de fond en fonction du montant
            if column == 1:  # Montant
                if transaction.amount > 0:
                    return QBrush(QColor("#d4edda"))  # Vert clair
                elif transaction.amount < 0:
                    return QBrush(QColor("#f8d7da"))  # Rouge clair
                    
        elif role == Qt.ItemDataRole.ForegroundRole:
            # Couleur du texte en fonction du montant
            if column == 1:  # Montant
                if transaction.amount > 0:
                    return QBrush(QColor("#155724"))  # Vert foncé
                elif transaction.amount < 0:
                    return QBrush(QColor("#721c24"))  # Rouge foncé
                    
        elif role == Qt.ItemDataRole.TextAlignmentRole:
            # Alignement du texte
            if column == 1:  # Montant
                return Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter
                
        return QVariant()
        
    def get_transaction_at_row(self, row):
        """Retourne la transaction à la ligne spécifiée"""
        if 0 <= row < len(self.filtered_transactions):
            return self.filtered_transactions[row]
        return None
        
    def get_id_at_row(self, row):
        """Retourne l'ID de la transaction à la ligne spécifiée"""
        transaction = self.get_transaction_at_row(row)
        if transaction:
            return transaction.id
        return None


class CustomerSaleTableModel(QAbstractTableModel):
    """Modèle de table pour les ventes des clients"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.sales = []
        self.filtered_sales = []
        self.headers = [
            "Numéro", "Date", "Montant", "Payé", "Reste", "Statut", "Échéance"
        ]
        
        # Filtres
        self.customer_filter = None
        self.payment_status_filter = None
        
    def set_data(self, sales):
        """Définit les données du modèle"""
        self.sales = sales
        self.apply_filters()
        
    def apply_filters(self):
        """Applique les filtres aux données"""
        # Commencer avec toutes les ventes
        filtered = self.sales
        
        # Filtrer par client
        if self.customer_filter is not None:
            filtered = [s for s in filtered if s.customer_id == self.customer_filter]
            
        # Filtrer par statut de paiement
        if self.payment_status_filter is not None:
            filtered = [s for s in filtered if s.payment_status.value == self.payment_status_filter]
            
        # Mettre à jour les ventes filtrées
        self.filtered_sales = filtered
        
        # Notifier le modèle que les données ont changé
        self.layoutChanged.emit()
        
    def filter_by_customer(self, customer_id):
        """Filtre les ventes par client"""
        self.customer_filter = customer_id
        self.apply_filters()
        
    def filter_by_payment_status(self, status):
        """Filtre les ventes par statut de paiement"""
        self.payment_status_filter = status
        self.apply_filters()
        
    def clear_payment_status_filter(self):
        """Efface le filtre de statut de paiement"""
        self.payment_status_filter = None
        self.apply_filters()
        
    def rowCount(self, parent=QModelIndex()):
        """Retourne le nombre de lignes"""
        return len(self.filtered_sales)
        
    def columnCount(self, parent=QModelIndex()):
        """Retourne le nombre de colonnes"""
        return len(self.headers)
        
    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        """Retourne les en-têtes du tableau"""
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return QVariant()
        
    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        """Retourne les données à afficher"""
        if not index.isValid() or index.row() >= len(self.filtered_sales):
            return QVariant()
            
        sale = self.filtered_sales[index.row()]
        column = index.column()
        
        if role == Qt.ItemDataRole.DisplayRole:
            # Données à afficher
            if column == 0:  # Numéro
                return sale.number
            elif column == 1:  # Date
                return sale.date.strftime("%d/%m/%Y %H:%M")
            elif column == 2:  # Montant
                return f"{sale.final_amount:.2f} DA"
            elif column == 3:  # Payé
                return f"{sale.total_paid:.2f} DA"
            elif column == 4:  # Reste
                return f"{(sale.final_amount - sale.total_paid):.2f} DA"
            elif column == 5:  # Statut
                return sale.payment_status.value.capitalize()
            elif column == 6:  # Échéance
                if sale.due_date:
                    return sale.due_date.strftime("%d/%m/%Y")
                return ""
                
        elif role == Qt.ItemDataRole.BackgroundRole:
            # Couleur de fond en fonction du statut de paiement
            if sale.payment_status == PaymentStatus.PAID:
                return QBrush(QColor("#d4edda"))  # Vert clair
            elif sale.payment_status == PaymentStatus.PARTIAL:
                return QBrush(QColor("#fff3cd"))  # Jaune clair
            elif sale.payment_status == PaymentStatus.PENDING:
                return QBrush(QColor("#f8f9fa"))  # Gris clair
            elif sale.payment_status == PaymentStatus.OVERDUE:
                return QBrush(QColor("#f8d7da"))  # Rouge clair
                
        elif role == Qt.ItemDataRole.ForegroundRole:
            # Couleur du texte pour le statut
            if column == 5:
                if sale.payment_status == PaymentStatus.PAID:
                    return QBrush(QColor("#155724"))  # Vert foncé
                elif sale.payment_status == PaymentStatus.PARTIAL:
                    return QBrush(QColor("#856404"))  # Jaune foncé
                elif sale.payment_status == PaymentStatus.PENDING:
                    return QBrush(QColor("#383d41"))  # Gris foncé
                elif sale.payment_status == PaymentStatus.OVERDUE:
                    return QBrush(QColor("#721c24"))  # Rouge foncé
                    
        elif role == Qt.ItemDataRole.TextAlignmentRole:
            # Alignement du texte
            if column in [2, 3, 4]:  # Montants
                return Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter
                
        return QVariant()
        
    def get_sale_at_row(self, row):
        """Retourne la vente à la ligne spécifiée"""
        if 0 <= row < len(self.filtered_sales):
            return self.filtered_sales[row]
        return None
        
    def get_id_at_row(self, row):
        """Retourne l'ID de la vente à la ligne spécifiée"""
        sale = self.get_sale_at_row(row)
        if sale:
            return sale.id
        return None
