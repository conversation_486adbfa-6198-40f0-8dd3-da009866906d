"""
Modèle de table pour les réparations des clients.
"""
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant
from PyQt6.QtGui import QColor
from typing import List, Optional, Any
from datetime import datetime

from app.core.models.repair import RepairOrder, RepairStatus, PaymentStatus


class CustomerRepairTableModel(QAbstractTableModel):
    """Modèle de table pour les réparations des clients"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.repairs = []
        self.filtered_repairs = []
        self.customer_filter = None
        self.status_filter = None
        self.payment_status_filter = None
        
        self.headers = [
            "N° Réparation",
            "Date",
            "Équipement",
            "Statut",
            "Priorité",
            "Montant",
            "Statut Paiement",
            "Technicien"
        ]

    def set_data(self, repairs: List[RepairOrder]):
        """Définit les données du modèle"""
        self.beginResetModel()
        self.repairs = repairs
        self.filtered_repairs = repairs.copy()
        self.endResetModel()
        
    def apply_filters(self):
        """Applique les filtres aux données"""
        # Commencer avec toutes les réparations
        filtered = self.repairs
        
        # Filtrer par client
        if self.customer_filter is not None:
            filtered = [r for r in filtered if r.customer_id == self.customer_filter]
            
        # Filtrer par statut
        if self.status_filter is not None:
            filtered = [r for r in filtered if r.status.value == self.status_filter]
            
        # Filtrer par statut de paiement
        if self.payment_status_filter is not None:
            filtered = [r for r in filtered if r.payment_status.value == self.payment_status_filter]
            
        # Mettre à jour les réparations filtrées
        self.filtered_repairs = filtered
        
        # Notifier le modèle que les données ont changé
        self.layoutChanged.emit()
        
    def filter_by_customer(self, customer_id):
        """Filtre les réparations par client"""
        self.customer_filter = customer_id
        self.apply_filters()
        
    def filter_by_status(self, status):
        """Filtre les réparations par statut"""
        self.status_filter = status
        self.apply_filters()
        
    def filter_by_payment_status(self, payment_status):
        """Filtre les réparations par statut de paiement"""
        self.payment_status_filter = payment_status
        self.apply_filters()
        
    def clear_status_filter(self):
        """Efface le filtre de statut"""
        self.status_filter = None
        self.apply_filters()
        
    def clear_payment_status_filter(self):
        """Efface le filtre de statut de paiement"""
        self.payment_status_filter = None
        self.apply_filters()
        
    def rowCount(self, parent=QModelIndex()):
        """Retourne le nombre de lignes"""
        return len(self.filtered_repairs)
        
    def columnCount(self, parent=QModelIndex()):
        """Retourne le nombre de colonnes"""
        return len(self.headers)
        
    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        """Retourne les en-têtes du tableau"""
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return QVariant()
        
    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        """Retourne les données pour l'affichage"""
        if not index.isValid() or index.row() >= len(self.filtered_repairs):
            return QVariant()
            
        repair = self.filtered_repairs[index.row()]
        
        if role == Qt.ItemDataRole.DisplayRole:
            column = index.column()
            if column == 0:  # N° Réparation
                return repair.number
            elif column == 1:  # Date
                return repair.created_at.strftime("%d/%m/%Y")
            elif column == 2:  # Équipement
                return f"{repair.brand} {repair.model}"
            elif column == 3:  # Statut
                return self.get_status_display(repair.status)
            elif column == 4:  # Priorité
                return self.get_priority_display(repair.priority)
            elif column == 5:  # Montant
                return f"{repair.final_amount:.2f} DA"
            elif column == 6:  # Statut Paiement
                return self.get_payment_status_display(repair.payment_status)
            elif column == 7:  # Technicien
                return repair.technician_name or "Non assigné"
                
        elif role == Qt.ItemDataRole.BackgroundRole:
            # Coloration selon le statut
            status = repair.status
            if status == RepairStatus.PENDING:
                return QColor(255, 255, 200)  # Jaune clair
            elif status == RepairStatus.IN_PROGRESS:
                return QColor(200, 255, 200)  # Vert clair
            elif status == RepairStatus.COMPLETED:
                return QColor(200, 200, 255)  # Bleu clair
            elif status == RepairStatus.INVOICED:
                return QColor(255, 200, 255)  # Rose clair
            elif status == RepairStatus.PAID:
                return QColor(200, 255, 255)  # Cyan clair
                
        elif role == Qt.ItemDataRole.UserRole:
            # Retourner l'objet réparation complet
            return repair
            
        return QVariant()
        
    def get_id_at_row(self, row):
        """Retourne l'ID de la réparation à la ligne spécifiée"""
        if 0 <= row < len(self.filtered_repairs):
            return self.filtered_repairs[row].id
        return None
        
    def get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_map = {
            RepairStatus.PENDING: "En attente",
            RepairStatus.DIAGNOSED: "Diagnostiqué",
            RepairStatus.IN_PROGRESS: "En cours",
            RepairStatus.COMPLETED: "Terminé",
            RepairStatus.INVOICED: "Facturé",
            RepairStatus.PAID: "Payé",
            RepairStatus.CANCELLED: "Annulé",
            RepairStatus.ON_HOLD: "En pause"
        }
        return status_map.get(status, str(status))
        
    def get_priority_display(self, priority):
        """Retourne l'affichage de la priorité"""
        priority_map = {
            "low": "Basse",
            "medium": "Moyenne",
            "high": "Haute",
            "urgent": "Urgente"
        }
        return priority_map.get(priority.value, str(priority))
        
    def get_payment_status_display(self, status):
        """Retourne l'affichage du statut de paiement"""
        status_map = {
            PaymentStatus.PENDING: "En attente",
            PaymentStatus.PARTIAL: "Partiel",
            PaymentStatus.PAID: "Payé",
            PaymentStatus.OVERDUE: "En retard"
        }
        return status_map.get(status, str(status))
