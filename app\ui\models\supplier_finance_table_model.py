"""
Modèles de table pour les finances fournisseurs.
"""
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QDate
from PyQt6.QtGui import QColor
from datetime import datetime

from app.core.models.supplier_finance import InvoiceStatus, PaymentMethod

class SupplierInvoiceTableModel(QAbstractTableModel):
    """Modèle de table pour les factures fournisseurs"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.invoices = []
        self.filtered_invoices = []
        self.headers = [
            "N° Facture", "Fournisseur", "Date", "Échéance",
            "Montant", "Statut", "Commande liée"
        ]

        # Couleurs pour les statuts
        self.status_colors = {
            InvoiceStatus.PENDING: QColor("#FFF9C4"),  # Jaune clair
            InvoiceStatus.PARTIAL: QColor("#FFE0B2"),  # Orange clair
            InvoiceStatus.PAID: QColor("#C8E6C9"),  # Vert clair
            InvoiceStatus.CANCELLED: QColor("#FFCDD2"),  # Rouge clair
            InvoiceStatus.DISPUTED: QColor("#E1BEE7")  # Violet clair
        }

    def rowCount(self, parent=QModelIndex()):
        return len(self.filtered_invoices)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return None

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.filtered_invoices):
            return None

        invoice = self.filtered_invoices[index.row()]

        if role == Qt.ItemDataRole.DisplayRole:
            col = index.column()
            if col == 0:  # N° Facture
                return invoice.invoice_number
            elif col == 1:  # Fournisseur
                return invoice.supplier.name if invoice.supplier else "N/A"
            elif col == 2:  # Date
                return invoice.invoice_date.strftime("%d/%m/%Y") if invoice.invoice_date else "N/A"
            elif col == 3:  # Échéance
                return invoice.due_date.strftime("%d/%m/%Y") if invoice.due_date else "N/A"
            elif col == 4:  # Montant
                # Vérifier si le montant total est nul ou zéro
                if invoice.total_amount is None or invoice.total_amount == 0:
                    # Si la facture est liée à une commande, utiliser le montant total de la commande
                    if invoice.purchase_order and hasattr(invoice.purchase_order, 'total_amount') and invoice.purchase_order.total_amount:
                        return f"{invoice.purchase_order.total_amount:.2f} {invoice.currency}"
                    # Sinon, afficher 0
                    return f"0.00 {invoice.currency}"
                return f"{invoice.total_amount:.2f} {invoice.currency}"
            elif col == 5:  # Statut
                return self._get_status_display(invoice.status)
            elif col == 6:  # Commande liée
                if invoice.purchase_order:
                    return invoice.purchase_order.po_number or f"#PO-{invoice.purchase_order.id}"
                return "Aucune"

        elif role == Qt.ItemDataRole.BackgroundRole:
            # Colorer la ligne en fonction du statut
            return self.status_colors.get(invoice.status, None)

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            col = index.column()
            if col in [2, 3, 4]:  # Date, Échéance, Montant
                return int(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            return int(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)

        return None

    def set_data(self, invoices):
        """Définit les données du modèle"""
        self.beginResetModel()
        self.invoices = invoices
        self.filtered_invoices = invoices
        self.endResetModel()

    def filter_by_supplier(self, supplier_id):
        """Filtre les factures par fournisseur"""
        self.beginResetModel()
        if supplier_id:
            self.filtered_invoices = [
                invoice for invoice in self.invoices
                if invoice.supplier_id == supplier_id
            ]
        else:
            self.filtered_invoices = self.invoices
        self.endResetModel()

    def filter_by_status(self, status):
        """Filtre les factures par statut"""
        self.beginResetModel()
        if status:
            self.filtered_invoices = [
                invoice for invoice in self.filtered_invoices
                if invoice.status.value == status
            ]
        self.endResetModel()

    def clear_status_filter(self):
        """Efface le filtre de statut"""
        self.beginResetModel()
        # Réappliquer uniquement le filtre de fournisseur
        supplier_id = None
        if self.filtered_invoices and self.invoices:
            # Détecter si un filtre de fournisseur est appliqué
            if len(self.filtered_invoices) < len(self.invoices):
                if self.filtered_invoices:
                    supplier_id = self.filtered_invoices[0].supplier_id

        if supplier_id:
            self.filtered_invoices = [
                invoice for invoice in self.invoices
                if invoice.supplier_id == supplier_id
            ]
        else:
            self.filtered_invoices = self.invoices
        self.endResetModel()

    def get_id_at_row(self, row):
        """Retourne l'ID de la facture à la ligne spécifiée"""
        if 0 <= row < len(self.filtered_invoices):
            return self.filtered_invoices[row].id
        return None

    def _get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            InvoiceStatus.PENDING: "En attente",
            InvoiceStatus.PARTIAL: "Partiellement payée",
            InvoiceStatus.PAID: "Payée",
            InvoiceStatus.CANCELLED: "Annulée",
            InvoiceStatus.DISPUTED: "Contestée"
        }
        return status_display.get(status, str(status))


class SupplierPaymentTableModel(QAbstractTableModel):
    """Modèle de table pour les paiements fournisseurs"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.payments = []
        self.filtered_payments = []
        self.headers = [
            "Date", "Fournisseur", "Facture", "Montant",
            "Méthode", "Référence", "Traité par"
        ]

        # Couleurs pour les méthodes de paiement
        self.method_colors = {
            PaymentMethod.cash: QColor("#E8F5E9"),  # Vert très clair
            PaymentMethod.bank_transfer: QColor("#E3F2FD"),  # Bleu très clair
            PaymentMethod.check: QColor("#FFF3E0"),  # Orange très clair
            PaymentMethod.credit_card: QColor("#F3E5F5"),  # Violet très clair
            PaymentMethod.other: QColor("#FAFAFA")  # Gris très clair
        }

    def rowCount(self, parent=QModelIndex()):
        return len(self.filtered_payments)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return None

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.filtered_payments):
            return None

        payment = self.filtered_payments[index.row()]

        if role == Qt.ItemDataRole.DisplayRole:
            col = index.column()
            if col == 0:  # Date
                return payment.payment_date.strftime("%d/%m/%Y") if payment.payment_date else "N/A"
            elif col == 1:  # Fournisseur
                return payment.supplier.name if payment.supplier else "N/A"
            elif col == 2:  # Facture
                if payment.invoice:
                    return payment.invoice.invoice_number
                return "Aucune"
            elif col == 3:  # Montant
                currency = payment.invoice.currency if payment.invoice else "DA"
                return f"{payment.amount:.2f} {currency}"
            elif col == 4:  # Méthode
                return self._get_payment_method_display(payment.payment_method)
            elif col == 5:  # Référence
                return payment.reference or "N/A"
            elif col == 6:  # Traité par
                if payment.processor:
                    return payment.processor.full_name
                return "N/A"

        elif role == Qt.ItemDataRole.BackgroundRole:
            # Colorer la ligne en fonction de la méthode de paiement
            return self.method_colors.get(payment.payment_method, None)

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            col = index.column()
            if col in [0, 3]:  # Date, Montant
                return int(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            return int(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)

        return None

    def set_data(self, payments):
        """Définit les données du modèle"""
        self.beginResetModel()
        self.payments = payments
        self.filtered_payments = payments
        self.endResetModel()

    def filter_by_supplier(self, supplier_id):
        """Filtre les paiements par fournisseur"""
        self.beginResetModel()
        if supplier_id:
            self.filtered_payments = [
                payment for payment in self.payments
                if payment.supplier_id == supplier_id
            ]
        else:
            self.filtered_payments = self.payments
        self.endResetModel()

    def filter_by_method(self, method):
        """Filtre les paiements par méthode"""
        self.beginResetModel()
        if method:
            self.filtered_payments = [
                payment for payment in self.filtered_payments
                if payment.payment_method.value == method
            ]
        self.endResetModel()

    def clear_method_filter(self):
        """Efface le filtre de méthode"""
        self.beginResetModel()
        # Réappliquer uniquement le filtre de fournisseur
        supplier_id = None
        if self.filtered_payments and self.payments:
            # Détecter si un filtre de fournisseur est appliqué
            if len(self.filtered_payments) < len(self.payments):
                if self.filtered_payments:
                    supplier_id = self.filtered_payments[0].supplier_id

        if supplier_id:
            self.filtered_payments = [
                payment for payment in self.payments
                if payment.supplier_id == supplier_id
            ]
        else:
            self.filtered_payments = self.payments
        self.endResetModel()

    def get_id_at_row(self, row):
        """Retourne l'ID du paiement à la ligne spécifiée"""
        if 0 <= row < len(self.filtered_payments):
            return self.filtered_payments[row].id
        return None

    def _get_payment_method_display(self, method):
        """Retourne l'affichage de la méthode de paiement"""
        method_display = {
            PaymentMethod.cash: "Espèces",
            PaymentMethod.bank_transfer: "Virement bancaire",
            PaymentMethod.check: "Chèque",
            PaymentMethod.credit_card: "Carte de crédit",
            PaymentMethod.other: "Autre"
        }
        return method_display.get(method, str(method))
