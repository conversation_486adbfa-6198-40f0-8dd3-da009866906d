# Resource object code (Python 3)
# Created by: object code
# Created by: The Resource Compiler for Qt version 6.9.0
# WARNING! All changes made in this file will be lost!

from PyQt6 import QtCore

qt_resource_data = b"\
\x00\x00\x00\xb7\
<\
svg xmlns=\x22http:\
//www.w3.org/200\
0/svg\x22 width=\x2224\
\x22 height=\x2224\x22 vi\
ewBox=\x220 0 24 24\
\x22>\x0a    <path fil\
l=\x22currentColor\x22\
 d=\x22M3 3h8v8H3V3\
m0 10h8v8H3v-8m1\
0-10h8v8h-8V3m0 \
10h8v8h-8v-8\x22/>\x0a\
</svg>\
\x00\x00\x01A\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <c\
ircle cx=\x2212\x22 cy\
=\x2212\x22 r=\x2210\x22></c\
ircle>\x0a  <polyli\
ne points=\x2212 6 \
12 12 16 14\x22></p\
olyline>\x0a</svg>\x0a\
\
\x00\x00\x01\xa9\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <p\
olyline points=\x22\
6 9 6 2 18 2 18 \
9\x22></polyline>\x0a \
 <path d=\x22M6 18H\
4a2 2 0 0 1-2-2v\
-5a2 2 0 0 1 2-2\
h16a2 2 0 0 1 2 \
2v5a2 2 0 0 1-2 \
2h-2\x22></path>\x0a  \
<rect x=\x226\x22 y=\x221\
4\x22 width=\x2212\x22 he\
ight=\x228\x22></rect>\
\x0a</svg>\x0a\
\x00\x00\x01\xa0\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <l\
ine x1=\x2218\x22 y1=\x22\
20\x22 x2=\x2218\x22 y2=\x22\
10\x22></line>\x0a  <l\
ine x1=\x2212\x22 y1=\x22\
20\x22 x2=\x2212\x22 y2=\x22\
4\x22></line>\x0a  <li\
ne x1=\x226\x22 y1=\x2220\
\x22 x2=\x226\x22 y2=\x2214\x22\
></line>\x0a  <line\
 x1=\x223\x22 y1=\x2220\x22 \
x2=\x2221\x22 y2=\x2220\x22>\
</line>\x0a</svg>\x0a\
\x00\x00\x01\xb6\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <r\
ect x=\x221\x22 y=\x223\x22 \
width=\x2215\x22 heigh\
t=\x2213\x22></rect>\x0a \
 <polygon points\
=\x2216 8 20 8 23 1\
1 23 16 16 16 8\x22></polygon>\x0a \
 <circle cx=\x225.5\
\x22 cy=\x2218.5\x22 r=\x222\
.5\x22></circle>\x0a  \
<circle cx=\x2218.5\
\x22 cy=\x2218.5\x22 r=\x222\
.5\x22></circle>\x0a</\
svg>\x0a\
\x00\x00\x01{\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <p\
ath d=\x22M23 4v6h-\
6\x22></path>\x0a  <pa\
th d=\x22M1 20v-6h6\
\x22></path>\x0a  <pat\
h d=\x22M3.51 9a9 9\
 0 0 1 14.85-3.3\
6L23 10M1 14l4.6\
4 4.36A9 9 0 0 0\
 20.49 15\x22></pat\
h>\x0a</svg>\x0a\
\x00\x00\x01\xd8\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <r\
ect x=\x222\x22 y=\x225\x22 \
width=\x2220\x22 heigh\
t=\x2214\x22 rx=\x222\x22></\
rect>\x0a  <line x1\
=\x222\x22 y1=\x2210\x22 x2=\
\x2222\x22 y2=\x2210\x22></l\
ine>\x0a  <line x1=\
\x222\x22 y1=\x2214\x22 x2=\x22\
22\x22 y2=\x2214\x22></li\
ne>\x0a  <line x1=\x22\
6\x22 y1=\x222\x22 x2=\x226\x22\
 y2=\x2222\x22></line>\
\x0a  <line x1=\x2218\x22\
 y1=\x222\x22 x2=\x2218\x22 \
y2=\x2222\x22></line>\x0a\
</svg>\x0a\
\x00\x00\x00\xf6\
<\
svg xmlns=\x22http:\
//www.w3.org/200\
0/svg\x22 width=\x2224\
\x22 height=\x2224\x22 vi\
ewBox=\x220 0 24 24\
\x22>\x0a    <path fil\
l=\x22currentColor\x22\
 d=\x22M19 12v7H5v-\
7H3v7c0 1.1.9 2 \
2 2h14c1.1 0 2-.\
9 2-2v-7h-2zm-6 \
.67l2.59-2.58L17\
 11.5l-5 5-5-5 1\
.41-1.41L11 12.6\
7V3h2v9.67z\x22/>\x0a<\
/svg>\
\x00\x00\x00\xc4\
<\
svg xmlns=\x22http:\
//www.w3.org/200\
0/svg\x22 width=\x2224\
\x22 height=\x2224\x22 vi\
ewBox=\x220 0 24 24\
\x22>\x0a    <path fil\
l=\x22currentColor\x22\
 d=\x22M20 8h-3V4H3\
v13h2v3h16V8zM5 \
6h10v2H5V6zm0 4h\
10v2H5v-2zm0 4h1\
0v2H5v-2z\x22/>\x0a</s\
vg>\
\x00\x00\x01\x87\
<\
svg xmlns=\x22http:\
//www.w3.org/200\
0/svg\x22 width=\x2224\
\x22 height=\x2224\x22 vi\
ewBox=\x220 0 24 24\
\x22>\x0d\x0a    <path fi\
ll=\x22currentColor\
\x22 d=\x22M13.7 15.3a\
1 1 0 0 1-1.4 1.\
4l-2-2a1 1 0 0 1\
 1.4-1.4l2 2zM12\
 3a9 9 0 0 1 9 9\
h-2a7 7 0 0 0-7-\
7V3z\x22/>\x0d\x0a    <pa\
th fill=\x22current\
Color\x22 d=\x22M15.9 \
15.9a9 9 0 0 1-1\
2.7 0 9 9 0 0 1 \
0-12.7 9 9 0 0 1\
 12.7 0l-1.4 1.4\
a7 7 0 0 0-9.9 0\
 7 7 0 0 0 0 9.9\
 7 7 0 0 0 9.9 0\
l1.4 1.4z\x22/>\x0d\x0a</\
svg>\x0d\x0a\
\x00\x00\x01@\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <l\
ine x1=\x2218\x22 y1=\x22\
6\x22 x2=\x226\x22 y2=\x2218\
\x22></line>\x0a  <lin\
e x1=\x226\x22 y1=\x226\x22 \
x2=\x2218\x22 y2=\x2218\x22>\
</line>\x0a</svg>\x0a\
\x00\x00\x015\
<\
svg xmlns=\x22http:\
//www.w3.org/200\
0/svg\x22 width=\x2224\
\x22 height=\x2224\x22 vi\
ewBox=\x220 0 24 24\
\x22>\x0a    <path fil\
l=\x22currentColor\x22\
 d=\x22M12 4a4 4 0 \
1 1 0 8 4 4 0 0 \
1 0-8zm0 2a2 2 0\
 1 0 0 4 2 2 0 0\
 0 0-4zm0 7c2.67\
 0 8 1.33 8 4v3H\
4v-3c0-2.67 5.33\
-4 8-4zm0 1.9c-2\
.97 0-6.1 1.46-6\
.1 2.1v1.1h12.2V\
17c0-.64-3.13-2.\
1-6.1-2.1z\x22/>\x0a</\
svg>\
\x00\x00\x01\xef\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <p\
ath d=\x22M14 2H6a2\
 2 0 0 0-2 2v16a\
2 2 0 0 0 2 2h12\
a2 2 0 0 0 2-2V8\
z\x22></path>\x0a  <po\
lyline points=\x221\
4 2 14 8 20 8\x22><\
/polyline>\x0a  <li\
ne x1=\x2216\x22 y1=\x221\
3\x22 x2=\x228\x22 y2=\x2213\
\x22></line>\x0a  <lin\
e x1=\x2216\x22 y1=\x2217\
\x22 x2=\x228\x22 y2=\x2217\x22\
></line>\x0a  <poly\
line points=\x2210 \
9 9 9 8 9\x22></pol\
yline>\x0a</svg>\x0a\
\x00\x00\x03'\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg width\
=\x2224px\x22 height=\x22\
24px\x22 viewBox=\x220\
 0 24 24\x22 stroke\
-width=\x221.5\x22 fil\
l=\x22none\x22 xmlns=\x22\
http://www.w3.or\
g/2000/svg\x22 colo\
r=\x22#000000\x22>\x0a  <\
path d=\x22M12 2C6.\
47715 2 2 6.4771\
5 2 12C2 17.5228\
 6.47715 22 12 2\
2C17.5228 22 22 \
17.5228 22 12C22\
 6.47715 17.5228\
 2 12 2Z\x22 stroke\
=\x22#000000\x22 strok\
e-width=\x221.5\x22 st\
roke-linecap=\x22ro\
und\x22 stroke-line\
join=\x22round\x22></p\
ath>\x0a  <path d=\x22\
M4.271 18.3457C4\
.271 18.3457 6.5\
0002 15.5 12 15.\
5C17.5 15.5 19.7\
291 18.3457 19.7\
291 18.3457\x22 str\
oke=\x22#000000\x22 st\
roke-width=\x221.5\x22\
 stroke-linecap=\
\x22round\x22 stroke-l\
inejoin=\x22round\x22>\
</path>\x0a  <path \
d=\x22M12 12C13.656\
9 12 15 10.6569 \
15 9C15 7.34315 \
13.6569 6 12 6C1\
0.3431 6 9 7.343\
15 9 9C9 10.6569\
 10.3431 12 12 1\
2Z\x22 stroke=\x22#000\
000\x22 stroke-widt\
h=\x221.5\x22 stroke-l\
inecap=\x22round\x22 s\
troke-linejoin=\x22\
round\x22></path>\x0a<\
/svg>\x0a\
\x00\x00\x00\xcd\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0d\x0a<svg xmln\
s=\x22http://www.w3\
.org/2000/svg\x22 w\
idth=\x2224\x22 height\
=\x2224\x22 viewBox=\x220\
 0 24 24\x22>\x0d\x0a  <p\
ath fill=\x22curren\
tColor\x22 d=\x22M19 1\
3h-6v6h-2v-6H5v-\
2h6V5h2v6h6v2z\x22/\
>\x0d\x0a</svg> \x0d\x0a\
\x00\x00\x01I\
<\
svg xmlns=\x22http:\
//www.w3.org/200\
0/svg\x22 width=\x2224\
\x22 height=\x2224\x22 vi\
ewBox=\x220 0 24 24\
\x22>\x0a    <path fil\
l=\x22currentColor\x22\
 d=\x22M15.5 14h-.7\
9l-.28-.27A6.471\
 6.471 0 0 0 16 \
9.5 6.5 6.5 0 1 \
0 9.5 16c1.61 0 \
3.09-.59 4.23-1.\
57l.27.28v.79l5 \
4.99L20.49 19l-4\
.99-5zm-6 0C7.01\
 14 5 11.99 5 9.\
5S7.01 5 9.5 5 1\
4 7.01 14 9.5 11\
.99 14 9.5 14z\x22/\
>\x0a</svg>\
\x00\x00\x01\x14\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <p\
olyline points=\x22\
20 6 9 17 4 12\x22>\
</polyline>\x0a</sv\
g>\x0a\
\x00\x00\x00\xf7\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0d\x0a<svg xmln\
s=\x22http://www.w3\
.org/2000/svg\x22 w\
idth=\x2224\x22 height\
=\x2224\x22 viewBox=\x220\
 0 24 24\x22>\x0d\x0a  <p\
ath fill=\x22curren\
tColor\x22 d=\x22M4 7v\
12h16V7H4zm2 2h1\
2v8H6V9z\x22/>\x0d\x0a  <\
path fill=\x22curre\
ntColor\x22 d=\x22M2 5\
h20v2H2z\x22/>\x0d\x0a</s\
vg> \x0d\x0a\
\x00\x00\x00\xeb\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0d\x0a<svg xmln\
s=\x22http://www.w3\
.org/2000/svg\x22 w\
idth=\x2224\x22 height\
=\x2224\x22 viewBox=\x220\
 0 24 24\x22>\x0d\x0a  <p\
ath fill=\x22curren\
tColor\x22 d=\x22M19 1\
5l-6 6-1.42-1.42\
L15.17 16H4V4h2v\
10h9.17l-3.59-3.\
58L13 9l6 6z\x22/>\x0d\
\x0a</svg> \x0d\x0a\
\x00\x00\x01\xb3\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <p\
ath d=\x22M10.29 3.\
86L1.82 18a2 2 0\
 0 0 1.71 3h16.9\
4a2 2 0 0 0 1.71\
-3L13.71 3.86a2 \
2 0 0 0-3.42 0z\x22\
></path>\x0a  <line\
 x1=\x2212\x22 y1=\x229\x22 \
x2=\x2212\x22 y2=\x2213\x22>\
</line>\x0a  <line \
x1=\x2212\x22 y1=\x2217\x22 \
x2=\x2212.01\x22 y2=\x221\
7\x22></line>\x0a</svg\
>\x0a\
\x00\x00\x01\x8b\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0a<svg xmlns\
=\x22http://www.w3.\
org/2000/svg\x22 wi\
dth=\x2224\x22 height=\
\x2224\x22 viewBox=\x220 \
0 24 24\x22 fill=\x22n\
one\x22 stroke=\x22cur\
rentColor\x22 strok\
e-width=\x222\x22 stro\
ke-linecap=\x22roun\
d\x22 stroke-linejo\
in=\x22round\x22>\x0a  <c\
ircle cx=\x229\x22 cy=\
\x2221\x22 r=\x221\x22></cir\
cle>\x0a  <circle c\
x=\x2220\x22 cy=\x2221\x22 r\
=\x221\x22></circle>\x0a \
 <path d=\x22M1 1h4\
l2.68 13.39a2 2 \
0 0 0 2 1.61h9.7\
2a2 2 0 0 0 2-1.\
61L23 6H6\x22></pat\
h>\x0a</svg>\x0a\
"

qt_resource_name = b"\
\x00\x05\
\x00o\xa6S\
\x00i\
\x00c\x00o\x00n\x00s\
\x00\x0d\
\x0d\x94\x89\xc7\
\x00d\
\x00a\x00s\x00h\x00b\x00o\x00a\x00r\x00d\x00.\x00s\x00v\x00g\
\x00\x0b\
\x06FO\xa7\
\x00h\
\x00i\x00s\x00t\x00o\x00r\x00y\x00.\x00s\x00v\x00g\
\x00\x09\
\x00W\xb5\xe7\
\x00p\
\x00r\x00i\x00n\x00t\x00.\x00s\x00v\x00g\
\x00\x09\
\x08\x97\x89\x07\
\x00c\
\x00h\x00a\x00r\x00t\x00.\x00s\x00v\x00g\
\x00\x0c\
\x01\xc4;G\
\x00d\
\x00e\x00l\x00i\x00v\x00e\x00r\x00y\x00.\x00s\x00v\x00g\
\x00\x0b\
\x0cj!\xc7\
\x00r\
\x00e\x00f\x00r\x00e\x00s\x00h\x00.\x00s\x00v\x00g\
\x00\x0b\
\x0c\xbaz\xa7\
\x00p\
\x00a\x00y\x00m\x00e\x00n\x00t\x00.\x00s\x00v\x00g\
\x00\x0a\
\x06\x9a\xc4'\
\x00e\
\x00x\x00p\x00o\x00r\x00t\x00.\x00s\x00v\x00g\
\x00\x0c\
\x00\x9d\xb4\x87\
\x00s\
\x00u\x00p\x00p\x00l\x00i\x00e\x00r\x00.\x00s\x00v\x00g\
\x00\x0a\
\x08\x0aB\x07\
\x00r\
\x00e\x00p\x00a\x00i\x00r\x00.\x00s\x00v\x00g\
\x00\x0a\
\x09\xb2jG\
\x00c\
\x00a\x00n\x00c\x00e\x00l\x00.\x00s\x00v\x00g\
\x00\x0c\
\x0e\x9c\x0c\x07\
\x00c\
\x00u\x00s\x00t\x00o\x00m\x00e\x00r\x00.\x00s\x00v\x00g\
\x00\x0b\
\x0fx\xe0g\
\x00i\
\x00n\x00v\x00o\x00i\x00c\x00e\x00.\x00s\x00v\x00g\
\x00\x08\
\x09\xc5UG\
\x00u\
\x00s\x00e\x00r\x00.\x00s\x00v\x00g\
\x00\x07\
\x07\xa7Z\x07\
\x00a\
\x00d\x00d\x00.\x00s\x00v\x00g\
\x00\x0a\
\x08\x94m\xc7\
\x00s\
\x00e\x00a\x00r\x00c\x00h\x00.\x00s\x00v\x00g\
\x00\x09\
\x0b\x9e\x89\x07\
\x00c\
\x00h\x00e\x00c\x00k\x00.\x00s\x00v\x00g\
\x00\x0d\
\x06%\xd0g\
\x00i\
\x00n\x00v\x00e\x00n\x00t\x00o\x00r\x00y\x00.\x00s\x00v\x00g\
\x00\x0c\
\x06\xda\xc2\xa7\
\x00m\
\x00o\x00v\x00e\x00m\x00e\x00n\x00t\x00.\x00s\x00v\x00g\
\x00\x0b\
\x00\xb5Hg\
\x00w\
\x00a\x00r\x00n\x00i\x00n\x00g\x00.\x00s\x00v\x00g\
\x00\x0c\
\x06qI\x07\
\x00p\
\x00u\x00r\x00c\x00h\x00a\x00s\x00e\x00.\x00s\x00v\x00g\
"

qt_resource_struct = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x02\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x15\x00\x00\x00\x03\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00L\x00\x00\x00\x00\x00\x01\x00\x00\x02\x00\
\x00\x00\x01\x96\xa0\xbe\xde\xd0\
\x00\x00\x00\xec\x00\x00\x00\x00\x00\x01\x00\x00\x0b`\
\x00\x00\x01\x96v\xd7\xa5r\
\x00\x00\x02\x12\x00\x00\x00\x00\x00\x01\x00\x00\x1an\
\x00\x00\x01\x96w\xef\xffa\
\x00\x00\x00|\x00\x00\x00\x00\x00\x01\x00\x00\x05Q\
\x00\x00\x01\x96w\xd3\xb0\xf2\
\x00\x00\x01\xd4\x00\x00\x00\x00\x00\x01\x00\x00\x18\x84\
\x00\x00\x01\x96v\xeb[T\
\x00\x00\x000\x00\x00\x00\x00\x00\x01\x00\x00\x00\xbb\
\x00\x00\x01\x96\xa0\xbe\x97\x89\
\x00\x00\x02.\x00\x00\x00\x00\x00\x01\x00\x00\x1c%\
\x00\x00\x01\x96w\xcc\xe0S\
\x00\x00\x00\xd2\x00\x00\x00\x00\x00\x01\x00\x00\x0af\
\x00\x00\x01\x96v\xd7\xe94\
\x00\x00\x01\xf4\x00\x00\x00\x00\x00\x01\x00\x00\x19\x7f\
\x00\x00\x01\x96v\xebg\xa5\
\x00\x00\x01\x8e\x00\x00\x00\x00\x00\x01\x00\x00\x15N\
\x00\x00\x01\x96v\xeb8\xc0\
\x00\x00\x01\x0a\x00\x00\x00\x00\x00\x01\x00\x00\x0c(\
\x00\x00\x01\x96v\xebN\x09\
\x00\x00\x01\xa2\x00\x00\x00\x00\x00\x01\x00\x00\x16\x1f\
\x00\x00\x01\x96v\xd7\xd9\x89\
\x00\x00\x00d\x00\x00\x00\x00\x00\x01\x00\x00\x03\xad\
\x00\x00\x01\x96w\xaa\xea\xf7\
\x00\x00\x01$\x00\x00\x00\x00\x00\x01\x00\x00\x0d\xb3\
\x00\x00\x01\x96w\xd3\x7f\xaf\
\x00\x00\x01x\x00\x00\x00\x00\x00\x01\x00\x00\x12#\
\x00\x00\x01\x96xP\x87y\
\x00\x00\x01\xbc\x00\x00\x00\x00\x00\x01\x00\x00\x17l\
\x00\x00\x01\x96w\xd3P}\
\x00\x00\x00\x9a\x00\x00\x00\x00\x00\x01\x00\x00\x07\x0b\
\x00\x00\x01\x96w\xef\xd6\x19\
\x00\x00\x00\xb6\x00\x00\x00\x00\x00\x01\x00\x00\x08\x8a\
\x00\x00\x01\x96\x87\xce;\xef\
\x00\x00\x00\x10\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
\x00\x00\x01\x96v\xd6\xc8\xb9\
\x00\x00\x01>\x00\x00\x00\x00\x00\x01\x00\x00\x0e\xf7\
\x00\x00\x01\x96v\xd7\x87\x0b\
\x00\x00\x01\x5c\x00\x00\x00\x00\x00\x01\x00\x00\x100\
\x00\x00\x01\x96\x87\xcd\xd9g\
"

def qInitResources():
    QtCore.qRegisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

def qCleanupResources():
    QtCore.qUnregisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

qInitResources()
