/* Styles communs pour les icônes dans les deux thèmes */

/* Note: Ce fichier ne doit PAS définir de styles pour QPushButton ou QToolButton
   car cela interfère avec les styles spécifiques aux thèmes.
   Les styles des boutons doivent être définis dans les fichiers de thème spécifiques. */

/* Assurer que les icônes dans les menus sont visibles */
QMenu::item QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les barres d'outils sont visibles */
QToolBar QAction {
    color: #2196F3;
}

/* Assurer que les icônes dans les onglets sont visibles */
QTabBar QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les en-têtes de tableau sont visibles */
QHeaderView QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les éléments de liste sont visibles */
QListView::item QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les éléments de tableau sont visibles */
QTableView::item QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les éléments d'arbre sont visibles */
QTreeView::item QIcon {
    color: #2196F3;
}
