/* Styles communs pour la barre d'outils dans les deux thèmes */

/* Style de base pour la barre d'outils */
QToolBar {
    background-color: transparent;
    border: none;
    spacing: 6px;
    padding: 4px;
}

/* Style pour les boutons de la barre d'outils */
QToolBar QToolButton {
    background-color: transparent;
    color: palette(text);
    border: none;
    border-radius: 4px;
    padding: 4px;
    margin: 2px;
    font-size: 14px;
}

QToolBar QToolButton:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

QToolBar QToolButton:pressed {
    background-color: rgba(0, 0, 0, 0.15);
}

QToolBar QToolButton:checked {
    background-color: rgba(0, 0, 0, 0.15);
}

/* Style pour les actions de la barre d'outils */
QToolBar QAction {
    color: palette(text);
}

/* Style pour les séparateurs de la barre d'outils */
QToolBar::separator {
    background-color: rgba(0, 0, 0, 0.2);
    width: 1px;
    height: 24px;
    margin: 4px 8px;
}

/* Style pour les icônes dans la barre d'outils */
QToolBar QToolButton QIcon {
    color: palette(text);
}

/* Style pour le texte des boutons dans la barre d'outils */
QToolBar QToolButton::text {
    color: palette(text);
}

/* Style pour les boutons de la barre d'outils avec menu */
QToolBar QToolButton::menu-button {
    border: none;
    width: 16px;
}

QToolBar QToolButton::menu-arrow {
    image: url(app/ui/resources/icons/arrow-down.svg);
}

/* Style pour la barre d'outils verticale (à gauche) */
QToolBar[orientation="vertical"] {
    min-width: 48px;
    max-width: 48px;
}

QToolBar[orientation="vertical"] QToolButton {
    min-width: 40px;
    max-width: 40px;
    min-height: 40px;
    max-height: 40px;
}

/* Style pour la barre d'outils horizontale (en haut) */
QToolBar[orientation="horizontal"] {
    min-height: 40px;
    max-height: 40px;
}

QToolBar[orientation="horizontal"] QToolButton {
    min-height: 32px;
    max-height: 32px;
}

/* Style spécifique pour les boutons d'icônes Unicode */
QToolBar QToolButton#notificationButton, QToolBar QToolButton#settingsButton {
    min-width: 24px;
    max-width: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 0px;
    margin: 0px;
    font-size: 14px;
}
