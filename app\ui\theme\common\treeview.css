/* Styles communs pour les TreeViews dans les deux thèmes */

/* Style de base pour les TreeViews */
QTreeView {
    show-decoration-selected: 1;
    alternate-background-color: palette(alternate-base);
    border: 1px solid palette(mid);
}

QTreeView::item {
    border: none;
    border-top-color: transparent;
    border-bottom-color: transparent;
    padding: 4px;
}

QTreeView::item:hover {
    background-color: palette(highlight);
    color: palette(highlighted-text);
    border: none;
}

QTreeView::item:selected {
    background-color: palette(highlight);
    color: palette(highlighted-text);
}

QTreeView::item:selected:active {
    background-color: palette(highlight);
}

QTreeView::item:selected:!active {
    background-color: palette(highlight);
    color: palette(highlighted-text);
}

/* Style pour les branches des TreeViews */
QTreeView::branch {
    background-color: palette(base);
}

QTreeView::branch:selected {
    background-color: palette(highlight);
}

/* Style pour les en-têtes de TreeView */
QHeaderView::section {
    padding: 4px;
    border: 1px solid palette(mid);
    background-color: palette(button);
    color: palette(text);
}

QHeaderView::section:checked {
    background-color: palette(highlight);
    color: palette(highlighted-text);
}

QHeaderView::section:hover {
    background-color: palette(button);
    border: 1px solid palette(highlight);
}

/* Style pour les barres de défilement dans les TreeViews */
QTreeView QScrollBar:vertical {
    border: none;
    background-color: palette(base);
    width: 10px;
    margin: 0px;
}

QTreeView QScrollBar::handle:vertical {
    background-color: palette(mid);
    border-radius: 5px;
}

QTreeView QScrollBar::handle:vertical:hover {
    background-color: palette(dark);
}

QTreeView QScrollBar:horizontal {
    border: none;
    background-color: palette(base);
    height: 10px;
    margin: 0px;
}

QTreeView QScrollBar::handle:horizontal {
    background-color: palette(mid);
    border-radius: 5px;
}

QTreeView QScrollBar::handle:horizontal:hover {
    background-color: palette(dark);
}
