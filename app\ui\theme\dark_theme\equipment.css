/* Styles pour la vue d'équipement - Thème Sombre */

/* Conteneur principal */
#equipmentView {
    background-color: #121212;
    color: #FFFFFF;
}

/* En-tête de la vue équipement */
#equipmentHeader {
    background-color: #1E1E1E;
    border-bottom: 1px solid #333333;
    padding: 15px;
    margin-bottom: 10px;
}

#equipmentTitle {
    font-size: 24px;
    font-weight: bold;
    color: #2196F3;
    margin-bottom: 5px;
}

#equipmentSubtitle {
    font-size: 14px;
    color: #B3B3B3;
}

/* Barre d'outils d'équipement */
#equipmentToolbar {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
}

#equipmentToolbar QPushButton {
    background-color: #3498db;
    color: #212121;
    border: 1px solid #2980b9;
    border-radius: 4px;
    padding: 8px 16px;
    margin: 2px;
    font-weight: bold;
}

#equipmentToolbar QPushButton:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

#equipmentToolbar QPushButton:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

#equipmentToolbar QPushButton:disabled {
    background-color: #333333;
    color: #666666;
    border: 1px solid #555555;
}

/* Boutons spécialisés - Unifiés avec le style standard */
#addEquipmentButton {
    background-color: #3498db;
    color: #212121;
    border: 1px solid #2980b9;
    font-weight: bold;
}

#addEquipmentButton:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

#addEquipmentButton:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

#editEquipmentButton {
    background-color: #3498db;
    color: #212121;
    border: 1px solid #2980b9;
    font-weight: bold;
}

#editEquipmentButton:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

#editEquipmentButton:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

#deleteEquipmentButton {
    background-color: #3498db;
    color: #212121;
    border: 1px solid #2980b9;
    font-weight: bold;
}

#deleteEquipmentButton:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

#deleteEquipmentButton:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

/* Filtres d'équipement */
#equipmentFilters {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

#equipmentFilters QLabel {
    color: #FFFFFF;
    font-weight: 500;
    margin-bottom: 5px;
}

#equipmentFilters QLineEdit {
    background-color: #121212;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 8px;
    color: #FFFFFF;
    margin-bottom: 10px;
}

#equipmentFilters QLineEdit:focus {
    border-color: #2196F3;
    background-color: #1A1A1A;
}

#equipmentFilters QComboBox {
    background-color: #121212;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 8px;
    color: #FFFFFF;
    min-width: 120px;
}

#equipmentFilters QComboBox:hover {
    border-color: #2196F3;
}

#equipmentFilters QComboBox::drop-down {
    border: none;
    width: 20px;
}

#equipmentFilters QComboBox::down-arrow {
    image: url(:/icons/arrow_down_white.png);
    width: 12px;
    height: 12px;
}

/* Table d'équipement - Style unifié */
#equipmentTable {
    background-color: #1E1E1E;
    border: 1px solid #2980b9;
    border-radius: 4px;
    gridline-color: #333333;
    selection-background-color: #3498db;
    selection-color: #212121;
    alternate-background-color: #252525;
}

#equipmentTable::item {
    padding: 12px 8px;
    border-bottom: 1px solid #333333;
    color: #FFFFFF;
}

#equipmentTable::item:selected {
    background-color: #3498db;
    color: #212121;
    font-weight: bold;
    border: 1px solid #2980b9;
}

#equipmentTable::item:hover {
    background-color: #2C2C2C;
    color: #FFFFFF;
    border: 1px solid #3498db;
}

#equipmentTable::item:selected:hover {
    background-color: #2980b9;
    color: #FFFFFF;
    font-weight: bold;
}

#equipmentTable QHeaderView::section {
    background-color: #3498db;
    color: #212121;
    padding: 12px 8px;
    border: 1px solid #2980b9;
    border-right: 1px solid #2980b9;
    border-bottom: 2px solid #2980b9;
    font-weight: bold;
    font-size: 14px;
}

#equipmentTable QHeaderView::section:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

#equipmentTable QHeaderView::section:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

#equipmentTable QHeaderView::section {
    background-color: #121212;
    color: #FFFFFF;
    padding: 12px;
    border: 1px solid #333333;
    font-weight: bold;
    text-align: left;
}

#equipmentTable QHeaderView::section:hover {
    background-color: #252525;
}

#equipmentTable QHeaderView::section:pressed {
    background-color: #2196F3;
}

/* Cartes d'équipement */
#equipmentCard {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 20px;
    margin: 10px;
    min-width: 300px;
    max-width: 400px;
}

#equipmentCard:hover {
    border-color: #2196F3;
    background-color: #252525;
}

#equipmentCardHeader {
    border-bottom: 1px solid #333333;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

#equipmentCardTitle {
    font-size: 18px;
    font-weight: bold;
    color: #FFFFFF;
    margin-bottom: 5px;
}

#equipmentCardSubtitle {
    font-size: 14px;
    color: #B3B3B3;
}

#equipmentCardBody {
    margin-bottom: 15px;
}

#equipmentCardLabel {
    font-size: 12px;
    color: #B3B3B3;
    font-weight: 500;
    margin-bottom: 2px;
}

#equipmentCardValue {
    font-size: 14px;
    color: #FFFFFF;
    margin-bottom: 8px;
}

/* Statuts d'équipement */
#equipmentStatus {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
}

#equipmentStatus[status="operational"] {
    background-color: rgba(129, 199, 132, 0.2);
    color: #81C784;
    border: 1px solid #81C784;
}

#equipmentStatus[status="maintenance"] {
    background-color: rgba(255, 183, 77, 0.2);
    color: #FFB74D;
    border: 1px solid #FFB74D;
}

#equipmentStatus[status="out_of_service"] {
    background-color: rgba(207, 102, 121, 0.2);
    color: #CF6679;
    border: 1px solid #CF6679;
}

#equipmentStatus[status="retired"] {
    background-color: rgba(179, 179, 179, 0.2);
    color: #B3B3B3;
    border: 1px solid #B3B3B3;
}

/* Formulaire d'équipement */
#equipmentForm {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 20px;
}

#equipmentForm QLabel {
    color: #FFFFFF;
    font-weight: 500;
    margin-bottom: 5px;
}

#equipmentForm QLineEdit, #equipmentForm QTextEdit {
    background-color: #121212;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 10px;
    color: #FFFFFF;
    margin-bottom: 15px;
}

#equipmentForm QLineEdit:focus, #equipmentForm QTextEdit:focus {
    border-color: #2196F3;
    background-color: #1A1A1A;
}

#equipmentForm QComboBox {
    background-color: #121212;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 10px;
    color: #FFFFFF;
    margin-bottom: 15px;
}

#equipmentForm QDateEdit {
    background-color: #121212;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 10px;
    color: #FFFFFF;
    margin-bottom: 15px;
}

#equipmentForm QSpinBox, #equipmentForm QDoubleSpinBox {
    background-color: #121212;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 10px;
    color: #FFFFFF;
    margin-bottom: 15px;
}

/* Boutons du formulaire */
#equipmentFormButtons {
    text-align: right;
    margin-top: 20px;
}

#saveEquipmentButton {
    background-color: #3498db;
    color: #212121;
    border: 1px solid #2980b9;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: bold;
    margin-left: 10px;
}

#saveEquipmentButton:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

#saveEquipmentButton:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

#cancelEquipmentButton {
    background-color: #3498db;
    color: #212121;
    border: 1px solid #2980b9;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: bold;
}

#cancelEquipmentButton:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

#cancelEquipmentButton:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

/* Détails d'équipement */
#equipmentDetails {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 20px;
}

#equipmentDetailsTitle {
    font-size: 20px;
    font-weight: bold;
    color: #2196F3;
    margin-bottom: 15px;
    border-bottom: 1px solid #333333;
    padding-bottom: 10px;
}

#equipmentDetailsSection {
    margin-bottom: 20px;
}

#equipmentDetailsSectionTitle {
    font-size: 16px;
    font-weight: bold;
    color: #FFFFFF;
    margin-bottom: 10px;
}

#equipmentDetailsGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

#equipmentDetailsItem {
    margin-bottom: 10px;
}

#equipmentDetailsLabel {
    font-size: 12px;
    color: #B3B3B3;
    font-weight: 500;
    margin-bottom: 2px;
}

#equipmentDetailsValue {
    font-size: 14px;
    color: #FFFFFF;
}

/* Historique de maintenance */
#maintenanceHistory {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    margin-top: 15px;
}

#maintenanceHistoryTitle {
    font-size: 16px;
    font-weight: bold;
    color: #FFFFFF;
    padding: 15px;
    border-bottom: 1px solid #333333;
}

#maintenanceHistoryItem {
    padding: 15px;
    border-bottom: 1px solid #333333;
}

#maintenanceHistoryItem:last-child {
    border-bottom: none;
}

#maintenanceHistoryItem:hover {
    background-color: #252525;
}

#maintenanceHistoryDate {
    font-size: 12px;
    color: #B3B3B3;
    margin-bottom: 5px;
}

#maintenanceHistoryType {
    font-size: 14px;
    font-weight: bold;
    color: #2196F3;
    margin-bottom: 5px;
}

#maintenanceHistoryDescription {
    font-size: 13px;
    color: #FFFFFF;
    line-height: 1.4;
}

/* Indicateurs de performance */
#performanceIndicator {
    background-color: #121212;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 15px;
    margin: 10px;
    text-align: center;
}

#performanceIndicatorTitle {
    font-size: 14px;
    color: #B3B3B3;
    margin-bottom: 8px;
}

#performanceIndicatorValue {
    font-size: 24px;
    font-weight: bold;
    color: #2196F3;
    margin-bottom: 5px;
}

#performanceIndicatorUnit {
    font-size: 12px;
    color: #B3B3B3;
}
