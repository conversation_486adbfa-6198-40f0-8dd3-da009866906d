/* Styles pour les icônes dans le thème sombre */

/* Configuration générale des icônes */
QWidget {
    qproperty-iconSize: 16px 16px;
}

/* Assurer que les icônes dans les boutons sont visibles */
QPushButton, QToolButton {
    color: #FFFFFF;
}

QPushButton QIcon, QToolButton QIcon {
    color: #FFFFFF;
}

QPushButton:disabled QIcon, QToolButton:disabled QIcon {
    color: #666666;
}

/* Assurer que les icônes dans les menus sont visibles */
QMenu QIcon {
    color: white;
}

/* Assurer que les icônes dans les TreeViews sont visibles */
QTreeView::branch:has-children:closed {
    background: url(app/ui/resources/icons/arrow-right-white.svg) no-repeat;
}

QTreeView::branch:has-children:open {
    background: url(app/ui/resources/icons/arrow-down-white.svg) no-repeat;
}

/* Assurer que les icônes dans les listes déroulantes sont visibles */
QComboBox::down-arrow {
    image: url(app/ui/resources/icons/arrow-down-white.svg);
    width: 12px;
    height: 12px;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
    background-color: transparent;
}

/* Icônes pour les SpinBox */
QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
    image: url(app/ui/resources/icons/arrow-up-white.svg);
    width: 10px;
    height: 10px;
}

QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    image: url(app/ui/resources/icons/arrow-down-white.svg);
    width: 10px;
    height: 10px;
}

QSpinBox::up-button, QDoubleSpinBox::up-button,
QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #333333;
    border: none;
    border-radius: 2px;
    width: 16px;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #444444;
}

/* Assurer que les icônes dans les barres d'outils sont visibles */
QToolBar QAction {
    color: white;
}

/* Assurer que les icônes dans les onglets sont visibles */
QTabBar QIcon {
    color: white;
}

/* Assurer que les icônes dans les en-têtes de tableau sont visibles */
QHeaderView QIcon {
    color: white;
}

/* Assurer que les icônes dans les éléments de liste sont visibles */
QListView::item QIcon {
    color: white;
}

/* Assurer que les icônes dans les éléments de tableau sont visibles */
QTableView::item QIcon {
    color: white;
}

/* Assurer que les icônes dans les éléments d'arbre sont visibles */
QTreeView::item QIcon {
    color: #FFFFFF;
}

/* Icônes pour les cases à cocher */
QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #333333;
    border-radius: 3px;
    background-color: #1E1E1E;
}

QCheckBox::indicator:checked {
    background-color: #2196F3;
    border-color: #2196F3;
    image: url(app/ui/resources/icons/check-white.svg);
}

QCheckBox::indicator:hover {
    border-color: #2196F3;
}

/* Icônes pour les boutons radio */
QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #333333;
    border-radius: 8px;
    background-color: #1E1E1E;
}

QRadioButton::indicator:checked {
    background-color: #2196F3;
    border-color: #2196F3;
}

QRadioButton::indicator:hover {
    border-color: #2196F3;
}

/* Icônes pour les en-têtes de tableau */
QHeaderView::down-arrow {
    image: url(app/ui/resources/icons/sort-down-white.svg);
    width: 12px;
    height: 12px;
}

QHeaderView::up-arrow {
    image: url(app/ui/resources/icons/sort-up-white.svg);
    width: 12px;
    height: 12px;
}

/* Icônes pour les onglets */
QTabBar::close-button {
    image: url(app/ui/resources/icons/close-small-white.svg);
    width: 12px;
    height: 12px;
    background-color: transparent;
    border: none;
    border-radius: 6px;
}

QTabBar::close-button:hover {
    background-color: #CF6679;
}

/* Icônes pour les calendriers */
QDateEdit::drop-down, QTimeEdit::drop-down, QDateTimeEdit::drop-down {
    image: url(app/ui/resources/icons/calendar-white.svg);
    width: 16px;
    height: 16px;
    background-color: #333333;
    border: none;
    border-radius: 2px;
}

QDateEdit::drop-down:hover, QTimeEdit::drop-down:hover, QDateTimeEdit::drop-down:hover {
    background-color: #444444;
}

/* Icônes pour les barres de défilement */
QScrollBar::add-line:vertical {
    image: url(app/ui/resources/icons/arrow-down-white.svg);
    background-color: #333333;
    border: none;
    height: 12px;
}

QScrollBar::sub-line:vertical {
    image: url(app/ui/resources/icons/arrow-up-white.svg);
    background-color: #333333;
    border: none;
    height: 12px;
}

QScrollBar::add-line:horizontal {
    image: url(app/ui/resources/icons/arrow-right-white.svg);
    background-color: #333333;
    border: none;
    width: 12px;
}

QScrollBar::sub-line:horizontal {
    image: url(app/ui/resources/icons/arrow-left-white.svg);
    background-color: #333333;
    border: none;
    width: 12px;
}

/* Icônes pour les dock widgets */
QDockWidget::close-button {
    image: url(app/ui/resources/icons/close-white.svg);
    background-color: transparent;
    border: none;
    border-radius: 2px;
    padding: 2px;
}

QDockWidget::close-button:hover {
    background-color: #CF6679;
}

QDockWidget::float-button {
    image: url(app/ui/resources/icons/undock-white.svg);
    background-color: transparent;
    border: none;
    border-radius: 2px;
    padding: 2px;
}

QDockWidget::float-button:hover {
    background-color: #333333;
}

/* Icônes pour les menus */
QMenu::indicator {
    width: 16px;
    height: 16px;
}

QMenu::indicator:checked {
    image: url(app/ui/resources/icons/check-white.svg);
}

QMenu::indicator:unchecked {
    image: url(app/ui/resources/icons/uncheck-white.svg);
}

/* Icônes spécifiques aux boutons d'action */
QPushButton[action="add"] {
    qproperty-icon: url(app/ui/resources/icons/add-white.svg);
}

QPushButton[action="edit"] {
    qproperty-icon: url(app/ui/resources/icons/edit-white.svg);
}

QPushButton[action="delete"] {
    qproperty-icon: url(app/ui/resources/icons/delete-white.svg);
}

QPushButton[action="save"] {
    qproperty-icon: url(app/ui/resources/icons/save-white.svg);
}

QPushButton[action="cancel"] {
    qproperty-icon: url(app/ui/resources/icons/cancel-white.svg);
}

QPushButton[action="refresh"] {
    qproperty-icon: url(app/ui/resources/icons/refresh-white.svg);
}

QPushButton[action="search"] {
    qproperty-icon: url(app/ui/resources/icons/search-white.svg);
}

QPushButton[action="export"] {
    qproperty-icon: url(app/ui/resources/icons/export-white.svg);
}

QPushButton[action="print"] {
    qproperty-icon: url(app/ui/resources/icons/print-white.svg);
}

QPushButton[action="settings"] {
    qproperty-icon: url(app/ui/resources/icons/settings-white.svg);
}
