/* Style général - Thème Sombre */
QWidget {
    background-color: #121212;
    color: #FFFFFF;
    font-family: "Segoe UI", "Arial", sans-serif;
    font-size: 14px;
    selection-background-color: #2196F3;
    selection-color: #FFFFFF;
}

QMainWindow {
    background-color: #121212;
    color: #FFFFFF;
}

QDialog {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border: 1px solid #333333;
}

/* Boutons */
QPushButton {
    background-color: #3498db;
    color: #212121;
    border: 1px solid #2980b9;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

QPushButton:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

QPushButton:disabled {
    background-color: #333333;
    color: #666666;
    border: 1px solid #555555;
}

/* Boutons d'outils */
QToolButton {
    background-color: transparent;
    border: none;
    border-radius: 4px;
    padding: 4px;
}

QToolButton:hover {
    background-color: #333333;
}

QToolButton:pressed {
    background-color: #424242;
}

/* Champs de texte */
QLineEdit {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 8px;
    color: #FFFFFF;
    selection-background-color: #2196F3;
    selection-color: #FFFFFF;
}

QLineEdit:focus {
    border: 2px solid #2196F3;
    background-color: #252525;
}

QLineEdit:disabled {
    background-color: #0F0F0F;
    color: #666666;
    border-color: #222222;
}

QTextEdit {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 8px;
    color: #FFFFFF;
    selection-background-color: #2196F3;
    selection-color: #FFFFFF;
}

QTextEdit:focus {
    border: 2px solid #2196F3;
    background-color: #252525;
}

QPlainTextEdit {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 8px;
    color: #FFFFFF;
    selection-background-color: #2196F3;
    selection-color: #FFFFFF;
}

QPlainTextEdit:focus {
    border: 2px solid #2196F3;
    background-color: #252525;
}

/* Barres de défilement */
QScrollBar:vertical {
    border: none;
    background-color: #1E1E1E;
    width: 12px;
    margin: 0px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #333333;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #444444;
}

QScrollBar::handle:vertical:pressed {
    background-color: #2196F3;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

QScrollBar:horizontal {
    border: none;
    background-color: #1E1E1E;
    height: 12px;
    margin: 0px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #333333;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #444444;
}

QScrollBar::handle:horizontal:pressed {
    background-color: #2196F3;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
    width: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}

/* Tableaux - Style unifié cohérent avec les boutons */
QTableWidget, QTableView {
    background-color: #1E1E1E;
    border: 1px solid #2980b9;
    border-radius: 4px;
    gridline-color: #333333;
    selection-background-color: #3498db;
    selection-color: #212121;
    alternate-background-color: #252525;
    font-weight: normal;
}

QTableWidget::item, QTableView::item {
    padding: 12px 8px;
    border-bottom: 1px solid #333333;
    color: #FFFFFF;
    font-weight: normal;
}

QTableWidget::item:selected, QTableView::item:selected {
    background-color: #3498db;
    color: #212121;
    font-weight: bold;
    border: 1px solid #2980b9;
}

QTableWidget::item:hover, QTableView::item:hover {
    background-color: #2C2C2C;
    color: #FFFFFF;
    border: 1px solid #3498db;
}

QTableWidget::item:selected:hover, QTableView::item:selected:hover {
    background-color: #2980b9;
    color: #FFFFFF;
    font-weight: bold;
}

/* En-têtes de tableaux - Style cohérent avec les boutons */
QHeaderView::section {
    background-color: #3498db;
    color: #212121;
    padding: 12px 8px;
    border: 1px solid #2980b9;
    border-right: 1px solid #2980b9;
    border-bottom: 2px solid #2980b9;
    font-weight: bold;
    font-size: 14px;
}

QHeaderView::section:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

QHeaderView::section:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

/* Onglets */
QTabWidget::pane {
    border: 1px solid #333333;
    background-color: #1E1E1E;
    border-radius: 4px;
}

QTabWidget::tab-bar {
    alignment: left;
}

QTabBar::tab {
    background-color: #121212;
    border: 1px solid #333333;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 8px 16px;
    margin-right: 2px;
    color: #B3B3B3;
    min-width: 80px;
}

QTabBar::tab:selected {
    background-color: #1E1E1E;
    border-bottom: 2px solid #2196F3;
    color: #2196F3;
    font-weight: bold;
}

QTabBar::tab:hover:not(:selected) {
    background-color: #252525;
    color: #FFFFFF;
    border-color: #444444;
}

QTabBar::tab:disabled {
    color: #666666;
    background-color: #0F0F0F;
}

/* ComboBox */
QComboBox {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 8px;
    color: #FFFFFF;
    min-width: 120px;
}

QComboBox:hover {
    border-color: #2196F3;
}

QComboBox:focus {
    border: 2px solid #2196F3;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
    background-color: transparent;
}

QComboBox::down-arrow {
    image: url(:/icons/arrow_down_white.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    color: #FFFFFF;
    selection-background-color: #2196F3;
    selection-color: #FFFFFF;
}

QComboBox QAbstractItemView::item {
    padding: 8px;
    border-bottom: 1px solid #333333;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #252525;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #2196F3;
}

/* SpinBox et DoubleSpinBox */
QSpinBox, QDoubleSpinBox {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 8px;
    color: #FFFFFF;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border: 2px solid #2196F3;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    background-color: #333333;
    border: none;
    border-radius: 2px;
    width: 16px;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
    background-color: #444444;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #333333;
    border: none;
    border-radius: 2px;
    width: 16px;
}

QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #444444;
}

/* CheckBox */
QCheckBox {
    color: #FFFFFF;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #333333;
    border-radius: 3px;
    background-color: #1E1E1E;
}

QCheckBox::indicator:hover {
    border-color: #2196F3;
}

QCheckBox::indicator:checked {
    background-color: #2196F3;
    border-color: #2196F3;
    image: url(:/icons/check_white.png);
}

QCheckBox::indicator:checked:hover {
    background-color: #1976D2;
}

QCheckBox::indicator:disabled {
    border-color: #666666;
    background-color: #0F0F0F;
}

/* RadioButton */
QRadioButton {
    color: #FFFFFF;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #333333;
    border-radius: 8px;
    background-color: #1E1E1E;
}

QRadioButton::indicator:hover {
    border-color: #2196F3;
}

QRadioButton::indicator:checked {
    background-color: #2196F3;
    border-color: #2196F3;
}

QRadioButton::indicator:checked:hover {
    background-color: #1976D2;
}

QRadioButton::indicator:disabled {
    border-color: #666666;
    background-color: #0F0F0F;
}

/* Slider */
QSlider::groove:horizontal {
    border: 1px solid #333333;
    height: 6px;
    background-color: #1E1E1E;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #2196F3;
    border: 1px solid #1976D2;
    width: 18px;
    margin: -6px 0;
    border-radius: 9px;
}

QSlider::handle:horizontal:hover {
    background-color: #1976D2;
}

QSlider::handle:horizontal:pressed {
    background-color: #0D47A1;
}

/* Menus */
QMenuBar {
    background-color: #121212;
    color: #FFFFFF;
    border-bottom: 1px solid #333333;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 12px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #2196F3;
    color: #FFFFFF;
}

QMenuBar::item:pressed {
    background-color: #1976D2;
}

QMenu {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 5px;
    color: #FFFFFF;
}

QMenu::item {
    padding: 8px 20px 8px 20px;
    border-radius: 3px;
    color: #FFFFFF;
}

QMenu::item:selected {
    background-color: #2196F3;
    color: #FFFFFF;
}

QMenu::item:disabled {
    color: #666666;
}

QMenu::separator {
    height: 1px;
    background-color: #333333;
    margin: 5px 0px 5px 0px;
}

QMenu::indicator {
    width: 16px;
    height: 16px;
}

QMenu::indicator:checked {
    background-color: #2196F3;
    border: 1px solid #1976D2;
    border-radius: 2px;
}

/* Barres d'outils */
QToolBar {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    spacing: 3px;
    padding: 5px;
}

QToolBar::handle {
    background-color: #333333;
    width: 8px;
    margin: 2px;
    border-radius: 2px;
}

QToolBar::separator {
    background-color: #333333;
    width: 1px;
    margin: 5px;
}

/* Barre de statut */
QStatusBar {
    background-color: #121212;
    color: #FFFFFF;
    border-top: 1px solid #333333;
}

QStatusBar::item {
    border: none;
}

QStatusBar QLabel {
    color: #B3B3B3;
    padding: 2px 5px;
}

/* ProgressBar */
QProgressBar {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    text-align: center;
    color: #FFFFFF;
    font-weight: bold;
}

QProgressBar::chunk {
    background-color: #2196F3;
    border-radius: 3px;
}

/* GroupBox */
QGroupBox {
    color: #FFFFFF;
    border: 1px solid #333333;
    border-radius: 4px;
    margin-top: 10px;
    font-weight: bold;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #2196F3;
    font-weight: bold;
}

/* Frame */
QFrame {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
}

QFrame[frameShape="0"] {
    border: none;
}

/* DateEdit et TimeEdit */
QDateEdit, QTimeEdit, QDateTimeEdit {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 8px;
    color: #FFFFFF;
}

QDateEdit:focus, QTimeEdit:focus, QDateTimeEdit:focus {
    border: 2px solid #2196F3;
}

QDateEdit::drop-down, QTimeEdit::drop-down, QDateTimeEdit::drop-down {
    background-color: #333333;
    border: none;
    width: 20px;
    border-radius: 2px;
}

QDateEdit::drop-down:hover, QTimeEdit::drop-down:hover, QDateTimeEdit::drop-down:hover {
    background-color: #444444;
}

QCalendarWidget {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border: 1px solid #333333;
    border-radius: 4px;
}

QCalendarWidget QToolButton {
    background-color: #333333;
    color: #FFFFFF;
    border: none;
    border-radius: 2px;
    margin: 2px;
}

QCalendarWidget QToolButton:hover {
    background-color: #2196F3;
}

QCalendarWidget QMenu {
    background-color: #1E1E1E;
    color: #FFFFFF;
}

QCalendarWidget QSpinBox {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border: 1px solid #333333;
}

/* Splitter */
QSplitter::handle {
    background-color: #333333;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

QSplitter::handle:pressed {
    background-color: #2196F3;
}

/* ToolTip */
QToolTip {
    background-color: #2C2C2C;
    color: #FFFFFF;
    border: 1px solid #444444;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
}

/* Dock Widget */
QDockWidget {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border: 1px solid #333333;
    titlebar-close-icon: url(:/icons/close_white.png);
    titlebar-normal-icon: url(:/icons/undock_white.png);
}

QDockWidget::title {
    background-color: #121212;
    color: #FFFFFF;
    padding: 8px;
    border-bottom: 1px solid #333333;
}

QDockWidget::close-button, QDockWidget::float-button {
    background-color: transparent;
    border: none;
    border-radius: 2px;
    padding: 2px;
}

QDockWidget::close-button:hover, QDockWidget::float-button:hover {
    background-color: #333333;
}

/* Scrollable Area */
QScrollArea {
    background-color: #121212;
    border: 1px solid #333333;
    border-radius: 4px;
}

QScrollArea > QWidget > QWidget {
    background-color: #121212;
}

/* Labels */
QLabel {
    color: #FFFFFF;
    background-color: transparent;
}

QLabel[class="title"] {
    font-size: 18px;
    font-weight: bold;
    color: #2196F3;
}

QLabel[class="subtitle"] {
    font-size: 14px;
    color: #B3B3B3;
}

QLabel[class="error"] {
    color: #CF6679;
    font-weight: bold;
}

QLabel[class="success"] {
    color: #81C784;
    font-weight: bold;
}

QLabel[class="warning"] {
    color: #FFB74D;
    font-weight: bold;
}

QToolButton:checked {
    background-color: #1E1E1E;
    border: 1px solid #424242;
}

/* Champs de texte */
QLineEdit, QTextEdit, QPlainTextEdit {
    padding: 8px;
    border: 1px solid #333333;
    border-radius: 4px;
    background-color: #1E1E1E;
    color: white;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border: 2px solid #2196F3;
    background-color: #262626;
}

/* Listes déroulantes */
QComboBox {
    padding: 8px;
    border: 1px solid #333333;
    border-radius: 4px;
    background-color: #1E1E1E;
    color: white;
    min-width: 6em;
}

QComboBox:hover {
    border: 1px solid #424242;
}

QComboBox:focus {
    border: 2px solid #2196F3;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #333333;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

QComboBox::down-arrow {
    image: url(app/ui/resources/icons/arrow-down.svg);
    width: 16px;
    height: 16px;
}

QComboBox QAbstractItemView {
    border: 1px solid #333333;
    background-color: #1E1E1E;
    selection-background-color: #2196F3;
    selection-color: white;
}

/* Cases à cocher */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 1px solid #424242;
    border-radius: 3px;
    background-color: #1E1E1E;
}

QCheckBox::indicator:checked {
    background-color: #2196F3;
    border-color: #2196F3;
}

QCheckBox::indicator:hover {
    border-color: #2196F3;
}

/* Boutons radio */
QRadioButton {
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border: 1px solid #424242;
    border-radius: 9px;
    background-color: #1E1E1E;
}

QRadioButton::indicator:checked {
    background-color: #2196F3;
    border-color: #2196F3;
}

QRadioButton::indicator:hover {
    border-color: #2196F3;
}

/* Barres de défilement */
QScrollBar:vertical {
    border: none;
    background-color: #1E1E1E;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #424242;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background-color: #616161;
}

QScrollBar:horizontal {
    border: none;
    background-color: #1E1E1E;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #424242;
    border-radius: 5px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #616161;
}

/* Menus */
QMenu {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    padding: 5px;
}

QMenu::item {
    padding: 5px 30px 5px 20px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #2196F3;
    color: white;
}

QMenu::separator {
    height: 1px;
    background-color: #333333;
    margin: 5px 0px;
}

/* Onglets */
QTabWidget::pane {
    border: 1px solid #333333;
    background-color: #1E1E1E;
}

QTabBar::tab {
    background-color: #262626;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #1E1E1E;
    border-bottom: 2px solid #2196F3;
}

QTabBar::tab:hover:!selected {
    background-color: #333333;
}

/* Barres d'outils */
QToolBar {
    background-color: #1E1E1E;
    border-bottom: 1px solid #333333;
    spacing: 6px;
    padding: 3px;
}

/* Barre de statut */
QStatusBar {
    background-color: #1E1E1E;
    color: #B3B3B3;
}

/* TreeView et ListView - Styles spécifiques */
QTreeView, QListView {
    background-color: #1E1E1E;
    border: 1px solid #2980b9;
    gridline-color: #333333;
    selection-background-color: #3498db;
    selection-color: #212121;
    alternate-background-color: #252525;
}

QTreeView::item, QListView::item {
    padding: 8px;
    color: #FFFFFF;
}

QTreeView::item:hover, QListView::item:hover {
    background-color: #2C2C2C;
    border: 1px solid #3498db;
}

QTreeView::item:selected, QListView::item:selected {
    background-color: #3498db;
    color: #212121;
    font-weight: bold;
}

/* Boîtes de dialogue */
QDialog {
    background-color: #121212;
}

QDialogButtonBox {
    button-layout: 0;
}

/* Groupes */
QGroupBox {
    border: 1px solid #333333;
    border-radius: 4px;
    margin-top: 20px;
    padding-top: 24px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px;
    color: #B3B3B3;
}

/* Spinbox */
QSpinBox, QDoubleSpinBox, QDateEdit, QTimeEdit, QDateTimeEdit {
    padding: 4px 8px;
    border: 1px solid #333333;
    border-radius: 4px;
    background-color: #1E1E1E;
    color: white;
}

QSpinBox::up-button, QDoubleSpinBox::up-button, QDateEdit::up-button, QTimeEdit::up-button, QDateTimeEdit::up-button {
    subcontrol-origin: border;
    subcontrol-position: top right;
    width: 16px;
    border-left: 1px solid #333333;
    border-bottom: 1px solid #333333;
    border-top-right-radius: 3px;
    background-color: #262626;
}

QSpinBox::down-button, QDoubleSpinBox::down-button, QDateEdit::down-button, QTimeEdit::down-button, QDateTimeEdit::down-button {
    subcontrol-origin: border;
    subcontrol-position: bottom right;
    width: 16px;
    border-left: 1px solid #333333;
    border-top: 1px solid #333333;
    border-bottom-right-radius: 3px;
    background-color: #262626;
}

/* Sliders */
QSlider::groove:horizontal {
    border: none;
    height: 8px;
    background-color: #262626;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background-color: #2196F3;
    border: none;
    width: 16px;
    margin: -4px 0;
    border-radius: 8px;
}

QSlider::groove:vertical {
    border: none;
    width: 8px;
    background-color: #262626;
    border-radius: 4px;
}

QSlider::handle:vertical {
    background-color: #2196F3;
    border: none;
    height: 16px;
    margin: 0 -4px;
    border-radius: 8px;
}

/* Progress Bar */
QProgressBar {
    border: 1px solid #333333;
    border-radius: 4px;
    text-align: center;
    background-color: #1E1E1E;
    color: white;
}

QProgressBar::chunk {
    background-color: #2196F3;
    width: 20px;
}

/* Dashboard styles */
#dashboardHeader {
    font-size: 24px;
    font-weight: bold;
    padding: 16px;
    color: white;
}

#kpiCard {
    background-color: #1E1E1E;
    border-radius: 8px;
    padding: 16px;
    min-width: 200px;
    min-height: 150px;
    border: 1px solid #333333;
}

#kpiTitle {
    font-size: 14px;
    color: #B3B3B3;
}

#kpiValue {
    font-size: 24px;
    font-weight: bold;
    color: #2196F3;
}

QChartView {
    background-color: #1E1E1E;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    min-height: 300px;
}

/* Inventory styles */
#inventoryHeader {
    font-size: 24px;
    font-weight: bold;
    padding: 16px;
    color: white;
}

#inventoryTable {
    background-color: #1E1E1E;
    border: none;
    border-radius: 8px;
}

#inventoryTable::item {
    padding: 8px;
}

#alertCard {
    background-color: #1E1E1E;
    border-radius: 8px;
    padding: 12px;
    margin: 4px;
    border: 1px solid #333333;
}

#alertTitle {
    font-weight: bold;
    color: white;
}

#alertQuantity {
    color: #B3B3B3;
}

#alertStatus {
    color: #FF5252;
    font-weight: bold;
}

#alertHeader {
    font-size: 18px;
    font-weight: bold;
    padding: 12px;
    color: white;
}
