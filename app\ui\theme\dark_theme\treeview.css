/* Styles améliorés pour les TreeViews dans le thème sombre */

/* Note: Qt ne supporte pas @import. Les styles communs doivent être intégrés manuellement. */

/* Surcharger les styles spécifiques au thème sombre */
QTreeView {
    background-color: #1E1E1E;
    color: white;
    border: 1px solid #333333;
}

QTreeView::item {
    color: white;
}

QTreeView::item:hover {
    background-color: #333333;
}

QTreeView::item:selected {
    background-color: #2196F3;
    color: white;
}

QTreeView::item:selected:not(active) {
    background-color: #1976D2;
}

QTreeView::branch {
    background-color: #1E1E1E;
}

/* The following selector is not supported in Qt stylesheets and has been removed:
QTreeView::branch:has-siblings:!adjoins-item {
    border-image: url(app/ui/resources/icons/vline.svg) 0;
}
*/

QTreeView::branch:has-siblings:adjoins-item {
    border-image: url(app/ui/resources/icons/branch-more.svg) 0;
}

/*
QTreeView::branch:!has-children:!has-siblings:adjoins-item {
    border-image: url(app/ui/resources/icons/branch-end.svg) 0;
}
*/

/*
QTreeView::branch:has-children:!has-siblings:closed,
QTreeView::branch:closed:has-children:has-siblings {
    border-image: none;
    image: url(app/ui/resources/icons/arrow-right-white.svg);
}

QTreeView::branch:open:has-children:!has-siblings,
QTreeView::branch:open:has-children:has-siblings {
    border-image: none;
    image: url(app/ui/resources/icons/arrow-down-white.svg);
}
*/

/* Styles pour les en-têtes de TreeView */
QHeaderView::section {
    background-color: #262626;
    color: #B3B3B3;
    border: 1px solid #333333;
}

QHeaderView::section:checked {
    background-color: #2196F3;
    color: white;
}

QHeaderView::section:hover {
    background-color: #333333;
}

/* Styles pour les barres de défilement dans les TreeViews */
QTreeView QScrollBar:vertical {
    background-color: #1E1E1E;
}

QTreeView QScrollBar::handle:vertical {
    background-color: #424242;
}

QTreeView QScrollBar::handle:vertical:hover {
    background-color: #616161;
}

QTreeView QScrollBar:horizontal {
    background-color: #1E1E1E;
}

QTreeView QScrollBar::handle:horizontal {
    background-color: #424242;
}

QTreeView QScrollBar::handle:horizontal:hover {
    background-color: #616161;
}

/* Style spécifique pour le tableau de rapport */
#reportTable {
    background-color: #1E1E1E;
    background-color: #1E1E1E;
    color: white;
    border: 1px solid #333333;
}

#reportTable QHeaderView::section {
    background-color: #262626;
    color: #B3B3B3;
    border: 1px solid #333333;
}

#reportTable::item {
    color: white;
}

#reportTable::item:selected {
    background-color: #2196F3;
    color: white;
}