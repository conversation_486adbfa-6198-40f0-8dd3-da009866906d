/* Styles pour la vue des équipements */
#equipmentHeader {
    font-size: 24px;
    font-weight: bold;
    color: #1976D2;
    margin-bottom: 15px;
}

#equipmentTable {
    border: 1px solid #2980b9;
    border-radius: 4px;
    background-color: #FFFFFF;
    gridline-color: #E0E0E0;
    selection-background-color: #3498db;
    selection-color: #212121;
    alternate-background-color: #F8F9FA;
}

#equipmentTable::item {
    padding: 12px 8px;
    border-bottom: 1px solid #E0E0E0;
    color: #212121;
}

#equipmentTable::item:selected {
    background-color: #3498db;
    color: #212121;
    font-weight: bold;
    border: 1px solid #2980b9;
}

#equipmentTable::item:hover {
    background-color: #E8F4FD;
    color: #212121;
    border: 1px solid #3498db;
}

#equipmentTable::item:selected:hover {
    background-color: #2980b9;
    color: #FFFFFF;
    font-weight: bold;
}

#equipmentTable QHeaderView::section {
    background-color: #3498db;
    color: #212121;
    padding: 12px 8px;
    border: 1px solid #2980b9;
    border-right: 1px solid #2980b9;
    border-bottom: 2px solid #2980b9;
    font-weight: bold;
    font-size: 14px;
}

#equipmentTable QHeaderView::section:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

#equipmentTable QHeaderView::section:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

/* Styles pour les statuts d'équipement */
.status-operational {
    color: #4CAF50;
    font-weight: bold;
}

.status-maintenance {
    color: #2196F3;
    font-weight: bold;
}

.status-repair {
    color: #FF9800;
    font-weight: bold;
}

.status-out-of-service {
    color: #F44336;
    font-weight: bold;
}

.status-retired {
    color: #9E9E9E;
    font-weight: bold;
}

/* Styles pour les filtres */
.filter-box {
    background-color: #F5F5F5;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
}

/* Styles pour les boutons d'action */
.action-button {
    background-color: #3498db;
    color: #212121;
    border: 1px solid #2980b9;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}

.action-button:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

.action-button:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

/* Styles pour les boîtes de dialogue */
.equipment-dialog {
    background-color: white;
    border-radius: 8px;
}

.equipment-dialog QLabel {
    font-weight: bold;
}

.equipment-dialog QLineEdit, 
.equipment-dialog QComboBox,
.equipment-dialog QDateEdit,
.equipment-dialog QTextEdit {
    padding: 8px;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    background-color: #F5F5F5;
}

.equipment-dialog QLineEdit:focus, 
.equipment-dialog QComboBox:focus,
.equipment-dialog QDateEdit:focus,
.equipment-dialog QTextEdit:focus {
    border: 2px solid #1976D2;
    background-color: white;
}
