/* Styles pour les icônes dans le thème clair */

/* Assurer que les icônes dans les boutons sont visibles */
QPushButton QIcon, QToolButton QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les menus sont visibles */
QMenu QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les TreeViews sont visibles */
QTreeView::branch:has-children:closed {
    background-image: url(app/ui/resources/icons/arrow-right.svg);
}

QTreeView::branch:has-children:open {
    background-image: url(app/ui/resources/icons/arrow-down.svg);}

/* Assurer que les icônes dans les listes déroulantes sont visibles */
QComboBox::down-arrow {
    background-image: url(app/ui/resources/icons/arrow-down.svg);
}

/* Assurer que les icônes dans les barres d'outils sont visibles */
QToolBar QAction {
    color: #2196F3;
}

/* Assurer que les icônes dans les onglets sont visibles */
QTabBar QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les en-têtes de tableau sont visibles */
QHeaderView QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les éléments de liste sont visibles */
QListView::item QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les éléments de tableau sont visibles */
QTableView::item QIcon {
    color: #2196F3;
}

/* Assurer que les icônes dans les éléments d'arbre sont visibles */
QTreeView::item QIcon {
    color: #2196F3;
}
