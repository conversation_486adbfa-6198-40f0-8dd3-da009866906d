/* Styles pour le centre de notifications */
#notificationCenterTitle {
    font-size: 18px;
    font-weight: bold;
    color: #1976D2;
    margin-bottom: 10px;
}

#notificationList {
    background-color: transparent;
    border: none;
}

#notificationItem {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 8px;
}

#notificationItem[status="unread"] {
    border-left: 4px solid #1976D2;
}

#notificationItem[status="read"] {
    background-color: #F5F5F5;
    opacity: 0.8;
}

#notificationItem[priority="high"] {
    border-left: 4px solid #FF9800;
}

#notificationItem[priority="critical"] {
    border-left: 4px solid #F44336;
}

#notificationTitle {
    font-weight: bold;
    color: #212121;
}

#notificationMessage {
    color: #757575;
    margin-top: 5px;
}

#notificationDate {
    color: #9E9E9E;
    font-size: 12px;
}

#emptyNotifications {
    color: #9E9E9E;
    font-style: italic;
}

/* Styles pour le bouton de notification */
#notificationButton {
    background-color: transparent;
    border: none;
}

#notificationButton:hover {
    background-color: rgba(25, 118, 210, 0.1);
    border-radius: 4px;
}

/* Styles pour le menu contextuel */
QMenu {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    padding: 5px;
}

QMenu::item {
    padding: 5px 20px 5px 20px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #E3F2FD;
    color: #1976D2;
}

QMenu::separator {
    height: 1px;
    background-color: #E0E0E0;
    margin: 5px 0px 5px 0px;
}
