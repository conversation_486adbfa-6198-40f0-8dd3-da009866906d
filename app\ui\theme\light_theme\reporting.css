/* Styles pour la vue de reporting */
#reportingHeader {
    font-size: 24px;
    font-weight: bold;
    color: #1976D2;
    margin-bottom: 15px;
}

#reportingTabs {
    background-color: white;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
}

#reportingTabs::pane {
    border: none;
    background-color: white;
    padding: 10px;
}

#reportingTabs QTabBar::tab {
    background-color: #F5F5F5;
    border: 1px solid #E0E0E0;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 8px 16px;
    margin-right: 2px;
    color: #757575;
}

#reportingTabs QTabBar::tab:selected {
    background-color: white;
    border-bottom: 2px solid #1976D2;
    color: #1976D2;
    font-weight: bold;
}

#reportingTabs QTabBar::tab:hover:not(:selected) {
    background-color: #E3F2FD;
    color: #1976D2;
}

/* Styles pour les widgets de KPI */
#kpiWidget {
    background-color: white;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    padding: 15px;
    margin: 5px;
}

#kpiTitle {
    font-size: 14px;
    color: #757575;
    margin-bottom: 5px;
}

#kpiValue {
    font-size: 24px;
    font-weight: bold;
    color: #1976D2;
}

/* Styles pour les widgets de graphique */
#chartWidget {
    background-color: white;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    padding: 15px;
    margin: 5px;
}

#chartTitle {
    font-size: 16px;
    font-weight: bold;
    color: #424242;
    margin-bottom: 10px;
}

/* Styles pour les tableaux de rapport - Style unifié */
#reportTable {
    background-color: #FFFFFF;
    border: 1px solid #2980b9;
    border-radius: 4px;
    gridline-color: #E0E0E0;
    selection-background-color: #3498db;
    selection-color: #212121;
    alternate-background-color: #F8F9FA;
}

#reportTable::item {
    padding: 12px 8px;
    border-bottom: 1px solid #E0E0E0;
    color: #212121;
}

#reportTable::item:selected {
    background-color: #3498db;
    color: #212121;
    font-weight: bold;
    border: 1px solid #2980b9;
}

#reportTable::item:hover {
    background-color: #E8F4FD;
    color: #212121;
    border: 1px solid #3498db;
}

#reportTable::item:selected:hover {
    background-color: #2980b9;
    color: #FFFFFF;
    font-weight: bold;
}

#reportTable QHeaderView::section {
    background-color: #3498db;
    color: #212121;
    padding: 12px 8px;
    border: 1px solid #2980b9;
    border-right: 1px solid #2980b9;
    border-bottom: 2px solid #2980b9;
    font-weight: bold;
    font-size: 14px;
}

#reportTable QHeaderView::section:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

#reportTable QHeaderView::section:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

/* Styles pour l'overlay de chargement */
#loadingOverlay {
    background-color: rgba(0, 0, 0, 0.5);
}

#loadingLabel {
    color: white;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

#loadingProgressBar {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
}

#loadingProgressBar::chunk {
    background-color: #1976D2;
    border-radius: 5px;
}
