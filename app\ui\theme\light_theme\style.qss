/* Style général */
QWidget {
    background-color: #FFFFFF;
    color: #212121;
    font-family: "Segoe UI";
    font-size: 14px;
}

/* Boutons */
QPushButton {
    background-color: #3498db;
    color: #212121;
    border: 1px solid #2980b9;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

QPushButton:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

QPushButton:disabled {
    background-color: #BDBDBD;
    color: #757575;
    border: 1px solid #9E9E9E;
}

/* Champs de texte */
QLineEdit {
    padding: 8px;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    background-color: #F5F5F5;
}

QLineEdit:focus {
    border: 2px solid #1976D2;
    background-color: white;
}

/* Barr<PERSON> de défilement */
QScrollBar:vertical {
    border: none;
    background-color: #F5F5F5;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #BDBDBD;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9E9E9E;
}

/* Menus */
QMenu {
    background-color: white;
    border: 1px solid #E0E0E0;
    padding: 5px;
}

QMenu::item {
    padding: 5px 30px 5px 20px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #E3F2FD;
    color: #1976D2;
}

/* Onglets */
QTabWidget::pane {
    border: 1px solid #E0E0E0;
    background-color: white;
}

/* Assure que les pages des onglets héritent d'un fond visible */
QTabWidget > QWidget, QTabWidget QWidget#qt_tabwidget_stackedwidget, QStackedWidget, QStackedWidget > QWidget {
    background-color: #FFFFFF;
    color: #212121;
}

QTabBar::tab {
    background-color: #F5F5F5;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom: 2px solid #1976D2;
}

/* Barres d'outils */
QToolBar {
    background-color: #F5F5F5;
    border-bottom: 1px solid #E0E0E0;
    spacing: 6px;
    padding: 3px;
}

/* Barre de statut */
QStatusBar {
    background-color: #F5F5F5;
    color: #757575;
}

/* Tableaux - Style unifié cohérent avec les boutons */
QTableWidget, QTableView {
    background-color: #FFFFFF;
    border: 1px solid #2980b9;
    border-radius: 4px;
    gridline-color: #E0E0E0;
    selection-background-color: #3498db;
    selection-color: #212121;
    alternate-background-color: #F8F9FA;
    font-weight: normal;
}

QTableWidget::item, QTableView::item {
    padding: 12px 8px;
    border-bottom: 1px solid #E0E0E0;
    color: #212121;
    font-weight: normal;
}

QTableWidget::item:selected, QTableView::item:selected {
    background-color: #3498db;
    color: #212121;
    font-weight: bold;
    border: 1px solid #2980b9;
}

QTableWidget::item:hover, QTableView::item:hover {
    background-color: #E8F4FD;
    color: #212121;
    border: 1px solid #3498db;
}

QTableWidget::item:selected:hover, QTableView::item:selected:hover {
    background-color: #2980b9;
    color: #FFFFFF;
    font-weight: bold;
}

/* En-têtes de tableaux - Style cohérent avec les boutons */
QHeaderView::section {
    background-color: #3498db;
    color: #212121;
    padding: 12px 8px;
    border: 1px solid #2980b9;
    border-right: 1px solid #2980b9;
    border-bottom: 2px solid #2980b9;
    font-weight: bold;
    font-size: 14px;
}

QHeaderView::section:hover {
    background-color: #2980b9;
    color: #FFFFFF;
}

QHeaderView::section:pressed {
    background-color: #1f6da8;
    color: #FFFFFF;
}

/* Dashboard styles */
#dashboardHeader {
    font-size: 24px;
    font-weight: bold;
    padding: 16px;
    color: #212121;
}

#kpiCard {
    background-color: white;
    border-radius: 8px;
    padding: 16px;
    min-width: 200px;
    min-height: 150px;
    border: 1px solid #E0E0E0;
}

#kpiTitle {
    font-size: 14px;
    color: #757575;
}

#kpiValue {
    font-size: 24px;
    font-weight: bold;
    color: #1976D2;
}

QChartView {
    background-color: white;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    min-height: 300px;
    border: 1px solid #E0E0E0;
}

/* Inventory styles */
#inventoryHeader {
    font-size: 24px;
    font-weight: bold;
    padding: 16px;
    color: #212121;
}

#inventoryTable {
    background-color: white;
    border: none;
    border-radius: 8px;
}

#inventoryTable::item {
    padding: 8px;
}

#alertCard {
    background-color: white;
    border-radius: 8px;
    padding: 12px;
    margin: 4px;
    border: 1px solid #E0E0E0;
}

#alertTitle {
    font-weight: bold;
    color: #212121;
}

#alertQuantity {
    color: #757575;
}

#alertStatus {
    color: #F44336;
    font-weight: bold;
}

#alertHeader {
    font-size: 18px;
    font-weight: bold;
    padding: 12px;
    color: #212121;
}

/* Styles des boutons déjà définis au début du fichier - suppression de la duplication */

/* Entrées de texte */
QLineEdit {
    padding: 8px;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    background-color: #F5F5F5;
}
QLineEdit:focus {
    border: 2px solid #1976D2;
    background-color: #FFFFFF;
}

/* ComboBox - Thème clair (style material) */
QComboBox {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    padding: 8px 10px;
    color: #212121;
    min-width: 140px;
}
QComboBox:hover {
    border-color: #90CAF9;
}
QComboBox:focus {
    border: 2px solid #1976D2;
}
QComboBox::drop-down {
    border: none;
    width: 22px;
    background: transparent;
}
QComboBox::down-arrow {
    image: url(app/ui/resources/icons/arrow-down.svg);
    width: 12px;
    height: 12px;
}
QComboBox QAbstractItemView {
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    color: #212121;
    selection-background-color: #E3F2FD;
    selection-color: #1976D2;
}
QComboBox QAbstractItemView::item {
    padding: 8px;
    border-bottom: 1px solid #F0F0F0;
}

/* Fix rendu des pages d'onglets et zones défilables en thème clair */
QTabWidget QWidget {
    background-color: #FFFFFF;
    color: #212121;
}

QScrollArea, QScrollArea > QWidget, QScrollArea QWidget {
    background-color: #FFFFFF;
    color: #212121;
}

QGroupBox {
    background-color: #FFFFFF;
    color: #212121;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    margin-top: 8px;
}

QLabel { color: #212121; }

