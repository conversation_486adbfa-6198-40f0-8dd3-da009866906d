/* Styles pour les TreeViews dans le thème clair */

/* Note: Qt ne supporte pas @import. Les styles communs doivent être intégrés manuellement. */

/* Surcharger les styles spécifiques au thème clair */
QTreeView {
    background-color: white;
    color: #212121;
    border: 1px solid #E0E0E0;
}

QTreeView::item {
    color: #212121;
}

QTreeView::item:hover {
    background-color: #E3F2FD;
}

QTreeView::item:selected {
    background-color: #2196F3;
    color: white;
}

/* Removed invalid selector :!active; Qt stylesheets do not support :!active. If you want to style inactive selected items, use QTreeView::item:selected:inactive instead. */
QTreeView::item:selected:inactive {
    background-color: #64B5F6;
}

QTreeView::branch {
    background-color: white;
}

/* QTreeView::branch:has-siblings:!adjoins-item {
    border-image: url(app/ui/resources/icons/vline.svg) 0;
} */
/* Removed invalid selector: :has-siblings and :!adjoins-item are not supported in standard CSS */

/* Removed invalid selector: :has-siblings and :adjoins-item are not supported in standard CSS or Qt stylesheets
QTreeView::branch:has-siblings:adjoins-item {
    border-image: url(app/ui/resources/icons/branch-more.svg) 0;
}
*/

/*
QTreeView::branch:!has-children:!has-siblings:adjoins-item {
    border-image: url(app/ui/resources/icons/branch-end.svg) 0;
}
*/
/* Removed invalid selector: :!has-children and :!has-siblings are not supported in Qt stylesheets */

/* The following selectors used unsupported pseudo-classes and have been removed or replaced with valid Qt selectors. 
   If you want to style closed or open branches, use only :closed or :open. */

QTreeView::branch:closed {
    border-image: none;
    /* icon property is not supported in Qt stylesheets */
}

QTreeView::branch:open {
    border-image: none;
    /* icon property is not supported in Qt stylesheets */
}

/* Styles pour les en-têtes de TreeView */
QHeaderView::section {
    background-color: #F5F5F5;
    color: #616161;
    border: 1px solid #E0E0E0;
}

QHeaderView::section:checked {
    background-color: #2196F3;
    color: white;
}

QHeaderView::section:hover {
    background-color: #E3F2FD;
}

/* Styles pour les barres de défilement dans les TreeViews */
QTreeView QScrollBar:vertical {
    background-color: #F5F5F5;
}

QTreeView QScrollBar::handle:vertical {
    background-color: #BDBDBD;
}

QTreeView QScrollBar::handle:vertical:hover {
    background-color: #9E9E9E;
}

QTreeView QScrollBar:horizontal {
    background-color: #F5F5F5;
}

QTreeView QScrollBar::handle:horizontal {
    background-color: #BDBDBD;
}

QTreeView QScrollBar::handle:horizontal:hover {
    background-color: #9E9E9E;
}

/* Style spécifique pour le tableau de rapport */
#reportTable {
    background-color: white;
    background-color: white;
    color: #212121;
    border: 1px solid #E0E0E0;
}

#reportTable QHeaderView::section {
    background-color: #F5F5F5;
    color: #616161;
    border: 1px solid #E0E0E0;
}

#reportTable::item {
    color: #212121;
}

#reportTable::item:selected {
    background-color: #2196F3;
    color: white;
}
