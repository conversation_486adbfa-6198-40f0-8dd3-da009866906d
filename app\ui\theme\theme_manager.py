
from PyQt6.QtCore import QObject, pyqtSignal, QSettings
from PyQt6.QtWidgets import QApplication
from pathlib import Path
import json
import os

class ThemeManager(QObject):
    """Gestionnaire de thèmes de l'application"""

    # Signal émis lors du changement de thème
    themeChanged = pyqtSignal(str)

    # Thèmes disponibles
    AVAILABLE_THEMES = ["light", "dark"]
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.theme_dir = Path(__file__).parent
        self.settings = QSettings("GestionApp", "Themes")

        # Charger le thème sauvegardé
        saved_theme = self.settings.value("current_theme", "light")
        if saved_theme in self.AVAILABLE_THEMES:
            self.current_theme = saved_theme
        
    def load_theme(self, theme_name: str) -> dict:
        """Charge un thème depuis les fichiers de configuration"""
        theme_path = self.theme_dir / f"{theme_name}_theme/theme.json"
        
        try:
            with open(theme_path, 'r', encoding='utf-8') as f:
                theme_data = json.load(f)
            return theme_data
        except FileNotFoundError:
            raise ValueError(f"Theme '{theme_name}' not found")
            
    def get_stylesheet(self, theme_name: str) -> str:
        """Récupère la feuille de style QSS du thème"""
        theme_path = self.theme_dir / f"{theme_name}_theme/style.qss"
        
        try:
            with open(theme_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            raise ValueError(f"Stylesheet for theme '{theme_name}' not found")
            
    def switch_theme(self, theme_name: str):
        """Change le thème actif"""
        if theme_name not in self.AVAILABLE_THEMES:
            raise ValueError(f"Invalid theme name: {theme_name}")

        self.current_theme = theme_name

        # Sauvegarder le thème
        self.settings.setValue("current_theme", theme_name)

        # Émettre le signal
        self.themeChanged.emit(theme_name)
        
        # Appliquer le thème avec réinitialisation complète
        self.reset_and_apply_theme()
        
    def get_color(self, color_name: str) -> str:
        """Récupère une couleur du thème actuel"""
        theme_data = self.load_theme(self.current_theme)
        colors = theme_data.get("colors", {})

        # Support pour les couleurs imbriquées (ex: "text.primary")
        if "." in color_name:
            keys = color_name.split(".")
            value = colors
            for key in keys:
                if isinstance(value, dict):
                    value = value.get(key)
                else:
                    return None
            return value

        return colors.get(color_name)

    def get_all_stylesheets(self, theme_name: str) -> str:
        """Récupère toutes les feuilles de style d'un thème"""
        stylesheets = []
        theme_path = self.theme_dir / f"{theme_name}_theme"

        # Fichiers CSS à charger dans l'ordre
        css_files = [
            "style.qss",
            "notifications.css",
            "reporting.css",
            "equipment.css",
            "treeview.css",
            "icons.css"
        ]

        for css_file in css_files:
            file_path = theme_path / css_file
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        stylesheets.append(f"/* {css_file} */\n{content}\n")
                except Exception as e:
                    print(f"Erreur lors du chargement de {css_file}: {e}")

        return "\n".join(stylesheets)

    def is_dark_theme(self) -> bool:
        """Vérifie si le thème actuel est sombre"""
        return self.current_theme == "dark"

    def toggle_theme(self):
        """Bascule entre thème clair et sombre"""
        new_theme = "dark" if self.current_theme == "light" else "light"
        self.switch_theme(new_theme)

    def get_theme_info(self, theme_name: str = None) -> dict:
        """Récupère les informations d'un thème"""
        if theme_name is None:
            theme_name = self.current_theme

        try:
            return self.load_theme(theme_name)
        except ValueError:
            return {}

    def get_available_themes(self) -> list:
        """Récupère la liste des thèmes disponibles"""
        return self.AVAILABLE_THEMES.copy()

    def _apply_palette(self, app: QApplication):
        """Applique une palette adaptée au thème courant (corrige texte invisible)."""
        try:
            from PyQt6.QtGui import QPalette, QColor
            # Utiliser la palette standard du style Fusion comme base
            base_palette = app.style().standardPalette() if app.style() else app.palette()
            pal = QPalette(base_palette)
            if self.current_theme == "light":
                # Garantir une palette claire explicite
                pal.setColor(QPalette.ColorRole.Window, QColor("#FFFFFF"))
                pal.setColor(QPalette.ColorRole.Base, QColor("#FFFFFF"))
                pal.setColor(QPalette.ColorRole.AlternateBase, QColor("#F5F5F5"))
                pal.setColor(QPalette.ColorRole.WindowText, QColor("#212121"))
                pal.setColor(QPalette.ColorRole.Text, QColor("#212121"))
                pal.setColor(QPalette.ColorRole.Button, QColor("#F5F5F5"))
                pal.setColor(QPalette.ColorRole.ButtonText, QColor("#212121"))
                pal.setColor(QPalette.ColorRole.ToolTipBase, QColor("#FFFFFF"))
                pal.setColor(QPalette.ColorRole.ToolTipText, QColor("#212121"))
            else:
                # Palette sombre de base (la QSS gère la plupart des couleurs, on ajuste le texte de base)
                pal.setColor(QPalette.ColorRole.WindowText, QColor("#FFFFFF"))
                pal.setColor(QPalette.ColorRole.Text, QColor("#FFFFFF"))
                pal.setColor(QPalette.ColorRole.ButtonText, QColor("#FFFFFF"))
            app.setPalette(pal)
        except Exception:
            pass

    def apply_theme_to_app(self, app: QApplication = None):
        """Applique le thème actuel à l'application"""
        if app is None:
            app = QApplication.instance()

        if app:
            # Réinitialiser le style avant d'appliquer le nouveau
            app.setStyleSheet("")
            
            # Appliquer une palette adaptée au thème
            self._apply_palette(app)

            # Charger et appliquer le nouveau style
            stylesheet = self.get_all_stylesheets(self.current_theme)
            app.setStyleSheet(stylesheet)

            # Forcer un re-polish et un repaint des widgets pour éviter les éléments invisibles
            try:
                for widget in app.allWidgets():
                    st = widget.style()
                    if st is not None:
                        st.unpolish(widget)
                        st.polish(widget)
                    widget.update()
                app.processEvents()
            except Exception:
                # En cas de problème, ignorer silencieusement pour ne pas bloquer l'app
                pass

    def get_icon_path(self, icon_name: str, theme_name: str = None) -> str:
        """Récupère le chemin d'une icône pour un thème donné"""
        if theme_name is None:
            theme_name = self.current_theme

        theme_path = self.theme_dir / f"{theme_name}_theme" / "icons"
        icon_path = theme_path / f"{icon_name}.png"

        if icon_path.exists():
            return str(icon_path)

        # Fallback vers les icônes communes
        common_path = self.theme_dir / "common" / "icons" / f"{icon_name}.png"
        if common_path.exists():
            return str(common_path)

        return ""

    def reset_and_apply_theme(self, app: QApplication = None):
        """Réinitialise complètement les styles et applique le thème actuel"""
        if app is None:
            app = QApplication.instance()
        
        if app:
            # Réinitialiser complètement tous les styles
            app.setStyle("Fusion")  # Revenir au style de base Fusion
            app.setStyleSheet("")   # Effacer toutes les feuilles de style
        
            # Forcer une mise à jour
            app.processEvents()
        
            # Appliquer le nouveau thème
            stylesheet = self.get_all_stylesheets(self.current_theme)
            app.setStyleSheet(stylesheet)

            # Re-polish et repaint pour s'assurer que tous les widgets se rafraichissent correctement
            try:
                for widget in app.allWidgets():
                    st = widget.style()
                    if st is not None:
                        st.unpolish(widget)
                        st.polish(widget)
                    widget.update()
                app.processEvents()
            except Exception:
                pass
        
            # Forcer une mise à jour finale
            app.processEvents()
        
            print(f"Thème {self.current_theme} réinitialisé et appliqué avec succès")


