import asyncio
import traceback
from PyQt6.QtWidgets import QMessageBox

class AsyncRunner:
    @staticmethod
    def run_async(coro, error_message=None, parent=None):
        try:
            # Utiliser l'event loop existant ou en créer un nouveau de manière sûre
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            return loop.run_until_complete(coro)
        except Exception as e:
            if error_message:
                QMessageBox.critical(parent, "Erreur", f"{error_message}: {str(e)}")
            else:
                QMessageBox.critical(parent, "Erreur", str(e))
            traceback.print_exc()
            return None
