from PyQt6.QtWidgets import QMessageBox
import os

class DialogUtils:
    @staticmethod
    def open_modal_dialog(dialog_class, *args, **kwargs):
        dialog = dialog_class(*args, **kwargs)
        result = dialog.exec()
        return result == dialog.Accepted if hasattr(dialog, 'Accepted') else bool(result)

    @staticmethod
    def open_pdf(pdf_path, parent=None):
        try:
            if pdf_path and os.path.exists(pdf_path):
                os.startfile(pdf_path)
            else:
                QMessageBox.warning(parent, "Avertissement", "Impossible d'ouvrir le fichier PDF.")
        except Exception as e:
            QMessageBox.critical(parent, "Erreur", f"Erreur lors de l'ouverture du PDF: {str(e)}")
