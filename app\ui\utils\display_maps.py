from app.core.models.repair import (
    RepairStatus,
    RepairPriority,
    PaymentStatus,
    PaymentMethod,
    UnifiedRepairStatus,
)

# Centralized display labels for UI

STATUS_LABELS = {
    RepairStatus.PENDING: "En attente",
    RepairStatus.DIAGNOSED: "Diagnostiqué",
    RepairStatus.IN_PROGRESS: "En cours",
    # Unifier les deux variantes sur le même libellé
    RepairStatus.WAITING_PARTS: "En attente de pièces",
    RepairStatus.WAITING_FOR_PARTS: "En attente de pièces",
    RepairStatus.WAITING_FOR_CUSTOMER: "En attente du client",
    RepairStatus.READY_FOR_PICKUP: "Prête pour récupération",
    RepairStatus.DELIVERED: "Livrée",
    RepairStatus.COMPLETED: "Terminé",
    RepairStatus.CANCELLED: "Annulé",
    RepairStatus.ON_HOLD: "En pause",
    RepairStatus.INVOICED: "Facturé",
    RepairStatus.PAID: "Payé",
}

# Unified status labels (single status for UI/filters)
UNIFIED_STATUS_LABELS = {
    UnifiedRepairStatus.PENDING: "En attente",
    UnifiedRepairStatus.DIAGNOSED: "Diagnostiquée",
    UnifiedRepairStatus.IN_PROGRESS: "En cours",
    UnifiedRepairStatus.WAITING_FOR_PARTS: "En attente de pièces",
    UnifiedRepairStatus.WAITING_FOR_CUSTOMER: "En attente du client",
    UnifiedRepairStatus.ON_HOLD: "En pause",
    UnifiedRepairStatus.READY_FOR_PICKUP_PAID: "Prête (payée)",
    UnifiedRepairStatus.READY_FOR_PICKUP_UNPAID: "Prête (non payée)",
    UnifiedRepairStatus.INVOICED_PAID: "Facturée (payée)",
    UnifiedRepairStatus.INVOICED_UNPAID: "Facturée (impayée)",
    UnifiedRepairStatus.COMPLETED_AWAITING_PAYMENT: "Terminée (attente paiement)",
    UnifiedRepairStatus.CLOSED: "Clôturée",
    UnifiedRepairStatus.CANCELLED: "Annulée",
    UnifiedRepairStatus.OTHER: "Autre",
}

PRIORITY_LABELS = {
    RepairPriority.CRITICAL: "Critique",
    RepairPriority.HIGH: "Haute",
    RepairPriority.NORMAL: "Normale",
    RepairPriority.LOW: "Basse",
}

PAYMENT_STATUS_LABELS = {
    PaymentStatus.PENDING: "En attente",
    PaymentStatus.PARTIAL: "Partiel",
    PaymentStatus.PAID: "Payé",
    PaymentStatus.OVERDUE: "En retard",
    PaymentStatus.CANCELLED: "Annulé",
    PaymentStatus.DELIVERED: "Livrée",
}

PAYMENT_METHOD_LABELS = {
    PaymentMethod.cash: "Espèces",
    PaymentMethod.credit_card: "Carte de crédit",
    PaymentMethod.bank_transfer: "Virement bancaire",
    PaymentMethod.check: "Chèque",
    PaymentMethod.credit: "Crédit",
}

# Build value-to-label map for robustness (handles raw string values)
STATUS_LABELS_BY_VALUE = {(
    k.value if hasattr(k, "value") else str(k)
): v for k, v in STATUS_LABELS.items()}


def status_label(status) -> str:
    """Return French label for a RepairStatus.
    Accepts both Enum members and raw string values (e.g. values from DB).
    """
    if isinstance(status, str):
        return STATUS_LABELS_BY_VALUE.get(status, status)
    # Try enum mapping first, then fallback to value-based mapping
    return STATUS_LABELS.get(status, STATUS_LABELS_BY_VALUE.get(getattr(status, "value", ""), str(status)))

def unified_status_label(status) -> str:
    return UNIFIED_STATUS_LABELS.get(status, str(status))

def priority_label(priority) -> str:
    return PRIORITY_LABELS.get(priority, str(priority))

def payment_status_label(status) -> str:
    return PAYMENT_STATUS_LABELS.get(status, str(status))

def payment_method_label(method) -> str:
    # Handle both enum and raw string values
    if isinstance(method, str):
        for key, label in PAYMENT_METHOD_LABELS.items():
            if key.value == method:
                return label
        return method
    return PAYMENT_METHOD_LABELS.get(method, str(method))