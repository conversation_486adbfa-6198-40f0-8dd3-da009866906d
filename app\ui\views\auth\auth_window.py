from PyQt6.QtWidgets import QMainWindow, QVBoxLayout, QWidget, QMessageBox, QFileDialog
from PyQt6.QtCore import Qt, pyqtSlot, QEvent, QTimer, pyqtSignal
from PyQt6.QtGui import QCloseEvent, QIcon
import asyncio
import sys
import sqlite3
import secrets
from functools import partial

from app.ui.views.auth.login_view import LoginView
from app.controllers.auth_controller import AuthController
from app.ui.views.user.dialogs.password_reset_dialog import PasswordResetRequestDialog
from app.ui.views.auth.register_view import RegisterView
from app.core.models.config import reconfigure_engine

class AuthWindow(QMainWindow):
    """Fenêtre d'authentification principale"""

    # Signal émis lorsque l'utilisateur demande à se connecter
    loginRequested = pyqtSignal(str, str, bool)

    def __init__(self):
        super().__init__()

        # Configuration de la fenêtre
        self.setWindowTitle("Nadjib-GSM - Authentification")
        self.setFixedSize(450, 650)
        self.setWindowFlags(Qt.WindowType.WindowCloseButtonHint | Qt.WindowType.MSWindowsFixedSizeDialogHint)

        # Définir l'icône de la fenêtre
        self.setWindowIcon(QIcon("app/ui/resources/images/app.ico"))
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
        """)

        # Contrôleur d'authentification
        self.auth_controller = AuthController()

        # Connexion des signaux
        self.auth_controller.loginSucceeded.connect(self.on_login_success)
        self.auth_controller.loginFailed.connect(self.on_login_failed)

        # Initialisation de l'interface
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # Vue de connexion
        self.login_view = LoginView()
        self.login_view.loginRequested.connect(self.handle_login)
        self.login_view.forgotPasswordRequested.connect(self.handle_forgot_password)
        self.login_view.createAccountRequested.connect(self.open_register_view)
        self.login_view.changeDatabaseRequested.connect(self.open_change_database_dialog)

        # Ajout de la vue au layout
        layout.addWidget(self.login_view)

    def run_async(self, coro):
        """Exécute une coroutine asyncio de manière sécurisée dans un environnement Qt"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la coroutine dans la boucle
            return loop.run_until_complete(coro)
        finally:
            # Fermer la boucle
            loop.close()


    def handle_login(self, email: str, password: str, remember_me: bool):
        # Émettre le signal de demande de connexion
        self.loginRequested.emit(email, password, remember_me)

    def _direct_login(self, email, password, remember_me):
        # Méthode interne pour exécuter la connexion directement
        try:
            # Authentification directe avec SQLite
            db_path = "data/app.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Récupérer l'utilisateur
            cursor.execute("""
                SELECT id, email, hashed_password, full_name, status, is_active
                FROM users
                WHERE email = ?
            """, (email,))

            user = cursor.fetchone()

            if not user:
                self.login_view.show_error(f"Utilisateur avec l'email {email} non trouvé.")
                conn.close()
                return

            user_id, user_email, hashed_password, full_name, status, is_active = user

            # Vérifier le mot de passe
            from passlib.context import CryptContext
            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
            is_password_valid = pwd_context.verify(password, hashed_password)

            if not is_password_valid:
                self.login_view.show_error("Mot de passe incorrect.")
                conn.close()
                return

            if not is_active:
                self.login_view.show_error("Ce compte n'est pas actif.")
                conn.close()
                return

            if status != "active":
                self.login_view.show_error(f"Ce compte est {status}.")
                conn.close()
                return

            # Récupérer les rôles de l'utilisateur
            cursor.execute("""
                SELECT r.name
                FROM roles r
                JOIN user_roles ur ON r.id = ur.role_id
                WHERE ur.user_id = ?
            """, (user_id,))

            roles = [role[0] for role in cursor.fetchall()]

            # Créer les informations de l'utilisateur
            user_info = {
                "id": user_id,
                "email": user_email,
                "full_name": full_name,
                "permissions": roles
            }

            # Simuler un token
            token = secrets.token_hex(32)

            # Mettre à jour le contrôleur d'authentification
            self.auth_controller.current_user = user_info
            self.auth_controller.access_token = token

            # Émettre le signal de connexion réussie
            self.auth_controller.loginSucceeded.emit(user_info)

            conn.close()
        except Exception as e:
            self.login_view.show_error(f"Erreur lors de la connexion: {str(e)}")

    def handle_forgot_password(self, email: str):
        """Gère la demande de réinitialisation de mot de passe"""
        dialog = PasswordResetRequestDialog(self)
        if email:
            # Pré-remplir l'email s'il est fourni
            if hasattr(dialog, 'email_input'):
                dialog.email_input.setText(email)
        dialog.exec()

    def open_register_view(self):
        self.register_view = RegisterView(self)
        self.register_view.registerRequested.connect(self.handle_register)
        self.register_view.show()

    def handle_register(self, registration_data):
        # TODO: Envoyer registration_data au service d'inscription
        QMessageBox.information(self, "Inscription", f"Inscription demandée pour : {registration_data['email']}")
        self.register_view.close()

    def open_change_database_dialog(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Choisir une base de données", "data/", "Fichiers SQLite (*.db)")
        if file_path:
            reconfigure_engine(file_path)
            QMessageBox.information(self, "Base de données sélectionnée", f"Nouvelle base : {file_path}\nVeuillez vous reconnecter.")
            self.login_view.clear_fields()

    @pyqtSlot(dict)
    def on_login_success(self, user_info: dict):
        """Appelé lorsque la connexion réussit"""
        self.accept()

    @pyqtSlot(str)
    def on_login_failed(self, error_message: str):
        """Appelé lorsque la connexion échoue"""
        self.login_view.show_error(error_message)

    def accept(self):
        """Ferme la fenêtre avec un code de succès"""
        self.close()

    def reject(self):
        """Ferme l'application lorsque l'utilisateur annule"""
        self.close()

    def reset_fields(self):
        """Réinitialise les champs de saisie"""
        if hasattr(self, 'login_view'):
            self.login_view.clear_fields()

    def closeEvent(self, event: QCloseEvent):
        """Gère l'événement de fermeture de la fenêtre"""
        # Si l'utilisateur est authentifié, on accepte la fermeture
        if self.auth_controller.current_user is not None:
            event.accept()
            return

        # Sinon, on demande confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation de fermeture",
            "Voulez-vous vraiment quitter l'application ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Quitter l'application
            event.accept()
            sys.exit(0)
        else:
            # Annuler la fermeture
            event.ignore()
