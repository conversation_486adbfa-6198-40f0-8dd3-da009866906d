from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QFormLayout, QLineEdit,
                           QPushButton, QCheckBox, QMessageBox, QLabel, QHBoxLayout,
                           QFrame, QSizePolicy, QSpacerItem)
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtGui import QIcon, QPixmap, QFont

class LoginView(QWidget):
    loginRequested = pyqtSignal(str, str, bool)
    forgotPasswordRequested = pyqtSignal(str)
    createAccountRequested = pyqtSignal()
    changeDatabaseRequested = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # Conteneur pour le logo et le titre
        header_container = QFrame()
        header_container.setObjectName("headerContainer")
        header_layout = QVBoxLayout(header_container)

        # Logo
        logo_label = QLabel()
        try:
            # Essayer de charger le logo
            logo_pixmap = QPixmap("app/ui/resources/images/logo.png")
            if not logo_pixmap.isNull():
                logo_pixmap = logo_pixmap.scaled(120, 120, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                logo_label.setPixmap(logo_pixmap)
                logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                header_layout.addWidget(logo_label)
        except Exception as e:
            print(f"Erreur lors du chargement du logo: {e}")

        # Titre
        title_label = QLabel("Nadjib-GSM")
        title_label.setObjectName("loginTitle")
        title_label.setStyleSheet("font-size: 28px; font-weight: bold; color: #2c3e50;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)

        # Sous-titre
        subtitle_label = QLabel("Connexion à votre compte")
        subtitle_label.setObjectName("loginSubtitle")
        subtitle_label.setStyleSheet("font-size: 16px; color: #7f8c8d;")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(subtitle_label)

        main_layout.addWidget(header_container)

        # Séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet("background-color: #e0e0e0; max-height: 1px;")
        main_layout.addWidget(separator)

        # Formulaire
        form_container = QFrame()
        form_container.setObjectName("formContainer")
        form_layout = QFormLayout(form_container)
        form_layout.setContentsMargins(10, 20, 10, 10)
        form_layout.setSpacing(15)

        # Email
        email_label = QLabel("Email:")
        email_label.setStyleSheet("font-weight: bold;")
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("Entrez votre email")
        self.email_input.setMinimumHeight(35)
        self.email_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
                background-color: #f5f5f5;
                color: #333333;
            }
            QLineEdit:focus {
                border: 1px solid #3498db;
                background-color: white;
                color: #333333;
            }
        """)
        # Connecter l'événement returnPressed pour passer au champ mot de passe quand l'utilisateur appuie sur Entrée
        self.email_input.returnPressed.connect(lambda: self.password_input.setFocus())
        form_layout.addRow(email_label, self.email_input)

        # Mot de passe
        password_label = QLabel("Mot de passe:")
        password_label.setStyleSheet("font-weight: bold;")
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Entrez votre mot de passe")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setMinimumHeight(35)
        self.password_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
                background-color: #f5f5f5;
                color: #333333;
            }
            QLineEdit:focus {
                border: 1px solid #3498db;
                background-color: white;
                color: #333333;
            }
        """)
        # Connecter l'événement returnPressed pour valider le formulaire quand l'utilisateur appuie sur Entrée
        self.password_input.returnPressed.connect(self.handle_login)
        form_layout.addRow(password_label, self.password_input)

        # Case à cocher "Se souvenir de moi"
        self.remember_checkbox = QCheckBox("Se souvenir de moi")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: #555;
            }
            QCheckBox::indicator {
                width: 15px;
                height: 15px;
            }
        """)
        form_layout.addRow("", self.remember_checkbox)

        main_layout.addWidget(form_container)

        # Boutons
        buttons_container = QFrame()
        buttons_container.setObjectName("buttonsContainer")
        buttons_layout = QVBoxLayout(buttons_container)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        buttons_layout.setSpacing(10)

        # Bouton de connexion
        login_button = QPushButton("Se connecter")
        login_button.setMinimumHeight(40)
        login_button.setCursor(Qt.CursorShape.PointingHandCursor)
        login_button.setStyleSheet("""
            QPushButton {
                background-color: #2980b9;
                color: white;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3498db;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        login_button.clicked.connect(self.handle_login)
        buttons_layout.addWidget(login_button)

        # Bouton créer un compte
        create_account_button = QPushButton("Créer un compte")
        create_account_button.setMinimumHeight(35)
        create_account_button.setCursor(Qt.CursorShape.PointingHandCursor)
        create_account_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:pressed {
                background-color: #219150;
            }
        """)
        create_account_button.clicked.connect(self.createAccountRequested.emit)
        buttons_layout.addWidget(create_account_button)

        # Bouton changer de base de données
        change_db_button = QPushButton("Changer de base de données")
        change_db_button.setMinimumHeight(30)
        change_db_button.setCursor(Qt.CursorShape.PointingHandCursor)
        change_db_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #f1c40f;
            }
            QPushButton:pressed {
                background-color: #d68910;
            }
        """)
        change_db_button.clicked.connect(self.changeDatabaseRequested.emit)
        buttons_layout.addWidget(change_db_button)

        # Lien mot de passe oublié
        forgot_password_link = QPushButton("Mot de passe oublié ?")
        forgot_password_link.setFlat(True)
        forgot_password_link.setCursor(Qt.CursorShape.PointingHandCursor)
        forgot_password_link.setStyleSheet("""
            QPushButton {
                color: #3498db;
                text-align: center;
                border: none;
            }
            QPushButton:hover {
                color: #2980b9;
                text-decoration: underline;
            }
        """)
        forgot_password_link.clicked.connect(self.handle_forgot_password)
        buttons_layout.addWidget(forgot_password_link)

        main_layout.addWidget(buttons_container)

        # Ajouter un espace extensible en bas
        main_layout.addStretch(1)

        self.setLayout(main_layout)

    def handle_login(self):
        email = self.email_input.text().strip()
        password = self.password_input.text()
        remember_me = self.remember_checkbox.isChecked()

        if not email or not password:
            QMessageBox.warning(
                self,
                "Erreur de saisie",
                "Veuillez remplir tous les champs."
            )
            return

        self.loginRequested.emit(email, password, remember_me)

    def handle_forgot_password(self):
        email = self.email_input.text().strip()
        self.forgotPasswordRequested.emit(email)

    def clear_fields(self):
        self.email_input.clear()
        self.password_input.clear()
        self.remember_checkbox.setChecked(False)

    def show_error(self, message: str):
        """Affichage d'un message d'erreur"""
        QMessageBox.critical(self, "Erreur de connexion", message)
