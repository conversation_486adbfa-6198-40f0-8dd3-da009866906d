from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLineEdit,
                            QPushButton, QLabel, QMessageBox, QComboBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon
from app.core.models.auth import RoleType

class RegisterView(QWidget):
    """Vue d'inscription"""
    
    # Signal émis lors de la demande d'inscription
    registerRequested = pyqtSignal(dict)  # données d'inscription
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Configuration de l'interface utilisateur"""
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Titre
        title_label = QLabel("Création de compte")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Formulaire
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)
        
        # Nom complet
        self.full_name_input = QLineEdit()
        self.full_name_input.setPlaceholderText("Nom complet")
        self.full_name_input.setMinimumWidth(300)
        form_layout.addWidget(self.full_name_input)
        
        # Email
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("Adresse email")
        form_layout.addWidget(self.email_input)
        
        # Rôle
        self.role_combo = QComboBox()
        for role in RoleType:
            self.role_combo.addItem(role.value.capitalize(), role.value)
        form_layout.addWidget(self.role_combo)
        
        # Mot de passe
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Mot de passe")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addWidget(self.password_input)
        
        # Confirmation du mot de passe
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setPlaceholderText("Confirmer le mot de passe")
        self.confirm_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addWidget(self.confirm_password_input)
        
        # Numéro de téléphone (optionnel)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("Numéro de téléphone (optionnel)")
        form_layout.addWidget(self.phone_input)
        
        # Bouton d'inscription
        self.register_button = QPushButton("Créer le compte")
        self.register_button.setMinimumHeight(40)
        self.register_button.clicked.connect(self.handle_register)
        form_layout.addWidget(self.register_button)
        
        layout.addLayout(form_layout)
        self.setLayout(layout)
        
    def handle_register(self):
        """Gestion de la demande d'inscription"""
        # Validation des champs
        if not self._validate_fields():
            return
            
        # Création du dictionnaire de données
        registration_data = {
            "full_name": self.full_name_input.text().strip(),
            "email": self.email_input.text().strip(),
            "password": self.password_input.text(),
            "role": self.role_combo.currentData(),
            "phone_number": self.phone_input.text().strip() or None
        }
        
        # Émission du signal avec les données
        self.registerRequested.emit(registration_data)
        
    def _validate_fields(self) -> bool:
        """Validation des champs du formulaire"""
        if not all([
            self.full_name_input.text().strip(),
            self.email_input.text().strip(),
            self.password_input.text(),
            self.confirm_password_input.text()
        ]):
            QMessageBox.warning(
                self,
                "Erreur de saisie",
                "Veuillez remplir tous les champs obligatoires."
            )
            return False
            
        if self.password_input.text() != self.confirm_password_input.text():
            QMessageBox.warning(
                self,
                "Erreur de saisie",
                "Les mots de passe ne correspondent pas."
            )
            return False
            
        return True
        
    def clear_fields(self):
        """Réinitialisation des champs"""
        self.full_name_input.clear()
        self.email_input.clear()
        self.password_input.clear()
        self.confirm_password_input.clear()
        self.phone_input.clear()
        self.role_combo.setCurrentIndex(0)
        
    def show_error(self, message: str):
        """Affichage d'un message d'erreur"""
        QMessageBox.critical(self, "Erreur d'inscription", message)