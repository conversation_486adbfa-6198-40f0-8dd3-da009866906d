from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView,
    QPushButton, QLineEdit, QComboBox, QLabel,
    QFrame, QMessageBox, QMenu, QTabWidget, QHeaderView
)
from PyQt6.QtCore import Qt, QSortFilterProxyModel, QTimer, QPoint
from PyQt6.QtGui import QIcon, QAction, QCursor
import asyncio

from .customer_table_model import CustomerTableModel
from .dialogs.customer_dialog import CustomerDialog
from ...components.custom_widgets import SearchBar, LoadingOverlay, FilterComboBox
from app.core.services.customer_service import CustomerService

class CustomerView(QWidget):
    """Vue principale pour la gestion des clients"""

    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
        # Utiliser QTimer pour planifier correctement le chargement asynchrone
        QTimer.singleShot(0, self._init_data)
        # Permissions: à appliquer après authentification
        self._permissions_applied = False

    def apply_permissions(self, auth_controller):
        """Active/désactive les boutons selon les permissions utilisateur."""
        if not hasattr(auth_controller, 'has_permission'):
            return
        self.add_button.setEnabled(auth_controller.has_permission('customer.create'))
        self.edit_button.setEnabled(auth_controller.has_permission('customer.edit'))
        self.delete_button.setEnabled(auth_controller.has_permission('customer.delete'))
        self._permissions_applied = True

    def _init_data(self):
        """Initialise le chargement des données"""
        print("Initialisation des données clients...")
        # Utiliser notre wrapper pour exécuter load_data de manière asynchrone
        self._load_data_wrapper()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)



        # Barre d'outils (style inspiré de Réparation)
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(18)

        button_style = (
            "QPushButton { background: #f5f7fa; color: #1976D2; border: 1px solid #e0e0e0; border-radius: 6px; padding: 8px 18px; font-weight: 600; } "
            "QPushButton:hover { background: #e3f0fc; color: #1565c0; border: 1px solid #90caf9; } "
            "QPushButton:pressed { background: #bbdefb; color: #0d47a1; } "
            "QPushButton:disabled { background: #f5f5f5; color: #bdbdbd; border: 1px solid #eeeeee; } "
        )

        # Bouton d'ajout
        self.add_button = QPushButton("Nouveau Client")
        self.add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.add_button)

        # Bouton de modification
        self.edit_button = QPushButton("Modifier")
        self.edit_button.setIcon(QIcon("app/ui/resources/icons/edit.svg"))
        self.edit_button.setEnabled(False)
        self.edit_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.edit_button)

        # Bouton de suppression
        self.delete_button = QPushButton("Supprimer")
        self.delete_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
        self.delete_button.setEnabled(False)
        self.delete_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.delete_button)

        # Bouton d'export
        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))
        self.export_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.export_button)

        # Filtres (recherche + statut client)
        toolbar_layout.addStretch(1)

        # Barre de recherche (style inspiré)
        self.search_bar = SearchBar("Rechercher un client...")
        self.search_bar.setFixedHeight(36)
        self.search_bar.setStyleSheet(
            """
            QLineEdit {
                background: #f5f7fa;
                border: 1.5px solid #e0e0e0;
                border-radius: 18px;
                padding-left: 36px;
                font-size: 15px;
                color: #222;
            }
            QLineEdit:focus {
                border: 1.5px solid #1976D2;
                background: #fff;
            }
            """
        )
        toolbar_layout.addWidget(self.search_bar, stretch=2)

        # Filtre de statut client
        combo_style = (
            "QComboBox { background: #f5f7fa; border: 1.5px solid #e0e0e0; border-radius: 16px; padding: 6px 24px 6px 14px; font-size: 15px; color: #1976D2; min-width: 160px; } "
            "QComboBox:focus { border: 1.5px solid #1976D2; background: #fff; } "
            "QComboBox::drop-down { border: none; } "
            "QComboBox QAbstractItemView { background: #fff; border-radius: 8px; } "
        )
        self.status_filter = FilterComboBox("statut clients")
        self.status_filter.setStyleSheet(combo_style)
        self.status_filter.setFixedHeight(36)
        self.status_filter.addItem("Clients à jour", userData="up_to_date")
        self.status_filter.addItem("Clients non à jour", userData="not_up_to_date")
        self.status_filter.currentIndexChanged.connect(self.apply_status_filter)
        toolbar_layout.addWidget(self.status_filter, stretch=1)

        main_layout.addLayout(toolbar_layout)

        # Panneau de détails (onglets) sous la barre d'outils
        self.details_tabs = QTabWidget()
        self.details_tabs.setObjectName("customerDetailsTabs")
        # Fond et style comme Réparation (éviter fond blanc)
        try:
            self.details_tabs.setStyleSheet("QTabWidget::pane { background: #FFFFFF; } QStackedWidget, QStackedWidget > QWidget { background: #FFFFFF; color: #212121; }")
        except Exception:
            pass
        # Onglets basiques (placeholders pour l’instant)
        from app.ui.views.customer.widgets.customer_balance_widget import CustomerBalanceWidget
        from app.ui.views.customer.widgets.customer_info_widget import CustomerInfoWidget
        from app.ui.views.customer.widgets.customer_payments_widget import CustomerPaymentsWidget
        from app.ui.views.customer.widgets.customer_history_widget import CustomerHistoryWidget
        self.info_tab = CustomerInfoWidget()
        self.finances_tab = CustomerBalanceWidget()
        self.payments_tab = CustomerPaymentsWidget()
        self.history_tab = CustomerHistoryWidget()
        self.details_tabs.addTab(self.info_tab, "Informations")
        self.details_tabs.addTab(self.finances_tab, "Finances")
        self.details_tabs.addTab(self.payments_tab, "Paiements")
        self.details_tabs.addTab(self.history_tab, "Historique client")
        main_layout.addWidget(self.details_tabs)

        # Tableau des clients en bas
        self.table_view = QTableView()
        self.table_view.setObjectName("customerTable")
        self.table_model = CustomerTableModel()
        from ...components.custom_filter_proxy_model import CustomFilterProxyModel
        self.proxy_model = CustomFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.table_view.setModel(self.proxy_model)

        # Configuration du tableau
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)

        # Configuration avancée du tableau pour une meilleure présentation
        header = self.table_view.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Désactiver l'édition directe dans le tableau
        self.table_view.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.table_view.setShowGrid(True)

        # Style modernisé du tableau (identique à Réparation)
        self.table_view.setStyleSheet(
            """
            QTableView {
                background: #fff;
                border-radius: 12px;
                border: 1.5px solid #e0e0e0;
                font-size: 15px;
                color: #222;
                selection-background-color: #1976D2;
                selection-color: #fff;
                alternate-background-color: #f5f7fa;
                gridline-color: #e0e0e0;
                qproperty-alignment: 'AlignCenter';
            }
            QHeaderView::section {
                background: #f5f7fa;
                color: #1976D2;
                font-weight: bold;
                font-size: 15px;
                border: none;
                border-bottom: 2px solid #e0e0e0;
                border-radius: 8px 8px 0 0;
                padding: 8px 0;
                qproperty-alignment: 'AlignCenter';
                text-align: center;
            }
            QTableView::item { text-align: center; }
            QTableView::item:selected { background-color: #1976D2; color: #fff; font-weight: bold; }
            QTableView::item:hover { background-color: #e3f0fc; }
            """
        )

        # Activer les lignes alternées pour une meilleure lisibilité
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setMinimumHeight(180)
        self.table_view.setMaximumHeight(350)

        main_layout.addWidget(self.table_view)

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.add_button.clicked.connect(self.show_add_dialog)
        self.edit_button.clicked.connect(self.edit_selected_customer)
        self.delete_button.clicked.connect(self.delete_customer)
        self.export_button.clicked.connect(self.export_customers)

        self.search_bar.textChanged.connect(self.filter_customers)

        self.table_view.doubleClicked.connect(self.show_edit_dialog)
        self.table_view.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table_view.customContextMenuRequested.connect(self.show_context_menu)
        # Mettre à jour le panneau de détails selon la sélection
        self.table_view.selectionModel().selectionChanged.connect(self.update_details_panel)

    async def load_data(self):
        """Charge les données des clients"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau
            print("CustomerView: Chargement des données...")
            await self.table_model.load_data()
            print(f"CustomerView: {self.table_model.rowCount()} clients chargés")
        except Exception as e:
            print(f"CustomerView: Erreur lors du chargement des données: {e}")
        finally:
            self.loading_overlay.hide()

    def filter_customers(self):
        """Applique le filtre de recherche sur le tableau"""
        search_text = self.search_bar.text()
        self.proxy_model.set_filter_text(search_text)

    def apply_status_filter(self):
        """Filtre par statut client (à jour / non à jour)"""
        # up_to_date: current_balance <= 0
        # not_up_to_date: current_balance > 0
        selected = self.status_filter.currentData()
        # Nous utilisons set_filters avec une clé dédiée que filterAcceptsRow saura interpréter
        self.proxy_model.set_filters({'customer_status': selected})

    def show_add_dialog(self):
        """Affiche la boîte de dialogue d'ajout de client"""
        dialog = CustomerDialog(self)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

    def edit_selected_customer(self):
        """Édite le client sélectionné"""
        customer_id = self.get_selected_customer_id()
        if not customer_id:
            return

        dialog = CustomerDialog(self, customer_id=customer_id)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

            # Afficher un message de succès
            QMessageBox.information(self, "Succès", "Le client a été modifié avec succès.")

    def show_edit_dialog(self, index):
        """Affiche la boîte de dialogue d'édition de client (appelé lors du double-clic)"""
        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(index)
        customer_id = self.table_model.get_customer_id(source_index.row())

        dialog = CustomerDialog(self, customer_id=customer_id)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

            # Afficher un message de succès
            QMessageBox.information(self, "Succès", "Le client a été modifié avec succès.")

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        try:
            # Essayer d'utiliser le loop existant
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Si le loop tourne déjà, utiliser create_task
                task = asyncio.create_task(self.load_data())
                # Connecter le callback pour la fin de la tâche
                task.add_done_callback(self._on_load_complete)
            else:
                # Si pas de loop en cours, en créer un nouveau
                loop.run_until_complete(self.load_data())
                # Après chargement, auto-sélectionner la première ligne
                self._post_load_ui_update()
        except RuntimeError:
            # Fallback: créer un nouveau loop dans un thread séparé
            import threading
            thread = threading.Thread(target=self._load_data_in_thread)
            thread.daemon = True
            thread.start()

    def _on_load_complete(self, task):
        """Callback appelé quand la tâche asynchrone est terminée"""
        try:
            # Récupérer le résultat ou l'exception
            task.result()
            # Mettre à jour l'interface dans le thread principal
            QTimer.singleShot(0, self._post_load_ui_update)
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()

    def _load_data_in_thread(self):
        """Charge les données dans un thread séparé"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.load_data())
                # Mettre à jour l'interface dans le thread principal
                QTimer.singleShot(0, self._post_load_ui_update)
            finally:
                loop.close()
        except Exception as e:
            print(f"Erreur lors du chargement des données dans le thread: {e}")
            import traceback
            traceback.print_exc()

    def _post_load_ui_update(self):
        """Met à jour l'interface après le chargement des données"""
        try:
            if self.table_model.rowCount() > 0:
                self.table_view.selectRow(0)
                # Forcer la mise à jour des onglets de détails
                sel = self.table_view.selectionModel().selection()
                self.update_details_panel(sel, sel)
        except Exception:
            pass

    def on_selection_changed(self, selected, deselected):
        """Gère le changement de sélection dans le tableau"""
        # Activer/désactiver les boutons en fonction de la sélection
        has_selection = len(self.table_view.selectionModel().selectedRows()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)

    def update_details_panel(self, selected, deselected):
        """Met à jour les onglets de détails en fonction du client sélectionné"""
        customer_id = self.get_selected_customer_id()
        if not customer_id:
            # Aucun client sélectionné, remettre à zéro
            for w in (getattr(self, 'finances_tab', None), getattr(self, 'info_tab', None), getattr(self, 'payments_tab', None), getattr(self, 'history_tab', None)):
                if hasattr(w, 'clear'):
                    w.clear()
            return
        # Mettre à jour les onglets
        if hasattr(self, 'finances_tab') and hasattr(self.finances_tab, 'set_customer'):
            self.finances_tab.set_customer(customer_id)
        if hasattr(self, 'info_tab') and hasattr(self.info_tab, 'set_customer'):
            self.info_tab.set_customer(customer_id)
        if hasattr(self, 'payments_tab') and hasattr(self.payments_tab, 'set_customer'):
            self.payments_tab.set_customer(customer_id)
        if hasattr(self, 'history_tab') and hasattr(self.history_tab, 'set_customer'):
            self.history_tab.set_customer(customer_id)

    def show_context_menu(self, position):
        """Affiche le menu contextuel"""
        # Vérifier si une ligne est sélectionnée
        indexes = self.table_view.selectionModel().selectedRows()
        if not indexes:
            return

        # Créer le menu contextuel
        menu = QMenu(self)

        # Ajouter les actions
        edit_action = QAction(QIcon("app/ui/resources/icons/edit.svg"), "Modifier", self)
        edit_action.triggered.connect(lambda: self.show_edit_dialog(indexes[0]))
        menu.addAction(edit_action)

        menu.addSeparator()

        delete_action = QAction(QIcon("app/ui/resources/icons/delete.svg"), "Supprimer", self)
        delete_action.triggered.connect(self.delete_customer)
        menu.addAction(delete_action)

        # Afficher le menu
        menu.exec(QCursor.pos())

    def get_selected_customer_id(self):
        """Récupère l'ID du client sélectionné"""
        indexes = self.table_view.selectionModel().selectedRows()
        if not indexes:
            return None

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(indexes[0])
        return self.table_model.get_customer_id(source_index.row())

    def delete_customer(self):
        """Supprime (désactive) le client sélectionné"""
        customer_id = self.get_selected_customer_id()
        if not customer_id:
            return

        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir supprimer ce client ? Cette action ne peut pas être annulée.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # Désactiver le client
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Créer une nouvelle session pour éviter les conflits
            from app.utils.database import SessionLocal
            db = SessionLocal()
            service = CustomerService(db)

            # Désactiver le client
            loop.run_until_complete(service.deactivate_customer(customer_id))

            # Fermer la session
            db.close()

            # Fermer la boucle après utilisation
            loop.close()

            # Actualiser les données
            self._load_data_wrapper()

            QMessageBox.information(self, "Succès", "Le client a été supprimé avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du client: {str(e)}")
            print(f"Erreur lors de la suppression du client: {e}")
            import traceback
            traceback.print_exc()

    # La gestion financière par bouton est retirée; les opérations financières seront disponibles dans l'onglet "Finances" du panneau de détails.

    def export_customers(self):
        """Exporte les données des clients"""
        # TODO: Implémenter l'export des données
        QMessageBox.information(self, "Export", "Fonctionnalité d'export non implémentée.")
        pass
