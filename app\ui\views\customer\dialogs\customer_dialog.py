from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QFormLayout, QHBoxLayout,
    QLineEdit, QTextEdit, QCheckBox, QDialogButtonBox,
    QLabel, QPushButton, QMessageBox
)
from PyQt6.QtCore import Qt
import asyncio
from app.core.services.customer_service import CustomerService

class CustomerDialog(QDialog):
    """Boîte de dialogue pour ajouter ou modifier un client"""

    def __init__(self, parent=None, customer_id=None):
        super().__init__(parent)
        self.customer_id = customer_id

        # Initialiser le service avec une session de base de données explicite
        from app.utils.database import SessionLocal
        self.db = SessionLocal()
        self.service = CustomerService(self.db)
        self.customer = None

        # S'assurer que la session est fermée lorsque le dialogue est fermé
        self.finished.connect(self.cleanup)

        self.setWindowTitle("Nouveau Client" if customer_id is None else "Modifier Client")
        self.setMinimumWidth(500)

        self.setup_ui()
        self.setup_connections()

        # Charger les données si on est en mode édition
        if customer_id is not None:
            self.load_customer_data()

    def setup_ui(self):
        """Configure l'interface utilisateur du dialogue"""
        main_layout = QVBoxLayout(self)

        # Formulaire principal
        form_layout = QFormLayout()

        # Nom du client
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Nom de l'entreprise ou du particulier")
        form_layout.addRow("Nom*:", self.name_edit)

        # Personne de contact
        self.contact_edit = QLineEdit()
        self.contact_edit.setPlaceholderText("Personne à contacter")
        form_layout.addRow("Contact:", self.contact_edit)

        # Téléphone
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("Numéro de téléphone")
        form_layout.addRow("Téléphone*:", self.phone_edit)

        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("Adresse email")
        form_layout.addRow("Email:", self.email_edit)

        # Adresse
        self.address_edit = QLineEdit()
        self.address_edit.setPlaceholderText("Adresse postale")
        form_layout.addRow("Adresse:", self.address_edit)

        # Commune
        self.commune_edit = QLineEdit()
        self.commune_edit.setPlaceholderText("Commune")
        form_layout.addRow("Commune:", self.commune_edit)

        # Ville
        self.city_edit = QLineEdit()
        self.city_edit.setPlaceholderText("Ville")
        form_layout.addRow("Ville:", self.city_edit)

        # Code postal
        self.postal_code_edit = QLineEdit()
        self.postal_code_edit.setPlaceholderText("Code postal")
        form_layout.addRow("Code postal:", self.postal_code_edit)

        # Section financière
        finance_label = QLabel("Informations financières")
        finance_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        form_layout.addRow(finance_label)

        # Limite de crédit
        self.credit_limit_edit = QLineEdit()
        self.credit_limit_edit.setPlaceholderText("Limite de crédit en DA")
        self.credit_limit_edit.setText("0.0")
        form_layout.addRow("Limite de crédit:", self.credit_limit_edit)

        # Solde actuel
        self.current_balance_edit = QLineEdit()
        self.current_balance_edit.setPlaceholderText("Solde actuel en DA")
        self.current_balance_edit.setText("0.0")
        self.current_balance_edit.setReadOnly(True)  # Le solde actuel est en lecture seule
        form_layout.addRow("Solde actuel:", self.current_balance_edit)

        # Conditions de paiement par défaut
        self.payment_terms_edit = QLineEdit()
        self.payment_terms_edit.setPlaceholderText("Nombre de jours")
        self.payment_terms_edit.setText("30")
        form_layout.addRow("Conditions de paiement (jours):", self.payment_terms_edit)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes ou commentaires sur ce client")
        form_layout.addRow("Notes:", self.notes_edit)

        # Actif
        self.active_check = QCheckBox("Client actif")
        self.active_check.setChecked(True)
        form_layout.addRow("", self.active_check)

        main_layout.addLayout(form_layout)

        # Note sur les champs obligatoires
        required_note = QLabel("* Champs obligatoires")
        required_note.setStyleSheet("color: red;")
        main_layout.addWidget(required_note)

        # Boutons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save |
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.validate_and_accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        pass

    def cleanup(self):
        """Nettoie les ressources lors de la fermeture du dialogue"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("Session de base de données fermée")

    def load_customer_data(self):
        """Charge les données du client pour l'édition"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la méthode de chargement
            loop.run_until_complete(self._load_customer_async())

            # Fermer la boucle
            loop.close()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors du chargement des données du client: {e}")
            import traceback
            traceback.print_exc()

    async def _load_customer_async(self):
        """Charge les données de manière asynchrone"""
        self.customer = await self.service.get(self.customer_id)
        if self.customer:
            self.name_edit.setText(self.customer.name)
            if self.customer.contact_person:
                self.contact_edit.setText(self.customer.contact_person)
            self.phone_edit.setText(self.customer.phone)
            if self.customer.email:
                self.email_edit.setText(self.customer.email)
            if self.customer.address:
                self.address_edit.setText(self.customer.address)
            if self.customer.commune:
                self.commune_edit.setText(self.customer.commune)
            if self.customer.city:
                self.city_edit.setText(self.customer.city)
            if self.customer.postal_code:
                self.postal_code_edit.setText(self.customer.postal_code)
            if self.customer.notes:
                self.notes_edit.setText(self.customer.notes)
            self.active_check.setChecked(self.customer.active)

            # Champs financiers
            self.credit_limit_edit.setText(str(self.customer.credit_limit))
            self.current_balance_edit.setText(str(self.customer.current_balance))
            self.payment_terms_edit.setText(str(self.customer.default_payment_terms))

    def validate_and_accept(self):
        """Valide les données avant d'accepter"""
        # Vérifier les champs obligatoires
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Validation", "Le nom du client est obligatoire.")
            self.name_edit.setFocus()
            return

        if not self.phone_edit.text().strip():
            QMessageBox.warning(self, "Validation", "Le numéro de téléphone est obligatoire.")
            self.phone_edit.setFocus()
            return

        self.accept()

    def accept(self):
        """Enregistre les données et ferme la boîte de dialogue"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la méthode d'enregistrement
            loop.run_until_complete(self._save_customer_async())

            # Fermer la boucle
            loop.close()

            # Accepter le dialogue
            super().accept()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors de l'enregistrement du client: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement du client: {str(e)}")

    async def _save_customer_async(self):
        """Enregistre les données de manière asynchrone"""
        # Récupérer les données du formulaire
        try:
            credit_limit = float(self.credit_limit_edit.text().strip() or "0.0")
            payment_terms = int(self.payment_terms_edit.text().strip() or "30")
        except ValueError:
            # En cas d'erreur de conversion, utiliser des valeurs par défaut
            credit_limit = 0.0
            payment_terms = 30

        data = {
            "name": self.name_edit.text().strip(),
            "contact_person": self.contact_edit.text().strip() or None,
            "phone": self.phone_edit.text().strip(),
            "email": self.email_edit.text().strip() or None,
            "address": self.address_edit.text().strip() or None,
            "commune": self.commune_edit.text().strip() or None,
            "city": self.city_edit.text().strip() or None,
            "postal_code": self.postal_code_edit.text().strip() or None,
            "notes": self.notes_edit.toPlainText().strip() or None,
            "active": self.active_check.isChecked(),
            "credit_limit": credit_limit,
            "default_payment_terms": payment_terms
        }

        # Si c'est une mise à jour, ne pas modifier le solde actuel
        if self.customer_id is not None and self.customer:
            # Conserver le solde actuel
            data["current_balance"] = self.customer.current_balance

        # Import ici pour éviter les imports circulaires
        from app.core.models.customer import CustomerPydantic

        if self.customer_id is None:
            # Création d'un nouveau client
            # Ne pas spécifier d'ID pour laisser SQLite le générer automatiquement
            customer_model = CustomerPydantic(**data)
            await self.service.create(customer_model)
        else:
            # Mise à jour d'un client existant
            await self.service.update(self.customer_id, data)
