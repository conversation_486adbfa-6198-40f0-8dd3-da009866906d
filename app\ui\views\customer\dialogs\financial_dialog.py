from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QTextEdit, QDialogButtonBox, QTabWidget,
    QLabel, QPushButton, QMessageBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QComboBox, QDateEdit,
    QDoubleSpinBox, QSpinBox, QWidget
)
from PyQt6.QtCore import Qt, QDate
import asyncio
from datetime import datetime, timezone
from app.core.services.customer_service import CustomerService
from app.core.models.customer import CustomerTransactionPydantic
from app.core.services.finance_service import FinanceService
from app.core.models.treasury import PaymentMethod as TreasuryPaymentMethod

class FinancialDialog(QDialog):
    """Boîte de dialogue pour gérer les finances d'un client"""

    def __init__(self, parent=None, customer_id=None):
        super().__init__(parent)
        self.customer_id = customer_id

        if not customer_id:
            QMessageBox.critical(self, "Erreur", "ID client non spécifié")
            self.reject()
            return

        # Initialiser le service avec une session de base de données explicite
        from app.utils.database import SessionLocal
        self.db = SessionLocal()
        self.service = CustomerService(self.db)
        self.customer = None

        # S'assurer que la session est fermée lorsque le dialogue est fermé
        self.finished.connect(self.cleanup)

        self.setWindowTitle("Gestion financière du client")
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)

        self.setup_ui()
        self.setup_connections()

        # Charger les données du client
        self.load_customer_data()

    def setup_ui(self):
        """Configure l'interface utilisateur du dialogue"""
        main_layout = QVBoxLayout(self)

        # En-tête avec informations du client
        self.header_layout = QFormLayout()
        self.client_name_label = QLabel("Chargement...")
        self.client_balance_label = QLabel("Chargement...")
        self.client_credit_label = QLabel("Chargement...")

        self.header_layout.addRow("Client:", self.client_name_label)
        self.header_layout.addRow("Solde actuel:", self.client_balance_label)
        self.header_layout.addRow("Crédit disponible:", self.client_credit_label)

        main_layout.addLayout(self.header_layout)

        # Onglets
        self.tab_widget = QTabWidget()

        # Onglet Transactions
        self.transactions_tab = QWidget()
        self.setup_transactions_tab()
        self.tab_widget.addTab(self.transactions_tab, "Transactions")

        # Onglet Réparations
        self.repairs_tab = QWidget()
        self.setup_repairs_tab()
        self.tab_widget.addTab(self.repairs_tab, "Réparations")

        # Onglet Nouvelle transaction
        self.new_transaction_tab = QWidget()
        self.setup_new_transaction_tab()
        self.tab_widget.addTab(self.new_transaction_tab, "Nouvelle transaction")

        main_layout.addWidget(self.tab_widget)

        # Boutons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def setup_transactions_tab(self):
        """Configure l'onglet des transactions"""
        layout = QVBoxLayout(self.transactions_tab)

        # Tableau des transactions
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(6)
        self.transactions_table.setHorizontalHeaderLabels([
            "Date", "Montant", "Description", "Référence", "Réparation", "Traité par"
        ])
        self.transactions_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        layout.addWidget(self.transactions_table)

        # Boutons
        buttons_layout = QHBoxLayout()
        self.refresh_transactions_button = QPushButton("Actualiser")
        buttons_layout.addWidget(self.refresh_transactions_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

    def setup_repairs_tab(self):
        """Configure l'onglet des réparations"""
        layout = QVBoxLayout(self.repairs_tab)

        # Tableau des réparations
        self.repairs_table = QTableWidget()
        self.repairs_table.setColumnCount(7)
        self.repairs_table.setHorizontalHeaderLabels([
            "Numéro", "Date", "Statut", "Montant total", "Payé", "Reste à payer", "Statut paiement"
        ])
        self.repairs_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        layout.addWidget(self.repairs_table)

        # Boutons
        buttons_layout = QHBoxLayout()
        self.refresh_repairs_button = QPushButton("Actualiser")
        self.pay_repair_button = QPushButton("Enregistrer un paiement")
        buttons_layout.addWidget(self.refresh_repairs_button)
        buttons_layout.addWidget(self.pay_repair_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

    def setup_new_transaction_tab(self):
        """Configure l'onglet de nouvelle transaction"""
        layout = QFormLayout(self.new_transaction_tab)

        # Montant
        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setRange(-1000000, 1000000)
        self.amount_edit.setSingleStep(100)
        self.amount_edit.setPrefix("DA ")
        self.amount_edit.setValue(0)
        layout.addRow("Montant*:", self.amount_edit)

        # Description
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("Description de la transaction")
        layout.addRow("Description:", self.description_edit)

        # Référence
        self.reference_edit = QLineEdit()
        self.reference_edit.setPlaceholderText("Numéro de référence")
        layout.addRow("Référence:", self.reference_edit)

        # Date
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        layout.addRow("Date:", self.date_edit)

        # Réparation associée
        self.repair_combo = QComboBox()
        self.repair_combo.addItem("Aucune", None)
        layout.addRow("Réparation associée:", self.repair_combo)

        # Bouton d'enregistrement
        self.save_transaction_button = QPushButton("Enregistrer la transaction")
        layout.addRow("", self.save_transaction_button)

        # Note explicative
        note = QLabel("Note: Un montant positif représente un paiement du client (crédit). Un montant négatif représente une dette (débit).")
        note.setWordWrap(True)
        layout.addRow(note)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.refresh_transactions_button.clicked.connect(self.load_transactions)
        self.refresh_repairs_button.clicked.connect(self.load_repairs)
        self.save_transaction_button.clicked.connect(self.save_transaction)
        self.pay_repair_button.clicked.connect(self.pay_repair)

        # Activer/désactiver le bouton de paiement en fonction de la sélection
        self.repairs_table.itemSelectionChanged.connect(self.update_pay_button_state)

    def update_pay_button_state(self):
        """Met à jour l'état du bouton de paiement en fonction de la sélection"""
        self.pay_repair_button.setEnabled(len(self.repairs_table.selectedItems()) > 0)

    def cleanup(self):
        """Nettoie les ressources lors de la fermeture du dialogue"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("Session de base de données fermée")

    def load_customer_data(self):
        """Charge les données du client"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la méthode de chargement
            loop.run_until_complete(self._load_customer_async())

            # Fermer la boucle
            loop.close()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors du chargement des données du client: {e}")
            import traceback
            traceback.print_exc()

    async def _load_customer_async(self):
        """Charge les données du client de manière asynchrone"""
        try:
            # Charger le client
            self.customer = await self.service.get(self.customer_id)
            if not self.customer:
                QMessageBox.critical(self, "Erreur", "Client non trouvé")
                self.reject()
                return

            # Mettre à jour l'en-tête
            self.client_name_label.setText(self.customer.name)
            self.client_balance_label.setText(f"{self.customer.current_balance:.2f} DA")

            # Obtenir le statut de crédit
            credit_status = await self.service.get_customer_credit_status(self.customer_id)
            self.client_credit_label.setText(f"{credit_status['available_credit']:.2f} DA")

            # Charger les transactions
            transactions = await self.service.get_customer_transactions(self.customer_id)

            # Effacer le tableau des transactions
            self.transactions_table.setRowCount(0)

            # Remplir le tableau des transactions
            for i, transaction in enumerate(transactions):
                self.transactions_table.insertRow(i)

                # Date
                date_item = QTableWidgetItem(transaction.transaction_date.strftime("%d/%m/%Y %H:%M"))
                self.transactions_table.setItem(i, 0, date_item)

                # Montant
                amount_item = QTableWidgetItem(f"{transaction.amount:.2f} DA")
                amount_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                if transaction.amount < 0:
                    amount_item.setForeground(Qt.GlobalColor.red)
                else:
                    amount_item.setForeground(Qt.GlobalColor.darkGreen)
                self.transactions_table.setItem(i, 1, amount_item)

                # Description
                description_item = QTableWidgetItem(transaction.description or "")
                self.transactions_table.setItem(i, 2, description_item)

                # Référence
                reference_item = QTableWidgetItem(transaction.reference_number or "")
                self.transactions_table.setItem(i, 3, reference_item)

                # Réparation
                repair_item = QTableWidgetItem(str(transaction.repair_order_id or ""))
                self.transactions_table.setItem(i, 4, repair_item)

                # Traité par
                processor_item = QTableWidgetItem(str(transaction.processed_by or ""))
                self.transactions_table.setItem(i, 5, processor_item)

            # Charger les réparations
            repairs = await self.service.get_customer_repair_orders(self.customer_id)

            # Effacer le tableau des réparations
            self.repairs_table.setRowCount(0)

            # Remplir le tableau des réparations
            for i, repair in enumerate(repairs):
                self.repairs_table.insertRow(i)

                # Numéro
                number_item = QTableWidgetItem(repair.number)
                self.repairs_table.setItem(i, 0, number_item)

                # Date
                date_item = QTableWidgetItem(repair.created_at.strftime("%d/%m/%Y"))
                self.repairs_table.setItem(i, 1, date_item)

                # Statut
                status_item = QTableWidgetItem(repair.status.value)
                self.repairs_table.setItem(i, 2, status_item)

                # Montant total
                total_item = QTableWidgetItem(f"{repair.final_amount:.2f} DA")
                total_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.repairs_table.setItem(i, 3, total_item)

                # Payé
                paid_item = QTableWidgetItem(f"{repair.total_paid:.2f} DA")
                paid_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.repairs_table.setItem(i, 4, paid_item)

                # Reste à payer
                remaining = repair.final_amount - repair.total_paid
                remaining_item = QTableWidgetItem(f"{remaining:.2f} DA")
                remaining_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                if remaining > 0:
                    remaining_item.setForeground(Qt.GlobalColor.red)
                self.repairs_table.setItem(i, 5, remaining_item)

                # Statut paiement
                payment_status_item = QTableWidgetItem(repair.payment_status.value)
                self.repairs_table.setItem(i, 6, payment_status_item)

                # Stocker l'ID de la réparation dans les données de la ligne
                number_item.setData(Qt.ItemDataRole.UserRole, repair.id)

            # Charger les réparations dans le combo
            # Effacer le combo
            self.repair_combo.clear()
            self.repair_combo.addItem("Aucune", None)

            # Remplir le combo
            for repair in repairs:
                # N'ajouter que les réparations qui ne sont pas entièrement payées
                if repair.payment_status != "paid":
                    remaining = repair.final_amount - repair.total_paid
                    self.repair_combo.addItem(f"{repair.number} - {remaining:.2f} DA", repair.id)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()

    def load_transactions(self):
        """Charge les transactions du client"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la méthode de chargement
            loop.run_until_complete(self._load_customer_async())

            # Fermer la boucle
            loop.close()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors du chargement des transactions: {e}")
            import traceback
            traceback.print_exc()

    def load_repairs(self):
        """Charge les réparations du client"""
        self.load_transactions()  # Utiliser la même méthode pour recharger toutes les données

    def save_transaction(self):
        """Enregistre une nouvelle transaction"""
        # Vérifier le montant
        amount = self.amount_edit.value()
        if amount == 0:
            QMessageBox.warning(self, "Validation", "Le montant ne peut pas être zéro.")
            return

        # Récupérer les données
        description = self.description_edit.text().strip()
        reference = self.reference_edit.text().strip()
        date = self.date_edit.date().toPyDate()
        repair_id = self.repair_combo.currentData()

        # Créer la transaction
        transaction_data = CustomerTransactionPydantic(
            customer_id=self.customer_id,
            repair_order_id=repair_id,
            amount=amount,
            transaction_date=datetime.combine(date, datetime.min.time()).replace(tzinfo=timezone.utc),
            description=description or None,
            reference_number=reference or None,
            processed_by=None  # TODO: Ajouter l'ID de l'utilisateur connecté
        )

        # Enregistrer la transaction
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la méthode d'enregistrement
            async def save_and_reload():
                # Utiliser FinanceService pour enregistrer la transaction
                finance_service = FinanceService(self.service.db)

                await finance_service.record_customer_transaction(
                    customer_id=transaction_data.customer_id,
                    amount=transaction_data.amount,
                    description=transaction_data.description or "Transaction manuelle",
                    transaction_type=transaction_data.transaction_type,
                    reference_number=transaction_data.reference_number,  # Si vide, sera généré automatiquement
                    repair_order_id=transaction_data.repair_order_id,
                    processed_by=transaction_data.processed_by or 1,
                    transaction_date=transaction_data.transaction_date,
                    auto_generate_reference=True,  # Génération automatique de référence unifiée
                    # Pas d'écriture trésorerie automatique pour les transactions manuelles
                )

                # Recharger les données
                await self._load_customer_async()

                # Réinitialiser le formulaire
                self.amount_edit.setValue(0)
                self.description_edit.clear()
                self.reference_edit.clear()
                self.date_edit.setDate(QDate.currentDate())
                self.repair_combo.setCurrentIndex(0)

                # Notifier l'utilisateur
                QMessageBox.information(self, "Succès", "Transaction enregistrée avec succès.")

            # Exécuter la méthode
            loop.run_until_complete(save_and_reload())

            # Fermer la boucle
            loop.close()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors de l'enregistrement de la transaction: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement de la transaction: {str(e)}")



    def pay_repair(self):
        """Enregistre un paiement pour une réparation"""
        # Vérifier si une réparation est sélectionnée
        selected_rows = self.repairs_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "Validation", "Veuillez sélectionner une réparation.")
            return

        # Récupérer l'ID de la réparation
        repair_id = selected_rows[0].data(Qt.ItemDataRole.UserRole)
        if not repair_id:
            QMessageBox.warning(self, "Validation", "Réparation invalide.")
            return

        # Récupérer le montant restant à payer
        remaining = float(selected_rows[5].text().replace(" DA", ""))
        if remaining <= 0:
            QMessageBox.information(self, "Information", "Cette réparation est déjà entièrement payée.")
            return

        # Demander le montant du paiement
        from PyQt6.QtWidgets import QInputDialog
        amount, ok = QInputDialog.getDouble(
            self, "Paiement", f"Montant à payer (max {remaining:.2f} DA):",
            remaining, 0, remaining, 2
        )

        if not ok or amount <= 0:
            return

        # Demander la méthode de paiement
        from PyQt6.QtWidgets import QComboBox, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel

        method_dialog = QDialog(self)
        method_dialog.setWindowTitle("Méthode de paiement")
        method_dialog.setModal(True)

        layout = QVBoxLayout(method_dialog)
        layout.addWidget(QLabel("Sélectionnez la méthode de paiement:"))

        method_combo = QComboBox()
        method_combo.addItem("Espèces", TreasuryPaymentMethod.cash)
        method_combo.addItem("Carte bancaire", TreasuryPaymentMethod.card)
        method_combo.addItem("Virement", TreasuryPaymentMethod.transfer)
        method_combo.addItem("Chèque", TreasuryPaymentMethod.check)
        layout.addWidget(method_combo)

        button_layout = QHBoxLayout()
        ok_button = QPushButton("OK")
        cancel_button = QPushButton("Annuler")
        ok_button.clicked.connect(method_dialog.accept)
        cancel_button.clicked.connect(method_dialog.reject)
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        if method_dialog.exec() != QDialog.DialogCode.Accepted:
            return

        payment_method = method_combo.currentData()

        # Enregistrer le paiement via FinanceService
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la méthode d'enregistrement
            async def save_and_reload():
                # Utiliser FinanceService pour le paiement de réparation client
                finance_service = FinanceService(self.service.db)

                customer_transaction, repair_payment = await finance_service.pay_customer_repair(
                    customer_id=self.customer_id,
                    repair_id=repair_id,
                    amount=amount,
                    method=payment_method,
                    processed_by=1,  # TODO: Ajouter l'ID de l'utilisateur connecté
                    reference_number=None,  # Laisser FinanceService générer une référence unifiée
                )

                # Recharger les données
                await self._load_customer_async()

                # Notifier l'utilisateur
                QMessageBox.information(self, "Succès",
                    f"Paiement de {amount:.2f} DA enregistré avec succès.\n"
                    f"Transaction client: #{customer_transaction.id}\n"
                    f"Paiement réparation: #{repair_payment.id}")

            # Exécuter la méthode
            loop.run_until_complete(save_and_reload())

            # Fermer la boucle
            loop.close()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors de l'enregistrement du paiement: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement du paiement: {str(e)}")
