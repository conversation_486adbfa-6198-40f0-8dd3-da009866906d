"""
Widget pour afficher le solde d'un client.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QPushButton, QSizePolicy
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont, QColor, QPalette
import asyncio
from datetime import datetime

from app.core.services.customer_service import CustomerService
from app.utils.database import SessionLocal
from app.utils.event_bus import event_bus

class CustomerBalanceWidget(QWidget):
    """Widget pour afficher le solde d'un client"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = CustomerService(self.db)

        # Données
        self.customer_id = None
        self.balance_data = None

        # Configuration de l'interface
        self.setup_ui()
        
        # Connexion au signal payment_processed du event_bus
        event_bus.payment_processed.connect(self.on_payment_processed)

    def __del__(self):
        """Destructeur pour fermer la session"""
        # Déconnecter du bus d'événements
        try:
            event_bus.payment_processed.disconnect(self.on_payment_processed)
        except:
            pass  # Ignorer les erreurs de déconnexion
            
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        title_layout = QHBoxLayout()

        title_label = QLabel("Solde client")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        title_layout.addWidget(title_label)

        self.refresh_button = QPushButton("Actualiser")
        self.refresh_button.clicked.connect(self.refresh_balance)
        title_layout.addWidget(self.refresh_button)

        main_layout.addLayout(title_layout)

        # Cadre principal
        main_frame = QFrame()
        main_frame.setFrameShape(QFrame.Shape.StyledPanel)
        main_frame.setFrameShadow(QFrame.Shadow.Raised)
        main_frame.setStyleSheet("background-color: #f5f5f5; border-radius: 5px;")

        frame_layout = QVBoxLayout(main_frame)

        # Nom du client
        self.customer_name_label = QLabel("Aucun client sélectionné")
        self.customer_name_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.customer_name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(self.customer_name_label)

        # Solde actuel
        balance_layout = QHBoxLayout()

        balance_title = QLabel("Solde actuel:")
        balance_title.setStyleSheet("font-weight: bold;")
        balance_layout.addWidget(balance_title)

        self.balance_label = QLabel("0.00 DA")
        self.balance_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        self.balance_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        balance_layout.addWidget(self.balance_label)

        frame_layout.addLayout(balance_layout)

        # Crédit disponible
        credit_layout = QHBoxLayout()

        credit_title = QLabel("Crédit disponible:")
        credit_title.setStyleSheet("font-weight: bold;")
        credit_layout.addWidget(credit_title)

        self.credit_label = QLabel("0.00 DA")
        self.credit_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        self.credit_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        credit_layout.addWidget(self.credit_label)

        frame_layout.addLayout(credit_layout)

        # Montant dû
        due_layout = QHBoxLayout()

        due_title = QLabel("Montant dû:")
        due_title.setStyleSheet("font-weight: bold;")
        due_layout.addWidget(due_title)

        self.due_label = QLabel("0.00 DA")
        self.due_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #dc3545;")
        self.due_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        due_layout.addWidget(self.due_label)

        frame_layout.addLayout(due_layout)

        # Montant payé
        paid_layout = QHBoxLayout()

        paid_title = QLabel("Montant payé:")
        paid_title.setStyleSheet("font-weight: bold;")
        paid_layout.addWidget(paid_title)

        self.paid_label = QLabel("0.00 DA")
        self.paid_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        self.paid_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        paid_layout.addWidget(self.paid_label)

        frame_layout.addLayout(paid_layout)

        # Statistiques
        stats_layout = QHBoxLayout()

        # Factures en attente
        self.pending_invoices_frame = QFrame()
        self.pending_invoices_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.pending_invoices_frame.setStyleSheet("background-color: #fff3cd; border-radius: 3px;")

        pending_layout = QVBoxLayout(self.pending_invoices_frame)

        self.pending_invoices_count = QLabel("0")
        self.pending_invoices_count.setStyleSheet("font-size: 18px; font-weight: bold; color: #856404;")
        self.pending_invoices_count.setAlignment(Qt.AlignmentFlag.AlignCenter)
        pending_layout.addWidget(self.pending_invoices_count)

        pending_label = QLabel("Factures en attente")
        pending_label.setStyleSheet("color: #856404;")
        pending_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        pending_layout.addWidget(pending_label)

        stats_layout.addWidget(self.pending_invoices_frame)

        # Factures en retard
        self.overdue_invoices_frame = QFrame()
        self.overdue_invoices_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.overdue_invoices_frame.setStyleSheet("background-color: #f8d7da; border-radius: 3px;")

        overdue_layout = QVBoxLayout(self.overdue_invoices_frame)

        self.overdue_invoices_count = QLabel("0")
        self.overdue_invoices_count.setStyleSheet("font-size: 18px; font-weight: bold; color: #721c24;")
        self.overdue_invoices_count.setAlignment(Qt.AlignmentFlag.AlignCenter)
        overdue_layout.addWidget(self.overdue_invoices_count)

        overdue_label = QLabel("Factures en retard")
        overdue_label.setStyleSheet("color: #721c24;")
        overdue_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        overdue_layout.addWidget(overdue_label)

        stats_layout.addWidget(self.overdue_invoices_frame)

        frame_layout.addLayout(stats_layout)

        main_layout.addWidget(main_frame)

        # Initialiser l'interface
        self.clear()

    def set_customer(self, customer_id):
        """Définit le client à afficher"""
        # Si le client est le même, forcer un rafraîchissement complet
        if self.customer_id == customer_id:
            # Fermer la session existante et en créer une nouvelle
            if hasattr(self, 'db') and self.db:
                self.db.close()

            # Créer une nouvelle session
            from app.utils.database import SessionLocal
            self.db = SessionLocal()
            self.service = CustomerService(self.db)

        self.customer_id = customer_id
        self.refresh_balance()

    def clear(self):
        """Efface les données affichées"""
        self.customer_id = None
        self.balance_data = None
        self.update_ui()

    def refresh_balance(self):
        """Rafraîchit les données de solde"""
        if not self.customer_id:
            return

        print(f"Demande de rafraîchissement du solde pour le client {self.customer_id}")

        # Utiliser QTimer pour exécuter le chargement de manière asynchrone
        QTimer.singleShot(0, self._refresh_balance_wrapper)
        
    def on_payment_processed(self, payment_id):
        """Appelé lorsqu'un paiement est traité"""
        print(f"Signal payment_processed reçu pour le paiement {payment_id}")
        if self.customer_id:
            self.refresh_balance()

    def _refresh_balance_wrapper(self):
        """Wrapper pour exécuter refresh_balance_async de manière asynchrone"""
        # Fermer la session existante et en créer une nouvelle pour s'assurer d'avoir des données à jour
        if hasattr(self, 'db') and self.db:
            self.db.close()

        # Créer une nouvelle session
        from app.utils.database import SessionLocal
        self.db = SessionLocal()
        self.service = CustomerService(self.db)

        # Créer une nouvelle boucle d'événements
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._refresh_balance_async())
        except Exception as e:
            print(f"Erreur dans _refresh_balance_wrapper: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def _refresh_balance_async(self):
        """Rafraîchit les données de solde de manière asynchrone"""
        try:
            # Récupérer les données de solde
            self.balance_data = await self.service.get_customer_balance(self.customer_id)

            # Mettre à jour l'interface
            self.update_ui()
            print(f"Solde client actualisé avec succès pour le client {self.customer_id}")
        except Exception as e:
            print(f"Erreur lors du rafraîchissement du solde: {e}")
            import traceback
            traceback.print_exc()

    def update_ui(self):
        """Met à jour l'interface utilisateur avec les données de solde"""
        if not self.balance_data:
            # Aucune donnée, afficher les valeurs par défaut
            self.customer_name_label.setText("Aucun client sélectionné")
            self.balance_label.setText("0.00 DA")
            self.credit_label.setText("0.00 DA")
            self.due_label.setText("0.00 DA")
            self.paid_label.setText("0.00 DA")
            self.pending_invoices_count.setText("0")
            self.overdue_invoices_count.setText("0")
            return

        # Mettre à jour les données
        customer = self.balance_data.get("customer")
        if customer:
            self.customer_name_label.setText(customer.name)

        # Solde
        balance = self.balance_data.get("balance", 0)
        self.balance_label.setText(f"{balance:.2f} DA")
        print(f"Mise à jour du solde affiché: {balance:.2f} DA")

        # Couleur du solde
        if balance < 0:
            self.balance_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #dc3545;")
        elif balance > 0:
            self.balance_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        else:
            self.balance_label.setStyleSheet("font-size: 16px; font-weight: bold;")

        # Crédit disponible
        credit = self.balance_data.get("available_credit", 0)
        self.credit_label.setText(f"{credit:.2f} DA")

        # Montant dû
        due = self.balance_data.get("total_due", 0)
        self.due_label.setText(f"{due:.2f} DA")

        # Montant payé
        paid = self.balance_data.get("total_paid", 0)
        self.paid_label.setText(f"{paid:.2f} DA")

        # Factures en attente
        pending_invoices = self.balance_data.get("pending_invoices", [])
        self.pending_invoices_count.setText(str(len(pending_invoices)))

        # Factures en retard
        overdue_invoices = self.balance_data.get("overdue_invoices", [])
        self.overdue_invoices_count.setText(str(len(overdue_invoices)))

        # Forcer la mise à jour de l'interface
        self.repaint()
