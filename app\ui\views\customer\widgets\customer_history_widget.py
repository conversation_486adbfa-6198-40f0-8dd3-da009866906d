from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QHBoxLayout, QPushButton,
    QTableWidget, QTableWidgetItem, QDialog, QTextEdit,
    QLineEdit, QComboBox, QDateEdit, QFileDialog, QCheckBox, QHeaderView
)
from PyQt6.QtCore import Qt, QDate
from datetime import datetime
import csv
import os

from app.core.services.customer_service import CustomerService
from app.utils.database import SessionLocal
from app.utils.event_bus import event_bus
from sqlalchemy import text
from app.utils.pdf_generator import PDFGenerator
from app.core.models.sale import PaymentStatus as SalePaymentStatus
from app.core.models.repair import PaymentStatus as RepairPaymentStatus

class CustomerHistoryWidget(QWidget):
    """
    Onglet Historique client: historique complet (achats/ventes, réparations, versements)
    - Colonnes: Date, Type, Référence, Description, Montant, Statut
    - Bouton "Détails" pour afficher un résumé détaillé de l'opération sélectionnée

    Modifié: 2025-09-13 - Ajout de la traduction des statuts en français
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.service = CustomerService(self.db)
        self.customer_id = None
        self._build_ui()
        
        # Connexion au signal payment_processed du event_bus
        event_bus.payment_processed.connect(self.on_payment_processed)

    def __del__(self):
        try:
            # Déconnecter du bus d'événements
            try:
                event_bus.payment_processed.disconnect(self.on_payment_processed)
            except:
                pass  # Ignorer les erreurs de déconnexion
                
            if hasattr(self, 'db') and self.db:
                self.db.close()
        except Exception:
            pass
            
    def on_payment_processed(self, payment_id):
        """Appelé lorsqu'un paiement est traité"""
        print(f"Signal payment_processed reçu pour le paiement {payment_id} dans CustomerHistoryWidget")
        if self.customer_id:
            self.refresh()

    def _get_sale_payment_status_display(self, status):
        """Retourne l'affichage du statut de paiement de vente en français"""
        if not status:
            return ""

        # Dictionnaire de traduction basé sur les valeurs string
        status_translations = {
            "pending": "En attente",
            "partial": "Partiellement payé",
            "paid": "Payé",
            "overdue": "En retard",
            "refunded": "Remboursé"
        }

        # Convertir en string pour la comparaison
        status_str = str(status).lower()

        # Si c'est un enum, utiliser sa valeur
        if hasattr(status, 'value'):
            status_str = status.value.lower()

        return status_translations.get(status_str, str(status))

    def _get_repair_payment_status_display(self, status):
        """Retourne l'affichage du statut de paiement de réparation en français"""
        if not status:
            return ""

        # Dictionnaire de traduction basé sur les valeurs string
        status_translations = {
            "pending": "En attente",
            "partial": "Partiellement payé",
            "paid": "Payé",
            "overdue": "En retard",
            "cancelled": "Annulé",
            "delivered": "Livré"
        }

        # Convertir en string pour la comparaison
        status_str = str(status).lower()

        # Si c'est un enum, utiliser sa valeur
        if hasattr(status, 'value'):
            status_str = status.value.lower()

        return status_translations.get(status_str, str(status))

    def _build_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # En-tête
        head = QHBoxLayout()
        title = QLabel("Historique client")
        title.setStyleSheet("font-size: 16px; font-weight: bold;")
        head.addWidget(title)
        head.addStretch(1)
        self.details_btn = QPushButton("Détails")
        self.details_btn.setEnabled(False)
        head.addWidget(self.details_btn)
        layout.addLayout(head)

        # Barre de filtres
        filters = QHBoxLayout()
        self.filter_type = QComboBox()
        self.filter_type.addItem("Tous", userData=None)
        self.filter_type.addItem("Vente", userData="Vente")
        self.filter_type.addItem("Réparation", userData="Réparation")
        self.filter_type.addItem("Versement", userData="Versement")

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher (référence, description, statut)...")

        self.date_enable = QCheckBox("Filtrer par période")
        self.date_enable.setChecked(False)
        self.date_from = QDateEdit()
        self.date_from.setCalendarPopup(True)
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_from.setEnabled(False)
        self.date_to = QDateEdit()
        self.date_to.setCalendarPopup(True)
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setEnabled(False)

        export_csv_btn = QPushButton("Exporter CSV")
        export_pdf_btn = QPushButton("Exporter PDF")

        filters.addWidget(QLabel("Type:"))
        filters.addWidget(self.filter_type)
        filters.addWidget(QLabel("Recherche:"))
        filters.addWidget(self.search_input)
        filters.addWidget(self.date_enable)
        filters.addWidget(QLabel("Du:"))
        filters.addWidget(self.date_from)
        filters.addWidget(QLabel("Au:"))
        filters.addWidget(self.date_to)
        filters.addStretch(1)
        filters.addWidget(export_csv_btn)
        filters.addWidget(export_pdf_btn)
        layout.addLayout(filters)

        # Table
        self.table = QTableWidget(0, 6)
        self.table.setHorizontalHeaderLabels(["Date", "Type", "Référence", "Description", "Montant", "Statut"])
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)

        # Configuration avancée du tableau pour une meilleure présentation
        header = self.table.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Ajouter des lignes alternées pour une meilleure lisibilité
        self.table.setAlternatingRowColors(True)

        # Désactiver l'édition directe dans le tableau
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.table.setShowGrid(True)
        self.table.setSortingEnabled(True)

        layout.addWidget(self.table)

        # Connexions
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.details_btn.clicked.connect(self._show_details_dialog)
        self.filter_type.currentIndexChanged.connect(self._apply_filters)
        self.search_input.textChanged.connect(self._apply_filters)
        self.date_enable.stateChanged.connect(self._on_date_toggle)
        self.date_from.dateChanged.connect(self._apply_filters)
        self.date_to.dateChanged.connect(self._apply_filters)
        export_csv_btn.clicked.connect(self._export_csv)
        export_pdf_btn.clicked.connect(self._export_pdf)

        # Données normalisées
        self._all_rows = []
        self._rows_data = []  # liste filtrée parallèle au QTableWidget

    def set_customer(self, customer_id: int):
        self.customer_id = customer_id
        self.refresh()

    def clear(self):
        self.customer_id = None
        self.table.setRowCount(0)
        self._rows_data = []
        self.details_btn.setEnabled(False)

    def _fmt_date(self, dt):
        if not dt:
            return ""
        if isinstance(dt, (datetime,)):
            return dt.strftime("%Y-%m-%d %H:%M")
        return str(dt)

    def refresh(self):
        if not self.customer_id:
            self.clear()
            return
        try:
            # Utiliser les méthodes du service pour plus de robustesse
            import asyncio

            async def load_data():
                # Récupérer ventes via service
                sales = await self.service.get_customer_sales(self.customer_id, limit=500)

                # Récupérer transactions via service
                transactions = await self.service.get_customer_transactions(self.customer_id, limit=500)

                # Récupérer réparations via requête SQL (pas de méthode service disponible)
                repairs = self.service.db.execute(
                    text("""
                    SELECT id, created_at, final_amount, total_paid, payment_status
                    FROM repair_orders
                    WHERE customer_id = :cid
                    ORDER BY created_at DESC
                    LIMIT 500
                    """),
                    {"cid": self.customer_id}
                ).fetchall()

                return sales, transactions, repairs

            # Exécuter de manière asynchrone
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            sales, transactions, repairs = loop.run_until_complete(load_data())
            loop.close()

            # Fusionner en une liste normalisée
            rows = []

            # Traiter les ventes (objets Sale)
            for sale in sales:
                due = (sale.final_amount or 0.0) - (sale.total_paid or 0.0)
                rows.append({
                    'type': 'Vente',
                    'id': sale.id,
                    'date': sale.date,
                    'reference': f"SALE-{sale.id}",
                    'description': f"Vente - dû: {due:.2f} DA",
                    'amount': float(sale.final_amount or 0.0),
                    'status': self._get_sale_payment_status_display(sale.payment_status)
                })

            # Traiter les réparations (tuples SQL)
            for r in repairs:
                due = (r[2] or 0.0) - (r[3] or 0.0)
                rows.append({
                    'type': 'Réparation',
                    'id': r[0],
                    'date': r[1],
                    'reference': f"REPAIR-{r[0]}",
                    'description': f"Réparation - dû: {due:.2f} DA",
                    'amount': float(r[2] or 0.0),
                    'status': self._get_repair_payment_status_display(r[4]) if r[4] is not None else ""
                })

            # Traiter les transactions (objets CustomerTransaction)
            for tx in transactions:
                rows.append({
                    'type': 'Versement',
                    'id': tx.id,
                    'date': tx.transaction_date,
                    'reference': tx.reference_number or f"TX-{tx.id}",
                    'description': tx.description or tx.transaction_type or "Transaction",
                    'amount': float(tx.amount or 0.0),
                    'status': tx.transaction_type or ""
                })

            # Trier par date desc dans le cas où l'ordre varie
            # Normaliser les dates avant le tri
            def normalize_date(date_value):
                if not date_value:
                    return datetime.min
                if isinstance(date_value, datetime):
                    return date_value
                if isinstance(date_value, str):
                    try:
                        return datetime.fromisoformat(date_value.replace('Z', '+00:00'))
                    except:
                        try:
                            return datetime.strptime(date_value, "%Y-%m-%d %H:%M:%S.%f")
                        except:
                            try:
                                return datetime.strptime(date_value, "%Y-%m-%d %H:%M:%S")
                            except:
                                return datetime.min
                return datetime.min

            rows.sort(key=lambda x: normalize_date(x.get('date')), reverse=True)

            # Stocker et appliquer les filtres
            self._all_rows = rows
            self._apply_filters()
        except Exception:
            self.clear()

    def _on_selection_changed(self):
        has_sel = len(self.table.selectedItems()) > 0
        self.details_btn.setEnabled(has_sel)

    def _on_date_toggle(self, state):
        enabled = self.date_enable.isChecked()
        self.date_from.setEnabled(enabled)
        self.date_to.setEnabled(enabled)
        self._apply_filters()

    def _parse_row_datetime(self, value):
        if not value:
            return None
        if isinstance(value, datetime):
            return value
        # Fallback: try parsing common formats
        try:
            return datetime.fromisoformat(str(value))
        except Exception:
            try:
                return datetime.strptime(str(value), "%Y-%m-%d %H:%M:%S")
            except Exception:
                return None

    def _apply_filters(self):
        # Gather filters
        type_filter = self.filter_type.currentData()
        query = (self.search_input.text() or "").strip().lower()
        use_dates = self.date_enable.isChecked()

        if use_dates:
            df = self.date_from.date()
            dt = self.date_to.date()
            date_from_py = datetime(df.year(), df.month(), df.day(), 0, 0, 0)
            date_to_py = datetime(dt.year(), dt.month(), dt.day(), 23, 59, 59)
        else:
            date_from_py = None
            date_to_py = None

        # Filter rows
        filtered = []
        for row in self._all_rows:
            # Type filter
            if type_filter and row.get('type') != type_filter:
                continue
            # Date filter
            if use_dates:
                rdt = self._parse_row_datetime(row.get('date'))
                if not rdt or rdt < date_from_py or rdt > date_to_py:
                    continue
            # Text search across reference, description, status
            if query:
                ref = str(row.get('reference', '')).lower()
                desc = str(row.get('description', '')).lower()
                status = str(row.get('status', '')).lower()
                if query not in ref and query not in desc and query not in status:
                    continue
            filtered.append(row)

        # Update table
        self.table.setRowCount(0)
        self._rows_data = filtered
        for row in filtered:
            r = self.table.rowCount()
            self.table.insertRow(r)
            self.table.setItem(r, 0, QTableWidgetItem(self._fmt_date(row['date'])))
            self.table.setItem(r, 1, QTableWidgetItem(row['type']))
            self.table.setItem(r, 2, QTableWidgetItem(str(row['reference'])))
            self.table.setItem(r, 3, QTableWidgetItem(row.get('description', "")))
            self.table.setItem(r, 4, QTableWidgetItem(f"{row.get('amount', 0.0):.2f}"))
            self.table.setItem(r, 5, QTableWidgetItem(row.get('status', "")))

        self.details_btn.setEnabled(self.table.rowCount() > 0)

    def _export_csv(self):
        # Ask path
        default_dir = os.path.join(os.getcwd(), "exports")
        os.makedirs(default_dir, exist_ok=True)
        default_path = os.path.join(default_dir, f"customer_history_{self.customer_id or 'na'}.csv")
        path, _ = QFileDialog.getSaveFileName(self, "Exporter en CSV", default_path, "CSV Files (*.csv)")
        if not path:
            return
        # Write CSV
        headers = ["Date", "Type", "Référence", "Description", "Montant", "Statut"]
        try:
            with open(path, "w", newline="", encoding="utf-8") as f:
                writer = csv.writer(f)
                writer.writerow(headers)
                for row in self._rows_data:
                    writer.writerow([
                        self._fmt_date(row.get('date')),
                        row.get('type', ''),
                        row.get('reference', ''),
                        row.get('description', ''),
                        f"{row.get('amount', 0.0):.2f}",
                        row.get('status', ''),
                    ])
        except Exception as e:
            # Silencieux pour l'UI; pourrait afficher un QMessageBox si désiré
            print(f"Erreur export CSV: {e}")

    def _export_pdf(self):
        # Ask path
        default_dir = os.path.join(os.getcwd(), "output")
        os.makedirs(default_dir, exist_ok=True)
        default_path = os.path.join(default_dir, f"customer_history_{self.customer_id or 'na'}.pdf")
        path, _ = QFileDialog.getSaveFileName(self, "Exporter en PDF", default_path, "PDF Files (*.pdf)")
        if not path:
            return
        try:
            pdf = PDFGenerator(path)
            pdf.add_title("Historique client")
            pdf.add_subtitle(f"Client #{self.customer_id}")
            pdf.add_text(f"Exporté le: {datetime.now().strftime('%d/%m/%Y %H:%M')}")
            pdf.add_spacer(1)
            # Simple table-like listing
            for row in self._rows_data:
                line = f"{self._fmt_date(row.get('date'))} | {row.get('type','')} | {row.get('reference','')} | {row.get('description','')} | {row.get('amount',0.0):.2f} | {row.get('status','')}"
                pdf.add_text(line)
            pdf.generate()
        except Exception as e:
            print(f"Erreur export PDF: {e}")

    def _show_details_dialog(self):
        row = self.table.currentRow()
        if row < 0 or row >= len(self._rows_data):
            return
        data = self._rows_data[row]
        dlg = _HistoryDetailsDialog(self, data)
        dlg.exec()


class _HistoryDetailsDialog(QDialog):
    def __init__(self, parent=None, data: dict | None = None):
        super().__init__(parent)
        self.setWindowTitle("Détails de l'opération")
        self.resize(520, 420)
        layout = QVBoxLayout(self)

        # Texte détaillé
        text = QTextEdit()
        text.setReadOnly(True)
        # Présentation simple des champs
        lines = []
        if data:
            for k in ["type", "id", "date", "reference", "description", "amount", "status"]:
                v = data.get(k, "")
                lines.append(f"{k}: {v}")
        text.setText("\n".join(lines))
        layout.addWidget(text)

        # Boutons d'action (placeholder pour ouvrir vue spécifique)
        btns = QHBoxLayout()
        close_btn = QPushButton("Fermer")
        close_btn.clicked.connect(self.accept)
        btns.addStretch(1)
        btns.addWidget(close_btn)
        layout.addLayout(btns)