from PyQt6.QtWidgets import <PERSON>Widget, QVBoxLayout, QLabel, QHBoxLayout, QPushButton, QLineEdit, QComboBox, QFormLayout
from PyQt6.QtCore import Qt

from app.core.services.customer_service import CustomerService
from app.utils.database import SessionLocal

class CustomerOperationsWidget(QWidget):
    """Onglet Opérations clients: ajout de transactions crédit/débit (structure simple)"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.service = CustomerService(self.db)
        self.customer_id = None
        self._build_ui()

    def __del__(self):
        try:
            if hasattr(self, 'db') and self.db:
                self.db.close()
        except Exception:
            pass

    def _build_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        title = QLabel("Opérations clients")
        title.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(title)

        form = QFormLayout()
        form.setLabelAlignment(Qt.AlignmentFlag.AlignLeft)
        form.setFormAlignment(Qt.AlignmentFlag.AlignTop)

        self.type_combo = QComboBox()
        self.type_combo.addItems(["Crédit (+)", "Débit (-)"])
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("Montant")
        self.desc_edit = QLineEdit()
        self.desc_edit.setPlaceholderText("Description (optionnel)")

        form.addRow("Type:", self.type_combo)
        form.addRow("Montant:", self.amount_edit)
        form.addRow("Description:", self.desc_edit)

        layout.addLayout(form)

        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("Ajouter l'opération")
        btn_layout.addWidget(self.add_btn)
        layout.addLayout(btn_layout)

        # Note: wiring réel d'enregistrement sera ajouté après validation
        self.add_btn.clicked.connect(self._not_implemented)

    def _not_implemented(self):
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "Info", "Enregistrement des opérations sera implémenté après validation du design.")

    def set_customer(self, customer_id: int):
        self.customer_id = customer_id

    def clear(self):
        self.customer_id = None
        self.amount_edit.clear()
        self.desc_edit.clear()