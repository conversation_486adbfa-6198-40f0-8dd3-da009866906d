from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QTableWidget, QTableWidgetItem,
    QHBoxLayout, QComboBox, QLineEdit, QDoubleSpinBox, QPushButton, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal

import os
import datetime

from app.core.services.customer_service import CustomerService
from app.core.services.treasury_service import TreasuryService
from app.core.services.finance_service import FinanceService
from app.core.models.customer import CustomerTransactionPydantic
from app.core.models.treasury import TransactionCategory, PaymentMethod as TreasuryPaymentMethod
from sqlalchemy import text
from app.utils.database import SessionLocal
from app.utils.async_utils import schedule_coro
from app.utils.pdf_generator import PDFGenerator
from app.utils.event_bus import event_bus

class CustomerPaymentsWidget(QWidget):
    """Onglet Paiements (client) inspiré de Réparation: liste + formulaire d'ajout de versement.

    - Permet l'enregistrement dans une caisse (optionnelle)
    - Supporte les méthodes de paiement (espèces, carte, virement, chèque, autre)
    - Met à jour la table après enregistrement
    """

    paymentRecorded = pyqtSignal(dict)  # {customer_id}

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.service = CustomerService(self.db)
        self.customer_id = None
        self.current_user_id = None  # à setter par parent si disponible
        self._build_ui()
        
        # Connexion au signal payment_processed du event_bus
        event_bus.payment_processed.connect(self.on_payment_processed)

    def __del__(self):
        try:
            # Déconnecter du bus d'événements
            try:
                event_bus.payment_processed.disconnect(self.on_payment_processed)
            except:
                pass  # Ignorer les erreurs de déconnexion
                
            if hasattr(self, 'db') and self.db:
                self.db.close()
        except Exception:
            pass
            
    def on_payment_processed(self, payment_id):
        """Appelé lorsqu'un paiement est traité"""
        print(f"Signal payment_processed reçu pour le paiement {payment_id}")
        if self.customer_id:
            self.refresh()

    async def _load_cash_registers_async(self):
        try:
            treasury = TreasuryService(self.service.db)
            # Lister les caisses actives
            registers = (
                self.service.db.query(treasury.model)
                .filter(treasury.model.is_active == True)
                .order_by(treasury.model.name.asc())
                .all()
            )
            # Reset (garder index 0 par défaut)
            while self.register_input.count() > 1:
                self.register_input.removeItem(1)
            for r in registers:
                self.register_input.addItem(r.name, userData=r.id)
        except Exception:
            # pas critique pour l'UI
            pass

    def _build_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        title = QLabel("Historique des paiements client")
        title.setObjectName("sectionSubtitle")
        layout.addWidget(title)

        # Tableau
        self.table = QTableWidget(0, 5)
        self.table.setHorizontalHeaderLabels(["Date", "Montant", "Type", "Référence", "Notes"])
        self.table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.table)

        # Formulaire d'ajout de versement / opération
        form = QHBoxLayout()

        self.amount_input = QDoubleSpinBox()
        self.amount_input.setSuffix(" DA")
        self.amount_input.setDecimals(2)
        self.amount_input.setMaximum(10_000_000)
        self.amount_input.setMinimum(0.0)
        self.amount_input.setValue(0.0)

        self.type_input = QComboBox()
        # Mapping simple vers transaction_type
        self.type_input.addItem("Versement", userData="payment")
        self.type_input.addItem("Ajustement", userData="manual")

        # Méthode de paiement
        self.method_input = QComboBox()
        self.method_input.addItem("Espèces", userData="cash")
        self.method_input.addItem("Carte de crédit", userData="credit_card")
        self.method_input.addItem("Virement bancaire", userData="bank_transfer")
        self.method_input.addItem("Chèque", userData="check")
        self.method_input.addItem("Autre", userData="other")

        # Caisse
        self.register_input = QComboBox()
        self.register_input.addItem("(Par défaut)", userData=None)
        # Charger les caisses actives
        schedule_coro(self._load_cash_registers_async())

        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("Référence (optionnel)")

        self.notes_input = QLineEdit()
        self.notes_input.setPlaceholderText("Notes (optionnel)")

        self.add_button = QPushButton("Enregistrer")
        self.add_button.clicked.connect(self._on_add_clicked)

        form.addWidget(QLabel("Montant:"))
        form.addWidget(self.amount_input)
        form.addWidget(QLabel("Type:"))
        form.addWidget(self.type_input)
        form.addWidget(QLabel("Méthode:"))
        form.addWidget(self.method_input)
        form.addWidget(QLabel("Caisse:"))
        form.addWidget(self.register_input)
        form.addWidget(QLabel("Référence:"))
        form.addWidget(self.reference_input)
        form.addWidget(QLabel("Notes:"))
        form.addWidget(self.notes_input)
        form.addWidget(self.add_button)

        layout.addLayout(form)

        # Message si aucun élément
        self.no_items_label = QLabel("Aucune transaction enregistrée")
        self.no_items_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.no_items_label.setVisible(False)
        layout.addWidget(self.no_items_label)

    def set_customer(self, customer_id: int):
        self.customer_id = customer_id
        self.refresh()

    def clear(self):
        self.customer_id = None
        self.table.setRowCount(0)
        self.no_items_label.setVisible(True)

    def refresh(self):
        if not self.customer_id:
            self.clear()
            return
        try:
            # Utiliser la méthode du service pour plus de robustesse
            import asyncio

            async def load_transactions():
                return await self.service.get_customer_transactions(self.customer_id, limit=100)

            # Exécuter de manière asynchrone
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            transactions = loop.run_until_complete(load_transactions())
            loop.close()

            self.table.setRowCount(0)
            for tx in transactions:
                r = self.table.rowCount()
                self.table.insertRow(r)
                self.table.setItem(r, 0, QTableWidgetItem(str(tx.transaction_date) if tx.transaction_date else ""))
                self.table.setItem(r, 1, QTableWidgetItem(f"{float(tx.amount or 0.0):.2f} DA"))
                self.table.setItem(r, 2, QTableWidgetItem(str(tx.transaction_type or "")))
                self.table.setItem(r, 3, QTableWidgetItem(tx.reference_number or ""))
                self.table.setItem(r, 4, QTableWidgetItem(tx.description or ""))
            has = self.table.rowCount() > 0
            self.table.setVisible(has)
            self.no_items_label.setVisible(not has)
        except Exception as e:
            print(f"Erreur lors du rafraîchissement des paiements clients: {e}")
            self.clear()

    def _on_add_clicked(self):
        if not self.customer_id:
            QMessageBox.warning(self, "Paiements", "Aucun client sélectionné.")
            return
        amount = float(self.amount_input.value())
        if amount <= 0:
            QMessageBox.warning(self, "Paiements", "Le montant doit être > 0.")
            return
        ttype = self.type_input.currentData() or "payment"
        method_value = self.method_input.currentData() or "cash"
        register_id = self.register_input.currentData() or None
        reference = self.reference_input.text().strip() or None
        notes = self.notes_input.text().strip() or None

        self.add_button.setEnabled(False)
        schedule_coro(self._record_transaction_async(amount, ttype, method_value, register_id, reference, notes))

    async def _record_transaction_async(self, amount: float, ttype: str, method_value: str, register_id: int | None, reference: str | None, notes: str | None):
        try:
            # Utiliser FinanceService pour enregistrer la transaction avec cohérence trésorerie
            finance_service = FinanceService(self.service.db)

            # Déterminer la méthode de paiement pour la trésorerie
            payment_method = None
            if register_id is not None and amount != 0:
                payment_method = TreasuryPaymentMethod(method_value)

            transaction = await finance_service.record_customer_transaction(
                customer_id=self.customer_id,
                amount=amount,  # positif = versement, négatif possible si besoin d'ajustement
                description=notes or f"Transaction client #{self.customer_id}",
                transaction_type=ttype,
                reference_number=reference,
                processed_by=int(self.current_user_id or 1),
                cash_register_id=register_id if amount != 0 else None,
                payment_method=payment_method,
            )

            # Reset
            self.amount_input.setValue(0.0)
            self.reference_input.clear()
            self.notes_input.clear()
            self.type_input.setCurrentIndex(0)
            self.method_input.setCurrentIndex(0)
            self.register_input.setCurrentIndex(0)
            # Générer un reçu PDF simple
            try:
                output_dir = os.path.join(os.getcwd(), "output")
                os.makedirs(output_dir, exist_ok=True)
                ts = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
                receipt_path = os.path.join(output_dir, f"customer_payment_receipt_{self.customer_id}_{ts}.pdf")
                pdf = PDFGenerator(receipt_path)
                pdf.add_title("REÇU DE PAIEMENT - CLIENT")
                pdf.add_subtitle(f"Client #{self.customer_id}")
                pdf.add_text(f"Date: {datetime.datetime.now().strftime('%d/%m/%Y %H:%M')}")
                pdf.add_text(f"Montant: {amount:.2f} DA")
                pdf.add_text(f"Méthode: {method_value}")
                if reference:
                    pdf.add_text(f"Référence: {reference}")
                if notes:
                    pdf.add_text(f"Notes: {notes}")
                pdf.add_spacer(1)
                pdf.add_text("Merci pour votre paiement.")
                pdf.generate()
            except Exception as _:
                pass

            # Refresh
            self.refresh()
            QMessageBox.information(self, "Paiements", "Opération enregistrée avec succès.")
            # Notify parent
            self.paymentRecorded.emit({"customer_id": self.customer_id})
            
            # Émettre le signal payment_processed
            try:
                event_bus.payment_processed.emit(transaction.id)
            except Exception as e:
                print(f"Erreur lors de l'émission du signal payment_processed: {e}")
                
        except Exception as e:
            QMessageBox.critical(self, "Paiements", f"Erreur lors de l'enregistrement: {e}")
        finally:
            self.add_button.setEnabled(True)