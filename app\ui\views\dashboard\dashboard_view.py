from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QScrollArea, QPushButton, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtCharts import <PERSON><PERSON><PERSON>, QChartView
from PyQt6.QtGui import QIcon

from .widgets.kpi_card import KPICard
from .widgets.chart_widget import <PERSON>air<PERSON><PERSON>, InventoryChart, MaintenanceChart
from ...components.custom_widgets import LoadingOverlay
from app.core.services.reporting_service import ReportingService
from app.utils.database import SessionLocal
import asyncio
from datetime import datetime

class DashboardView(QWidget):
    def __init__(self):
        super().__init__()

        # Créer une session de base de données
        self.db = SessionLocal()
        self.service = ReportingService(self.db)

        # Créer un timer pour le rafraîchissement automatique
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh)

        # Configurer l'interface utilisateur
        self.setup_ui()

        # Lancer le chargement initial des données
        QTimer.singleShot(100, self.init_data)

        # Démarrer le rafraîchissement automatique (toutes les 5 minutes)
        self.start_auto_refresh(300000)  # 5 minutes en millisecondes

    def __del__(self):
        """Destructeur pour fermer la session de base de données"""
        if hasattr(self, 'db') and self.db:
            try:
                self.db.close()
                print("DashboardView: Session de base de données fermée")
            except Exception as e:
                print(f"Erreur lors de la fermeture de la session: {e}")

        # Arrêter le timer de rafraîchissement
        if hasattr(self, 'refresh_timer') and self.refresh_timer.isActive():
            self.refresh_timer.stop()

    def setup_ui(self):
        """Configure l'interface utilisateur du tableau de bord"""
        main_layout = QVBoxLayout(self)

        # En-tête avec bouton de rafraîchissement
        header_layout = QHBoxLayout()

        header = QLabel("Tableau de Bord")
        header.setObjectName("dashboardHeader")
        header_layout.addWidget(header)

        header_layout.addStretch()

        # Bouton de rafraîchissement
        self.refresh_button = QPushButton("Actualiser")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.clicked.connect(self.refresh)
        header_layout.addWidget(self.refresh_button)

        main_layout.addLayout(header_layout)

        # Zone défilante pour le contenu
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # Section KPIs
        kpi_layout = QHBoxLayout()
        self.kpi_widgets = {
            "repairs": KPICard("Réparations en cours", "0", "repair"),
            "inventory": KPICard("Quantité en stock", "0", "inventory"),
            "maintenance": KPICard("Maintenance prévue", "0", "maintenance"),
            "alerts": KPICard("Alertes actives", "0", "alert")
        }
        for kpi in self.kpi_widgets.values():
            kpi_layout.addWidget(kpi)
        content_layout.addLayout(kpi_layout)

        # Section Graphiques
        charts_layout = QGridLayout()

        # Graphique des réparations - Partager la connexion à la base de données
        self.repair_chart = RepairChart(db=self.db)
        charts_layout.addWidget(self.repair_chart, 0, 0)

        # Graphique de l'inventaire - Partager la connexion à la base de données
        self.inventory_chart = InventoryChart(db=self.db)
        charts_layout.addWidget(self.inventory_chart, 0, 1)

        # Graphique de maintenance - Partager la connexion à la base de données
        self.maintenance_chart = MaintenanceChart(db=self.db)
        charts_layout.addWidget(self.maintenance_chart, 1, 0, 1, 2)

        content_layout.addLayout(charts_layout)
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()

    # Nouvelle méthode pour lancer la coroutine de manière correcte
    def init_data(self):
        """Initialise les données du tableau de bord de façon compatible Qt/asyncio"""
        try:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Si la boucle tourne déjà (ex: via QEventLoop), planifier la tâche
                    asyncio.ensure_future(self.load_data())
                else:
                    loop.run_until_complete(self.load_data())
            except RuntimeError:
                # Pas de boucle existante, en créer une temporaire
                loop = asyncio.new_event_loop()
                try:
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.load_data())
                finally:
                    loop.close()
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()

    async def load_data(self):
        """Charge les données du tableau de bord"""
        try:
            self.loading_overlay.show()
        except RuntimeError as e:
            print(f"Erreur lors de l'affichage de l'overlay: {e}")

        try:
            # Charger les KPIs
            try:
                await self.update_kpis()
            except Exception as e:
                print(f"Erreur lors de la mise à jour des KPIs: {e}")
                import traceback
                traceback.print_exc()

            # Charger les graphiques
            try:
                await self.repair_chart.update_data()
            except Exception as e:
                print(f"Erreur lors de la mise à jour du graphique des réparations: {e}")

            try:
                await self.inventory_chart.update_data()
            except Exception as e:
                print(f"Erreur lors de la mise à jour du graphique d'inventaire: {e}")

            try:
                await self.maintenance_chart.update_data()
            except Exception as e:
                print(f"Erreur lors de la mise à jour du graphique de maintenance: {e}")

        finally:
            try:
                if hasattr(self, 'loading_overlay') and self.loading_overlay:
                    self.loading_overlay.hide()
            except RuntimeError as e:
                print(f"Erreur lors de la fermeture de l'overlay: {e}")

    async def update_kpis(self):
        """Met à jour les indicateurs KPI"""
        try:
            # Récupérer les KPIs depuis le service de reporting
            try:
                kpis = await self.service.get_dashboard_kpis()
            except Exception as e:
                print(f"Erreur lors de la récupération des KPIs: {e}")
                # Si la connexion est fermée, essayer de la recréer
                if "This Connection is closed" in str(e):
                    print("Tentative de recréation de la connexion à la base de données...")
                    try:
                        # Fermer la connexion existante si elle existe
                        if hasattr(self, 'db') and self.db:
                            try:
                                self.db.close()
                            except:
                                pass

                        # Créer une nouvelle connexion
                        from app.utils.database import SessionLocal
                        self.db = SessionLocal()
                        from app.core.services.reporting_service import ReportingService
                        self.service = ReportingService(self.db)

                        # Réessayer de récupérer les KPIs
                        kpis = await self.service.get_dashboard_kpis()
                        print("Connexion recréée avec succès")
                    except Exception as inner_e:
                        print(f"Échec de la recréation de la connexion: {inner_e}")
                        return False
                else:
                    return False

            # Mettre à jour les widgets KPI avec les nouvelles valeurs
            try:
                self.kpi_widgets["repairs"].update_value(str(kpis.get("active_repairs", 0)))
                self.kpi_widgets["inventory"].update_value(str(kpis.get("total_stock_quantity", 0)))
                self.kpi_widgets["maintenance"].update_value(str(kpis.get("upcoming_maintenance", 0)))
                self.kpi_widgets["alerts"].update_value(str(kpis.get("pending_orders", 0) + kpis.get("out_of_service_equipment", 0)))
            except RuntimeError as e:
                print(f"Erreur lors de la mise à jour des widgets KPI: {e}")
                return False

            # Mettre à jour la barre de statut avec la date et l'heure de la dernière mise à jour
            self.last_update_time = datetime.now()

            print(f"KPIs mis à jour avec succès à {self.last_update_time.strftime('%H:%M:%S')}")
            return True
        except Exception as e:
            print(f"Erreur lors de la mise à jour des KPIs: {e}")
            import traceback
            traceback.print_exc()
            return False

    def refresh(self):
        """Rafraîchit toutes les données du tableau de bord"""
        # Désactiver le bouton de rafraîchissement pendant le chargement
        self.refresh_button.setEnabled(False)
        self.refresh_button.setText("Actualisation...")

        # Lancer le chargement des données
        QTimer.singleShot(0, self.init_data)

        # Réactiver le bouton après un délai
        QTimer.singleShot(2000, self._enable_refresh_button)

    def _enable_refresh_button(self):
        """Réactive le bouton de rafraîchissement"""
        self.refresh_button.setEnabled(True)
        self.refresh_button.setText("Actualiser")

    def start_auto_refresh(self, interval_ms=300000):
        """Démarre le rafraîchissement automatique

        Args:
            interval_ms: Intervalle de rafraîchissement en millisecondes (par défaut: 5 minutes)
        """
        if hasattr(self, 'refresh_timer'):
            # Arrêter le timer s'il est déjà actif
            if self.refresh_timer.isActive():
                self.refresh_timer.stop()

            # Démarrer le timer avec le nouvel intervalle
            self.refresh_timer.start(interval_ms)
            print(f"Rafraîchissement automatique activé (intervalle: {interval_ms/1000} secondes)")

    def stop_auto_refresh(self):
        """Arrête le rafraîchissement automatique"""
        if hasattr(self, 'refresh_timer') and self.refresh_timer.isActive():
            self.refresh_timer.stop()
            print("Rafraîchissement automatique désactivé")
