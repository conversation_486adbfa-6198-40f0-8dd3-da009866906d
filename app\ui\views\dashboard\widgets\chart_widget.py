from PyQt6.QtCharts import QChart, QChartView, QLineSeries, QBarSeries, QBarSet, QValueAxis, QDateTimeAxis
from PyQt6.QtCore import Qt, QDateTime, QDate, QPointF, QTime
from PyQt6.QtGui import QPainter, QColor
from app.core.services.reporting_service import ReportingService
from app.utils.database import SessionLocal
from datetime import datetime, timedelta

class BaseChart(QChartView):
    def __init__(self, title: str, db=None):
        chart = QChart()
        super().__init__(chart)

        self.chart().setTitle(title)
        self.chart().setAnimationOptions(QChart.AnimationOption.SeriesAnimations)
        self.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Style
        self.chart().setBackgroundVisible(False)
        self.chart().legend().setVisible(True)
        self.chart().legend().setAlignment(Qt.AlignmentFlag.AlignBottom)

        # Utiliser la session de base de données fournie ou en créer une nouvelle
        self.db_owner = False  # Indique si cette classe est propriétaire de la connexion
        if db is None:
            try:
                self.db = SessionLocal()
                self.db_owner = True  # Cette classe est propriétaire de la connexion
                print(f"{title} Chart: Nouvelle session de base de données créée")
            except Exception as e:
                print(f"Erreur lors de la création de la session pour {title}: {e}")
                self.db = None
        else:
            self.db = db
            print(f"{title} Chart: Utilisation d'une session de base de données existante")

        # Créer le service de reporting
        if self.db:
            self.service = ReportingService(self.db)
        else:
            self.service = None

    def __del__(self):
        """Destructeur pour fermer la session de base de données si cette classe en est propriétaire"""
        if hasattr(self, 'db') and self.db and self.db_owner:
            try:
                self.db.close()
                print(f"{self.chart().title()} Chart: Session de base de données fermée")
            except Exception as e:
                print(f"Erreur lors de la fermeture de la session: {e}")

    async def update_data(self):
        """Méthode à implémenter dans les classes enfants"""
        raise NotImplementedError

    def clear_chart(self):
        """Efface toutes les séries du graphique"""
        try:
            self.chart().removeAllSeries()

            # Supprimer les axes existants
            axes = self.chart().axes()
            for axis in axes:
                self.chart().removeAxis(axis)
        except RuntimeError as e:
            # Ignorer les erreurs si l'objet Qt a été supprimé
            print(f"Erreur lors du nettoyage du graphique: {e}")
            pass

class RepairChart(BaseChart):
    def __init__(self, db=None):
        super().__init__("Statistiques des Réparations", db)
        self.setup_chart()

    def setup_chart(self):
        # Configuration spécifique au graphique des réparations
        self.series_completed = QLineSeries()
        self.series_completed.setName("Réparations terminées")
        self.series_completed.setColor(QColor("#28a745"))  # Vert

        self.series_pending = QLineSeries()
        self.series_pending.setName("Réparations en cours")
        self.series_pending.setColor(QColor("#ffc107"))  # Jaune

        # Ajouter les séries au graphique
        self.chart().addSeries(self.series_completed)
        self.chart().addSeries(self.series_pending)

        # Créer les axes
        self._create_axes()

    def _create_axes(self):
        """Crée les axes pour le graphique"""
        # Axe X (dates)
        axis_x = QDateTimeAxis()
        axis_x.setFormat("dd/MM")
        axis_x.setTitleText("Date")

        # Axe Y (nombre de réparations)
        axis_y = QValueAxis()
        axis_y.setLabelFormat("%d")
        axis_y.setTitleText("Nombre de réparations")
        axis_y.setMinorTickCount(4)

        # Ajouter les axes au graphique
        self.chart().addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        self.chart().addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)

        # Attacher les séries aux axes
        self.series_completed.attachAxis(axis_x)
        self.series_completed.attachAxis(axis_y)
        self.series_pending.attachAxis(axis_x)
        self.series_pending.attachAxis(axis_y)

    async def update_data(self):
        """Met à jour les données du graphique des réparations"""
        try:
            if not hasattr(self, 'service') or not self.service:
                print("Service de reporting non disponible")
                return

            # Effacer les données existantes
            try:
                self.series_completed.clear()
                self.series_pending.clear()
            except RuntimeError as e:
                print(f"Erreur lors de la mise à jour du graphique des réparations: {e}")
                return  # Sortir si les objets Qt ont été supprimés

            # Récupérer les données de tendance des réparations (30 derniers jours)
            try:
                repair_trend = await self.service.get_repair_trend_data(months=1)
            except Exception as e:
                print(f"Erreur lors de la récupération des données de tendance: {e}")
                return

            if not repair_trend:
                print("Aucune donnée de tendance des réparations disponible")
                return

            # Préparer les données pour le graphique
            min_value = float('inf')
            max_value = 0

            for data_point in repair_trend:
                # Convertir la date en QDateTime
                date_str = data_point.get('date')
                if not date_str:
                    continue

                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                q_date = QDateTime()
                q_date.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))

                # Ajouter les points aux séries
                completed = data_point.get('completed', 0)
                pending = data_point.get('pending', 0)

                try:
                    self.series_completed.append(q_date.toMSecsSinceEpoch(), completed)
                    self.series_pending.append(q_date.toMSecsSinceEpoch(), pending)
                except RuntimeError:
                    print("Erreur: Les séries du graphique ont été supprimées")
                    return

                # Mettre à jour les valeurs min/max
                min_value = min(min_value, completed, pending)
                max_value = max(max_value, completed, pending)

            # Mettre à jour les axes
            try:
                axes = self.chart().axes()
                if len(axes) >= 2:
                    # Axe X (dates)
                    date_axis = axes[0]
                    if isinstance(date_axis, QDateTimeAxis):
                        # Définir la plage de dates (30 derniers jours)
                        end_date = QDateTime.currentDateTime()
                        start_date = QDateTime(end_date)
                        start_date.setDate(start_date.date().addDays(-30))

                        date_axis.setRange(start_date, end_date)

                    # Axe Y (nombre de réparations)
                    value_axis = axes[1]
                    if isinstance(value_axis, QValueAxis):
                        # Ajouter une marge de 10% au-dessus de la valeur maximale
                        if max_value > 0:
                            value_axis.setRange(0, max_value * 1.1)
                        else:
                            value_axis.setRange(0, 10)  # Valeur par défaut si aucune donnée

                print("Données du graphique des réparations mises à jour avec succès")
            except RuntimeError as e:
                print(f"Erreur lors de la mise à jour des axes: {e}")

        except Exception as e:
            print(f"Erreur lors de la mise à jour du graphique des réparations: {e}")
            import traceback
            traceback.print_exc()

class InventoryChart(BaseChart):
    def __init__(self, db=None):
        super().__init__("Mouvements de Stock", db)
        self.setup_chart()

    def setup_chart(self):
        # Configuration spécifique au graphique d'inventaire
        self.series = QBarSeries()
        self.in_set = QBarSet("Entrées")
        self.in_set.setColor(QColor("#28a745"))  # Vert

        self.out_set = QBarSet("Sorties")
        self.out_set.setColor(QColor("#dc3545"))  # Rouge

        self.series.append(self.in_set)
        self.series.append(self.out_set)

        self.chart().addSeries(self.series)

        # Créer les axes
        self._create_axes()

    def _create_axes(self):
        """Crée les axes pour le graphique"""
        # Axe X (catégories)
        axis_x = QValueAxis()
        axis_x.setLabelFormat("%d")
        axis_x.setTitleText("Jours")

        # Axe Y (quantités)
        axis_y = QValueAxis()
        axis_y.setLabelFormat("%d")
        axis_y.setTitleText("Quantité")

        # Ajouter les axes au graphique
        self.chart().addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        self.chart().addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)

        # Attacher la série aux axes
        self.series.attachAxis(axis_x)
        self.series.attachAxis(axis_y)

    async def update_data(self):
        """Met à jour les données du graphique d'inventaire"""
        try:
            if not hasattr(self, 'service') or not self.service:
                print("Service de reporting non disponible")
                return

            # Effacer les données existantes
            try:
                self.in_set.remove(0, self.in_set.count())
                self.out_set.remove(0, self.out_set.count())
            except RuntimeError as e:
                print(f"Erreur lors de la mise à jour du graphique d'inventaire: {e}")
                return  # Sortir si les objets Qt ont été supprimés

            # Récupérer les mouvements d'inventaire des 7 derniers jours
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)

            try:
                inventory_movements = await self.service.get_inventory_movements(start_date, end_date)
            except Exception as e:
                print(f"Erreur lors de la récupération des mouvements d'inventaire: {e}")
                return

            if not inventory_movements:
                print("Aucune donnée de mouvement d'inventaire disponible")
                return

            # Préparer les données pour le graphique
            daily_in = [0] * 7
            daily_out = [0] * 7

            # Traiter les mouvements par jour
            for movement in inventory_movements:
                # Vérifier si movement est un dictionnaire ou une chaîne
                if isinstance(movement, dict):
                    date_str = movement.get('date')
                    if not date_str:
                        continue

                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                    days_ago = (end_date.date() - date_obj.date()).days

                    if 0 <= days_ago < 7:
                        if movement.get('type') == 'in':
                            daily_in[days_ago] += movement.get('quantity', 0)
                        else:
                            daily_out[days_ago] += movement.get('quantity', 0)
                elif isinstance(movement, str):
                    # Si c'est une chaîne, l'ignorer et continuer
                    print(f"Ignoré: movement est une chaîne: {movement}")
                    continue
                else:
                    # Si c'est un autre type, l'ignorer et continuer
                    print(f"Ignoré: movement est de type {type(movement)}: {movement}")
                    continue

            # Inverser les listes pour avoir l'ordre chronologique (du plus ancien au plus récent)
            daily_in.reverse()
            daily_out.reverse()

            # Ajouter les données aux barsets
            try:
                for value in daily_in:
                    self.in_set.append(value)

                for value in daily_out:
                    self.out_set.append(value)
            except RuntimeError:
                print("Erreur: Les barsets du graphique ont été supprimés")
                return

            # Mettre à jour les axes
            try:
                axes = self.chart().axes()
                if len(axes) >= 2:
                    # Axe X (jours)
                    axis_x = axes[0]
                    if isinstance(axis_x, QValueAxis):
                        axis_x.setRange(0, 7)

                    # Axe Y (quantités)
                    axis_y = axes[1]
                    if isinstance(axis_y, QValueAxis):
                        max_value = max(max(daily_in) if daily_in else 0, max(daily_out) if daily_out else 0)
                        if max_value > 0:
                            axis_y.setRange(0, max_value * 1.1)
                        else:
                            axis_y.setRange(0, 10)  # Valeur par défaut si aucune donnée

                print("Données du graphique d'inventaire mises à jour avec succès")
            except RuntimeError as e:
                print(f"Erreur lors de la mise à jour des axes: {e}")

        except Exception as e:
            print(f"Erreur lors de la mise à jour du graphique d'inventaire: {e}")
            import traceback
            traceback.print_exc()

class MaintenanceChart(BaseChart):
    def __init__(self, db=None):
        super().__init__("Planning de Maintenance", db)
        self.setup_chart()

    def setup_chart(self):
        # Configuration spécifique au graphique de maintenance
        self.series_scheduled = QLineSeries()
        self.series_scheduled.setName("Maintenance planifiée")
        self.series_scheduled.setColor(QColor("#17a2b8"))  # Bleu

        self.series_completed = QLineSeries()
        self.series_completed.setName("Maintenance effectuée")
        self.series_completed.setColor(QColor("#28a745"))  # Vert

        self.series_overdue = QLineSeries()
        self.series_overdue.setName("Maintenance en retard")
        self.series_overdue.setColor(QColor("#dc3545"))  # Rouge

        # Ajouter les séries au graphique
        self.chart().addSeries(self.series_scheduled)
        self.chart().addSeries(self.series_completed)
        self.chart().addSeries(self.series_overdue)

        # Créer les axes
        self._create_axes()

    def _create_axes(self):
        """Crée les axes pour le graphique"""
        # Axe X (dates)
        axis_x = QDateTimeAxis()
        axis_x.setFormat("dd/MM")
        axis_x.setTitleText("Date")

        # Axe Y (nombre de maintenances)
        axis_y = QValueAxis()
        axis_y.setLabelFormat("%d")
        axis_y.setTitleText("Nombre de maintenances")

        # Ajouter les axes au graphique
        self.chart().addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        self.chart().addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)

        # Attacher les séries aux axes
        self.series_scheduled.attachAxis(axis_x)
        self.series_scheduled.attachAxis(axis_y)
        self.series_completed.attachAxis(axis_x)
        self.series_completed.attachAxis(axis_y)
        self.series_overdue.attachAxis(axis_x)
        self.series_overdue.attachAxis(axis_y)

    async def update_data(self):
        """Met à jour les données du graphique de maintenance"""
        try:
            if not hasattr(self, 'service') or not self.service:
                print("Service de reporting non disponible")
                return

            # Effacer les données existantes
            try:
                self.series_scheduled.clear()
                self.series_completed.clear()
                self.series_overdue.clear()
            except RuntimeError as e:
                print(f"Erreur lors de la mise à jour du graphique de maintenance: {e}")
                return  # Sortir si les objets Qt ont été supprimés

            # Récupérer les données de maintenance pour les 30 prochains jours
            end_date = datetime.now() + timedelta(days=30)
            start_date = datetime.now() - timedelta(days=30)  # Inclure les 30 derniers jours aussi

            try:
                maintenance_data = await self.service.get_maintenance_compliance(start_date, end_date)
            except Exception as e:
                print(f"Erreur lors de la récupération des données de maintenance: {e}")
                return

            if not maintenance_data:
                print("Aucune donnée de maintenance disponible")
                return

            # Préparer les données pour le graphique
            max_value = 0

            # Traiter les données par jour
            for data_point in maintenance_data:
                # Vérifier si data_point est un dictionnaire ou une chaîne
                if isinstance(data_point, dict):
                    date_str = data_point.get('date')
                    if not date_str:
                        continue

                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                    q_date = QDateTime()
                    q_date.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
                    q_date.setTime(QTime(0, 0))  # Définir l'heure à minuit
                    ms_since_epoch = q_date.toMSecsSinceEpoch()

                    # Ajouter les points aux séries
                    scheduled = data_point.get('scheduled', 0)
                    completed = data_point.get('completed', 0)
                    overdue = data_point.get('overdue', 0)
                elif isinstance(data_point, str):
                    # Si c'est une chaîne, l'ignorer et continuer
                    print(f"Ignoré: data_point est une chaîne: {data_point}")
                    continue
                else:
                    # Si c'est un autre type, l'ignorer et continuer
                    print(f"Ignoré: data_point est de type {type(data_point)}: {data_point}")
                    continue

                try:
                    self.series_scheduled.append(ms_since_epoch, scheduled)
                    self.series_completed.append(ms_since_epoch, completed)
                    self.series_overdue.append(ms_since_epoch, overdue)
                except RuntimeError:
                    print("Erreur: Les séries du graphique ont été supprimées")
                    return

                # Mettre à jour la valeur maximale
                max_value = max(max_value, scheduled, completed, overdue)

            # Mettre à jour les axes
            try:
                axes = self.chart().axes()
                if len(axes) >= 2:
                    # Axe X (dates)
                    date_axis = axes[0]
                    if isinstance(date_axis, QDateTimeAxis):
                        # Définir la plage de dates
                        start_q_date = QDateTime()
                        start_q_date.setDate(QDate(start_date.year, start_date.month, start_date.day))
                        start_q_date.setTime(QTime(0, 0))  # Définir l'heure à minuit

                        end_q_date = QDateTime()
                        end_q_date.setDate(QDate(end_date.year, end_date.month, end_date.day))
                        end_q_date.setTime(QTime(23, 59, 59))  # Définir l'heure à la fin de la journée

                        date_axis.setRange(start_q_date, end_q_date)

                    # Axe Y (nombre de maintenances)
                    value_axis = axes[1]
                    if isinstance(value_axis, QValueAxis):
                        # Ajouter une marge de 10% au-dessus de la valeur maximale
                        if max_value > 0:
                            value_axis.setRange(0, max_value * 1.1)
                        else:
                            value_axis.setRange(0, 10)  # Valeur par défaut si aucune donnée

                print("Données du graphique de maintenance mises à jour avec succès")
            except RuntimeError as e:
                print(f"Erreur lors de la mise à jour des axes: {e}")

        except Exception as e:
            print(f"Erreur lors de la mise à jour du graphique de maintenance: {e}")
            import traceback
            traceback.print_exc()
