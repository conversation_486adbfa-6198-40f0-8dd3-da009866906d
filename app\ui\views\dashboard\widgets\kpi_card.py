from PyQt6.QtWidgets import QFrame, QVBoxLayout, QLabel
from PyQt6.QtCore import Qt, QDir
from PyQt6.QtGui import QIcon
import os

class KPICard(QFrame):
    def __init__(self, title: str, value: str, icon_name: str):
        super().__init__()
        self.setObjectName("kpiCard")
        self.setup_ui(title, value, icon_name)

    def setup_ui(self, title: str, value: str, icon_name: str):
        layout = QVBoxLayout(self)
        layout.setSpacing(8)

        # Icône - Utilisation du chemin direct au fichier
        icon_label = QLabel()
        # Chemin absolu vers le répertoire des icônes
        icons_dir = os.path.join(QDir.currentPath(), "app", "ui", "resources", "icons")
        icon_path = os.path.join(icons_dir, f"{icon_name}.svg")
        
        if os.path.exists(icon_path):
            icon_label.setPixmap(QIcon(icon_path).pixmap(32, 32))
        else:
            # Fallback si l'icône n'existe pas
            icon_label.setText("[icon]")
        
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(icon_label)

        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("kpiTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Valeur
        self.value_label = QLabel(value)
        self.value_label.setObjectName("kpiValue")
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.value_label)

    def update_value(self, value: str):
        """Met à jour la valeur affichée"""
        self.value_label.setText(value)