from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QFormLayout, QLineEdit,
    QComboBox, QDateEdit, QTextEdit, QDialogButtonBox,
    QLabel, QMessageBox
)
from PyQt6.QtCore import Qt, QDate
import asyncio
from datetime import datetime

from app.core.services.equipment_service import EquipmentService
from app.core.models.equipment import EquipmentStatus

class EquipmentDialog(QDialog):
    """Boîte de dialogue pour ajouter ou modifier un équipement"""
    
    def __init__(self, parent=None, equipment_id=None):
        super().__init__(parent)
        self.equipment_id = equipment_id
        self.service = EquipmentService()
        
        self.setWindowTitle("Nouvel équipement" if equipment_id is None else "Modifier l'équipement")
        self.setMinimumWidth(400)
        
        self.setup_ui()
        
        # Si on modifie un équipement existant, charger ses données
        if equipment_id is not None:
            self.load_equipment_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur de la boîte de dialogue"""
        layout = QVBoxLayout(self)
        
        form_layout = QFormLayout()
        
        # Champs de saisie
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Nom de l'équipement")
        form_layout.addRow("Nom:", self.name_edit)
        
        self.brand_edit = QLineEdit()
        self.brand_edit.setPlaceholderText("Marque")
        form_layout.addRow("Marque:", self.brand_edit)
        
        self.model_edit = QLineEdit()
        self.model_edit.setPlaceholderText("Modèle")
        form_layout.addRow("Modèle:", self.model_edit)
        
        self.serial_edit = QLineEdit()
        self.serial_edit.setPlaceholderText("Numéro de série")
        form_layout.addRow("N° Série:", self.serial_edit)
        
        self.status_combo = QComboBox()
        for status in EquipmentStatus:
            self.status_combo.addItem(self._get_status_display(status), status.value)
        form_layout.addRow("Statut:", self.status_combo)
        
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("Emplacement")
        form_layout.addRow("Emplacement:", self.location_edit)
        
        self.acquisition_date = QDateEdit()
        self.acquisition_date.setCalendarPopup(True)
        self.acquisition_date.setDate(QDate.currentDate())
        self.acquisition_date.setDisplayFormat("dd/MM/yyyy")
        form_layout.addRow("Date d'acquisition:", self.acquisition_date)
        
        self.warranty_date = QDateEdit()
        self.warranty_date.setCalendarPopup(True)
        self.warranty_date.setDate(QDate.currentDate().addYears(1))
        self.warranty_date.setDisplayFormat("dd/MM/yyyy")
        form_layout.addRow("Fin de garantie:", self.warranty_date)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes supplémentaires")
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # Boutons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def load_equipment_data(self):
        """Charge les données de l'équipement à modifier"""
        loop = asyncio.get_event_loop()
        if loop.is_running():
            asyncio.create_task(self._load_data())
        else:
            loop.run_until_complete(self._load_data())
    
    async def _load_data(self):
        """Charge les données de l'équipement de manière asynchrone"""
        equipment = await self.service.get(self.equipment_id)
        if not equipment:
            QMessageBox.warning(self, "Erreur", "Équipement non trouvé")
            self.reject()
            return
        
        self.name_edit.setText(equipment.name)
        self.brand_edit.setText(equipment.brand)
        self.model_edit.setText(equipment.model)
        self.serial_edit.setText(equipment.serial_number or "")
        
        # Trouver l'index du statut dans le combobox
        status_index = self.status_combo.findData(equipment.status.value)
        if status_index >= 0:
            self.status_combo.setCurrentIndex(status_index)
        
        self.location_edit.setText(equipment.location or "")
        
        if equipment.acquisition_date:
            self.acquisition_date.setDate(QDate(
                equipment.acquisition_date.year,
                equipment.acquisition_date.month,
                equipment.acquisition_date.day
            ))
        
        if equipment.warranty_expiry:
            self.warranty_date.setDate(QDate(
                equipment.warranty_expiry.year,
                equipment.warranty_expiry.month,
                equipment.warranty_expiry.day
            ))
        
        self.notes_edit.setText(equipment.notes or "")
    
    def accept(self):
        """Valide et enregistre les données de l'équipement"""
        # Vérifier les champs obligatoires
        if not self.name_edit.text() or not self.brand_edit.text() or not self.model_edit.text():
            QMessageBox.warning(self, "Champs obligatoires", "Veuillez remplir tous les champs obligatoires")
            return
        
        # Enregistrer les données
        loop = asyncio.get_event_loop()
        if loop.is_running():
            asyncio.create_task(self._save_data())
        else:
            loop.run_until_complete(self._save_data())
            super().accept()
    
    async def _save_data(self):
        """Enregistre les données de l'équipement de manière asynchrone"""
        try:
            # Préparer les données
            equipment_data = {
                "name": self.name_edit.text(),
                "brand": self.brand_edit.text(),
                "model": self.model_edit.text(),
                "serial_number": self.serial_edit.text() or None,
                "status": EquipmentStatus(self.status_combo.currentData()),
                "location": self.location_edit.text() or None,
                "acquisition_date": datetime(
                    self.acquisition_date.date().year(),
                    self.acquisition_date.date().month(),
                    self.acquisition_date.date().day()
                ),
                "warranty_expiry": datetime(
                    self.warranty_date.date().year(),
                    self.warranty_date.date().month(),
                    self.warranty_date.date().day()
                ),
                "notes": self.notes_edit.toPlainText() or None,
                "is_active": True
            }
            
            if self.equipment_id is None:
                # Créer un nouvel équipement
                await self.service.create(equipment_data)
            else:
                # Mettre à jour l'équipement existant
                equipment_data["id"] = self.equipment_id
                await self.service.update(self.equipment_id, equipment_data)
            
            super().accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
    
    def _get_status_display(self, status: EquipmentStatus) -> str:
        """Retourne l'affichage du statut"""
        status_map = {
            EquipmentStatus.OPERATIONAL: "Opérationnel",
            EquipmentStatus.MAINTENANCE: "En maintenance",
            EquipmentStatus.REPAIR: "En réparation",
            EquipmentStatus.OUT_OF_SERVICE: "Hors service",
            EquipmentStatus.RETIRED: "Retiré"
        }
        return status_map.get(status, str(status))
