from PyQt6.QtCore import Qt, QAbstractTableModel
from app.core.services.equipment_service import EquipmentService
from app.core.models.equipment import EquipmentStatus
from datetime import datetime

class EquipmentTableModel(QAbstractTableModel):
    """Modèle de table pour l'affichage des équipements"""

    def __init__(self):
        super().__init__()
        # Créer une nouvelle session pour le modèle de table
        from app.utils.database import SessionLocal
        self.db = SessionLocal()
        self.service = EquipmentService(self.db)  # Utiliser la même session pour le service
        self.equipment_list = []
        self.headers = [
            "ID",
            "Nom",
            "Marque",
            "Modèle",
            "N° Série",
            "Statut",
            "Emplacement",
            "Date d'acquisition",
            "Fin de garantie"
        ]

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("EquipmentTableModel: Session de base de données fermée")

    async def load_data(self):
        """Charge les données depuis le service"""
        self.beginResetModel()
        try:
            # Fermer l'ancienne session si elle existe
            if hasattr(self, 'db') and self.db:
                self.db.close()
                print("EquipmentTableModel: Ancienne session fermée")

            # Créer une nouvelle session
            from app.utils.database import SessionLocal
            self.db = SessionLocal()
            self.service = EquipmentService(self.db)

            # Charger les données avec la nouvelle session
            self.equipment_list = await self.service.get_active_equipment()
            print(f"Équipements chargés: {len(self.equipment_list)}")
        except Exception as e:
            print(f"Erreur lors du chargement des équipements: {e}")
            self.equipment_list = []
        self.endResetModel()

    def rowCount(self, parent=None):
        return len(self.equipment_list)

    def columnCount(self, parent=None):
        return len(self.headers)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.ItemDataRole.DisplayRole:
            equipment = self.equipment_list[index.row()]
            column = index.column()

            # Formatage des données selon la colonne
            if column == 0:
                return str(equipment.id)
            elif column == 1:
                return equipment.name
            elif column == 2:
                return equipment.brand
            elif column == 3:
                return equipment.model
            elif column == 4:
                return equipment.serial_number or ""
            elif column == 5:
                return self._get_status_display(equipment.status)
            elif column == 6:
                return equipment.location or ""
            elif column == 7:
                return equipment.acquisition_date.strftime("%d/%m/%Y") if equipment.acquisition_date else ""
            elif column == 8:
                return equipment.warranty_expiry.strftime("%d/%m/%Y") if equipment.warranty_expiry else ""

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            # Alignement des colonnes
            if index.column() in [0, 7, 8]:  # ID, dates
                return Qt.AlignmentFlag.AlignCenter
            elif index.column() == 5:  # Statut
                return Qt.AlignmentFlag.AlignCenter

        elif role == Qt.ItemDataRole.BackgroundRole:
            equipment = self.equipment_list[index.row()]

            # Coloration selon le statut
            if equipment.status == EquipmentStatus.OUT_OF_SERVICE:
                return Qt.GlobalColor.red
            elif equipment.status == EquipmentStatus.REPAIR:
                return Qt.GlobalColor.yellow
            elif equipment.status == EquipmentStatus.MAINTENANCE:
                return Qt.GlobalColor.cyan

        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            return self.headers[section]
        return None

    def get_equipment_id(self, row: int) -> int:
        """Retourne l'ID de l'équipement à la ligne spécifiée"""
        return self.equipment_list[row].id

    def _get_status_display(self, status: EquipmentStatus) -> str:
        """Retourne l'affichage du statut"""
        status_map = {
            EquipmentStatus.OPERATIONAL: "Opérationnel",
            EquipmentStatus.MAINTENANCE: "En maintenance",
            EquipmentStatus.REPAIR: "En réparation",
            EquipmentStatus.OUT_OF_SERVICE: "Hors service",
            EquipmentStatus.RETIRED: "Retiré"
        }
        return status_map.get(status, str(status))
