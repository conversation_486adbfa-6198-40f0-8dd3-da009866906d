from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView,
    QPushButton, QLineEdit, QComboBox, QLabel,
    QStackedWidget, QFrame, QMessageBox
)
from PyQt6.QtCore import Qt, QSortFilterProxyModel, QTimer
from PyQt6.QtGui import QIcon
import asyncio

from .equipment_table_model import EquipmentTableModel
from .dialogs.equipment_dialog import EquipmentDialog
from ...components.custom_widgets import SearchBar, FilterComboBox, LoadingOverlay
from app.core.models.equipment import EquipmentStatus

class EquipmentView(QWidget):
    """Vue principale pour la gestion des équipements"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_connections()
        # Use QTimer to properly schedule the async load
        QTimer.singleShot(0, self._init_data)

    def _init_data(self):
        """Initialize data loading"""
        # Utiliser notre wrapper pour exécuter load_data de manière asynchrone
        self._load_data_wrapper()

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone sans bloquer la boucle existante"""
        import asyncio
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(self.load_data())
        except RuntimeError:
            # Pas de boucle en cours (ex: thread GUI sans event loop async), exécuter directement
            asyncio.run(self.load_data())

    def setup_ui(self):
        """Configure l'interface utilisateur de la vue équipements"""
        main_layout = QVBoxLayout(self)

        # En-tête avec titre et boutons d'action
        header_layout = QHBoxLayout()

        title = QLabel("Gestion des Équipements")
        title.setObjectName("equipmentHeader")
        header_layout.addWidget(title)

        # Boutons d'action
        self.add_button = QPushButton("Nouvel Équipement")
        self.add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))

        header_layout.addWidget(self.add_button)
        header_layout.addWidget(self.export_button)
        header_layout.addStretch()

        main_layout.addLayout(header_layout)

        # Barre de recherche et filtres
        search_layout = QHBoxLayout()

        self.search_bar = SearchBar("Rechercher un équipement...")
        self.status_filter = FilterComboBox("Statut")
        self.location_filter = FilterComboBox("Emplacement")

        search_layout.addWidget(self.search_bar)
        search_layout.addWidget(self.status_filter)
        search_layout.addWidget(self.location_filter)

        main_layout.addLayout(search_layout)

        # Tableau d'équipements
        self.table_view = QTableView()
        self.table_view.setObjectName("equipmentTable")
        self.table_model = EquipmentTableModel()
        self.proxy_model = QSortFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.table_view.setModel(self.proxy_model)

        # Configuration du tableau
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.horizontalHeader().setStretchLastSection(True)

        main_layout.addWidget(self.table_view)

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.add_button.clicked.connect(self.show_add_dialog)
        self.export_button.clicked.connect(self.export_equipment)

        self.search_bar.textChanged.connect(self.filter_equipment)
        self.status_filter.currentTextChanged.connect(self.filter_equipment)
        self.location_filter.currentTextChanged.connect(self.filter_equipment)

        self.table_view.doubleClicked.connect(self.show_edit_dialog)

    async def load_data(self):
        """Charge les données des équipements"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau
            await self.table_model.load_data()

            # Charger les filtres
            await self.load_filters()

        finally:
            self.loading_overlay.hide()

    async def load_filters(self):
        """Charge les options des filtres"""
        # Statuts
        self.status_filter.clear()
        self.status_filter.addItem("Tous les statuts", "")
        for status in EquipmentStatus:
            self.status_filter.addItem(self._get_status_display(status), status.value)

        # Emplacements
        self.location_filter.clear()
        self.location_filter.addItem("Tous les emplacements", "")

        # Récupérer les emplacements depuis le service
        # Utiliser la même session que le modèle de table
        locations = await self.table_model.service.get_equipment_locations()

        for location in locations:
            self.location_filter.addItem(location, location)

    def filter_equipment(self):
        """Applique les filtres sur le tableau"""
        search_text = self.search_bar.text().lower()
        status = self.status_filter.currentData()
        location = self.location_filter.currentData()

        # Filtrage par texte
        self.proxy_model.setFilterFixedString(search_text)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.proxy_model.setFilterKeyColumn(-1)  # Recherche dans toutes les colonnes

        # TODO: Implémenter le filtrage par statut et emplacement

    def show_add_dialog(self):
        """Affiche la boîte de dialogue d'ajout d'équipement"""
        dialog = EquipmentDialog(self)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

    def show_edit_dialog(self, index):
        """Affiche la boîte de dialogue d'édition d'équipement"""
        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(index)
        equipment_id = self.table_model.get_equipment_id(source_index.row())

        dialog = EquipmentDialog(self, equipment_id=equipment_id)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

    def export_equipment(self):
        """Exporte les données des équipements"""
        # TODO: Implémenter l'exportation des données
        QMessageBox.information(self, "Export", "Fonctionnalité d'export non implémentée.")

    def _get_status_display(self, status: EquipmentStatus) -> str:
        """Retourne l'affichage du statut"""
        status_map = {
            EquipmentStatus.OPERATIONAL: "Opérationnel",
            EquipmentStatus.MAINTENANCE: "En maintenance",
            EquipmentStatus.REPAIR: "En réparation",
            EquipmentStatus.OUT_OF_SERVICE: "Hors service",
            EquipmentStatus.RETIRED: "Retiré"
        }
        return status_map.get(status, str(status))
