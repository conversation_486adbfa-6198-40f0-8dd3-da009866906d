"""
Vue pour la gestion des catégories d'articles.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QLineEdit, QCheckBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon
import asyncio

from app.core.services.item_category_service import ItemCategoryService
from app.ui.views.inventory.dialogs.category_dialog import CategoryDialog
from app.ui.components.custom_widgets import LoadingOverlay

class CategoryView(QWidget):
    """Vue pour la gestion des catégories d'articles"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.service = ItemCategoryService()
        self.categories = []
        self.setup_ui()
        self._load_data_wrapper()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # En-tête
        header = QLabel("Gestion des Catégories d'Articles")
        header.setObjectName("sectionHeader")
        main_layout.addWidget(header)
        
        # Barre d'outils
        toolbar_layout = QHBoxLayout()
        
        # Bouton d'ajout
        self.add_button = QPushButton("Nouvelle Catégorie")
        self.add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_button.setObjectName("primaryButton")
        toolbar_layout.addWidget(self.add_button)
        
        # Bouton de modification
        self.edit_button = QPushButton("Modifier")
        self.edit_button.setIcon(QIcon("app/ui/resources/icons/edit.svg"))
        self.edit_button.setEnabled(False)
        toolbar_layout.addWidget(self.edit_button)
        
        # Bouton de suppression
        self.delete_button = QPushButton("Supprimer")
        self.delete_button.setIcon(QIcon("app/ui/resources/icons/cancel.svg"))
        self.delete_button.setEnabled(False)
        toolbar_layout.addWidget(self.delete_button)
        
        # Bouton de rafraîchissement
        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        toolbar_layout.addWidget(self.refresh_button)
        
        toolbar_layout.addStretch()
        
        # Recherche
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher une catégorie...")
        self.search_input.setClearButtonEnabled(True)
        toolbar_layout.addWidget(self.search_input)
        
        # Afficher les catégories inactives
        self.show_inactive_check = QCheckBox("Afficher les inactives")
        toolbar_layout.addWidget(self.show_inactive_check)
        
        main_layout.addLayout(toolbar_layout)
        
        # Tableau des catégories
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(["ID", "Nom", "Code", "Catégorie parente", "Active"])
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        main_layout.addWidget(self.table)
        
        # Connexions
        self.add_button.clicked.connect(self.show_add_dialog)
        self.edit_button.clicked.connect(self.show_edit_dialog)
        self.delete_button.clicked.connect(self.delete_category)
        self.refresh_button.clicked.connect(self._load_data_wrapper)
        self.search_input.textChanged.connect(self.filter_categories)
        self.show_inactive_check.stateChanged.connect(self._load_data_wrapper)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.doubleClicked.connect(self.show_edit_dialog)
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
    
    def _load_data_wrapper(self):
        """Wrapper pour charger les données de manière asynchrone"""
        self.loading_overlay.show()
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._load_data_async())
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()
            self.loading_overlay.hide()
    
    async def _load_data_async(self):
        """Charge les données de manière asynchrone"""
        # Récupérer toutes les catégories
        include_inactive = self.show_inactive_check.isChecked()
        self.categories = await self.service.get_all_categories(include_inactive=include_inactive)
        
        # Mettre à jour le tableau
        self.update_table()
    
    def update_table(self):
        """Met à jour le tableau avec les catégories"""
        self.table.setRowCount(0)
        
        # Filtrer les catégories selon la recherche
        search_text = self.search_input.text().lower()
        filtered_categories = [
            category for category in self.categories
            if search_text in category.name.lower() or search_text in category.code.lower()
        ]
        
        # Remplir le tableau
        for row, category in enumerate(filtered_categories):
            self.table.insertRow(row)
            
            # ID
            id_item = QTableWidgetItem(str(category.id))
            id_item.setData(Qt.ItemDataRole.UserRole, category.id)
            self.table.setItem(row, 0, id_item)
            
            # Nom
            self.table.setItem(row, 1, QTableWidgetItem(category.name))
            
            # Code
            self.table.setItem(row, 2, QTableWidgetItem(category.code))
            
            # Catégorie parente
            parent_name = "Aucune"
            if category.parent_id:
                for parent in self.categories:
                    if parent.id == category.parent_id:
                        parent_name = parent.name
                        break
            self.table.setItem(row, 3, QTableWidgetItem(parent_name))
            
            # Active
            active_item = QTableWidgetItem("Oui" if category.is_active else "Non")
            active_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(row, 4, active_item)
        
        # Masquer la colonne ID
        self.table.hideColumn(0)
    
    def filter_categories(self):
        """Filtre les catégories selon la recherche"""
        self.update_table()
    
    def on_selection_changed(self):
        """Gère le changement de sélection dans le tableau"""
        selected_rows = self.table.selectedItems()
        has_selection = len(selected_rows) > 0
        
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
    
    def show_add_dialog(self):
        """Affiche la boîte de dialogue d'ajout de catégorie"""
        dialog = CategoryDialog(self)
        if dialog.exec():
            self._load_data_wrapper()
    
    def show_edit_dialog(self):
        """Affiche la boîte de dialogue de modification de catégorie"""
        selected_items = self.table.selectedItems()
        if not selected_items:
            return
        
        row = selected_items[0].row()
        category_id = self.table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        
        dialog = CategoryDialog(self, category_id=category_id)
        if dialog.exec():
            self._load_data_wrapper()
    
    def delete_category(self):
        """Supprime la catégorie sélectionnée"""
        selected_items = self.table.selectedItems()
        if not selected_items:
            return
        
        row = selected_items[0].row()
        category_id = self.table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        category_name = self.table.item(row, 1).text()
        
        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer la catégorie '{category_name}' ?\n\n"
            "Cette action désactivera la catégorie mais ne la supprimera pas définitivement.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self._delete_category_wrapper(category_id)
    
    def _delete_category_wrapper(self, category_id):
        """Wrapper pour supprimer la catégorie de manière asynchrone"""
        self.loading_overlay.show()
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._delete_category_async(category_id))
            self._load_data_wrapper()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()
            self.loading_overlay.hide()
    
    async def _delete_category_async(self, category_id):
        """Désactive la catégorie de manière asynchrone"""
        # Récupérer la catégorie
        category = await self.service.get(category_id)
        
        # Mettre à jour la catégorie pour la désactiver
        category_data = {
            "is_active": False
        }
        
        await self.service.update(category_id, category_data)
