"""
Boîte de dialogue pour gérer les catégories d'articles.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QComboBox, QTextEdit, QPushButton, QDialogButtonBox, QMessageBox,
    QLabel, QCheckBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon
import asyncio

from app.core.services.item_category_service import ItemCategoryService

class CategoryDialog(QDialog):
    """Boîte de dialogue pour ajouter ou modifier une catégorie d'article"""
    
    def __init__(self, parent=None, category_id=None):
        super().__init__(parent)
        self.category_id = category_id
        self.service = ItemCategoryService()
        self.categories = []
        self.setup_ui()
        self._load_categories_wrapper()
        if category_id:
            self._load_category_data_wrapper()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.setWindowTitle("Catégorie d'article")
        self.setMinimumWidth(400)
        layout = QVBoxLayout(self)
        
        form = QFormLayout()
        
        self.name_edit = QLineEdit()
        self.code_edit = QLineEdit()
        self.code_edit.setMaxLength(10)
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.parent_combo = QComboBox()
        self.is_active_check = QCheckBox("Catégorie active")
        self.is_active_check.setChecked(True)
        
        # Ajouter les champs au formulaire
        form.addRow("Nom:", self.name_edit)
        form.addRow("Code:", self.code_edit)
        form.addRow("Description:", self.description_edit)
        form.addRow("Catégorie parente:", self.parent_combo)
        form.addRow("", self.is_active_check)
        
        layout.addLayout(form)
        
        # Boutons
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.validate_and_accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def _load_categories_wrapper(self):
        """Wrapper pour charger les catégories de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._load_categories_async())
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des catégories: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()
    
    async def _load_categories_async(self):
        """Charge les catégories d'articles de manière asynchrone"""
        # Récupérer toutes les catégories
        self.categories = await self.service.get_all_categories(include_inactive=True)
        
        # Vider le combo
        self.parent_combo.clear()
        
        # Ajouter l'option "Aucune"
        self.parent_combo.addItem("Aucune", None)
        
        # Ajouter les catégories au combo
        for category in self.categories:
            # Ne pas ajouter la catégorie en cours d'édition
            if self.category_id and category.id == self.category_id:
                continue
            self.parent_combo.addItem(category.name, category.id)
    
    def _load_category_data_wrapper(self):
        """Wrapper pour charger les données de la catégorie de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._load_category_data_async())
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de la catégorie: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()
    
    async def _load_category_data_async(self):
        """Charge les données de la catégorie pour l'édition de manière asynchrone"""
        category = await self.service.get(self.category_id)
        if category:
            self.name_edit.setText(category.name)
            self.code_edit.setText(category.code)
            self.description_edit.setText(category.description or "")
            self.is_active_check.setChecked(category.is_active)
            
            # Sélectionner la catégorie parente
            if category.parent_id:
                index = self.parent_combo.findData(category.parent_id)
                if index >= 0:
                    self.parent_combo.setCurrentIndex(index)
    
    def validate_and_accept(self):
        """Valide les données et accepte le dialogue"""
        # Vérifier que les champs obligatoires sont remplis
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Validation", "Veuillez saisir un nom.")
            return
        
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "Validation", "Veuillez saisir un code.")
            return
        
        # Vérifier que le code est valide
        code = self.code_edit.text().strip()
        if len(code) < 3 or len(code) > 10:
            QMessageBox.warning(self, "Validation", "Le code doit contenir entre 3 et 10 caractères.")
            return
        
        # Sauvegarder la catégorie
        self._save_category_wrapper()
    
    def _save_category_wrapper(self):
        """Wrapper pour sauvegarder la catégorie de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._save_category_async())
            super().accept()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()
    
    def get_category_data(self):
        """Retourne les données de la catégorie"""
        return {
            "name": self.name_edit.text().strip(),
            "code": self.code_edit.text().strip().upper(),
            "description": self.description_edit.toPlainText().strip(),
            "parent_id": self.parent_combo.currentData(),
            "is_active": self.is_active_check.isChecked()
        }
    
    async def _save_category_async(self):
        """Enregistre les modifications de manière asynchrone"""
        data = self.get_category_data()
        
        if self.category_id:
            await self.service.update(self.category_id, data)
        else:
            await self.service.create_category(data)
