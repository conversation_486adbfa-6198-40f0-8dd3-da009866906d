from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QComboBox, QSpinBox, QDoubleSpinBox, QPushButton,
    QDialogButtonBox, QMessageBox, QLabel, QToolButton
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon
from app.core.services.inventory_service import InventoryService
from app.core.services.item_category_service import ItemCategoryService
from app.core.models.inventory import LegacyItemCategory, ItemStatus, ItemCondition
import random
import string
import datetime
import asyncio

class ItemDialog(QDialog):

    def __init__(self, parent=None, item_id=None):
        super().__init__(parent)
        self.item_id = item_id
        self.service = InventoryService()
        self.category_service = ItemCategoryService()
        self.categories = []
        self._updating_fields = False  # Pour éviter les boucles de signaux
        self.setup_ui()
        self._load_categories_wrapper()
        
        # Configuration des spinboxes
        self.purchase_price_spin.setKeyboardTracking(False)
        self.margin_spin.setKeyboardTracking(False)
        self.sale_price_spin.setKeyboardTracking(False)

        # Valeurs par défaut
        self.margin_spin.setValue(30)  # 30% de marge par défaut
        
        # Connecter les signaux
        self.purchase_price_spin.valueChanged.connect(self._on_purchase_price_value_changed)
        self.margin_spin.valueChanged.connect(self._on_margin_value_changed)
        self.sale_price_spin.valueChanged.connect(self._on_sale_price_value_changed)

        # Info-bulle pour guider l'utilisateur
        self.purchase_price_spin.setToolTip("Entrez le prix d'achat - la marge et le prix de vente seront calculés automatiquement")
        self.margin_spin.setToolTip("Ajustez la marge - le prix de vente sera recalculé automatiquement")
        self.sale_price_spin.setToolTip("Ajustez le prix de vente - la marge sera recalculée automatiquement")

        # Charger les données de l'article si on est en mode édition
        if item_id:
            self._load_item_data_wrapper()
        else:
            # Forcer un calcul initial pour les nouveaux articles
            self.update_sale_price()


    def _on_purchase_price_value_changed(self, value):
        """Gestionnaire d'événement pour le changement du prix d'achat"""
        self.update_sale_price()

    def _on_margin_value_changed(self, value):
        """Gestionnaire d'événement pour le changement de la marge"""
        self.update_sale_price()

    def _on_sale_price_value_changed(self, value):
        """Gestionnaire d'événement pour le changement du prix de vente"""
        self.update_margin()


    def _get_float_from_spinbox(self, spinbox):
        try:
            return float(spinbox.lineEdit().text().replace(',', '.'))
        except Exception:
            return 0.0

    def update_sale_price(self):
        """Met à jour le prix de vente en fonction du prix d'achat et de la marge"""
        if not self._updating_fields:
            self._updating_fields = True
            try:
                purchase = self.purchase_price_spin.value()
                margin = self.margin_spin.value()
                sale = purchase * (1 + margin / 100)
                self.sale_price_spin.setValue(round(sale, 2))
            finally:
                self._updating_fields = False

    def update_margin(self):
        """Met à jour la marge en fonction du prix d'achat et du prix de vente"""
        if not self._updating_fields:
            self._updating_fields = True
            try:
                purchase = self.purchase_price_spin.value()
                sale = self.sale_price_spin.value()
                if purchase > 0:
                    margin = ((sale - purchase) / purchase) * 100
                    self.margin_spin.setValue(round(margin, 1))
            finally:
                self._updating_fields = False
    # Cette méthode est dupliquée et a été supprimée car elle écrasait la première implémentation de __init__

    def setup_ui(self):
        self.setWindowTitle("Article d'inventaire")
        layout = QVBoxLayout(self)

        form = QFormLayout()

        self.sku_edit = QLineEdit()
        self.sku_edit.setPlaceholderText("Généré automatiquement")
        self.sku_edit.setReadOnly(True)

        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText("Généré automatiquement")
        self.barcode_edit.setReadOnly(True)

        self.generate_button = QPushButton("Générer")
        self.generate_button.clicked.connect(self.generate_codes)

        sku_layout = QHBoxLayout()
        sku_layout.addWidget(self.sku_edit)
        sku_layout.addWidget(self.generate_button)

        self.name_edit = QLineEdit()

        # Catégorie avec bouton d'ajout
        category_layout = QHBoxLayout()
        self.category_combo = QComboBox()
        self.category_combo.setMinimumWidth(200)
        category_layout.addWidget(self.category_combo)

        # Bouton pour ajouter une nouvelle catégorie
        self.add_category_button = QPushButton("Nouvelle catégorie")
        self.add_category_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_category_button.clicked.connect(self.show_add_category_dialog)
        category_layout.addWidget(self.add_category_button)

        self.quantity_spin = QSpinBox()
        self.min_quantity_spin = QSpinBox()
        self.sale_price_spin = QDoubleSpinBox()
        self.purchase_price_spin = QDoubleSpinBox()
        self.margin_spin = QDoubleSpinBox()
        self.location_edit = QLineEdit()
        
        # Combo pour l'état (neuf/occasion)
        self.condition_combo = QComboBox()
        self.condition_combo.addItem("Neuf", ItemCondition.NEW.value)
        self.condition_combo.addItem("Occasion", ItemCondition.USED.value)

        # Configuration des widgets de quantité
        self.quantity_spin.setRange(0, 999999)
        self.min_quantity_spin.setRange(0, 999999)
        
        # Configuration du prix d'achat
        self.purchase_price_spin.setRange(0.01, 999999.99)  # Prix minimum de 0.01
        self.purchase_price_spin.setDecimals(2)
        self.purchase_price_spin.setSuffix(" DA")
        self.purchase_price_spin.setSingleStep(0.01)
        self.purchase_price_spin.setValue(0.01)  # Valeur par défaut pour éviter la division par zéro
        
        # Configuration du prix de vente
        self.sale_price_spin.setRange(0.01, 999999.99)  # Prix minimum de 0.01
        self.sale_price_spin.setDecimals(2)
        self.sale_price_spin.setSuffix(" DA")
        self.sale_price_spin.setSingleStep(0.01)
        
        # Configuration de la marge
        self.margin_spin.setRange(0, 1000)  # Permettre des marges jusqu'à 1000%
        self.margin_spin.setDecimals(1)
        self.margin_spin.setSuffix(" %")
        self.margin_spin.setSingleStep(1.0)  # Pas de 1% pour la marge

        # Ajout des champs au formulaire
        form.addRow("SKU:", sku_layout)
        form.addRow("Code-barres:", self.barcode_edit)
        form.addRow("Nom:", self.name_edit)
        form.addRow("Catégorie:", category_layout)
        form.addRow("Quantité:", self.quantity_spin)
        form.addRow("Quantité minimum:", self.min_quantity_spin)
        form.addRow("Prix d'achat:", self.purchase_price_spin)
        form.addRow("Prix de vente:", self.sale_price_spin)
        form.addRow("Marge bénéficiaire:", self.margin_spin)
        form.addRow("Emplacement:", self.location_edit)
        form.addRow("État:", self.condition_combo)

        layout.addLayout(form)

        # Boutons
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.validate_and_accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def _load_categories_wrapper(self):
        """Wrapper pour charger les catégories de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._load_categories_async())
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des catégories: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def _load_categories_async(self):
        """Charge les catégories d'articles de manière asynchrone"""
        # Récupérer toutes les catégories
        self.categories = await self.category_service.get_all_categories()

        # Vider le combo
        self.category_combo.clear()

        # Ajouter les catégories au combo
        for category in self.categories:
            self.category_combo.addItem(category.name, category.id)

    def _load_item_data_wrapper(self):
        """Wrapper pour charger les données de l'article de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._load_item_data_async())
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de l'article: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def _load_item_data_async(self):
        """Charge les données de l'article pour l'édition de manière asynchrone"""
        item = await self.service.get(self.item_id)
        if item:
            self.sku_edit.setText(item.sku)
            if item.barcode:
                self.barcode_edit.setText(item.barcode)
            self.name_edit.setText(item.name)

            # Sélectionner la catégorie
            if item.category_id:
                index = self.category_combo.findData(item.category_id)
                if index >= 0:
                    self.category_combo.setCurrentIndex(index)
            elif item.category:
                for i in range(self.category_combo.count()):
                    if self.category_combo.itemText(i).lower() == item.category.lower():
                        self.category_combo.setCurrentIndex(i)
                        break

            self.quantity_spin.setValue(item.quantity)
            self.min_quantity_spin.setValue(item.minimum_quantity)
            # Bloquer les signaux pour éviter recalculs multiples
            self._updating_fields = True
            self.purchase_price_spin.setValue(getattr(item, 'purchase_price', 0))
            self.margin_spin.setValue(getattr(item, 'margin_percent', 0))
            self.sale_price_spin.setValue(getattr(item, 'unit_price', 0))
            self._updating_fields = False
            self.location_edit.setText(item.location)
            
            # Sélectionner l'état
            condition = getattr(item, 'condition', ItemCondition.NEW)
            index = self.condition_combo.findData(condition.value if hasattr(condition, 'value') else condition)
            if index >= 0:
                self.condition_combo.setCurrentIndex(index)

    def show_add_category_dialog(self):
        """Affiche la boîte de dialogue d'ajout de catégorie"""
        from PyQt6.QtWidgets import QInputDialog, QLineEdit

        # Demander le nom de la catégorie
        name, ok = QInputDialog.getText(
            self, "Nouvelle catégorie", "Nom de la catégorie:", QLineEdit.EchoMode.Normal
        )

        if ok and name:
            # Demander le code de la catégorie
            code, ok = QInputDialog.getText(
                self, "Nouvelle catégorie", "Code de la catégorie (3-10 caractères):", QLineEdit.EchoMode.Normal
            )

            if ok and code:
                # Vérifier que le code est valide
                if len(code) < 3 or len(code) > 10:
                    QMessageBox.warning(self, "Validation", "Le code doit contenir entre 3 et 10 caractères.")
                    return

                # Demander la description de la catégorie
                description, ok = QInputDialog.getText(
                    self, "Nouvelle catégorie", "Description (optionnelle):", QLineEdit.EchoMode.Normal
                )

                if ok:
                    # Créer la catégorie
                    self._create_category_wrapper(name, code.upper(), description)

    def _create_category_wrapper(self, name, code, description):
        """Wrapper pour créer une catégorie de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._create_category_async(name, code, description))
            # Recharger les catégories
            loop.run_until_complete(self._load_categories_async())

            # Sélectionner la nouvelle catégorie
            for i in range(self.category_combo.count()):
                if self.category_combo.itemText(i) == name:
                    self.category_combo.setCurrentIndex(i)
                    break
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la création de la catégorie: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def _create_category_async(self, name, code, description):
        """Crée une nouvelle catégorie de manière asynchrone"""
        category_data = {
            "name": name,
            "code": code,
            "description": description
        }

        await self.category_service.create_category(category_data)

    def generate_codes(self):
        """Génère automatiquement le SKU et le code-barres"""
        from app.utils.sku_generator import SKUGenerator

        # Récupérer les informations de la catégorie sélectionnée
        category_name = self.category_combo.currentText()
        category_id = self.category_combo.currentData()

        # Générer le SKU en utilisant le générateur centralisé
        sku = SKUGenerator.generate_sku(
            category_id=category_id,
            category_name=category_name,
            categories=self.categories
        )
        self.sku_edit.setText(sku)

        # Générer le code-barres en utilisant le générateur centralisé
        barcode = SKUGenerator.generate_barcode()
        self.barcode_edit.setText(barcode)

    def validate_and_accept(self):
        """Valide les données et accepte le dialogue"""
        # Calculer une dernière fois la marge avant de sauvegarder
        self.update_margin()
        
        # Valider les données
        data = {
            "sku": self.sku_edit.text(),
            "barcode": self.barcode_edit.text(),
            "name": self.name_edit.text(),
            "quantity": self.quantity_spin.value(),
            "minimum_quantity": self.min_quantity_spin.value(),
            "purchase_price": self.purchase_price_spin.value(),
            "unit_price": self.sale_price_spin.value(),
            "margin_percent": self.margin_spin.value(),
            "location": self.location_edit.text()
        }
        
        # Valider les champs obligatoires
        if not data["name"]:
            QMessageBox.warning(self, "Validation", "Le nom est obligatoire.")
            self.name_edit.setFocus()
            return
            
        if data["purchase_price"] <= 0:
            QMessageBox.warning(self, "Validation", "Le prix d'achat doit être supérieur à 0.")
            self.purchase_price_spin.setFocus()
            return
            
        if data["unit_price"] <= 0:
            QMessageBox.warning(self, "Validation", "Le prix de vente doit être supérieur à 0.")
            self.sale_price_spin.setFocus()
            return
        
        # Récupérer la catégorie sélectionnée
        category_id = self.category_combo.currentData()
        if category_id:
            data["category_id"] = category_id

        # Sauvegarder l'article
        self.data = data
        # Appeler le wrapper de sauvegarde au lieu de simplement accepter
        self._save_item_wrapper()

    def _save_item_wrapper(self):
        """Wrapper pour exécuter _save_item_async de manière asynchrone"""
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._save_item_async())
            super().accept()
        except Exception as e:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def get_item_data(self):
        """Retourne les données de l'article"""
        # Récupérer l'ID de la catégorie sélectionnée
        category_id = self.category_combo.currentData()

        # Trouver la catégorie correspondante pour la compatibilité avec l'ancien système
        legacy_category = None
        category_name = self.category_combo.currentText().lower()

        # Essayer de faire correspondre avec les catégories existantes
        if "pièce" in category_name or "part" in category_name:
            legacy_category = LegacyItemCategory.PART
        elif "outil" in category_name or "tool" in category_name:
            legacy_category = LegacyItemCategory.TOOL
        elif "consommable" in category_name or "consumable" in category_name:
            legacy_category = LegacyItemCategory.CONSUMABLE
        elif "équipement" in category_name or "equipment" in category_name:
            legacy_category = LegacyItemCategory.EQUIPMENT
        else:
            # Par défaut, utiliser PART
            legacy_category = LegacyItemCategory.PART

        # Calcul du statut selon la logique stricte
        quantity = self.quantity_spin.value()
        minimum_quantity = self.min_quantity_spin.value()
        if quantity == 0:
            status = ItemStatus.OUT_OF_STOCK
        elif quantity > 0 and quantity <= minimum_quantity:
            status = ItemStatus.LOW_STOCK
        else:
            status = ItemStatus.AVAILABLE

        # Récupérer l'état sélectionné
        condition_value = self.condition_combo.currentData()
        condition = ItemCondition(condition_value) if condition_value else ItemCondition.NEW
        
        return {
            "sku": self.sku_edit.text(),
            "name": self.name_edit.text(),
            "category": legacy_category,  # Pour la compatibilité
            "category_id": category_id,   # Nouvelle relation
            "quantity": quantity,
            "minimum_quantity": minimum_quantity,
            "purchase_price": self.purchase_price_spin.value(),
            "unit_price": self.sale_price_spin.value(),
            "margin_percent": self.margin_spin.value(),
            "location": self.location_edit.text(),
            "barcode": self.barcode_edit.text(),
            "condition": condition,       # État (neuf/occasion)
            # Valeurs par défaut pour les champs obligatoires
            "description": "",
            "supplier_id": 1,  # Valeur temporaire
            "status": status,
            "specifications": {},
            "is_active": True
        }

    async def _save_item_async(self):
        """Enregistre les modifications de manière asynchrone"""
        data = self.get_item_data()

        # Import ici pour éviter les imports circulaires
        from app.core.models.inventory import InventoryItemPydantic

        if self.item_id:
            await self.service.update(self.item_id, data)
        else:
            # Ne pas spécifier d'ID pour laisser SQLite le générer automatiquement
            item_model = InventoryItemPydantic(**data)
            await self.service.create(item_model)