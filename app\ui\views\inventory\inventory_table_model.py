from PyQt6.QtCore import Qt, QAbstractTableModel
from typing import List, Any
from app.core.services.inventory_service import InventoryService

class InventoryTableModel(QAbstractTableModel):
    def __init__(self):
        super().__init__()
        # Créer une session indépendante pour le modèle de table
        from app.core.models.config import session_factory
        self.db = session_factory()
        self.service = InventoryService(self.db)  # Utiliser la même session pour le service
        self.items = []
        self.headers = [
            "SKU",
            "Nom",
            "Catégorie",
            "Quantité",
            "Emplacement",
            "Prix d'achat",
            "Prix de vente",
            "Marge bénéficiaire",
            "Statut",
            "État"
        ]

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("InventoryTableModel: Session de base de données fermée")

    async def load_data(self):
        """Charge les données depuis le service et corrige les statuts incohérents"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info("Début de load_data dans le modèle de table d'inventaire")

        # Préparer une nouvelle session et charger les données avant de remplacer
        old_db = getattr(self, 'db', None)
        try:
            from app.core.models.config import session_factory
            new_db = session_factory()
            new_service = InventoryService(new_db)

            logger.info("Chargement des articles depuis le service d'inventaire")
            new_items = await new_service.get_all()
            logger.info(f"Articles chargés: {len(new_items)}")

            # Correction automatique des statuts incohérents (utiliser la nouvelle session)
            try:
                from app.core.models.inventory import ItemStatus
                updates_made = False
                for item in new_items:
                    if item is None:
                        continue
                    if item.quantity == 0:
                        correct_status = ItemStatus.OUT_OF_STOCK
                    elif item.quantity > 0 and item.quantity <= item.minimum_quantity:
                        correct_status = ItemStatus.LOW_STOCK
                    else:
                        correct_status = ItemStatus.AVAILABLE
                    if item.status != correct_status:
                        logger.warning(
                            f"Correction du statut de l'article {getattr(item, 'sku', '?')} : {item.status} -> {correct_status}"
                        )
                        item.status = correct_status
                        updates_made = True
                if updates_made:
                    new_db.commit()
            except Exception:
                # Ne pas bloquer le chargement si la correction échoue
                pass

            # Remplacer atomiquement le modèle et la session
            self.beginResetModel()
            self.db = new_db
            self.service = new_service
            self.items = new_items
            self.endResetModel()

        except Exception as e:
            logger.error(f"Erreur lors du chargement des articles: {e}")
            import traceback
            traceback.print_exc()
            self.beginResetModel()
            self.items = []
            self.endResetModel()
        finally:
            # Fermer l'ancienne session seulement après remplacement
            try:
                if old_db is not None and old_db is not getattr(self, 'db', None):
                    old_db.close()
                    logger.info("InventoryTableModel: Ancienne session fermée")
            except Exception:
                pass

        logger.info("Fin de load_data dans le modèle de table d'inventaire")

    def rowCount(self, parent=None):
        return len(self.items)

    def columnCount(self, parent=None):
        return len(self.headers)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.ItemDataRole.DisplayRole:
            try:
                item = self.items[index.row()]
                column = index.column()

                # Vérifier si l'objet est détaché sans déclencher de lazy-load
                try:
                    from sqlalchemy.orm import object_session
                    session_bound = object_session(item) is not None
                except Exception:
                    session_bound = True

                if not session_bound:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error("Erreur lors de l'accès à l'objet: instance détachée")

                    # Essayer de rafraîchir l'objet en utilisant son ID directement depuis __dict__
                    try:
                        item_id = item.__dict__.get('id')
                        if item_id is None:
                            return "Erreur"

                        refreshed = self.db.query(self.service.model).filter(self.service.model.id == item_id).first()
                        if refreshed is None:
                            return "Erreur"

                        self.items[index.row()] = refreshed
                        item = refreshed
                        logger.info(f"Objet rafraîchi avec succès: {item_id}")
                    except Exception as e:
                        logger.error(f"Erreur lors du rafraîchissement de l'objet: {e}")
                        return "Erreur"

                # Formatage des données selon la colonne
                if column == 0:
                    return getattr(item, 'sku', 'N/A')
                elif column == 1:
                    return getattr(item, 'name', 'N/A')
                elif column == 2:
                    # Afficher le nom réel de la catégorie si déjà chargé, sans lazy-load
                    category_obj = item.__dict__.get('category_relation', None)
                    if category_obj and hasattr(category_obj, 'name'):
                        return category_obj.name
                    # Fallback sur l'enum ou N/A sans déclencher d'accès relationnel
                    category = getattr(item, 'category', None)
                    return getattr(category, 'value', 'N/A')
                elif column == 3:
                    return str(getattr(item, 'quantity', 'N/A'))
                elif column == 4:
                    return getattr(item, 'location', 'N/A')
                elif column == 5:
                    purchase_price = getattr(item, 'purchase_price', 0)
                    return f"{purchase_price:.2f} DA"
                elif column == 6:
                    unit_price = getattr(item, 'unit_price', 0)
                    return f"{unit_price:.2f} DA"
                elif column == 7:
                    margin = getattr(item, 'margin_percent', 0)
                    return f"{margin:.1f} %"
                elif column == 8:
                    status = getattr(item, 'status', None)
                    # Traduction française
                    status_map = {
                        'available': 'Disponible',
                        'low_stock': 'Stock bas',
                        'out_of_stock': 'Rupture',
                        'discontinued': 'Arrêté',
                    }
                    # Affichage de debug : valeur brute
                    debug_val = status.value if status else str(status)
                    print(f"[DEBUG STATUT] Ligne {index.row()} : {debug_val}")
                    if status:
                        return status_map.get(status.value, status.value)
                    return 'N/A'
                elif column == 9:
                    condition = getattr(item, 'condition', None)
                    # Traduction française
                    condition_map = {
                        'new': 'Neuf',
                        'used': 'Occasion',
                    }
                    if condition:
                        return condition_map.get(condition.value, condition.value)
                    return 'Neuf'  # Valeur par défaut
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Erreur lors de l'affichage des données: {e}")
                import traceback
                traceback.print_exc()
                return "Erreur"

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            # Centrer tous les headers et toutes les cellules sauf Nom (col 1)
            if index.column() == 1:
                return Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft
            else:
                return Qt.AlignmentFlag.AlignCenter

        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal:
            if role == Qt.ItemDataRole.DisplayRole:
                return self.headers[section]
            elif role == Qt.ItemDataRole.TextAlignmentRole:
                return Qt.AlignmentFlag.AlignCenter
        return None

    def get_item_id(self, row: int) -> int:
        """Retourne l'ID de l'article à la ligne spécifiée"""
        try:
            item = self.items[row]

            # Vérifier si l'objet est détaché
            try:
                return item.id
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Erreur lors de l'accès à l'ID de l'objet: {e}")

                # Essayer de rafraîchir l'objet
                try:
                    # Récupérer l'ID de l'objet (cela peut échouer)
                    item_id = getattr(item, '_sa_instance_state', None)
                    if item_id and hasattr(item_id, 'key') and item_id.key:
                        item_id = item_id.key[1]
                    else:
                        # Si nous ne pouvons pas récupérer l'ID, retourner -1
                        return -1

                    # Rafraîchir l'objet depuis la base de données
                    item = self.db.query(self.service.model).filter(self.service.model.id == item_id).first()

                    # Mettre à jour l'objet dans la liste
                    self.items[row] = item

                    logger.info(f"Objet rafraîchi avec succès: {item_id}")
                    return item.id
                except Exception as e:
                    logger.error(f"Erreur lors du rafraîchissement de l'objet: {e}")
                    return -1
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors de la récupération de l'ID: {e}")
            import traceback
            traceback.print_exc()
            return -1