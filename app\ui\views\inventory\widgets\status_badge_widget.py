from PyQt6.QtWidgets import <PERSON>Widget, QHBoxLayout, QLabel, QFrame, QStyledItemDelegate, QStyleOptionViewItem
from PyQt6.QtCore import Qt, pyqtSignal, QRect
from PyQt6.QtGui import QColor, QFont, QPalette, QPainter

class StatusBadgeWidget(QFrame):
    """Widget pour afficher un badge de statut coloré dans les tableaux"""
    
    clicked = pyqtSignal(str)  # Signal émis quand le badge est cliqué
    
    def __init__(self, status, show_icon=True, compact=False):
        super().__init__()
        self.status = status
        self.show_icon = show_icon
        self.compact = compact
        self.setup_ui()
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
    def get_status_config(self, status):
        """Retourne la configuration complète pour un statut"""
        configs = {
            'available': {
                'text': 'Disponible',
                'icon': '✓',
                'color': QColor(76, 175, 80),  # Vert
                'bg_color': QColor(232, 245, 233),  # Vert très clair
                'text_color': QColor(27, 94, 32),  # Vert foncé
            },
            'low_stock': {
                'text': 'Stock bas',
                'icon': '⚠',
                'color': QColor(255, 152, 0),  # Orange
                'bg_color': QColor(255, 243, 224),  # Orange très clair
                'text_color': QColor(194, 24, 91),  # Orange foncé
            },
            'out_of_stock': {
                'text': 'Rupture',
                'icon': '✗',
                'color': QColor(244, 67, 54),  # Rouge
                'bg_color': QColor(255, 235, 238),  # Rouge très clair
                'text_color': QColor(183, 28, 28),  # Rouge foncé
            },
            'discontinued': {
                'text': 'Arrêté',
                'icon': '⊘',
                'color': QColor(158, 158, 158),  # Gris
                'bg_color': QColor(245, 245, 245),  # Gris très clair
                'text_color': QColor(97, 97, 97),  # Gris foncé
            },
        }
        return configs.get(status, configs['discontinued'])
    
    def setup_ui(self):
        """Configure l'interface du badge"""
        config = self.get_status_config(self.status)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(6, 3, 6, 3) if self.compact else layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(3)
        
        # Icône (optionnelle)
        if self.show_icon:
            icon_label = QLabel(config['icon'])
            icon_label.setStyleSheet(f"""
                font-size: {'10px' if self.compact else '12px'};
                font-weight: bold;
                color: {config['text_color'].name()};
            """)
            layout.addWidget(icon_label)
        
        # Texte
        text_label = QLabel(config['text'])
        text_label.setStyleSheet(f"""
            font-size: {'10px' if self.compact else '11px'};
            font-weight: 500;
            color: {config['text_color'].name()};
        """)
        layout.addWidget(text_label)
        
        # Style du badge
        self.setStyleSheet(f"""
            StatusBadgeWidget {{
                background-color: {config['bg_color'].name()};
                border: 1px solid {config['color'].name()};
                border-radius: {'8px' if self.compact else '12px'};
                padding: {'1px 4px' if self.compact else '2px 6px'};
            }}
            StatusBadgeWidget:hover {{
                background-color: {config['color'].name()};
                color: white;
            }}
            StatusBadgeWidget:hover QLabel {{
                color: white;
            }}
        """)
    
    def mousePressEvent(self, event):
        """Gère le clic sur le badge"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.status)
        super().mousePressEvent(event)

class AlertBadgeWidget(QFrame):
    """Widget pour afficher des badges d'alerte multiples"""
    
    def __init__(self, alerts):
        super().__init__()
        self.alerts = alerts
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface des badges d'alerte"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(4, 2, 4, 2)
        layout.setSpacing(4)
        
        if not self.alerts:
            # Aucune alerte
            ok_label = QLabel("✅ OK")
            ok_label.setStyleSheet("""
                color: #4caf50;
                font-size: 10px;
                font-weight: 500;
            """)
            layout.addWidget(ok_label)
        else:
            # Afficher les alertes
            for alert in self.alerts:
                alert_badge = QLabel(alert)
                alert_badge.setStyleSheet("""
                    background-color: #ffebee;
                    color: #c62828;
                    border: 1px solid #ef5350;
                    border-radius: 8px;
                    padding: 2px 6px;
                    font-size: 9px;
                    font-weight: 500;
                """)
                layout.addWidget(alert_badge)
        
        layout.addStretch()
        
        # Style du conteneur
        self.setStyleSheet("""
            AlertBadgeWidget {
                background-color: transparent;
                border: none;
            }
        """)

class StatusBadgeDelegate(QStyledItemDelegate):
    """Délégué personnalisé pour afficher des badges de statut dans les cellules"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def paint(self, painter, option, index):
        """Dessine le badge de statut dans la cellule"""
        if not index.isValid():
            return
            
        # Récupérer les données de la cellule
        status = index.data()
        if not status:
            super().paint(painter, option, index)
            return
            
        # Configuration du badge selon le statut
        config = self.get_status_config(status)
        if not config:
            super().paint(painter, option, index)
            return
            
        # Sauvegarder l'état du painter
        painter.save()
        
        # Configuration du rectangle de la cellule
        rect = option.rect
        cell_rect = QRect(rect.x() + 2, rect.y() + 2, rect.width() - 4, rect.height() - 4)
        
        # Dessiner le fond du badge
        painter.setBrush(config['bg_color'])
        painter.setPen(config['color'])
        painter.drawRoundedRect(cell_rect, 8, 8)
        
        # Configuration du texte
        painter.setPen(config['text_color'])
        font = QFont()
        font.setPointSize(9)
        font.setWeight(QFont.Weight.Medium)
        painter.setFont(font)
        
        # Centrer le texte
        text_rect = cell_rect
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter, config['text'])
        
        # Restaurer l'état du painter
        painter.restore()
    
    def get_status_config(self, status):
        """Retourne la configuration pour un statut donné"""
        # Nettoyer le statut (enlever les symboles)
        clean_status = status.replace('✓ ', '').replace('⚠ ', '').replace('✗ ', '').replace('⊘ ', '')
        
        configs = {
            'Disponible': {
                'text': '✓ Disponible',
                'color': QColor(76, 175, 80),
                'bg_color': QColor(232, 245, 233),
                'text_color': QColor(27, 94, 32),
            },
            'Stock bas': {
                'text': '⚠ Stock bas',
                'color': QColor(255, 152, 0),
                'bg_color': QColor(255, 243, 224),
                'text_color': QColor(194, 24, 91),
            },
            'Rupture': {
                'text': '✗ Rupture',
                'color': QColor(244, 67, 54),
                'bg_color': QColor(255, 235, 238),
                'text_color': QColor(183, 28, 28),
            },
            'Arrêté': {
                'text': '⊘ Arrêté',
                'color': QColor(158, 158, 158),
                'bg_color': QColor(245, 245, 245),
                'text_color': QColor(97, 97, 97),
            },
        }
        
        return configs.get(clean_status)

class AlertBadgeDelegate(QStyledItemDelegate):
    """Délégué personnalisé pour afficher des badges d'alerte dans les cellules"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def paint(self, painter, option, index):
        """Dessine les badges d'alerte dans la cellule"""
        if not index.isValid():
            return
            
        # Récupérer les données de la cellule
        alerts_text = index.data()
        if not alerts_text:
            super().paint(painter, option, index)
            return
            
        # Sauvegarder l'état du painter
        painter.save()
        
        # Configuration du rectangle de la cellule
        rect = option.rect
        cell_rect = QRect(rect.x() + 2, rect.y() + 2, rect.width() - 4, rect.height() - 4)
        
        # Si c'est "✅ OK", afficher en vert
        if alerts_text == "✅ OK":
            painter.setBrush(QColor(232, 245, 233))
            painter.setPen(QColor(76, 175, 80))
            painter.drawRoundedRect(cell_rect, 6, 6)
            painter.setPen(QColor(27, 94, 32))
        else:
            # Sinon, afficher en rouge pour les alertes
            painter.setBrush(QColor(255, 235, 238))
            painter.setPen(QColor(244, 67, 54))
            painter.drawRoundedRect(cell_rect, 6, 6)
            painter.setPen(QColor(183, 28, 28))
        
        # Configuration du texte
        font = QFont()
        font.setPointSize(8)
        font.setWeight(QFont.Weight.Medium)
        painter.setFont(font)
        
        # Centrer le texte
        text_rect = cell_rect
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter, alerts_text)
        
        # Restaurer l'état du painter
        painter.restore()

class StatusIndicatorWidget(QWidget):
    """Widget pour afficher un indicateur de statut avec icône et texte"""
    
    def __init__(self, status, quantity=None, minimum_quantity=None):
        super().__init__()
        self.status = status
        self.quantity = quantity
        self.minimum_quantity = minimum_quantity
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface de l'indicateur"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(4, 2, 4, 2)
        layout.setSpacing(6)
        
        # Indicateur de statut
        status_badge = StatusBadgeWidget(self.status, show_icon=True, compact=True)
        layout.addWidget(status_badge)
        
        # Informations supplémentaires si disponibles
        if self.quantity is not None and self.minimum_quantity is not None:
            if self.quantity <= self.minimum_quantity and self.quantity > 0:
                warning_label = QLabel(f"⚠ {self.quantity}/{self.minimum_quantity}")
                warning_label.setStyleSheet("""
                    color: #ff9800;
                    font-size: 9px;
                    font-weight: bold;
                """)
                layout.addWidget(warning_label)
            elif self.quantity == 0:
                critical_label = QLabel("🚨 0")
                critical_label.setStyleSheet("""
                    color: #f44336;
                    font-size: 9px;
                    font-weight: bold;
                """)
                layout.addWidget(critical_label)
        
        layout.addStretch()
        
        # Style du conteneur
        self.setStyleSheet("""
            StatusIndicatorWidget {
                background-color: transparent;
                border: none;
            }
        """) 