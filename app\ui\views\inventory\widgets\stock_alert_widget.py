
from PyQt6.QtWidgets import Q<PERSON>idget, QVBoxLayout, QLabel, QFrame, QHBoxLayout, QSizePolicy, QTableView, QHeaderView
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex
from PyQt6.QtGui import QColor
from app.core.services.inventory_service import InventoryService
from .status_badge_widget import StatusBadgeDelegate

class StockAlertTableModel(QAbstractTableModel):
    """Modèle simple pour afficher les alertes de stock dans un tableau"""
    def __init__(self, items=None):
        super().__init__()
        self.items = items or []
        self.headers = ["Nom", "Qté", "Minimum", "Statut"]

    def rowCount(self, parent=QModelIndex()):
        return len(self.items)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return None
        item = self.items[index.row()]
        col = index.column()
        if role == Qt.ItemDataRole.DisplayRole:
            if col == 0:
                return getattr(item, 'name', 'N/A')
            if col == 1:
                return str(getattr(item, 'quantity', ''))
            if col == 2:
                return str(getattr(item, 'minimum_quantity', ''))
            if col == 3:
                status = getattr(item, 'status', None)
                # Map to French labels like the main table for delegate rendering
                if status:
                    status_map = {
                        'available': 'Disponible',
                        'low_stock': 'Stock bas',
                        'out_of_stock': 'Rupture',
                        'discontinued': 'Arrêté',
                    }
                    return status_map.get(getattr(status, 'value', ''), getattr(status, 'value', 'N/A'))
                return 'N/A'
        if role == Qt.ItemDataRole.TextAlignmentRole:
            return Qt.AlignmentFlag.AlignCenter if col != 0 else (Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft)
        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal:
            if role == Qt.ItemDataRole.DisplayRole:
                return self.headers[section]
            if role == Qt.ItemDataRole.TextAlignmentRole:
                return Qt.AlignmentFlag.AlignCenter
        return None

    def set_items(self, items):
        # Custom sort: Rupture > Stock bas > Disponible > Arrêté, then by name
        def sort_key(it):
            # Map ItemStatus enum value to French label like in data()
            status = getattr(it, 'status', None)
            if status:
                val = getattr(status, 'value', '')
            else:
                val = ''
            order = {
                'Rupture': 0,
                'Stock bas': 1,
                'Disponible': 2,
                'Arrêté': 3,
            }
            # Convert enum english to french if needed
            fr = {
                'out_of_stock': 'Rupture',
                'low_stock': 'Stock bas',
                'available': 'Disponible',
                'discontinued': 'Arrêté',
            }.get(val, val)
            return (order.get(fr, 99), getattr(it, 'name', ''))

        self.beginResetModel()
        self.items = sorted(items or [], key=sort_key)
        self.endResetModel()

class StockAlertWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.service = InventoryService()
        self.model = StockAlertTableModel([])
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(6)
        layout.setContentsMargins(0, 0, 0, 0)

        # Style du widget principal
        self.setStyleSheet("""
            StockAlertWidget {
                background-color: #ffffff;
                border: 1.5px solid #e0e0e0;
                border-radius: 12px;
                padding: 6px;
            }
        """)

        header = QLabel("\u26A0\uFE0F  Alertes de stock")
        header.setStyleSheet("font-size: 15px; font-weight: 600; color: #d32f2f; margin-bottom: 2px;")
        layout.addWidget(header)

        # Tableau stylé comme le tableau des articles
        self.table = QTableView()
        self.table.setModel(self.model)
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableView.SelectionMode.NoSelection)
        self.table.setSortingEnabled(True)
        self.table.setWordWrap(False)
        self.table.setShowGrid(True)

        self.table.setStyleSheet("""
            QTableView {
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                gridline-color: #e0e0e0;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                selection-background-color: #e3f2fd;
                selection-color: #1976d2;
            }
            QTableView::item { padding: 6px; border: none; }
            QHeaderView::section {
                background-color: #f5f7fa;
                color: #1976d2;
                padding: 10px 6px;
                border: none;
                border-bottom: 2px solid #e0e0e0;
                font-weight: 600;
                font-size: 12px;
            }
        """)

        header_view = self.table.horizontalHeader()
        header_view.setStretchLastSection(False)
        header_view.setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)

        # Largeurs colonnes compactes pour respecter les dimensions actuelles
        col_widths = {0: 160, 1: 60, 2: 80, 3: 100}
        for c, w in col_widths.items():
            self.table.setColumnWidth(c, w)
        header_view.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

        # Délégué badge pour la colonne Statut
        self.table.setItemDelegateForColumn(3, StatusBadgeDelegate(self.table))

        layout.addWidget(self.table)

        # Respecter la hauteur actuelle du widget
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Maximum)
        self.setMaximumHeight(320)

        # Tri par défaut: Statut (Rupture > Stock bas > Disponible/Arrêté) puis Nom
        try:
            self.table.sortByColumn(3, Qt.SortOrder.AscendingOrder)
        except Exception:
            pass

    async def update_alerts(self):
        try:
            # Rafraîchir la session
            try:
                if hasattr(self.service, 'db') and self.service.db:
                    self.service.db.expire_all()
            except Exception:
                pass

            # Charger les alertes
            low_stock_items = await self.service.get_low_stock_items()

            if not low_stock_items:
                try:
                    Model = self.service.model
                    from sqlalchemy import or_
                    low_stock_items = (
                        self.service.db.query(Model)
                        .filter(or_(Model.quantity == 0, Model.quantity <= Model.minimum_quantity))
                        .all()
                    )
                except Exception:
                    low_stock_items = []

            self.model.set_items(low_stock_items)
        except Exception:
            self.model.set_items([])