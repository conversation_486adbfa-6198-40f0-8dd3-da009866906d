"""
Widget pour afficher les informations de valorisation des stocks.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QComboBox, QSpinBox, QTabWidget
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QColor, QPainter, QPixmap
from PyQt6.QtCharts import QChart, QChartView, QPieSeries, QBarSeries, QBarSet, QBarCategoryAxis, QValueAxis
import asyncio
from datetime import datetime

from app.core.services.inventory_valuation_service import InventoryValuationService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class ValuationWidget(QWidget):
    """Widget pour afficher les informations de valorisation des stocks"""

    # Signal émis lorsqu'un produit est sélectionné
    product_selected = pyqtSignal(int)

    def __init__(self, parent=None):
        super().__init__(parent)

        # Service
        self.db = SessionLocal()
        self.valuation_service = InventoryValuationService(self.db)

        # Configuration de l'interface
        self.setup_ui()

        # Indicateur de chargement
        self.is_loading = False

        # Charger les données
        self.load_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def closeEvent(self, event):
        """Appelé lorsque le widget est fermé"""
        # Annuler tout chargement en cours
        self.is_loading = False
        super().closeEvent(event)

    def setup_ui(self):
        """Affichage simple et lisible des valeurs de valorisation"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(6)
        # (Titre supprimé)
        # Valeurs principales + bouton refresh à droite
        values_layout = QHBoxLayout()
        values_layout.setSpacing(18)
        # Valeur totale
        self.value_value = QLabel("0.00 DA")
        self.value_value.setStyleSheet("font-size: 18px; font-weight: bold; color: #1976D2;")
        values_layout.addWidget(QLabel("Valeur totale:"))
        values_layout.addWidget(self.value_value)
        # Nombre d'articles
        self.items_value = QLabel("0")
        self.items_value.setStyleSheet("font-size: 18px; font-weight: bold; color: #388e3c;")
        values_layout.addWidget(QLabel("Articles:"))
        values_layout.addWidget(self.items_value)
        # Quantité totale
        self.quantity_value = QLabel("0")
        self.quantity_value.setStyleSheet("font-size: 18px; font-weight: bold; color: #f9a825;")
        values_layout.addWidget(QLabel("Quantité:"))
        values_layout.addWidget(self.quantity_value)
        # Bouton refresh bien visible à droite
        values_layout.addStretch(1)
        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.setToolTip("Rafraîchir")
        self.refresh_button.setFixedHeight(38)
        self.refresh_button.setMinimumWidth(120)
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: #1976D2;
                color: #fff;
                font-size: 16px;
                font-weight: 600;
                border: none;
                border-radius: 8px;
                padding: 8px 24px;
                box-shadow: 0 2px 8px rgba(25, 118, 210, 0.10);
            }
            QPushButton:hover {
                background: #1565c0;
                color: #fff;
            }
            QPushButton:pressed {
                background: #0d47a1;
            }
        """)
        self.refresh_button.clicked.connect(self.load_data)
        values_layout.addWidget(self.refresh_button)
        main_layout.addLayout(values_layout)

    def load_data(self):
        """Charge uniquement les valeurs globales de stock (version simplifiée)"""
        if self.is_loading:
            print("Chargement déjà en cours, ignoré")
            return
        self.is_loading = True
        if hasattr(self, 'refresh_button'):
            try:
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("Chargement...")
            except RuntimeError:
                pass
        QTimer.singleShot(0, self._load_global_kpis)

    def _load_global_kpis(self):
        """Charge et affiche uniquement les valeurs globales"""
        try:
            inventory_value = self.valuation_service.calculate_inventory_value_sync()
            self.value_value.setText(f"{inventory_value['total_value']:.2f} DA")
            self.items_value.setText(str(inventory_value['total_items']))
            self.quantity_value.setText(str(inventory_value['total_quantity']))
        except Exception as e:
            print(f"Erreur lors du chargement des informations globales: {str(e)}")
        finally:
            self._finish_loading()

    def _load_data_step2(self):
        """Deuxième étape du chargement des données"""
        try:
            # Récupérer les valeurs par catégorie de manière synchrone
            category_values = self.valuation_service.calculate_category_values_sync()

            # Mettre à jour le graphique des catégories
            try:
                self._update_category_chart(category_values)
            except RuntimeError:
                print("Erreur lors de la mise à jour du graphique des catégories")

            # Mettre à jour le tableau des catégories
            try:
                self.category_table.setRowCount(len(category_values))
                for i, category in enumerate(category_values):
                    self.category_table.setItem(i, 0, QTableWidgetItem(category["category"]))
                    self.category_table.setItem(i, 1, QTableWidgetItem(f"{category['total_value']:.2f} DA"))
                    self.category_table.setItem(i, 2, QTableWidgetItem(str(category["total_items"])))
                    self.category_table.setItem(i, 3, QTableWidgetItem(str(category["total_quantity"])))
            except RuntimeError:
                print("Erreur lors de la mise à jour du tableau des catégories")

            # Passer à l'étape suivante
            QTimer.singleShot(0, self._load_data_step3)
        except Exception as e:
            print(f"Erreur lors du chargement des catégories: {str(e)}")
            self._finish_loading()

    def _load_data_step3(self):
        """Troisième étape du chargement des données"""
        try:
            # Récupérer les valeurs par fournisseur de manière synchrone
            supplier_values = self.valuation_service.calculate_supplier_values_sync()

            # Mettre à jour le graphique des fournisseurs
            try:
                self._update_supplier_chart(supplier_values)
            except RuntimeError:
                print("Erreur lors de la mise à jour du graphique des fournisseurs")

            # Mettre à jour le tableau des fournisseurs
            try:
                self.supplier_table.setRowCount(len(supplier_values))
                for i, supplier in enumerate(supplier_values):
                    self.supplier_table.setItem(i, 0, QTableWidgetItem(supplier["supplier_name"]))
                    self.supplier_table.setItem(i, 1, QTableWidgetItem(f"{supplier['total_value']:.2f} DA"))
                    self.supplier_table.setItem(i, 2, QTableWidgetItem(str(supplier["total_items"])))
                    self.supplier_table.setItem(i, 3, QTableWidgetItem(str(supplier["total_quantity"])))
            except RuntimeError:
                print("Erreur lors de la mise à jour du tableau des fournisseurs")

            # Passer à l'étape suivante
            QTimer.singleShot(0, self._load_data_step4)
        except Exception as e:
            print(f"Erreur lors du chargement des fournisseurs: {str(e)}")
            self._finish_loading()

    def _load_data_step4(self):
        """Quatrième étape du chargement des données"""
        try:
            # Récupérer l'analyse ABC de manière synchrone
            abc_analysis = self.valuation_service.calculate_abc_analysis_sync()

            # Mettre à jour le graphique de l'analyse ABC
            try:
                self._update_abc_chart(abc_analysis)
            except RuntimeError:
                print("Erreur lors de la mise à jour du graphique ABC")

            # Mettre à jour le tableau de l'analyse ABC
            try:
                self.abc_table.setRowCount(3)

                # Catégorie A
                self.abc_table.setItem(0, 0, QTableWidgetItem("A"))
                self.abc_table.setItem(0, 1, QTableWidgetItem(str(abc_analysis["category_a"]["count"])))
                self.abc_table.setItem(0, 2, QTableWidgetItem(f"{abc_analysis['category_a']['percent_of_items']:.2f}%"))
                self.abc_table.setItem(0, 3, QTableWidgetItem(f"{abc_analysis['category_a']['percent_of_value']:.2f}%"))

                # Catégorie B
                self.abc_table.setItem(1, 0, QTableWidgetItem("B"))
                self.abc_table.setItem(1, 1, QTableWidgetItem(str(abc_analysis["category_b"]["count"])))
                self.abc_table.setItem(1, 2, QTableWidgetItem(f"{abc_analysis['category_b']['percent_of_items']:.2f}%"))
                self.abc_table.setItem(1, 3, QTableWidgetItem(f"{abc_analysis['category_b']['percent_of_value']:.2f}%"))

                # Catégorie C
                self.abc_table.setItem(2, 0, QTableWidgetItem("C"))
                self.abc_table.setItem(2, 1, QTableWidgetItem(str(abc_analysis["category_c"]["count"])))
                self.abc_table.setItem(2, 2, QTableWidgetItem(f"{abc_analysis['category_c']['percent_of_items']:.2f}%"))
                self.abc_table.setItem(2, 3, QTableWidgetItem(f"{abc_analysis['category_c']['percent_of_value']:.2f}%"))
            except RuntimeError:
                print("Erreur lors de la mise à jour du tableau ABC")

            # Passer à l'étape suivante
            QTimer.singleShot(0, self._load_data_step5)
        except Exception as e:
            print(f"Erreur lors du chargement de l'analyse ABC: {str(e)}")
            self._finish_loading()

    def _load_data_step5(self):
        """Cinquième étape du chargement des données"""
        try:
            # Récupérer les articles à faible rotation de manière synchrone
            slow_moving_items = self.valuation_service.get_slow_moving_items_sync()

            # Mettre à jour le tableau des articles à faible rotation
            try:
                self.slow_table.setRowCount(len(slow_moving_items))
                for i, item in enumerate(slow_moving_items):
                    self.slow_table.setItem(i, 0, QTableWidgetItem(item["product_name"]))
                    self.slow_table.setItem(i, 1, QTableWidgetItem(str(item["quantity"])))
                    self.slow_table.setItem(i, 2, QTableWidgetItem(f"{item['item_value']:.2f} DA"))
                    self.slow_table.setItem(i, 3, QTableWidgetItem(str(item["days_since_last_movement"])))

                    # Formater la date du dernier mouvement
                    last_movement_date = item["last_movement_date"]
                    if last_movement_date:
                        from datetime import datetime
                        if isinstance(last_movement_date, str):
                            date_str = last_movement_date
                        else:
                            date_str = last_movement_date.strftime("%d/%m/%Y")
                    else:
                        date_str = "N/A"
                    self.slow_table.setItem(i, 4, QTableWidgetItem(date_str))

                    # Stocker l'ID du produit dans les données de l'item
                    for j in range(5):
                        self.slow_table.item(i, j).setData(Qt.ItemDataRole.UserRole, item["product_id"])
            except RuntimeError:
                print("Erreur lors de la mise à jour du tableau des articles à faible rotation")

            # Passer à l'étape suivante
            QTimer.singleShot(0, self._load_data_step6)
        except Exception as e:
            print(f"Erreur lors du chargement des articles à faible rotation: {str(e)}")
            self._finish_loading()

    def _load_data_step6(self):
        """Sixième étape du chargement des données"""
        try:
            # Récupérer les articles de haute valeur de manière synchrone
            high_value_items = self.valuation_service.get_high_value_items_sync()

            # Mettre à jour le tableau des articles de haute valeur
            try:
                self.high_table.setRowCount(len(high_value_items))
                for i, item in enumerate(high_value_items):
                    self.high_table.setItem(i, 0, QTableWidgetItem(item["product_name"]))
                    self.high_table.setItem(i, 1, QTableWidgetItem(str(item["quantity"])))
                    self.high_table.setItem(i, 2, QTableWidgetItem(f"{item['purchase_price']:.2f} DA"))
                    self.high_table.setItem(i, 3, QTableWidgetItem(f"{item['item_value']:.2f} DA"))

                    # Stocker l'ID du produit dans les données de l'item
                    for j in range(4):
                        self.high_table.item(i, j).setData(Qt.ItemDataRole.UserRole, item["product_id"])
            except RuntimeError:
                print("Erreur lors de la mise à jour du tableau des articles de haute valeur")
        except Exception as e:
            print(f"Erreur lors du chargement des articles de haute valeur: {str(e)}")
        finally:
            # Terminer le chargement
            self._finish_loading()

    def _finish_loading(self):
        """Termine le processus de chargement"""
        self.is_loading = False
        if hasattr(self, 'refresh_button'):
            try:
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("Rafraîchir")
            except RuntimeError:
                pass
        print("Chargement des données terminé")

    def _update_category_chart(self, category_values):
        """Met à jour le graphique des catégories"""
        try:
            # Effacer le graphique existant
            self.category_chart.removeAllSeries()

            # Créer une série de camembert
            series = QPieSeries()

            # Ajouter les données
            for category in category_values:
                series.append(category["category"], category["total_value"])

            # Ajouter la série au graphique
            self.category_chart.addSeries(series)

            # Mettre à jour le graphique
            self.category_chart.update()
        except RuntimeError as e:
            print(f"Erreur lors de la mise à jour du graphique des catégories: {e}")

    def _update_supplier_chart(self, supplier_values):
        """Met à jour le graphique des fournisseurs"""
        try:
            # Effacer le graphique existant
            self.supplier_chart.removeAllSeries()

            # Créer une série de camembert
            series = QPieSeries()

            # Limiter le nombre de fournisseurs affichés
            top_suppliers = supplier_values[:5]

            # Calculer la valeur totale des autres fournisseurs
            other_value = sum(supplier["total_value"] for supplier in supplier_values[5:])

            # Ajouter les données
            for supplier in top_suppliers:
                series.append(supplier["supplier_name"], supplier["total_value"])

            # Ajouter les autres fournisseurs
            if other_value > 0:
                series.append("Autres", other_value)

            # Ajouter la série au graphique
            self.supplier_chart.addSeries(series)

            # Mettre à jour le graphique
            self.supplier_chart.update()
        except RuntimeError as e:
            print(f"Erreur lors de la mise à jour du graphique des fournisseurs: {e}")

    def _update_abc_chart(self, abc_analysis):
        """Met à jour le graphique de l'analyse ABC"""
        try:
            # Effacer le graphique existant
            self.abc_chart.removeAllSeries()

            # Créer une série de barres pour les articles
            items_set = QBarSet("% Articles")
            items_set.append(abc_analysis["category_a"]["percent_of_items"])
            items_set.append(abc_analysis["category_b"]["percent_of_items"])
            items_set.append(abc_analysis["category_c"]["percent_of_items"])

            # Créer une série de barres pour la valeur
            value_set = QBarSet("% Valeur")
            value_set.append(abc_analysis["category_a"]["percent_of_value"])
            value_set.append(abc_analysis["category_b"]["percent_of_value"])
            value_set.append(abc_analysis["category_c"]["percent_of_value"])

            # Créer une série de barres
            series = QBarSeries()
            series.append(items_set)
            series.append(value_set)

            # Ajouter la série au graphique
            self.abc_chart.addSeries(series)

            # Créer l'axe des catégories
            categories = ["A", "B", "C"]
            axis_x = QBarCategoryAxis()
            axis_x.append(categories)
            self.abc_chart.addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
            series.attachAxis(axis_x)

            # Créer l'axe des valeurs
            axis_y = QValueAxis()
            axis_y.setRange(0, 100)
            axis_y.setTitleText("%")
            self.abc_chart.addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)
            series.attachAxis(axis_y)

            # Mettre à jour le graphique
            self.abc_chart.update()
        except RuntimeError as e:
            print(f"Erreur lors de la mise à jour du graphique ABC: {e}")

    def on_product_selected(self, row, column):
        """Gère la sélection d'un produit"""
        try:
            # Déterminer quelle table a été cliquée
            sender = self.sender()
            if sender == self.slow_table:
                product_id = self.slow_table.item(row, column).data(Qt.ItemDataRole.UserRole)
            else:
                product_id = self.high_table.item(row, column).data(Qt.ItemDataRole.UserRole)

            # Émettre le signal avec l'ID du produit
            self.product_selected.emit(product_id)
        except RuntimeError as e:
            print(f"Erreur lors de la sélection du produit: {e}")
        except Exception as e:
            print(f"Erreur inattendue lors de la sélection du produit: {e}")
