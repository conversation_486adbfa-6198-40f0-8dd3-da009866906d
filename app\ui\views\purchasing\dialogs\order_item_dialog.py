from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QFormLayout,
    QSpinBox, QDoubleSpinBox, QDialogButtonBox,
    QMessageBox, QDateEdit, QPushButton, QHBoxLayout
)
from PyQt6.QtCore import QDate, pyqtSignal
from PyQt6.QtGui import QIcon
import asyncio
import datetime
import random
import string

from app.core.services.inventory_service import InventoryService
from app.ui.components.product_search_widget import ProductSearchWidget
from app.ui.views.inventory.dialogs.item_dialog import ItemDialog
from app.ui.components.barcode_generator_dialog import BarcodeGeneratorDialog
from app.ui.views.purchasing.dialogs.supplier_comparison_dialog import SupplierComparisonDialog
from app.utils.database import SessionLocal


class OrderItemDialog(QDialog):
    """Dialogue pour ajouter/modifier un article de commande"""

    # Signal émis lorsqu'un produit est créé depuis ce dialogue
    product_created = pyqtSignal(object)

    def __init__(self, parent=None, products=None, item=None):
        super().__init__(parent)
        self.products = products or []
        self.item = item
        self.is_edit_mode = item is not None

        # Services
        self.db = SessionLocal()
        self.inventory_service = InventoryService(self.db)

        # Configuration de la fenêtre
        self.setWindowTitle("Ajouter un article" if not self.is_edit_mode else "Modifier l'article")
        self.setMinimumWidth(400)

        # Initialisation de l'interface
        self.setup_ui()
        self.setup_connections()

        # Remplir le formulaire en mode édition
        if self.is_edit_mode:
            self._populate_form()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)

        # Widget de recherche de produits
        self.product_search = ProductSearchWidget(self.products, self)
        layout.addWidget(self.product_search)

        form = QFormLayout()

        # Quantité
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 9999)
        self.quantity_spin.setValue(1)
        form.addRow("Quantité:", self.quantity_spin)

        # Prix d'achat
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(0.01, 9999999.99)
        self.unit_price_spin.setValue(0.0)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSuffix(" DA")
        form.addRow("Prix d'achat:", self.unit_price_spin)

        # Date de livraison
        self.delivery_date_edit = QDateEdit()
        self.delivery_date_edit.setCalendarPopup(True)
        self.delivery_date_edit.setDate(QDate.currentDate().addDays(14))
        form.addRow("Date de livraison:", self.delivery_date_edit)

        layout.addLayout(form)

        # Boutons de dialogue
        buttons_layout = QHBoxLayout()

        # Bouton pour créer un nouveau produit
        self.new_product_button = QPushButton("Nouveau produit")
        self.new_product_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        buttons_layout.addWidget(self.new_product_button)

        # Bouton pour dupliquer un produit existant
        self.duplicate_button = QPushButton("Dupliquer produit")
        self.duplicate_button.setIcon(QIcon("app/ui/resources/icons/copy.svg"))
        self.duplicate_button.setToolTip("Dupliquer un produit existant")
        buttons_layout.addWidget(self.duplicate_button)

        # Bouton pour générer un code-barres
        self.barcode_button = QPushButton("Générer code-barres")
        self.barcode_button.setIcon(QIcon("app/ui/resources/icons/barcode.svg"))
        self.barcode_button.setToolTip("Générer un code-barres pour ce produit")
        buttons_layout.addWidget(self.barcode_button)

        # Bouton pour comparer les fournisseurs
        self.compare_button = QPushButton("Comparer fournisseurs")
        self.compare_button.setIcon(QIcon("app/ui/resources/icons/compare.svg"))
        self.compare_button.setToolTip("Comparer les prix et délais des fournisseurs")
        buttons_layout.addWidget(self.compare_button)

        buttons_layout.addStretch()

        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.validate_and_accept)
        self.button_box.rejected.connect(self.reject)
        buttons_layout.addWidget(self.button_box)

        layout.addLayout(buttons_layout)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        # Connecter le signal de sélection de produit
        self.product_search.product_selected.connect(self._update_price_from_product)

        # Connecter le signal de création de nouveau produit
        self.product_search.create_new_product.connect(self._create_new_product)
        self.new_product_button.clicked.connect(self._create_new_product)
        
        # Signal vers le parent pour l'informer qu'un produit a été créé
        try:
            from PyQt6.QtCore import pyqtSignal
        except Exception:
            pass

        # Connecter le bouton de duplication
        self.duplicate_button.clicked.connect(self._duplicate_product)

        # Connecter le bouton de génération de codes-barres
        self.barcode_button.clicked.connect(self._generate_barcode)

        # Connecter le bouton de comparaison des fournisseurs
        self.compare_button.clicked.connect(self._compare_suppliers)

    def _update_price_from_product(self, product):
        """Met à jour le prix d'achat en fonction du produit sélectionné"""
        if product:
            # Utiliser purchase_price en priorité, sinon purchase_unit_price comme fallback
            purchase_price = getattr(product, 'purchase_price', getattr(product, 'purchase_unit_price', 0))
            self.unit_price_spin.setValue(purchase_price)

    def _create_new_product(self):
        """Ouvre la boîte de dialogue pour créer un nouveau produit"""
        # Protection contre les créations multiples
        if hasattr(self, '_creating_product') and self._creating_product:
            return

        self._creating_product = True
        try:
            dialog = ItemDialog(self)
            if dialog.exec():
                # Récupérer les données du nouveau produit
                new_product_data = dialog.get_item_data()

                # Créer le produit dans la base de données
                try:
                    # Créer une nouvelle session pour éviter les conflits
                    new_db = SessionLocal()
                    new_inventory_service = InventoryService(new_db)

                    # Vérifier si le SKU existe déjà
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_closed():
                            raise RuntimeError("Event loop is closed")
                    except RuntimeError:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                    # Vérifier si un produit avec le même SKU existe déjà
                    existing_product = loop.run_until_complete(new_inventory_service.get_by_sku(new_product_data['sku']))
                    if existing_product:
                        # Si le produit existe déjà, l'utiliser au lieu de créer un doublon
                        print(f"Produit existant trouvé avec SKU {new_product_data['sku']}: {existing_product.name}")

                        # Fermer la session temporaire
                        new_db.close()

                        # Utiliser le produit existant
                        self.product_search.search_input.setText(existing_product.name)
                        self.product_search.update_product_info(existing_product)
                        self._update_price_from_product(existing_product)

                        # Émettre le signal de création de produit pour mettre à jour la liste
                        self.product_created.emit(existing_product)
                        return

                    # Vérifier si un produit avec le même nom existe déjà
                    existing_by_name = loop.run_until_complete(new_inventory_service.get_by_name(new_product_data['name']))
                    if existing_by_name:
                        # Si un produit avec le même nom existe, demander confirmation
                        reply = QMessageBox.question(
                            self,
                            "Produit similaire détecté",
                            f"Un produit avec le nom '{new_product_data['name']}' existe déjà.\n"
                            f"SKU existant: {existing_by_name.sku}\n"
                            f"Voulez-vous utiliser le produit existant au lieu d'en créer un nouveau ?",
                            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                            QMessageBox.StandardButton.Yes
                        )

                        if reply == QMessageBox.StandardButton.Yes:
                            # Utiliser le produit existant
                            new_db.close()
                            self.product_search.search_input.setText(existing_by_name.name)
                            self.product_search.update_product_info(existing_by_name)
                            self._update_price_from_product(existing_by_name)
                            self.product_created.emit(existing_by_name)
                            return

                    # Vérifier si le code-barres existe déjà et en générer un nouveau si nécessaire
                    if 'barcode' in new_product_data and new_product_data['barcode']:
                        barcode = new_product_data['barcode']
                        existing_barcode = loop.run_until_complete(new_inventory_service.get_by_barcode(barcode))
                        if existing_barcode:
                            # Générer un nouveau code-barres unique
                            max_attempts = 10
                            for attempt in range(max_attempts):
                                prefix = "200"
                                middle = ''.join(random.choices(string.digits, k=9))
                                check_digit = random.choice(string.digits)
                                new_barcode = f"{prefix}{middle}{check_digit}"

                                # Vérifier l'unicité du nouveau code-barres
                                existing_new_barcode = loop.run_until_complete(new_inventory_service.get_by_barcode(new_barcode))
                                if not existing_new_barcode:
                                    new_product_data['barcode'] = new_barcode
                                    break
                            else:
                                # Si on n'arrive pas à générer un code-barres unique, le laisser vide
                                new_product_data['barcode'] = ""

                    # Créer le produit
                    new_product = loop.run_until_complete(new_inventory_service.create(new_product_data))

                    # Fermer la session
                    new_db.close()

                    # Ajouter le produit à la liste
                    self.products.append(new_product)

                    # Mettre à jour le widget de recherche
                    self.product_search.update_products(self.products)

                    # Émettre un signal pour prévenir le parent afin de rafraîchir sa liste de produits
                    try:
                        self.product_created.emit(new_product)
                    except Exception:
                        pass

                    # Sélectionner le nouveau produit
                    self.product_search.search_input.setText(new_product.name)
                    self.product_search.update_product_info(new_product)

                    # Mettre à jour le prix
                    self._update_price_from_product(new_product)

                    QMessageBox.information(
                        self,
                        "Nouveau produit",
                        f"Le produit '{new_product.name}' a été créé avec succès."
                    )

                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "Erreur",
                        f"Erreur lors de la création du produit: {str(e)}"
                    )
                    # Rollback en cas d'erreur
                    if 'new_db' in locals():
                        new_db.rollback()
                        new_db.close()
        finally:
            self._creating_product = False

    def _generate_barcode(self):
        """Génère un code-barres pour le produit sélectionné"""
        # Vérifier si un produit est sélectionné
        selected_product = self.product_search.get_selected_product()
        if not selected_product:
            QMessageBox.warning(
                self,
                "Génération de code-barres",
                "Veuillez d'abord sélectionner un produit."
            )
            return

        # Ouvrir la boîte de dialogue de génération de codes-barres
        dialog = BarcodeGeneratorDialog(self, selected_product.id)
        dialog.exec()

    def _compare_suppliers(self):
        """Compare les fournisseurs pour le produit sélectionné"""
        # Vérifier si un produit est sélectionné
        selected_product = self.product_search.get_selected_product()
        if not selected_product:
            QMessageBox.warning(
                self,
                "Comparaison des fournisseurs",
                "Veuillez d'abord sélectionner un produit."
            )
            return

        # Ouvrir la boîte de dialogue de comparaison des fournisseurs
        dialog = SupplierComparisonDialog(self, selected_product.id)
        dialog.exec()

    def _duplicate_product(self):
        """Duplique un produit existant"""
        # Vérifier si un produit est sélectionné
        selected_product = self.product_search.get_selected_product()
        if not selected_product:
            QMessageBox.warning(
                self,
                "Duplication de produit",
                "Veuillez d'abord sélectionner un produit à dupliquer."
            )
            return

        # Ouvrir la boîte de dialogue pour modifier les données du produit dupliqué
        dialog = ItemDialog(self)

        # Pré-remplir les données du produit
        dialog.name_edit.setText(f"Copie de {selected_product.name}")

        if hasattr(selected_product, 'sku') and selected_product.sku:
            dialog.sku_edit.setText(f"{selected_product.sku}-COPY")

        if hasattr(selected_product, 'description') and selected_product.description:
            dialog.description_edit.setText(selected_product.description)

        if hasattr(selected_product, 'category') and selected_product.category:
            if hasattr(selected_product.category, 'value'):
                index = dialog.category_combo.findText(selected_product.category.value)
                if index >= 0:
                    dialog.category_combo.setCurrentIndex(index)
            else:
                index = dialog.category_combo.findText(str(selected_product.category))
                if index >= 0:
                    dialog.category_combo.setCurrentIndex(index)

        # Utiliser purchase_price en priorité, sinon unit_price comme fallback
        purchase_price = getattr(selected_product, 'purchase_price', getattr(selected_product, 'unit_price', 0))
        if purchase_price > 0:
            dialog.price_spin.setValue(purchase_price)

        if hasattr(selected_product, 'quantity') and selected_product.quantity:
            dialog.quantity_spin.setValue(selected_product.quantity)

        if hasattr(selected_product, 'supplier_id') and selected_product.supplier_id:
            index = dialog.supplier_combo.findData(selected_product.supplier_id)
            if index >= 0:
                dialog.supplier_combo.setCurrentIndex(index)

        # Exécuter la boîte de dialogue
        if dialog.exec():
            # Récupérer les données du nouveau produit
            new_product_data = dialog.get_item_data()

            # Créer le produit dans la base de données
            try:
                # Créer une nouvelle session pour éviter les conflits
                new_db = SessionLocal()
                new_inventory_service = InventoryService(new_db)

                # Créer le produit
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_closed():
                        raise RuntimeError("Event loop is closed")
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                new_product = loop.run_until_complete(new_inventory_service.create(new_product_data))

                # Fermer la session
                new_db.close()

                # Ajouter le produit à la liste
                self.products.append(new_product)

                # Mettre à jour le widget de recherche
                self.product_search.update_products(self.products)

                # Sélectionner le nouveau produit
                self.product_search.search_input.setText(new_product.name)
                self.product_search.update_product_info(new_product)

                # Mettre à jour le prix
                self._update_price_from_product(new_product)

                QMessageBox.information(
                    self,
                    "Produit dupliqué",
                    f"Le produit '{new_product.name}' a été créé avec succès."
                )

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Erreur",
                    f"Erreur lors de la duplication du produit: {str(e)}"
                )

    def _populate_form(self):
        """Remplit le formulaire avec les données de l'article"""
        if not self.item:
            return

        # Produit
        for product in self.products:
            if product.id == self.item.product_id:
                # Mettre à jour le widget de recherche
                self.product_search.search_input.setText(product.name)
                self.product_search.update_product_info(product)
                break

        # Quantité et prix
        self.quantity_spin.setValue(self.item.quantity)
        # Utiliser purchase_unit_price en priorité, unit_price comme fallback pour compatibilité
        purchase_price = getattr(self.item, 'purchase_unit_price', getattr(self.item, 'unit_price', 0))
        self.unit_price_spin.setValue(purchase_price)

        # Date de livraison
        if self.item.delivery_date:
            self.delivery_date_edit.setDate(QDate(
                self.item.delivery_date.year,
                self.item.delivery_date.month,
                self.item.delivery_date.day
            ))

    def validate_and_accept(self):
        """Valide les données et accepte le dialogue"""
        # Protection contre les validations multiples
        if hasattr(self, '_validating') and self._validating:
            return

        self._validating = True
        try:
            # Récupérer le produit sélectionné
            selected_product = self.product_search.get_selected_product()

            # Vérifier que les champs obligatoires sont remplis
            if not selected_product:
                QMessageBox.warning(self, "Validation", "Veuillez sélectionner un produit.")
                return

            if self.quantity_spin.value() <= 0:
                QMessageBox.warning(self, "Validation", "La quantité doit être supérieure à zéro.")
                return

            if self.unit_price_spin.value() <= 0:
                QMessageBox.warning(self, "Validation", "Le prix d'achat doit être supérieur à zéro.")
                return

            self.accept()
        finally:
            self._validating = False

    def get_item_data(self):
        """Retourne les données de l'article"""
        selected_product = self.product_search.get_selected_product()

        if not selected_product:
            return None

        return {
            "product_id": selected_product.id,
            "product": selected_product,
            "quantity": self.quantity_spin.value(),
            "purchase_unit_price": self.unit_price_spin.value(),
            "delivery_date": self.delivery_date_edit.date().toPyDate(),
            "specifications": {},  # Valeur par défaut
            "received_quantity": 0  # Valeur par défaut
        }
