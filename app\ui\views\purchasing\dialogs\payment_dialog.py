from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QTextEdit, QDateEdit, QComboBox,
    QPushButton, QDialogButtonBox, QMessageBox, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime

from app.core.services.purchasing_service import PurchasingService
from app.core.models.supplier_finance import PaymentMethod
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay


class PaymentDialog(QDialog):
    """Dialogue pour enregistrer un paiement pour une commande d'achat"""

    def __init__(self, parent=None, order_id=None):
        super().__init__(parent)
        self.order_id = order_id

        if not order_id:
            raise ValueError("L'ID de commande est requis")

        # Services
        self.db = SessionLocal()
        self.purchasing_service = PurchasingService(self.db)

        # Données
        self.order = None
        self.payment_data = {}

        # Configuration de la fenêtre
        self.setWindowTitle("Enregistrer un acompte")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # Initialisation de l'interface
        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.init_data()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Formulaire
        form_layout = QFormLayout()

        # Informations de la commande
        self.order_info_label = QLabel()
        form_layout.addRow("Commande:", self.order_info_label)

        self.supplier_label = QLabel()
        form_layout.addRow("Fournisseur:", self.supplier_label)

        self.total_amount_label = QLabel()
        form_layout.addRow("Montant total:", self.total_amount_label)

        self.remaining_amount_label = QLabel()
        form_layout.addRow("Montant restant:", self.remaining_amount_label)

        # Séparateur
        separator = QLabel()
        separator.setFrameShape(QLabel.Shape.HLine)
        separator.setFrameShadow(QLabel.Shadow.Sunken)
        form_layout.addRow(separator)

        # Montant du paiement
        self.amount_spinbox = QDoubleSpinBox()
        self.amount_spinbox.setRange(0, 1000000)
        self.amount_spinbox.setDecimals(2)
        self.amount_spinbox.setSuffix(" DA")
        self.amount_spinbox.setAlignment(Qt.AlignmentFlag.AlignRight)
        form_layout.addRow("Montant:", self.amount_spinbox)

        # Méthode de paiement
        self.payment_method_combo = QComboBox()
        for method in PaymentMethod:
            self.payment_method_combo.addItem(self._get_payment_method_display(method.value), method.value)
        form_layout.addRow("Méthode de paiement:", self.payment_method_combo)

        # Date de paiement
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setCalendarPopup(True)
        self.payment_date_edit.setDate(QDate.currentDate())
        form_layout.addRow("Date de paiement:", self.payment_date_edit)

        # Référence
        self.reference_edit = QLineEdit()
        form_layout.addRow("Référence:", self.reference_edit)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)

        main_layout.addLayout(form_layout)

        # Boutons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.amount_spinbox.valueChanged.connect(self._validate_amount)

    def init_data(self):
        """Initialise les données"""
        self.loading_overlay.show()
        QTimer.singleShot(0, self._init_data_wrapper)

    def _init_data_wrapper(self):
        """Wrapper pour exécuter init_data_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._init_data_async())
        finally:
            loop.close()
            self.loading_overlay.hide()

    async def _init_data_async(self):
        """Initialise les données de manière asynchrone"""
        try:
            # Récupérer la commande
            self.order = await self.purchasing_service.get(self.order_id)
            if not self.order:
                raise ValueError(f"Commande avec ID {self.order_id} non trouvée")

            # Récupérer le statut de paiement
            payment_status = await self.purchasing_service.update_payment_status(self.order_id)

            # Mettre à jour l'interface
            self.order_info_label.setText(self.order.po_number or f"CMD-{self.order.id}")
            self.supplier_label.setText(self.order.supplier.name if self.order.supplier else "N/A")
            self.total_amount_label.setText(f"{self.order.total_amount:.2f} DA")
            
            remaining_amount = payment_status.get("remaining_amount", self.order.total_amount)
            self.remaining_amount_label.setText(f"{remaining_amount:.2f} DA")

            # Définir le montant maximum et la valeur par défaut
            self.amount_spinbox.setMaximum(remaining_amount)
            self.amount_spinbox.setValue(remaining_amount)

            # Générer une référence par défaut
            default_reference = f"ADV-{self.order.po_number or self.order.id}-{datetime.now().strftime('%Y%m%d')}"
            self.reference_edit.setText(default_reference)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
            self.reject()

    def _validate_amount(self, value):
        """Valide le montant du paiement"""
        try:
            remaining_amount = float(self.remaining_amount_label.text().replace(" DA", ""))
            if value > remaining_amount:
                self.amount_spinbox.setValue(remaining_amount)

        except (ValueError, AttributeError):
            pass

    def _get_payment_method_display(self, method):
        """Retourne l'affichage de la méthode de paiement"""
        method_display = {
            "cash": "Espèces",
            "bank_transfer": "Virement bancaire",
            "check": "Chèque",
            "credit_card": "Carte de crédit",
            "debit_card": "Carte de débit",
            "mobile_payment": "Paiement mobile",
            "other": "Autre"
        }
        return method_display.get(method.lower(), str(method))

    def accept(self):
        """Gère l'acceptation du dialogue"""
        # Vérifier les données
        amount = self.amount_spinbox.value()
        if amount <= 0:
            QMessageBox.warning(self, "Avertissement", "Le montant doit être supérieur à zéro.")
            return

        payment_method = self.payment_method_combo.currentData()
        payment_date = self.payment_date_edit.date().toPyDate()
        reference = self.reference_edit.text().strip()
        notes = self.notes_edit.toPlainText().strip()

        # Préparer les données de paiement
        self.payment_data = {
            "amount": amount,
            "payment_method": payment_method,
            "payment_date": payment_date,
            "reference": reference,
            "notes": notes
        }

        # Enregistrer le paiement
        self.loading_overlay.show()
        QTimer.singleShot(0, self._save_payment_wrapper)

    def _save_payment_wrapper(self):
        """Wrapper pour exécuter save_payment_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._save_payment_async())
            super().accept()  # Fermer le dialogue seulement si tout s'est bien passé
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement du paiement: {str(e)}")
        finally:
            loop.close()
            self.loading_overlay.hide()

    async def _save_payment_async(self):
        """Enregistre le paiement de manière asynchrone"""
        # Enregistrer le paiement
        result = await self.purchasing_service.record_advance_payment(
            self.order_id,
            self.payment_data
        )

        # Afficher un message de confirmation
        QMessageBox.information(
            self,
            "Paiement enregistré",
            f"Un acompte de {self.payment_data['amount']:.2f} DA a été enregistré pour la commande {self.order.po_number or f'CMD-{self.order.id}'}."
        )
