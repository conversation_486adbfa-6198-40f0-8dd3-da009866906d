from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QTabWidget,
    QLabel, QLineEdit, QTextEdit, QComboBox, QDateEdit,
    QPushButton, QDialogButtonBox, QMessageBox, QTableView,
    QSpinBox, QDoubleSpinBox, QCheckBox, QWidget, QHeaderView
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime, date

from app.core.services.purchasing_service import PurchasingService
from app.core.services.supplier_service import SupplierService
from app.core.services.inventory_service import InventoryService
from app.core.models.purchasing import OrderStatus, PurchaseOrderItem
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from .order_item_dialog import OrderItemDialog
from ..order_items_table_model import OrderItemsTableModel


class PurchaseOrderDialog(QDialog):
    """Dialogue pour ajouter/modifier une commande d'achat"""

    def __init__(self, parent=None, order_id=None):
        super().__init__(parent)
        self.order_id = order_id
        self.is_edit_mode = order_id is not None

        # Services
        self.db = SessionLocal()
        self.purchasing_service = PurchasingService(self.db)
        self.supplier_service = SupplierService(self.db)
        self.inventory_service = InventoryService(self.db)

        # Données
        self.suppliers = []
        self.products = []
        self.order = None
        self.order_items = []
        self.items_model = None

        # Configuration de la fenêtre
        self.setWindowTitle("Nouvelle commande" if not self.is_edit_mode else "Modifier la commande")
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)

        # Initialisation de l'interface
        self.setup_ui()
        self.setup_connections()

        # Handler par défaut pour nouveaux produits créés depuis le sous-dialogue
        if not hasattr(self, "_on_product_created"):
            def _on_product_created(product):
                try:
                    self.products.append(product)
                except Exception:
                    pass
            self._on_product_created = _on_product_created

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.init_data()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Onglets
        self.tab_widget = QTabWidget()

        # Onglet Informations générales
        general_tab = QWidget()
        general_layout = QFormLayout(general_tab)

        # Fournisseur
        self.supplier_combo = QComboBox()
        general_layout.addRow("Fournisseur:", self.supplier_combo)

        # Numéro de commande
        self.po_number_edit = QLineEdit()
        self.po_number_edit.setPlaceholderText("Généré automatiquement si vide")
        general_layout.addRow("N° Commande:", self.po_number_edit)

        # Date de commande
        self.order_date_edit = QDateEdit()
        self.order_date_edit.setCalendarPopup(True)
        self.order_date_edit.setDate(QDate.currentDate())
        general_layout.addRow("Date de commande:", self.order_date_edit)

        # Date de livraison prévue
        self.expected_delivery_edit = QDateEdit()
        self.expected_delivery_edit.setCalendarPopup(True)
        self.expected_delivery_edit.setDate(QDate.currentDate().addDays(14))
        general_layout.addRow("Livraison prévue:", self.expected_delivery_edit)

        # Devise (fixée à DA)
        self.currency_label = QLabel("DA")
        self.currency_label.setObjectName("currencyLabel")
        general_layout.addRow("Devise:", self.currency_label)

        # Conditions de paiement
        self.payment_terms_edit = QLineEdit()
        self.payment_terms_edit.setPlaceholderText("Ex: 30 jours")
        general_layout.addRow("Conditions de paiement:", self.payment_terms_edit)

        # Conditions de livraison
        self.shipping_terms_edit = QLineEdit()
        self.shipping_terms_edit.setPlaceholderText("Ex: Franco de port")
        general_layout.addRow("Conditions de livraison:", self.shipping_terms_edit)

        # Frais de livraison
        self.shipping_amount_spin = QDoubleSpinBox()
        self.shipping_amount_spin.setRange(0, 1000000)
        self.shipping_amount_spin.setDecimals(2)
        self.shipping_amount_spin.setSuffix(" DA")
        self.shipping_amount_spin.setValue(0)
        general_layout.addRow("Frais de livraison:", self.shipping_amount_spin)

        # Remise globale
        self.discount_percent_spin = QDoubleSpinBox()
        self.discount_percent_spin.setRange(0, 100)
        self.discount_percent_spin.setDecimals(2)
        self.discount_percent_spin.setSuffix(" %")
        self.discount_percent_spin.setValue(0)
        general_layout.addRow("Remise globale:", self.discount_percent_spin)

        # TVA
        self.tax_percent_spin = QDoubleSpinBox()
        self.tax_percent_spin.setRange(0, 100)
        self.tax_percent_spin.setDecimals(2)
        self.tax_percent_spin.setSuffix(" %")
        self.tax_percent_spin.setValue(19)  # 19% par défaut
        general_layout.addRow("TVA:", self.tax_percent_spin)

        # Recalculer le total quand ces champs changent
        self.shipping_amount_spin.valueChanged.connect(self._update_items_table)
        self.discount_percent_spin.valueChanged.connect(self._update_items_table)
        self.tax_percent_spin.valueChanged.connect(self._update_items_table)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes ou instructions spéciales")
        general_layout.addRow("Notes:", self.notes_edit)

        # Statut (uniquement en mode édition)
        self.status_combo = QComboBox()
        for status in OrderStatus:
            self.status_combo.addItem(self._get_status_display(status), status.value)
        general_layout.addRow("Statut:", self.status_combo)

        self.tab_widget.addTab(general_tab, "Informations générales")

        # Onglet Articles
        items_tab = QWidget()
        items_layout = QVBoxLayout(items_tab)

        # Tableau des articles
        self.items_table = QTableView()
        self.items_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.items_table.setAlternatingRowColors(True)

        # Configuration du tableau pour une meilleure présentation
        header = self.items_table.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Désactiver l'édition directe dans le tableau
        self.items_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.items_table.setShowGrid(True)
        self.items_table.setSortingEnabled(True)

        items_layout.addWidget(self.items_table)

        # Boutons d'action pour les articles
        items_buttons_layout = QHBoxLayout()

        self.add_item_button = QPushButton("Ajouter un article")
        self.add_item_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        items_buttons_layout.addWidget(self.add_item_button)

        self.edit_item_button = QPushButton("Modifier")
        items_buttons_layout.addWidget(self.edit_item_button)

        self.remove_item_button = QPushButton("Supprimer")
        items_buttons_layout.addWidget(self.remove_item_button)

        items_buttons_layout.addStretch()

        self.total_amount_label = QLabel("Total: 0.00 DA")
        self.total_amount_label.setObjectName("totalAmountLabel")
        items_buttons_layout.addWidget(self.total_amount_label)

        items_layout.addLayout(items_buttons_layout)

        self.tab_widget.addTab(items_tab, "Articles")

        main_layout.addWidget(self.tab_widget)

        # Boutons de dialogue
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save |
            QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)

        main_layout.addWidget(self.button_box)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.add_item_button.clicked.connect(self.add_item)
        self.edit_item_button.clicked.connect(self.edit_item)
        self.remove_item_button.clicked.connect(self.remove_item)

        # Connexion pour la validation avant d'accepter
        self.button_box.accepted.disconnect()
        self.button_box.accepted.connect(self.validate_and_accept)

    def init_data(self):
        """Initialise les données du dialogue"""
        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        try:
            # Utiliser l'event loop existant ou en créer un nouveau de manière sûre
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Exécuter la coroutine
            loop.run_until_complete(self.load_data())
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement: {str(e)}")

    async def load_data(self):
        """Charge les données nécessaires"""
        self.loading_overlay.show()
        try:
            # Charger les fournisseurs
            self.suppliers = await self.supplier_service.get_all()
            self.supplier_combo.clear()
            for supplier in self.suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)

            # Charger les produits
            self.products = await self.inventory_service.get_all()

            # En mode édition, charger la commande
            if self.is_edit_mode:
                self.order = await self.purchasing_service.get(self.order_id)
                if self.order:
                    self._populate_form()
        finally:
            self.loading_overlay.hide()

    def _populate_form(self):
        """Remplit le formulaire avec les données de la commande"""
        if not self.order:
            return

        # Informations générales
        supplier_index = self.supplier_combo.findData(self.order.supplier_id)
        if supplier_index >= 0:
            self.supplier_combo.setCurrentIndex(supplier_index)

        self.po_number_edit.setText(self.order.po_number or "")

        if self.order.order_date:
            self.order_date_edit.setDate(QDate(
                self.order.order_date.year,
                self.order.order_date.month,
                self.order.order_date.day
            ))

        if self.order.expected_delivery:
            self.expected_delivery_edit.setDate(QDate(
                self.order.expected_delivery.year,
                self.order.expected_delivery.month,
                self.order.expected_delivery.day
            ))

        # La devise est fixée à DA, donc pas besoin de la mettre à jour

        self.payment_terms_edit.setText(self.order.payment_terms or "")
        self.shipping_terms_edit.setText(self.order.shipping_terms or "")
        self.notes_edit.setText(self.order.notes or "")

        # Remplir les nouveaux champs financiers
        self.shipping_amount_spin.setValue(self.order.shipping_amount or 0)
        self.discount_percent_spin.setValue(self.order.discount_percent or 0)
        self.tax_percent_spin.setValue(self.order.tax_percent or 19)

        status_index = self.status_combo.findData(self.order.status.value)
        if status_index >= 0:
            self.status_combo.setCurrentIndex(status_index)

        # Articles
        self.order_items = self.order.items
        self._update_items_table()

    def _update_items_table(self):
        """Met à jour le tableau des articles"""
        # Créer le modèle de tableau s'il n'existe pas ou s'il est None
        if self.items_model is None:
            self.items_model = OrderItemsTableModel()
            self.items_table.setModel(self.items_model)

        # Mettre à jour les données du modèle
        self.items_model.set_items(self.order_items)

        # Mettre à jour le montant total (HT -> remise globale -> TVA -> + livraison)
        # Utiliser purchase_unit_price en priorité, unit_price comme fallback pour compatibilité
        subtotal = sum(float(getattr(item, 'purchase_unit_price', getattr(item, 'unit_price', 0)) or 0) * float(getattr(item, 'quantity', 0) or 0) for item in self.order_items)
        discount_percent = float(self.discount_percent_spin.value() or 0)
        discount_amount = subtotal * (discount_percent / 100.0)
        taxable_base = max(0.0, subtotal - discount_amount)
        tax_percent = float(self.tax_percent_spin.value() or 0)
        tax_amount = taxable_base * (tax_percent / 100.0)
        shipping_amount = float(self.shipping_amount_spin.value() or 0)
        total_amount = taxable_base + tax_amount + shipping_amount
        self.total_amount_label.setText(f"Total: {total_amount:.2f} DA")

    def add_item(self):
        """Ajoute un nouvel article à la commande"""
        # Protection contre les doubles clics
        if hasattr(self, '_adding_item') and self._adding_item:
            return

        self._adding_item = True
        try:
            dialog = OrderItemDialog(self, self.products)
            # Mettre à jour la liste des produits du parent quand un nouveau produit est créé
            try:
                dialog.product_created.connect(self._on_product_created)
            except Exception:
                # Au cas où la méthode n'existe pas encore
                pass

            if dialog.exec():
                # Récupérer les données de l'article
                item_data = dialog.get_item_data()

                if item_data is None:
                    return

                # Créer un nouvel objet PurchaseOrderItem (objet temporaire côté UI)
                item = PurchaseOrderItem(
                    product_id=item_data["product_id"],
                    quantity=item_data["quantity"],
                    purchase_unit_price=item_data["purchase_unit_price"],
                    delivery_date=item_data["delivery_date"],
                    specifications=item_data["specifications"],
                    received_quantity=item_data["received_quantity"]
                )

                # Ajouter l'article à la liste
                if self.order_items is None:
                    self.order_items = []

                # Vérifier l'unicité (éviter les doublons)
                for existing_item in self.order_items:
                    if (existing_item.product_id == item.product_id and
                        abs(existing_item.purchase_unit_price - item.purchase_unit_price) < 0.01):
                        QMessageBox.warning(self, "Doublon",
                                          "Cet article avec ce prix existe déjà dans la commande.")
                        return

                # Ajouter la référence au produit pour l'affichage
                item.product = item_data["product"]

                self.order_items.append(item)

                # Mettre à jour le tableau
                self._update_items_table()
        finally:
            self._adding_item = False

    def edit_item(self):
        """Modifie l'article sélectionné"""
        # Vérifier qu'un article est sélectionné
        selected_rows = self.items_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner un article à modifier.")
            return

        # Récupérer l'article sélectionné
        row = selected_rows[0].row()
        item = self.order_items[row]

        # Ouvrir le dialogue d'édition
        dialog = OrderItemDialog(self, self.products, item)
        if dialog.exec():
            # Récupérer les données modifiées
            item_data = dialog.get_item_data()

            # Mettre à jour l'article
            item.product_id = item_data["product_id"]
            item.product = item_data["product"]
            item.quantity = item_data["quantity"]
            item.purchase_unit_price = item_data["purchase_unit_price"]
            item.delivery_date = item_data["delivery_date"]

            # Mettre à jour le tableau
            self._update_items_table()

    def remove_item(self):
        """Supprime l'article sélectionné"""
        # Vérifier qu'un article est sélectionné
        selected_rows = self.items_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner un article à supprimer.")
            return

        # Demander confirmation
        confirm = QMessageBox.question(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir supprimer cet article ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if confirm == QMessageBox.StandardButton.Yes:
            # Supprimer l'article
            row = selected_rows[0].row()
            del self.order_items[row]

            # Mettre à jour le tableau
            self._update_items_table()

    def validate_and_accept(self):
        """Valide les données et accepte le dialogue"""
        # Vérifier que les champs obligatoires sont remplis
        if self.supplier_combo.currentIndex() < 0:
            QMessageBox.warning(self, "Validation", "Veuillez sélectionner un fournisseur.")
            return

        # Vérifier qu'il y a au moins un article
        if not self.order_items:
            QMessageBox.warning(self, "Validation", "Veuillez ajouter au moins un article à la commande.")
            return

        # Sauvegarder la commande
        self._save_order_wrapper()

    def _save_order_wrapper(self):
        """Wrapper pour exécuter la sauvegarde de manière asynchrone, UI-safe"""
        loop = None
        created_new_loop = False
        # Montrer l'overlay dans le thread UI
        try:
            self.loading_overlay.show()
        except Exception:
            pass
        try:
            # Utiliser l'event loop existant ou en créer un nouveau de manière sûre
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                created_new_loop = True

            # Exécuter la coroutine (sans appels UI à l'intérieur)
            loop.run_until_complete(self._save_order_core())
            # Fermer la boîte de dialogue côté UI après la réussite
            super().accept()
        except Exception as e:
            # Afficher les erreurs côté UI uniquement
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            # Cacher l'overlay côté UI
            try:
                self.loading_overlay.hide()
            except Exception:
                pass
            # Ne fermer l'event loop que si nous l'avons créé ici
            if created_new_loop and loop is not None and not loop.is_closed():
                loop.close()

    async def _save_order_core(self):
        """Sauvegarde la commande (logique pure sans UI)"""
        # Récupérer les données du formulaire
        supplier_id = self.supplier_combo.currentData()

        # Vérifier si supplier_id est valide
        if supplier_id is None or supplier_id == 0:
            raise ValueError("Veuillez sélectionner un fournisseur valide.")

        po_number = self.po_number_edit.text().strip() or None
        order_date = self.order_date_edit.date().toPyDate()
        expected_delivery = self.expected_delivery_edit.date().toPyDate()
        currency = "DA"  # Devise fixée à DA
        payment_terms = self.payment_terms_edit.text().strip() or None
        shipping_terms = self.shipping_terms_edit.text().strip() or None
        notes = self.notes_edit.toPlainText().strip() or None
        status = OrderStatus(self.status_combo.currentData())

        # Récupérer les données financières
        shipping_amount = self.shipping_amount_spin.value()
        discount_percent = self.discount_percent_spin.value()
        tax_percent = self.tax_percent_spin.value()

        # Créer ou mettre à jour la commande
        if self.is_edit_mode:
            # Mettre à jour la commande existante
            await self.purchasing_service.update(
                self.order_id,
                {
                    "supplier_id": supplier_id,
                    "po_number": po_number,
                    "order_date": order_date,
                    "expected_delivery": expected_delivery,
                    "currency": currency,
                    "payment_terms": payment_terms,
                    "shipping_terms": shipping_terms,
                    "notes": notes,
                    "status": status,

                    # Données financières
                    "shipping_amount": shipping_amount,
                    "discount_percent": discount_percent,
                    "tax_percent": tax_percent
                }
            )
        else:
            # Créer une nouvelle commande via une méthode qui reconstruit les items pour éviter les doublons
            order_data = {
                "supplier_id": supplier_id,
                "po_number": po_number,
                "order_date": order_date,
                "expected_delivery": expected_delivery,
                "currency": currency,
                "payment_terms": payment_terms,
                "shipping_terms": shipping_terms,
                "notes": notes,
                "status": status,
                # Données financières
                "shipping_amount": shipping_amount,
                "discount_percent": discount_percent,
                "tax_percent": tax_percent,
            }
            # Transformer les objets UI en payload dict pour éviter de passer des instances SA pouvant créer des doublons
            items_payload = []
            for it in self.order_items:
                items_payload.append({
                    "product_id": int(it.product_id),
                    "quantity": float(getattr(it, "quantity", 0) or 0),
                    "purchase_unit_price": float(getattr(it, "purchase_unit_price", 0) or 0),
                    "delivery_date": getattr(it, "delivery_date", None),
                    "specifications": getattr(it, "specifications", None),
                    "received_quantity": float(getattr(it, "received_quantity", 0) or 0),
                })

            # Utiliser la nouvelle API pour créer proprement la commande
            await self.purchasing_service.create_with_items(order_data, items_payload)

        # La fermeture du dialogue est gérée par _save_order_wrapper

    def _get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            OrderStatus.DRAFT: "Brouillon",
            OrderStatus.PENDING: "En attente",
            OrderStatus.SUBMITTED: "Soumis",
            OrderStatus.APPROVED: "Approuvé",
            OrderStatus.ORDERED: "Commandé",
            OrderStatus.PARTIALLY_RECEIVED: "Partiellement reçu",
            OrderStatus.COMPLETED: "Terminé",
            OrderStatus.CANCELLED: "Annulé",
        }
        return status_display.get(status, str(status))
