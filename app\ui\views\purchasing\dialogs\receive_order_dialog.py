from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QTextEdit, QDateEdit, QTableView,
    QPushButton, QDialogButtonBox, QMessageBox, QDoubleSpinBox,
    QCheckBox
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime

from app.core.services.purchasing_service import PurchasingService
from app.core.services.inventory_service import InventoryService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from app.utils.event_manager import event_manager, EventType


class ReceiveOrderDialog(QDialog):
    """Dialogue pour réceptionner une commande d'achat"""

    def __init__(self, parent=None, order_id=None):
        super().__init__(parent)
        self.order_id = order_id

        if not order_id:
            raise ValueError("L'ID de commande est requis")

        # Services
        self.db = SessionLocal()
        self.purchasing_service = PurchasingService(self.db)
        self.inventory_service = InventoryService(self.db)

        # S'abonner aux événements
        self._subscribe_to_events()

        # Données
        self.order = None
        self.received_items = []

        # Configuration de la fenêtre
        self.setWindowTitle("Réceptionner la commande")
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)

        # Initialisation de l'interface
        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.init_data()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # En-tête
        header_layout = QHBoxLayout()

        self.order_number_label = QLabel("Commande: ")
        header_layout.addWidget(self.order_number_label)

        self.supplier_label = QLabel("Fournisseur: ")
        header_layout.addWidget(self.supplier_label)

        main_layout.addLayout(header_layout)

        # Informations de réception
        form_layout = QFormLayout()

        # Date de réception
        self.receipt_date_edit = QDateEdit()
        self.receipt_date_edit.setCalendarPopup(True)
        self.receipt_date_edit.setDate(QDate.currentDate())
        form_layout.addRow("Date de réception:", self.receipt_date_edit)

        # Numéro de bon de livraison
        self.delivery_note_edit = QLineEdit()
        form_layout.addRow("Bon de livraison:", self.delivery_note_edit)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes ou commentaires sur la réception")
        form_layout.addRow("Notes:", self.notes_edit)

        main_layout.addLayout(form_layout)

        # Tableau des articles
        self.items_table = QTableView()
        self.items_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        main_layout.addWidget(self.items_table)

        # Options de mise à jour des prix
        price_options_layout = QHBoxLayout()

        self.update_price_check = QCheckBox("Mettre à jour les prix d'achat")
        self.update_price_check.setChecked(True)
        price_options_layout.addWidget(self.update_price_check)

        self.update_selling_price_check = QCheckBox("Mettre à jour les prix de vente")
        self.update_selling_price_check.setChecked(False)
        price_options_layout.addWidget(self.update_selling_price_check)

        main_layout.addLayout(price_options_layout)

        # Option de création de facture
        invoice_options_layout = QHBoxLayout()

        self.create_invoice_check = QCheckBox("Créer une facture fournisseur")
        self.create_invoice_check.setChecked(False)
        invoice_options_layout.addWidget(self.create_invoice_check)

        # Champ pour le numéro de facture
        self.invoice_number_label = QLabel("N° Facture:")
        invoice_options_layout.addWidget(self.invoice_number_label)

        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setPlaceholderText("Numéro de facture fournisseur")
        self.invoice_number_edit.setEnabled(False)
        invoice_options_layout.addWidget(self.invoice_number_edit)

        # Connexion pour activer/désactiver le champ de numéro de facture
        self.create_invoice_check.toggled.connect(self.invoice_number_edit.setEnabled)

        main_layout.addLayout(invoice_options_layout)

        # Boutons de dialogue
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save |
            QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.button(QDialogButtonBox.StandardButton.Save).setText("Réceptionner")
        self.button_box.accepted.connect(self.validate_and_accept)
        self.button_box.rejected.connect(self.reject)

        main_layout.addWidget(self.button_box)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        pass

    def _subscribe_to_events(self):
        """S'abonne aux événements pertinents"""
        # S'abonner à l'événement de mise à jour du prix d'un article
        event_manager.subscribe(EventType.INVENTORY_PRICE_UPDATED, self._handle_price_updated)

    def _handle_price_updated(self, **kwargs):
        """Gère l'événement de mise à jour du prix d'un article"""
        item_id = kwargs.get('item_id')
        old_purchase_price = kwargs.get('old_purchase_price')
        new_purchase_price = kwargs.get('new_purchase_price')
        old_unit_price = kwargs.get('old_unit_price')
        new_unit_price = kwargs.get('new_unit_price')

        # Afficher un message de confirmation
        print(f"Prix mis à jour pour l'article {item_id}:")
        print(f"  - Prix d'achat: {old_purchase_price} -> {new_purchase_price}")
        print(f"  - Prix de vente: {old_unit_price} -> {new_unit_price}")

        # Mettre à jour l'interface si nécessaire
        # (par exemple, rafraîchir le tableau des articles)

    def _unsubscribe_from_events(self):
        """Se désabonne des événements"""
        event_manager.unsubscribe(EventType.INVENTORY_PRICE_UPDATED, self._handle_price_updated)

    def closeEvent(self, event):
        """Gère l'événement de fermeture du dialogue"""
        # Se désabonner des événements
        self._unsubscribe_from_events()

        # Fermer la session de base de données
        if hasattr(self, 'db') and self.db:
            self.db.close()

        # Accepter l'événement de fermeture
        event.accept()

    def init_data(self):
        """Initialise les données du dialogue"""
        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_data())
        finally:
            loop.close()

    async def load_data(self):
        """Charge les données nécessaires"""
        self.loading_overlay.show()
        try:
            # Charger la commande
            self.order = await self.purchasing_service.get(self.order_id)
            if not self.order:
                QMessageBox.critical(self, "Erreur", "Commande introuvable")
                self.reject()
                return

            # Mettre à jour l'interface
            self.order_number_label.setText(f"Commande: {self.order.po_number or f'CMD-{self.order.id}'}")
            self.supplier_label.setText(f"Fournisseur: {self.order.supplier.name if self.order.supplier else 'N/A'}")

            # Initialiser les articles à réceptionner
            self.received_items = []
            for item in self.order.items:
                # Calculer la quantité restante à recevoir
                remaining_qty = item.quantity - (item.received_quantity or 0)
                if remaining_qty > 0:
                    self.received_items.append({
                        "item_id": item.id,
                        "product_id": item.product_id,
                        "product_name": item.product.name if item.product else f"Produit #{item.product_id}",
                        "ordered_qty": item.quantity,
                        "received_qty": item.received_quantity or 0,
                        "remaining_qty": remaining_qty,
                        "to_receive": remaining_qty  # Quantité à recevoir par défaut
                    })

            # Mettre à jour le tableau des articles
            self._update_items_table()
        finally:
            self.loading_overlay.hide()

    def _update_items_table(self):
        """Met à jour le tableau des articles"""
        # Créer un modèle de tableau simple pour les articles
        from PyQt6.QtGui import QStandardItemModel, QStandardItem

        model = QStandardItemModel(0, 5, self)
        model.setHorizontalHeaderLabels(["Article", "Quantité", "Reçu", "À recevoir", "Prix unitaire"])

        for item in self.received_items:
            row = []
            # Nom de l'article
            row.append(QStandardItem(item.get("product_name", "N/A")))
            # Quantité commandée
            row.append(QStandardItem(str(item.get("ordered_qty", 0))))
            # Quantité déjà reçue
            row.append(QStandardItem(str(item.get("received_qty", 0))))
            # Quantité à recevoir
            row.append(QStandardItem(str(item.get("to_receive", 0))))
            # Prix unitaire (si disponible)
            unit_price = 0
            if hasattr(self.order, 'items'):
                for order_item in self.order.items:
                    if order_item.id == item.get("item_id"):
                        unit_price = getattr(order_item, 'purchase_unit_price', getattr(order_item, 'unit_price', 0))
                        break
            row.append(QStandardItem(f"{unit_price:.2f} DA"))

            model.appendRow(row)

        self.items_table.setModel(model)
        self.items_table.resizeColumnsToContents()

    def validate_and_accept(self):
        """Valide les données et accepte le dialogue"""
        # Instead of using asyncio.create_task directly
        # Use a different approach to run the async function

        import asyncio
        from PyQt6.QtCore import QTimer

        # Utiliser l'event loop existant ou en créer un nouveau de manière sûre
        try:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Run the async function in the loop
            loop.run_until_complete(self.save_receipt())
            self.accept()
        except Exception as e:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    async def save_receipt(self):
        """Sauvegarde la réception"""
        self.loading_overlay.show()
        try:
            # Récupérer les données du formulaire
            receipt_date = self.receipt_date_edit.date().toPyDate()
            delivery_note = self.delivery_note_edit.text().strip() or None
            notes = self.notes_edit.toPlainText().strip() or None

            # Récupérer les options de mise à jour des prix
            update_price = self.update_price_check.isChecked()
            update_selling_price = self.update_selling_price_check.isChecked()

            # Filtrer les articles avec une quantité à recevoir > 0
            items_to_receive = [
                {
                    "item_id": item["item_id"],
                    "quantity": item["to_receive"],
                    "update_price": update_price,
                    "update_selling_price": update_selling_price
                }
                for item in self.received_items
                if item["to_receive"] > 0
            ]

            # Enregistrer la réception
            updated_order = await self.purchasing_service.receive_items(
                self.order_id,
                {
                    "received_items": items_to_receive,
                    "receipt_date": receipt_date,
                    "delivery_note": delivery_note,
                    "notes": notes
                }
            )

            # Créer une facture si l'option est cochée
            if self.create_invoice_check.isChecked():
                # Récupérer le numéro de facture
                invoice_number = self.invoice_number_edit.text().strip()

                # Calculer la date d'échéance (30 jours après la date de réception)
                from datetime import timedelta
                due_date = receipt_date + timedelta(days=30)

                # Créer la facture
                try:
                    invoice = await self.purchasing_service.create_invoice_from_order(
                        self.order_id,
                        {
                            "invoice_number": invoice_number,
                            "invoice_date": receipt_date,
                            "due_date": due_date,
                            "notes": f"Facture créée automatiquement lors de la réception de la commande {updated_order.po_number or f'#PO-{updated_order.id}'}"
                        }
                    )

                    # Afficher un message de confirmation
                    QMessageBox.information(
                        self,
                        "Facture créée",
                        f"La facture {invoice.invoice_number} a été créée avec succès."
                    )
                except Exception as e:
                    # Afficher un message d'erreur
                    QMessageBox.warning(
                        self,
                        "Erreur",
                        f"La réception a été enregistrée, mais la création de la facture a échoué: {str(e)}"
                    )

            # Fermer le dialogue
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()
