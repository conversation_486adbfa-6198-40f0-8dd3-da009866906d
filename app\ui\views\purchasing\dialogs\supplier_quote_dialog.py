from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QTextEdit, QComboBox, QDateEdit,
    QPushButton, QDialogButtonBox, QMessageBox, QSpinBox, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime, timedelta

from app.core.services.purchasing_service import PurchasingService
from app.core.services.supplier_service import SupplierService
from app.core.services.inventory_service import InventoryService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay


class SupplierQuoteDialog(QDialog):
    """Dialogue pour ajouter/modifier un devis fournisseur"""

    def __init__(self, parent=None, quote_id=None):
        super().__init__(parent)
        self.quote_id = quote_id
        self.is_edit_mode = quote_id is not None

        # Services
        self.db = SessionLocal()
        self.purchasing_service = PurchasingService(self.db)
        self.supplier_service = SupplierService(self.db)
        self.inventory_service = InventoryService(self.db)

        # Données
        self.suppliers = []
        self.products = []
        self.quote = None

        # Configuration de la fenêtre
        self.setWindowTitle("Nouveau devis" if not self.is_edit_mode else "Modifier le devis")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # Initialisation de l'interface
        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.init_data()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        form_layout = QFormLayout()

        # Fournisseur
        self.supplier_combo = QComboBox()
        form_layout.addRow("Fournisseur:", self.supplier_combo)

        # Produit
        self.product_combo = QComboBox()
        form_layout.addRow("Produit:", self.product_combo)

        # Quantité
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setMinimum(0.01)
        self.quantity_spin.setMaximum(9999999.99)
        self.quantity_spin.setValue(1.0)
        self.quantity_spin.setDecimals(2)
        form_layout.addRow("Quantité:", self.quantity_spin)

        # Prix d'achat
        self.purchase_unit_price_spin = QDoubleSpinBox()
        self.purchase_unit_price_spin.setMinimum(0.01)
        self.purchase_unit_price_spin.setMaximum(9999999.99)
        self.purchase_unit_price_spin.setValue(0.0)
        self.purchase_unit_price_spin.setDecimals(2)
        self.purchase_unit_price_spin.setSuffix(" DA")
        form_layout.addRow("Prix d'achat:", self.purchase_unit_price_spin)

        # Délai de livraison
        self.delivery_time_spin = QSpinBox()
        self.delivery_time_spin.setMinimum(1)
        self.delivery_time_spin.setMaximum(365)
        self.delivery_time_spin.setValue(14)
        self.delivery_time_spin.setSuffix(" jours")
        form_layout.addRow("Délai de livraison:", self.delivery_time_spin)

        # Période de validité
        self.validity_period_spin = QSpinBox()
        self.validity_period_spin.setMinimum(1)
        self.validity_period_spin.setMaximum(365)
        self.validity_period_spin.setValue(30)
        self.validity_period_spin.setSuffix(" jours")
        form_layout.addRow("Période de validité:", self.validity_period_spin)

        # Conditions de paiement
        self.payment_terms_edit = QLineEdit()
        self.payment_terms_edit.setPlaceholderText("Ex: 30 jours fin de mois")
        form_layout.addRow("Conditions de paiement:", self.payment_terms_edit)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes ou commentaires sur ce devis")
        form_layout.addRow("Notes:", self.notes_edit)

        main_layout.addLayout(form_layout)

        # Montant total (calculé automatiquement)
        self.total_amount_label = QLabel("Montant total: 0.00 DA")
        self.total_amount_label.setObjectName("totalAmountLabel")
        main_layout.addWidget(self.total_amount_label)

        # Boutons de dialogue
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save |
            QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.validate_and_accept)
        self.button_box.rejected.connect(self.reject)

        main_layout.addWidget(self.button_box)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        # Mettre à jour le montant total lorsque la quantité ou le prix d'achat change
        self.quantity_spin.valueChanged.connect(self._update_total_amount)
        self.purchase_unit_price_spin.valueChanged.connect(self._update_total_amount)

    def init_data(self):
        """Initialise les données du dialogue"""
        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        try:
            # Utiliser l'event loop existant ou en créer un nouveau de manière sûre
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Exécuter la coroutine
            loop.run_until_complete(self.load_data())
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement: {str(e)}")

    async def load_data(self):
        """Charge les données nécessaires"""
        self.loading_overlay.show()
        try:
            # Charger les fournisseurs
            self.suppliers = await self.supplier_service.get_all()
            self.supplier_combo.clear()
            for supplier in self.suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)

            # Charger les produits
            self.products = await self.inventory_service.get_all()
            self.product_combo.clear()
            for product in self.products:
                self.product_combo.addItem(product.name, product.id)

            # En mode édition, charger le devis
            if self.is_edit_mode:
                self.quote = await self.purchasing_service.get_quote(self.quote_id)
                if self.quote:
                    self._populate_form()
        finally:
            self.loading_overlay.hide()

    def _populate_form(self):
        """Remplit le formulaire avec les données du devis"""
        if not self.quote:
            return

        # Fournisseur
        supplier_index = self.supplier_combo.findData(self.quote.supplier_id)
        if supplier_index >= 0:
            self.supplier_combo.setCurrentIndex(supplier_index)

        # Produit
        product_index = self.product_combo.findData(self.quote.product_id)
        if product_index >= 0:
            self.product_combo.setCurrentIndex(product_index)

        # Quantité et prix
        self.quantity_spin.setValue(self.quote.quantity)
        self.purchase_unit_price_spin.setValue(self.quote.purchase_unit_price)

        # Délais
        self.delivery_time_spin.setValue(self.quote.delivery_time)
        self.validity_period_spin.setValue(self.quote.validity_period)

        # Autres informations
        self.payment_terms_edit.setText(self.quote.payment_terms or "")
        self.notes_edit.setText(self.quote.notes or "")

        # Mettre à jour le montant total
        self._update_total_amount()

    def _update_total_amount(self):
        """Met à jour le montant total"""
        quantity = self.quantity_spin.value()
        purchase_unit_price = self.purchase_unit_price_spin.value()
        total = quantity * purchase_unit_price
        self.total_amount_label.setText(f"Montant total: {total:.2f} DA")

    def validate_and_accept(self):
        """Valide les données et accepte le dialogue"""
        # Vérifier que les champs obligatoires sont remplis
        if self.supplier_combo.currentIndex() < 0:
            QMessageBox.warning(self, "Validation", "Veuillez sélectionner un fournisseur.")
            return

        if self.product_combo.currentIndex() < 0:
            QMessageBox.warning(self, "Validation", "Veuillez sélectionner un produit.")
            return

        if self.quantity_spin.value() <= 0:
            QMessageBox.warning(self, "Validation", "La quantité doit être supérieure à zéro.")
            return

        if self.purchase_unit_price_spin.value() <= 0:
            QMessageBox.warning(self, "Validation", "Le prix d'achat doit être supérieur à zéro.")
            return

        # Sauvegarder le devis
        self._save_quote_wrapper()

    def _save_quote_wrapper(self):
        """Wrapper pour exécuter save_quote de manière asynchrone"""
        try:
            # Utiliser l'event loop existant ou en créer un nouveau de manière sûre
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Exécuter la coroutine
            loop.run_until_complete(self.save_quote())
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")

    async def save_quote(self):
        """Sauvegarde le devis"""
        self.loading_overlay.show()
        try:
            # Récupérer les données du formulaire
            supplier_id = self.supplier_combo.currentData()
            product_id = self.product_combo.currentData()
            quantity = self.quantity_spin.value()
            purchase_unit_price = self.purchase_unit_price_spin.value()
            delivery_time = self.delivery_time_spin.value()
            validity_period = self.validity_period_spin.value()
            payment_terms = self.payment_terms_edit.text().strip() or None
            notes = self.notes_edit.toPlainText().strip() or None

            # Calculer le montant total et la date de validité
            total_amount = quantity * purchase_unit_price
            valid_until = datetime.now() + timedelta(days=validity_period)

            # Créer ou mettre à jour le devis
            if self.is_edit_mode:
                # Mettre à jour le devis existant
                await self.purchasing_service.update_quote(
                    self.quote_id,
                    {
                        "supplier_id": supplier_id,
                        "product_id": product_id,
                        "quantity": quantity,
                        "purchase_unit_price": purchase_unit_price,
                        "total_amount": total_amount,
                        "delivery_time": delivery_time,
                        "validity_period": validity_period,
                        "payment_terms": payment_terms,
                        "notes": notes,
                        "valid_until": valid_until
                    }
                )
            else:
                # Créer un nouveau devis
                await self.purchasing_service.create_quote(
                    {
                        "supplier_id": supplier_id,
                        "product_id": product_id,
                        "quantity": quantity,
                        "purchase_unit_price": purchase_unit_price,
                        "total_amount": total_amount,
                        "delivery_time": delivery_time,
                        "validity_period": validity_period,
                        "payment_terms": payment_terms,
                        "notes": notes,
                        "valid_until": valid_until
                    }
                )

            # Fermer le dialogue
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()
