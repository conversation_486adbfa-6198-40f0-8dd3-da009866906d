from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex
from PyQt6.QtGui import QColor
import asyncio

from app.core.models.purchasing import PurchaseOrderItem


class OrderItemsTableModel(QAbstractTableModel):
    """Modèle de table pour les articles d'une commande d'achat"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.items = []
        self.headers = [
            "Produit", "Quantité", "Prix d'achat", "Total",
            "Quantité reçue", "Spécifications", "Date de livraison"
        ]

    def rowCount(self, parent=QModelIndex()):
        return len(self.items)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return None

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.items):
            return None

        item = self.items[index.row()]
        column = index.column()

        if role == Qt.ItemDataRole.DisplayRole:
            if column == 0:
                return item.product.name if hasattr(item, 'product') and item.product else f"Produit #{item.product_id}"
            elif column == 1:
                return f"{item.quantity:.2f}"
            elif column == 2:
                return f"{item.purchase_unit_price:.2f} DA"
            elif column == 3:
                return f"{item.purchase_unit_price * item.quantity:.2f} DA"
            elif column == 4:
                return f"{item.received_quantity:.2f}" if item.received_quantity else "0.00"
            elif column == 5:
                return str(item.specifications) if item.specifications else ""
            elif column == 6:
                return item.delivery_date.strftime("%d/%m/%Y") if item.delivery_date else "N/A"

        elif role == Qt.ItemDataRole.BackgroundRole:
            # Mettre en évidence les articles entièrement reçus
            if item.received_quantity and item.received_quantity >= item.quantity:
                return QColor("#E8F5E9")  # Vert très clair

        return None

    def set_items(self, items):
        """Définit les articles à afficher"""
        self.beginResetModel()
        self.items = items
        self.endResetModel()

    def get_item(self, row):
        """Retourne l'article à la ligne spécifiée"""
        if 0 <= row < len(self.items):
            return self.items[row]
        return None
