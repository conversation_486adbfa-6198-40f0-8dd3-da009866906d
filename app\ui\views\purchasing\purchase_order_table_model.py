from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex
from PyQt6.QtGui import QColor
import asyncio
from datetime import datetime

from app.core.services.purchasing_service import PurchasingService
from app.core.models.purchasing import OrderStatus
from app.utils.database import SessionLocal


class PurchaseOrderTableModel(QAbstractTableModel):
    """Modèle de table pour les commandes d'achat"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.orders = []
        self.headers = [
            "N° Commande", "Fournisseur", "Date", "Statut",
            "Montant Total", "Livraison Prévue", "Créé par"
        ]

        # Couleurs pour les statuts
        self.status_colors = {
            OrderStatus.DRAFT: QColor("#E3F2FD"),  # Bleu clair
            OrderStatus.PENDING: QColor("#FFF9C4"),  # Jaune clair
            OrderStatus.SUBMITTED: QColor("#FFE0B2"),  # Orange clair
            OrderStatus.APPROVED: QColor("#C8E6C9"),  # Vert clair
            OrderStatus.ORDERED: QColor("#BBDEFB"),  # Bleu
            OrderStatus.PARTIALLY_RECEIVED: QColor("#DCEDC8"),  # Vert-jaune
            OrderStatus.COMPLETED: QColor("#A5D6A7"),  # Vert
            OrderStatus.CANCELLED: QColor("#FFCDD2"),  # Rouge clair
        }

        # Service
        self.db = SessionLocal()
        self.service = PurchasingService(self.db)

    def rowCount(self, parent=QModelIndex()):
        return len(self.orders)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return None

    def data(self, index, role):
        if not index.isValid():
            return None

        if role == Qt.ItemDataRole.DisplayRole:
            order = self.orders[index.row()]
            column = index.column()

            if column == 0:
                return order.po_number or f"PO-{order.id}"
            elif column == 1:
                return order.supplier.name if order.supplier else ""
            elif column == 2:
                return order.order_date.strftime("%Y-%m-%d") if hasattr(order, 'order_date') and order.order_date else ""
            elif column == 3:
                return self._get_status_display(order.status)
            elif column == 4:
                # Handle the case where total_amount might be None or 0
                if order.total_amount is None or order.total_amount == 0:
                    # Calculer le montant total à partir des articles de la commande
                    total = 0
                    if hasattr(order, 'items') and order.items:
                        for item in order.items:
                            if hasattr(item, 'quantity'):
                                # Utiliser purchase_unit_price en priorité, unit_price comme fallback pour compatibilité
                                price = getattr(item, 'purchase_unit_price', getattr(item, 'unit_price', 0))
                                total += price * item.quantity
                    return f"{total:.2f} {order.currency}" if order.currency else f"{total:.2f}"
                else:
                    return f"{order.total_amount:.2f} {order.currency}"
            elif column == 5:
                return order.expected_delivery.strftime("%Y-%m-%d") if hasattr(order, 'expected_delivery') and order.expected_delivery else ""
            elif column == 6:
                return order.created_by_user.username if hasattr(order, 'created_by_user') and order.created_by_user else ""

        elif role == Qt.ItemDataRole.BackgroundRole:
            order = self.orders[index.row()]
            if order.status in self.status_colors:
                return self.status_colors[order.status]

        return None

    def _get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            OrderStatus.DRAFT: "Brouillon",
            OrderStatus.PENDING: "En attente",
            OrderStatus.SUBMITTED: "Soumis",
            OrderStatus.APPROVED: "Approuvé",
            OrderStatus.ORDERED: "Commandé",
            OrderStatus.PARTIALLY_RECEIVED: "Partiellement reçu",
            OrderStatus.COMPLETED: "Terminé",
            OrderStatus.CANCELLED: "Annulé",
        }
        return status_display.get(status, str(status))

    async def load_data(self):
        """Charge les données des commandes d'achat de manière asynchrone"""
        self.beginResetModel()

        # Récupérer toutes les commandes avec leurs articles
        self.orders = await self.service.get_all_with_items()

        self.endResetModel()

    def load_data_sync(self):
        """Charge les données des commandes d'achat de manière synchrone"""
        self.beginResetModel()

        # Récupérer toutes les commandes de manière synchrone
        self.orders = self.service.get_all_sync()

        self.endResetModel()

    def get_order_id(self, row):
        """Retourne l'ID de la commande à la ligne spécifiée"""
        if 0 <= row < len(self.orders):
            return self.orders[row].id
        return None

    def get_order(self, row):
        """Retourne la commande à la ligne spécifiée"""
        if 0 <= row < len(self.orders):
            return self.orders[row]
        return None
