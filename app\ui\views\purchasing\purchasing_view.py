from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView, QSplitter,
    QLabel, QFrame, QMessageBox, QTabWidget, QPushButton, QHeaderView
)
from PyQt6.QtCore import Qt, QSortFilterProxyModel, QTimer
from PyQt6.QtGui import QIcon
import asyncio

from .purchase_order_table_model import PurchaseOrderTableModel
from .dialogs.purchase_order_dialog import PurchaseOrderDialog
from .dialogs.supplier_quote_dialog import SupplierQuoteDialog
from .dialogs.receive_order_dialog import ReceiveOrderDialog
from .dialogs.low_stock_dialog import LowStockDialog
from .widgets.order_details_widget import OrderDetailsWidget
from .widgets.order_items_widget import OrderItemsWidget
from .widgets.financial_details_widget import FinancialDetailsWidget
from app.ui.components.custom_widgets import <PERSON><PERSON><PERSON>omboB<PERSON>, <PERSON>adingOverlay, SearchBar
from app.core.models.purchasing import OrderStatus
from app.core.services.purchasing_service import PurchasingService
from app.utils.database import SessionLocal


class PurchasingView(QWidget):
    """Vue principale du module de gestion des achats"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = PurchasingService(self.db)

        # Configuration de l'interface
        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.init_data()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)



        # Barre d'outils + Recherche + Filtre sur une seule ligne
        topbar_layout = QHBoxLayout()

        # Toolbar (4 boutons)
        self.add_order_button = QPushButton("Nouvelle Commande")
        self.add_order_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        topbar_layout.addWidget(self.add_order_button)

        self.low_stock_button = QPushButton("Articles à faible stock")
        self.low_stock_button.setIcon(QIcon("app/ui/resources/icons/warning.svg"))
        topbar_layout.addWidget(self.low_stock_button)

        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))
        topbar_layout.addWidget(self.export_button)

        self.help_button = QPushButton("Aide")
        self.help_button.setIcon(QIcon("app/ui/resources/icons/help.svg"))
        topbar_layout.addWidget(self.help_button)

        # Barre de recherche
        self.search_bar = SearchBar()
        self.search_bar.setPlaceholderText("Rechercher une commande...")
        topbar_layout.addWidget(self.search_bar, 1)

        # Filtre de statut
        self.status_filter = FilterComboBox()
        self.status_filter.addItem("Tous les statuts", None)
        for status in OrderStatus:
            self.status_filter.addItem(self._get_status_display(status), status.value)
        topbar_layout.addWidget(self.status_filter)

        main_layout.addLayout(topbar_layout)

        # Contenu principal avec splitter (vertical: panneau en haut, tableau en bas)
        splitter = QSplitter(Qt.Orientation.Vertical)

        # Tableau des commandes (à gauche)
        orders_widget = QWidget()
        orders_layout = QVBoxLayout(orders_widget)
        orders_layout.setContentsMargins(0, 0, 0, 0)

        self.table_view = QTableView()
        self.table_view.setObjectName("ordersTable")
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.setAlternatingRowColors(True)

        # Modèle de données
        self.table_model = PurchaseOrderTableModel()
        self.proxy_model = QSortFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.table_view.setModel(self.proxy_model)

        # Configuration du tableau pour une meilleure présentation
        header = self.table_view.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Désactiver l'édition directe dans le tableau
        self.table_view.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.table_view.setShowGrid(True)
        self.table_view.setSortingEnabled(True)

        orders_layout.addWidget(self.table_view)
        # S'assurer que la zone du tableau reste visible
        orders_widget.setMinimumHeight(200)

        # Détails de la commande (à droite)
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        details_layout.setContentsMargins(0, 0, 0, 0)

        # Onglets pour les détails
        self.details_tabs = QTabWidget()

        # Onglet Informations
        self.order_details_widget = OrderDetailsWidget()
        self.details_tabs.addTab(self.order_details_widget, "Informations")

        # Onglet Articles
        self.order_items_widget = OrderItemsWidget()
        self.details_tabs.addTab(self.order_items_widget, "Articles")

        # Onglet Finances
        self.financial_details_widget = FinancialDetailsWidget()
        self.details_tabs.addTab(self.financial_details_widget, "Finances")

        # Onglet Factures
        from .widgets.invoice_details_widget import InvoiceDetailsWidget
        self.invoice_details_widget = InvoiceDetailsWidget()
        self.details_tabs.addTab(self.invoice_details_widget, "Factures")

        # Onglet Paiements
        from .widgets.payment_details_widget import PaymentDetailsWidget
        self.payment_details_widget = PaymentDetailsWidget()
        self.details_tabs.addTab(self.payment_details_widget, "Paiements")

        # Onglet Historique
        from .widgets.order_history_widget import OrderHistoryWidget
        self.order_history_widget = OrderHistoryWidget()
        self.details_tabs.addTab(self.order_history_widget, "Historique")

        details_layout.addWidget(self.details_tabs)

        # Ajouter les widgets au splitter (panneau en haut, tableau en bas)
        splitter.addWidget(details_widget)
        splitter.addWidget(orders_widget)

        # Définir les tailles initiales robustes et facteurs d'étirement
        # Utiliser des valeurs fixes pour éviter 0 au démarrage, puis laisser s’étirer
        splitter.setSizes([400, 600])  # ~40% / 60% par défaut
        splitter.setStretchFactor(0, 2)  # panneau de détails (moins d'espace vertical)
        splitter.setStretchFactor(1, 3)  # tableau des commandes (plus d'espace vertical)

        main_layout.addWidget(splitter)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        # Connexions recherches et filtres
        self.search_bar.textChanged.connect(self.filter_orders)
        self.status_filter.currentIndexChanged.connect(self.filter_orders)

        # Connexions toolbar
        self.add_order_button.clicked.connect(self.show_add_order_dialog)
        self.low_stock_button.clicked.connect(self.show_low_stock_dialog)
        self.export_button.clicked.connect(self.export_orders)
        self.help_button.clicked.connect(self.show_help)

        # Connexions du tableau
        self.table_view.selectionModel().selectionChanged.connect(self._on_selection_changed)
        self.table_view.doubleClicked.connect(self.show_edit_order_dialog)

        # Connexions pour les actions sur les commandes
        self.order_details_widget.orderSubmitted.connect(self.submit_order)
        self.order_details_widget.orderApproved.connect(self.approve_order)
        self.order_details_widget.orderCancelled.connect(self.cancel_order)
        self.order_details_widget.orderReceived.connect(self.receive_order)

        # Connexions pour les actions financières
        self.payment_details_widget.paymentRecorded.connect(self._on_payment_recorded)
        self.invoice_details_widget.invoiceCreated.connect(self._on_invoice_created)

    def init_data(self):
        """Initialise les données de la vue"""
        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter le chargement des données"""
        # Utiliser directement la version synchrone pour éviter les problèmes avec asyncio
        self._load_data_sync()

    def _load_data_sync(self):
        """Version synchrone de load_data"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau de manière synchrone
            self.table_model.load_data_sync()

            # Ajuster les colonnes
            self.table_view.resizeColumnsToContents()

        finally:
            self.loading_overlay.hide()

    async def load_data(self):
        """Charge les données des commandes"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau
            await self.table_model.load_data()

            # Ajuster les colonnes
            self.table_view.resizeColumnsToContents()

        finally:
            self.loading_overlay.hide()

    def filter_orders(self):
        """Applique les filtres (recherche + statut) sur le tableau des commandes"""
        search_text = (self.search_bar.text() or "").strip().lower()
        status = self.status_filter.currentData()

        def row_matches(row: int) -> bool:
            order = self.table_model.get_order(row)
            if not order:
                return False
            # Filtre statut
            if status and order.status.value != status:
                return False
            # Filtre recherche
            if search_text:
                po_number = (order.po_number or f"PO-{order.id}")
                if search_text in po_number.lower():
                    return True
                if order.supplier and search_text in order.supplier.name.lower():
                    return True
                status_display = self._get_status_display(order.status)
                if search_text in status_display.lower():
                    return True
                return False
            return True

        # Réinitialiser l'état de visibilité et appliquer le filtre
        for row in range(self.table_model.rowCount()):
            self.table_view.setRowHidden(row, not row_matches(row))

        # Garder les détails synchronisés
        current_indexes = self.table_view.selectionModel().selectedRows()
        if current_indexes:
            self._on_selection_changed(self.table_view.selectionModel().selection(), None)

    def show_add_order_dialog(self):
        """Affiche la boîte de dialogue d'ajout de commande"""
        dialog = PurchaseOrderDialog(self)
        if dialog.exec():
            self._load_data_wrapper()

    def show_edit_order_dialog(self, index):
        """Affiche la boîte de dialogue d'édition de commande"""
        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(index)
        order_id = self.table_model.get_order_id(source_index.row())

        dialog = PurchaseOrderDialog(self, order_id=order_id)
        if dialog.exec():
            self._load_data_wrapper()

    def show_add_quote_dialog(self):
        """Affiche la boîte de dialogue d'ajout de devis"""
        dialog = SupplierQuoteDialog(self)
        if dialog.exec():
            # Pas besoin de recharger les commandes car les devis sont gérés séparément
            pass

    def show_low_stock_dialog(self):
        """Affiche la boîte de dialogue des articles à faible stock"""
        dialog = LowStockDialog(self)
        if dialog.exec():
            # Recharger les données car de nouvelles commandes ont pu être créées
            self._load_data_wrapper()

    def export_orders(self):
        """Exporte les données des commandes"""
        # TODO: Implémenter l'export des données
        QMessageBox.information(self, "Export", "Fonctionnalité d'export non implémentée.")

    def show_help(self):
        """Affiche l'aide sur le flux de travail des commandes"""
        try:
            # Lire le fichier d'aide
            with open("app/ui/views/purchasing/help/workflow_help.md", "r", encoding="utf-8") as f:
                help_content = f.read()

            # Afficher l'aide dans une boîte de dialogue
            help_dialog = QMessageBox(self)
            help_dialog.setWindowTitle("Aide - Flux de travail des commandes")
            help_dialog.setText(help_content)
            help_dialog.setStandardButtons(QMessageBox.StandardButton.Ok)
            help_dialog.setDefaultButton(QMessageBox.StandardButton.Ok)
            help_dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Impossible de charger l'aide : {str(e)}")

    def _on_selection_changed(self, selected, _):
        """Gère le changement de sélection dans le tableau"""
        indexes = selected.indexes()
        if indexes:
            # Convertir l'index du modèle proxy en index du modèle source
            source_index = self.proxy_model.mapToSource(indexes[0])
            order = self.table_model.get_order(source_index.row())

            # Mettre à jour les widgets de détails
            self.order_details_widget.set_order(order)
            self.order_items_widget.set_items(order.items if order else [])
            self.financial_details_widget.set_order(order)
            self.invoice_details_widget.set_order(order)
            self.payment_details_widget.set_order(order)
            self.order_history_widget.set_order_id(order.id if order else None)
        else:
            # Effacer les détails
            self.order_details_widget.set_order(None)
            self.order_items_widget.set_items([])
            self.financial_details_widget.set_order(None)
            self.invoice_details_widget.set_order(None)
            self.payment_details_widget.set_order(None)
            self.order_history_widget.set_order_id(None)

    def submit_order(self, order_id):
        """Soumet une commande pour approbation (exécuté de façon synchrone dans le thread UI)"""
        self.loading_overlay.show()
        try:
            # Récupérer l'ID de l'utilisateur connecté (à remplacer par la méthode appropriée)
            user_id = 1  # Valeur par défaut pour les tests

            # Exécuter la coroutine du service dans une boucle événementielle dédiée
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.service.submit_order(order_id, user_id))
            finally:
                loop.close()

            # Recharger les données et re-sélectionner la commande affectée
            self._load_data_wrapper()
            self._reselect_order(order_id)

            QMessageBox.information(self, "Succès", "La commande a été soumise avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def approve_order(self, order_id):
        """Approuve une commande (exécuté de façon synchrone dans le thread UI)"""
        self.loading_overlay.show()
        try:
            # Récupérer l'ID de l'utilisateur connecté (à remplacer par la méthode appropriée)
            user_id = 1  # Valeur par défaut pour les tests

            # Exécuter la coroutine du service dans une boucle événementielle dédiée
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.service.approve_order(order_id, user_id))
            finally:
                loop.close()

            # Recharger les données et re-sélectionner la commande affectée
            self._load_data_wrapper()
            self._reselect_order(order_id)

            QMessageBox.information(self, "Succès", "La commande a été approuvée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def cancel_order(self, order_id):
        """Annule une commande (exécuté de façon synchrone dans le thread UI)"""
        # Demander confirmation
        reply = QMessageBox.question(
            self, "Confirmation",
            "Êtes-vous sûr de vouloir annuler cette commande ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.loading_overlay.show()
            try:
                # Exécuter la coroutine du service dans une boucle événementielle dédiée
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(self.service.cancel_order(order_id))
                finally:
                    loop.close()
                # Recharger les données et re-sélectionner la commande affectée
                self._load_data_wrapper()
                self._reselect_order(order_id)
                QMessageBox.information(self, "Succès", "La commande a été annulée avec succès.")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
            finally:
                self.loading_overlay.hide()

    def receive_order(self, order_id):
        """Affiche la boîte de dialogue de réception de commande"""
        try:
            dialog = ReceiveOrderDialog(self, order_id=order_id)
            if dialog.exec():
                self._load_data_wrapper()
                QMessageBox.information(self, "Succès", "La commande a été réceptionnée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'ouverture du dialogue de réception: {str(e)}")

    def _get_order_with_relations(self, order_id):
        """Recharge l'objet order avec toutes ses relations (supplier, items)"""
        from sqlalchemy.orm import joinedload
        from app.core.models.purchasing import PurchaseOrder, PurchaseOrderItem
        # Utiliser la session pour charger les relations existantes
        return self.db.query(PurchaseOrder).options(
            joinedload(PurchaseOrder.supplier),
            joinedload(PurchaseOrder.items).joinedload(PurchaseOrderItem.product),
            joinedload(PurchaseOrder.creator),
            joinedload(PurchaseOrder.approver)
        ).filter_by(id=order_id).first()

    def _on_payment_recorded(self, order_id):
        """Gère l'enregistrement d'un paiement"""
        # Recharger les données
        self._load_data_wrapper()

        # Recharger l'objet order à jour avec toutes ses relations
        order = self._get_order_with_relations(order_id)
        if order:
            self.financial_details_widget.set_order(order)
        self._reselect_order(order_id)

    def _on_invoice_created(self, order_id):
        """Gère la création d'une facture"""
        from .dialogs.invoice_dialog import InvoiceDialog

        try:
            # Ouvrir le dialogue de création de facture
            dialog = InvoiceDialog(self, order_id=order_id)
            if dialog.exec():
                # Recharger les données après création de la facture
                self._load_data_wrapper()
                order = self._get_order_with_relations(order_id)
                def update_financial_details():
                    if order:
                        self.financial_details_widget.set_order(order)
                        self.invoice_details_widget.set_order(order)
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(0, update_financial_details)
                self._reselect_order(order_id)
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'ouverture du dialogue de facture: {str(e)}")

    def _get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            OrderStatus.DRAFT: "Brouillon",
            OrderStatus.PENDING: "En attente",
            OrderStatus.SUBMITTED: "Soumis",
            OrderStatus.APPROVED: "Approuvé",
            OrderStatus.ORDERED: "Commandé",
            OrderStatus.PARTIALLY_RECEIVED: "Partiellement reçu",
            OrderStatus.COMPLETED: "Terminé",
            OrderStatus.CANCELLED: "Annulé",
        }
        return status_display.get(status, str(status))

    def _reselect_order(self, order_id: int) -> None:
        """Re-sélectionne la ligne de la commande et fait défiler la vue si nécessaire."""
        if order_id is None:
            return
        try:
            # Parcourir le modèle source pour retrouver la ligne
            for row in range(self.table_model.rowCount()):
                try:
                    current_id = self.table_model.get_order_id(row)
                except Exception:
                    current_id = None
                if current_id == order_id:
                    source_index = self.table_model.index(row, 0)
                    proxy_index = self.proxy_model.mapFromSource(source_index)
                    if proxy_index.isValid():
                        self.table_view.selectRow(proxy_index.row())
                        self.table_view.scrollTo(proxy_index)
                    break
        except Exception:
            # Sélection silencieuse: ne pas interrompre le flux en cas de petit souci
            pass
