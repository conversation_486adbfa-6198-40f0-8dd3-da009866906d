from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QFrame
)
from PyQt6.QtCore import Qt


class FinancialDetailsWidget(QWidget):
    """Widget affichant les détails financiers d'une commande d'achat"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.order = None
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        self.title_label = QLabel("Détails financiers")
        self.title_label.setObjectName("sectionSubHeader")
        main_layout.addWidget(self.title_label)

        # Sections encadrées, chaque item en horizontal (label à gauche, valeur à droite)
        frame_style = "QFrame { border: none; background: transparent; }"
        title_style = "color: #555; font-weight: 600; font-size: 12px;"
        value_style = "color: #222; font-size: 14px; font-weight: 700;"

        def make_item(title, value_label_attr, word_wrap=False):
            w = QWidget()
            h = QHBoxLayout(w)
            h.setContentsMargins(6, 6, 6, 6)
            h.setSpacing(6)
            t = QLabel(title)
            t.setStyleSheet(title_style)
            t.setFixedWidth(120)
            t.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(t)
            val = QLabel("-")
            val.setStyleSheet(value_style)
            val.setWordWrap(word_wrap)
            val.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(val, 1)
            setattr(self, value_label_attr, val)
            return w

        # Frame 1 - Montants de base (colonne gauche)
        frame1 = QFrame()
        frame1.setStyleSheet(frame_style)
        f1 = QVBoxLayout(frame1)
        f1.setContentsMargins(8, 8, 8, 8)
        f1.setSpacing(8)
        f1.addWidget(make_item("Sous-total", "subtotal_label"))
        f1.addWidget(make_item("Remise", "discount_label"))
        f1.addWidget(make_item("TVA", "tax_label"))

        # Frame 2 - Frais et total (colonne droite)
        frame2 = QFrame()
        frame2.setStyleSheet(frame_style)
        f2 = QVBoxLayout(frame2)
        f2.setContentsMargins(8, 8, 8, 8)
        f2.setSpacing(8)
        f2.addWidget(make_item("Frais livraison", "shipping_label"))
        f2.addWidget(make_item("Montant total", "total_amount_label"))

        # Disposer les 2 frames côte à côte
        row_layout = QHBoxLayout()
        row_layout.setSpacing(12)
        row_layout.addWidget(frame1, 1)
        row_layout.addWidget(frame2, 1)
        main_layout.addLayout(row_layout)

        # Espacement
        main_layout.addStretch()

    def set_order(self, order):
        """Définit la commande à afficher"""
        self.order = order

        if not order:
            self._clear_details()
            return

        # Mettre à jour les détails de base
        self.subtotal_label.setText(f"{order.subtotal_amount:.2f} DA" if hasattr(order, 'subtotal_amount') else "0.00 DA")
        self.discount_label.setText(f"{order.discount_amount:.2f} DA" if hasattr(order, 'discount_amount') else "0.00 DA")
        self.tax_label.setText(f"{order.tax_amount:.2f} DA" if hasattr(order, 'tax_amount') else "0.00 DA")
        self.shipping_label.setText(f"{order.shipping_amount:.2f} DA" if hasattr(order, 'shipping_amount') else "0.00 DA")
        self.total_amount_label.setText(f"{order.total_amount:.2f} DA" if order.total_amount else "0.00 DA")

    def _clear_details(self):
        """Efface les détails affichés"""
        self.subtotal_label.setText("0.00 DA")
        self.discount_label.setText("0.00 DA")
        self.tax_label.setText("0.00 DA")
        self.shipping_label.setText("0.00 DA")
        self.total_amount_label.setText("0.00 DA")



