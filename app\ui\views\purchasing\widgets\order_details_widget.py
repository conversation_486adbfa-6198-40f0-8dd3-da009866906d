from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QPushButton, QFrame, QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon
from datetime import datetime

from app.core.models.purchasing import OrderStatus


class OrderDetailsWidget(QWidget):
    """Widget affichant les détails d'une commande d'achat"""

    # Signaux
    orderSubmitted = pyqtSignal(int)  # ID de la commande soumise
    orderApproved = pyqtSignal(int)  # ID de la commande approuvée
    orderCancelled = pyqtSignal(int)  # ID de la commande annulée
    orderReceived = pyqtSignal(int)  # ID de la commande reçue

    def __init__(self, parent=None):
        super().__init__(parent)
        self.order = None

        # Créer une session de base de données
        try:
            from app.utils.database import SessionLocal
            self.db = SessionLocal()
            print("OrderDetailsWidget: Nouvelle session de base de données créée")
        except Exception as e:
            print(f"Erreur lors de la création de la session: {e}")
            self.db = None

        self.setup_ui()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            try:
                self.db.close()
                print("OrderDetailsWidget: Session de base de données fermée")
            except Exception as e:
                print(f"Erreur lors de la fermeture de la session: {e}")

    def refresh_session(self):
        """Rafraîchit la session de base de données"""
        if hasattr(self, 'db') and self.db:
            try:
                self.db.close()
                print("OrderDetailsWidget: Ancienne session fermée")
            except Exception as e:
                print(f"Erreur lors de la fermeture de la session: {e}")

        try:
            from app.utils.database import SessionLocal
            self.db = SessionLocal()
            print("OrderDetailsWidget: Nouvelle session créée")
        except Exception as e:
            print(f"Erreur lors de la création de la session: {e}")
            self.db = None

    def _safe_get_attr(self, obj, attr_name, default=None):
        """Récupère un attribut de manière sécurisée

        Args:
            obj: L'objet dont on veut récupérer l'attribut
            attr_name: Le nom de l'attribut
            default: La valeur par défaut si l'attribut n'existe pas ou si une erreur se produit

        Returns:
            La valeur de l'attribut ou la valeur par défaut
        """
        try:
            if hasattr(obj, attr_name):
                return getattr(obj, attr_name)
            return default
        except Exception as e:
            print(f"Erreur lors de la récupération de l'attribut {attr_name}: {e}")
            return default

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        self.title_label = QLabel("Détails de la commande")
        self.title_label.setObjectName("sectionSubHeader")
        main_layout.addWidget(self.title_label)

        # Sections encadrées, chaque item en horizontal (label à gauche, valeur à droite)
        frame_style = "QFrame { border: none; background: transparent; }"
        title_style = "color: #555; font-weight: 600; font-size: 12px;"
        value_style = "color: #222; font-size: 14px; font-weight: 700;"

        def make_item(title, value_label_attr, word_wrap=False):
            w = QWidget()
            h = QHBoxLayout(w)
            h.setContentsMargins(6, 6, 6, 6)
            h.setSpacing(6)
            t = QLabel(title)
            t.setStyleSheet(title_style)
            t.setFixedWidth(120)
            t.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(t)
            val = QLabel("-")
            val.setStyleSheet(value_style)
            val.setWordWrap(word_wrap)
            val.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(val, 1)
            setattr(self, value_label_attr, val)
            return w

        # Frame 1 - Informations générales (colonne gauche)
        frame1 = QFrame()
        frame1.setStyleSheet(frame_style)
        f1 = QVBoxLayout(frame1)
        f1.setContentsMargins(8, 8, 8, 8)
        f1.setSpacing(8)
        f1.addWidget(make_item("N° Commande", "po_number_label"))
        f1.addWidget(make_item("Fournisseur", "supplier_label"))
        f1.addWidget(make_item("Statut", "status_label"))
        f1.addWidget(make_item("Montant total", "total_amount_label"))

        # Frame 2 - Dates et livraison (colonne centrale)
        frame2 = QFrame()
        frame2.setStyleSheet(frame_style)
        f2 = QVBoxLayout(frame2)
        f2.setContentsMargins(8, 8, 8, 8)
        f2.setSpacing(8)
        f2.addWidget(make_item("Date commande", "order_date_label"))
        f2.addWidget(make_item("Livraison prévue", "expected_delivery_label"))
        f2.addWidget(make_item("Créé par", "created_by_label"))
        f2.addWidget(make_item("Notes", "notes_label", word_wrap=True))

        # Frame 3 - Workflow et approbation (colonne droite)
        frame3 = QFrame()
        frame3.setStyleSheet(frame_style)
        f3 = QVBoxLayout(frame3)
        f3.setContentsMargins(8, 8, 8, 8)
        f3.setSpacing(8)
        f3.addWidget(make_item("Soumis par", "submitted_by_label"))
        f3.addWidget(make_item("Date soumission", "submitted_at_label"))
        f3.addWidget(make_item("Approuvé par", "approved_by_label"))
        f3.addWidget(make_item("Date approbation", "approved_at_label"))

        # Disposer les 3 frames côte à côte
        row_layout = QHBoxLayout()
        row_layout.setSpacing(12)
        row_layout.addWidget(frame1, 1)
        row_layout.addWidget(frame2, 1)
        row_layout.addWidget(frame3, 1)
        main_layout.addLayout(row_layout)

        # Actions
        actions_group = QGroupBox("Actions")
        actions_layout = QHBoxLayout(actions_group)

        self.submit_button = QPushButton("Soumettre")
        self.submit_button.setIcon(QIcon("app/ui/resources/icons/check.svg"))
        self.submit_button.setObjectName("infoButton")
        self.submit_button.setToolTip("Soumettre la commande pour approbation")
        self.submit_button.clicked.connect(self._on_submit_clicked)
        actions_layout.addWidget(self.submit_button)

        self.approve_button = QPushButton("Approuver")
        self.approve_button.setIcon(QIcon("app/ui/resources/icons/check.svg"))
        self.approve_button.setObjectName("successButton")
        self.approve_button.setToolTip("Approuver la commande soumise")
        self.approve_button.clicked.connect(self._on_approve_clicked)
        actions_layout.addWidget(self.approve_button)

        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.setIcon(QIcon("app/ui/resources/icons/cancel.svg"))
        self.cancel_button.setObjectName("dangerButton")
        self.cancel_button.setToolTip("Annuler la commande")
        self.cancel_button.clicked.connect(self._on_cancel_clicked)
        actions_layout.addWidget(self.cancel_button)

        self.receive_button = QPushButton("Réceptionner")
        self.receive_button.setIcon(QIcon("app/ui/resources/icons/delivery.svg"))
        self.receive_button.setObjectName("primaryButton")
        self.receive_button.setToolTip("Réceptionner les articles de la commande")
        self.receive_button.clicked.connect(self._on_receive_clicked)
        actions_layout.addWidget(self.receive_button)

        main_layout.addWidget(actions_group)

        # Espacement
        main_layout.addStretch()

    def set_order(self, order):
        """Définit la commande à afficher"""
        try:
            # Vérifier si l'ordre est valide
            if not order:
                self._clear_details()
                return

            # Vérifier si l'objet est détaché et le rafraîchir si nécessaire
            if hasattr(self, 'db') and self.db and hasattr(order, 'id'):
                try:
                    # Vérifier si la session est active
                    if not self.db.is_active:
                        self.refresh_session()

                    # Essayer d'accéder à un attribut pour voir si l'objet est détaché
                    try:
                        # Ceci va déclencher une erreur si l'objet est détaché
                        if hasattr(order, 'creator') and order.creator:
                            _ = order.creator.id
                    except Exception as e:
                        print(f"Objet détaché détecté: {e}")
                        # Essayer de rafraîchir l'objet
                        from app.core.services.purchasing_service import PurchasingService
                        service = PurchasingService(self.db)
                        fresh_order = service.get_sync(order.id)
                        if fresh_order:
                            order = fresh_order
                            print(f"Commande {order.id} rafraîchie avec succès")
                except Exception as e:
                    print(f"Erreur lors de la vérification/rafraîchissement de la commande: {e}")

            # Stocker la commande
            self.order = order

            # Récupérer les attributs de base de manière sécurisée
            order_id = self._safe_get_attr(order, 'id', 0)
            po_number = self._safe_get_attr(order, 'po_number', "")
            status = self._safe_get_attr(order, 'status', None)

            # Mettre à jour les détails de base
            self.po_number_label.setText(po_number or f"CMD-{order_id}")

            # Récupérer le fournisseur de manière sécurisée
            supplier = self._safe_get_attr(order, 'supplier', None)
            supplier_name = self._safe_get_attr(supplier, 'name', "N/A") if supplier else "N/A"
            self.supplier_label.setText(supplier_name)

            # Statut
            self.status_label.setText(self._get_status_display(status))

            # Dates
            order_date = self._safe_get_attr(order, 'order_date', None)
            self.order_date_label.setText(
                order_date.strftime("%d/%m/%Y") if order_date else "N/A"
            )

            expected_delivery = self._safe_get_attr(order, 'expected_delivery', None)
            self.expected_delivery_label.setText(
                expected_delivery.strftime("%d/%m/%Y") if expected_delivery else "N/A"
            )

            # Montant total
            total_amount = self._safe_get_attr(order, 'total_amount', None)
            if total_amount is None:
                self.total_amount_label.setText("0.00 DA")
            else:
                self.total_amount_label.setText(f"{total_amount:.2f} DA")

            # Informations de workflow
            creator = self._safe_get_attr(order, 'creator', None)
            creator_full_name = self._safe_get_attr(creator, 'full_name', "N/A") if creator else "N/A"
            self.created_by_label.setText(creator_full_name)

            # Informations de soumission
            if status in [OrderStatus.SUBMITTED, OrderStatus.APPROVED, OrderStatus.ORDERED,
                          OrderStatus.PARTIALLY_RECEIVED, OrderStatus.COMPLETED]:
                submitter = self._safe_get_attr(order, 'submitter', None)
                submitter_full_name = self._safe_get_attr(submitter, 'full_name', "N/A") if submitter else "N/A"
                self.submitted_by_label.setText(submitter_full_name)

                submitted_at = self._safe_get_attr(order, 'submitted_at', None)
                self.submitted_at_label.setText(
                    submitted_at.strftime("%d/%m/%Y %H:%M") if submitted_at else "N/A"
                )
            else:
                self.submitted_by_label.setText("Non soumis")
                self.submitted_at_label.setText("N/A")

            # Informations d'approbation
            if status in [OrderStatus.APPROVED, OrderStatus.ORDERED,
                          OrderStatus.PARTIALLY_RECEIVED, OrderStatus.COMPLETED]:
                approver = self._safe_get_attr(order, 'approver', None)
                approver_full_name = self._safe_get_attr(approver, 'full_name', "N/A") if approver else "N/A"
                self.approved_by_label.setText(approver_full_name)

                approved_at = self._safe_get_attr(order, 'approved_at', None)
                self.approved_at_label.setText(
                    approved_at.strftime("%d/%m/%Y %H:%M") if approved_at else "N/A"
                )
            else:
                self.approved_by_label.setText("Non approuvé")
                self.approved_at_label.setText("N/A")

            # Notes
            notes = self._safe_get_attr(order, 'notes', "")
            self.notes_label.setText(notes or "Aucune note")

            # Mettre à jour les boutons d'action selon le statut
            self._update_action_buttons()

        except Exception as e:
            print(f"Erreur lors de l'affichage des détails de la commande: {e}")
            self._clear_details()

    def _clear_details(self):
        """Efface les détails affichés"""
        self.po_number_label.setText("")
        self.supplier_label.setText("")
        self.status_label.setText("")
        self.order_date_label.setText("")
        self.expected_delivery_label.setText("")
        self.total_amount_label.setText("")
        self.created_by_label.setText("")
        self.submitted_by_label.setText("")
        self.submitted_at_label.setText("")
        self.approved_by_label.setText("")
        self.approved_at_label.setText("")
        self.notes_label.setText("")

        # Désactiver tous les boutons
        self.submit_button.setEnabled(False)
        self.approve_button.setEnabled(False)
        self.cancel_button.setEnabled(False)
        self.receive_button.setEnabled(False)

    def _update_action_buttons(self):
        """Met à jour l'état des boutons d'action selon le statut de la commande"""
        if not self.order:
            return

        # Soumettre : uniquement pour les commandes en brouillon
        self.submit_button.setEnabled(self.order.status == OrderStatus.DRAFT)

        # Approuver : uniquement pour les commandes soumises
        self.approve_button.setEnabled(self.order.status == OrderStatus.SUBMITTED)

        # Annuler : pour toutes les commandes sauf celles déjà annulées ou terminées
        self.cancel_button.setEnabled(
            self.order.status not in [OrderStatus.CANCELLED, OrderStatus.COMPLETED]
        )

        # Réceptionner : uniquement pour les commandes approuvées, commandées ou partiellement reçues
        self.receive_button.setEnabled(
            self.order.status in [
                OrderStatus.APPROVED,
                OrderStatus.ORDERED,
                OrderStatus.PARTIALLY_RECEIVED
            ]
        )

    def _get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            OrderStatus.DRAFT: "Brouillon",
            OrderStatus.PENDING: "En attente",
            OrderStatus.SUBMITTED: "Soumis",
            OrderStatus.APPROVED: "Approuvé",
            OrderStatus.ORDERED: "Commandé",
            OrderStatus.PARTIALLY_RECEIVED: "Partiellement reçu",
            OrderStatus.COMPLETED: "Terminé",
            OrderStatus.CANCELLED: "Annulé",
        }
        return status_display.get(status, str(status))

    def _on_submit_clicked(self):
        """Gère le clic sur le bouton Soumettre"""
        if self.order:
            # Demander confirmation avant de soumettre
            from PyQt6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "Confirmation",
                "Êtes-vous sûr de vouloir soumettre cette commande pour approbation ?\n\n"
                "Une fois soumise, la commande ne pourra plus être modifiée.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.orderSubmitted.emit(self.order.id)

    def _on_approve_clicked(self):
        """Gère le clic sur le bouton Approuver"""
        if self.order:
            # Demander confirmation avant d'approuver
            from PyQt6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "Confirmation",
                "Êtes-vous sûr de vouloir approuver cette commande ?\n\n"
                "Une fois approuvée, la commande pourra être envoyée au fournisseur.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.orderApproved.emit(self.order.id)

    def _on_cancel_clicked(self):
        """Gère le clic sur le bouton Annuler"""
        if self.order:
            # Demander confirmation avant d'annuler
            from PyQt6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "Confirmation",
                "Êtes-vous sûr de vouloir annuler cette commande ?\n\n"
                "Cette action est irréversible.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.orderCancelled.emit(self.order.id)

    def _on_receive_clicked(self):
        """Gère le clic sur le bouton Réceptionner"""
        if self.order:
            # Demander confirmation avant de réceptionner
            from PyQt6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "Confirmation",
                "Êtes-vous sûr de vouloir réceptionner cette commande ?\n\n"
                "Cette action va mettre à jour le stock des articles reçus.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.orderReceived.emit(self.order.id)
