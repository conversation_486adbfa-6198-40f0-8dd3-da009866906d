from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView,
    QPushButton, QLabel, QFrame, QHeaderView
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon

from ..order_items_table_model import OrderItemsTableModel


class OrderItemsWidget(QWidget):
    """Widget affichant les articles d'une commande d'achat"""

    # Signaux
    itemSelected = pyqtSignal(object)  # Article sélectionné

    def __init__(self, parent=None):
        super().__init__(parent)
        self.items = []

        # Créer une session de base de données
        try:
            from app.utils.database import SessionLocal
            self.db = SessionLocal()
            print("OrderItemsWidget: Nouvelle session de base de données créée")
        except Exception as e:
            print(f"Erreur lors de la création de la session: {e}")
            self.db = None

        self.setup_ui()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            try:
                self.db.close()
                print("OrderItemsWidget: Session de base de données fermée")
            except Exception as e:
                print(f"Erreur lors de la fermeture de la session: {e}")

    def _safe_get_attr(self, obj, attr_name, default=None):
        """Récupère un attribut de manière sécurisée

        Args:
            obj: L'objet dont on veut récupérer l'attribut
            attr_name: Le nom de l'attribut
            default: La valeur par défaut si l'attribut n'existe pas ou si une erreur se produit

        Returns:
            La valeur de l'attribut ou la valeur par défaut
        """
        try:
            if hasattr(obj, attr_name):
                return getattr(obj, attr_name)
            return default
        except Exception as e:
            print(f"Erreur lors de la récupération de l'attribut {attr_name}: {e}")
            return default

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        self.title_label = QLabel("Articles de la commande")
        self.title_label.setObjectName("sectionSubHeader")
        main_layout.addWidget(self.title_label)

        # Tableau des articles
        self.table_view = QTableView()
        self.table_view.setObjectName("itemsTable")
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.setAlternatingRowColors(True)

        # Modèle de données
        self.table_model = OrderItemsTableModel()
        self.table_view.setModel(self.table_model)

        # Configuration du tableau pour une meilleure présentation
        header = self.table_view.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Désactiver l'édition directe dans le tableau
        self.table_view.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.table_view.setShowGrid(True)
        self.table_view.setSortingEnabled(True)

        main_layout.addWidget(self.table_view)

        # Résumé
        summary_frame = QFrame()
        summary_frame.setObjectName("summaryFrame")
        summary_layout = QHBoxLayout(summary_frame)

        self.total_items_label = QLabel("Total articles: 0")
        summary_layout.addWidget(self.total_items_label)

        summary_layout.addStretch()

        self.total_amount_label = QLabel("Montant total: 0.00 DA")
        summary_layout.addWidget(self.total_amount_label)

        main_layout.addWidget(summary_frame)

        # Connexions
        self.table_view.selectionModel().selectionChanged.connect(self._on_selection_changed)

    def set_items(self, items):
        """Définit les articles à afficher"""
        try:
            # Vérifier si les articles sont valides
            if not items:
                self.items = []
                self.table_model.set_items([])
                self.table_view.resizeColumnsToContents()
                self._update_summary()
                return

            # Vérifier si les objets sont détachés et les rafraîchir si nécessaire
            refreshed_items = []
            for item in items:
                try:
                    # Vérifier si l'objet est détaché
                    if hasattr(self, 'db') and self.db and hasattr(item, 'id'):
                        try:
                            # Essayer d'accéder à un attribut pour voir si l'objet est détaché
                            _ = self._safe_get_attr(item, 'unit_price', None)
                            _ = self._safe_get_attr(item, 'quantity', None)
                            refreshed_items.append(item)
                        except Exception as e:
                            print(f"Objet détaché détecté: {e}")
                            # Pour l'instant, nous ne pouvons pas facilement rafraîchir les articles individuels
                            # Nous allons simplement utiliser les attributs de manière sécurisée
                            refreshed_items.append(item)
                    else:
                        refreshed_items.append(item)
                except Exception as e:
                    print(f"Erreur lors de la vérification de l'article: {e}")

            # Stocker les articles
            self.items = refreshed_items

            # Mettre à jour le modèle de table
            self.table_model.set_items(refreshed_items)
            self.table_view.resizeColumnsToContents()

            # Mettre à jour le résumé
            self._update_summary()

        except Exception as e:
            print(f"Erreur lors de l'affichage des articles: {e}")
            self.items = []
            self.table_model.set_items([])
            self.table_view.resizeColumnsToContents()
            self._update_summary()

    def _update_summary(self):
        """Met à jour le résumé des articles"""
        try:
            total_items = len(self.items)
            self.total_items_label.setText(f"Total articles: {total_items}")

            # Calculer le montant total de manière sécurisée
            total_amount = 0.0
            for item in self.items:
                try:
                    # Utiliser purchase_unit_price en priorité, unit_price comme fallback
                    purchase_unit_price = self._safe_get_attr(item, 'purchase_unit_price',
                                                            self._safe_get_attr(item, 'unit_price', 0.0))
                    quantity = self._safe_get_attr(item, 'quantity', 0.0)
                    total_amount += purchase_unit_price * quantity
                except Exception as e:
                    print(f"Erreur lors du calcul du montant pour un article: {e}")

            self.total_amount_label.setText(f"Montant total: {total_amount:.2f} DA")

        except Exception as e:
            print(f"Erreur lors de la mise à jour du résumé: {e}")
            self.total_items_label.setText("Total articles: 0")
            self.total_amount_label.setText("Montant total: 0.00 DA")

    def _on_selection_changed(self, selected, deselected=None):
        """Gère le changement de sélection dans le tableau"""
        try:
            indexes = selected.indexes()
            if indexes:
                row = indexes[0].row()
                item = self.table_model.get_item(row)
                if item:
                    # Vérifier si l'objet est détaché
                    if hasattr(self, 'db') and self.db and hasattr(item, 'id'):
                        try:
                            # Essayer d'accéder à un attribut pour voir si l'objet est détaché
                            _ = self._safe_get_attr(item, 'unit_price', None)
                            _ = self._safe_get_attr(item, 'quantity', None)
                        except Exception as e:
                            print(f"Objet détaché détecté lors de la sélection: {e}")
                            # Pour l'instant, nous ne pouvons pas facilement rafraîchir les articles individuels

                    self.itemSelected.emit(item)
        except Exception as e:
            print(f"Erreur lors du changement de sélection: {e}")
