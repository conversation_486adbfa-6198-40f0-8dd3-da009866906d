"""
Widget pour afficher les détails de paiement d'une commande d'achat
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QPushButton, QFrame, QGroupBox, QTableWidget,
    QTableWidgetItem, QHeaderView
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QColor
from datetime import datetime
import asyncio

from app.core.services.purchasing_service import PurchasingService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay


class PaymentDetailsWidget(QWidget):
    """Widget affichant les détails de paiement d'une commande d'achat"""

    # Signaux
    paymentRecorded = pyqtSignal(int)  # ID de la commande pour laquelle un paiement a été enregistré

    def __init__(self, parent=None):
        super().__init__(parent)
        self.order = None
        self.order_id = None
        self.financial_data = None

        # Services
        self.db = SessionLocal()
        self.purchasing_service = PurchasingService(self.db)

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        self.title_label = QLabel("Détails de paiement")
        self.title_label.setObjectName("sectionSubHeader")
        main_layout.addWidget(self.title_label)

        # Sections encadrées, chaque item en horizontal (label à gauche, valeur à droite)
        frame_style = "QFrame { border: none; background: transparent; }"
        title_style = "color: #555; font-weight: 600; font-size: 12px;"
        value_style = "color: #222; font-size: 14px; font-weight: 700;"

        def make_item(title, value_label_attr, word_wrap=False):
            w = QWidget()
            h = QHBoxLayout(w)
            h.setContentsMargins(6, 6, 6, 6)
            h.setSpacing(6)
            t = QLabel(title)
            t.setStyleSheet(title_style)
            t.setFixedWidth(140)
            t.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(t)
            val = QLabel("-")
            val.setStyleSheet(value_style)
            val.setWordWrap(word_wrap)
            val.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(val, 1)
            setattr(self, value_label_attr, val)
            return w

        # Frame 1 - Statut (colonne gauche)
        frame1 = QFrame()
        frame1.setStyleSheet(frame_style)
        f1 = QVBoxLayout(frame1)
        f1.setContentsMargins(8, 8, 8, 8)
        f1.setSpacing(8)
        f1.addWidget(make_item("Statut de paiement", "payment_status_label"))
        f1.addWidget(make_item("Date d'échéance", "payment_due_date_label"))

        # Frame 2 - Conditions (colonne centrale)
        frame2 = QFrame()
        frame2.setStyleSheet(frame_style)
        f2 = QVBoxLayout(frame2)
        f2.setContentsMargins(8, 8, 8, 8)
        f2.setSpacing(8)
        f2.addWidget(make_item("Conditions paiement", "payment_terms_label"))
        f2.addWidget(make_item("Total facturé", "total_invoiced_label"))

        # Frame 3 - Montants (colonne droite)
        frame3 = QFrame()
        frame3.setStyleSheet(frame_style)
        f3 = QVBoxLayout(frame3)
        f3.setContentsMargins(8, 8, 8, 8)
        f3.setSpacing(8)
        f3.addWidget(make_item("Total payé", "total_paid_label"))
        f3.addWidget(make_item("Solde dû", "balance_due_label"))

        # Disposer les 3 frames côte à côte
        row_layout = QHBoxLayout()
        row_layout.setSpacing(12)
        row_layout.addWidget(frame1, 1)
        row_layout.addWidget(frame2, 1)
        row_layout.addWidget(frame3, 1)
        main_layout.addLayout(row_layout)


        # Espacement
        main_layout.addSpacing(10)

        # Titre du tableau des paiements
        payments_title = QLabel("Paiements")
        payments_title.setObjectName("sectionSubHeader")
        payments_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #333; margin-bottom: 5px;")
        main_layout.addWidget(payments_title)

        # Tableau des paiements
        self.payments_table = QTableWidget(0, 4)
        self.payments_table.setHorizontalHeaderLabels(["Date", "Montant", "Méthode", "Référence"])

        # Configuration des colonnes avec largeurs optimisées
        header = self.payments_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Date
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Montant
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Méthode
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)           # Référence

        self.payments_table.setAlternatingRowColors(True)
        self.payments_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.payments_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.payments_table.setMinimumHeight(200)  # Hauteur minimum pour le tableau
        main_layout.addWidget(self.payments_table)

        # Espacement
        main_layout.addSpacing(10)

        # Titre des actions
        actions_title = QLabel("Actions")
        actions_title.setObjectName("sectionSubHeader")
        actions_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #333; margin-bottom: 5px;")
        main_layout.addWidget(actions_title)

        # Actions financières
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(10)
        actions_layout.setContentsMargins(0, 0, 0, 0)

        self.record_payment_button = QPushButton("Enregistrer un acompte")
        self.record_payment_button.setIcon(QIcon("app/ui/resources/icons/payment.svg"))
        self.record_payment_button.setObjectName("primaryButton")
        self.record_payment_button.clicked.connect(self._on_record_payment_clicked)
        actions_layout.addWidget(self.record_payment_button)

        self.view_history_button = QPushButton("Historique des paiements")
        self.view_history_button.setIcon(QIcon("app/ui/resources/icons/history.svg"))
        self.view_history_button.setObjectName("infoButton")
        self.view_history_button.clicked.connect(self._on_view_history_clicked)
        actions_layout.addWidget(self.view_history_button)

        # Ajouter un stretch pour pousser les boutons vers la gauche
        actions_layout.addStretch()

        main_layout.addLayout(actions_layout)

        # Espacement
        main_layout.addStretch()

    def set_order(self, order):
        """Définit la commande à afficher"""
        self.order = order
        self.order_id = order.id if order else None

        if not order:
            self._clear_details()
            return

        # Charger les données financières de manière asynchrone
        QTimer.singleShot(0, self._load_financial_data_wrapper)

    def _clear_details(self):
        """Efface les détails affichés"""
        self.payment_status_label.setText("Non payé")
        self.payment_terms_label.setText("N/A")
        self.payment_due_date_label.setText("N/A")
        self.total_invoiced_label.setText("0.00 DA")
        self.total_paid_label.setText("0.00 DA")
        self.balance_due_label.setText("0.00 DA")

        # Vider le tableau
        self.payments_table.setRowCount(0)

        # Désactiver les boutons
        self.record_payment_button.setEnabled(False)
        self.view_history_button.setEnabled(False)

    def _load_financial_data_wrapper(self):
        """Wrapper pour charger les données financières"""
        if self.order_id:
            self.loading_overlay.show()
            # Utiliser QTimer pour exécuter de manière asynchrone
            QTimer.singleShot(0, self._load_financial_data_sync)

    def _load_financial_data_sync(self):
        """Charge les données financières de manière synchrone"""
        try:
            # Utiliser la version synchrone du service si disponible
            if hasattr(self.purchasing_service, 'update_payment_status_sync'):
                payment_status = self.purchasing_service.update_payment_status_sync(self.order_id)
            else:
                # Fallback : créer une nouvelle boucle d'événements
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    payment_status = loop.run_until_complete(
                        self.purchasing_service.update_payment_status(self.order_id)
                    )
                finally:
                    loop.close()

            self.financial_data = payment_status

            # Mettre à jour l'interface
            self._update_payment_status(payment_status)
            self._update_payments_table(payment_status.get("payments", []))

            # Mettre à jour les boutons d'action
            self._update_action_buttons()

        except Exception as e:
            print(f"Erreur lors du chargement des données financières: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            self.loading_overlay.hide()

    async def _load_financial_data_async(self):
        """Charge les données financières de manière asynchrone (pour usage futur)"""
        try:
            # Mettre à jour le statut de paiement
            payment_status = await self.purchasing_service.update_payment_status(self.order_id)
            self.financial_data = payment_status

            # Mettre à jour l'interface
            self._update_payment_status(payment_status)
            self._update_invoices_table(payment_status.get("invoices", []))
            self._update_payments_table(payment_status.get("payments", []))

            # Mettre à jour les boutons d'action
            self._update_action_buttons()

        except Exception as e:
            print(f"Erreur lors du chargement des données financières: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            self.loading_overlay.hide()

    def _update_payment_status(self, payment_status):
        """Met à jour les informations de statut de paiement"""
        if not payment_status:
            return

        # Statut de paiement
        status = payment_status.get("status", "Non payé")
        self.payment_status_label.setText(status)

        # Conditions de paiement
        terms = payment_status.get("payment_terms", "N/A")
        self.payment_terms_label.setText(terms)

        # Date d'échéance
        due_date = payment_status.get("due_date")
        if due_date:
            if isinstance(due_date, str):
                self.payment_due_date_label.setText(due_date)
            else:
                self.payment_due_date_label.setText(due_date.strftime("%d/%m/%Y"))
        else:
            self.payment_due_date_label.setText("N/A")

        # Montants
        self.total_invoiced_label.setText(f"{payment_status.get('total_invoiced', 0):.2f} DA")
        self.total_paid_label.setText(f"{payment_status.get('total_paid', 0):.2f} DA")
        self.balance_due_label.setText(f"{payment_status.get('balance_due', 0):.2f} DA")



    def _update_payments_table(self, payments):
        """Met à jour le tableau des paiements"""
        self.payments_table.setRowCount(len(payments))
        
        for row, payment in enumerate(payments):
            date_str = ""
            if payment.get("date"):
                if isinstance(payment["date"], str):
                    date_str = payment["date"]
                else:
                    date_str = payment["date"].strftime("%d/%m/%Y")
            self.payments_table.setItem(row, 0, QTableWidgetItem(date_str))
            
            self.payments_table.setItem(row, 1, QTableWidgetItem(f"{payment.get('amount', 0):.2f} DA"))
            self.payments_table.setItem(row, 2, QTableWidgetItem(str(payment.get("method", ""))))
            self.payments_table.setItem(row, 3, QTableWidgetItem(str(payment.get("reference", ""))))

    def _update_action_buttons(self):
        """Met à jour l'état des boutons d'action"""
        if not self.order:
            return

        # Activer les boutons selon le statut de la commande
        valid_statuses = ["ordered", "partially_received", "completed"]
        order_status = self.order.status.value if hasattr(self.order.status, 'value') else str(self.order.status)

        # Enregistrer un acompte : pour les commandes commandées, partiellement reçues ou terminées
        self.record_payment_button.setEnabled(order_status.lower() in valid_statuses)

        # Historique : toujours disponible si une commande est sélectionnée
        self.view_history_button.setEnabled(True)

    def _on_record_payment_clicked(self):
        """Gère le clic sur le bouton d'enregistrement de paiement"""
        if self.order:
            self.paymentRecorded.emit(self.order.id)



    def _on_view_history_clicked(self):
        """Gère le clic sur le bouton d'historique des paiements"""
        # TODO: Implémenter l'affichage de l'historique des paiements
        pass
