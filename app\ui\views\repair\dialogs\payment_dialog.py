from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QComboBox, QDateEdit, QTextEdit,
    QPushButton, QFormLayout, QMessageBox, QCheckBox
)
from PyQt6.QtCore import QDate
from PyQt6.QtGui import QDoubleValidator
import asyncio

from app.core.models.repair import PaymentMethod, PaymentStatus
from app.core.services.finance_service import FinanceService


class PaymentDialog(QDialog):
    """Boîte de dialogue pour enregistrer un paiement"""

    def __init__(self, parent=None, repair_id=None):
        super().__init__(parent)
        self.repair_id = repair_id
        self.financial_summary = None
        self.setup_ui()
        self.setup_connections()

        # Charger les données
        if repair_id:
            self._load_data_wrapper()

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_data())
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def _process_payment_wrapper(self, payment_data):
        """Wrapper pour exécuter process_payment de manière asynchrone"""
        # Désactiver le bouton de sauvegarde pour éviter les doubles clics
        if hasattr(self, 'save_button'):
            self.save_button.setEnabled(False)

        # Utiliser QTimer pour exécuter la fonction de manière asynchrone
        from PyQt6.QtCore import QTimer

        def callback():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.process_payment(payment_data))
            except Exception as e:
                print(f"Erreur lors du traitement du paiement: {e}")
                import traceback
                traceback.print_exc()

                # Afficher l'erreur à l'utilisateur
                QMessageBox.critical(self, "Erreur",
                    f"Erreur lors de l'enregistrement du paiement:\n{str(e)}")
            finally:
                loop.close()
                # Réactiver le bouton
                if hasattr(self, 'save_button'):
                    self.save_button.setEnabled(True)

        # Exécuter le callback après un court délai
        QTimer.singleShot(0, callback)

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.setWindowTitle("Enregistrer un paiement")
        self.setMinimumWidth(400)

        main_layout = QVBoxLayout(self)

        # Formulaire
        form_layout = QFormLayout()

        # Montant
        self.amount_edit = QLineEdit()
        # Ne pas utiliser de validateur pour éviter les problèmes de saisie
        # Nous validerons manuellement lors de l'enregistrement
        form_layout.addRow("Montant:", self.amount_edit)

        # Méthode de paiement
        self.payment_method_combo = QComboBox()
        for method in PaymentMethod:
            self.payment_method_combo.addItem(self.get_payment_method_display(method), method.value)
        form_layout.addRow("Méthode de paiement:", self.payment_method_combo)

        # Date de paiement
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setCalendarPopup(True)
        self.payment_date_edit.setDate(QDate.currentDate())
        form_layout.addRow("Date de paiement:", self.payment_date_edit)

        # Numéro de référence
        self.reference_number_edit = QLineEdit()
        form_layout.addRow("Numéro de référence:", self.reference_number_edit)

        # Conditions de crédit (visible uniquement pour le paiement à crédit)
        self.credit_terms_edit = QLineEdit()
        self.credit_terms_edit.setValidator(QDoubleValidator(0, 365, 0))
        self.credit_terms_edit.setText("30")
        self.credit_terms_label = QLabel("Conditions de crédit (jours):")
        form_layout.addRow(self.credit_terms_label, self.credit_terms_edit)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)

        # Envoyer un reçu
        self.send_receipt_check = QCheckBox("Envoyer un reçu au client")
        self.send_receipt_check.setChecked(True)
        form_layout.addRow("", self.send_receipt_check)

        main_layout.addLayout(form_layout)

        # Résumé financier
        self.summary_label = QLabel("Chargement des informations financières...")
        main_layout.addWidget(self.summary_label)

        # Boutons
        buttons_layout = QHBoxLayout()

        self.cancel_button = QPushButton("Annuler")
        buttons_layout.addWidget(self.cancel_button)

        buttons_layout.addStretch()

        self.save_button = QPushButton("Enregistrer")
        self.save_button.setObjectName("primaryButton")
        buttons_layout.addWidget(self.save_button)

        main_layout.addLayout(buttons_layout)

        # Cacher les champs de crédit par défaut
        self.credit_terms_label.setVisible(False)
        self.credit_terms_edit.setVisible(False)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.cancel_button.clicked.connect(self.reject)
        self.save_button.clicked.connect(self.save_payment)
        self.payment_method_combo.currentIndexChanged.connect(self.on_payment_method_changed)
        # Ne pas connecter le signal textChanged pour permettre à l'utilisateur de modifier librement le montant

    def on_payment_method_changed(self, index):
        """Gère le changement de méthode de paiement"""
        method = self.payment_method_combo.currentData()

        # Afficher les champs de crédit uniquement pour le paiement à crédit
        is_credit = method == PaymentMethod.credit.value
        self.credit_terms_label.setVisible(is_credit)
        self.credit_terms_edit.setVisible(is_credit)

    def update_amount(self, text):
        """Met à jour le montant en fonction du solde dû"""
        # Désactiver cette méthode pour permettre à l'utilisateur de saisir librement
        # Nous validerons le montant lors de l'enregistrement
        pass

    async def load_data(self):
        """Charge les données financières de la réparation"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            service = RepairService(db)

            # Charger la réparation
            repair = await service.get(self.repair_id)
            if not repair:
                raise ValueError(f"Réparation avec ID {self.repair_id} non trouvée")

            # Créer un résumé financier à partir des données de la réparation
            from decimal import Decimal

            # Convertir en Decimal pour éviter les erreurs de type
            final_amount = Decimal(str(getattr(repair, 'final_amount', 0.0) or 0.0))
            total_paid = Decimal(str(getattr(repair, 'total_paid', 0.0) or 0.0))
            balance_due = final_amount - total_paid

            self.financial_summary = {
                'final_amount': float(final_amount),
                'total_paid': float(total_paid),
                'balance_due': float(balance_due)
            }

            # Mettre à jour l'interface
            self.update_summary()

            # Définir le montant par défaut au solde dû
            # Ne pas bloquer les signaux pour permettre à l'utilisateur de modifier le montant
            formatted_amount = f"{self.financial_summary['balance_due']:.2f}"
            self.amount_edit.setText(formatted_amount)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
            self.reject()

    def update_summary(self):
        """Met à jour l'affichage du résumé financier"""
        if not self.financial_summary:
            return

        summary_text = (
            f"<b>Montant total:</b> {self.financial_summary['final_amount']:.2f} DA<br>"
            f"<b>Déjà payé:</b> {self.financial_summary['total_paid']:.2f} DA<br>"
            f"<b>Solde dû:</b> {self.financial_summary['balance_due']:.2f} DA"
        )

        self.summary_label.setText(summary_text)

    def save_payment(self):
        """Enregistre le paiement"""
        # Valider les données
        try:
            # Récupérer le texte du montant
            amount_text = self.amount_edit.text().strip()

            # Vérifier si le montant est vide
            if not amount_text:
                QMessageBox.warning(self, "Avertissement", "Veuillez entrer un montant.")
                return

            # Remplacer la virgule par un point pour la conversion en float
            amount_text = amount_text.replace(',', '.')

            # Convertir en float
            try:
                amount = float(amount_text)
            except ValueError:
                QMessageBox.warning(self, "Avertissement", "Veuillez entrer un montant valide.")
                return

            # Vérifier si le montant est positif
            if amount <= 0:
                QMessageBox.warning(self, "Avertissement", "Le montant doit être supérieur à zéro.")
                return

            # Vérifier si le montant dépasse le solde dû
            if self.financial_summary and amount > self.financial_summary['balance_due']:
                QMessageBox.warning(self, "Avertissement", f"Le montant ne peut pas dépasser le solde dû ({self.financial_summary['balance_due']:.2f} DA).")
                return
        except Exception as e:
            print(f"Erreur lors de la validation du montant: {e}")
            QMessageBox.warning(self, "Avertissement", "Veuillez entrer un montant valide.")
            return

        # Récupérer les données
        payment_data = {
            'amount': amount,
            'payment_method': self.payment_method_combo.currentData(),
            'payment_date': self.payment_date_edit.date().toPyDate(),
            'reference_number': self.reference_number_edit.text().strip() or None,
            'notes': self.notes_edit.toPlainText().strip() or None,
            'send_receipt': self.send_receipt_check.isChecked(),
            'processed_by': 1  # TODO: Récupérer l'ID de l'utilisateur connecté depuis la session
        }

        # Ajouter les conditions de crédit si nécessaire
        if payment_data['payment_method'] == PaymentMethod.credit.value:
            try:
                payment_data['credit_terms'] = int(self.credit_terms_edit.text())
            except ValueError:
                QMessageBox.warning(self, "Avertissement", "Veuillez entrer un nombre de jours valide pour les conditions de crédit.")
                return

        # Enregistrer le paiement
        self._process_payment_wrapper(payment_data)

    async def process_payment(self, payment_data):
        """Traite l'enregistrement du paiement de manière asynchrone"""
        db = None
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal
            from app.utils.event_bus import event_bus

            db = SessionLocal()
            finance_service = FinanceService(db)

            # Utiliser FinanceService pour enregistrer le paiement avec cohérence trésorerie et références unifiées
            payment = await finance_service.pay_repair(
                repair_id=self.repair_id,
                amount=payment_data['amount'],
                method=payment_data['payment_method'],
                processed_by=payment_data.get('processed_by', 1),
                reference_number=payment_data.get('reference_number'),  # Si vide, sera généré automatiquement
                cash_register_id=payment_data.get('cash_register_id'),
                credit_terms=payment_data.get('credit_terms'),
                payment_date=payment_data.get('payment_date'),
                auto_generate_reference=True  # Génération automatique de référence unifiée
            )

            # Afficher un message de succès
            QMessageBox.information(self, "Succès",
                f"Le paiement de {payment_data['amount']:.2f} DA a été enregistré avec succès.\n"
                f"Statut de paiement: {repair.payment_status.value if repair.payment_status else 'Non défini'}")

            # Émettre un signal pour actualiser les vues
            event_bus.show_success(f"Paiement de {payment_data['amount']:.2f} DA enregistré")

            self.accept()

        except Exception as e:
            import traceback
            error_msg = f"Erreur lors de l'enregistrement du paiement: {str(e)}"
            print(error_msg)
            print("Traceback complet:")
            traceback.print_exc()

            QMessageBox.critical(self, "Erreur", error_msg)

        finally:
            if db:
                db.close()

    def get_payment_method_display(self, method):
        """Retourne l'affichage de la méthode de paiement (centralisé)"""
        from app.ui.utils.display_maps import payment_method_label
        return payment_method_label(method)