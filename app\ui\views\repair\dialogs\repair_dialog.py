from PyQt6.QtWidgets import (
    QDialog, QVBox<PERSON><PERSON>out, QHBox<PERSON>ayout, QFormLayout,
    QLineEdit, QTextEdit, QComboBox, QDateEdit,
    QCheckBox, QPushButton, QLabel, QDialogButtonBox,
    QMessageBox, QFileDialog, QScrollArea, QWidget,
    QGridLayout, QGroupBox
)
from PyQt6.QtCore import Qt, QDate, QTimer, QSize
from PyQt6.QtGui import QPixmap, QImage, QIcon
import asyncio
import os
import subprocess
from app.core.services.repair_service import RepairService
from app.core.services.customer_service import CustomerService
from app.core.services.user_service import UserService
from app.core.models.repair import (
    RepairStatus, RepairPriority, PaymentStatus,
    RepairOrderPydantic, UsedPartPydantic
)
from app.core.models.repair_photo import RepairPhotoPydantic, PhotoType
from app.core.models.repair_note import RepairNotePydantic, NoteType
from app.ui.components.custom_widgets import LoadingOverlay
from datetime import datetime
from app.core.services.repair_docs_service import RepairDocsService
from app.core.services.settings_service import SettingsService

# Imports pour l'auto-complétion intelligente
from app.ui.components.smart_autocomplete import BrandModelWidget
from app.core.services.brand_model_service import BrandModelService

# Reconstruire le modèle après avoir défini toutes les dépendances
RepairOrderPydantic.model_rebuild()

class RepairDialog(QDialog):
    def __init__(self, parent=None, repair_id=None):
        super().__init__(parent)
        self.repair_id = repair_id
        self.db = None
        self.service = None
        self.customer_service = None
        self.user_service = None
        self.docs_service = None
        self.brand_model_service = None  # Service d'auto-complétion
        # Variables liées aux notes supprimées
        
        try:
            self._initialize_services()
        except Exception as e:
            self._handle_error(e, "Erreur d'initialisation")
            raise

        # Initialiser l'attribut repair avec un objet vide
        from app.core.models.repair import RepairOrder, RepairStatus, RepairPriority
        self.repair = RepairOrder(
            id=None,
            number="",
            brand="",
            model="",
            customer_name="",
            status=RepairStatus.PENDING,
            priority=RepairPriority.NORMAL,
            description="",
            reported_issue="",
            total_cost=0.0,
            labor_cost=0.0,
            parts_cost=0.0,
            warranty=False
        )

        # Données
        self.customers = []
        self.technicians = []

        # S'assurer que la session est fermée lorsque le dialogue est fermé
        self.finished.connect(self.cleanup)

        self.setWindowTitle("Nouvelle Réparation" if repair_id is None else "Modifier Réparation")
        self.setMinimumSize(1100, 700)  # Fenêtre plus compacte

        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.init_data()

    def _get_status_display(self, status):
        """Retourne l'affichage du statut de réparation en français"""
        status_display = {
            RepairStatus.PENDING: "En attente",
            RepairStatus.DIAGNOSED: "Diagnostiqué",
            RepairStatus.IN_PROGRESS: "En cours",
            RepairStatus.WAITING_PARTS: "En attente de pièces",
            RepairStatus.WAITING_FOR_PARTS: "En attente de pièces",
            RepairStatus.WAITING_FOR_CUSTOMER: "En attente du client",
            RepairStatus.READY_FOR_PICKUP: "Prête pour récupération",
            RepairStatus.DELIVERED: "Livrée",
            RepairStatus.COMPLETED: "Terminé",
            RepairStatus.CANCELLED: "Annulé",
            RepairStatus.ON_HOLD: "En pause",
            RepairStatus.INVOICED: "Facturé",
            RepairStatus.PAID: "Payé",
        }
        return status_display.get(status, str(status))

    def _get_priority_display(self, priority):
        """Retourne l'affichage de la priorité en français"""
        priority_display = {
            RepairPriority.CRITICAL: "Critique",
            RepairPriority.HIGH: "Haute",
            RepairPriority.NORMAL: "Normale",
            RepairPriority.LOW: "Basse"
        }
        return priority_display.get(priority, str(priority))

    def _on_brand_model_changed(self, brand: str, model: str):
        """Appelé quand la marque ou le modèle change"""
        print(f"Marque/Modèle changé: {brand} - {model}")
        # Optionnel: validation ou autres actions

    def _on_learning_completed(self, brand: str, model: str):
        """Appelé quand l'apprentissage d'une nouvelle entrée est terminé"""
        print(f"Apprentissage terminé: {brand} - {model}")
        # Optionnel: afficher une notification à l'utilisateur

    def get_brand_model_data(self) -> dict:
        """Récupère les données de marque et modèle pour la sauvegarde"""
        brand, model = self.brand_model_widget.get_brand_model()
        return {
            'brand': brand,
            'model': model
        }

    def set_brand_model_data(self, brand: str, model: str):
        """Définit les données de marque et modèle (pour l'édition)"""
        self.brand_model_widget.set_brand_model(brand, model)

    def _initialize_services(self):
        """Initialise les services avec une nouvelle session de base de données"""
        from app.utils.database import SessionLocal
        print("Initialisation des services...")
        self.db = SessionLocal()
        self.service = RepairService(self.db)
        self.customer_service = CustomerService(self.db)
        self.user_service = UserService(self.db)
        self.brand_model_service = BrandModelService(self.db)
        self.docs_service = RepairDocsService(self.db)
        self.settings_service = SettingsService(self.db)
        print("Services initialisés avec succès")

    def setup_ui(self):
        """Configure l'interface utilisateur du dialogue"""
        # Configuration de la taille du dialogue
        self.setMinimumSize(800, 600)
        self.setMaximumSize(1000, 800)
        self.resize(900, 700)

        main_layout = QVBoxLayout(self)

        # Créer un widget de contenu avec scroll
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # Formulaire principal
        form_layout = QFormLayout()

        # Numéro de réparation
        self.number_edit = QLineEdit()
        form_layout.addRow("Numéro:", self.number_edit)

        # Client
        self.customer_combo = QComboBox()
        form_layout.addRow("Client:", self.customer_combo)

        # Widget intelligent pour marque et modèle
        self.brand_model_widget = BrandModelWidget(self.brand_model_service)
        self.brand_model_widget.brand_model_changed.connect(self._on_brand_model_changed)
        self.brand_model_widget.learning_completed.connect(self._on_learning_completed)

        # Ajouter le widget au layout
        brand_model_group = QGroupBox("Équipement")
        brand_model_layout = QVBoxLayout(brand_model_group)
        brand_model_layout.addWidget(self.brand_model_widget)
        form_layout.addRow(brand_model_group)

        # Numéro de série
        self.serial_number_edit = QLineEdit()
        form_layout.addRow("N° Série:", self.serial_number_edit)

        # Statut
        self.status_combo = QComboBox()
        for status in RepairStatus:
            self.status_combo.addItem(self._get_status_display(status), status)
        form_layout.addRow("Statut:", self.status_combo)

        # Priorité
        self.priority_combo = QComboBox()
        for priority in RepairPriority:
            self.priority_combo.addItem(self._get_priority_display(priority), priority)
        form_layout.addRow("Priorité:", self.priority_combo)

        # Description
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("Description de la réparation...")
        form_layout.addRow("Description:", self.description_edit)

        # Problème signalé
        self.issue_edit = QLineEdit()
        self.issue_edit.setPlaceholderText("Problème signalé par le client...")
        form_layout.addRow("Problème signalé:", self.issue_edit)

        # Date prévue
        self.scheduled_date = QDateEdit()
        self.scheduled_date.setCalendarPopup(True)
        self.scheduled_date.setDate(QDate.currentDate())
        form_layout.addRow("Date prévue:", self.scheduled_date)

        # Technicien
        self.technician_combo = QComboBox()
        form_layout.addRow("Technicien:", self.technician_combo)

        # Garantie
        self.warranty_check = QCheckBox()
        form_layout.addRow("Sous garantie:", self.warranty_check)

        # Prix total estimé/final
        self.estimated_cost_edit = QLineEdit()
        # Ne pas utiliser de validateur pour éviter les problèmes de saisie
        # Nous validerons manuellement lors de l'enregistrement
        self.estimated_cost_edit.setText("0.00")

        # Le libellé change selon qu'il s'agit d'une création ou d'une modification
        self.price_label = "Prix total estimé (DA):" if not self.repair_id else "Prix final (DA):"
        form_layout.addRow(self.price_label, self.estimated_cost_edit)

        # Ajouter le formulaire au layout de contenu
        content_layout.addLayout(form_layout)

        # Section des notes supprimée pour simplifier l'interface

        # Boutons d'impression (visibles uniquement en mode édition)
        if self.repair_id:
            print_layout = QHBoxLayout()

            # Bouton pour imprimer l'ordre de réparation
            self.print_order_button = QPushButton("Imprimer ordre de réparation")
            self.print_order_button.clicked.connect(self.print_repair_order)
            print_layout.addWidget(self.print_order_button)

            # Bouton pour imprimer la facture
            self.print_invoice_button = QPushButton("Imprimer facture")
            self.print_invoice_button.clicked.connect(self.print_repair_invoice)
            print_layout.addWidget(self.print_invoice_button)

            # Bouton pour imprimer le reçu
            self.print_receipt_button = QPushButton("Imprimer reçu")
            self.print_receipt_button.clicked.connect(self.print_repair_receipt)
            print_layout.addWidget(self.print_receipt_button)

            main_layout.addLayout(print_layout)

        # Finaliser le contenu et ajouter au scroll area
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

        # Boutons personnalisés (toujours visibles en bas)
        buttons_layout = QHBoxLayout()

        # Bouton Imprimer
        self.print_button = QPushButton("Imprimer")
        self.print_button.clicked.connect(self.print_repair_order)
        buttons_layout.addWidget(self.print_button)

        # Espaceur pour pousser les boutons Enregistrer et Annuler vers la droite
        buttons_layout.addStretch()

        # Bouton Enregistrer
        self.save_button = QPushButton("Enregistrer")
        self.save_button.clicked.connect(self.validate_and_accept)
        buttons_layout.addWidget(self.save_button)

        # Bouton Annuler
        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)

        main_layout.addLayout(buttons_layout)

        # Configurer l'ordre de tabulation maintenant que tous les widgets sont créés
        self._setup_tab_order()

    def setup_connections(self):
        """Configure les connexions des signaux"""
        # Les connexions sont maintenant configurées directement lors de la création des boutons
        pass

    def _setup_tab_order(self):
        """Configure l'ordre de tabulation pour une navigation fluide"""
        # Ordre de tabulation logique : client -> marque -> modèle -> série -> etc.
        self.setTabOrder(self.customer_combo, self.brand_model_widget.brand_edit)
        self.setTabOrder(self.brand_model_widget.brand_edit, self.brand_model_widget.model_edit)
        self.setTabOrder(self.brand_model_widget.model_edit, self.serial_number_edit)
        self.setTabOrder(self.serial_number_edit, self.status_combo)
        self.setTabOrder(self.status_combo, self.priority_combo)
        self.setTabOrder(self.priority_combo, self.description_edit)
        self.setTabOrder(self.description_edit, self.issue_edit)
        self.setTabOrder(self.issue_edit, self.scheduled_date)
        self.setTabOrder(self.scheduled_date, self.warranty_check)
        self.setTabOrder(self.warranty_check, self.estimated_cost_edit)
        self.setTabOrder(self.estimated_cost_edit, self.save_button)

    def init_data(self):
        """Initialise les données du dialogue"""
        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)

        # Charger les données de la réparation si on est en mode édition
        # Utiliser un délai plus long pour s'assurer que les clients et techniciens sont chargés d'abord
        if self.repair_id is not None:
            QTimer.singleShot(100, self.load_repair_data)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_data())
        except Exception as e:
            print(f"Erreur dans le wrapper de chargement: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def load_data(self):
        """Charge les données nécessaires"""
        self.loading_overlay.show()
        try:
            # Charger les clients
            print("Chargement des clients...")
            self.customers = await self.customer_service.get_active_customers()
            self.customer_combo.clear()
            for customer in self.customers:
                self.customer_combo.addItem(customer.name, customer.id)
            print(f"{len(self.customers)} clients chargés")

            # Charger les techniciens
            print("Chargement des techniciens...")
            self.technicians = await self.user_service.get_technicians()
            self.technician_combo.clear()

            # Ajouter une option vide
            self.technician_combo.addItem("-- Sélectionner un technicien --", None)

            if self.technicians:
                for technician in self.technicians:
                    # Utiliser le nom complet s'il existe, sinon l'email ou le nom d'utilisateur
                    display_name = technician.full_name if hasattr(technician, 'full_name') and technician.full_name else (
                        technician.email if hasattr(technician, 'email') and technician.email else (
                            technician.username if hasattr(technician, 'username') and technician.username else f"Utilisateur {technician.id}"
                        )
                    )
                    self.technician_combo.addItem(display_name, technician.id)
                print(f"{len(self.technicians)} techniciens chargés")
            else:
                print("Aucun technicien trouvé")
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.loading_overlay.hide()

    def load_repair_data(self):
        """Charge les données de la réparation pour l'édition"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._load_repair_async())
        except Exception as e:
            print(f"Erreur lors du chargement des données de la réparation: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def _load_repair_async(self):
        """Charge les données de manière asynchrone"""
        print("Début du chargement des données de la réparation")
        try:
            self.repair = await self.service.get(self.repair_id)
            if self.repair:
                print("Réparation trouvée, chargement des détails...")
                self.number_edit.setText(self.repair.number)

                # Sélectionner le client dans la combobox
                if self.repair.customer_id:
                    index = self.customer_combo.findData(self.repair.customer_id)
                    if index >= 0:
                        self.customer_combo.setCurrentIndex(index)
                    else:
                        print(f"Client avec ID {self.repair.customer_id} non trouvé dans la combobox")
                elif self.repair.customer_name:
                    index = self.customer_combo.findText(self.repair.customer_name)
                    if index >= 0:
                        self.customer_combo.setCurrentIndex(index)
                    else:
                        print(f"Client avec nom {self.repair.customer_name} non trouvé dans la combobox")

                # Désactiver le champ client en mode édition
                self.customer_combo.setEnabled(False)
                self.customer_combo.setToolTip("Le client ne peut pas être modifié après la création de la réparation")

                # Marque, modèle et numéro de série
                self.set_brand_model_data(self.repair.brand or "", self.repair.model or "")
                if self.repair.serial_number:
                    self.serial_number_edit.setText(self.repair.serial_number)

                # Statut
                index = self.status_combo.findData(self.repair.status)
                if index >= 0:
                    self.status_combo.setCurrentIndex(index)

                # Priorité
                index = self.priority_combo.findData(self.repair.priority)
                if index >= 0:
                    self.priority_combo.setCurrentIndex(index)

                self.description_edit.setText(self.repair.description or "")
                self.issue_edit.setText(self.repair.reported_issue or "")

                if self.repair.scheduled_date:
                    self.scheduled_date.setDate(QDate.fromString(
                        self.repair.scheduled_date.strftime("%Y-%m-%d"),
                        "yyyy-MM-dd"
                    ))

                # Sélectionner le technicien dans la combobox
                if self.repair.technician_id:
                    index = self.technician_combo.findData(self.repair.technician_id)
                    if index >= 0:
                        self.technician_combo.setCurrentIndex(index)
                    else:
                        print(f"Technicien avec ID {self.repair.technician_id} non trouvé dans la combobox")
                elif self.repair.technician_name:
                    # Parcourir tous les éléments de la combobox pour trouver une correspondance partielle
                    found = False
                    for i in range(self.technician_combo.count()):
                        if self.repair.technician_name in self.technician_combo.itemText(i):
                            self.technician_combo.setCurrentIndex(i)
                            found = True
                            break

                    if not found:
                        print(f"Technicien avec nom {self.repair.technician_name} non trouvé dans la combobox")
                        # Ajouter le technicien manuellement à la combobox
                        self.technician_combo.addItem(self.repair.technician_name, None)
                        self.technician_combo.setCurrentText(self.repair.technician_name)

                # Le technicien peut être modifié en mode édition

                self.warranty_check.setChecked(self.repair.warranty)

                # Charger le prix total estimé
                if hasattr(self.repair, 'total_cost') and self.repair.total_cost is not None:
                    self.estimated_cost_edit.setText(f"{self.repair.total_cost:.2f}")

                # Chargement des notes supprimé
                
                print("Chargement des données terminé")
            else:
                print("Réparation non trouvée")
        except Exception as e:
            print(f"Erreur lors du chargement des données de la réparation: {e}")
            import traceback
            traceback.print_exc()
            self._handle_error(e, "Erreur de chargement des données")
        finally:
            self.loading_overlay.hide()

    # Méthodes liées aux notes supprimées pour simplifier l'interface

    def _validate_amount(self, amount_str, field_name):
        """Valide un montant et retourne sa valeur"""
        try:
            amount = float(amount_str.replace(',', '.'))
            if amount < 0:
                raise ValueError(f"Le montant {field_name} ne peut pas être négatif")
            return round(amount, 2)
        except ValueError as e:
            QMessageBox.warning(self, "Validation", f"Montant {field_name} invalide: {str(e)}")
            return None

    def _validate_required_field(self, value, field_name):
        """Valide qu'un champ obligatoire est rempli"""
        if not value or not value.strip():
            QMessageBox.warning(self, "Validation", f"Le champ {field_name} est obligatoire")
            return False
        return True

    def validate_and_accept(self):
        """Valide les données et accepte le dialogue"""
        # Validation des champs obligatoires
        if not self._validate_required_field(self.customer_combo.currentText(), "client"):
            return
        brand_model_data = self.get_brand_model_data()
        if not self._validate_required_field(brand_model_data['brand'], "marque"):
            return
        if not self._validate_required_field(brand_model_data['model'], "modèle"):
            return
        if not self._validate_required_field(self.description_edit.text(), "description"):
            return

        # Validation du prix
        price = self._validate_amount(self.estimated_cost_edit.text(), "total")
        if price is None:
            return

        # Vérification du numéro de réparation
        number = self.number_edit.text().strip()
        if number and self.repair_id is None:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                repairs = loop.run_until_complete(
                    self.service.search_repairs(number=number)
                )
                if repairs:
                    QMessageBox.warning(
                        self,
                        "Validation",
                        f"Le numéro de réparation '{number}' existe déjà. Veuillez en choisir un autre."
                    )
                    return
            finally:
                loop.close()

        # Sauvegarder la réparation
        self._save_repair_wrapper()

    def _handle_error(self, error, title="Erreur"):
        """Gère les erreurs de manière uniforme"""
        from PyQt6.QtWidgets import QMessageBox
        error_message = str(error)
        print(f"Erreur: {error_message}")
        import traceback
        traceback.print_exc()
        QMessageBox.critical(self, title, f"Une erreur est survenue: {error_message}")

    def _save_repair_wrapper(self):
        """Wrapper pour exécuter _save_repair_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._save_repair_async())
            super().accept()  # Utiliser super().accept() au lieu de self.accept()
        except Exception as e:
            self._handle_error(e, "Erreur de sauvegarde")
        finally:
            loop.close()

    def cleanup(self):
        """Nettoie les ressources lors de la fermeture du dialogue"""
        if self.db:
            self.db.close()
            print("Session de base de données fermée")

    def _open_pdf(self, pdf_path):
        """Ouvre un fichier PDF avec l'application par défaut"""
        if not os.path.exists(pdf_path):
            self._handle_error(f"Le fichier PDF n'existe pas: {pdf_path}", "Erreur d'ouverture")
            return False

        if not pdf_path.lower().endswith('.pdf'):
            self._handle_error("Le fichier n'est pas un PDF valide", "Erreur d'ouverture")
            return False

        try:
            if os.name == 'nt':  # Windows
                os.startfile(pdf_path)
            elif os.name == 'posix':  # macOS et Linux
                subprocess.call(('xdg-open', pdf_path))
            else:
                self._handle_error(f"Système d'exploitation non supporté: {os.name}", "Erreur d'ouverture")
                return False
            return True
        except Exception as e:
            self._handle_error(f"Erreur lors de l'ouverture du PDF: {str(e)}", "Erreur d'ouverture")
            return False

    def print_repair_order(self):
        """Imprime l'ordre de réparation"""
        if not self.repair_id:
            QMessageBox.warning(self, "Impression", "Veuillez d'abord enregistrer la réparation.")
            return

        # S'assurer que le répertoire des PDF existe
        pdf_dir = self._ensure_pdf_directory()
        
        # Générer un nom de fichier unique
        filename = self._generate_pdf_filename("ordre_reparation")
        file_path = os.path.join(pdf_dir, filename)

        # Générer le PDF
        self._update_ui_state(True)
        QTimer.singleShot(0, lambda: self._print_repair_order_wrapper(file_path))

    def _print_repair_order_wrapper(self, file_path):
        """Wrapper pour exécuter _print_repair_order_async de manière asynchrone"""
        print(f"Début du wrapper d'impression avec le chemin: {file_path}")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            print("Exécution de la méthode asynchrone")
            loop.run_until_complete(self._print_repair_order_async(file_path))
            print("Méthode asynchrone terminée avec succès")
        except Exception as e:
            print(f"Erreur lors de l'impression: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'impression: {str(e)}")
        finally:
            loop.close()
            print("Boucle d'événements fermée")
            self.loading_overlay.hide()
            print("Overlay de chargement masqué")

    async def _print_repair_order_async(self, file_path):
        """Génère le PDF de l'ordre de réparation de manière asynchrone"""
        print(f"Début de la méthode asynchrone avec le chemin: {file_path}")

        # Générer le PDF
        print(f"Appel du service pour générer le PDF pour la réparation {self.repair_id}")
        pdf_path = await self.service.print_repair_order(self.repair_id, file_path)
        print(f"Résultat du service: {pdf_path}")

        if pdf_path:
            # Ouvrir le PDF avec le lecteur par défaut
            if os.path.exists(pdf_path):
                print(f"Le fichier PDF existe: {pdf_path}")
                if self._open_pdf(pdf_path):
                    print("Affichage du message de succès")
                    QMessageBox.information(
                        self,
                        "Impression",
                        f"L'ordre de réparation a été généré avec succès et enregistré à l'emplacement:\n{pdf_path}"
                    )
                else:
                    print("Échec de l'ouverture du PDF")
                    QMessageBox.warning(
                        self,
                        "Impression",
                        f"Le PDF a été généré mais n'a pas pu être ouvert automatiquement:\n{pdf_path}"
                    )
            else:
                print(f"Le fichier PDF n'existe pas: {pdf_path}")
                QMessageBox.warning(
                    self,
                    "Impression",
                    f"Le PDF a été généré mais le fichier n'a pas été trouvé à l'emplacement:\n{pdf_path}"
                )
        else:
            print("Aucun chemin de PDF retourné par le service")
            QMessageBox.critical(
                self,
                "Erreur",
                "Une erreur est survenue lors de la génération du PDF."
            )

    def print_repair_invoice(self):
        """Imprime la facture de réparation"""
        if not self.repair_id:
            QMessageBox.warning(self, "Impression", "Veuillez d'abord enregistrer la réparation.")
            return

        # Demander à l'utilisateur où enregistrer le fichier PDF
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Enregistrer la facture",
            os.path.join(os.getcwd(), f"facture_reparation_{self.repair_id}.pdf"),
            "Fichiers PDF (*.pdf)"
        )

        if not file_path:
            return

        # Générer le PDF
        self.loading_overlay.show()
        QTimer.singleShot(0, lambda: self._print_repair_invoice_wrapper(file_path))

    def _print_repair_invoice_wrapper(self, file_path):
        """Wrapper pour exécuter _print_repair_invoice_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_repair_invoice_async(file_path))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'impression: {str(e)}")
        finally:
            loop.close()
            self.loading_overlay.hide()

    async def _print_repair_invoice_async(self, file_path):
        """Génère le PDF de la facture de réparation de manière asynchrone"""
        print(f"Début de la méthode asynchrone avec le chemin: {file_path}")

        # Générer le PDF
        print(f"Appel du service pour générer le PDF pour la réparation {self.repair_id}")
        pdf_path = await self.service.print_repair_invoice(self.repair_id, file_path)
        print(f"Résultat du service: {pdf_path}")

        if pdf_path:
            # Ouvrir le PDF avec le lecteur par défaut
            if os.path.exists(pdf_path):
                print(f"Le fichier PDF existe: {pdf_path}")
                if self._open_pdf(pdf_path):
                    print("Affichage du message de succès")
                    QMessageBox.information(
                        self,
                        "Impression",
                        f"La facture a été générée avec succès et enregistrée à l'emplacement:\n{pdf_path}"
                    )
                else:
                    print("Échec de l'ouverture du PDF")
                    QMessageBox.warning(
                        self,
                        "Impression",
                        f"Le PDF a été généré mais n'a pas pu être ouvert automatiquement:\n{pdf_path}"
                    )
            else:
                print(f"Le fichier PDF n'existe pas: {pdf_path}")
                QMessageBox.warning(
                    self,
                    "Impression",
                    f"Le PDF a été généré mais le fichier n'a pas été trouvé à l'emplacement:\n{pdf_path}"
                )
        else:
            print("Aucun chemin de PDF retourné par le service")
            QMessageBox.critical(
                self,
                "Erreur",
                "Une erreur est survenue lors de la génération du PDF."
            )

    def print_repair_receipt(self):
        """Imprime le reçu de paiement"""
        if not self.repair_id:
            QMessageBox.warning(self, "Impression", "Veuillez d'abord enregistrer la réparation.")
            return

        # Récupérer les paiements de la réparation
        self.loading_overlay.show()
        QTimer.singleShot(0, self._get_payments_wrapper)

    def _get_payments_wrapper(self):
        """Wrapper pour exécuter _get_payments_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            payments = loop.run_until_complete(self._get_payments_async())
            self.loading_overlay.hide()

            if not payments:
                QMessageBox.warning(
                    self,
                    "Impression",
                    "Aucun paiement n'a été trouvé pour cette réparation."
                )
                return

            # Si un seul paiement, l'utiliser directement
            if len(payments) == 1:
                payment_id = payments[0].id
                self._select_payment_file(payment_id)
            else:
                # Afficher une boîte de dialogue pour sélectionner le paiement
                from PyQt6.QtWidgets import QInputDialog
                payment_items = [
                    f"Paiement #{p.id} - {p.amount:.2f} DA - {p.payment_date.strftime('%d/%m/%Y')}"
                    for p in payments
                ]
                selected_item, ok = QInputDialog.getItem(
                    self,
                    "Sélectionner un paiement",
                    "Choisissez le paiement pour lequel générer un reçu:",
                    payment_items,
                    0,
                    False
                )

                if ok and selected_item:
                    # Extraire l'ID du paiement
                    payment_id = int(selected_item.split('#')[1].split(' ')[0])
                    self._select_payment_file(payment_id)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
            self.loading_overlay.hide()
        finally:
            loop.close()

    async def _get_payments_async(self):
        """Récupère les paiements de la réparation de manière asynchrone"""
        from app.core.models.repair import RepairPayment

        # Récupérer les paiements
        payments = self.db.query(RepairPayment).filter(
            RepairPayment.repair_order_id == self.repair_id
        ).all()

        return payments

    def _select_payment_file(self, payment_id):
        """Demande à l'utilisateur où enregistrer le fichier PDF du reçu"""
        # Demander à l'utilisateur où enregistrer le fichier PDF
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Enregistrer le reçu",
            os.path.join(os.getcwd(), f"recu_paiement_{self.repair_id}_{payment_id}.pdf"),
            "Fichiers PDF (*.pdf)"
        )

        if not file_path:
            return

        # Générer le PDF
        self.loading_overlay.show()
        QTimer.singleShot(0, lambda: self._print_repair_receipt_wrapper(payment_id, file_path))

    def _print_repair_receipt_wrapper(self, payment_id, file_path):
        """Wrapper pour exécuter _print_repair_receipt_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_repair_receipt_async(payment_id, file_path))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'impression: {str(e)}")
        finally:
            loop.close()
            self.loading_overlay.hide()

    async def _print_repair_receipt_async(self, payment_id, file_path):
        """Génère le PDF du reçu de paiement de manière asynchrone"""
        print(f"Début de la méthode asynchrone avec le chemin: {file_path}, paiement: {payment_id}")

        # Générer le PDF
        print(f"Appel du service pour générer le PDF pour la réparation {self.repair_id}, paiement {payment_id}")
        pdf_path = await self.service.print_repair_receipt(self.repair_id, payment_id, file_path)
        print(f"Résultat du service: {pdf_path}")

        if pdf_path:
            # Ouvrir le PDF avec le lecteur par défaut
            if os.path.exists(pdf_path):
                print(f"Le fichier PDF existe: {pdf_path}")
                if self._open_pdf(pdf_path):
                    print("Affichage du message de succès")
                    QMessageBox.information(
                        self,
                        "Impression",
                        f"Le reçu a été généré avec succès et enregistré à l'emplacement:\n{pdf_path}"
                    )
                else:
                    print("Échec de l'ouverture du PDF")
                    QMessageBox.warning(
                        self,
                        "Impression",
                        f"Le PDF a été généré mais n'a pas pu être ouvert automatiquement:\n{pdf_path}"
                    )
            else:
                print(f"Le fichier PDF n'existe pas: {pdf_path}")
                QMessageBox.warning(
                    self,
                    "Impression",
                    f"Le PDF a été généré mais le fichier n'a pas été trouvé à l'emplacement:\n{pdf_path}"
                )
        else:
            print("Aucun chemin de PDF retourné par le service")
            QMessageBox.critical(
                self,
                "Erreur",
                "Une erreur est survenue lors de la génération du PDF."
            )

    def _update_ui_state(self, is_loading=False):
        """Met à jour l'état de l'interface utilisateur"""
        self.loading_overlay.setVisible(is_loading)
        self.save_button.setEnabled(not is_loading)
        self.cancel_button.setEnabled(not is_loading)
        self.print_button.setEnabled(not is_loading)
        self.customer_combo.setEnabled(not is_loading)
        self.technician_combo.setEnabled(not is_loading)
        self.brand_model_widget.setEnabled(not is_loading)
        self.serial_number_edit.setEnabled(not is_loading)
        self.status_combo.setEnabled(not is_loading)
        self.priority_combo.setEnabled(not is_loading)
        self.description_edit.setEnabled(not is_loading)
        self.issue_edit.setEnabled(not is_loading)
        self.scheduled_date.setEnabled(not is_loading)
        self.warranty_check.setEnabled(not is_loading)
        self.estimated_cost_edit.setEnabled(not is_loading)

    def _validate_date(self, date, field_name):
        """Valide une date"""
        if not date:
            QMessageBox.warning(self, "Validation", f"La date {field_name} est obligatoire")
            return False
        if date < QDate.currentDate():
            QMessageBox.warning(self, "Validation", f"La date {field_name} ne peut pas être dans le passé")
            return False
        return True

    def _calculate_payment_details(self, total_price, parts_cost):
        """Calcule les détails du paiement"""
        # Récupérer le taux de TVA depuis les paramètres
        setting = self.settings_service.db.query(self.settings_service.model).filter(
            self.settings_service.model.key == "general.tax_rate"
        ).first()
        
        # Utiliser la valeur par défaut si le paramètre n'existe pas
        tax_rate_str = setting.value if setting else "19.0"
        tax_rate = float(tax_rate_str) / 100  # Convertir le pourcentage en décimal
        
        labor_cost = round(max(0.0, total_price - parts_cost), 2)
        tax_amount = round(total_price * tax_rate, 2)  # Utiliser le taux de TVA configuré
        final_amount = round(total_price + tax_amount, 2)
        
        return {
            "total_cost": round(total_price, 2),
            "labor_cost": labor_cost,
            "parts_cost": parts_cost,
            "tax_amount": tax_amount,
            "final_amount": final_amount
        }

    def _generate_pdf_filename(self, prefix, extension=".pdf"):
        """Génère un nom de fichier PDF unique"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{prefix}_{self.repair_id}_{timestamp}{extension}"

    def _ensure_pdf_directory(self):
        """S'assure que le répertoire des PDF existe"""
        pdf_dir = os.path.join(os.getcwd(), "pdfs")
        if not os.path.exists(pdf_dir):
            os.makedirs(pdf_dir)
        return pdf_dir

    async def _save_repair_async(self):
        from datetime import datetime  # Correction explicite pour éviter UnboundLocalError
        """Enregistre les données de manière asynchrone"""
        self._update_ui_state(True)
        try:
            # Récupérer et valider les données
            customer_id = self.customer_combo.currentData()
            technician_id = self.technician_combo.currentData()
            customer_name = self.customer_combo.currentText()
            technician_name = self.technician_combo.currentText()

            # Générer un numéro de réparation
            number = self.number_edit.text().strip()
            if not number:
                from datetime import datetime
                number = f"REP-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                self.number_edit.setText(number)

            # Valider la date planifiée
            scheduled_date = self.scheduled_date.date()
            if not self._validate_date(scheduled_date, "planifiée"):
                return

            # Convertir la date planifiée
            scheduled_date_str = scheduled_date.toString("yyyy-MM-dd")
            scheduled_date = datetime.strptime(scheduled_date_str, "%Y-%m-%d")

            # Valider et récupérer le prix total
            total_price = self._validate_amount(self.estimated_cost_edit.text(), "total")
            if total_price is None:
                return

            # Calculer les coûts
            parts_cost = 0.0
            if self.repair_id:
                existing_repair = await self.service.get(self.repair_id)
                if existing_repair:
                    parts_cost = round(getattr(existing_repair, 'parts_cost', 0.0) or 0.0, 2)

            # Calculer les détails du paiement
            payment_details = self._calculate_payment_details(total_price, parts_cost)

            # Préparer les données
            data = {
                "number": number,
                "customer_id": customer_id,
                "customer_name": customer_name,
                "technician_id": technician_id,
                "technician_name": technician_name,
                **self.get_brand_model_data(),
                "serial_number": self.serial_number_edit.text().strip(),
                "status": self.status_combo.currentData(),
                "priority": self.priority_combo.currentData(),
                "description": self.description_edit.text().strip(),
                "reported_issue": self.issue_edit.text().strip(),
                "scheduled_date": scheduled_date,
                "warranty": self.warranty_check.isChecked(),
                **payment_details,
                "payment_status": PaymentStatus.PENDING
            }

            # Créer ou mettre à jour la réparation
            if self.repair_id is None:
                repair_model = RepairOrderPydantic(**data)
                await self.service.create(repair_model)
                QMessageBox.information(self, "Succès", "La réparation a été créée avec succès")
            else:
                repair_model = RepairOrderPydantic(**data)
                await self.service.update(self.repair_id, repair_model)
                QMessageBox.information(self, "Succès", "La réparation a été mise à jour avec succès")

            # Apprentissage automatique des marques et modèles
            brand_model_data = self.get_brand_model_data()
            if brand_model_data['brand']:
                try:
                    self.brand_model_service.learn_from_input(
                        brand_model_data['brand'],
                        brand_model_data['model']
                    )
                    print(f"Apprentissage terminé: {brand_model_data['brand']} - {brand_model_data['model']}")
                except Exception as e:
                    print(f"Erreur lors de l'apprentissage: {e}")

        except Exception as e:
            self._handle_error(e, "Erreur de sauvegarde")
            raise
        finally:
            self._update_ui_state(False)

    # Toutes les méthodes liées aux notes ont été supprimées pour simplifier l'interface