from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QFormLayout, QSplitter
)
from PyQt6.QtCore import Qt
import asyncio
from decimal import Decimal
from app.ui.components.custom_widgets import SearchBar

class UsedPartsDialog(QDialog):
    """Boîte de dialogue pour ajouter des pièces utilisées dans une réparation"""

    def __init__(self, parent=None, repair_id=None):
        super().__init__(parent)
        self.repair_id = repair_id
        self.parts_inventory = []
        self.used_parts = []

        self.setup_ui()
        self.setup_connections()

        # Charger les données
        if repair_id:
            self._load_data_wrapper()

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                raise RuntimeError("Event loop is closed")
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        try:
            loop.run_until_complete(self.load_data())
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.setWindowTitle("Pièces utilisées")
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)

        main_layout = QVBoxLayout(self)

        # Formulaire d'ajout de pièce
        form_widget = QFormLayout()

        # Recherche temps réel
        self.search_input = SearchBar("Rechercher une pièce (SKU, nom)...")
        self.search_input.setMinimumWidth(250)
        form_widget.addRow("Recherche:", self.search_input)

        # Sélection de pièce (liste filtrable)
        self.part_combo = QComboBox()
        self.part_combo.setMinimumWidth(350)
        self.part_combo.setEditable(False)
        form_widget.addRow("Pièce:", self.part_combo)

        # Quantité
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(100)
        self.quantity_spin.setValue(1)
        form_widget.addRow("Quantité:", self.quantity_spin)

        # Prix d'achat
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setMinimum(0)
        self.unit_price_spin.setMaximum(999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSuffix(" DA")
        form_widget.addRow("Prix d'achat:", self.unit_price_spin)

        # Bouton d'ajout
        add_button_layout = QHBoxLayout()
        add_button_layout.addStretch()

        self.add_button = QPushButton("Ajouter")
        self.add_button.setObjectName("primaryButton")
        add_button_layout.addWidget(self.add_button)

        form_widget.addRow("", add_button_layout)

        main_layout.addLayout(form_widget)

        # Tableau des pièces utilisées
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(["Référence", "Désignation", "Quantité", "Prix unitaire", "Total"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.table.setAlternatingRowColors(True)

        main_layout.addWidget(self.table)

        # Total
        total_layout = QHBoxLayout()
        total_layout.addStretch()

        self.total_label = QLabel("Total: 0.00 DA")
        self.total_label.setObjectName("totalAmount")
        total_layout.addWidget(self.total_label)

        main_layout.addLayout(total_layout)

        # Boutons
        buttons_layout = QHBoxLayout()

        self.remove_button = QPushButton("Supprimer")
        self.remove_button.setEnabled(False)
        buttons_layout.addWidget(self.remove_button)

        buttons_layout.addStretch()

        self.cancel_button = QPushButton("Annuler")
        buttons_layout.addWidget(self.cancel_button)

        self.save_button = QPushButton("Enregistrer")
        self.save_button.setObjectName("primaryButton")
        buttons_layout.addWidget(self.save_button)

        main_layout.addLayout(buttons_layout)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.cancel_button.clicked.connect(self.reject)
        self.save_button.clicked.connect(self.save_used_parts)
        self.add_button.clicked.connect(self.add_part)
        self.remove_button.clicked.connect(self.remove_part)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.part_combo.currentIndexChanged.connect(self.on_part_selected)
        self.search_input.textChanged.connect(self.on_search_text_changed)

    def on_selection_changed(self):
        """Gère le changement de sélection dans le tableau"""
        self.remove_button.setEnabled(len(self.table.selectedItems()) > 0)

    def on_part_selected(self, index):
        """Gère la sélection d'une pièce dans le combo"""
        if index < 0:
            return
        # Récupérer l'ID de la pièce depuis le combo filtré
        part_id = self.part_combo.currentData()
        if part_id is None:
            return
        # Retrouver l'objet dans l'inventaire complet
        part = next((p for p in self.parts_inventory if getattr(p, 'id', None) == part_id), None)
        if not part:
            return
        # Mettre à jour le prix d'achat
        purchase_price = getattr(part, 'purchase_price', 0.0)
        self.unit_price_spin.setValue(purchase_price)

    async def load_data(self):
        """Charge les données des pièces et de la réparation"""
        try:
            # Charger l'inventaire des pièces
            await self.load_parts_inventory()

            # Charger les pièces déjà utilisées
            await self.load_used_parts()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
            self.reject()

    async def load_parts_inventory(self):
        """Charge l'inventaire des pièces"""
        from app.core.services.inventory_service import InventoryService
        from app.utils.database import SessionLocal

        db = SessionLocal()
        service = InventoryService(db)

        try:
            # Charger les pièces
            self.parts_inventory = await service.get_all()

            # Construire une liste d'affichage initiale (non filtrée)
            self._rebuild_parts_combo()

            # Sélectionner la première pièce
            if self.parts_inventory:
                self.on_part_selected(0)
        finally:
            db.close()

    def _rebuild_parts_combo(self, filter_text: str = ""):
        """Reconstruit le combo des pièces selon le filtre fourni."""
        text = (filter_text or "").strip().lower()
        self.part_combo.blockSignals(True)
        try:
            self.part_combo.clear()
            for part in self.parts_inventory:
                label = f"{getattr(part, 'sku', '')} - {getattr(part, 'name', '')} ({getattr(part, 'quantity', 0)} en stock)"
                # Filtrage par SKU ou nom
                if not text or text in getattr(part, 'sku', '').lower() or text in getattr(part, 'name', '').lower():
                    self.part_combo.addItem(label, getattr(part, 'id', None))
        finally:
            self.part_combo.blockSignals(False)

        # Si après filtrage il y a des éléments, sélectionner le premier par défaut
        if self.part_combo.count() > 0:
            self.part_combo.setCurrentIndex(0)
            self.on_part_selected(0)
        else:
            self.unit_price_spin.setValue(0.0)

    def on_search_text_changed(self, text: str):
        """Filtre la liste des pièces en temps réel selon le texte saisi."""
        self._rebuild_parts_combo(text)

    async def load_used_parts(self):
        """Charge les pièces déjà utilisées dans la réparation"""
        from app.core.services.repair_service import RepairService
        from app.utils.database import SessionLocal
        from app.core.models.repair import UsedPart

        db = SessionLocal()
        service = RepairService(db)

        try:
            # Charger la réparation
            repair = await service.get(self.repair_id)

            # Récupérer les pièces utilisées
            if repair:
                # Récupérer les pièces utilisées directement depuis la base de données
                self.used_parts = db.query(UsedPart).filter(UsedPart.repair_order_id == self.repair_id).all()
            else:
                self.used_parts = []

            # Mettre à jour le tableau
            self.update_table()
        finally:
            db.close()

    def update_table(self):
        """Met à jour le tableau des pièces utilisées"""
        self.table.setRowCount(0)

        total = 0

        for i, part in enumerate(self.used_parts):
            self.table.insertRow(i)

            # Récupérer les informations de la pièce depuis l'inventaire
            inventory_part = next((p for p in self.parts_inventory if p.id == part.part_id), None)

            # Référence (SKU)
            reference = getattr(inventory_part, 'sku', 'N/A') if inventory_part else 'N/A'
            self.table.setItem(i, 0, QTableWidgetItem(reference))

            # Désignation
            name = getattr(inventory_part, 'name', 'N/A') if inventory_part else 'N/A'
            self.table.setItem(i, 1, QTableWidgetItem(name))

            # Quantité
            quantity_item = QTableWidgetItem(str(part.quantity))
            quantity_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(i, 2, quantity_item)

            # Prix d'achat
            purchase_price = getattr(part, 'purchase_unit_price', 0)
            unit_price_item = QTableWidgetItem(f"{purchase_price:.2f} DA")
            unit_price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.table.setItem(i, 3, unit_price_item)

            # Total
            part_total = part.quantity * purchase_price
            total_item = QTableWidgetItem(f"{part_total:.2f} DA")
            total_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.table.setItem(i, 4, total_item)

            # Ajouter au total
            total += part_total

        # Mettre à jour le total
        self.total_label.setText(f"Total: {total:.2f} DA")

    def add_part(self):
        """Ajoute une pièce à la liste des pièces utilisées"""
        if self.part_combo.currentIndex() < 0:
            QMessageBox.warning(self, "Avertissement", "Veuillez sélectionner une pièce.")
            return

        # Récupérer les données
        part_id = self.part_combo.currentData()
        part = next((p for p in self.parts_inventory if p.id == part_id), None)

        if not part:
            QMessageBox.warning(self, "Avertissement", "Pièce non trouvée dans l'inventaire.")
            return

        quantity = self.quantity_spin.value()
        purchase_unit_price = self.unit_price_spin.value()

        # Vérifier la disponibilité
        if quantity > part.quantity:
            QMessageBox.warning(self, "Avertissement", f"Quantité insuffisante en stock. Disponible: {part.quantity}")
            return

        # Vérifier si la pièce est déjà dans la liste
        existing_part = next((p for p in self.used_parts if hasattr(p, 'part_id') and p.part_id == part_id), None)

        if existing_part:
            # Mettre à jour la quantité et le prix
            existing_part.quantity += quantity
            existing_part.purchase_unit_price = purchase_unit_price
            # Mettre à jour le prix total
            existing_part.total_price = existing_part.quantity * purchase_unit_price
        else:
            # Créer une nouvelle entrée
            from app.core.models.repair import UsedPart

            used_part = UsedPart(
                part_id=part_id,
                repair_order_id=self.repair_id,  # Utiliser repair_order_id au lieu de repair_id
                quantity=quantity,
                purchase_unit_price=purchase_unit_price,
                total_price=quantity * purchase_unit_price  # Calculer le prix total
            )

            self.used_parts.append(used_part)

        # Mettre à jour temporairement le stock dans l'interface utilisateur
        part.quantity -= quantity

        # Mettre à jour le combo pour refléter le nouveau stock
        # On régénère la liste en conservant le filtre courant
        self._rebuild_parts_combo(self.search_input.text())

        # Mettre à jour le tableau
        self.update_table()

    def remove_part(self):
        """Supprime une pièce de la liste des pièces utilisées"""
        selected_row = self.table.currentRow()

        if selected_row < 0:
            return

        # Récupérer la pièce avant de la supprimer
        part_to_remove = self.used_parts[selected_row]

        # Restaurer temporairement le stock dans l'interface utilisateur
        if hasattr(part_to_remove, 'part_id'):
            part = next((p for p in self.parts_inventory if p.id == part_to_remove.part_id), None)
            if part:
                # Restaurer la quantité
                part.quantity += part_to_remove.quantity

                # Mettre à jour le combo pour refléter le nouveau stock (reconstruire selon filtre)
                self._rebuild_parts_combo(self.search_input.text())

        # Supprimer la pièce
        del self.used_parts[selected_row]

        # Mettre à jour le tableau
        self.update_table()

    def save_used_parts(self):
        """Enregistre les pièces utilisées"""
        if not self.used_parts:
            QMessageBox.warning(self, "Avertissement", "Aucune pièce n'a été ajoutée.")
            return

        # Enregistrer les pièces
        self._process_used_parts_wrapper()

    def _process_used_parts_wrapper(self):
        """Wrapper pour exécuter process_used_parts de manière asynchrone"""
        # Utiliser QTimer pour exécuter la fonction de manière asynchrone
        from PyQt6.QtCore import QTimer

        def callback():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.process_used_parts())
            except Exception as e:
                print(f"Erreur lors du traitement des pièces utilisées: {e}")
                print(f"Type d'erreur: {type(e).__name__}")
                import traceback
                traceback.print_exc()
                # Afficher aussi l'erreur à l'utilisateur
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.critical(None, "Erreur", f"Erreur lors de la mise à jour des pièces utilisées: {str(e)}")
            finally:
                loop.close()

        # Exécuter le callback après un court délai
        QTimer.singleShot(0, callback)

    async def process_used_parts(self):
        """Traite l'enregistrement des pièces utilisées de manière asynchrone"""
        try:
            from app.core.services.repair_service import RepairService
            from app.core.services.inventory_service import InventoryService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            service = RepairService(db)
            inventory_service = InventoryService(db)

            try:
                # Enregistrer les pièces utilisées
                success = await service.update_used_parts(self.repair_id, self.used_parts)

                if success:
                    # Actualiser le stock dans l'inventaire
                    try:
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.info("Actualisation du stock dans l'inventaire depuis UsedPartsDialog")

                        # Utiliser la fonction update_inventory
                        from app.utils.inventory_updater import update_inventory
                        if update_inventory():
                            logger.info("Stock actualisé avec succès")
                        else:
                            logger.warning("Impossible d'actualiser le stock")

                            # Essayer une autre approche
                            logger.info("Tentative d'actualisation directe de la vue d'inventaire")

                            # Trouver la fenêtre principale
                            from PyQt6.QtWidgets import QApplication, QWidget
                            main_window = None
                            for widget in QApplication.topLevelWidgets():
                                if widget.objectName() == "MainWindow":
                                    main_window = widget
                                    break

                            if main_window:
                                # Trouver la vue d'inventaire
                                inventory_view = None
                                for child in main_window.findChildren(QWidget):
                                    if child.__class__.__name__ == "InventoryView":
                                        inventory_view = child
                                        break

                                # Si la vue d'inventaire est trouvée, actualiser les données
                                if inventory_view:
                                    logger.info("Vue d'inventaire trouvée, actualisation des données")
                                    # Actualiser les données de l'inventaire
                                    inventory_view._load_data_wrapper()
                                    logger.info("Données de l'inventaire actualisées avec succès")
                                else:
                                    logger.warning("Vue d'inventaire non trouvée")
                            else:
                                logger.warning("Fenêtre principale non trouvée")
                    except Exception as e:
                        logger.error(f"Erreur lors de l'actualisation du stock: {e}")
                        import traceback
                        traceback.print_exc()

                    # Notifier l'utilisateur via le thread principal
                    from PyQt6.QtCore import QTimer
                    def show_success():
                        QMessageBox.information(self, "Succès", "Les pièces utilisées ont été enregistrées avec succès et le stock a été mis à jour.")
                        self.accept()
                    QTimer.singleShot(0, show_success)
                else:
                    # Notifier l'erreur via le thread principal
                    from PyQt6.QtCore import QTimer
                    def show_warning():
                        QMessageBox.warning(self, "Avertissement", "Impossible d'enregistrer les pièces utilisées.")
                    QTimer.singleShot(0, show_warning)
            finally:
                db.close()

        except Exception as e:
            # Notifier l'erreur via le thread principal
            from PyQt6.QtCore import QTimer
            def show_error():
                QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement des pièces: {str(e)}")
            QTimer.singleShot(0, show_error)