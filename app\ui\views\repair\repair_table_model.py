from PyQt6.QtCore import Qt, QAbstractTableModel
from typing import List, Any
from app.core.services.repair_service import RepairService

class RepairTableModel(QAbstractTableModel):
    def __init__(self):
        super().__init__()
        # Créer une nouvelle session pour le modèle de table
        from app.utils.database import SessionLocal
        self.db = SessionLocal()
        self.service = RepairService(self.db)  # Utiliser la même session pour le service
        self.repairs = []
        self.headers = [
            "Numéro",
            "Client",
            "Téléphone",
            "Marque",
            "Modèle",
            "N° Série",
            "Statut",
            "Priorité",
            "Technicien",
            "Prix estimé",
            "Date prévue",
            "Date de création"
        ]

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("RepairTableModel: Session de base de données fermée")

    async def load_data(self):
        """Charge les données depuis le service"""
        self.beginResetModel()
        try:
            # Fermer l'ancienne session si elle existe
            if hasattr(self, 'db') and self.db:
                self.db.close()
                print("RepairTableModel: Ancienne session fermée")

            # Créer une nouvelle session
            from app.utils.database import SessionLocal
            self.db = SessionLocal()
            self.service = RepairService(self.db)

            # Charger les données avec la nouvelle session
            self.repairs = await self.service.get_all()

            # Charger les informations des clients pour chaque réparation
            from app.core.services.customer_service import CustomerService
            customer_service = CustomerService(self.db)

            for repair in self.repairs:
                if repair.customer_id:
                    try:
                        customer = await customer_service.get(repair.customer_id)
                        if customer:
                            # Ajouter le téléphone du client à la réparation
                            repair.customer_phone = customer.phone
                        else:
                            repair.customer_phone = "N/A"
                    except Exception as e:
                        print(f"Erreur lors du chargement du client {repair.customer_id}: {e}")
                        repair.customer_phone = "N/A"
                else:
                    repair.customer_phone = "N/A"

            print(f"Réparations chargées: {len(self.repairs)}")
        except Exception as e:
            print(f"Erreur lors du chargement des réparations: {e}")
            import traceback
            traceback.print_exc()
            self.repairs = []
        self.endResetModel()

    def rowCount(self, parent=None):
        return len(self.repairs)

    def columnCount(self, parent=None):
        return len(self.headers)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return None

        repair = self.repairs[index.row()]
        column = index.column()

        if role == Qt.ItemDataRole.DisplayRole:
            # Formatage des données selon la colonne
            if column == 0:
                return repair.number
            elif column == 1:
                return repair.customer_name
            elif column == 2:
                return getattr(repair, 'customer_phone', "N/A")
            elif column == 3:
                return repair.brand
            elif column == 4:
                return repair.model
            elif column == 5:
                return repair.serial_number if repair.serial_number else "N/A"
            elif column == 6:
                from app.ui.utils.display_maps import status_label
                return status_label(repair.status)
            elif column == 7:
                from app.ui.utils.display_maps import priority_label
                return priority_label(repair.priority)
            elif column == 8:
                return repair.technician_name if repair.technician_name else "Non assigné"
            elif column == 9:
                # Prix total estimé
                return f"{repair.total_cost:.2f} DA" if hasattr(repair, 'total_cost') and repair.total_cost is not None else "0.00 DA"
            elif column == 10:
                return repair.scheduled_date.strftime("%d/%m/%Y") if repair.scheduled_date else "Non planifiée"
            elif column == 11:
                return repair.created_at.strftime("%d/%m/%Y")

        elif role == Qt.ItemDataRole.UserRole:
            # Données brutes pour le filtrage
            # Retourner un dict consolidé sur la colonne 0 pour permettre des filtres multiples robustes
            if column == 0:
                return {
                    'number': repair.number,
                    'status': repair.status.value if hasattr(repair.status, 'value') else str(repair.status),
                    'priority': repair.priority.value if hasattr(repair.priority, 'value') else str(repair.priority),
                    'technician_id': getattr(repair, 'technician_id', None),
                    'payment_status': getattr(getattr(repair, 'payment_status', None), 'value', str(getattr(repair, 'payment_status', ''))),
                    'customer_phone': getattr(repair, 'customer_phone', ''),
                }
            elif column == 1:
                return repair.customer_name
            elif column == 2:
                return getattr(repair, 'customer_phone', "")
            elif column == 3:
                return repair.brand
            elif column == 4:
                return repair.model
            elif column == 5:
                return repair.serial_number
            elif column == 6:
                return repair.status.value if hasattr(repair.status, 'value') else str(repair.status)
            elif column == 7:
                return repair.priority.value if hasattr(repair.priority, 'value') else str(repair.priority)
            elif column == 8:
                return getattr(repair, 'technician_id', None)
            elif column == 9:
                return repair.total_cost if hasattr(repair, 'total_cost') else 0.0
            elif column == 10:
                return repair.scheduled_date
            elif column == 11:
                return repair.created_at

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            # Alignement des colonnes
            if index.column() in [9, 10, 11]:
                return Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter

        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            return self.headers[section]
        return None

    def get_repair_id(self, row: int) -> int:
        """Retourne l'ID de la réparation à la ligne spécifiée"""
        return self.repairs[row].id

    def get_repair_status(self, row: int) -> str:
        """Retourne le statut de la réparation à la ligne spécifiée"""
        repair = self.repairs[row]
        return repair.status.value if hasattr(repair.status, 'value') else str(repair.status)

    def get_payment_status(self, row: int) -> str:
        """Retourne le statut de paiement de la réparation à la ligne spécifiée"""
        repair = self.repairs[row]
        return repair.payment_status.value if hasattr(repair.payment_status, 'value') else str(repair.payment_status)

    async def refresh_repair(self, repair_id: int):
        """Rafraîchit une réparation spécifique dans le modèle"""
        try:
            # Trouver l'index de la réparation
            repair_row = None
            for i, repair in enumerate(self.repairs):
                if repair.id == repair_id:
                    repair_row = i
                    break

            if repair_row is not None:
                # Recharger la réparation depuis la base de données
                updated_repair = await self.service.get(repair_id)
                if updated_repair:
                    # Charger les informations du client
                    from app.core.services.customer_service import CustomerService
                    customer_service = CustomerService(self.db)

                    if updated_repair.customer_id:
                        try:
                            customer = await customer_service.get(updated_repair.customer_id)
                            if customer:
                                updated_repair.customer_phone = customer.phone
                            else:
                                updated_repair.customer_phone = "N/A"
                        except Exception as e:
                            print(f"Erreur lors du chargement du client {updated_repair.customer_id}: {e}")
                            updated_repair.customer_phone = "N/A"
                    else:
                        updated_repair.customer_phone = "N/A"

                    # Remplacer la réparation dans la liste
                    self.repairs[repair_row] = updated_repair

                    # Émettre le signal de changement de données pour cette ligne
                    top_left = self.index(repair_row, 0)
                    bottom_right = self.index(repair_row, self.columnCount() - 1)
                    self.dataChanged.emit(top_left, bottom_right)

                    print(f"Réparation {repair_id} rafraîchie avec succès")
                    return True

            print(f"Réparation {repair_id} non trouvée dans le modèle")
            return False

        except Exception as e:
            print(f"Erreur lors du rafraîchissement de la réparation {repair_id}: {e}")
            import traceback
            traceback.print_exc()
            return False
