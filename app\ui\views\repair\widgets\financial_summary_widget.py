from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QFrame, QGridLayout, QPushButton
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon
import asyncio

from app.core.models.repair import PaymentStatus


class FinancialSummaryWidget(QWidget):
    """Widget affichant le résumé financier d'une réparation"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

        # Données
        self.financial_data = None

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Cadre pour le résumé
        summary_frame = QFrame()
        summary_frame.setFrameShape(QFrame.Shape.StyledPanel)
        summary_frame.setObjectName("summaryFrame")

        summary_layout = QGridLayout(summary_frame)

        # Titre
        title = QLabel("Résumé financier")
        title.setObjectName("sectionSubtitle")
        summary_layout.addWidget(title, 0, 0, 1, 2)

        # Coûts
        self.setup_cost_section(summary_layout, 1)

        # Séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        summary_layout.addWidget(separator, 5, 0, 1, 2)

        # Paiements
        self.setup_payment_section(summary_layout, 6)

        main_layout.addWidget(summary_frame)
        main_layout.addStretch()

    def setup_cost_section(self, layout, row):
        """Configure la section des coûts"""
        # Nous gardons les attributs pour la compatibilité mais ne les affichons pas
        self.parts_cost_label = QLabel("0.00 DA")
        self.labor_cost_label = QLabel("0.00 DA")

        # Total avant taxes (prix total estimé)
        layout.addWidget(QLabel("Total avant taxes:"), row, 0)
        self.total_cost_label = QLabel("0.00 DA")
        self.total_cost_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.total_cost_label.setObjectName("totalAmount")
        layout.addWidget(self.total_cost_label, row, 1)

        # TVA
        layout.addWidget(QLabel("TVA:"), row + 1, 0)
        self.tax_amount_label = QLabel("0.00 DA")
        self.tax_amount_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout.addWidget(self.tax_amount_label, row + 1, 1)

        # Remise
        layout.addWidget(QLabel("Remise:"), row + 2, 0)
        self.discount_amount_label = QLabel("0.00 DA")
        self.discount_amount_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout.addWidget(self.discount_amount_label, row + 2, 1)

        # Montant final
        layout.addWidget(QLabel("Montant final:"), row + 3, 0)
        self.final_amount_label = QLabel("0.00 DA")
        self.final_amount_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.final_amount_label.setObjectName("finalAmount")
        layout.addWidget(self.final_amount_label, row + 3, 1)

    def setup_payment_section(self, layout, row):
        """Configure la section des paiements"""
        # Total payé
        layout.addWidget(QLabel("Total payé:"), row, 0)
        self.total_paid_label = QLabel("0.00 DA")
        self.total_paid_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout.addWidget(self.total_paid_label, row, 1)

        # Solde dû
        layout.addWidget(QLabel("Solde dû:"), row + 1, 0)
        self.balance_due_label = QLabel("0.00 DA")
        self.balance_due_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.balance_due_label.setObjectName("balanceDue")
        layout.addWidget(self.balance_due_label, row + 1, 1)

        # Statut de paiement
        layout.addWidget(QLabel("Statut de paiement:"), row + 2, 0)
        self.payment_status_label = QLabel("En attente")
        self.payment_status_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout.addWidget(self.payment_status_label, row + 2, 1)

        # Date d'échéance
        layout.addWidget(QLabel("Date d'échéance:"), row + 3, 0)
        self.due_date_label = QLabel("-")
        self.due_date_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout.addWidget(self.due_date_label, row + 3, 1)

    def set_data(self, data):
        """Définit les données financières à afficher"""
        if not data:
            self.clear()
            return

        self.financial_data = data

        # Mettre à jour les coûts
        self.parts_cost_label.setText(f"{data['parts_cost']:.2f} DA")
        self.labor_cost_label.setText(f"{data['labor_cost']:.2f} DA")
        self.total_cost_label.setText(f"{data['total_cost']:.2f} DA")
        self.tax_amount_label.setText(f"{data['tax_amount']:.2f} DA")
        self.discount_amount_label.setText(f"{data['discount_amount']:.2f} DA")
        self.final_amount_label.setText(f"{data['final_amount']:.2f} DA")

        # Mettre à jour les paiements
        self.total_paid_label.setText(f"{data['total_paid']:.2f} DA")
        self.balance_due_label.setText(f"{data['balance_due']:.2f} DA")

        # Statut de paiement
        status_display = self.get_payment_status_display(data['payment_status'])
        self.payment_status_label.setText(status_display)

        # Colorer le statut de paiement
        if data['payment_status'] == PaymentStatus.PAID:
            self.payment_status_label.setStyleSheet("color: green;")
        elif data['payment_status'] == PaymentStatus.PARTIAL:
            self.payment_status_label.setStyleSheet("color: orange;")
        elif data['payment_status'] == PaymentStatus.OVERDUE:
            self.payment_status_label.setStyleSheet("color: red;")
        else:
            self.payment_status_label.setStyleSheet("")

        # Date d'échéance
        if data.get('due_date'):
            self.due_date_label.setText(data['due_date'].strftime("%d/%m/%Y"))
        else:
            self.due_date_label.setText("-")

    def clear(self):
        """Efface les données affichées"""
        self.parts_cost_label.setText("0.00 DA")
        self.labor_cost_label.setText("0.00 DA")
        self.total_cost_label.setText("0.00 DA")
        self.tax_amount_label.setText("0.00 DA")
        self.discount_amount_label.setText("0.00 DA")
        self.final_amount_label.setText("0.00 DA")

        self.total_paid_label.setText("0.00 DA")
        self.balance_due_label.setText("0.00 DA")
        self.payment_status_label.setText("En attente")
        self.payment_status_label.setStyleSheet("")
        self.due_date_label.setText("-")

        self.financial_data = None

    def get_payment_status_display(self, status):
        """Retourne l'affichage du statut de paiement (centralisé)"""
        from app.ui.utils.display_maps import payment_status_label
        return payment_status_label(status)