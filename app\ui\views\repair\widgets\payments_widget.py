from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QTableView, QLabel,
    QHeaderView, QSplitter, QHBoxLayout, QComboBox, QLineEdit, QPushButton, QDoubleSpinBox, QMessageBox
)
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QTimer, pyqtSignal, QThread
from datetime import datetime
from types import SimpleNamespace
import asyncio

from app.core.models.repair import PaymentMethod
from app.controllers.payments_controller import PaymentsController


class PaymentWorkerThread(QThread):
    """Thread pour gérer les opérations de paiement asynchrones"""

    payment_success = pyqtSignal()
    payment_error = pyqtSignal(str)

    def __init__(self, controller, repair_id, payload):
        super().__init__()
        self.controller = controller
        self.repair_id = repair_id
        self.payload = payload

    def run(self):
        """Exécute l'opération de paiement dans un thread séparé"""
        try:
            # Créer un nouvel event loop pour ce thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # Enregistrer le paiement
                loop.run_until_complete(
                    self.controller.record_payment(self.repair_id, self.payload)
                )

                # Recharger les paiements
                loop.run_until_complete(
                    self.controller.load_payments(self.repair_id)
                )

                # Signaler le succès
                self.payment_success.emit()

            finally:
                loop.close()

        except Exception as e:
            self.payment_error.emit(str(e))


class PaymentLoaderThread(QThread):
    """Thread pour charger les paiements de manière asynchrone"""

    payments_loaded = pyqtSignal()
    load_error = pyqtSignal(str)

    def __init__(self, controller, repair_id):
        super().__init__()
        self.controller = controller
        self.repair_id = repair_id

    def run(self):
        """Charge les paiements dans un thread séparé"""
        try:
            # Créer un nouvel event loop pour ce thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # Charger les paiements
                loop.run_until_complete(
                    self.controller.load_payments(self.repair_id)
                )

                # Signaler le succès
                self.payments_loaded.emit()

            finally:
                loop.close()

        except Exception as e:
            self.load_error.emit(str(e))


class PaymentsTableModel(QAbstractTableModel):
    """Modèle de tableau pour les paiements"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.payments = []
        self.headers = ["Date", "Montant", "Méthode", "Référence", "Notes"]

    def rowCount(self, parent=QModelIndex()):
        return len(self.payments)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.payments):
            return None

        payment = self.payments[index.row()]

        if role == Qt.ItemDataRole.DisplayRole:
            col = index.column()
            if col == 0:  # Date
                return payment.payment_date.strftime("%d/%m/%Y")
            elif col == 1:  # Montant
                return f"{payment.amount:.2f} DA"
            elif col == 2:  # Méthode
                return self.get_payment_method_display(payment.payment_method)
            elif col == 3:  # Référence
                return payment.reference_number or ""
            elif col == 4:  # Notes
                return payment.notes or ""

        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            return self.headers[section]
        return None

    def set_payments(self, payments):
        self.beginResetModel()
        self.payments = payments
        self.endResetModel()

    def get_payment_method_display(self, method):
        """Retourne l'affichage de la méthode de paiement (centralisé)"""
        from app.ui.utils.display_maps import payment_method_label
        return payment_method_label(method)

    def _parse_date(self, value):
        """Supporte datetime direct, ou chaîne ISO."""
        if isinstance(value, datetime):
            return value
        if isinstance(value, str):
            try:
                # Pydantic .model_dump_json/isodates
                return datetime.fromisoformat(value.replace('Z', '+00:00'))
            except Exception:
                pass
        return datetime.now()


class PaymentsWidget(QWidget):
    """Widget affichant l'historique des paiements d'une réparation"""

    paymentRecorded = pyqtSignal(dict)  # {repair_id, total_paid, payment_status, status}

    def __init__(self, parent=None):
        super().__init__(parent)
        self._repair_id = None
        self._on_payment_recorded_callback = None
        self.controller = PaymentsController()
        self.controller.paymentsLoaded.connect(self._on_payments_loaded)
        self.controller.paymentRecorded.connect(self._on_payment_recorded)
        self.controller.errorOccurred.connect(self._on_error)
        # Charger les caisses vers la combo quand reçu
        self.controller.cashRegistersLoaded.connect(self._on_cash_registers_loaded)
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Titre
        title = QLabel("Historique des paiements")
        title.setObjectName("sectionSubtitle")
        main_layout.addWidget(title)

        # Tableau des paiements
        self.table_view = QTableView()
        self.table_view.setAlternatingRowColors(True)
        self.table_model = PaymentsTableModel()
        self.table_view.setModel(self.table_model)

        # Configuration du tableau
        self.table_view.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table_view.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Date
        self.table_view.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Montant
        self.table_view.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Méthode

        main_layout.addWidget(self.table_view)

        # Formulaire d'ajout de paiement
        form_layout = QHBoxLayout()

        self.amount_input = QDoubleSpinBox()
        self.amount_input.setPrefix("")
        self.amount_input.setSuffix(" DA")
        self.amount_input.setDecimals(2)
        self.amount_input.setMaximum(10_000_000)
        self.amount_input.setMinimum(0.0)
        self.amount_input.setValue(0.0)
        self.amount_input.setToolTip("Montant du paiement")

        self.method_input = QComboBox()
        self.method_input.addItem("Espèces", userData="cash")
        self.method_input.addItem("Carte de crédit", userData="credit_card")
        self.method_input.addItem("Virement bancaire", userData="bank_transfer")
        self.method_input.addItem("Chèque", userData="check")
        self.method_input.addItem("Crédit", userData="credit")

        # Sélecteur de caisse optionnel
        self.register_input = QComboBox()
        self.register_input.addItem("(Par défaut)", userData=None)
        self.register_input.setToolTip("Caisse à utiliser (optionnel)")
        # Charger la liste des caisses actives de manière synchrone
        self._load_cash_registers_sync()

        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("Référence (optionnel)")

        self.notes_input = QLineEdit()
        self.notes_input.setPlaceholderText("Notes (optionnel)")

        self.add_button = QPushButton("Enregistrer le paiement")
        self.add_button.clicked.connect(self._on_add_payment_clicked)

        form_layout.addWidget(QLabel("Montant:"))
        form_layout.addWidget(self.amount_input)
        form_layout.addWidget(QLabel("Méthode:"))
        form_layout.addWidget(self.method_input)

        # Label de la caisse (pour pouvoir masquer/afficher)
        self.register_label = QLabel("Caisse:")
        form_layout.addWidget(self.register_label)
        form_layout.addWidget(self.register_input)

        form_layout.addWidget(QLabel("Référence:"))
        form_layout.addWidget(self.reference_input)
        form_layout.addWidget(QLabel("Notes:"))
        form_layout.addWidget(self.notes_input)
        form_layout.addWidget(self.add_button)

        # Masquer la sélection de caisse pour le mode Crédit
        self.method_input.currentIndexChanged.connect(self._on_method_changed)
        self._on_method_changed(self.method_input.currentIndex())

        main_layout.addLayout(form_layout)

        # Message si aucun paiement
        self.no_payments_label = QLabel("Aucun paiement enregistré")
        self.no_payments_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.no_payments_label.setVisible(False)
        main_layout.addWidget(self.no_payments_label)

    def set_payments(self, payments):
        """Définit les paiements à afficher"""
        # Si on reçoit des dicts (depuis le controller), créer de petits objets simples
        normalized = []
        for p in payments:
            # p peut être un pydantic model ou un dict
            if hasattr(p, 'model_dump'):
                p = p.model_dump()
            normalized.append(SimpleNamespace(
                payment_date=self.table_model._parse_date(p.get('payment_date')),
                amount=float(p.get('amount', 0)),
                payment_method=p.get('payment_method'),
                reference_number=p.get('reference_number'),
                notes=p.get('notes'),
            ))
        self.table_model.set_payments(normalized)

        # Afficher un message si aucun paiement
        has_items = len(normalized) > 0
        self.table_view.setVisible(has_items)
        self.no_payments_label.setVisible(not has_items)

    def set_repair_id(self, repair_id: int):
        self._repair_id = repair_id
        # Charger les paiements de manière asynchrone avec un thread
        self._load_payments_thread(repair_id)

    def _load_payments_thread(self, repair_id: int):
        """Lance le chargement des paiements dans un thread séparé"""
        try:
            # Éviter de lancer plusieurs threads en même temps
            if hasattr(self, 'loader_thread') and self.loader_thread.isRunning():
                return

            # Créer et lancer le thread de chargement
            self.loader_thread = PaymentLoaderThread(self.controller, repair_id)
            # Le signal payments_loaded du thread ne transporte pas de données
            # Les données sont gérées par le signal paymentsLoaded du contrôleur
            self.loader_thread.load_error.connect(self._on_payments_load_error)
            self.loader_thread.start()

        except Exception as e:
            print(f"Erreur lors du lancement du thread de chargement: {e}")



    def _on_payments_load_error(self, error_message):
        """Gère les erreurs de chargement des paiements"""
        print(f"Erreur lors du chargement des paiements: {error_message}")

    def load_payments(self, repair_id: int):
        """Charge les paiements pour une réparation (interface publique)"""
        self._load_payments_thread(repair_id)

    def on_payment_recorded(self, callback):
        """Permet au parent d'enregistrer un callback appelé après enregistrement d'un paiement."""
        self._on_payment_recorded_callback = callback

    def _on_payments_loaded(self, payments: list):
        self.set_payments(payments)

    def _on_payment_recorded(self, payload: dict):
        """Relaye l'événement du contrôleur vers le parent et rafraîchit si besoin."""
        try:
            # Rafraîchir la liste si on a l'ID de la réparation
            if self._repair_id:
                try:
                    self._load_payments_thread(self._repair_id)
                except Exception:
                    pass
            # Émettre notre propre signal pour informer le parent
            self.paymentRecorded.emit(payload or {"repair_id": self._repair_id})
            if callable(self._on_payment_recorded_callback):
                self._on_payment_recorded_callback(payload)
        except Exception:
            pass

    def _on_error(self, message: str):
        # Option: afficher un toast ou status bar, pour l'instant juste print
        print(f"PaymentsWidget error: {message}")

    def _on_method_changed(self, _index: int):
        """Masque la sélection de caisse si la méthode est 'Crédit'."""
        method_value = self.method_input.currentData()
        is_credit = (method_value == "credit")
        self.register_label.setVisible(not is_credit)
        self.register_input.setVisible(not is_credit)

    def _load_cash_registers_sync(self):
        """Charge la liste des caisses de manière synchrone"""
        try:
            # Charger les caisses directement depuis la base de données
            from app.utils.database import SessionLocal
            from app.core.models.treasury import CashRegister

            db = SessionLocal()
            try:
                # Récupérer les caisses actives
                registers = db.query(CashRegister).filter(
                    CashRegister.is_active == True
                ).order_by(CashRegister.name).all()

                # Ajouter les caisses au combo box
                for register in registers:
                    self.register_input.addItem(
                        f"{register.name} ({register.current_balance:.2f} DA)",
                        userData=register.id
                    )

            finally:
                db.close()

        except Exception as e:
            print(f"Erreur lors du chargement des caisses: {e}")
            # En cas d'erreur, laisser juste l'option par défaut

    def _on_cash_registers_loaded(self, registers: list):
        # Réinitialiser (garder l'entrée '(Par défaut)' à l'index 0)
        while self.register_input.count() > 1:
            self.register_input.removeItem(1)
        # Ajouter: "Nom (type)"
        for r in registers:
            name = r.get("name", "")
            rtype = r.get("type", "")
            label = f"{name} ({rtype})" if rtype else name
            self.register_input.addItem(label, userData=r.get("id"))



    def _on_add_payment_clicked(self):
        if not self._repair_id:
            QMessageBox.warning(self, "Paiement", "Aucune réparation sélectionnée.")
            return
        amount = float(self.amount_input.value())
        if amount <= 0:
            QMessageBox.warning(self, "Paiement", "Le montant doit être > 0.")
            return
        method_value = self.method_input.currentData()
        reference = self.reference_input.text().strip() or None
        notes = self.notes_input.text().strip() or None
        register_id = self.register_input.currentData() or None

        # Désactiver le bouton pour éviter les doubles clics
        self.add_button.setEnabled(False)
        self.add_button.setText("Enregistrement...")

        # Préparer le payload
        payload = {
            "amount": amount,
            "payment_method": method_value,
            "reference_number": reference,
            "notes": notes,
            "processed_by": 1,  # TODO: Récupérer l'ID de l'utilisateur connecté
        }
        if register_id is not None:
            payload["cash_register_id"] = register_id

        # Lancer l'enregistrement dans un thread séparé
        self.payment_thread = PaymentWorkerThread(self.controller, self._repair_id, payload)
        self.payment_thread.payment_success.connect(self._on_payment_success)
        self.payment_thread.payment_error.connect(self._on_payment_error)
        self.payment_thread.start()

    def _on_payment_success(self):
        """Gère le succès de l'enregistrement du paiement"""
        try:
            # Réactiver le bouton
            self.add_button.setEnabled(True)
            self.add_button.setText("Enregistrer le paiement")

            # Vider les champs
            self.amount_input.setValue(0.0)
            self.reference_input.clear()
            self.notes_input.clear()

            # Actualiser l'affichage des paiements
            self.load_payments(self._repair_id)

            # Afficher un message de succès
            QMessageBox.information(self, "Succès", "Paiement enregistré avec succès!")

            # Émettre un signal pour actualiser la vue parent
            from app.utils.event_bus import event_bus
            event_bus.show_success(f"Paiement de {self.amount_input.value():.2f} DA enregistré")

            # Propager l'événement au parent (UI principale)
            payload2 = {
                "repair_id": self._repair_id,
            }
            self.paymentRecorded.emit(payload2)
            if callable(self._on_payment_recorded_callback):
                try:
                    self._on_payment_recorded_callback(payload2)
                except Exception:
                    pass

        except Exception as e:
            print(f"Erreur lors de la gestion du succès: {e}")

    def _on_payment_error(self, error_message):
        """Gère les erreurs d'enregistrement du paiement"""
        try:
            # Réactiver le bouton
            self.add_button.setEnabled(True)
            self.add_button.setText("Enregistrer le paiement")

            # Afficher l'erreur
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement du paiement:\n{error_message}")

            print(f"Erreur de paiement: {error_message}")

        except Exception as e:
            print(f"Erreur lors de la gestion d'erreur: {e}")



    def clear(self):
        """Efface les paiements affichés"""
        self.table_model.set_payments([])
        self.table_view.setVisible(False)
        self.no_payments_label.setVisible(True)