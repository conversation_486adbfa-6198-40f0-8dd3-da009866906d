from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QGridLayout, QPushButton, QFrame
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime

class RepairDetailsWidget(QWidget):
    """Widget d'affichage des détails d'une réparation"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.repair = None
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Sections encadrées, chaque item en vertical (label au-dessus de la valeur)
        frame_style = "QFrame { border: none; background: transparent; }"
        title_style = "color: #555; font-weight: 600; font-size: 12px;"
        value_style = "color: #222; font-size: 14px; font-weight: 700;"

        def make_item(title, value_label_attr, word_wrap=False):
            w = QWidget()
            h = QHBoxLayout(w)
            h.setContentsMargins(6, 6, 6, 6)
            h.setSpacing(6)
            t = QLabel(title)
            t.setStyleSheet(title_style)
            t.setFixedWidth(120)
            t.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(t)
            val = QLabel("-")
            val.setStyleSheet(value_style)
            val.setWordWrap(word_wrap)
            val.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(val, 1)
            setattr(self, value_label_attr, val)
            return w

        # Frame 1 (colonne verticale)
        frame1 = QFrame()
        frame1.setStyleSheet(frame_style)
        f1 = QVBoxLayout(frame1)
        f1.setContentsMargins(8, 8, 8, 8)
        f1.setSpacing(8)
        f1.addWidget(make_item("Numéro", "number_label"))
        f1.addWidget(make_item("Client", "customer_label"))
        f1.addWidget(make_item("Téléphone", "customer_phone_label"))
        f1.addWidget(make_item("Date création", "creation_date_label"))
        f1.addWidget(make_item("Date prévue", "scheduled_date_label"))
        f1.addWidget(make_item("Date fin", "completion_date_label"))

        # Frame 2 (colonne verticale)
        frame2 = QFrame()
        frame2.setStyleSheet(frame_style)
        f2 = QVBoxLayout(frame2)
        f2.setContentsMargins(8, 8, 8, 8)
        f2.setSpacing(8)
        f2.addWidget(make_item("Équipement", "equipment_label", word_wrap=True))
        f2.addWidget(make_item("N° Série", "serial_label"))
        f2.addWidget(make_item("Priorité", "priority_label"))
        f2.addWidget(make_item("Statut", "status_label"))
        f2.addWidget(make_item("Technicien", "technician_label"))

        # Frame 3 (colonne verticale)
        frame3 = QFrame()
        frame3.setStyleSheet(frame_style)
        f3 = QVBoxLayout(frame3)
        f3.setContentsMargins(8, 8, 8, 8)
        f3.setSpacing(8)
        f3.addWidget(make_item("Problème signalé", "reported_issue_label", word_wrap=True))
        f3.addWidget(make_item("Description", "description_label", word_wrap=True))
        f3.addWidget(make_item("Sous garantie", "warranty_label"))
        f3.addWidget(make_item("Notes", "notes_label", word_wrap=True))

        # Disposer les 3 frames côte à côte
        row_layout = QHBoxLayout()
        row_layout.setSpacing(12)
        row_layout.addWidget(frame1, 1)
        row_layout.addWidget(frame2, 1)
        row_layout.addWidget(frame3, 1)
        main_layout.addLayout(row_layout)

        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)  # léger espacement entre les boutons
        button_style = (
            "QPushButton { background: #f5f7fa; color: #1976D2; border: 1px solid #e0e0e0; border-radius: 6px; padding: 8px 18px; font-weight: 600; } "
            "QPushButton:hover { background: #e3f0fc; color: #1565c0; border: 1px solid #90caf9; } "
            "QPushButton:pressed { background: #bbdefb; color: #0d47a1; } "
            "QPushButton:disabled { background: #f5f5f5; color: #bdbdbd; border: 1px solid #eeeeee; } "
        )
        self.edit_button = QPushButton("Modifier")
        self.edit_button.setIcon(QIcon("app/ui/resources/icons/edit.svg"))
        self.edit_button.setStyleSheet(button_style)
        self.edit_button.setToolTip("Modifier la réparation")
        self.edit_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.edit_button.setEnabled(False)  # désactivé par défaut tant qu'aucune réparation n'est chargée
        buttons_layout.addWidget(self.edit_button)
        self.print_button = QPushButton("Imprimer")
        self.print_button.setIcon(QIcon("app/ui/resources/icons/print.svg"))
        self.print_button.setStyleSheet(button_style)
        self.print_button.setToolTip("Imprimer l'ordre de réparation")
        self.print_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.print_button.setEnabled(False)  # désactivé par défaut tant qu'aucune réparation n'est chargée
        buttons_layout.addWidget(self.print_button)
        buttons_layout.addStretch()
        main_layout.addLayout(buttons_layout)

        # Connecter les signaux
        self.edit_button.clicked.connect(self.edit_repair)
        self.print_button.clicked.connect(self.print_repair)

    def set_repair(self, repair):
        """Définit la réparation à afficher"""
        self.repair = repair
        self.update_ui()

    def refresh_status(self):
        """Rafraîchit uniquement l'affichage du statut"""
        if self.repair:
            self.status_label.setText(self.get_status_display(self.repair.status))

    def update_ui(self):
        """Met à jour l'interface avec les données de la réparation"""
        if not self.repair:
            self.clear()
            self.edit_button.setEnabled(False)
            self.print_button.setEnabled(False)
            return

        # Informations de base
        self.number_label.setText(self.repair.number)
        self.status_label.setText(self.get_status_display(self.repair.status))
        self.priority_label.setText(self.get_priority_display(self.repair.priority))
        # N° série
        if hasattr(self.repair, 'serial_number') and self.repair.serial_number:
            self.serial_label.setText(self.repair.serial_number)
        else:
            self.serial_label.setText("-")

        # Dates
        self.creation_date_label.setText(self.format_date(getattr(self.repair, 'created_at', None)))
        self.scheduled_date_label.setText(self.format_date(self.repair.scheduled_date) if self.repair.scheduled_date else "-")
        self.completion_date_label.setText(self.format_date(self.repair.completion_date) if self.repair.completion_date else "-")

        # Client et équipement
        asyncio.create_task(self.load_customer_name())
        asyncio.create_task(self.load_equipment_name())

        # Problème et description
        self.reported_issue_label.setText(self.repair.reported_issue)
        self.description_label.setText(self.repair.description)

        # Technicien assigné
        if hasattr(self.repair, 'technician_id') and self.repair.technician_id:
            asyncio.create_task(self.load_technician_name())
        else:
            self.technician_label.setText("Non assigné")

        # Garantie
        self.warranty_label.setText("Oui" if self.repair.warranty else "Non")

        # Notes
        self.notes_label.setText(self.repair.notes if self.repair.notes else "-")
        # activer les actions quand une réparation est chargée
        self.edit_button.setEnabled(True)
        self.print_button.setEnabled(True)

    def clear(self):
        """Efface les données affichées"""
        self.number_label.setText("-")
        self.status_label.setText("-")
        self.priority_label.setText("-")
        self.creation_date_label.setText("-")
        self.scheduled_date_label.setText("-")
        self.completion_date_label.setText("-")
        self.customer_label.setText("-")
        self.customer_phone_label.setText("-")
        self.equipment_label.setText("-")
        self.serial_label.setText("-")
        self.reported_issue_label.setText("-")
        self.description_label.setText("-")
        self.technician_label.setText("-")
        self.warranty_label.setText("-")
        self.notes_label.setText("-")

    async def load_customer_name(self):
        """Charge le nom et le téléphone du client"""
        if not self.repair:
            return

        try:
            from app.core.services.customer_service import CustomerService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            service = CustomerService(db)

            customer = await service.get(self.repair.customer_id)
            if customer:
                self.customer_label.setText(customer.name)
                self.customer_phone_label.setText(customer.phone)

                # Stocker le téléphone dans l'objet réparation pour faciliter la recherche
                self.repair.customer_phone = customer.phone

        except Exception as e:
            print(f"Erreur lors du chargement du client: {e}")

    async def load_equipment_name(self):
        """Charge le nom de l'équipement"""
        if not self.repair:
            return

        try:
            # Directly use the equipment information from the repair object
            if hasattr(self.repair, 'brand') and hasattr(self.repair, 'model'):
                self.equipment_label.setText(f"{self.repair.brand} {self.repair.model}")
            elif hasattr(self.repair, 'equipment_id') and self.repair.equipment_id:
                from app.core.services.equipment_service import EquipmentService
                from app.utils.database import SessionLocal

                db = SessionLocal()
                service = EquipmentService(db)

                equipment = await service.get(self.repair.equipment_id)
                if equipment:
                    self.equipment_label.setText(f"{equipment.brand} {equipment.model}")

        except Exception as e:
            print(f"Erreur lors du chargement de l'équipement: {e}")

    async def load_technician_name(self):
        """Charge le nom du technicien"""
        if not self.repair:
            return

        try:
            technician_id = None
            if hasattr(self.repair, 'technician_id') and self.repair.technician_id:
                technician_id = self.repair.technician_id

            if technician_id:
                from app.core.services.user_service import UserService
                from app.utils.database import SessionLocal

                db = SessionLocal()
                service = UserService(db)

                user = await service.get(technician_id)
                if user:
                    self.technician_label.setText(user.full_name)

        except Exception as e:
            print(f"Erreur lors du chargement du technicien: {e}")

    def edit_repair(self):
        """Ouvre la boîte de dialogue d'édition de la réparation"""
        if not self.repair:
            return

        # Obtenir la fenêtre parente principale
        from app.ui.views.repair.repair_view import RepairView
        parent = self
        while parent and not isinstance(parent, RepairView):
            parent = parent.parent()

        if parent and isinstance(parent, RepairView):
            # Utiliser directement la boîte de dialogue de réparation
            from app.ui.views.repair.dialogs.repair_dialog import RepairDialog
            dialog = RepairDialog(parent, repair_id=self.repair.id)
            if dialog.exec():
                # Recharger les données après l'édition
                parent._load_data_wrapper()
                # Recharger les détails de la réparation
                parent._load_repair_details_wrapper(self.repair.id)

    def print_repair(self):
        """Imprime l'ordre de réparation"""
        if not self.repair:
            return

        # Obtenir la fenêtre parente principale
        from app.ui.views.repair.repair_view import RepairView
        parent = self
        while parent and not isinstance(parent, RepairView):
            parent = parent.parent()

        if parent and isinstance(parent, RepairView):
            # Appeler la méthode d'impression de la vue parente
            parent.print_repair_order(self.repair.id)

    def get_status_display(self, status):
        """Retourne l'affichage du statut (centralisé)"""
        from app.ui.utils.display_maps import status_label
        return status_label(status)

    def get_priority_display(self, priority):
        """Retourne l'affichage de la priorité (centralisé)"""
        from app.ui.utils.display_maps import priority_label
        return priority_label(priority)

    def format_date(self, date):
        """Formate une date pour l'affichage"""
        if not date:
            return "-"

        if isinstance(date, datetime):
            return date.strftime("%d/%m/%Y %H:%M")

        return str(date)