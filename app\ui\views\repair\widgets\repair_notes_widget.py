"""
Widget pour afficher et gérer les notes techniques d'une réparation.
"""
import asyncio
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QDialog, QComboBox, QLineEdit,
    QTextEdit, QCheckBox, QMessageBox, QSplitter, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon

from app.core.models.repair_note import NoteType
from app.core.services.repair_docs_service import RepairDocsService
from app.utils.database import SessionLocal
from ....components.custom_widgets import LoadingOverlay

class NoteViewDialog(QDialog):
    """Boîte de dialogue pour afficher une note technique"""

    def __init__(self, parent=None, title=None, content=None, note_type=None, is_private=False):
        super().__init__(parent)
        self.title = title
        self.content = content
        self.note_type = note_type
        self.is_private = is_private

        self.setWindowTitle("Visualisation de note")
        self.setMinimumSize(600, 400)

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)

        # En-tête
        header_layout = QHBoxLayout()

        # Titre
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
            header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Type de note
        if self.note_type:
            type_label = QLabel(self.get_note_type_display(self.note_type))
            type_label.setStyleSheet("color: gray;")
            header_layout.addWidget(type_label)

        # Indicateur de note privée
        if self.is_private:
            private_label = QLabel("Privée")
            private_label.setStyleSheet("color: red;")
            header_layout.addWidget(private_label)

        layout.addLayout(header_layout)

        # Contenu
        content_frame = QFrame()
        content_frame.setFrameShape(QFrame.Shape.StyledPanel)
        content_frame.setFrameShadow(QFrame.Shadow.Sunken)
        content_layout = QVBoxLayout(content_frame)

        content_text = QTextEdit()
        content_text.setReadOnly(True)
        content_text.setPlainText(self.content)
        content_layout.addWidget(content_text)

        layout.addWidget(content_frame)

        # Bouton de fermeture
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def get_note_type_display(self, note_type):
        """Retourne l'affichage du type de note"""
        type_display = {
            NoteType.DIAGNOSTIC: "Diagnostic",
            NoteType.REPAIR: "Réparation",
            NoteType.CUSTOMER: "Client",
            NoteType.INTERNAL: "Interne",
            NoteType.FOLLOW_UP: "Suivi",
            NoteType.OTHER: "Autre"
        }
        return type_display.get(note_type, str(note_type))

class RepairNotesWidget(QWidget):
    """Widget pour afficher et gérer les notes techniques d'une réparation"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.repair_id = None
        self.notes = []

        # Créer une session de base de données
        self.db = SessionLocal()
        self.service = RepairDocsService(self.db)

        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("RepairNotesWidget: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)

        # En-tête
        header_layout = QHBoxLayout()

        title_label = QLabel("Notes techniques")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        self.add_note_button = QPushButton("Ajouter une note")
        self.add_note_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_note_button.clicked.connect(self.add_note)
        header_layout.addWidget(self.add_note_button)

        layout.addLayout(header_layout)

        # Liste des notes
        self.notes_list = QListWidget()
        self.notes_list.itemDoubleClicked.connect(self.view_note)
        layout.addWidget(self.notes_list)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.view_button = QPushButton("Voir")
        self.view_button.setIcon(QIcon("app/ui/resources/icons/view.svg"))
        self.view_button.clicked.connect(self.view_selected_note)
        buttons_layout.addWidget(self.view_button)

        self.edit_button = QPushButton("Modifier")
        self.edit_button.setIcon(QIcon("app/ui/resources/icons/edit.svg"))
        self.edit_button.clicked.connect(self.edit_note)
        buttons_layout.addWidget(self.edit_button)

        self.delete_button = QPushButton("Supprimer")
        self.delete_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
        self.delete_button.clicked.connect(self.delete_note)
        buttons_layout.addWidget(self.delete_button)

        layout.addLayout(buttons_layout)

        # Désactiver les boutons par défaut
        self.view_button.setEnabled(False)
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)

        # Connecter le signal de sélection
        self.notes_list.itemSelectionChanged.connect(self.on_selection_changed)

    def set_repair_id(self, repair_id):
        """Définit l'ID de la réparation et charge les notes"""
        self.repair_id = repair_id
        if repair_id:
            asyncio.create_task(self.load_notes())
        else:
            self.clear()

    def clear(self):
        """Efface les données affichées"""
        self.notes = []
        self.notes_list.clear()

    def on_selection_changed(self):
        """Gère le changement de sélection dans la liste"""
        has_selection = len(self.notes_list.selectedItems()) > 0
        self.view_button.setEnabled(has_selection)
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)

    async def load_notes(self):
        """Charge les notes de la réparation"""
        if not self.repair_id:
            return

        self.loading_overlay.show()

        try:
            # Récupérer les notes
            self.notes = await self.service.get_notes(self.repair_id)

            # Mettre à jour l'affichage
            self.update_notes_list()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des notes: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def update_notes_list(self):
        """Met à jour la liste des notes"""
        self.notes_list.clear()

        for note in self.notes:
            item = QListWidgetItem()

            # Titre de l'élément
            title = note.title if note.title else f"Note #{note.id}"
            if note.is_private:
                title += " (Privée)"
            item.setText(title)

            # Données de l'élément
            item.setData(Qt.ItemDataRole.UserRole, note.id)

            # Icône selon le type de note
            icon_name = self.get_note_type_icon(note.note_type)
            item.setIcon(QIcon(f"app/ui/resources/icons/{icon_name}"))

            self.notes_list.addItem(item)

    def get_note_type_display(self, note_type):
        """Retourne l'affichage du type de note"""
        type_display = {
            NoteType.DIAGNOSTIC: "Diagnostic",
            NoteType.REPAIR: "Réparation",
            NoteType.CUSTOMER: "Client",
            NoteType.INTERNAL: "Interne",
            NoteType.FOLLOW_UP: "Suivi",
            NoteType.OTHER: "Autre"
        }
        return type_display.get(note_type, str(note_type))

    def get_note_type_icon(self, note_type):
        """Retourne l'icône correspondant au type de note"""
        icon_map = {
            NoteType.DIAGNOSTIC: "search.svg",
            NoteType.REPAIR: "repair.svg",
            NoteType.CUSTOMER: "customer.svg",
            NoteType.INTERNAL: "note.svg",
            NoteType.FOLLOW_UP: "calendar.svg",
            NoteType.OTHER: "info.svg"
        }
        return icon_map.get(note_type, "info.svg")

    def get_selected_note_id(self):
        """Récupère l'ID de la note sélectionnée"""
        selected_items = self.notes_list.selectedItems()
        if not selected_items:
            return None

        return selected_items[0].data(Qt.ItemDataRole.UserRole)

    def view_selected_note(self):
        """Affiche la note sélectionnée"""
        note_id = self.get_selected_note_id()
        if note_id:
            self.view_note(self.notes_list.currentItem())

    def view_note(self, item):
        """Affiche une note"""
        note_id = item.data(Qt.ItemDataRole.UserRole)

        # Trouver la note
        note = next((n for n in self.notes if n.id == note_id), None)
        if not note:
            return

        # Afficher la note
        dialog = NoteViewDialog(
            self,
            note.title,
            note.content,
            note.note_type,
            note.is_private
        )
        dialog.exec()

    def add_note(self):
        """Ajoute une nouvelle note"""
        if not self.repair_id:
            QMessageBox.warning(self, "Avertissement", "Aucune réparation sélectionnée.")
            return

        # Ouvrir une boîte de dialogue pour les détails de la note
        dialog = QDialog(self)
        dialog.setWindowTitle("Nouvelle note technique")
        dialog.setMinimumSize(600, 400)

        dialog_layout = QVBoxLayout(dialog)

        # Type de note
        type_layout = QHBoxLayout()
        type_label = QLabel("Type de note:")
        type_layout.addWidget(type_label)

        type_combo = QComboBox()
        for note_type in NoteType:
            type_combo.addItem(self.get_note_type_display(note_type), note_type)
        type_layout.addWidget(type_combo)

        dialog_layout.addLayout(type_layout)

        # Titre
        title_layout = QHBoxLayout()
        title_label = QLabel("Titre:")
        title_layout.addWidget(title_label)

        title_edit = QLineEdit()
        title_layout.addWidget(title_edit)

        dialog_layout.addLayout(title_layout)

        # Note privée
        private_check = QCheckBox("Note privée (non visible par le client)")
        dialog_layout.addWidget(private_check)

        # Contenu
        content_label = QLabel("Contenu:")
        dialog_layout.addWidget(content_label)

        content_edit = QTextEdit()
        dialog_layout.addWidget(content_edit)

        # Boutons
        buttons_layout = QHBoxLayout()

        cancel_button = QPushButton("Annuler")
        cancel_button.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_button)

        save_button = QPushButton("Enregistrer")
        save_button.clicked.connect(dialog.accept)
        buttons_layout.addWidget(save_button)

        dialog_layout.addLayout(buttons_layout)

        # Afficher la boîte de dialogue
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Récupérer les valeurs
            note_type = type_combo.currentData()
            title = title_edit.text()
            is_private = private_check.isChecked()
            content = content_edit.toPlainText()

            if not content:
                QMessageBox.warning(self, "Avertissement", "Le contenu de la note ne peut pas être vide.")
                return

            # Ajouter la note
            self.loading_overlay.show()
            asyncio.create_task(self._add_note_async(note_type, title, is_private, content))

    async def _add_note_async(self, note_type, title, is_private, content):
        """Ajoute une note de manière asynchrone"""
        try:
            # Ajouter la note
            await self.service.add_note(
                self.repair_id,
                content,
                note_type,
                title,
                is_private
            )

            # Recharger les notes
            await self.load_notes()

            QMessageBox.information(self, "Information", "La note a été ajoutée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de la note: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def edit_note(self):
        """Modifie une note"""
        note_id = self.get_selected_note_id()
        if not note_id:
            return

        # Trouver la note
        note = next((n for n in self.notes if n.id == note_id), None)
        if not note:
            return

        # Ouvrir une boîte de dialogue pour les détails de la note
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier la note technique")
        dialog.setMinimumSize(600, 400)

        dialog_layout = QVBoxLayout(dialog)

        # Type de note
        type_layout = QHBoxLayout()
        type_label = QLabel("Type de note:")
        type_layout.addWidget(type_label)

        type_combo = QComboBox()
        for note_type in NoteType:
            type_combo.addItem(self.get_note_type_display(note_type), note_type)
        type_combo.setCurrentText(self.get_note_type_display(note.note_type))
        type_layout.addWidget(type_combo)

        dialog_layout.addLayout(type_layout)

        # Titre
        title_layout = QHBoxLayout()
        title_label = QLabel("Titre:")
        title_layout.addWidget(title_label)

        title_edit = QLineEdit()
        title_edit.setText(note.title or "")
        title_layout.addWidget(title_edit)

        dialog_layout.addLayout(title_layout)

        # Note privée
        private_check = QCheckBox("Note privée (non visible par le client)")
        private_check.setChecked(note.is_private)
        dialog_layout.addWidget(private_check)

        # Contenu
        content_label = QLabel("Contenu:")
        dialog_layout.addWidget(content_label)

        content_edit = QTextEdit()
        content_edit.setPlainText(note.content)
        dialog_layout.addWidget(content_edit)

        # Boutons
        buttons_layout = QHBoxLayout()

        cancel_button = QPushButton("Annuler")
        cancel_button.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_button)

        save_button = QPushButton("Enregistrer")
        save_button.clicked.connect(dialog.accept)
        buttons_layout.addWidget(save_button)

        dialog_layout.addLayout(buttons_layout)

        # Afficher la boîte de dialogue
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Récupérer les valeurs
            note_type = type_combo.currentData()
            title = title_edit.text()
            is_private = private_check.isChecked()
            content = content_edit.toPlainText()

            if not content:
                QMessageBox.warning(self, "Avertissement", "Le contenu de la note ne peut pas être vide.")
                return

            # Mettre à jour la note
            self.loading_overlay.show()
            asyncio.create_task(self._update_note_async(note_id, note_type, title, is_private, content))

    async def _update_note_async(self, note_id, note_type, title, is_private, content):
        """Met à jour une note de manière asynchrone"""
        try:
            # Mettre à jour la note
            await self.service.update_note(
                note_id,
                {
                    "note_type": note_type,
                    "title": title,
                    "is_private": is_private,
                    "content": content
                }
            )

            # Recharger les notes
            await self.load_notes()

            QMessageBox.information(self, "Information", "La note a été mise à jour avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la mise à jour de la note: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def delete_note(self):
        """Supprime une note"""
        note_id = self.get_selected_note_id()
        if not note_id:
            return

        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir supprimer cette note ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Supprimer la note
            self.loading_overlay.show()
            asyncio.create_task(self._delete_note_async(note_id))

    async def _delete_note_async(self, note_id):
        """Supprime une note de manière asynchrone"""
        try:
            # Supprimer la note
            success = await self.service.delete_note(note_id)

            if success:
                # Recharger les notes
                await self.load_notes()

                QMessageBox.information(self, "Information", "La note a été supprimée avec succès.")
            else:
                QMessageBox.warning(self, "Avertissement", "Impossible de supprimer la note.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression de la note: {str(e)}")
        finally:
            self.loading_overlay.hide()
