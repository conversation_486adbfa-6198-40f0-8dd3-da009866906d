"""
Widget pour gérer les pièces détachées utilisées dans une réparation.
"""
import asyncio
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QDialog, QLineEdit, QSpinBox,
    QDoubleSpinBox, QComboBox, QMessageBox, QHeaderView, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon

from app.core.services.product_service import ProductService
from app.core.services.repair_service import RepairService
from app.utils.database import SessionLocal
from ....components.custom_widgets import LoadingOverlay, SearchLineEdit

class AddPartDialog(QDialog):
    """Boîte de dialogue pour ajouter une pièce à une réparation"""
    
    def __init__(self, parent=None, product_service=None):
        super().__init__(parent)
        self.product_service = product_service
        self.selected_product = None
        
        self.setWindowTitle("Ajouter une pièce")
        self.setMinimumSize(600, 400)
        
        self.setup_ui()
        
        # Charger les produits
        asyncio.create_task(self.load_products())
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        
        # Recherche de produit
        search_layout = QHBoxLayout()
        
        search_label = QLabel("Rechercher un produit:")
        search_layout.addWidget(search_label)
        
        self.search_edit = SearchLineEdit()
        self.search_edit.textChanged.connect(self.filter_products)
        search_layout.addWidget(self.search_edit)
        
        layout.addLayout(search_layout)
        
        # Table des produits
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(4)
        self.products_table.setHorizontalHeaderLabels(["ID", "Nom", "Référence", "Prix"])
        self.products_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.products_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.products_table.itemSelectionChanged.connect(self.on_product_selected)
        layout.addWidget(self.products_table)
        
        # Détails de la pièce
        details_layout = QVBoxLayout()
        
        # Quantité
        quantity_layout = QHBoxLayout()
        quantity_label = QLabel("Quantité:")
        quantity_layout.addWidget(quantity_label)
        
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(999)
        self.quantity_spin.setValue(1)
        self.quantity_spin.valueChanged.connect(self.update_total)
        quantity_layout.addWidget(self.quantity_spin)
        
        details_layout.addLayout(quantity_layout)
        
        # Prix d'achat
        price_layout = QHBoxLayout()
        price_label = QLabel("Prix d'achat:")
        price_layout.addWidget(price_label)
        
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setMinimum(0)
        self.price_spin.setMaximum(9999.99)
        self.price_spin.setDecimals(2)
        self.price_spin.setSingleStep(0.01)
        self.price_spin.valueChanged.connect(self.update_total)
        price_layout.addWidget(self.price_spin)
        
        details_layout.addLayout(price_layout)
        
        # Prix total
        total_layout = QHBoxLayout()
        total_label = QLabel("Prix total:")
        total_layout.addWidget(total_label)
        
        self.total_spin = QDoubleSpinBox()
        self.total_spin.setMinimum(0)
        self.total_spin.setMaximum(9999.99)
        self.total_spin.setDecimals(2)
        self.total_spin.setSingleStep(0.01)
        self.total_spin.valueChanged.connect(self.update_purchase_price)
        total_layout.addWidget(self.total_spin)
        
        details_layout.addLayout(total_layout)
        
        # Numéro de lot
        lot_layout = QHBoxLayout()
        lot_label = QLabel("Numéro de lot:")
        lot_layout.addWidget(lot_label)
        
        self.lot_edit = QLineEdit()
        lot_layout.addWidget(self.lot_edit)
        
        details_layout.addLayout(lot_layout)
        
        # Numéro de série
        serial_layout = QHBoxLayout()
        serial_label = QLabel("Numéro de série:")
        serial_layout.addWidget(serial_label)
        
        self.serial_edit = QLineEdit()
        serial_layout.addWidget(self.serial_edit)
        
        details_layout.addLayout(serial_layout)
        
        # Déduire du stock
        self.deduct_stock_check = QCheckBox("Déduire du stock")
        self.deduct_stock_check.setChecked(True)
        details_layout.addWidget(self.deduct_stock_check)
        
        layout.addLayout(details_layout)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        cancel_button = QPushButton("Annuler")
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        self.add_button = QPushButton("Ajouter")
        self.add_button.clicked.connect(self.accept)
        self.add_button.setEnabled(False)
        buttons_layout.addWidget(self.add_button)
        
        layout.addLayout(buttons_layout)
        
    async def load_products(self):
        """Charge les produits"""
        try:
            # Récupérer les produits
            products = await self.product_service.get_all_products()
            
            # Remplir la table
            self.products_table.setRowCount(len(products))
            
            for i, product in enumerate(products):
                self.products_table.setItem(i, 0, QTableWidgetItem(str(product.id)))
                self.products_table.setItem(i, 1, QTableWidgetItem(product.name))
                self.products_table.setItem(i, 2, QTableWidgetItem(product.reference or ""))
                self.products_table.setItem(i, 3, QTableWidgetItem(f"{product.price:.2f}"))
                
                # Stocker le produit dans l'élément
                self.products_table.item(i, 0).setData(Qt.ItemDataRole.UserRole, product)
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des produits: {str(e)}")
            
    def filter_products(self, text):
        """Filtre les produits selon le texte de recherche"""
        for i in range(self.products_table.rowCount()):
            show = False
            
            for j in range(self.products_table.columnCount()):
                item = self.products_table.item(i, j)
                if item and text.lower() in item.text().lower():
                    show = True
                    break
                    
            self.products_table.setRowHidden(i, not show)
            
    def on_product_selected(self):
        """Gère la sélection d'un produit"""
        selected_items = self.products_table.selectedItems()
        if not selected_items:
            self.selected_product = None
            self.add_button.setEnabled(False)
            return
            
        # Récupérer le produit
        row = selected_items[0].row()
        self.selected_product = self.products_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        
        # Mettre à jour les champs
        self.price_spin.setValue(self.selected_product.price)
        self.update_total()
        
        self.add_button.setEnabled(True)
        
    def update_total(self):
        """Met à jour le prix total en fonction de la quantité et du prix unitaire"""
        quantity = self.quantity_spin.value()
        unit_price = self.price_spin.value()
        total = quantity * unit_price
        
        # Mettre à jour le prix total sans déclencher le signal valueChanged
        self.total_spin.blockSignals(True)
        self.total_spin.setValue(total)
        self.total_spin.blockSignals(False)
        
    def update_purchase_price(self):
        """Met à jour le prix d'achat en fonction du prix total et de la quantité"""
        quantity = self.quantity_spin.value()
        total = self.total_spin.value()
        
        if quantity > 0:
            purchase_price = total / quantity
            
            # Mettre à jour le prix d'achat sans déclencher le signal valueChanged
            self.price_spin.blockSignals(True)
            self.price_spin.setValue(purchase_price)
            self.price_spin.blockSignals(False)
            
    def get_part_data(self):
        """Récupère les données de la pièce"""
        if not self.selected_product:
            return None
            
        return {
            "product_id": self.selected_product.id,
            "product_name": self.selected_product.name,
            "quantity": self.quantity_spin.value(),
            "unit_price": self.price_spin.value(),
            "total_price": self.total_spin.value(),
            "lot_number": self.lot_edit.text(),
            "serial_number": self.serial_edit.text(),
            "deduct_stock": self.deduct_stock_check.isChecked()
        }

class RepairPartsWidget(QWidget):
    """Widget pour gérer les pièces détachées utilisées dans une réparation"""
    
    parts_changed = pyqtSignal()  # Signal émis lorsque les pièces sont modifiées
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.repair_id = None
        self.parts = []
        
        # Créer une session de base de données
        self.db = SessionLocal()
        self.repair_service = RepairService(self.db)
        self.product_service = ProductService(self.db)
        
        self.setup_ui()
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()
        
    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("RepairPartsWidget: Session de base de données fermée")
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Pièces détachées")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        self.add_part_button = QPushButton("Ajouter une pièce")
        self.add_part_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_part_button.clicked.connect(self.add_part)
        header_layout.addWidget(self.add_part_button)
        
        layout.addLayout(header_layout)
        
        # Table des pièces
        self.parts_table = QTableWidget()
        self.parts_table.setColumnCount(6)
        self.parts_table.setHorizontalHeaderLabels(["ID", "Produit", "Quantité", "Prix d'achat", "Prix total", "Actions"])
        self.parts_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.parts_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        layout.addWidget(self.parts_table)
        
        # Résumé
        summary_layout = QHBoxLayout()
        
        self.total_label = QLabel("Total: 0.00")
        self.total_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(self.total_label)
        
        layout.addLayout(summary_layout)
        
    def set_repair_id(self, repair_id):
        """Définit l'ID de la réparation et charge les pièces"""
        self.repair_id = repair_id
        if repair_id:
            asyncio.create_task(self.load_parts())
        else:
            self.clear()
            
    def clear(self):
        """Efface les données affichées"""
        self.parts = []
        self.parts_table.setRowCount(0)
        self.update_total()
        
    async def load_parts(self):
        """Charge les pièces de la réparation"""
        if not self.repair_id:
            return
            
        self.loading_overlay.show()
        
        try:
            # Récupérer les pièces
            self.parts = await self.repair_service.get_repair_parts(self.repair_id)
            
            # Mettre à jour l'affichage
            self.update_parts_table()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des pièces: {str(e)}")
        finally:
            self.loading_overlay.hide()
            
    def update_parts_table(self):
        """Met à jour la table des pièces"""
        self.parts_table.setRowCount(len(self.parts))
        
        for i, part in enumerate(self.parts):
            self.parts_table.setItem(i, 0, QTableWidgetItem(str(part.id)))
            self.parts_table.setItem(i, 1, QTableWidgetItem(part.product_name))
            self.parts_table.setItem(i, 2, QTableWidgetItem(str(part.quantity)))
            self.parts_table.setItem(i, 3, QTableWidgetItem(f"{part.unit_price:.2f}"))
            self.parts_table.setItem(i, 4, QTableWidgetItem(f"{part.total_price:.2f}"))
            
            # Bouton de suppression
            delete_button = QPushButton("Supprimer")
            delete_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
            delete_button.clicked.connect(lambda checked, part_id=part.id: self.delete_part(part_id))
            self.parts_table.setCellWidget(i, 5, delete_button)
            
        self.update_total()
        
    def update_total(self):
        """Met à jour le total des pièces"""
        total = sum(part.total_price for part in self.parts)
        self.total_label.setText(f"Total: {total:.2f}")
        
    def add_part(self):
        """Ajoute une nouvelle pièce"""
        if not self.repair_id:
            QMessageBox.warning(self, "Avertissement", "Aucune réparation sélectionnée.")
            return
            
        # Ouvrir une boîte de dialogue pour sélectionner une pièce
        dialog = AddPartDialog(self, self.product_service)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Récupérer les données de la pièce
            part_data = dialog.get_part_data()
            
            if part_data:
                # Ajouter la pièce
                self.loading_overlay.show()
                asyncio.create_task(self._add_part_async(part_data))
                
    async def _add_part_async(self, part_data):
        """Ajoute une pièce de manière asynchrone"""
        try:
            # Ajouter la pièce
            await self.repair_service.add_repair_part(
                self.repair_id,
                part_data["product_id"],
                part_data["quantity"],
                part_data["unit_price"],  # Le service attend purchase_unit_price mais accepte unit_price
                part_data["lot_number"],
                part_data["serial_number"],
                part_data["deduct_stock"]
            )
            
            # Recharger les pièces
            await self.load_parts()
            
            # Émettre le signal de changement
            self.parts_changed.emit()
            
            QMessageBox.information(self, "Information", "La pièce a été ajoutée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de la pièce: {str(e)}")
        finally:
            self.loading_overlay.hide()
            
    def delete_part(self, part_id):
        """Supprime une pièce"""
        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir supprimer cette pièce ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Supprimer la pièce
            self.loading_overlay.show()
            asyncio.create_task(self._delete_part_async(part_id))
            
    async def _delete_part_async(self, part_id):
        """Supprime une pièce de manière asynchrone"""
        try:
            # Supprimer la pièce
            success = await self.repair_service.delete_repair_part(part_id)
            
            if success:
                # Recharger les pièces
                await self.load_parts()
                
                # Émettre le signal de changement
                self.parts_changed.emit()
                
                QMessageBox.information(self, "Information", "La pièce a été supprimée avec succès.")
            else:
                QMessageBox.warning(self, "Avertissement", "Impossible de supprimer la pièce.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression de la pièce: {str(e)}")
        finally:
            self.loading_overlay.hide()
