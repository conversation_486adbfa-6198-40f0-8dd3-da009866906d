"""
Widget pour afficher et gérer les photos d'une réparation.
"""
import os
import asyncio
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QFileDialog, QMessageBox, QComboBox, QLineEdit,
    QGridLayout, QDialog, QTextEdit, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QPixmap, QIcon, QImage

from app.core.models.repair_photo import PhotoType
from app.core.services.repair_docs_service import RepairDocsService
from app.utils.database import SessionLocal
from ....components.custom_widgets import LoadingOverlay

class PhotoViewDialog(QDialog):
    """Boîte de dialogue pour afficher une photo en grand format"""

    def __init__(self, parent=None, photo_path=None, title=None, description=None):
        super().__init__(parent)
        self.photo_path = photo_path
        self.title = title
        self.description = description

        self.setWindowTitle("Visualisation de photo")
        self.setMinimumSize(800, 600)

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)

        # Titre
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
            layout.addWidget(title_label)

        # Photo
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)

        photo_widget = QLabel()
        photo_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)

        if self.photo_path and os.path.exists(self.photo_path):
            pixmap = QPixmap(self.photo_path)
            photo_widget.setPixmap(pixmap)
        else:
            photo_widget.setText("Image non disponible")

        scroll_area.setWidget(photo_widget)
        layout.addWidget(scroll_area)

        # Description
        if self.description:
            description_frame = QFrame()
            description_frame.setFrameShape(QFrame.Shape.StyledPanel)
            description_frame.setFrameShadow(QFrame.Shadow.Sunken)
            description_layout = QVBoxLayout(description_frame)

            description_label = QLabel("Description:")
            description_label.setStyleSheet("font-weight: bold;")
            description_layout.addWidget(description_label)

            description_text = QLabel(self.description)
            description_text.setWordWrap(True)
            description_layout.addWidget(description_text)

            layout.addWidget(description_frame)

        # Bouton de fermeture
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

class PhotoThumbnail(QWidget):
    """Widget pour afficher une miniature de photo"""

    clicked = pyqtSignal(int)  # Signal émis lorsque la miniature est cliquée
    delete_requested = pyqtSignal(int)  # Signal émis lorsque la suppression est demandée

    def __init__(self, photo_id, thumbnail_path, title=None, photo_type=None, parent=None):
        super().__init__(parent)
        self.photo_id = photo_id
        self.thumbnail_path = thumbnail_path
        self.title = title
        self.photo_type = photo_type

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Cadre pour la miniature
        self.thumbnail_frame = QFrame()
        self.thumbnail_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.thumbnail_frame.setFrameShadow(QFrame.Shadow.Raised)
        self.thumbnail_frame.setStyleSheet("background-color: white;")
        thumbnail_layout = QVBoxLayout(self.thumbnail_frame)

        # Miniature
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.thumbnail_label.setFixedSize(150, 150)

        if self.thumbnail_path and os.path.exists(self.thumbnail_path):
            pixmap = QPixmap(self.thumbnail_path)
            pixmap = pixmap.scaled(150, 150, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            self.thumbnail_label.setPixmap(pixmap)
        else:
            self.thumbnail_label.setText("Image non disponible")

        thumbnail_layout.addWidget(self.thumbnail_label)

        # Titre
        if self.title:
            title_label = QLabel(self.title)
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setWordWrap(True)
            title_label.setStyleSheet("font-weight: bold;")
            thumbnail_layout.addWidget(title_label)

        # Type de photo
        if self.photo_type:
            type_label = QLabel(self.get_photo_type_display(self.photo_type))
            type_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            type_label.setStyleSheet("color: gray;")
            thumbnail_layout.addWidget(type_label)

        layout.addWidget(self.thumbnail_frame)

        # Bouton de suppression
        delete_button = QPushButton("Supprimer")
        delete_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
        delete_button.clicked.connect(lambda: self.delete_requested.emit(self.photo_id))
        layout.addWidget(delete_button)

        # Connecter le clic sur la miniature
        self.thumbnail_frame.mousePressEvent = self.on_thumbnail_clicked

    def on_thumbnail_clicked(self, event):
        """Gère le clic sur la miniature"""
        self.clicked.emit(self.photo_id)

    def get_photo_type_display(self, photo_type):
        """Retourne l'affichage du type de photo"""
        type_display = {
            PhotoType.BEFORE: "Avant",
            PhotoType.DURING: "Pendant",
            PhotoType.AFTER: "Après",
            PhotoType.DAMAGE: "Dommage",
            PhotoType.PART: "Pièce",
            PhotoType.OTHER: "Autre"
        }
        return type_display.get(photo_type, str(photo_type))

class RepairPhotosWidget(QWidget):
    """Widget pour afficher et gérer les photos d'une réparation"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.repair_id = None
        self.photos = []

        # Créer une session de base de données
        self.db = SessionLocal()
        self.service = RepairDocsService(self.db)

        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("RepairPhotosWidget: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)

        # En-tête
        header_layout = QHBoxLayout()

        title_label = QLabel("Photos")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        self.add_photo_button = QPushButton("Ajouter une photo")
        self.add_photo_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_photo_button.clicked.connect(self.add_photo)
        header_layout.addWidget(self.add_photo_button)

        layout.addLayout(header_layout)

        # Zone de défilement pour les miniatures
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)

        self.thumbnails_widget = QWidget()
        self.thumbnails_layout = QGridLayout(self.thumbnails_widget)

        scroll_area.setWidget(self.thumbnails_widget)
        layout.addWidget(scroll_area)

    def set_repair_id(self, repair_id):
        """Définit l'ID de la réparation et charge les photos"""
        self.repair_id = repair_id
        if repair_id:
            asyncio.create_task(self.load_photos())
        else:
            self.clear()

    def clear(self):
        """Efface les données affichées"""
        self.photos = []

        # Effacer les miniatures
        while self.thumbnails_layout.count():
            item = self.thumbnails_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

    async def load_photos(self):
        """Charge les photos de la réparation"""
        if not self.repair_id:
            return

        self.loading_overlay.show()

        try:
            # Récupérer les photos
            self.photos = await self.service.get_photos(self.repair_id)

            # Mettre à jour l'affichage
            self.update_thumbnails()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des photos: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def update_thumbnails(self):
        """Met à jour l'affichage des miniatures"""
        # Effacer les miniatures existantes
        self.clear()

        # Ajouter les nouvelles miniatures
        for i, photo in enumerate(self.photos):
            row = i // 3
            col = i % 3

            thumbnail = PhotoThumbnail(
                photo.id,
                photo.thumbnail_path,
                photo.title,
                photo.photo_type
            )
            thumbnail.clicked.connect(self.view_photo)
            thumbnail.delete_requested.connect(self.delete_photo)

            self.thumbnails_layout.addWidget(thumbnail, row, col)

    def add_photo(self):
        """Ajoute une nouvelle photo"""
        if not self.repair_id:
            QMessageBox.warning(self, "Avertissement", "Aucune réparation sélectionnée.")
            return

        # Ouvrir une boîte de dialogue pour sélectionner une image
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Sélectionner une image",
            "",
            "Images (*.png *.jpg *.jpeg *.bmp *.gif)"
        )

        if not file_path:
            return

        # Ouvrir une boîte de dialogue pour les détails de la photo
        dialog = QDialog(self)
        dialog.setWindowTitle("Détails de la photo")
        dialog.setMinimumWidth(400)

        dialog_layout = QVBoxLayout(dialog)

        # Type de photo
        type_layout = QHBoxLayout()
        type_label = QLabel("Type de photo:")
        type_layout.addWidget(type_label)

        type_combo = QComboBox()
        for photo_type in PhotoType:
            type_combo.addItem(self.get_photo_type_display(photo_type), photo_type)
        type_layout.addWidget(type_combo)

        dialog_layout.addLayout(type_layout)

        # Titre
        title_layout = QHBoxLayout()
        title_label = QLabel("Titre:")
        title_layout.addWidget(title_label)

        title_edit = QLineEdit()
        title_layout.addWidget(title_edit)

        dialog_layout.addLayout(title_layout)

        # Description
        description_layout = QVBoxLayout()
        description_label = QLabel("Description:")
        description_layout.addWidget(description_label)

        description_edit = QTextEdit()
        description_layout.addWidget(description_edit)

        dialog_layout.addLayout(description_layout)

        # Boutons
        buttons_layout = QHBoxLayout()

        cancel_button = QPushButton("Annuler")
        cancel_button.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_button)

        save_button = QPushButton("Enregistrer")
        save_button.clicked.connect(dialog.accept)
        buttons_layout.addWidget(save_button)

        dialog_layout.addLayout(buttons_layout)

        # Afficher la boîte de dialogue
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Récupérer les valeurs
            photo_type = type_combo.currentData()
            title = title_edit.text()
            description = description_edit.toPlainText()

            # Ajouter la photo
            self.loading_overlay.show()
            asyncio.create_task(self._add_photo_async(file_path, photo_type, title, description))

    async def _add_photo_async(self, file_path, photo_type, title, description):
        """Ajoute une photo de manière asynchrone"""
        try:
            # Ajouter la photo
            await self.service.add_photo(
                self.repair_id,
                file_path,
                photo_type,
                title,
                description
            )

            # Recharger les photos
            await self.load_photos()

            QMessageBox.information(self, "Information", "La photo a été ajoutée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de la photo: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def view_photo(self, photo_id):
        """Affiche une photo en grand format"""
        # Trouver la photo
        photo = next((p for p in self.photos if p.id == photo_id), None)
        if not photo:
            return

        # Afficher la photo
        dialog = PhotoViewDialog(
            self,
            photo.file_path,
            photo.title,
            photo.description
        )
        dialog.exec()

    def delete_photo(self, photo_id):
        """Supprime une photo"""
        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir supprimer cette photo ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Supprimer la photo
            self.loading_overlay.show()
            asyncio.create_task(self._delete_photo_async(photo_id))

    async def _delete_photo_async(self, photo_id):
        """Supprime une photo de manière asynchrone"""
        try:
            # Supprimer la photo
            success = await self.service.delete_photo(photo_id)

            if success:
                # Recharger les photos
                await self.load_photos()

                QMessageBox.information(self, "Information", "La photo a été supprimée avec succès.")
            else:
                QMessageBox.warning(self, "Avertissement", "Impossible de supprimer la photo.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression de la photo: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def get_photo_type_display(self, photo_type):
        """Retourne l'affichage du type de photo"""
        type_display = {
            PhotoType.BEFORE: "Avant",
            PhotoType.DURING: "Pendant",
            PhotoType.AFTER: "Après",
            PhotoType.DAMAGE: "Dommage",
            PhotoType.PART: "Pièce",
            PhotoType.OTHER: "Autre"
        }
        return type_display.get(photo_type, str(photo_type))
