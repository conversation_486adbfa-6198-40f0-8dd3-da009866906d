from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView,
    QPushButton, QMessageBox
)
from PyQt6.QtCore import Qt
import asyncio
from decimal import Decimal

class UsedPartsWidget(QWidget):
    """Widget d'affichage des pièces utilisées dans une réparation"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.repair_id = None
        self.used_parts = []
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Tableau des pièces utilisées
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(["<PERSON>é<PERSON><PERSON><PERSON>ce", "<PERSON><PERSON><PERSON>ation", "Quantité", "Prix d'achat", "Total"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.table.setAlternatingRowColors(True)

        main_layout.addWidget(self.table)

        # Total
        total_layout = QHBoxLayout()
        total_layout.addStretch()

        self.total_label = QLabel("Total: 0.00 DA")
        self.total_label.setObjectName("totalAmount")
        total_layout.addWidget(self.total_label)

        main_layout.addLayout(total_layout)

        # Boutons
        buttons_layout = QHBoxLayout()

        self.add_button = QPushButton("Ajouter des pièces")
        self.add_button.setObjectName("primaryButton")
        buttons_layout.addWidget(self.add_button)

        buttons_layout.addStretch()

        main_layout.addLayout(buttons_layout)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.add_button.clicked.connect(self.add_parts)

    def set_repair_id(self, repair_id):
        """Définit l'ID de la réparation et charge les pièces utilisées"""
        self.repair_id = repair_id
        if repair_id:
            asyncio.create_task(self.load_used_parts())
        else:
            self.clear()

    def set_parts(self, parts):
        """Définit les pièces utilisées directement"""
        self.used_parts = parts if parts else []
        self.update_table()

    def clear(self):
        """Efface les données affichées"""
        self.used_parts = []
        self.table.setRowCount(0)
        self.total_label.setText("Total: 0.00 DA")

    async def load_used_parts(self):
        """Charge les pièces utilisées dans la réparation"""
        if not self.repair_id:
            return

        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            service = RepairService(db)

            # Charger la réparation
            repair = await service.get(self.repair_id)

            # Récupérer les pièces utilisées
            self.used_parts = repair.used_parts if hasattr(repair, 'used_parts') else []

            # Mettre à jour le tableau
            self.update_table()

        except Exception as e:
            print(f"Erreur lors du chargement des pièces utilisées: {e}")

    def update_table(self):
        """Met à jour le tableau des pièces utilisées"""
        self.table.setRowCount(0)

        total = Decimal('0')

        # Récupérer les informations des pièces depuis l'inventaire
        inventory_items = {}
        try:
            from app.core.services.inventory_service import InventoryService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            service = InventoryService(db)

            # Récupérer les IDs des pièces utilisées
            part_ids = [part.part_id for part in self.used_parts if hasattr(part, 'part_id')]

            # Récupérer les pièces d'inventaire correspondantes
            if part_ids:
                for part_id in part_ids:
                    try:
                        item = service.get_sync(part_id)
                        if item:
                            inventory_items[part_id] = item
                    except Exception as e:
                        print(f"Erreur lors de la récupération de la pièce {part_id}: {e}")

            db.close()
        except Exception as e:
            print(f"Erreur lors de la récupération des pièces d'inventaire: {e}")

        for i, part in enumerate(self.used_parts):
            self.table.insertRow(i)

            # Récupérer les informations de la pièce depuis l'inventaire
            inventory_item = inventory_items.get(part.part_id) if hasattr(part, 'part_id') else None

            # Référence (SKU)
            sku = getattr(inventory_item, 'sku', 'N/A') if inventory_item else 'N/A'
            self.table.setItem(i, 0, QTableWidgetItem(sku))

            # Désignation
            name = getattr(inventory_item, 'name', 'N/A') if inventory_item else 'N/A'
            self.table.setItem(i, 1, QTableWidgetItem(name))

            # Quantité
            quantity_item = QTableWidgetItem(str(part.quantity))
            quantity_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(i, 2, quantity_item)

            # Prix d'achat (toujours Decimal)
            raw_price = getattr(part, 'purchase_unit_price', Decimal('0'))
            try:
                purchase_price = raw_price if isinstance(raw_price, Decimal) else Decimal(str(raw_price or 0))
            except Exception:
                purchase_price = Decimal('0')
            unit_price_item = QTableWidgetItem(f"{purchase_price:.2f} DA")
            unit_price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.table.setItem(i, 3, unit_price_item)

            # Total (quantité x prix)
            try:
                qty_dec = part.quantity if isinstance(part.quantity, Decimal) else Decimal(str(part.quantity))
            except Exception:
                qty_dec = Decimal('0')
            part_total = qty_dec * purchase_price
            total_item = QTableWidgetItem(f"{part_total:.2f} DA")
            total_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.table.setItem(i, 4, total_item)

            # Ajouter au total
            total += part_total

        # Mettre à jour le total
        self.total_label.setText(f"Total: {total:.2f} DA")

    def add_parts(self):
        """Ouvre la boîte de dialogue pour ajouter des pièces"""
        if not self.repair_id:
            QMessageBox.warning(self, "Avertissement", "Aucune réparation sélectionnée.")
            return

        try:
            from app.ui.views.repair.dialogs.used_parts_dialog import UsedPartsDialog

            dialog = UsedPartsDialog(self, self.repair_id)
            if dialog.exec():
                # Recharger les pièces après l'ajout
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_closed():
                        raise RuntimeError("Event loop is closed")
                    if loop.is_running():
                        asyncio.create_task(self.load_used_parts())
                    else:
                        loop.run_until_complete(self.load_used_parts())
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.load_used_parts())

                # Émettre un signal pour mettre à jour les coûts de la réparation
                # Obtenir la fenêtre parente principale
                from app.ui.views.repair.repair_view import RepairView
                parent = self
                while parent and not isinstance(parent, RepairView):
                    parent = parent.parent()

                if parent and isinstance(parent, RepairView):
                    # Recharger la réparation
                    parent._load_repair_details_wrapper(self.repair_id)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ouverture de la boîte de dialogue: {str(e)}")