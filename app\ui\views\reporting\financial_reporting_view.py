"""
Vue pour afficher les rapports financiers intégrés.
Cette vue permet d'accéder aux différents rapports financiers qui intègrent
les données des modules d'achat, de vente et de gestion des stocks.
"""
# Importer les correctifs
from .widgets.fix_matplotlib import *
from .fix_qtabwidget import *
import asyncio
from datetime import datetime, timedelta

from app.core.services.financial_reporting_service import FinancialReportingService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from .widgets.profit_loss_widget import ProfitLossWidget
from .widgets.cash_flow_widget import CashFlowWidget
from .widgets.accounts_receivable_widget import AccountsReceivableWidget
from .widgets.accounts_payable_widget import AccountsPayableWidget
from .widgets.sales_report_widget import SalesReportWidget
from .widgets.purchases_report_widget import PurchasesReportWidget
from .widgets.inventory_report_widget import InventoryReportWidget
from .widgets.margin_analysis_widget import MarginAnalysisWidget

class FinancialReportingView(QWidget):
    """Vue pour afficher les rapports financiers intégrés"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Service
        self.db = SessionLocal()
        self.service = FinancialReportingService(self.db)

        # Configuration de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        title_label = QLabel("Rapports financiers intégrés")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Onglets pour les différents rapports
        self.tab_widget = QTabWidget()

        # Onglet Profits et Pertes
        self.profit_loss_widget = ProfitLossWidget()
        self.tab_widget.addTab(self.profit_loss_widget, "Profits et Pertes")

        # Onglet Flux de trésorerie
        self.cash_flow_widget = CashFlowWidget()
        self.tab_widget.addTab(self.cash_flow_widget, "Flux de trésorerie")

        # Onglet Comptes clients
        self.accounts_receivable_widget = AccountsReceivableWidget()
        self.tab_widget.addTab(self.accounts_receivable_widget, "Comptes clients")

        # Onglet Comptes fournisseurs
        self.accounts_payable_widget = AccountsPayableWidget()
        self.tab_widget.addTab(self.accounts_payable_widget, "Comptes fournisseurs")

        # Onglet Rapport des ventes
        self.sales_report_widget = SalesReportWidget()
        self.tab_widget.addTab(self.sales_report_widget, "Rapport des ventes")

        # Onglet Rapport des achats
        self.purchases_report_widget = PurchasesReportWidget()
        self.tab_widget.addTab(self.purchases_report_widget, "Rapport des achats")

        # Onglet Rapport d'inventaire
        self.inventory_report_widget = InventoryReportWidget()
        self.tab_widget.addTab(self.inventory_report_widget, "Rapport d'inventaire")

        # Onglet Analyse des marges
        self.margin_analysis_widget = MarginAnalysisWidget()
        self.tab_widget.addTab(self.margin_analysis_widget, "Analyse des marges")

        main_layout.addWidget(self.tab_widget)

        # Connexions
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def on_tab_changed(self, index):
        """Gère le changement d'onglet"""
        # Charger les données de l'onglet sélectionné
        widget = self.tab_widget.widget(index)
        if hasattr(widget, 'load_data'):
            widget.load_data()
