from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QComboBox, QPushButton, QDateEdit, QFrame, QScrollArea,
    QGridLayout, QSplitter, QGroupBox
)
from PyQt6.QtCore import Qt, QDate, QTimer
import asyncio
from datetime import datetime

from .widgets.chart_widget import (
    RepairTrendChart, InventoryTrendChart, RepairStatusChart,
    TopEquipmentChart
)
from .widgets.kpi_widget import KPIWidget
from .widgets.report_table import ReportTable
from .widgets.technician_report_widget import TechnicianReportWidget
from .widgets.sales_report_widget import SalesReportWidget
from .widgets.purchases_report_widget import PurchasesReportWidget
from .widgets.inventory_report_widget import InventoryReportWidget
from .widgets.margin_analysis_widget import MarginAnalysisWidget
from .widgets.period_comparison_widget import PeriodComparisonWidget
from .widgets.executive_dashboard_widget import ExecutiveDashboardWidget
from .widgets.alerts_widget import AlertsWidget
from .financial_reporting_view import FinancialReportingView
from ...components.custom_widgets import LoadingOverlay
from app.core.services.reporting_service import ReportingService

class ReportingView(QWidget):
    def __init__(self):
        super().__init__()
        self.service = ReportingService()
        self.setup_ui()

        # Timer pour le rafraîchissement automatique des données
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh)

        # Démarrer le chargement initial des données
        QTimer.singleShot(100, self.init_data)

    def setup_ui(self):
        """Configure l'interface utilisateur de la vue de reporting"""
        main_layout = QVBoxLayout(self)

        # En-tête avec boutons de contrôle
        header_layout = QHBoxLayout()

        # Titre
        header = QLabel("Rapports et Statistiques")
        header.setObjectName("reportingHeader")
        header_layout.addWidget(header)

        header_layout.addStretch()

        # Bouton de rafraîchissement manuel
        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.setObjectName("refreshButton")
        self.refresh_button.clicked.connect(self.refresh)
        header_layout.addWidget(self.refresh_button)

        # Combobox pour l'intervalle de rafraîchissement automatique
        header_layout.addWidget(QLabel("Rafraîchissement auto:"))
        self.auto_refresh_combo = QComboBox()
        self.auto_refresh_combo.addItems(["Désactivé", "30 secondes", "1 minute", "5 minutes", "15 minutes"])
        self.auto_refresh_combo.currentIndexChanged.connect(self._on_auto_refresh_changed)
        header_layout.addWidget(self.auto_refresh_combo)

        main_layout.addLayout(header_layout)

        # Onglets
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("reportingTabs")

        # Onglet Tableau de bord
        self.dashboard_tab = self._create_dashboard_tab()
        self.tab_widget.addTab(self.dashboard_tab, "Tableau de bord")

        # Onglet Tableau de Bord Exécutif
        self.executive_dashboard_tab = self._create_executive_dashboard_tab()
        self.tab_widget.addTab(self.executive_dashboard_tab, "Tableau de Bord Exécutif")

        # Onglet Réparations
        self.repairs_tab = self._create_repairs_tab()
        self.tab_widget.addTab(self.repairs_tab, "Réparations")

        # Onglet Inventaire
        self.inventory_tab = self._create_inventory_tab()
        self.tab_widget.addTab(self.inventory_tab, "Inventaire")

        # Onglet Équipements
        self.equipment_tab = self._create_equipment_tab()
        self.tab_widget.addTab(self.equipment_tab, "Équipements")

        # Onglet Techniciens
        self.technicians_tab = self._create_technicians_tab()
        self.tab_widget.addTab(self.technicians_tab, "Techniciens")

        # Onglet Analyses Avancées
        self.advanced_analysis_tab = self._create_advanced_analysis_tab()
        self.tab_widget.addTab(self.advanced_analysis_tab, "Analyses Avancées")

        # Onglet Rapports
        self.reports_tab = self._create_reports_tab()
        self.tab_widget.addTab(self.reports_tab, "Rapports")

        # Onglet Finances
        self.finances_tab = self._create_finances_tab()
        self.tab_widget.addTab(self.finances_tab, "Finances")

        # Onglet Alertes
        self.alerts_tab = self._create_alerts_tab()
        self.tab_widget.addTab(self.alerts_tab, "Alertes")

        main_layout.addWidget(self.tab_widget)

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()

    def _create_dashboard_tab(self):
        """Crée l'onglet Tableau de bord"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Section KPIs
        kpi_layout = QGridLayout()
        self.kpi_widgets = {
            "active_repairs": KPIWidget("Réparations en cours", "0", "repair"),
            "waiting_parts": KPIWidget("En attente de pièces", "0", "repair"),
            "on_hold": KPIWidget("Réparations en pause", "0", "repair"),
            "unpaid_repairs": KPIWidget("Réparations impayées", "0", "repair"),
            "unpaid_amount": KPIWidget("Montant impayé", "0 DA", "money"),
            "monthly_revenue": KPIWidget("Chiffre d'affaires mensuel", "0 DA", "money"),
            "monthly_payments": KPIWidget("Paiements mensuels", "0 DA", "money"),
            "low_stock": KPIWidget("Stock faible", "0", "inventory"),
            "upcoming_maintenance": KPIWidget("Maintenance prévue", "0", "maintenance"),
            "pending_orders": KPIWidget("Commandes en attente", "0", "order")
        }

        # Ajouter les KPIs au layout (2 lignes de 5 KPIs)
        kpi_keys = list(self.kpi_widgets.keys())
        for i, key in enumerate(kpi_keys[:5]):
            kpi_layout.addWidget(self.kpi_widgets[key], 0, i)
        for i, key in enumerate(kpi_keys[5:]):
            kpi_layout.addWidget(self.kpi_widgets[key], 1, i)

        layout.addLayout(kpi_layout)

        # Section Graphiques
        charts_layout = QGridLayout()

        # Graphique des tendances de réparation
        self.repair_trend_chart = RepairTrendChart()
        charts_layout.addWidget(self.repair_trend_chart, 0, 0)

        # Graphique des statuts de réparation
        self.repair_status_chart = RepairStatusChart()
        charts_layout.addWidget(self.repair_status_chart, 0, 1)

        # Graphique des tendances d'inventaire
        self.inventory_trend_chart = InventoryTrendChart()
        charts_layout.addWidget(self.inventory_trend_chart, 1, 0)

        # Graphique des équipements les plus réparés
        self.top_equipment_chart = TopEquipmentChart()
        charts_layout.addWidget(self.top_equipment_chart, 1, 1)

        layout.addLayout(charts_layout)

        return tab

    def _create_executive_dashboard_tab(self):
        """Crée l'onglet Tableau de Bord Exécutif"""
        # Utiliser directement le widget spécialisé
        self.executive_dashboard_widget = ExecutiveDashboardWidget()

        # Charger les données initiales
        QTimer.singleShot(300, self.executive_dashboard_widget.load_data)

        return self.executive_dashboard_widget

    def _create_repairs_tab(self):
        """Crée l'onglet Réparations"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Filtres de date
        date_filter_layout = QHBoxLayout()
        date_filter_layout.addWidget(QLabel("Période:"))

        self.repair_period_combo = QComboBox()
        self.repair_period_combo.addItems(["7 derniers jours", "30 derniers jours", "3 derniers mois", "6 derniers mois", "12 derniers mois", "Personnalisé"])
        self.repair_period_combo.currentIndexChanged.connect(self._on_repair_period_changed)
        date_filter_layout.addWidget(self.repair_period_combo)

        date_filter_layout.addWidget(QLabel("Du:"))
        self.repair_start_date = QDateEdit()
        self.repair_start_date.setCalendarPopup(True)
        self.repair_start_date.setDate(QDate.currentDate().addDays(-30))
        date_filter_layout.addWidget(self.repair_start_date)

        date_filter_layout.addWidget(QLabel("Au:"))
        self.repair_end_date = QDateEdit()
        self.repair_end_date.setCalendarPopup(True)
        self.repair_end_date.setDate(QDate.currentDate())
        date_filter_layout.addWidget(self.repair_end_date)

        self.repair_apply_button = QPushButton("Appliquer")
        self.repair_apply_button.clicked.connect(self._load_repair_data)
        date_filter_layout.addWidget(self.repair_apply_button)

        date_filter_layout.addStretch()
        layout.addLayout(date_filter_layout)

        # Splitter pour les graphiques et tableaux
        splitter = QSplitter(Qt.Orientation.Vertical)

        # Section graphiques
        charts_widget = QWidget()
        charts_layout = QHBoxLayout(charts_widget)

        # Graphique des tendances de réparation
        self.repair_trend_detail_chart = RepairTrendChart()
        charts_layout.addWidget(self.repair_trend_detail_chart)

        # Graphique des statuts de réparation
        self.repair_status_detail_chart = RepairStatusChart()
        charts_layout.addWidget(self.repair_status_detail_chart)

        splitter.addWidget(charts_widget)

        # Section tableaux avec défilement
        tables_widget = QWidget()
        tables_main_layout = QVBoxLayout(tables_widget)

        # Créer un widget de défilement pour les tableaux
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # Widget contenant tous les tableaux
        tables_content = QWidget()
        tables_layout = QVBoxLayout(tables_content)
        tables_layout.setSpacing(10)

        # Première ligne: Statistiques de réparation et statistiques par marque
        top_tables_layout = QHBoxLayout()

        # Tableau des statistiques de réparation (à gauche)
        stats_group = QGroupBox("Statistiques de réparation")
        stats_layout = QVBoxLayout(stats_group)
        self.repair_stats_table = ReportTable(["Métrique", "Valeur"])
        self.repair_stats_table.setMinimumWidth(400)
        self.repair_stats_table.setMinimumHeight(300)
        stats_layout.addWidget(self.repair_stats_table)
        top_tables_layout.addWidget(stats_group, 1)

        # Tableaux des statistiques par marque et modèle (à droite)
        brand_model_group = QGroupBox("Statistiques par marque et modèle")
        brand_model_layout = QVBoxLayout(brand_model_group)

        # Tableau des statistiques par marque
        self.brand_stats_table = ReportTable(["Marque", "Réparations", "Coût total", "Montant total"])
        self.brand_stats_table.setMinimumHeight(150)
        brand_model_layout.addWidget(QLabel("Par marque:"))
        brand_model_layout.addWidget(self.brand_stats_table)

        # Tableau des statistiques par modèle
        self.model_stats_table = ReportTable(["Modèle", "Réparations", "Coût total", "Montant total"])
        self.model_stats_table.setMinimumHeight(150)
        brand_model_layout.addWidget(QLabel("Par modèle:"))
        brand_model_layout.addWidget(self.model_stats_table)

        top_tables_layout.addWidget(brand_model_group, 1)
        tables_layout.addLayout(top_tables_layout)

        # Deuxième ligne: Performances des techniciens
        tech_group = QGroupBox("Performance des techniciens")
        tech_layout = QVBoxLayout(tech_group)

        # Tableau des performances des techniciens
        self.technician_performance_table = ReportTable([
            "Technicien", "Réparations", "Durée moyenne", "Coût total",
            "Main d'œuvre", "Pièces", "Montant total", "Montant payé",
            "Taux de paiement", "Réparations/h", "Profit/h", "Efficacité"
        ])
        self.technician_performance_table.setMinimumHeight(200)
        tech_layout.addWidget(self.technician_performance_table)

        tables_layout.addWidget(tech_group)

        # Ajouter le contenu au widget de défilement
        scroll_area.setWidget(tables_content)
        tables_main_layout.addWidget(scroll_area)

        splitter.addWidget(tables_widget)

        layout.addWidget(splitter)

        return tab

    def _create_inventory_tab(self):
        """Crée l'onglet Inventaire"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Filtres de date
        date_filter_layout = QHBoxLayout()
        date_filter_layout.addWidget(QLabel("Période:"))

        self.inventory_period_combo = QComboBox()
        self.inventory_period_combo.addItems(["7 derniers jours", "30 derniers jours", "3 derniers mois", "6 derniers mois", "12 derniers mois", "Personnalisé"])
        self.inventory_period_combo.currentIndexChanged.connect(self._on_inventory_period_changed)
        date_filter_layout.addWidget(self.inventory_period_combo)

        date_filter_layout.addWidget(QLabel("Du:"))
        self.inventory_start_date = QDateEdit()
        self.inventory_start_date.setCalendarPopup(True)
        self.inventory_start_date.setDate(QDate.currentDate().addDays(-30))
        date_filter_layout.addWidget(self.inventory_start_date)

        date_filter_layout.addWidget(QLabel("Au:"))
        self.inventory_end_date = QDateEdit()
        self.inventory_end_date.setCalendarPopup(True)
        self.inventory_end_date.setDate(QDate.currentDate())
        date_filter_layout.addWidget(self.inventory_end_date)

        self.inventory_apply_button = QPushButton("Appliquer")
        self.inventory_apply_button.clicked.connect(self._load_inventory_data)
        date_filter_layout.addWidget(self.inventory_apply_button)

        date_filter_layout.addStretch()
        layout.addLayout(date_filter_layout)

        # Graphique des tendances d'inventaire
        self.inventory_trend_detail_chart = InventoryTrendChart()
        layout.addWidget(self.inventory_trend_detail_chart)

        # Tableau des mouvements d'inventaire
        self.inventory_movements_table = ReportTable(["Article", "Entrées", "Sorties", "Total"])
        layout.addWidget(QLabel("Mouvements d'inventaire:"))
        layout.addWidget(self.inventory_movements_table)

        return tab

    def _create_equipment_tab(self):
        """Crée l'onglet Équipements"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Filtres de date
        date_filter_layout = QHBoxLayout()
        date_filter_layout.addWidget(QLabel("Période:"))

        self.equipment_period_combo = QComboBox()
        self.equipment_period_combo.addItems(["30 derniers jours", "3 derniers mois", "6 derniers mois", "12 derniers mois", "Personnalisé"])
        self.equipment_period_combo.currentIndexChanged.connect(self._on_equipment_period_changed)
        date_filter_layout.addWidget(self.equipment_period_combo)

        date_filter_layout.addWidget(QLabel("Du:"))
        self.equipment_start_date = QDateEdit()
        self.equipment_start_date.setCalendarPopup(True)
        self.equipment_start_date.setDate(QDate.currentDate().addDays(-365))
        date_filter_layout.addWidget(self.equipment_start_date)

        date_filter_layout.addWidget(QLabel("Au:"))
        self.equipment_end_date = QDateEdit()
        self.equipment_end_date.setCalendarPopup(True)
        self.equipment_end_date.setDate(QDate.currentDate())
        date_filter_layout.addWidget(self.equipment_end_date)

        self.equipment_apply_button = QPushButton("Appliquer")
        self.equipment_apply_button.clicked.connect(self._load_equipment_data)
        date_filter_layout.addWidget(self.equipment_apply_button)

        date_filter_layout.addStretch()
        layout.addLayout(date_filter_layout)

        # Graphique des équipements les plus réparés
        self.top_equipment_detail_chart = TopEquipmentChart()
        layout.addWidget(self.top_equipment_detail_chart)

        # Tableau de fiabilité des équipements
        self.equipment_reliability_table = ReportTable(["Équipement", "Pannes", "Temps d'arrêt moyen", "Coût total", "MTBF (heures)"])
        layout.addWidget(QLabel("Fiabilité des équipements:"))
        layout.addWidget(self.equipment_reliability_table)

        return tab

    def _create_technicians_tab(self):
        """Crée l'onglet Techniciens"""
        # Utiliser directement le widget spécialisé
        self.technicians_widget = TechnicianReportWidget()

        # Charger les données initiales
        QTimer.singleShot(100, self.technicians_widget.update_report)

        return self.technicians_widget

    def _create_advanced_analysis_tab(self):
        """Crée l'onglet Analyses Avancées"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Titre de section
        title_label = QLabel("Analyses Financières et Commerciales Avancées")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # Créer un widget à onglets pour les différentes analyses
        analysis_tabs = QTabWidget()

        # Onglet Analyse des Ventes
        self.sales_analysis_widget = SalesReportWidget()
        analysis_tabs.addTab(self.sales_analysis_widget, "Analyse des Ventes")

        # Onglet Analyse des Achats
        self.purchases_analysis_widget = PurchasesReportWidget()
        analysis_tabs.addTab(self.purchases_analysis_widget, "Analyse des Achats")

        # Onglet Analyse de l'Inventaire
        self.inventory_analysis_widget = InventoryReportWidget()
        analysis_tabs.addTab(self.inventory_analysis_widget, "Analyse de l'Inventaire")

        # Onglet Analyse des Marges
        self.margin_analysis_widget = MarginAnalysisWidget()
        analysis_tabs.addTab(self.margin_analysis_widget, "Analyse des Marges")

        # Onglet Comparaison de Périodes
        self.period_comparison_widget = PeriodComparisonWidget()
        analysis_tabs.addTab(self.period_comparison_widget, "Comparaison de Périodes")

        layout.addWidget(analysis_tabs)

        # Charger les données initiales pour tous les widgets
        QTimer.singleShot(200, self._load_advanced_analysis_data)

        return tab

    def _load_advanced_analysis_data(self):
        """Charge les données pour les analyses avancées"""
        try:
            # Charger les données pour chaque widget d'analyse
            if hasattr(self, 'sales_analysis_widget'):
                QTimer.singleShot(100, self.sales_analysis_widget.load_data)

            if hasattr(self, 'purchases_analysis_widget'):
                QTimer.singleShot(200, self.purchases_analysis_widget.load_data)

            if hasattr(self, 'inventory_analysis_widget'):
                QTimer.singleShot(300, self.inventory_analysis_widget.load_data)

            if hasattr(self, 'margin_analysis_widget'):
                QTimer.singleShot(400, self.margin_analysis_widget.load_data)

        except Exception as e:
            print(f"Erreur lors du chargement des analyses avancées: {e}")

    def _create_reports_tab(self):
        """Crée l'onglet Rapports"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Section de génération de rapports
        report_group = QGroupBox("Générer un rapport")
        report_layout = QHBoxLayout(report_group)

        report_layout.addWidget(QLabel("Type de rapport:"))
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems(["Mensuel", "Trimestriel", "Annuel"])
        report_layout.addWidget(self.report_type_combo)

        # Sélecteur de période (mois, trimestre, année)
        self.period_stack = QTabWidget()
        self.period_stack.setTabBarAutoHide(True)

        # Sélecteur de mois
        month_widget = QWidget()
        month_layout = QHBoxLayout(month_widget)
        month_layout.addWidget(QLabel("Mois:"))
        self.month_combo = QComboBox()
        self.month_combo.addItems(["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"])
        self.month_combo.setCurrentIndex(datetime.now().month - 1)
        month_layout.addWidget(self.month_combo)
        self.period_stack.addTab(month_widget, "Mois")

        # Sélecteur de trimestre
        quarter_widget = QWidget()
        quarter_layout = QHBoxLayout(quarter_widget)
        quarter_layout.addWidget(QLabel("Trimestre:"))
        self.quarter_combo = QComboBox()
        self.quarter_combo.addItems(["T1 (Jan-Mar)", "T2 (Avr-Jun)", "T3 (Jul-Sep)", "T4 (Oct-Déc)"])
        current_quarter = (datetime.now().month - 1) // 3
        self.quarter_combo.setCurrentIndex(current_quarter)
        quarter_layout.addWidget(self.quarter_combo)
        self.period_stack.addTab(quarter_widget, "Trimestre")

        # Sélecteur d'année (commun à tous les types de rapports)
        report_layout.addWidget(QLabel("Année:"))
        self.year_combo = QComboBox()
        current_year = datetime.now().year
        self.year_combo.addItems([str(year) for year in range(current_year - 5, current_year + 1)])
        self.year_combo.setCurrentText(str(current_year))
        report_layout.addWidget(self.year_combo)

        # Bouton de génération
        self.generate_report_button = QPushButton("Générer")
        self.generate_report_button.clicked.connect(self._generate_report)
        report_layout.addWidget(self.generate_report_button)

        # Bouton d'exportation
        self.export_report_button = QPushButton("Exporter")
        self.export_report_button.clicked.connect(self._export_report)
        report_layout.addWidget(self.export_report_button)

        report_layout.addStretch()
        layout.addWidget(report_group)

        # Tableau de rapport
        self.report_table = ReportTable(["Métrique", "Valeur"])
        layout.addWidget(QLabel("Résumé du rapport:"))
        layout.addWidget(self.report_table)

        return tab

    def init_data(self):
        """Initialise les données de la vue de reporting"""
        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_data())
        finally:
            loop.close()

    async def load_data(self):
        """Charge les données de la vue de reporting"""
        self.loading_overlay.show()
        self.refresh_button.setEnabled(False)
        self.refresh_button.setText("Chargement...")

        success = True
        error_messages = []

        try:
            # Charger les KPIs
            print("Chargement des KPIs...")
            if not await self._load_kpis():
                success = False
                error_messages.append("Erreur lors du chargement des KPIs")

            # Charger les données des graphiques du tableau de bord
            print("Chargement des graphiques du tableau de bord...")
            if not await self._load_dashboard_charts():
                success = False
                error_messages.append("Erreur lors du chargement des graphiques du tableau de bord")

            # Charger les données initiales des onglets
            print("Chargement des données de réparation...")
            if not await self._load_repair_data():
                success = False
                error_messages.append("Erreur lors du chargement des données de réparation")

            print("Chargement des données d'inventaire...")
            if not await self._load_inventory_data():
                success = False
                error_messages.append("Erreur lors du chargement des données d'inventaire")

            print("Chargement des données d'équipement...")
            if not await self._load_equipment_data():
                success = False
                error_messages.append("Erreur lors du chargement des données d'équipement")

            if success:
                print("Chargement des données terminé avec succès")
            else:
                print(f"Chargement des données terminé avec des erreurs: {', '.join(error_messages)}")

        except Exception as e:
            print(f"Erreur générale lors du chargement des données: {e}")
            success = False
            error_messages.append(str(e))

        finally:
            self.refresh_button.setEnabled(True)
            self.refresh_button.setText("Rafraîchir")
            self.loading_overlay.hide()

            # Afficher un message d'erreur si nécessaire
            if not success:
                from PyQt6.QtWidgets import QMessageBox
                error_msg = QMessageBox()
                error_msg.setIcon(QMessageBox.Icon.Warning)
                error_msg.setWindowTitle("Erreur de chargement")
                error_msg.setText("Des erreurs sont survenues lors du chargement des données")
                error_msg.setDetailedText("\n".join(error_messages))
                error_msg.setStandardButtons(QMessageBox.StandardButton.Ok)
                error_msg.exec()

    async def _load_kpis(self):
        """Charge les KPIs"""
        try:
            kpis = await self.service.get_dashboard_kpis()

            # Utiliser l'animation pour mettre à jour les KPIs
            self.kpi_widgets["active_repairs"].update_with_animation(str(kpis["active_repairs"]))
            self.kpi_widgets["waiting_parts"].update_with_animation(str(kpis["waiting_parts"]))
            self.kpi_widgets["on_hold"].update_with_animation(str(kpis["on_hold"]))
            self.kpi_widgets["unpaid_repairs"].update_with_animation(str(kpis["unpaid_repairs"]))
            self.kpi_widgets["unpaid_amount"].update_with_animation(f"{kpis['unpaid_amount']:.2f} DA")
            self.kpi_widgets["monthly_revenue"].update_with_animation(f"{kpis['monthly_revenue']:.2f} DA")
            self.kpi_widgets["monthly_payments"].update_with_animation(f"{kpis['monthly_payments']:.2f} DA")
            self.kpi_widgets["low_stock"].update_with_animation(str(kpis["low_stock_items"]))
            self.kpi_widgets["upcoming_maintenance"].update_with_animation(str(kpis["upcoming_maintenance"]))
            self.kpi_widgets["pending_orders"].update_with_animation(str(kpis["pending_orders"]))

            print("KPIs mis à jour avec succès")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement des KPIs: {e}")
            return False

    async def _load_dashboard_charts(self):
        """Charge les données des graphiques du tableau de bord"""
        try:
            # Tendances des réparations
            repair_trends = await self.service.get_repair_trend_data(6)  # 6 derniers mois
            self.repair_trend_chart.update_data(repair_trends)

            # Distribution des statuts de réparation
            status_distribution = await self.service.get_repair_status_distribution()
            self.repair_status_chart.update_data(status_distribution)

            # Tendances d'inventaire
            inventory_trends = await self.service.get_inventory_trend_data(6)  # 6 derniers mois
            self.inventory_trend_chart.update_data(inventory_trends)

            # Équipements les plus réparés
            top_equipment = await self.service.get_top_repaired_equipment(5)  # Top 5
            self.top_equipment_chart.update_data(top_equipment)

            print("Graphiques du tableau de bord mis à jour avec succès")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement des graphiques du tableau de bord: {e}")
            return False

    async def _load_repair_data(self):
        """Charge les données de l'onglet Réparations"""
        try:
            # Récupérer les dates sélectionnées
            start_date = self.repair_start_date.date().toPyDate()
            end_date = self.repair_end_date.date().toPyDate()

            # Convertir en datetime
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())

            # Récupérer les statistiques de réparation
            print(f"Chargement des statistiques de réparation du {start_date} au {end_date}...")
            repair_stats = await self.service.get_repair_statistics(start_datetime, end_datetime)

            # Mettre à jour le tableau des statistiques
            self.repair_stats_table.clear_data()

            # Ajouter les statistiques de base
            self.repair_stats_table.add_row(["Total des réparations", str(repair_stats["total_repairs"])])

            # Ajouter les statistiques de statut
            self.repair_stats_table.add_row(["Réparations terminées", str(repair_stats["completed_repairs"])])
            self.repair_stats_table.add_row(["Réparations facturées", str(repair_stats["invoiced_repairs"])])
            self.repair_stats_table.add_row(["Réparations payées", str(repair_stats["paid_repairs"])])
            self.repair_stats_table.add_row(["En attente de pièces", str(repair_stats["waiting_parts_repairs"])])
            self.repair_stats_table.add_row(["Diagnostiquées", str(repair_stats["diagnosed_repairs"])])
            self.repair_stats_table.add_row(["En pause", str(repair_stats["on_hold_repairs"])])

            # Ajouter les statistiques de taux
            self.repair_stats_table.add_row(["Taux de complétion", f"{repair_stats['completion_rate']:.2f}%"])
            self.repair_stats_table.add_row(["Taux de paiement", f"{repair_stats['payment_rate']:.2f}%"])
            self.repair_stats_table.add_row(["Durée moyenne (heures)", f"{repair_stats['average_duration_hours']:.2f}"])

            # Ajouter les statistiques financières
            self.repair_stats_table.add_row(["Coût total", f"{repair_stats['total_cost']:.2f} DA"])
            self.repair_stats_table.add_row(["Coût moyen", f"{repair_stats['average_cost']:.2f} DA"])
            self.repair_stats_table.add_row(["Coût main d'œuvre", f"{repair_stats['total_labor_cost']:.2f} DA"])
            self.repair_stats_table.add_row(["Coût pièces", f"{repair_stats['total_parts_cost']:.2f} DA"])
            self.repair_stats_table.add_row(["Montant total", f"{repair_stats['total_final_amount']:.2f} DA"])
            self.repair_stats_table.add_row(["Montant moyen", f"{repair_stats['average_final_amount']:.2f} DA"])
            self.repair_stats_table.add_row(["Montant payé", f"{repair_stats['total_paid_amount']:.2f} DA"])
            self.repair_stats_table.add_row(["Montant restant", f"{repair_stats['remaining_amount']:.2f} DA"])
            self.repair_stats_table.add_row(["Remises totales", f"{repair_stats['total_discount_amount']:.2f} DA"])
            self.repair_stats_table.add_row(["Taxes totales", f"{repair_stats['total_tax_amount']:.2f} DA"], resize=True)

            # Récupérer les performances des techniciens
            print("Chargement des performances des techniciens...")
            technician_performance = await self.service.get_technician_performance(start_datetime, end_datetime)

            # Mettre à jour le tableau des performances des techniciens
            self.technician_performance_table.clear_data()
            for i, tech in enumerate(technician_performance):
                # Redimensionner uniquement après avoir ajouté la dernière ligne
                resize = (i == len(technician_performance) - 1)
                self.technician_performance_table.add_row([
                    f"Technicien #{tech['technician_id']}",
                    str(tech["total_repairs"]),
                    f"{tech['average_duration']:.2f} h",
                    f"{tech['total_cost']:.2f} DA",
                    f"{tech['total_labor_cost']:.2f} DA",
                    f"{tech['total_parts_cost']:.2f} DA",
                    f"{tech['total_final_amount']:.2f} DA",
                    f"{tech['total_paid']:.2f} DA",
                    f"{tech['payment_rate']:.2f}%",
                    f"{tech['repairs_per_hour']:.2f}",
                    f"{tech['profit_per_hour']:.2f} DA",
                    f"{tech['efficiency_score']:.2f}"
                ], resize=resize)

            # Mettre à jour les tableaux de statistiques par marque et modèle
            print("Mise à jour des statistiques par marque et modèle...")
            self.brand_stats_table.clear_data()
            brand_stats = repair_stats.get("brand_statistics", [])
            for i, brand_stat in enumerate(brand_stats):
                # Redimensionner uniquement après avoir ajouté la dernière ligne
                resize = (i == len(brand_stats) - 1)
                self.brand_stats_table.add_row([
                    brand_stat["brand"],
                    str(brand_stat["count"]),
                    f"{brand_stat['total_cost']:.2f} DA",
                    f"{brand_stat['total_final_amount']:.2f} DA"
                ], resize=resize)

            self.model_stats_table.clear_data()
            model_stats = repair_stats.get("model_statistics", [])
            for i, model_stat in enumerate(model_stats):
                # Redimensionner uniquement après avoir ajouté la dernière ligne
                resize = (i == len(model_stats) - 1)
                self.model_stats_table.add_row([
                    model_stat["model"],
                    str(model_stat["count"]),
                    f"{model_stat['total_cost']:.2f} DA",
                    f"{model_stat['total_final_amount']:.2f} DA"
                ], resize=resize)

            # Mettre à jour les graphiques
            print("Mise à jour des graphiques de réparation...")
            repair_trends = await self.service.get_repair_trend_data(6)
            self.repair_trend_detail_chart.update_data(repair_trends)

            status_distribution = await self.service.get_repair_status_distribution()
            self.repair_status_detail_chart.update_data(status_distribution)

            print("Données de réparation chargées avec succès")
            return True

        except Exception as e:
            print(f"Erreur lors du chargement des données de réparation: {e}")
            return False

    async def _load_inventory_data(self):
        """Charge les données de l'onglet Inventaire"""
        try:
            # Récupérer les dates sélectionnées
            start_date = self.inventory_start_date.date().toPyDate()
            end_date = self.inventory_end_date.date().toPyDate()

            # Convertir en datetime
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())

            # Récupérer les mouvements d'inventaire
            print(f"Chargement des mouvements d'inventaire du {start_date} au {end_date}...")
            inventory_movements = await self.service.get_inventory_movements(start_datetime, end_datetime)

            # Mettre à jour le tableau des mouvements d'inventaire
            self.inventory_movements_table.clear_data()
            for item in inventory_movements.get("movements_by_item", []):
                self.inventory_movements_table.add_row([
                    f"Article #{item['item_id']}",
                    str(item.get("in_quantity", 0)),
                    str(item.get("out_quantity", 0)),
                    str(item["total_quantity"])
                ])

            # Mettre à jour le graphique
            print("Mise à jour du graphique des tendances d'inventaire...")
            inventory_trends = await self.service.get_inventory_trend_data(6)
            self.inventory_trend_detail_chart.update_data(inventory_trends)

            print("Données d'inventaire chargées avec succès")
            return True

        except Exception as e:
            print(f"Erreur lors du chargement des données d'inventaire: {e}")
            return False

    async def _load_equipment_data(self):
        """Charge les données de l'onglet Équipements"""
        try:
            # Récupérer les dates sélectionnées
            start_date = self.equipment_start_date.date().toPyDate()
            end_date = self.equipment_end_date.date().toPyDate()

            # Convertir en datetime
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())

            # Récupérer la fiabilité des équipements
            print(f"Chargement des données de fiabilité des équipements du {start_date} au {end_date}...")
            equipment_reliability = await self.service.get_equipment_reliability(start_datetime, end_datetime)

            # Mettre à jour le tableau de fiabilité des équipements
            self.equipment_reliability_table.clear_data()
            for equip in equipment_reliability:
                self.equipment_reliability_table.add_row([
                    f"Équipement #{equip['equipment_id']}",
                    str(equip["failure_count"]),
                    f"{equip['average_downtime']:.2f} h",
                    f"{equip['total_maintenance_cost']:.2f} DA",
                    f"{equip['mtbf']:.2f}"
                ])

            # Mettre à jour le graphique
            print("Mise à jour du graphique des équipements les plus réparés...")
            top_equipment = await self.service.get_top_repaired_equipment(10)
            self.top_equipment_detail_chart.update_data(top_equipment)

            print("Données d'équipement chargées avec succès")
            return True

        except Exception as e:
            print(f"Erreur lors du chargement des données d'équipement: {e}")
            return False

    async def _generate_report(self):
        """Génère un rapport selon les paramètres sélectionnés"""
        self.loading_overlay.show()
        self.generate_report_button.setEnabled(False)
        self.generate_report_button.setText("Génération...")

        try:
            report_type = self.report_type_combo.currentText()
            year = int(self.year_combo.currentText())

            print(f"Génération d'un rapport {report_type} pour l'année {year}...")

            if report_type == "Mensuel":
                month = self.month_combo.currentIndex() + 1
                month_name = datetime(year, month, 1).strftime("%B")
                print(f"Génération du rapport pour {month_name} {year}...")
                report = await self.service.generate_monthly_report(year, month)
                self._display_report(report)

            elif report_type == "Trimestriel":
                quarter = self.quarter_combo.currentIndex() + 1
                print(f"Génération du rapport pour le trimestre {quarter} de {year}...")
                report = await self.service.generate_quarterly_report(year, quarter)
                self._display_report(report)

            elif report_type == "Annuel":
                print(f"Génération du rapport annuel pour {year}...")
                report = await self.service.generate_annual_report(year)
                self._display_report(report)

            print("Rapport généré avec succès")
            return True

        except Exception as e:
            print(f"Erreur lors de la génération du rapport: {e}")
            return False

        finally:
            self.generate_report_button.setEnabled(True)
            self.generate_report_button.setText("Générer")
            self.loading_overlay.hide()

    def _display_report(self, report):
        """Affiche les données du rapport dans le tableau"""
        self.report_table.clear_data()

        # Afficher la période
        period = report.get("period", {})
        if "month" in period:
            month_name = datetime(period["year"], period["month"], 1).strftime("%B")
            self.report_table.add_row(["Période", f"{month_name} {period['year']}"])
        elif "quarter" in period:
            self.report_table.add_row(["Période", f"T{period['quarter']} {period['year']}"])
        else:
            self.report_table.add_row(["Période", f"Année {period['year']}"])

        # Afficher les statistiques de réparation
        repair_stats = report.get("repair_statistics", {})
        self.report_table.add_row(["Total des réparations", str(repair_stats.get("total_repairs", 0))])
        self.report_table.add_row(["Réparations terminées", str(repair_stats.get("completed_repairs", 0))])
        self.report_table.add_row(["Taux de complétion", f"{repair_stats.get('completion_rate', 0):.2f}%"])

        # Afficher les statistiques d'inventaire
        inventory_stats = report.get("inventory_movements", {})
        self.report_table.add_row(["Total des mouvements", str(inventory_stats.get("total_movements", 0))])
        self.report_table.add_row(["Articles déplacés", str(inventory_stats.get("items_moved", 0))])

        # Afficher les statistiques de maintenance
        maintenance_stats = report.get("maintenance_compliance", {})
        self.report_table.add_row(["Maintenances planifiées", str(maintenance_stats.get("total_scheduled", 0))])
        self.report_table.add_row(["Taux de conformité", f"{maintenance_stats.get('compliance_rate', 0):.2f}%"])

    def _export_report(self):
        """Exporte le rapport au format CSV"""
        # TODO: Implémenter l'exportation du rapport
        pass

    def _on_repair_period_changed(self, _):
        """Gère le changement de période pour les réparations"""
        self._update_date_range(
            self.repair_period_combo.currentText(),
            self.repair_start_date,
            self.repair_end_date
        )

    def _on_inventory_period_changed(self, _):
        """Gère le changement de période pour l'inventaire"""
        self._update_date_range(
            self.inventory_period_combo.currentText(),
            self.inventory_start_date,
            self.inventory_end_date
        )

    def _on_equipment_period_changed(self, _):
        """Gère le changement de période pour les équipements"""
        self._update_date_range(
            self.equipment_period_combo.currentText(),
            self.equipment_start_date,
            self.equipment_end_date
        )

    def _update_date_range(self, period_text, start_date_edit, end_date_edit):
        """Met à jour les champs de date en fonction de la période sélectionnée"""
        today = QDate.currentDate()

        if period_text == "7 derniers jours":
            start_date_edit.setDate(today.addDays(-7))
        elif period_text == "30 derniers jours":
            start_date_edit.setDate(today.addDays(-30))
        elif period_text == "3 derniers mois":
            start_date_edit.setDate(today.addMonths(-3))
        elif period_text == "6 derniers mois":
            start_date_edit.setDate(today.addMonths(-6))
        elif period_text == "12 derniers mois":
            start_date_edit.setDate(today.addMonths(-12))

        end_date_edit.setDate(today)

    def _create_finances_tab(self):
        """Crée l'onglet Finances"""
        # Utiliser la vue complète des rapports financiers
        return FinancialReportingView()

    def _create_alerts_tab(self):
        """Crée l'onglet Alertes"""
        # Utiliser directement le widget d'alertes
        self.alerts_widget = AlertsWidget()

        # Charger les alertes initiales
        QTimer.singleShot(400, self.alerts_widget.load_alerts)

        return self.alerts_widget

    def refresh(self):
        """Rafraîchit toutes les données de la vue"""
        # Rafraîchir la session de base de données
        self.service.refresh_session()

        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)

    def start_auto_refresh(self, interval_ms=60000):
        """Démarre le rafraîchissement automatique des données

        Args:
            interval_ms: Intervalle de rafraîchissement en millisecondes (par défaut: 60 secondes)
        """
        self.refresh_timer.start(interval_ms)
        print(f"Rafraîchissement automatique démarré (intervalle: {interval_ms/1000} secondes)")

    def stop_auto_refresh(self):
        """Arrête le rafraîchissement automatique des données"""
        if self.refresh_timer.isActive():
            self.refresh_timer.stop()
            print("Rafraîchissement automatique arrêté")

    def _on_auto_refresh_changed(self, index):
        """Gère le changement d'intervalle de rafraîchissement automatique"""
        # Arrêter le timer actuel
        self.stop_auto_refresh()

        # Configurer le nouvel intervalle
        if index == 0:  # Désactivé
            print("Rafraîchissement automatique désactivé")
            return
        elif index == 1:  # 30 secondes
            interval = 30 * 1000
        elif index == 2:  # 1 minute
            interval = 60 * 1000
        elif index == 3:  # 5 minutes
            interval = 5 * 60 * 1000
        elif index == 4:  # 15 minutes
            interval = 15 * 60 * 1000
        else:
            interval = 60 * 1000  # Par défaut: 1 minute

        # Démarrer le timer avec le nouvel intervalle
        self.start_auto_refresh(interval)
