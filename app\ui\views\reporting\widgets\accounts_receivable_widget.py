"""
Widget pour afficher le rapport des comptes clients.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QGridLayout, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon, QColor
import asyncio
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

from app.core.services.financial_reporting_service import FinancialReportingService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class AccountsReceivableWidget(QWidget):
    """Widget pour afficher le rapport des comptes clients"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Service
        self.db = SessionLocal()
        self.service = FinancialReportingService(self.db)

        # Données
        self.report_data = None

        # Configuration de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.load_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Bouton de rafraîchissement
        refresh_layout = QHBoxLayout()
        refresh_layout.addStretch()

        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.clicked.connect(self.load_data)
        refresh_layout.addWidget(self.refresh_button)

        main_layout.addLayout(refresh_layout)

        # Résumé du rapport
        summary_group = QGroupBox("Résumé des comptes clients")
        summary_layout = QGridLayout(summary_group)

        # Montant total à recevoir
        total_label = QLabel("Montant total à recevoir:")
        total_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(total_label, 0, 0)

        self.total_value = QLabel("0.00 DA")
        self.total_value.setStyleSheet("font-size: 16px; color: #2196F3;")
        summary_layout.addWidget(self.total_value, 0, 1)

        # Nombre de ventes en attente
        pending_label = QLabel("Nombre de ventes en attente:")
        pending_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(pending_label, 1, 0)

        self.pending_value = QLabel("0")
        self.pending_value.setStyleSheet("font-size: 16px;")
        summary_layout.addWidget(self.pending_value, 1, 1)

        main_layout.addWidget(summary_group)

        # Tableau des comptes clients
        table_group = QGroupBox("Détail par client")
        table_layout = QVBoxLayout(table_group)

        self.receivables_table = QTableWidget()
        self.receivables_table.setColumnCount(4)
        self.receivables_table.setHorizontalHeaderLabels([
            "Client", "Montant dû", "Nombre de ventes", "Dernier achat"
        ])
        self.receivables_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.receivables_table.setAlternatingRowColors(True)
        self.receivables_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.receivables_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        table_layout.addWidget(self.receivables_table)

        main_layout.addWidget(table_group)

        # Graphique
        chart_group = QGroupBox("Répartition des comptes clients")
        chart_layout = QVBoxLayout(chart_group)

        # Créer la figure Matplotlib
        self.figure = Figure(figsize=(8, 4), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        chart_layout.addWidget(self.canvas)

        main_layout.addWidget(chart_group)

    def load_data(self):
        """Charge les données du rapport"""
        self.loading_overlay.show()

        # Utiliser QTimer pour exécuter le chargement de manière asynchrone
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data_async de manière asynchrone"""
        try:
            # Essayer d'utiliser le loop existant
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Si le loop tourne déjà, utiliser create_task
                task = asyncio.create_task(self._load_data_async())
                # Connecter le callback pour la fin de la tâche
                task.add_done_callback(self._on_load_complete)
            else:
                # Si pas de loop en cours, en créer un nouveau
                loop.run_until_complete(self._load_data_async())
        except RuntimeError:
            # Fallback: créer un nouveau loop dans un thread séparé
            import threading
            thread = threading.Thread(target=self._load_data_in_thread)
            thread.daemon = True
            thread.start()

    def _on_load_complete(self, task):
        """Callback appelé quand la tâche asynchrone est terminée"""
        try:
            # Récupérer le résultat ou l'exception
            task.result()
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Masquer l'overlay de chargement dans le thread principal
            QTimer.singleShot(0, self.loading_overlay.hide)

    def _load_data_in_thread(self):
        """Charge les données dans un thread séparé"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self._load_data_async())
            finally:
                loop.close()
        except Exception as e:
            print(f"Erreur lors du chargement des données dans le thread: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Masquer l'overlay de chargement dans le thread principal
            QTimer.singleShot(0, self.loading_overlay.hide)

    async def _load_data_async(self):
        """Charge les données du rapport de manière asynchrone"""
        try:
            # Générer le rapport
            self.report_data = await self.service.generate_accounts_receivable_report()

            # Mettre à jour l'interface dans le thread principal
            QTimer.singleShot(0, self.update_ui)

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des données: {str(e)}"
            )
        finally:
            # Masquer l'overlay dans le thread principal
            QTimer.singleShot(0, self.loading_overlay.hide)

    def update_ui(self):
        """Met à jour l'interface avec les données du rapport"""
        if not self.report_data:
            return

        # Mettre à jour les valeurs
        self.total_value.setText(f"{self.report_data['total_receivable']:.2f} DA")
        self.pending_value.setText(str(self.report_data['total_pending_sales']))

        # Remplir le tableau
        receivables = self.report_data['receivables_by_customer']
        self.receivables_table.setRowCount(len(receivables))

        for i, receivable in enumerate(receivables):
            # Client
            self.receivables_table.setItem(i, 0, QTableWidgetItem(receivable['customer_name']))

            # Montant dû
            amount_item = QTableWidgetItem(f"{receivable['total_amount']:.2f} DA")
            amount_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.receivables_table.setItem(i, 1, amount_item)

            # Nombre de ventes
            self.receivables_table.setItem(i, 2, QTableWidgetItem(str(receivable['sales_count'])))

            # Dernier achat
            if receivable.get('last_sale_date'):
                date_str = receivable['last_sale_date'].strftime("%d/%m/%Y")
            else:
                date_str = "N/A"
            self.receivables_table.setItem(i, 3, QTableWidgetItem(date_str))

        # Mettre à jour le graphique
        self.update_chart()

    def update_chart(self):
        """Met à jour le graphique avec les données du rapport"""
        if not self.report_data or not self.report_data['receivables_by_customer']:
            return

        # Effacer la figure
        self.figure.clear()

        # Créer un graphique à secteurs
        ax = self.figure.add_subplot(111)

        # Récupérer les 5 premiers clients (ou moins s'il y en a moins)
        top_receivables = self.report_data['receivables_by_customer'][:5]

        # Vérifier si les données sont vides
        if not top_receivables:
            # Afficher un message si aucune donnée
            ax.text(0.5, 0.5, "Aucune donnée disponible",
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)
            ax.axis('off')
            self.figure.tight_layout()
            self.canvas.draw()
            return

        # Calculer le total des autres clients
        other_amount = 0
        if len(self.report_data['receivables_by_customer']) > 5:
            other_receivables = self.report_data['receivables_by_customer'][5:]
            other_amount = sum(r['total_amount'] for r in other_receivables)

        # Données pour le graphique
        labels = [r['customer_name'] for r in top_receivables]
        if other_amount > 0:
            labels.append("Autres")

        values = [r['total_amount'] for r in top_receivables]
        if other_amount > 0:
            values.append(other_amount)

        # Vérifier si toutes les valeurs sont nulles ou négatives
        if not values or all(v <= 0 for v in values):
            # Afficher un message si toutes les valeurs sont nulles ou négatives
            ax.text(0.5, 0.5, "Aucune donnée positive disponible",
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)
            ax.axis('off')
            self.figure.tight_layout()
            self.canvas.draw()
            return

        # Créer le graphique à secteurs
        try:
            ax.pie(
                values,
                labels=labels,
                autopct='%1.1f%%',
                startangle=90,
                shadow=True
            )
        except Exception as e:
            print(f"Erreur lors de la création du graphique à secteurs: {str(e)}")
            # Afficher un message d'erreur dans le graphique
            ax.clear()
            ax.text(0.5, 0.5, f"Erreur: {str(e)}",
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)
            ax.axis('off')

        # Égaliser les axes pour obtenir un cercle
        ax.axis('equal')

        # Titre
        ax.set_title('Répartition des comptes clients')

        # Ajuster la mise en page
        self.figure.tight_layout()

        # Redessiner le canvas
        self.canvas.draw()
