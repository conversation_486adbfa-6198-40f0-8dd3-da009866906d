"""
Widget d'alertes et notifications pour les rapports.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QComboBox,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QGridLayout, QMessageBox, QScrollArea, QTextEdit
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QColor, QFont, QPalette
import asyncio
from datetime import datetime, timedelta
from enum import Enum

from app.core.services.reporting_service import ReportingService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class AlertLevel(Enum):
    """Niveaux d'alerte"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    SUCCESS = "success"

class AlertCard(QFrame):
    """Carte d'alerte individuelle"""
    
    dismissed = pyqtSignal(str)  # Signal émis quand l'alerte est fermée
    
    def __init__(self, alert_id, title, message, level=AlertLevel.INFO, timestamp=None, parent=None):
        super().__init__(parent)
        self.alert_id = alert_id
        self.title = title
        self.message = message
        self.level = level
        self.timestamp = timestamp or datetime.now()
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface de la carte d'alerte"""
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        
        # Couleurs selon le niveau
        colors = {
            AlertLevel.INFO: {"bg": "#E3F2FD", "border": "#2196F3", "icon": "ℹ️"},
            AlertLevel.WARNING: {"bg": "#FFF3E0", "border": "#FF9800", "icon": "⚠️"},
            AlertLevel.CRITICAL: {"bg": "#FFEBEE", "border": "#F44336", "icon": "🚨"},
            AlertLevel.SUCCESS: {"bg": "#E8F5E8", "border": "#4CAF50", "icon": "✅"}
        }
        
        color_config = colors.get(self.level, colors[AlertLevel.INFO])
        
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {color_config['bg']};
                border-left: 4px solid {color_config['border']};
                border-radius: 4px;
                padding: 8px;
                margin: 2px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(5)
        
        # En-tête avec titre et bouton de fermeture
        header_layout = QHBoxLayout()
        
        # Icône et titre
        title_layout = QHBoxLayout()
        icon_label = QLabel(color_config['icon'])
        icon_label.setStyleSheet("font-size: 16px;")
        title_layout.addWidget(icon_label)
        
        title_label = QLabel(self.title)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        header_layout.addLayout(title_layout)
        
        # Bouton de fermeture
        close_button = QPushButton("×")
        close_button.setFixedSize(20, 20)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                font-size: 16px;
                font-weight: bold;
                color: #666;
            }
            QPushButton:hover {
                background-color: rgba(0,0,0,0.1);
                border-radius: 10px;
            }
        """)
        close_button.clicked.connect(self.dismiss_alert)
        header_layout.addWidget(close_button)
        
        layout.addLayout(header_layout)
        
        # Message
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("color: #333; font-size: 12px;")
        layout.addWidget(message_label)
        
        # Timestamp
        time_label = QLabel(self.timestamp.strftime("%d/%m/%Y %H:%M"))
        time_label.setStyleSheet("color: #666; font-size: 10px;")
        layout.addWidget(time_label)
    
    def dismiss_alert(self):
        """Ferme l'alerte"""
        self.dismissed.emit(self.alert_id)
        self.hide()

class AlertsWidget(QWidget):
    """Widget principal pour les alertes et notifications"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.service = None
        self.alerts = {}
        self.setup_ui()
        self.init_service()
    
    def init_service(self):
        """Initialise le service de reporting"""
        try:
            db = SessionLocal()
            self.service = ReportingService(db)
        except Exception as e:
            print(f"Erreur lors de l'initialisation du service: {e}")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Alertes et Notifications")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        header_layout.addWidget(title_label)
        
        # Filtre par niveau
        header_layout.addWidget(QLabel("Niveau:"))
        self.level_filter = QComboBox()
        self.level_filter.addItems(["Tous", "Critique", "Avertissement", "Information", "Succès"])
        self.level_filter.currentTextChanged.connect(self.filter_alerts)
        header_layout.addWidget(self.level_filter)
        
        # Boutons d'action
        refresh_button = QPushButton("Rafraîchir")
        refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        refresh_button.clicked.connect(self.load_alerts)
        header_layout.addWidget(refresh_button)
        
        clear_button = QPushButton("Tout effacer")
        clear_button.setIcon(QIcon("app/ui/resources/icons/clear.svg"))
        clear_button.clicked.connect(self.clear_all_alerts)
        header_layout.addWidget(clear_button)
        
        header_layout.addStretch()
        main_layout.addLayout(header_layout)
        
        # Zone de défilement pour les alertes
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        
        # Widget contenant toutes les alertes
        self.alerts_widget = QWidget()
        self.alerts_layout = QVBoxLayout(self.alerts_widget)
        self.alerts_layout.setSpacing(5)
        self.alerts_layout.addStretch()  # Pour pousser les alertes vers le haut
        
        self.scroll_area.setWidget(self.alerts_widget)
        main_layout.addWidget(self.scroll_area)
        
        # Message quand aucune alerte
        self.no_alerts_label = QLabel("Aucune alerte à afficher")
        self.no_alerts_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.no_alerts_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
        self.no_alerts_label.hide()
        main_layout.addWidget(self.no_alerts_label)
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()
        
        # Charger les alertes initiales
        QTimer.singleShot(100, self.load_alerts)
        
        # Timer pour rafraîchir automatiquement
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.load_alerts)
        self.auto_refresh_timer.start(60000)  # Rafraîchir toutes les minutes
    
    def load_alerts(self):
        """Charge les alertes"""
        QTimer.singleShot(0, self._load_alerts_async)
    
    def _load_alerts_async(self):
        """Charge les alertes de manière asynchrone"""
        from PyQt6.QtCore import QThread, pyqtSignal

        class AlertsLoaderThread(QThread):
            finished_loading = pyqtSignal()
            error_occurred = pyqtSignal(str)

            def __init__(self, parent_widget):
                super().__init__()
                self.parent_widget = parent_widget

            def run(self):
                try:
                    # Utiliser un nouvel event loop dans ce thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(self.parent_widget._load_alerts())
                        self.finished_loading.emit()
                    finally:
                        loop.close()
                except Exception as e:
                    self.error_occurred.emit(str(e))

        # Créer et lancer le thread
        if not hasattr(self, 'loader_thread') or not self.loader_thread.isRunning():
            self.loader_thread = AlertsLoaderThread(self)
            self.loader_thread.finished_loading.connect(self._on_loading_finished)
            self.loader_thread.error_occurred.connect(self._on_loading_error)
            self.loader_thread.start()

    def _on_loading_finished(self):
        """Gère la fin du chargement"""
        print("Chargement des alertes terminé")

    def _on_loading_error(self, error_message):
        """Gère les erreurs de chargement"""
        print(f"Erreur lors du chargement des alertes: {error_message}")
        if hasattr(self, 'loading_overlay'):
            self.loading_overlay.hide()
    
    async def _load_alerts(self):
        """Charge les alertes depuis le service"""
        if not self.service:
            return
        
        self.loading_overlay.show()
        
        try:
            # Générer des alertes basées sur les données
            alerts_data = await self.generate_alerts()
            
            # Effacer les alertes existantes
            self.clear_alerts_display()
            
            # Ajouter les nouvelles alertes
            for alert_data in alerts_data:
                self.add_alert(
                    alert_data['id'],
                    alert_data['title'],
                    alert_data['message'],
                    alert_data['level'],
                    alert_data.get('timestamp')
                )
            
            # Afficher le message si aucune alerte
            if not alerts_data:
                self.no_alerts_label.show()
                self.scroll_area.hide()
            else:
                self.no_alerts_label.hide()
                self.scroll_area.show()
        
        except Exception as e:
            print(f"Erreur lors du chargement des alertes: {e}")
        
        finally:
            self.loading_overlay.hide()
    
    async def generate_alerts(self):
        """Génère des alertes basées sur les données du système"""
        alerts = []
        
        try:
            # Récupérer les KPIs pour détecter les problèmes
            kpis = await self.service.get_dashboard_kpis()
            
            # Alerte pour les réparations en attente de pièces
            if kpis.get('waiting_parts', 0) > 5:
                alerts.append({
                    'id': 'waiting_parts_high',
                    'title': 'Réparations en attente de pièces',
                    'message': f"{kpis['waiting_parts']} réparations sont en attente de pièces. Vérifiez les commandes de stock.",
                    'level': AlertLevel.WARNING,
                    'timestamp': datetime.now()
                })
            
            # Alerte pour les réparations impayées
            if kpis.get('unpaid_amount', 0) > 50000:
                alerts.append({
                    'id': 'unpaid_amount_high',
                    'title': 'Montant impayé élevé',
                    'message': f"Le montant total impayé s'élève à {kpis['unpaid_amount']:.2f} DA. Relancez les clients.",
                    'level': AlertLevel.CRITICAL,
                    'timestamp': datetime.now()
                })
            
            # Alerte pour le stock faible
            if kpis.get('low_stock_items', 0) > 0:
                alerts.append({
                    'id': 'low_stock',
                    'title': 'Stock faible détecté',
                    'message': f"{kpis['low_stock_items']} articles ont un stock faible. Planifiez un réapprovisionnement.",
                    'level': AlertLevel.WARNING,
                    'timestamp': datetime.now()
                })
            
            # Alerte positive pour les performances
            if kpis.get('monthly_revenue', 0) > 100000:
                alerts.append({
                    'id': 'revenue_good',
                    'title': 'Excellent chiffre d\'affaires',
                    'message': f"Le chiffre d'affaires mensuel atteint {kpis['monthly_revenue']:.2f} DA. Félicitations !",
                    'level': AlertLevel.SUCCESS,
                    'timestamp': datetime.now()
                })
        
        except Exception as e:
            print(f"Erreur lors de la génération des alertes: {e}")
        
        return alerts
    
    def add_alert(self, alert_id, title, message, level, timestamp=None):
        """Ajoute une nouvelle alerte"""
        if alert_id in self.alerts:
            return  # Alerte déjà présente
        
        alert_card = AlertCard(alert_id, title, message, level, timestamp)
        alert_card.dismissed.connect(self.remove_alert)
        
        # Insérer au début de la liste (les plus récentes en haut)
        self.alerts_layout.insertWidget(0, alert_card)
        self.alerts[alert_id] = alert_card
    
    def remove_alert(self, alert_id):
        """Supprime une alerte"""
        if alert_id in self.alerts:
            alert_card = self.alerts[alert_id]
            self.alerts_layout.removeWidget(alert_card)
            alert_card.deleteLater()
            del self.alerts[alert_id]
            
            # Afficher le message si plus d'alertes
            if not self.alerts:
                self.no_alerts_label.show()
                self.scroll_area.hide()
    
    def clear_all_alerts(self):
        """Efface toutes les alertes"""
        for alert_id in list(self.alerts.keys()):
            self.remove_alert(alert_id)
    
    def clear_alerts_display(self):
        """Efface l'affichage des alertes sans les supprimer du dictionnaire"""
        for alert_card in self.alerts.values():
            self.alerts_layout.removeWidget(alert_card)
            alert_card.deleteLater()
        self.alerts.clear()
    
    def filter_alerts(self, level_text):
        """Filtre les alertes par niveau"""
        level_mapping = {
            "Critique": AlertLevel.CRITICAL,
            "Avertissement": AlertLevel.WARNING,
            "Information": AlertLevel.INFO,
            "Succès": AlertLevel.SUCCESS
        }
        
        target_level = level_mapping.get(level_text)
        
        for alert_card in self.alerts.values():
            if level_text == "Tous" or alert_card.level == target_level:
                alert_card.show()
            else:
                alert_card.hide()
