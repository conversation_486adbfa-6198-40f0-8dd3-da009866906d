from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QFrame
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QPainter, QColor, QPen, QBrush, QFont, QLinearGradient, QGradient
import math

class BaseChartWidget(QFrame):
    """Widget de base pour les graphiques"""

    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.title = title
        self.data = None

        self.setMinimumSize(300, 200)
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)
        self.setObjectName("chartWidget")

        layout = QVBoxLayout(self)

        # Titre du graphique
        if title:
            title_label = QLabel(title)
            title_label.setObjectName("chartTitle")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(title_label)

        self.setLayout(layout)

    def update_data(self, data):
        """Met à jour les données du graphique"""
        self.data = data
        self.update()

    def paintEvent(self, event):
        """Dessine le graphique"""
        super().paintEvent(event)

        if not self.data:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Dessiner le graphique
        self._draw_chart(painter)

    def _draw_chart(self, painter):
        """Méthode à implémenter dans les classes dérivées"""
        pass

class RepairTrendChart(BaseChartWidget):
    """Graphique de tendance des réparations"""

    def __init__(self, parent=None):
        super().__init__("Tendance des réparations", parent)

    def _draw_chart(self, painter):
        """Dessine un graphique en ligne des réparations"""
        if not self.data:
            return

        # Dimensions du graphique
        padding = 40
        width = self.width() - 2 * padding
        height = self.height() - 2 * padding - 30  # Espace pour le titre

        # Trouver les valeurs min et max
        max_value = 0
        for month_data in self.data:
            total = month_data.get("total", 0)
            max_value = max(max_value, total)

        # Ajouter une marge de 10%
        max_value = max_value * 1.1 if max_value > 0 else 10

        # Dessiner les axes
        painter.setPen(QPen(QColor("#666666"), 1))

        # Axe X
        x_start = padding
        y_axis = self.height() - padding
        painter.drawLine(x_start, y_axis, x_start + width, y_axis)

        # Axe Y
        painter.drawLine(x_start, y_axis, x_start, padding)

        # Dessiner les graduations et les étiquettes de l'axe Y
        painter.setPen(QPen(QColor("#999999"), 1, Qt.PenStyle.DotLine))
        font = QFont("Arial", 8)
        painter.setFont(font)

        for i in range(5):
            y = int(y_axis - (i + 1) * height / 5)
            painter.drawLine(x_start, y, x_start + width, y)

            value = (i + 1) * max_value / 5
            painter.drawText(5, y + 4, f"{int(value)}")

        # Dessiner les données
        if len(self.data) > 1:
            # Largeur de chaque barre
            bar_width = width / len(self.data) * 0.8
            gap = width / len(self.data) * 0.2

            for i, month_data in enumerate(self.data):
                # Position X de la barre
                x = x_start + i * (bar_width + gap) + gap / 2

                # Utiliser les groupes de statuts pour simplifier l'affichage
                finished = month_data.get("finished", 0)
                in_progress_group = month_data.get("in_progress_group", 0)
                other_group = month_data.get("other_group", 0)

                # Hauteurs des barres
                finished_height = (finished / max_value) * height if max_value > 0 else 0
                in_progress_height = (in_progress_group / max_value) * height if max_value > 0 else 0
                other_height = (other_group / max_value) * height if max_value > 0 else 0

                # Dessiner les barres empilées
                # Barre des réparations terminées (terminées + facturées + payées)
                painter.setBrush(QBrush(QColor("#4CAF50")))  # Vert
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawRect(int(x), int(y_axis - finished_height), int(bar_width), int(finished_height))

                # Barre des réparations en cours (en cours + diagnostiquées + en attente de pièces)
                painter.setBrush(QBrush(QColor("#2196F3")))  # Bleu
                painter.drawRect(int(x), int(y_axis - finished_height - in_progress_height),
                                int(bar_width), int(in_progress_height))

                # Barre des autres réparations (en attente + annulées + en pause)
                painter.setBrush(QBrush(QColor("#FFC107")))  # Jaune
                painter.drawRect(int(x), int(y_axis - finished_height - in_progress_height - other_height),
                                int(bar_width), int(other_height))

                # Étiquette du mois
                painter.setPen(QPen(QColor("#666666"), 1))
                painter.drawText(int(x), int(y_axis + 15), int(bar_width), 20,
                                Qt.AlignmentFlag.AlignCenter, month_data.get("month_name", ""))

        # Légende
        legend_y = int(padding / 2)
        legend_x = int(padding)

        # Terminées, facturées, payées
        painter.setBrush(QBrush(QColor("#4CAF50")))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(legend_x, legend_y, 15, 15)
        painter.setPen(QPen(QColor("#666666"), 1))
        painter.drawText(legend_x + 20, legend_y, 100, 15, Qt.AlignmentFlag.AlignVCenter, "Terminées")

        # En cours, diagnostiquées, en attente de pièces
        legend_x += 100
        painter.setBrush(QBrush(QColor("#2196F3")))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(legend_x, legend_y, 15, 15)
        painter.setPen(QPen(QColor("#666666"), 1))
        painter.drawText(legend_x + 20, legend_y, 100, 15, Qt.AlignmentFlag.AlignVCenter, "En cours")

        # En attente, annulées, en pause
        legend_x += 100
        painter.setBrush(QBrush(QColor("#FFC107")))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(legend_x, legend_y, 15, 15)
        painter.setPen(QPen(QColor("#666666"), 1))
        painter.drawText(legend_x + 20, legend_y, 100, 15, Qt.AlignmentFlag.AlignVCenter, "Autres")

class InventoryTrendChart(BaseChartWidget):
    """Graphique de tendance des mouvements d'inventaire"""

    def __init__(self, parent=None):
        super().__init__("Mouvements d'inventaire", parent)

    def _draw_chart(self, painter):
        """Dessine un graphique en ligne des mouvements d'inventaire"""
        if not self.data:
            return

        # Dimensions du graphique
        padding = 40
        width = self.width() - 2 * padding
        height = self.height() - 2 * padding - 30  # Espace pour le titre

        # Trouver les valeurs min et max
        max_value = 0
        for month_data in self.data:
            in_quantity = month_data.get("in_quantity", 0)
            out_quantity = month_data.get("out_quantity", 0)
            max_value = max(max_value, in_quantity, out_quantity)

        # Ajouter une marge de 10%
        max_value = max_value * 1.1 if max_value > 0 else 10

        # Dessiner les axes
        painter.setPen(QPen(QColor("#666666"), 1))

        # Axe X
        x_start = padding
        y_axis = self.height() - padding
        painter.drawLine(x_start, y_axis, x_start + width, y_axis)

        # Axe Y
        painter.drawLine(x_start, y_axis, x_start, padding)

        # Dessiner les graduations et les étiquettes de l'axe Y
        painter.setPen(QPen(QColor("#999999"), 1, Qt.PenStyle.DotLine))
        font = QFont("Arial", 8)
        painter.setFont(font)

        for i in range(5):
            y = int(y_axis - (i + 1) * height / 5)
            painter.drawLine(x_start, y, x_start + width, y)

            value = (i + 1) * max_value / 5
            painter.drawText(5, y + 4, f"{int(value)}")

        # Dessiner les données
        if len(self.data) > 1:
            # Largeur de chaque groupe de barres
            group_width = width / len(self.data)
            bar_width = group_width * 0.4

            for i, month_data in enumerate(self.data):
                # Position X du groupe
                x = x_start + i * group_width + group_width * 0.1

                # Valeurs
                in_quantity = month_data.get("in_quantity", 0)
                out_quantity = month_data.get("out_quantity", 0)

                # Hauteurs des barres
                in_height = (in_quantity / max_value) * height if max_value > 0 else 0
                out_height = (out_quantity / max_value) * height if max_value > 0 else 0

                # Dessiner la barre des entrées
                painter.setBrush(QBrush(QColor("#4CAF50")))
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawRect(int(x), int(y_axis - in_height), int(bar_width), int(in_height))

                # Dessiner la barre des sorties
                painter.setBrush(QBrush(QColor("#F44336")))
                painter.drawRect(int(x + bar_width), int(y_axis - out_height), int(bar_width), int(out_height))

                # Étiquette du mois
                painter.setPen(QPen(QColor("#666666"), 1))
                painter.drawText(int(x), int(y_axis + 15), int(group_width), 20,
                                Qt.AlignmentFlag.AlignCenter, month_data.get("month_name", ""))

        # Légende
        legend_y = int(padding / 2)
        legend_x = int(padding)

        # Entrées
        painter.setBrush(QBrush(QColor("#4CAF50")))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(legend_x, legend_y, 15, 15)
        painter.setPen(QPen(QColor("#666666"), 1))
        painter.drawText(legend_x + 20, legend_y, 100, 15, Qt.AlignmentFlag.AlignVCenter, "Entrées")

        # Sorties
        legend_x += 100
        painter.setBrush(QBrush(QColor("#F44336")))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(legend_x, legend_y, 15, 15)
        painter.setPen(QPen(QColor("#666666"), 1))
        painter.drawText(legend_x + 20, legend_y, 100, 15, Qt.AlignmentFlag.AlignVCenter, "Sorties")

class RepairStatusChart(BaseChartWidget):
    """Graphique de distribution des statuts de réparation"""

    def __init__(self, parent=None):
        super().__init__("Statuts des réparations", parent)

    def _draw_chart(self, painter):
        """Dessine un graphique en camembert des statuts de réparation"""
        if not self.data:
            return

        # Dimensions du graphique
        center_x = self.width() / 2
        center_y = self.height() / 2
        radius = min(center_x, center_y) - 50

        # Calculer le total
        total = sum(self.data.values())

        if total == 0:
            return

        # Couleurs pour chaque statut
        colors = {
            "pending": QColor("#FFC107"),      # Jaune
            "in_progress": QColor("#2196F3"),  # Bleu
            "diagnosed": QColor("#03A9F4"),    # Bleu clair
            "waiting_parts": QColor("#00BCD4"), # Cyan
            "completed": QColor("#4CAF50"),    # Vert
            "invoiced": QColor("#8BC34A"),     # Vert clair
            "paid": QColor("#009688"),         # Vert-bleu
            "cancelled": QColor("#F44336"),    # Rouge
            "on_hold": QColor("#FF9800")       # Orange
        }

        # Dessiner les secteurs
        start_angle = 0

        for status, count in self.data.items():
            if count > 0:
                # Calculer l'angle du secteur
                angle = 360 * count / total

                # Dessiner le secteur
                painter.setBrush(QBrush(colors.get(status, QColor("#9E9E9E"))))
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawPie(int(center_x - radius), int(center_y - radius),
                               int(radius * 2), int(radius * 2),
                               int(start_angle * 16), int(angle * 16))

                # Mettre à jour l'angle de départ pour le prochain secteur
                start_angle += angle

        # Dessiner un cercle blanc au centre pour créer un effet de donut
        painter.setBrush(QBrush(QColor("white")))
        painter.drawEllipse(int(center_x - radius / 2), int(center_y - radius / 2),
                           int(radius), int(radius))

        # Dessiner le total au centre
        painter.setPen(QPen(QColor("#333333"), 1))
        font = QFont("Arial", 12, QFont.Weight.Bold)
        painter.setFont(font)
        painter.drawText(int(center_x - radius / 2), int(center_y - radius / 2),
                        int(radius), int(radius),
                        Qt.AlignmentFlag.AlignCenter, f"Total\n{total}")

        # Légende
        legend_y = int(self.height() - 30)
        legend_x = 20

        font = QFont("Arial", 8)
        painter.setFont(font)

        for status, color in colors.items():
            if status in self.data and self.data[status] > 0:
                # Calculer le pourcentage
                percentage = self.data[status] / total * 100

                # Dessiner le carré de couleur
                painter.setBrush(QBrush(color))
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawRect(legend_x, legend_y, 10, 10)

                # Dessiner le texte
                painter.setPen(QPen(QColor("#666666"), 1))
                status_text = status.replace("_", " ").title()
                painter.drawText(legend_x + 15, legend_y - 2, 150, 15,
                                Qt.AlignmentFlag.AlignVCenter, f"{status_text}: {percentage:.1f}%")

                # Mettre à jour la position X pour le prochain élément de légende
                legend_x += 150

class TopEquipmentChart(BaseChartWidget):
    """Graphique des équipements les plus réparés"""

    def __init__(self, parent=None):
        super().__init__("Équipements les plus réparés", parent)

    def _draw_chart(self, painter):
        """Dessine un graphique à barres horizontales des équipements les plus réparés"""
        if not self.data:
            return

        # Dimensions du graphique
        padding = 40
        width = self.width() - 2 * padding - 100  # Espace pour les étiquettes
        height = self.height() - 2 * padding - 30  # Espace pour le titre

        # Trouver la valeur max
        max_value = 0
        for equip in self.data:
            max_value = max(max_value, equip.get("repair_count", 0))

        # Ajouter une marge de 10%
        max_value = max_value * 1.1 if max_value > 0 else 10

        # Dessiner les axes
        painter.setPen(QPen(QColor("#666666"), 1))

        # Axe X
        x_start = padding + 100  # Espace pour les étiquettes
        y_axis = self.height() - padding
        painter.drawLine(x_start, y_axis, x_start + width, y_axis)

        # Axe Y
        painter.drawLine(x_start, y_axis, x_start, padding)

        # Dessiner les graduations et les étiquettes de l'axe X
        painter.setPen(QPen(QColor("#999999"), 1, Qt.PenStyle.DotLine))
        font = QFont("Arial", 8)
        painter.setFont(font)

        for i in range(5):
            x = x_start + (i + 1) * width / 5
            painter.drawLine(x, padding, x, y_axis)

            value = (i + 1) * max_value / 5
            painter.drawText(x - 15, y_axis + 15, 30, 20,
                            Qt.AlignmentFlag.AlignCenter, f"{int(value)}")

        # Dessiner les données
        if self.data:
            # Hauteur de chaque barre
            bar_height = height / min(len(self.data), 10) * 0.7
            gap = height / min(len(self.data), 10) * 0.3

            for i, equip in enumerate(self.data[:10]):  # Limiter à 10 équipements
                # Position Y de la barre
                y = padding + i * (bar_height + gap) + gap / 2

                # Valeur
                count = equip.get("repair_count", 0)

                # Largeur de la barre
                bar_width = (count / max_value) * width if max_value > 0 else 0

                # Dessiner la barre
                gradient = QLinearGradient(x_start, 0, x_start + bar_width, 0)
                gradient.setColorAt(0, QColor("#1976D2"))
                gradient.setColorAt(1, QColor("#64B5F6"))
                gradient.setSpread(QGradient.Spread.PadSpread)

                painter.setBrush(QBrush(gradient))
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawRect(int(x_start), int(y), int(bar_width), int(bar_height))

                # Étiquette de l'équipement
                painter.setPen(QPen(QColor("#666666"), 1))
                equip_name = equip.get("name", f"Équipement #{equip.get('equipment_id', i)}")
                if len(equip_name) > 15:
                    equip_name = equip_name[:12] + "..."
                painter.drawText(padding, y, 95, bar_height,
                                Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight, equip_name)

                # Valeur
                painter.setPen(QPen(QColor("white"), 1))
                painter.drawText(x_start + 5, y, bar_width - 10, bar_height,
                                Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft, str(count))

class TopCustomersChart(BaseChartWidget):
    """Graphique des clients avec le plus de réparations"""

    def __init__(self, parent=None):
        super().__init__("Clients principaux", parent)

    def _draw_chart(self, painter):
        """Dessine un graphique à barres horizontales des clients principaux"""
        if not self.data:
            return

        # Dimensions du graphique
        padding = 40
        width = self.width() - 2 * padding - 100  # Espace pour les étiquettes
        height = self.height() - 2 * padding - 30  # Espace pour le titre

        # Trouver la valeur max
        max_value = 0
        for customer in self.data:
            max_value = max(max_value, customer.get("repair_count", 0))

        # Ajouter une marge de 10%
        max_value = max_value * 1.1 if max_value > 0 else 10

        # Dessiner les axes
        painter.setPen(QPen(QColor("#666666"), 1))

        # Axe X
        x_start = padding + 100  # Espace pour les étiquettes
        y_axis = self.height() - padding
        painter.drawLine(x_start, y_axis, x_start + width, y_axis)

        # Axe Y
        painter.drawLine(x_start, y_axis, x_start, padding)

        # Dessiner les graduations et les étiquettes de l'axe X
        painter.setPen(QPen(QColor("#999999"), 1, Qt.PenStyle.DotLine))
        font = QFont("Arial", 8)
        painter.setFont(font)

        for i in range(5):
            x = x_start + (i + 1) * width / 5
            painter.drawLine(x, padding, x, y_axis)

            value = (i + 1) * max_value / 5
            painter.drawText(x - 15, y_axis + 15, 30, 20,
                            Qt.AlignmentFlag.AlignCenter, f"{int(value)}")

        # Dessiner les données
        if self.data:
            # Hauteur de chaque barre
            bar_height = height / min(len(self.data), 10) * 0.7
            gap = height / min(len(self.data), 10) * 0.3

            for i, customer in enumerate(self.data[:10]):  # Limiter à 10 clients
                # Position Y de la barre
                y = padding + i * (bar_height + gap) + gap / 2

                # Valeur
                count = customer.get("repair_count", 0)

                # Largeur de la barre
                bar_width = (count / max_value) * width if max_value > 0 else 0

                # Dessiner la barre
                gradient = QLinearGradient(x_start, 0, x_start + bar_width, 0)
                gradient.setColorAt(0, QColor("#FF9800"))
                gradient.setColorAt(1, QColor("#FFB74D"))
                gradient.setSpread(QGradient.Spread.PadSpread)

                painter.setBrush(QBrush(gradient))
                painter.setPen(Qt.PenStyle.NoPen)
                painter.drawRect(int(x_start), int(y), int(bar_width), int(bar_height))

                # Étiquette du client
                painter.setPen(QPen(QColor("#666666"), 1))
                customer_name = customer.get("customer_name", f"Client #{i+1}")
                if len(customer_name) > 15:
                    customer_name = customer_name[:12] + "..."
                painter.drawText(padding, y, 95, bar_height,
                                Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight, customer_name)

                # Valeur
                painter.setPen(QPen(QColor("white"), 1))
                painter.drawText(x_start + 5, y, bar_width - 10, bar_height,
                                Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft, str(count))
