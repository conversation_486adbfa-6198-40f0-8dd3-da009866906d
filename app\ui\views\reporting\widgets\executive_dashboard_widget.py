"""
Widget de tableau de bord exécutif avec KPIs et métriques de haut niveau.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QComboBox,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QGridLayout, QMessageBox, QScrollArea
)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt6.QtGui import QIcon, QColor, QFont, QPalette
import asyncio
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

from app.core.services.reporting_service import ReportingService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class ExecutiveKPICard(QFrame):
    """Carte KPI pour le tableau de bord exécutif"""
    
    def __init__(self, title, value, unit="", trend=None, color="#2196F3", parent=None):
        super().__init__(parent)
        self.title = title
        self.current_value = 0
        self.target_value = value
        self.unit = unit
        self.trend = trend
        self.color = color
        self.setup_ui()
        
    def setup_ui(self):
        """Configure l'interface de la carte KPI"""
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 10px;
            }}
            QFrame:hover {{
                border: 2px solid {self.color};
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(5)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet("font-size: 12px; color: #666; font-weight: bold;")
        title_label.setWordWrap(True)
        layout.addWidget(title_label)
        
        # Valeur principale
        self.value_label = QLabel("0")
        self.value_label.setStyleSheet(f"""
            font-size: 24px; 
            font-weight: bold; 
            color: {self.color};
            margin: 5px 0;
        """)
        layout.addWidget(self.value_label)
        
        # Unité
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet("font-size: 10px; color: #888;")
            layout.addWidget(unit_label)
        
        # Tendance
        if self.trend is not None:
            trend_label = QLabel(f"{self.trend:+.1f}%")
            trend_color = "#4CAF50" if self.trend >= 0 else "#F44336"
            trend_label.setStyleSheet(f"font-size: 11px; color: {trend_color}; font-weight: bold;")
            layout.addWidget(trend_label)
        
        layout.addStretch()
        
        # Animation de la valeur
        self.animation = QPropertyAnimation(self, b"animatedValue")
        self.animation.setDuration(1000)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def get_animated_value(self):
        return self.current_value
    
    def set_animated_value(self, value):
        self.current_value = value
        if isinstance(value, float):
            self.value_label.setText(f"{value:.1f}")
        else:
            self.value_label.setText(str(int(value)))
    
    animatedValue = pyqtProperty(float, get_animated_value, set_animated_value)
    
    def update_value(self, new_value):
        """Met à jour la valeur avec animation"""
        self.target_value = new_value
        self.animation.setStartValue(self.current_value)
        self.animation.setEndValue(new_value)
        self.animation.start()

class ExecutiveDashboardWidget(QWidget):
    """Widget de tableau de bord exécutif"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.service = None
        self.kpi_cards = {}
        self.setup_ui()
        self.init_service()
    
    def init_service(self):
        """Initialise le service de reporting"""
        try:
            db = SessionLocal()
            self.service = ReportingService(db)
        except Exception as e:
            print(f"Erreur lors de l'initialisation du service: {e}")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Titre
        title_label = QLabel("Tableau de Bord Exécutif")
        title_label.setStyleSheet("font-size: 20px; font-weight: bold; margin-bottom: 15px;")
        main_layout.addWidget(title_label)
        
        # Période de référence
        period_layout = QHBoxLayout()
        period_layout.addWidget(QLabel("Période:"))
        
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "7 derniers jours", "30 derniers jours", "3 derniers mois", 
            "6 derniers mois", "12 derniers mois", "Année en cours"
        ])
        self.period_combo.setCurrentText("30 derniers jours")
        self.period_combo.currentTextChanged.connect(self.load_data)
        period_layout.addWidget(self.period_combo)
        
        # Bouton de rafraîchissement
        refresh_button = QPushButton("Rafraîchir")
        refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        refresh_button.clicked.connect(self.load_data)
        period_layout.addWidget(refresh_button)
        
        period_layout.addStretch()
        main_layout.addLayout(period_layout)
        
        # Zone de défilement pour les KPIs
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        
        # Widget contenant tous les KPIs
        kpis_widget = QWidget()
        kpis_layout = QVBoxLayout(kpis_widget)
        
        # Section KPIs Financiers
        financial_group = QGroupBox("Indicateurs Financiers")
        financial_layout = QGridLayout(financial_group)
        
        # Créer les cartes KPI financières
        self.kpi_cards['revenue'] = ExecutiveKPICard(
            "Chiffre d'Affaires", 0, "DA", color="#4CAF50"
        )
        financial_layout.addWidget(self.kpi_cards['revenue'], 0, 0)
        
        self.kpi_cards['profit'] = ExecutiveKPICard(
            "Profit Net", 0, "DA", color="#2196F3"
        )
        financial_layout.addWidget(self.kpi_cards['profit'], 0, 1)
        
        self.kpi_cards['margin'] = ExecutiveKPICard(
            "Marge Bénéficiaire", 0, "%", color="#FF9800"
        )
        financial_layout.addWidget(self.kpi_cards['margin'], 0, 2)
        
        self.kpi_cards['cash_flow'] = ExecutiveKPICard(
            "Flux de Trésorerie", 0, "DA", color="#9C27B0"
        )
        financial_layout.addWidget(self.kpi_cards['cash_flow'], 0, 3)
        
        kpis_layout.addWidget(financial_group)
        
        # Section KPIs Opérationnels
        operational_group = QGroupBox("Indicateurs Opérationnels")
        operational_layout = QGridLayout(operational_group)
        
        self.kpi_cards['repairs'] = ExecutiveKPICard(
            "Réparations Terminées", 0, "", color="#607D8B"
        )
        operational_layout.addWidget(self.kpi_cards['repairs'], 0, 0)
        
        self.kpi_cards['avg_repair_time'] = ExecutiveKPICard(
            "Temps Moyen de Réparation", 0, "jours", color="#795548"
        )
        operational_layout.addWidget(self.kpi_cards['avg_repair_time'], 0, 1)
        
        self.kpi_cards['customer_satisfaction'] = ExecutiveKPICard(
            "Satisfaction Client", 0, "%", color="#E91E63"
        )
        operational_layout.addWidget(self.kpi_cards['customer_satisfaction'], 0, 2)
        
        self.kpi_cards['efficiency'] = ExecutiveKPICard(
            "Efficacité Opérationnelle", 0, "%", color="#00BCD4"
        )
        operational_layout.addWidget(self.kpi_cards['efficiency'], 0, 3)
        
        kpis_layout.addWidget(operational_group)
        
        # Section KPIs de Croissance
        growth_group = QGroupBox("Indicateurs de Croissance")
        growth_layout = QGridLayout(growth_group)
        
        self.kpi_cards['new_customers'] = ExecutiveKPICard(
            "Nouveaux Clients", 0, "", color="#8BC34A"
        )
        growth_layout.addWidget(self.kpi_cards['new_customers'], 0, 0)
        
        self.kpi_cards['repeat_customers'] = ExecutiveKPICard(
            "Clients Récurrents", 0, "%", color="#FFC107"
        )
        growth_layout.addWidget(self.kpi_cards['repeat_customers'], 0, 1)
        
        self.kpi_cards['avg_order_value'] = ExecutiveKPICard(
            "Valeur Moyenne Commande", 0, "DA", color="#FF5722"
        )
        growth_layout.addWidget(self.kpi_cards['avg_order_value'], 0, 2)
        
        self.kpi_cards['growth_rate'] = ExecutiveKPICard(
            "Taux de Croissance", 0, "%", color="#3F51B5"
        )
        growth_layout.addWidget(self.kpi_cards['growth_rate'], 0, 3)
        
        kpis_layout.addWidget(growth_group)
        
        # Graphique de tendances
        trends_group = QGroupBox("Tendances")
        trends_layout = QVBoxLayout(trends_group)
        
        self.trends_figure = Figure(figsize=(12, 4))
        self.trends_canvas = FigureCanvas(self.trends_figure)
        trends_layout.addWidget(self.trends_canvas)
        
        kpis_layout.addWidget(trends_group)
        
        scroll_area.setWidget(kpis_widget)
        main_layout.addWidget(scroll_area)
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()
        
        # Charger les données initiales
        QTimer.singleShot(100, self.load_data)
    
    def load_data(self):
        """Charge les données du tableau de bord"""
        QTimer.singleShot(0, self._load_data_async)
    
    def _load_data_async(self):
        """Charge les données de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._load_data())
        finally:
            loop.close()
    
    async def _load_data(self):
        """Charge les données du tableau de bord"""
        if not self.service:
            return
        
        self.loading_overlay.show()
        
        try:
            # Récupérer les KPIs exécutifs
            kpis = await self.service.get_executive_kpis()
            
            # Mettre à jour les cartes KPI avec animation
            for key, card in self.kpi_cards.items():
                if key in kpis:
                    try:
                        card.update_value(float(kpis[key] or 0))
                    except Exception:
                        # Fallback si non numérique
                        card.update_value(0.0)
            
            # Mettre à jour le graphique de tendances
            self.update_trends_chart()
            
        except Exception as e:
            print(f"Erreur lors du chargement des données exécutives: {e}")
        
        finally:
            self.loading_overlay.hide()
    
    def update_trends_chart(self):
        """Met à jour le graphique de tendances"""
        # Implémentation simplifiée
        self.trends_figure.clear()
        ax = self.trends_figure.add_subplot(111)
        
        # Données d'exemple (à remplacer par de vraies données)
        import numpy as np
        x = np.arange(30)
        revenue = np.random.normal(50000, 10000, 30).cumsum()
        
        ax.plot(x, revenue, label="Chiffre d'affaires", color="#4CAF50", linewidth=2)
        ax.set_title("Évolution du Chiffre d'Affaires (30 derniers jours)")
        ax.set_xlabel("Jours")
        ax.set_ylabel("Montant (DA)")
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        self.trends_figure.tight_layout()
        self.trends_canvas.draw()
