"""
Widget pour afficher le rapport financier de l'inventaire.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QGridLayout, QMessageBox, QTabWidget
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon, QColor
import asyncio
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

from app.core.services.financial_reporting_service import FinancialReportingService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class InventoryReportWidget(QWidget):
    """Widget pour afficher le rapport financier de l'inventaire"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Service
        self.db = SessionLocal()
        self.service = FinancialReportingService(self.db)

        # Données
        self.report_data = None

        # Configuration de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.load_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Bouton de rafraîchissement
        refresh_layout = QHBoxLayout()
        refresh_layout.addStretch()

        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.clicked.connect(self.load_data)
        refresh_layout.addWidget(self.refresh_button)

        main_layout.addLayout(refresh_layout)

        # Résumé du rapport
        summary_group = QGroupBox("Résumé de l'inventaire")
        summary_layout = QGridLayout(summary_group)

        # Valeur totale de l'inventaire
        total_label = QLabel("Valeur totale de l'inventaire:")
        total_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(total_label, 0, 0)

        self.total_value = QLabel("0.00 DA")
        self.total_value.setStyleSheet("font-size: 16px; color: #2196F3;")
        summary_layout.addWidget(self.total_value, 0, 1)

        # Rotation des stocks
        turnover_label = QLabel("Rotation des stocks:")
        turnover_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(turnover_label, 1, 0)

        self.turnover_value = QLabel("0.00")
        self.turnover_value.setStyleSheet("font-size: 16px;")
        summary_layout.addWidget(self.turnover_value, 1, 1)

        main_layout.addWidget(summary_group)

        # Onglets pour les différentes analyses
        self.tab_widget = QTabWidget()

        # Onglet Valeur par catégorie
        category_tab = QWidget()
        category_layout = QVBoxLayout(category_tab)

        self.category_table = QTableWidget()
        self.category_table.setColumnCount(3)
        self.category_table.setHorizontalHeaderLabels([
            "Catégorie", "Valeur", "Pourcentage"
        ])
        self.category_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.category_table.setAlternatingRowColors(True)
        self.category_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        category_layout.addWidget(self.category_table)

        # Graphique pour les catégories
        self.category_figure = Figure(figsize=(8, 4), dpi=100)
        self.category_canvas = FigureCanvas(self.category_figure)
        category_layout.addWidget(self.category_canvas)

        self.tab_widget.addTab(category_tab, "Valeur par catégorie")

        # Onglet Valeur par fournisseur
        supplier_tab = QWidget()
        supplier_layout = QVBoxLayout(supplier_tab)

        self.supplier_table = QTableWidget()
        self.supplier_table.setColumnCount(3)
        self.supplier_table.setHorizontalHeaderLabels([
            "Fournisseur", "Valeur", "Pourcentage"
        ])
        self.supplier_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.supplier_table.setAlternatingRowColors(True)
        self.supplier_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        supplier_layout.addWidget(self.supplier_table)

        # Graphique pour les fournisseurs
        self.supplier_figure = Figure(figsize=(8, 4), dpi=100)
        self.supplier_canvas = FigureCanvas(self.supplier_figure)
        supplier_layout.addWidget(self.supplier_canvas)

        self.tab_widget.addTab(supplier_tab, "Valeur par fournisseur")

        # Onglet Articles à faible rotation
        slow_moving_tab = QWidget()
        slow_moving_layout = QVBoxLayout(slow_moving_tab)

        self.slow_moving_table = QTableWidget()
        self.slow_moving_table.setColumnCount(5)
        self.slow_moving_table.setHorizontalHeaderLabels([
            "Article", "Quantité", "Valeur", "Dernière vente", "Jours sans vente"
        ])
        self.slow_moving_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.slow_moving_table.setAlternatingRowColors(True)
        self.slow_moving_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        slow_moving_layout.addWidget(self.slow_moving_table)

        self.tab_widget.addTab(slow_moving_tab, "Articles à faible rotation")

        # Onglet Articles de haute valeur
        high_value_tab = QWidget()
        high_value_layout = QVBoxLayout(high_value_tab)

        self.high_value_table = QTableWidget()
        self.high_value_table.setColumnCount(4)
        self.high_value_table.setHorizontalHeaderLabels([
            "Article", "Quantité", "Valeur unitaire", "Valeur totale"
        ])
        self.high_value_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.high_value_table.setAlternatingRowColors(True)
        self.high_value_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        high_value_layout.addWidget(self.high_value_table)

        self.tab_widget.addTab(high_value_tab, "Articles de haute valeur")

        # Onglet Analyse ABC
        abc_tab = QWidget()
        abc_layout = QVBoxLayout(abc_tab)

        self.abc_table = QTableWidget()
        self.abc_table.setColumnCount(5)
        self.abc_table.setHorizontalHeaderLabels([
            "Classe", "Nombre d'articles", "% des articles", "Valeur", "% de la valeur"
        ])
        self.abc_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.abc_table.setAlternatingRowColors(True)
        self.abc_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        abc_layout.addWidget(self.abc_table)

        # Graphique pour l'analyse ABC
        self.abc_figure = Figure(figsize=(8, 4), dpi=100)
        self.abc_canvas = FigureCanvas(self.abc_figure)
        abc_layout.addWidget(self.abc_canvas)

        self.tab_widget.addTab(abc_tab, "Analyse ABC")

        main_layout.addWidget(self.tab_widget)

    def load_data(self):
        """Charge les données du rapport"""
        self.loading_overlay.show()

        # Utiliser QTimer pour exécuter le chargement de manière asynchrone
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter le chargement des données"""
        # Utiliser directement la version synchrone pour éviter les problèmes avec asyncio
        self._load_data_sync()

    def _load_data_sync(self):
        """Version synchrone de _load_data_async"""
        try:
            # Générer le rapport de manière synchrone
            self.report_data = self.service.generate_inventory_financial_report_sync()

            # Mettre à jour l'interface
            self.update_ui()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des données: {str(e)}"
            )
        finally:
            self.loading_overlay.hide()

    async def _load_data_async(self):
        """Charge les données du rapport de manière asynchrone"""
        try:
            # Générer le rapport
            self.report_data = await self.service.generate_inventory_financial_report()

            # Mettre à jour l'interface
            self.update_ui()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des données: {str(e)}"
            )
        finally:
            self.loading_overlay.hide()

    def update_ui(self):
        """Met à jour l'interface avec les données du rapport"""
        if not self.report_data:
            return

        # Mettre à jour les valeurs
        if isinstance(self.report_data['inventory_value'], (int, float)):
            self.total_value.setText(f"{self.report_data['inventory_value']:.2f} DA")
        else:
            self.total_value.setText(str(self.report_data['inventory_value']) + " DA")

        if isinstance(self.report_data['inventory_turnover'], (int, float)):
            self.turnover_value.setText(f"{self.report_data['inventory_turnover']:.2f}")
        else:
            self.turnover_value.setText(str(self.report_data['inventory_turnover']))

        # Mettre à jour le tableau des catégories
        category_values = self.report_data['category_values']
        self.category_table.setRowCount(len(category_values))

        for i, category in enumerate(category_values):
            # Catégorie
            self.category_table.setItem(i, 0, QTableWidgetItem(category['category']))

            # Valeur
            value_item = QTableWidgetItem(f"{category['value']:.2f} DA")
            value_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.category_table.setItem(i, 1, value_item)

            # Pourcentage
            percent_item = QTableWidgetItem(f"{category['percentage']:.2f}%")
            percent_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.category_table.setItem(i, 2, percent_item)

        # Mettre à jour le graphique des catégories
        self.update_category_chart()

        # Mettre à jour le tableau des fournisseurs
        supplier_values = self.report_data['supplier_values']
        self.supplier_table.setRowCount(len(supplier_values))

        for i, supplier in enumerate(supplier_values):
            # Fournisseur
            self.supplier_table.setItem(i, 0, QTableWidgetItem(supplier['supplier']))

            # Valeur
            value_item = QTableWidgetItem(f"{supplier['value']:.2f} DA")
            value_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.supplier_table.setItem(i, 1, value_item)

            # Pourcentage
            percent_item = QTableWidgetItem(f"{supplier['percentage']:.2f}%")
            percent_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.supplier_table.setItem(i, 2, percent_item)

        # Mettre à jour le graphique des fournisseurs
        self.update_supplier_chart()

        # Mettre à jour le tableau des articles à faible rotation
        slow_moving_items = self.report_data['slow_moving_items']
        self.slow_moving_table.setRowCount(len(slow_moving_items))

        for i, item in enumerate(slow_moving_items):
            # Article
            self.slow_moving_table.setItem(i, 0, QTableWidgetItem(item['name']))

            # Quantité
            self.slow_moving_table.setItem(i, 1, QTableWidgetItem(str(item['quantity'])))

            # Valeur
            value_item = QTableWidgetItem(f"{item['value']:.2f} DA")
            value_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.slow_moving_table.setItem(i, 2, value_item)

            # Dernière vente
            if item.get('last_sale_date'):
                date_str = item['last_sale_date'].strftime("%d/%m/%Y")
            else:
                date_str = "Jamais"
            self.slow_moving_table.setItem(i, 3, QTableWidgetItem(date_str))

            # Jours sans vente
            self.slow_moving_table.setItem(i, 4, QTableWidgetItem(str(item['days_without_sale'])))

        # Mettre à jour le tableau des articles de haute valeur
        high_value_items = self.report_data['high_value_items']
        self.high_value_table.setRowCount(len(high_value_items))

        for i, item in enumerate(high_value_items):
            # Article
            self.high_value_table.setItem(i, 0, QTableWidgetItem(item['name']))

            # Quantité
            self.high_value_table.setItem(i, 1, QTableWidgetItem(str(item['quantity'])))

            # Valeur unitaire
            unit_value_item = QTableWidgetItem(f"{item['unit_value']:.2f} DA")
            unit_value_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.high_value_table.setItem(i, 2, unit_value_item)

            # Valeur totale
            total_value_item = QTableWidgetItem(f"{item['total_value']:.2f} DA")
            total_value_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.high_value_table.setItem(i, 3, total_value_item)

        # Mettre à jour le tableau de l'analyse ABC
        abc_analysis = self.report_data['abc_analysis']
        self.abc_table.setRowCount(len(abc_analysis))

        for i, abc_class in enumerate(abc_analysis):
            # Classe
            self.abc_table.setItem(i, 0, QTableWidgetItem(abc_class['class']))

            # Nombre d'articles
            self.abc_table.setItem(i, 1, QTableWidgetItem(str(abc_class['item_count'])))

            # % des articles
            item_percent_item = QTableWidgetItem(f"{abc_class['item_percentage']:.2f}%")
            item_percent_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.abc_table.setItem(i, 2, item_percent_item)

            # Valeur
            value_item = QTableWidgetItem(f"{abc_class['value']:.2f} DA")
            value_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.abc_table.setItem(i, 3, value_item)

            # % de la valeur
            value_percent_item = QTableWidgetItem(f"{abc_class['value_percentage']:.2f}%")
            value_percent_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.abc_table.setItem(i, 4, value_percent_item)

        # Mettre à jour le graphique de l'analyse ABC
        self.update_abc_chart()

    def update_category_chart(self):
        """Met à jour le graphique des catégories"""
        if not self.report_data or not self.report_data['category_values']:
            return

        # Effacer la figure
        self.category_figure.clear()

        # Créer un graphique à secteurs
        ax = self.category_figure.add_subplot(111)

        # Vérifier si les données sont vides
        if not self.report_data['category_values']:
            # Afficher un message si aucune donnée
            ax.text(0.5, 0.5, "Aucune donnée disponible",
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)
            ax.axis('off')
            self.category_figure.tight_layout()
            self.category_canvas.draw()
            return

        # Données pour le graphique
        labels = [category['category'] for category in self.report_data['category_values']]
        values = [category['value'] for category in self.report_data['category_values']]

        # Vérifier si toutes les valeurs sont nulles ou négatives
        if not values or all(v <= 0 for v in values):
            # Afficher un message si toutes les valeurs sont nulles ou négatives
            ax.text(0.5, 0.5, "Aucune donnée positive disponible",
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)
            ax.axis('off')
            self.category_figure.tight_layout()
            self.category_canvas.draw()
            return

        # Créer le graphique à secteurs
        try:
            ax.pie(
                values,
                labels=labels,
                autopct='%1.1f%%',
                startangle=90,
                shadow=True
            )
        except Exception as e:
            print(f"Erreur lors de la création du graphique à secteurs des catégories: {str(e)}")
            # Afficher un message d'erreur dans le graphique
            ax.clear()
            ax.text(0.5, 0.5, f"Erreur: {str(e)}",
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)
            ax.axis('off')

        # Égaliser les axes pour obtenir un cercle
        ax.axis('equal')

        # Titre
        ax.set_title('Répartition de la valeur par catégorie')

        # Ajuster la mise en page
        self.category_figure.tight_layout()

        # Redessiner le canvas
        self.category_canvas.draw()

    def update_supplier_chart(self):
        """Met à jour le graphique des fournisseurs"""
        if not self.report_data or not self.report_data['supplier_values']:
            return

        # Effacer la figure
        self.supplier_figure.clear()

        # Créer un graphique à secteurs
        ax = self.supplier_figure.add_subplot(111)

        # Vérifier si les données sont vides
        if not self.report_data['supplier_values']:
            # Afficher un message si aucune donnée
            ax.text(0.5, 0.5, "Aucune donnée disponible",
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)
            ax.axis('off')
            self.supplier_figure.tight_layout()
            self.supplier_canvas.draw()
            return

        # Récupérer les 5 premiers fournisseurs (ou moins s'il y en a moins)
        top_suppliers = self.report_data['supplier_values'][:5]

        # Calculer le total des autres fournisseurs
        other_value = 0
        if len(self.report_data['supplier_values']) > 5:
            other_suppliers = self.report_data['supplier_values'][5:]
            other_value = sum(supplier['value'] for supplier in other_suppliers)

        # Données pour le graphique
        labels = [supplier['supplier'] for supplier in top_suppliers]
        if other_value > 0:
            labels.append("Autres")

        values = [supplier['value'] for supplier in top_suppliers]
        if other_value > 0:
            values.append(other_value)

        # Vérifier si toutes les valeurs sont nulles ou négatives
        if not values or all(v <= 0 for v in values):
            # Afficher un message si toutes les valeurs sont nulles ou négatives
            ax.text(0.5, 0.5, "Aucune donnée positive disponible",
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)
            ax.axis('off')
            self.supplier_figure.tight_layout()
            self.supplier_canvas.draw()
            return

        # Créer le graphique à secteurs
        try:
            ax.pie(
                values,
                labels=labels,
                autopct='%1.1f%%',
                startangle=90,
                shadow=True
            )
        except Exception as e:
            print(f"Erreur lors de la création du graphique à secteurs des fournisseurs: {str(e)}")
            # Afficher un message d'erreur dans le graphique
            ax.clear()
            ax.text(0.5, 0.5, f"Erreur: {str(e)}",
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)
            ax.axis('off')

        # Égaliser les axes pour obtenir un cercle
        ax.axis('equal')

        # Titre
        ax.set_title('Répartition de la valeur par fournisseur')

        # Ajuster la mise en page
        self.supplier_figure.tight_layout()

        # Redessiner le canvas
        self.supplier_canvas.draw()

    def update_abc_chart(self):
        """Met à jour le graphique de l'analyse ABC"""
        if not self.report_data or not self.report_data['abc_analysis']:
            return

        # Effacer la figure
        self.abc_figure.clear()

        # Créer un graphique à barres
        ax = self.abc_figure.add_subplot(111)

        # Données pour le graphique
        classes = [abc_class['class'] for abc_class in self.report_data['abc_analysis']]
        item_percentages = [abc_class['item_percentage'] for abc_class in self.report_data['abc_analysis']]
        value_percentages = [abc_class['value_percentage'] for abc_class in self.report_data['abc_analysis']]

        # Positions des barres
        x = range(len(classes))
        width = 0.35

        # Créer les barres
        ax.bar([i - width/2 for i in x], item_percentages, width, label='% des articles', color='#2196F3')
        ax.bar([i + width/2 for i in x], value_percentages, width, label='% de la valeur', color='#4CAF50')

        # Configurer le graphique
        ax.set_title('Analyse ABC')
        ax.set_xlabel('Classe')
        ax.set_ylabel('Pourcentage')
        ax.set_xticks(x)
        ax.set_xticklabels(classes)
        ax.legend()
        ax.grid(axis='y', linestyle='--', alpha=0.7)

        # Ajuster la mise en page
        self.abc_figure.tight_layout()

        # Redessiner le canvas
        self.abc_canvas.draw()
