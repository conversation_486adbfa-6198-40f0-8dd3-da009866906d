from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QFrame
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon, QPixmap

class KPIWidget(QFrame):
    """Widget pour afficher un indicateur clé de performance (KPI)"""

    def __init__(self, title, value, icon_name=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.icon_name = icon_name

        self.setObjectName("kpiWidget")
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur du widget"""
        layout = QVBoxLayout(self)

        # Titre
        self.title_label = QLabel(self.title)
        self.title_label.setObjectName("kpiTitle")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.title_label)

        # Valeur
        self.value_label = QLabel(self.value)
        self.value_label.setObjectName("kpiValue")
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.value_label)

        # Icône (si spécifiée)
        if self.icon_name:
            self.icon_label = QLabel()
            self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

            # Charger l'icône
            icon = QIcon(f"app/ui/resources/icons/{self.icon_name}.svg")
            if not icon.isNull():
                pixmap = icon.pixmap(32, 32)
                self.icon_label.setPixmap(pixmap)
                layout.addWidget(self.icon_label)

        self.setLayout(layout)

    def update_value(self, value):
        """Met à jour la valeur du KPI"""
        self.value = value
        self.value_label.setText(value)

        # Forcer le rafraîchissement du widget
        self.update()

    def update_with_animation(self, value):
        """Met à jour la valeur du KPI avec une animation

        Cette méthode change temporairement le style du widget pour indiquer
        visuellement que la valeur a été mise à jour.
        """
        # Sauvegarder le style actuel
        current_style = self.styleSheet()

        # Appliquer un style de mise en évidence
        self.setStyleSheet(current_style + "QFrame#kpiWidget { background-color: rgba(33, 150, 243, 0.3); }")

        # Mettre à jour la valeur
        self.update_value(value)

        # Restaurer le style original après un court délai
        QTimer.singleShot(500, lambda: self.setStyleSheet(current_style))
