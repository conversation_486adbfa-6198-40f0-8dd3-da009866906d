"""
Widget pour afficher le rapport de profits et pertes.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QDateEdit,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QGridLayout, QMessageBox
)
from PyQt6.QtCore import Qt, QDate, QTimer
from PyQt6.QtGui import QIcon, QColor
import asyncio
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

from app.core.services.financial_reporting_service import FinancialReportingService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from app.utils.export_utils import export_profit_loss_report

class ProfitLossWidget(QWidget):
    """Widget pour afficher le rapport de profits et pertes"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Service
        self.db = SessionLocal()
        self.service = FinancialReportingService(self.db)

        # Données
        self.report_data = None

        # Configuration de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.load_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Sélection de la période
        period_frame = QFrame()
        period_frame.setFrameShape(QFrame.Shape.StyledPanel)
        period_frame.setStyleSheet("background-color: #f5f5f5; border-radius: 5px;")
        period_layout = QHBoxLayout(period_frame)

        period_label = QLabel("Période:")
        period_layout.addWidget(period_label)

        # Date de début
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        period_layout.addWidget(self.start_date_edit)

        # Date de fin
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate())
        period_layout.addWidget(self.end_date_edit)

        # Bouton de génération
        self.generate_button = QPushButton("Générer le rapport")
        self.generate_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.generate_button.clicked.connect(self.load_data)
        period_layout.addWidget(self.generate_button)

        # Bouton d'exportation
        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))
        self.export_button.clicked.connect(self.export_report)
        self.export_button.setEnabled(False)  # Désactivé jusqu'à ce que les données soient chargées
        period_layout.addWidget(self.export_button)

        main_layout.addWidget(period_frame)

        # Résumé du rapport
        summary_group = QGroupBox("Résumé du rapport")
        summary_layout = QGridLayout(summary_group)

        # Revenus
        revenue_label = QLabel("Revenus totaux:")
        revenue_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(revenue_label, 0, 0)

        self.revenue_value = QLabel("0.00 DA")
        self.revenue_value.setStyleSheet("font-size: 16px;")
        summary_layout.addWidget(self.revenue_value, 0, 1)

        # Coût des marchandises vendues
        cogs_label = QLabel("Coût des marchandises vendues:")
        cogs_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(cogs_label, 1, 0)

        self.cogs_value = QLabel("0.00 DA")
        self.cogs_value.setStyleSheet("font-size: 16px;")
        summary_layout.addWidget(self.cogs_value, 1, 1)

        # Marge brute
        gross_profit_label = QLabel("Marge brute:")
        gross_profit_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(gross_profit_label, 2, 0)

        self.gross_profit_value = QLabel("0.00 DA")
        self.gross_profit_value.setStyleSheet("font-size: 16px;")
        summary_layout.addWidget(self.gross_profit_value, 2, 1)

        # Pourcentage de marge brute
        gross_margin_label = QLabel("Pourcentage de marge brute:")
        gross_margin_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(gross_margin_label, 0, 2)

        self.gross_margin_value = QLabel("0.00%")
        self.gross_margin_value.setStyleSheet("font-size: 16px;")
        summary_layout.addWidget(self.gross_margin_value, 0, 3)

        # Dépenses d'achat
        expenses_label = QLabel("Dépenses d'achat:")
        expenses_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(expenses_label, 1, 2)

        self.expenses_value = QLabel("0.00 DA")
        self.expenses_value.setStyleSheet("font-size: 16px;")
        summary_layout.addWidget(self.expenses_value, 1, 3)

        # Résultat net
        net_profit_label = QLabel("Résultat net:")
        net_profit_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(net_profit_label, 2, 2)

        self.net_profit_value = QLabel("0.00 DA")
        self.net_profit_value.setStyleSheet("font-size: 16px;")
        summary_layout.addWidget(self.net_profit_value, 2, 3)

        main_layout.addWidget(summary_group)

        # Graphique
        chart_group = QGroupBox("Graphique")
        chart_layout = QVBoxLayout(chart_group)

        # Créer la figure Matplotlib
        self.figure = Figure(figsize=(8, 4), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        chart_layout.addWidget(self.canvas)

        main_layout.addWidget(chart_group)

        # Connexions
        self.start_date_edit.dateChanged.connect(self.on_date_changed)
        self.end_date_edit.dateChanged.connect(self.on_date_changed)

    def on_date_changed(self, date):
        """Gère le changement de date"""
        # Vérifier que la date de début est antérieure à la date de fin
        start_date = self.start_date_edit.date()
        end_date = self.end_date_edit.date()

        if start_date > end_date:
            if self.sender() == self.start_date_edit:
                self.end_date_edit.setDate(start_date)
            else:
                self.start_date_edit.setDate(end_date)

    def load_data(self):
        """Charge les données du rapport"""
        self.loading_overlay.show()

        # Utiliser QTimer pour exécuter le chargement de manière asynchrone
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter le chargement des données"""
        # Utiliser directement la version synchrone pour éviter les problèmes avec asyncio
        self._load_data_sync()

    def _load_data_sync(self):
        """Version synchrone de _load_data_async"""
        try:
            # Récupérer les dates
            start_date_qdate = self.start_date_edit.date()
            start_date = datetime(start_date_qdate.year(), start_date_qdate.month(), start_date_qdate.day(), 0, 0, 0)

            end_date_qdate = self.end_date_edit.date()
            end_date = datetime(end_date_qdate.year(), end_date_qdate.month(), end_date_qdate.day(), 23, 59, 59)

            # Générer le rapport (version synchrone)
            self.report_data = self.service.generate_profit_loss_report_sync(start_date, end_date)

            # Mettre à jour l'interface
            self.update_ui()

            # Activer le bouton d'exportation
            self.export_button.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des données: {str(e)}"
            )
            # Désactiver le bouton d'exportation
            self.export_button.setEnabled(False)
        finally:
            self.loading_overlay.hide()

    async def _load_data_async(self):
        """Charge les données du rapport de manière asynchrone"""
        try:
            # Récupérer les dates
            start_date_qdate = self.start_date_edit.date()
            start_date = datetime(start_date_qdate.year(), start_date_qdate.month(), start_date_qdate.day(), 0, 0, 0)

            end_date_qdate = self.end_date_edit.date()
            end_date = datetime(end_date_qdate.year(), end_date_qdate.month(), end_date_qdate.day(), 23, 59, 59)

            # Générer le rapport
            self.report_data = await self.service.generate_profit_loss_report(start_date, end_date)

            # Mettre à jour l'interface
            self.update_ui()

            # Activer le bouton d'exportation
            self.export_button.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des données: {str(e)}"
            )
            # Désactiver le bouton d'exportation
            self.export_button.setEnabled(False)
        finally:
            self.loading_overlay.hide()

    def update_ui(self):
        """Met à jour l'interface avec les données du rapport"""
        if not self.report_data:
            return

        # Mettre à jour les valeurs
        # Convertir en float pour éviter Decimal dans les f-strings
        self.revenue_value.setText(f"{float(self.report_data['total_revenue'] or 0):.2f} DA")
        self.cogs_value.setText(f"{float(self.report_data['total_cogs'] or 0):.2f} DA")
        self.gross_profit_value.setText(f"{float(self.report_data['gross_profit'] or 0):.2f} DA")
        self.gross_margin_value.setText(f"{float(self.report_data['gross_margin_percent'] or 0):.2f}%")
        self.expenses_value.setText(f"{float(self.report_data['total_purchase_expenses'] or 0):.2f} DA")
        self.net_profit_value.setText(f"{float(self.report_data['net_profit'] or 0):.2f} DA")

        # Colorer le résultat net en fonction de sa valeur
        if self.report_data['net_profit'] > 0:
            self.net_profit_value.setStyleSheet("font-size: 16px; color: #4CAF50;")  # Vert
        else:
            self.net_profit_value.setStyleSheet("font-size: 16px; color: #F44336;")  # Rouge

        # Mettre à jour le graphique
        self.update_chart()

    def update_chart(self):
        """Met à jour le graphique avec les données du rapport"""
        if not self.report_data:
            return

        # Effacer la figure
        self.figure.clear()

        # Créer un graphique à barres pour le résumé
        ax1 = self.figure.add_subplot(121)

        # Données pour le graphique à barres
        labels = ['Revenus', 'Coût des ventes', 'Marge brute', 'Dépenses', 'Résultat net']
        values = [
            float(self.report_data['total_revenue'] or 0),
            float(self.report_data['total_cogs'] or 0),
            float(self.report_data['gross_profit'] or 0),
            float(self.report_data['total_purchase_expenses'] or 0),
            float(self.report_data['net_profit'] or 0)
        ]

        # Couleurs
        colors = ['#4CAF50', '#F44336', '#2196F3', '#FF9800', '#9C27B0']

        # Créer le graphique à barres
        bars = ax1.bar(labels, values, color=colors, width=0.6)

        # Ajouter les valeurs au-dessus des barres
        for bar in bars:
            height = bar.get_height()
            ax1.text(
                bar.get_x() + bar.get_width() / 2.,
                height,
                f'{height:.2f}',
                ha='center',
                va='bottom',
                rotation=0,
                fontsize=8
            )

        # Configurer le graphique à barres
        ax1.set_title('Résumé')
        ax1.set_ylabel('Montant (DA)')
        ax1.grid(axis='y', linestyle='--', alpha=0.7)

        # Créer un graphique linéaire pour les données mensuelles
        if 'monthly_data' in self.report_data and self.report_data['monthly_data']:
            ax2 = self.figure.add_subplot(122)

            # Extraire les données mensuelles
            months = [data['month'] for data in self.report_data['monthly_data']]
            revenues = [float(data['revenue'] or 0) for data in self.report_data['monthly_data']]
            profits = [float(data['profit'] or 0) for data in self.report_data['monthly_data']]

            # Créer le graphique linéaire
            ax2.plot(months, revenues, 'o-', color='#4CAF50', label='Revenus')
            ax2.plot(months, profits, 'o-', color='#9C27B0', label='Profits')

            # Configurer le graphique linéaire
            ax2.set_title('Évolution mensuelle')
            ax2.set_ylabel('Montant (DA)')
            ax2.grid(True, linestyle='--', alpha=0.7)
            ax2.legend()

            # Définir les ticks et les étiquettes
            ax2.set_xticks(range(len(months)))
            ax2.set_xticklabels(months, rotation=45, ha='right')

        # Ajuster la mise en page
        self.figure.tight_layout()

        # Redessiner le canvas
        self.canvas.draw()

    def export_report(self):
        """Exporte le rapport"""
        if not self.report_data:
            QMessageBox.warning(
                self,
                "Avertissement",
                "Aucune donnée à exporter. Veuillez d'abord générer le rapport."
            )
            return

        # Exporter le rapport
        export_profit_loss_report(self.report_data, self.figure, self)
