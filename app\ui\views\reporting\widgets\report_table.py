from PyQt6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
from PyQt6.QtCore import Qt

class ReportTable(QTableWidget):
    """Tableau pour afficher des données de rapport"""

    def __init__(self, headers, parent=None):
        super().__init__(parent)
        self.headers = headers

        self.setObjectName("reportTable")
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        self.setAlternatingRowColors(True)
        self.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)

        # Améliorer l'apparence du tableau
        self.setShowGrid(True)
        self.setGridStyle(Qt.PenStyle.SolidLine)
        self.horizontalHeader().setHighlightSections(False)
        self.verticalHeader().setVisible(False)  # Masquer les numéros de ligne

        # Définir une police pour l'en-tête
        header_font = self.horizontalHeader().font()
        header_font.setBold(True)
        self.horizontalHeader().setFont(header_font)

        # Ajuster les colonnes
        header = self.horizontalHeader()

        # Si le tableau a seulement 2 colonnes (comme pour les statistiques de réparation)
        if len(headers) == 2:
            # Première colonne (métrique) prend 40% de l'espace
            header.setSectionResizeMode(0, QHeaderView.ResizeMode.Interactive)
            header.setStretchLastSection(True)
        else:
            # Pour les autres tableaux, ajuster les colonnes de manière plus intelligente
            for i in range(len(headers)):
                # Utiliser ResizeToContents pour les colonnes avec des valeurs numériques
                if i > 0:  # Supposons que la première colonne est toujours un nom/identifiant
                    header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
                else:
                    header.setSectionResizeMode(i, QHeaderView.ResizeMode.Interactive)

            # S'assurer que la dernière colonne s'étire pour remplir l'espace restant
            header.setStretchLastSection(True)

        # Définir une hauteur de ligne raisonnable
        self.verticalHeader().setDefaultSectionSize(25)

    def add_row(self, data, resize=False):
        """
        Ajoute une ligne au tableau

        Args:
            data: Liste des valeurs pour chaque colonne
            resize: Si True, redimensionne les colonnes après l'ajout
        """
        row = self.rowCount()
        self.insertRow(row)

        for col, value in enumerate(data):
            item = QTableWidgetItem(value)
            # Aligner les valeurs numériques à droite
            if col > 0 and isinstance(value, str) and any(c.isdigit() for c in value):
                item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.setItem(row, col, item)

        # Redimensionner les colonnes si demandé
        if resize:
            self.resizeColumnsToContents()

    def clear_data(self):
        """Efface toutes les données du tableau"""
        self.setRowCount(0)

    def resizeColumnsToContents(self):
        """Ajuste la taille des colonnes en fonction du contenu"""
        # Ajuster toutes les colonnes au contenu
        super().resizeColumnsToContents()

        # Si le tableau a seulement 2 colonnes (comme pour les statistiques de réparation)
        if self.columnCount() == 2:
            # Première colonne (métrique) prend au moins 200px
            if self.columnWidth(0) < 200:
                self.setColumnWidth(0, 200)

        # S'assurer que la largeur totale des colonnes ne dépasse pas la largeur du tableau
        total_width = sum(self.columnWidth(i) for i in range(self.columnCount()))
        if total_width > self.width() and self.columnCount() > 1:
            # Réduire proportionnellement la largeur des colonnes
            ratio = self.width() / total_width
            for i in range(self.columnCount()):
                self.setColumnWidth(i, int(self.columnWidth(i) * ratio))

    def get_data(self):
        """Récupère toutes les données du tableau"""
        data = []
        for row in range(self.rowCount()):
            row_data = []
            for col in range(self.columnCount()):
                item = self.item(row, col)
                row_data.append(item.text() if item else "")
            data.append(row_data)
        return data

    def export_to_csv(self, filename):
        """Exporte les données du tableau au format CSV"""
        import csv

        with open(filename, 'w', newline='') as file:
            writer = csv.writer(file)

            # Écrire les en-têtes
            writer.writerow(self.headers)

            # Écrire les données
            for row in range(self.rowCount()):
                row_data = []
                for col in range(self.columnCount()):
                    item = self.item(row, col)
                    row_data.append(item.text() if item else "")
                writer.writerow(row_data)
