"""
Widget pour afficher le rapport des ventes.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QComboBox,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QGridLayout, QMessageBox, QSpinBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon, QColor
import asyncio
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

from app.core.services.financial_reporting_service import FinancialReportingService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class SalesReportWidget(QWidget):
    """Widget pour afficher le rapport des ventes"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Service
        self.db = SessionLocal()
        self.service = FinancialReportingService(self.db)
        
        # Données
        self.report_data = None
        
        # Configuration de l'interface
        self.setup_ui()
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        
        # Charger les données
        self.load_data()
        
    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Sélection de la période
        period_frame = QFrame()
        period_frame.setFrameShape(QFrame.Shape.StyledPanel)
        period_frame.setStyleSheet("background-color: #f5f5f5; border-radius: 5px;")
        period_layout = QHBoxLayout(period_frame)
        
        # Type de période
        period_type_label = QLabel("Type de période:")
        period_layout.addWidget(period_type_label)
        
        self.period_type_combo = QComboBox()
        self.period_type_combo.addItem("Journalier", "daily")
        self.period_type_combo.addItem("Mensuel", "monthly")
        self.period_type_combo.addItem("Annuel", "yearly")
        self.period_type_combo.currentIndexChanged.connect(self.on_period_type_changed)
        period_layout.addWidget(self.period_type_combo)
        
        # Année
        year_label = QLabel("Année:")
        period_layout.addWidget(year_label)
        
        current_year = datetime.now().year
        self.year_spin = QSpinBox()
        self.year_spin.setRange(current_year - 10, current_year + 10)
        self.year_spin.setValue(current_year)
        period_layout.addWidget(self.year_spin)
        
        # Mois (pour le rapport journalier)
        self.month_label = QLabel("Mois:")
        period_layout.addWidget(self.month_label)
        
        self.month_combo = QComboBox()
        for i, month in enumerate(["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", 
                                  "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]):
            self.month_combo.addItem(month, i + 1)
        self.month_combo.setCurrentIndex(datetime.now().month - 1)
        period_layout.addWidget(self.month_combo)
        
        # Bouton de génération
        self.generate_button = QPushButton("Générer le rapport")
        self.generate_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.generate_button.clicked.connect(self.load_data)
        period_layout.addWidget(self.generate_button)
        
        main_layout.addWidget(period_frame)
        
        # Résumé du rapport
        summary_group = QGroupBox("Résumé du rapport")
        summary_layout = QGridLayout(summary_group)
        
        # Montant total des ventes
        total_label = QLabel("Montant total des ventes:")
        total_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(total_label, 0, 0)
        
        self.total_value = QLabel("0.00 DA")
        self.total_value.setStyleSheet("font-size: 16px; color: #4CAF50;")
        summary_layout.addWidget(self.total_value, 0, 1)
        
        # Nombre de ventes
        count_label = QLabel("Nombre de ventes:")
        count_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(count_label, 1, 0)
        
        self.count_value = QLabel("0")
        self.count_value.setStyleSheet("font-size: 16px;")
        summary_layout.addWidget(self.count_value, 1, 1)
        
        main_layout.addWidget(summary_group)
        
        # Tableau des ventes par période
        table_group = QGroupBox("Détail par période")
        table_layout = QVBoxLayout(table_group)
        
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(3)
        self.sales_table.setHorizontalHeaderLabels([
            "Période", "Montant", "Nombre de ventes"
        ])
        self.sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.sales_table.setAlternatingRowColors(True)
        self.sales_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.sales_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        table_layout.addWidget(self.sales_table)
        
        main_layout.addWidget(table_group)
        
        # Graphique
        chart_group = QGroupBox("Graphique des ventes")
        chart_layout = QVBoxLayout(chart_group)
        
        # Créer la figure Matplotlib
        self.figure = Figure(figsize=(8, 4), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        chart_layout.addWidget(self.canvas)
        
        main_layout.addWidget(chart_group)
        
        # Initialiser l'affichage en fonction du type de période
        self.on_period_type_changed(0)
        
    def on_period_type_changed(self, index):
        """Gère le changement de type de période"""
        period_type = self.period_type_combo.currentData()
        
        # Afficher/masquer le sélecteur de mois en fonction du type de période
        self.month_label.setVisible(period_type == "daily")
        self.month_combo.setVisible(period_type == "daily")
        
    def load_data(self):
        """Charge les données du rapport"""
        self.loading_overlay.show()
        
        # Utiliser QTimer pour exécuter le chargement de manière asynchrone
        QTimer.singleShot(0, self._load_data_wrapper)
        
    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._load_data_async())
        finally:
            loop.close()
            
    async def _load_data_async(self):
        """Charge les données du rapport de manière asynchrone"""
        try:
            # Récupérer les paramètres
            period_type = self.period_type_combo.currentData()
            year = self.year_spin.value()
            month = self.month_combo.currentData() if period_type == "daily" else None
            
            # Générer le rapport
            self.report_data = await self.service.generate_sales_by_period_report(period_type, year, month)
            
            # Mettre à jour l'interface
            self.update_ui()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des données: {str(e)}"
            )
        finally:
            self.loading_overlay.hide()
            
    def update_ui(self):
        """Met à jour l'interface avec les données du rapport"""
        if not self.report_data:
            return
            
        # Mettre à jour les valeurs
        # Convertir en float pour éviter Decimal dans f-string formatage
        self.total_value.setText(f"{float(self.report_data['total_amount'] or 0):.2f} DA")
        self.count_value.setText(str(self.report_data['total_sales']))
        
        # Remplir le tableau
        sales_by_period = self.report_data['sales_by_period']
        self.sales_table.setRowCount(len(sales_by_period))
        
        period_type = self.report_data['period_type']
        
        for i, sale in enumerate(sales_by_period):
            # Période
            if period_type == "daily":
                period_text = sale['date'].strftime("%d/%m/%Y")
            elif period_type == "monthly":
                period_text = sale['month_name']
            else:  # yearly
                period_text = str(sale['year'])
            self.sales_table.setItem(i, 0, QTableWidgetItem(period_text))
            
            # Montant
            amount_item = QTableWidgetItem(f"{float(sale['total_amount'] or 0):.2f} DA")
            amount_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.sales_table.setItem(i, 1, amount_item)
            
            # Nombre de ventes
            count_key = 'sales_count'
            self.sales_table.setItem(i, 2, QTableWidgetItem(str(sale[count_key])))
            
        # Mettre à jour le graphique
        self.update_chart()
        
    def update_chart(self):
        """Met à jour le graphique avec les données du rapport"""
        if not self.report_data or not self.report_data['sales_by_period']:
            return
            
        # Effacer la figure
        self.figure.clear()
        
        # Créer un graphique à barres
        ax = self.figure.add_subplot(111)
        
        period_type = self.report_data['period_type']
        sales_by_period = self.report_data['sales_by_period']
        
        # Données pour le graphique
        if period_type == "daily":
            labels = [sale['date'].strftime("%d") for sale in sales_by_period]
            title = f"Ventes journalières - {self.month_combo.currentText()} {self.year_spin.value()}"
        elif period_type == "monthly":
            labels = [sale['month_name'] for sale in sales_by_period]
            title = f"Ventes mensuelles - {self.year_spin.value()}"
        else:  # yearly
            labels = [str(sale['year']) for sale in sales_by_period]
            title = "Ventes annuelles"
            
        values = [float(sale['total_amount'] or 0) for sale in sales_by_period]
        
        # Créer le graphique à barres
        bars = ax.bar(labels, values, color='#4CAF50', width=0.6)
        
        # Ajouter les valeurs au-dessus des barres
        for bar in bars:
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2.,
                height,
                f'{height:.0f}',
                ha='center',
                va='bottom',
                rotation=0,
                fontsize=8
            )
            
        # Configurer le graphique
        ax.set_title(title)
        ax.set_ylabel('Montant (DA)')
        ax.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Rotation des étiquettes pour les rapports journaliers et mensuels
        if period_type in ["daily", "monthly"]:
            plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
            
        # Ajuster la mise en page
        self.figure.tight_layout()
        
        # Redessiner le canvas
        self.canvas.draw()
