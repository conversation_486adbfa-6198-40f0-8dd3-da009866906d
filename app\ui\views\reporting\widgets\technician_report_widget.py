"""
Widget de rapport pour les statistiques des techniciens
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QComboBox, QPushButton, QDateEdit, QTableWidget,
    QTableWidgetItem, QHeaderView, QTabWidget, QScrollArea,
    QFrame, QSplitter, QMessageBox
)
from PyQt6.QtCore import Qt, QDate, QTimer
from PyQt6.QtGui import QFont, QColor, QPalette
import asyncio
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

from app.core.services.reporting_service import ReportingService
from app.utils.database import SessionLocal
from ....components.custom_widgets import LoadingOverlay

class TechnicianReportWidget(QWidget):
    """Widget pour les rapports de performance des techniciens"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.service = None
        self.current_data = {}
        self.setup_ui()
        self.init_service()
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        
        # Section de contrôles
        controls_group = QGroupBox("Filtres et Période")
        controls_layout = QHBoxLayout(controls_group)
        
        # Sélection de période
        controls_layout.addWidget(QLabel("Période:"))
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "7 derniers jours",
            "30 derniers jours", 
            "3 derniers mois",
            "6 derniers mois",
            "Cette année",
            "Année dernière",
            "Personnalisée"
        ])
        self.period_combo.setCurrentText("30 derniers jours")
        controls_layout.addWidget(self.period_combo)
        
        # Dates personnalisées
        controls_layout.addWidget(QLabel("Du:"))
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        controls_layout.addWidget(self.start_date)
        
        controls_layout.addWidget(QLabel("Au:"))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        controls_layout.addWidget(self.end_date)
        
        # Sélection de technicien
        controls_layout.addWidget(QLabel("Technicien:"))
        self.technician_combo = QComboBox()
        self.technician_combo.addItem("Tous les techniciens", None)
        controls_layout.addWidget(self.technician_combo)
        
        # Bouton de mise à jour
        self.update_button = QPushButton("Mettre à jour")
        self.update_button.clicked.connect(self.update_report)
        controls_layout.addWidget(self.update_button)
        
        controls_layout.addStretch()
        layout.addWidget(controls_group)
        
        # Onglets principaux
        self.tabs = QTabWidget()
        
        # Onglet Vue d'ensemble
        self.overview_tab = self.create_overview_tab()
        self.tabs.addTab(self.overview_tab, "Vue d'ensemble")
        
        # Onglet Détails par technicien
        self.details_tab = self.create_details_tab()
        self.tabs.addTab(self.details_tab, "Détails par technicien")
        
        # Onglet Comparaison
        self.comparison_tab = self.create_comparison_tab()
        self.tabs.addTab(self.comparison_tab, "Comparaison")
        
        # Onglet Évolution temporelle
        self.timeline_tab = self.create_timeline_tab()
        self.tabs.addTab(self.timeline_tab, "Évolution temporelle")
        
        layout.addWidget(self.tabs)
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        
        # Connecter les signaux
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        
    def create_overview_tab(self):
        """Crée l'onglet de vue d'ensemble"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Splitter pour graphiques et KPIs
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Section KPIs
        kpis_widget = QWidget()
        kpis_layout = QGridLayout(kpis_widget)
        
        # KPIs principaux
        self.kpi_labels = {}
        kpi_items = [
            ("total_repairs", "Total Réparations", "0"),
            ("avg_duration", "Durée Moyenne", "0h"),
            ("total_revenue", "Chiffre d'Affaires", "0 DA"),
            ("avg_revenue_per_repair", "CA/Réparation", "0 DA"),
            ("completion_rate", "Taux de Complétion", "0%"),
            ("customer_satisfaction", "Satisfaction Client", "0%")
        ]
        
        for i, (key, label, default) in enumerate(kpi_items):
            row, col = i // 2, i % 2
            
            kpi_frame = QFrame()
            kpi_frame.setFrameStyle(QFrame.Shape.Box)
            kpi_frame.setStyleSheet("QFrame { border: 1px solid #ddd; border-radius: 5px; padding: 10px; }")
            
            kpi_layout_inner = QVBoxLayout(kpi_frame)
            
            title_label = QLabel(label)
            title_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            kpi_layout_inner.addWidget(title_label)
            
            value_label = QLabel(default)
            value_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
            value_label.setStyleSheet("color: #2196F3;")
            self.kpi_labels[key] = value_label
            kpi_layout_inner.addWidget(value_label)
            
            kpis_layout.addWidget(kpi_frame, row, col)
        
        splitter.addWidget(kpis_widget)
        
        # Section graphiques
        charts_widget = QWidget()
        charts_layout = QVBoxLayout(charts_widget)
        
        # Graphique de répartition des réparations par technicien
        self.repairs_distribution_figure = Figure(figsize=(8, 4))
        self.repairs_distribution_canvas = FigureCanvas(self.repairs_distribution_figure)
        charts_layout.addWidget(self.repairs_distribution_canvas)
        
        # Graphique de performance (efficacité vs qualité)
        self.performance_figure = Figure(figsize=(8, 4))
        self.performance_canvas = FigureCanvas(self.performance_figure)
        charts_layout.addWidget(self.performance_canvas)
        
        splitter.addWidget(charts_widget)
        splitter.setSizes([300, 700])
        
        layout.addWidget(splitter)
        
        return tab
        
    def create_details_tab(self):
        """Crée l'onglet de détails par technicien"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Tableau détaillé des performances
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(15)
        self.details_table.setHorizontalHeaderLabels([
            "Technicien", "Réparations", "Terminées", "En cours", "Annulées",
            "Durée Moy.", "CA Total", "CA/Réparation", "Taux Complétion",
            "Appareils Réparés", "Types d'Appareils", "Pièces Utilisées",
            "Coût Pièces", "Marge Bénéficiaire", "Satisfaction"
        ])
        
        # Configuration du tableau
        header = self.details_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        self.details_table.setAlternatingRowColors(True)
        self.details_table.setSortingEnabled(True)
        
        layout.addWidget(self.details_table)
        
        return tab
        
    def create_comparison_tab(self):
        """Crée l'onglet de comparaison entre techniciens"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Graphiques de comparaison
        self.comparison_figure = Figure(figsize=(12, 8))
        self.comparison_canvas = FigureCanvas(self.comparison_figure)
        layout.addWidget(self.comparison_canvas)
        
        return tab
        
    def create_timeline_tab(self):
        """Crée l'onglet d'évolution temporelle"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Graphique d'évolution temporelle
        self.timeline_figure = Figure(figsize=(12, 8))
        self.timeline_canvas = FigureCanvas(self.timeline_figure)
        layout.addWidget(self.timeline_canvas)
        
        return tab
        
    def init_service(self):
        """Initialise le service de reporting"""
        try:
            db = SessionLocal()
            self.service = ReportingService(db)
        except Exception as e:
            print(f"Erreur lors de l'initialisation du service: {e}")
            
    def on_period_changed(self, period_text):
        """Gère le changement de période"""
        if period_text == "Personnalisée":
            self.start_date.setEnabled(True)
            self.end_date.setEnabled(True)
        else:
            self.start_date.setEnabled(False)
            self.end_date.setEnabled(False)
            
            # Calculer les dates automatiquement
            end_date = datetime.now()
            
            if period_text == "7 derniers jours":
                start_date = end_date - timedelta(days=7)
            elif period_text == "30 derniers jours":
                start_date = end_date - timedelta(days=30)
            elif period_text == "3 derniers mois":
                start_date = end_date - timedelta(days=90)
            elif period_text == "6 derniers mois":
                start_date = end_date - timedelta(days=180)
            elif period_text == "Cette année":
                start_date = datetime(end_date.year, 1, 1)
            elif period_text == "Année dernière":
                start_date = datetime(end_date.year - 1, 1, 1)
                end_date = datetime(end_date.year - 1, 12, 31)
            else:
                return
                
            self.start_date.setDate(QDate.fromString(start_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
            self.end_date.setDate(QDate.fromString(end_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
            
    def update_report(self):
        """Met à jour le rapport"""
        QTimer.singleShot(0, self._update_report_async)
        
    def _update_report_async(self):
        """Version asynchrone de la mise à jour du rapport"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_data())
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la mise à jour: {str(e)}")
        finally:
            loop.close()
            
    async def load_data(self):
        """Charge les données des techniciens"""
        if not self.service:
            return
            
        self.loading_overlay.show()
        
        try:
            # Récupérer les dates
            start_date = self.start_date.date().toPyDate()
            end_date = self.end_date.date().toPyDate()
            
            # Convertir en datetime
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            
            # Charger les données étendues des techniciens
            self.current_data = await self.load_extended_technician_data(start_datetime, end_datetime)
            
            # Mettre à jour l'interface
            self.update_overview_tab()
            self.update_details_tab()
            self.update_comparison_tab()
            self.update_timeline_tab()
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
        finally:
            self.loading_overlay.hide()

    async def load_extended_technician_data(self, start_date, end_date):
        """Charge les données étendues des techniciens"""
        # Utiliser le service existant et l'étendre
        basic_data = await self.service.get_technician_performance(start_date, end_date)

        # Ajouter des données supplémentaires pour chaque technicien
        extended_data = []

        for tech_data in basic_data:
            technician_name = tech_data.get('technician_name', 'Inconnu')

            # Récupérer des statistiques détaillées
            detailed_stats = await self.get_detailed_technician_stats(
                technician_name, start_date, end_date
            )

            # Fusionner les données
            extended_tech_data = {**tech_data, **detailed_stats}
            extended_data.append(extended_tech_data)

        return {
            'technicians': extended_data,
            'period': {'start': start_date, 'end': end_date},
            'summary': self.calculate_summary_stats(extended_data)
        }

    async def get_detailed_technician_stats(self, technician_name, start_date, end_date):
        """Récupère des statistiques détaillées pour un technicien"""
        from app.core.models.repair import RepairOrder, RepairStatus
        from app.core.models.equipment import Equipment
        from sqlalchemy import func, distinct

        # Requête pour les réparations du technicien
        repairs_query = self.service.db.query(RepairOrder).filter(
            RepairOrder.technician_name == technician_name,
            RepairOrder.created_at.between(start_date, end_date)
        )

        repairs = repairs_query.all()

        if not repairs:
            return {
                'device_types': [],
                'brands_repaired': [],
                'parts_used_count': 0,
                'parts_cost_total': 0,
                'customer_satisfaction': 0,
                'repeat_customers': 0,
                'avg_repair_value': 0,
                'profit_margin': 0
            }

        # Analyser les types d'appareils réparés
        device_types = {}
        brands_repaired = set()
        total_parts_cost = 0
        total_revenue = 0
        total_cost = 0

        for repair in repairs:
            # Types d'appareils
            device_key = f"{repair.brand} {repair.model}".strip()
            if device_key:
                device_types[device_key] = device_types.get(device_key, 0) + 1
                brands_repaired.add(repair.brand)

            # Coûts et revenus
            parts_cost = getattr(repair, 'parts_cost', 0) or 0
            final_amount = getattr(repair, 'final_amount', 0) or 0
            total_cost_repair = getattr(repair, 'total_cost', 0) or 0

            total_parts_cost += parts_cost
            total_revenue += final_amount
            total_cost += total_cost_repair

        # Calculer les métriques
        repair_count = len(repairs)
        avg_repair_value = total_revenue / repair_count if repair_count > 0 else 0
        profit_margin = ((total_revenue - total_cost) / total_revenue * 100) if total_revenue > 0 else 0

        # Compter les pièces utilisées (approximation)
        parts_used_count = sum(1 for repair in repairs if getattr(repair, 'parts_cost', 0) > 0)

        # Satisfaction client (simulation basée sur les réparations terminées sans retour)
        completed_repairs = [r for r in repairs if r.status in [RepairStatus.COMPLETED, RepairStatus.PAID]]
        satisfaction_score = (len(completed_repairs) / repair_count * 100) if repair_count > 0 else 0

        # Clients récurrents (simulation)
        customer_ids = [repair.customer_id for repair in repairs if repair.customer_id]
        unique_customers = len(set(customer_ids))
        repeat_customers = len(customer_ids) - unique_customers

        return {
            'device_types': list(device_types.keys())[:5],  # Top 5
            'device_types_count': dict(list(device_types.items())[:5]),
            'brands_repaired': list(brands_repaired),
            'parts_used_count': parts_used_count,
            'parts_cost_total': total_parts_cost,
            'customer_satisfaction': round(satisfaction_score, 1),
            'repeat_customers': repeat_customers,
            'unique_customers': unique_customers,
            'avg_repair_value': round(avg_repair_value, 2),
            'profit_margin': round(profit_margin, 1),
            'total_revenue': total_revenue,
            'total_cost': total_cost
        }

    def calculate_summary_stats(self, technicians_data):
        """Calcule les statistiques de résumé"""
        if not technicians_data:
            return {}

        total_repairs = sum(tech.get('total_repairs', 0) for tech in technicians_data)
        total_revenue = sum(tech.get('total_revenue', 0) for tech in technicians_data)
        avg_satisfaction = sum(tech.get('customer_satisfaction', 0) for tech in technicians_data) / len(technicians_data)

        return {
            'total_repairs': total_repairs,
            'total_revenue': total_revenue,
            'avg_satisfaction': round(avg_satisfaction, 1),
            'technician_count': len(technicians_data),
            'avg_repairs_per_tech': round(total_repairs / len(technicians_data), 1) if technicians_data else 0,
            'avg_revenue_per_tech': round(total_revenue / len(technicians_data), 2) if technicians_data else 0
        }

    def update_overview_tab(self):
        """Met à jour l'onglet de vue d'ensemble"""
        if not self.current_data:
            return

        summary = self.current_data.get('summary', {})

        # Mettre à jour les KPIs
        self.kpi_labels['total_repairs'].setText(str(summary.get('total_repairs', 0)))

        # Calculer la durée moyenne (simulation)
        avg_duration = 4.5  # heures moyennes par réparation
        self.kpi_labels['avg_duration'].setText(f"{avg_duration:.1f}h")

        total_revenue = summary.get('total_revenue', 0)
        self.kpi_labels['total_revenue'].setText(f"{total_revenue:,.0f} DA")

        avg_revenue = summary.get('avg_revenue_per_tech', 0)
        self.kpi_labels['avg_revenue_per_repair'].setText(f"{avg_revenue:,.0f} DA")

        # Taux de complétion (simulation)
        completion_rate = 85.5
        self.kpi_labels['completion_rate'].setText(f"{completion_rate:.1f}%")

        satisfaction = summary.get('avg_satisfaction', 0)
        self.kpi_labels['customer_satisfaction'].setText(f"{satisfaction:.1f}%")

        # Mettre à jour les graphiques
        self.update_repairs_distribution_chart()
        self.update_performance_chart()

    def update_repairs_distribution_chart(self):
        """Met à jour le graphique de répartition des réparations"""
        self.repairs_distribution_figure.clear()

        if not self.current_data or not self.current_data.get('technicians'):
            return

        technicians = self.current_data['technicians']

        # Préparer les données
        names = [tech.get('technician_name', 'Inconnu')[:15] for tech in technicians[:10]]  # Top 10
        repairs = [tech.get('total_repairs', 0) for tech in technicians[:10]]

        if not names:
            return

        # Créer le graphique
        ax = self.repairs_distribution_figure.add_subplot(111)

        colors = plt.cm.Set3(np.linspace(0, 1, len(names)))
        bars = ax.bar(names, repairs, color=colors)

        # Ajouter les valeurs sur les barres
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{int(height)}', ha='center', va='bottom')

        ax.set_title('Répartition des Réparations par Technicien')
        ax.set_ylabel('Nombre de Réparations')
        ax.tick_params(axis='x', rotation=45)

        self.repairs_distribution_figure.tight_layout()
        self.repairs_distribution_canvas.draw()

    def update_performance_chart(self):
        """Met à jour le graphique de performance"""
        self.performance_figure.clear()

        if not self.current_data or not self.current_data.get('technicians'):
            return

        technicians = self.current_data['technicians']

        # Préparer les données pour le scatter plot
        efficiency = [tech.get('total_repairs', 0) for tech in technicians]
        satisfaction = [tech.get('customer_satisfaction', 0) for tech in technicians]
        names = [tech.get('technician_name', 'Inconnu') for tech in technicians]

        if not efficiency:
            return

        # Créer le graphique scatter
        ax = self.performance_figure.add_subplot(111)

        scatter = ax.scatter(efficiency, satisfaction, s=100, alpha=0.6, c=range(len(names)), cmap='viridis')

        # Ajouter les noms des techniciens
        for i, name in enumerate(names):
            ax.annotate(name[:10], (efficiency[i], satisfaction[i]),
                       xytext=(5, 5), textcoords='offset points', fontsize=8)

        ax.set_xlabel('Nombre de Réparations (Efficacité)')
        ax.set_ylabel('Satisfaction Client (%)')
        ax.set_title('Performance: Efficacité vs Satisfaction')
        ax.grid(True, alpha=0.3)

        self.performance_figure.tight_layout()
        self.performance_canvas.draw()

    def update_details_tab(self):
        """Met à jour l'onglet de détails"""
        if not self.current_data or not self.current_data.get('technicians'):
            self.details_table.setRowCount(0)
            return

        technicians = self.current_data['technicians']
        self.details_table.setRowCount(len(technicians))

        for row, tech in enumerate(technicians):
            # Données de base
            self.details_table.setItem(row, 0, QTableWidgetItem(tech.get('technician_name', 'Inconnu')))
            self.details_table.setItem(row, 1, QTableWidgetItem(str(tech.get('total_repairs', 0))))

            # Réparations par statut (simulation basée sur les données existantes)
            total = tech.get('total_repairs', 0)
            completed = int(total * 0.85)  # 85% terminées
            in_progress = int(total * 0.10)  # 10% en cours
            cancelled = total - completed - in_progress  # Le reste annulé

            self.details_table.setItem(row, 2, QTableWidgetItem(str(completed)))
            self.details_table.setItem(row, 3, QTableWidgetItem(str(in_progress)))
            self.details_table.setItem(row, 4, QTableWidgetItem(str(cancelled)))

            # Durée moyenne
            avg_duration = tech.get('average_duration', 0)
            self.details_table.setItem(row, 5, QTableWidgetItem(f"{avg_duration:.1f}h"))

            # Chiffre d'affaires
            revenue = tech.get('total_revenue', 0)
            self.details_table.setItem(row, 6, QTableWidgetItem(f"{revenue:,.0f} DA"))

            # CA par réparation
            avg_revenue = revenue / total if total > 0 else 0
            self.details_table.setItem(row, 7, QTableWidgetItem(f"{avg_revenue:,.0f} DA"))

            # Taux de complétion
            completion_rate = (completed / total * 100) if total > 0 else 0
            self.details_table.setItem(row, 8, QTableWidgetItem(f"{completion_rate:.1f}%"))

            # Appareils réparés
            device_types = tech.get('device_types', [])
            self.details_table.setItem(row, 9, QTableWidgetItem(str(len(device_types))))

            # Types d'appareils
            brands = tech.get('brands_repaired', [])
            self.details_table.setItem(row, 10, QTableWidgetItem(str(len(brands))))

            # Pièces utilisées
            parts_count = tech.get('parts_used_count', 0)
            self.details_table.setItem(row, 11, QTableWidgetItem(str(parts_count)))

            # Coût des pièces
            parts_cost = tech.get('parts_cost_total', 0)
            self.details_table.setItem(row, 12, QTableWidgetItem(f"{parts_cost:,.0f} DA"))

            # Marge bénéficiaire
            margin = tech.get('profit_margin', 0)
            self.details_table.setItem(row, 13, QTableWidgetItem(f"{margin:.1f}%"))

            # Satisfaction client
            satisfaction = tech.get('customer_satisfaction', 0)
            self.details_table.setItem(row, 14, QTableWidgetItem(f"{satisfaction:.1f}%"))

    def update_comparison_tab(self):
        """Met à jour l'onglet de comparaison"""
        self.comparison_figure.clear()

        if not self.current_data or not self.current_data.get('technicians'):
            return

        technicians = self.current_data['technicians'][:8]  # Top 8 pour la lisibilité

        if not technicians:
            return

        # Préparer les données
        names = [tech.get('technician_name', 'Inconnu')[:10] for tech in technicians]
        repairs = [tech.get('total_repairs', 0) for tech in technicians]
        revenue = [tech.get('total_revenue', 0) / 1000 for tech in technicians]  # En milliers
        satisfaction = [tech.get('customer_satisfaction', 0) for tech in technicians]

        # Créer les sous-graphiques
        fig = self.comparison_figure

        # Graphique 1: Nombre de réparations
        ax1 = fig.add_subplot(2, 2, 1)
        bars1 = ax1.bar(names, repairs, color='#2196F3', alpha=0.7)
        ax1.set_title('Nombre de Réparations')
        ax1.set_ylabel('Réparations')
        ax1.tick_params(axis='x', rotation=45)

        # Ajouter les valeurs
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height)}', ha='center', va='bottom', fontsize=8)

        # Graphique 2: Chiffre d'affaires
        ax2 = fig.add_subplot(2, 2, 2)
        bars2 = ax2.bar(names, revenue, color='#4CAF50', alpha=0.7)
        ax2.set_title('Chiffre d\'Affaires (k DA)')
        ax2.set_ylabel('Milliers DA')
        ax2.tick_params(axis='x', rotation=45)

        # Ajouter les valeurs
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.0f}k', ha='center', va='bottom', fontsize=8)

        # Graphique 3: Satisfaction client
        ax3 = fig.add_subplot(2, 2, 3)
        bars3 = ax3.bar(names, satisfaction, color='#FF9800', alpha=0.7)
        ax3.set_title('Satisfaction Client (%)')
        ax3.set_ylabel('Pourcentage')
        ax3.tick_params(axis='x', rotation=45)
        ax3.set_ylim(0, 100)

        # Ajouter les valeurs
        for bar in bars3:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.1f}%', ha='center', va='bottom', fontsize=8)

        # Graphique 4: Efficacité (réparations vs satisfaction)
        ax4 = fig.add_subplot(2, 2, 4)
        colors = ['#FF5722' if s < 80 else '#4CAF50' for s in satisfaction]
        ax4.scatter(repairs, satisfaction, c=colors, s=100, alpha=0.7)

        for i, name in enumerate(names):
            ax4.annotate(name, (repairs[i], satisfaction[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=7)

        ax4.set_xlabel('Nombre de Réparations')
        ax4.set_ylabel('Satisfaction (%)')
        ax4.set_title('Efficacité vs Satisfaction')
        ax4.grid(True, alpha=0.3)

        fig.tight_layout()
        self.comparison_canvas.draw()

    def update_timeline_tab(self):
        """Met à jour l'onglet d'évolution temporelle"""
        self.timeline_figure.clear()

        if not self.current_data:
            return

        # Simuler des données d'évolution temporelle
        # Dans une implémentation réelle, ces données viendraient de la base de données
        technicians = self.current_data.get('technicians', [])[:5]  # Top 5

        if not technicians:
            return

        # Générer des données mensuelles simulées
        months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun']

        fig = self.timeline_figure

        # Graphique 1: Évolution du nombre de réparations
        ax1 = fig.add_subplot(2, 1, 1)

        for i, tech in enumerate(technicians):
            name = tech.get('technician_name', 'Inconnu')
            # Convertir en float pour éviter Decimal x float
            base_repairs = float(tech.get('total_repairs', 0) or 0) / 6  # Moyenne mensuelle

            # Simuler une variation
            monthly_repairs = [
                base_repairs * (1 + 0.1 * np.sin(j + i))
                for j in range(len(months))
            ]

            ax1.plot(months, monthly_repairs, marker='o', label=name[:10], linewidth=2)

        ax1.set_title('Évolution du Nombre de Réparations par Mois')
        ax1.set_ylabel('Réparations')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Graphique 2: Évolution du chiffre d'affaires
        ax2 = fig.add_subplot(2, 1, 2)

        for i, tech in enumerate(technicians):
            name = tech.get('technician_name', 'Inconnu')
            # Convertir en float pour éviter Decimal x float
            base_revenue = float(tech.get('total_revenue', 0) or 0) / 6000  # En milliers, moyenne mensuelle

            # Simuler une variation
            monthly_revenue = [
                base_revenue * (1 + 0.15 * np.cos(j + i))
                for j in range(len(months))
            ]

            ax2.plot(months, monthly_revenue, marker='s', label=name[:10], linewidth=2)

        ax2.set_title('Évolution du Chiffre d\'Affaires par Mois (k DA)')
        ax2.set_ylabel('Milliers DA')
        ax2.set_xlabel('Mois')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        fig.tight_layout()
        self.timeline_canvas.draw()
