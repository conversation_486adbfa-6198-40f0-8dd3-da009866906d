from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QComboBox, QDoubleSpinBox,
    QDateEdit, QTextEdit, QDialogButtonBox, QMessageBox
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime, timezone

from app.core.models.sale import PaymentMethod, PaymentPydantic
from app.core.models.treasury import CashRegister
from app.core.services.sale_service import SaleService
from app.core.services.finance_service import FinanceService
from app.utils.database import SessionLocal

class PaymentDialog(QDialog):
    """Boîte de dialogue pour enregistrer un paiement"""

    def __init__(self, parent=None, sale_id=None, customer_id=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = SaleService(self.db)
        self.finance_service = FinanceService(self.db)

        # Données
        self.sale_id = sale_id
        self.sale = None
        self.customer_id = customer_id

        # Configuration de l'interface
        self.setWindowTitle("Enregistrer un paiement")
        self.setup_ui()

        # Charger les données
        if sale_id:
            self.load_sale_data()
        elif customer_id:
            # Si nous avons un customer_id mais pas de sale_id, nous allons
            # charger la liste des ventes impayées pour ce client
            self.load_customer_sales()
        else:
            QMessageBox.critical(self, "Erreur", "Aucune vente ou client sélectionné")
            self.reject()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("PaymentDialog: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.resize(400, 350)

        main_layout = QVBoxLayout(self)

        # Sélection de vente (visible uniquement si customer_id est fourni sans sale_id)
        self.sale_selection_layout = QFormLayout()
        self.sale_combo = QComboBox()
        self.sale_combo.currentIndexChanged.connect(self.on_sale_selected)
        self.sale_selection_layout.addRow("Sélectionner une vente:", self.sale_combo)
        main_layout.addLayout(self.sale_selection_layout)

        # Par défaut, masquer la sélection de vente
        self.sale_combo.setVisible(False)

        # Informations de la vente
        self.sale_info_label = QLabel()
        self.sale_info_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(self.sale_info_label)

        # Montant restant
        self.remaining_label = QLabel()
        self.remaining_label.setStyleSheet("font-weight: bold; color: red;")
        main_layout.addWidget(self.remaining_label)

        # Formulaire de paiement
        form_layout = QFormLayout()

        # Montant
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0, 1000000)
        self.amount_spin.setSuffix(" DA")
        self.amount_spin.setAlignment(Qt.AlignmentFlag.AlignRight)
        form_layout.addRow("Montant:", self.amount_spin)

        # Méthode de paiement
        self.payment_method_combo = QComboBox()
        for method in PaymentMethod:
            self.payment_method_combo.addItem(method.value.capitalize(), method)
        self.payment_method_combo.currentIndexChanged.connect(self.on_payment_method_changed)
        form_layout.addRow("Méthode de paiement:", self.payment_method_combo)

        # Sélection de caisse (visible uniquement pour les espèces)
        self.cash_register_label = QLabel("Caisse:")
        self.cash_register_combo = QComboBox()
        self.cash_register_combo.addItem("(Par défaut)", None)
        self.load_cash_registers()
        form_layout.addRow(self.cash_register_label, self.cash_register_combo)

        # Date
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow("Date:", self.date_edit)

        # Référence
        self.reference_edit = QLineEdit()
        form_layout.addRow("Référence:", self.reference_edit)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes...")
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)

        main_layout.addLayout(form_layout)

        # Boutons
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)

        # Initialiser la visibilité de la sélection de caisse
        self.on_payment_method_changed()

    def load_sale_data(self):
        """Charge les données de la vente"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Récupérer la vente
            self.sale = loop.run_until_complete(self.service.get(self.sale_id))
            if not self.sale:
                QMessageBox.critical(self, "Erreur", "Vente non trouvée")
                self.reject()
                return

            # Afficher les informations de la vente
            self.sale_info_label.setText(f"Vente #{self.sale.number} - {self.sale.date.strftime('%d/%m/%Y')}")

            # Calculer le montant restant
            remaining = self.sale.final_amount - self.sale.total_paid
            self.remaining_label.setText(f"Montant restant: {remaining:.2f} DA")

            # Définir le montant par défaut
            self.amount_spin.setValue(remaining)
            self.amount_spin.setMaximum(remaining)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de la vente: {str(e)}")
            print(f"Erreur lors du chargement de la vente: {e}")
            import traceback
            traceback.print_exc()
            self.reject()
        finally:
            loop.close()

    def load_customer_sales(self):
        """Charge les ventes impayées d'un client"""
        if not self.customer_id:
            return

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Récupérer les ventes impayées du client
            sales = loop.run_until_complete(self.service.get_customer_unpaid_sales(self.customer_id))

            if not sales:
                QMessageBox.information(self, "Information", "Ce client n'a pas de ventes impayées.")
                self.reject()
                return

            # Afficher le combo de sélection de vente
            self.sale_combo.clear()
            self.sale_combo.setVisible(True)

            # Ajouter les ventes au combo
            for sale in sales:
                sale_date = sale.date.strftime('%d/%m/%Y') if hasattr(sale, 'date') and sale.date else "N/A"
                remaining = sale.final_amount - sale.total_paid
                self.sale_combo.addItem(
                    f"Vente #{sale.number} - {sale_date} - Reste: {remaining:.2f} DA",
                    sale.id
                )

            # Sélectionner la première vente
            if self.sale_combo.count() > 0:
                self.sale_combo.setCurrentIndex(0)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des ventes: {str(e)}")
            print(f"Erreur lors du chargement des ventes: {e}")
            import traceback
            traceback.print_exc()
            self.reject()
        finally:
            loop.close()

    def on_sale_selected(self, index):
        """Gère la sélection d'une vente dans le combo"""
        if index < 0:
            return

        # Récupérer l'ID de la vente sélectionnée
        self.sale_id = self.sale_combo.itemData(index)

        # Charger les données de la vente
        if self.sale_id:
            self.load_sale_data()

    def load_cash_registers(self):
        """Charge la liste des caisses actives"""
        try:
            # Récupérer les caisses actives
            registers = self.db.query(CashRegister).filter(
                CashRegister.is_active == True
            ).order_by(CashRegister.name).all()

            # Ajouter les caisses au combo box
            for register in registers:
                self.cash_register_combo.addItem(
                    f"{register.name} ({register.current_balance:.2f} DA)",
                    register.id
                )

        except Exception as e:
            print(f"Erreur lors du chargement des caisses: {e}")
            # En cas d'erreur, laisser juste l'option par défaut

    def on_payment_method_changed(self):
        """Gère le changement de méthode de paiement"""
        current_method = self.payment_method_combo.currentData()

        # Afficher la sélection de caisse uniquement pour les espèces
        is_cash = current_method == PaymentMethod.cash
        self.cash_register_label.setVisible(is_cash)
        self.cash_register_combo.setVisible(is_cash)

    def accept(self):
        """Enregistre le paiement et ferme la boîte de dialogue"""
        if not self.sale:
            QMessageBox.critical(self, "Erreur", "Aucune vente sélectionnée")
            return

        # Récupérer les données du paiement
        amount = self.amount_spin.value()
        payment_method = self.payment_method_combo.currentData()
        date = self.date_edit.date().toPyDate()
        reference = self.reference_edit.text().strip()
        notes = self.notes_edit.toPlainText().strip()

        # Récupérer l'ID de la caisse sélectionnée (uniquement pour les espèces)
        cash_register_id = None
        if payment_method == PaymentMethod.cash:
            cash_register_id = self.cash_register_combo.currentData()

        # Vérifier le montant
        if amount <= 0:
            QMessageBox.warning(self, "Erreur", "Le montant doit être supérieur à 0")
            return

        # Enregistrer le paiement via FinanceService pour garantir la cohérence
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Utiliser FinanceService pour créer le paiement avec cohérence trésorerie et références unifiées
            payment = loop.run_until_complete(self.finance_service.pay_sale(
                sale_id=self.sale_id,
                amount=amount,
                method=payment_method,
                processed_by=1,  # TODO: Récupérer l'ID de l'utilisateur connecté
                reference=reference or None,  # Si vide, sera généré automatiquement
                cash_register_id=cash_register_id,  # Caisse sélectionnée pour les espèces
                payment_date=datetime.combine(date, datetime.min.time()).replace(tzinfo=timezone.utc),
                auto_generate_reference=True  # Génération automatique de référence unifiée
            ))

            # Afficher un message de succès
            QMessageBox.information(self, "Succès", f"Le paiement de {amount:.2f} DA a été enregistré avec succès")

            # Fermer la boîte de dialogue
            super().accept()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement du paiement: {str(e)}")
            print(f"Erreur lors de l'enregistrement du paiement: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()
