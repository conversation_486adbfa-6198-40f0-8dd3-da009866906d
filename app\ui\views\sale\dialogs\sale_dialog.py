from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
    QComboBox, QDoubleSpinBox, QSpinBox, QDateEdit, QTextEdit,
    QDialogButtonBox, QMessageBox, QHeaderView, QTabWidget,
    QCheckBox, QFrame, QWidget
)
from PyQt6.QtCore import Qt, QDate, QDateTime
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime, timezone, timedelta

from app.core.models.sale import (
    Sale, SaleItem, SaleStatus, PaymentMethod, PaymentStatus
)
from app.core.services.sale_service import SaleService
from app.core.services.customer_service import CustomerService
from app.core.services.inventory_service import InventoryService
from app.utils.database import SessionLocal
from .product_selection_dialog import ProductSelectionDialog
from ..widgets.payments_widget import PaymentsWidget

class SaleDialog(QDialog):
    """Boîte de dialogue pour ajouter/modifier une vente"""

    def __init__(self, parent=None, sale_id=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = SaleService(self.db)
        self.customer_service = CustomerService(self.db)
        self.inventory_service = InventoryService(self.db)

        # Données
        self.sale_id = sale_id
        self.sale = None
        self.sale_items = []

        # Configuration de l'interface
        self.setup_ui()

        # Charger les données
        if sale_id:
            self.setWindowTitle("Modifier la vente")
            self.load_sale_data()
        else:
            self.setWindowTitle("Nouvelle vente")
            self.init_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("SaleDialog: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.resize(800, 600)

        main_layout = QVBoxLayout(self)

        # Onglets
        self.tabs = QTabWidget()

        # Onglet Vente
        sale_tab = QWidget()
        sale_layout = QVBoxLayout(sale_tab)

        # Informations de base
        form_layout = QFormLayout()

        # Numéro de vente
        self.number_edit = QLineEdit()
        self.number_edit.setReadOnly(True)
        form_layout.addRow("Numéro:", self.number_edit)

        # Date
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow("Date:", self.date_edit)

        # Client
        self.customer_combo = QComboBox()
        form_layout.addRow("Client:", self.customer_combo)

        # Statut
        self.status_combo = QComboBox()
        for status in SaleStatus:
            self.status_combo.addItem(status.value.capitalize(), status)
        form_layout.addRow("Statut:", self.status_combo)

        # Méthode de paiement
        self.payment_method_combo = QComboBox()
        for method in PaymentMethod:
            self.payment_method_combo.addItem(method.value.capitalize(), method)
        form_layout.addRow("Méthode de paiement:", self.payment_method_combo)

        # Statut de paiement
        self.payment_status_combo = QComboBox()
        for status in PaymentStatus:
            self.payment_status_combo.addItem(status.value.capitalize(), status)
        form_layout.addRow("Statut de paiement:", self.payment_status_combo)

        # Facture
        self.invoice_check = QCheckBox("Générer une facture")
        form_layout.addRow("", self.invoice_check)

        sale_layout.addLayout(form_layout)

        # Tableau des articles
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "Produit", "Prix", "Quantité", "Remise", "TVA", "Total", ""
        ])
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)
        self.items_table.setColumnWidth(6, 40)  # Largeur de la colonne de suppression
        sale_layout.addWidget(self.items_table)

        # Bouton d'ajout d'article
        add_layout = QHBoxLayout()
        self.add_item_button = QPushButton("Ajouter un article")
        self.add_item_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        add_layout.addWidget(self.add_item_button)
        add_layout.addStretch()
        sale_layout.addLayout(add_layout)

        # Résumé
        summary_frame = QFrame()
        summary_frame.setFrameShape(QFrame.Shape.StyledPanel)
        summary_frame.setFrameShadow(QFrame.Shadow.Raised)
        summary_frame.setObjectName("summaryFrame")
        summary_frame.setStyleSheet("""
            #summaryFrame {
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        summary_layout = QGridLayout(summary_frame)

        # Sous-total
        summary_layout.addWidget(QLabel("Sous-total:"), 0, 0)
        self.subtotal_label = QLabel("0.00 DA")
        self.subtotal_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        summary_layout.addWidget(self.subtotal_label, 0, 1)

        # Remise
        summary_layout.addWidget(QLabel("Remise:"), 1, 0)
        discount_layout = QHBoxLayout()
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setRange(0, 100)
        self.discount_spin.setSuffix(" %")
        self.discount_spin.setAlignment(Qt.AlignmentFlag.AlignRight)
        discount_layout.addWidget(self.discount_spin)
        self.discount_label = QLabel("0.00 DA")
        self.discount_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        discount_layout.addWidget(self.discount_label)
        summary_layout.addLayout(discount_layout, 1, 1)

        # TVA
        summary_layout.addWidget(QLabel("TVA:"), 2, 0)
        self.tax_label = QLabel("0.00 DA")
        self.tax_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        summary_layout.addWidget(self.tax_label, 2, 1)

        # Total
        summary_layout.addWidget(QLabel("Total:"), 3, 0)
        self.total_label = QLabel("0.00 DA")
        self.total_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.total_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        summary_layout.addWidget(self.total_label, 3, 1)

        sale_layout.addWidget(summary_frame)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes...")
        self.notes_edit.setMaximumHeight(100)
        sale_layout.addWidget(self.notes_edit)

        self.tabs.addTab(sale_tab, "Vente")

        # Onglet Paiements
        self.payments_widget = PaymentsWidget(self, sale_id=self.sale_id)
        self.payments_widget.payment_added.connect(self.on_payment_added)
        self.tabs.addTab(self.payments_widget, "Paiements")

        main_layout.addWidget(self.tabs)

        # Boutons
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)

        # Connexions
        self.add_item_button.clicked.connect(self.show_product_selection)
        self.discount_spin.valueChanged.connect(self.update_totals)
        self.invoice_check.toggled.connect(self.on_invoice_toggled)

    def init_data(self):
        """Initialise les données pour une nouvelle vente"""
        # Charger les clients
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Récupérer tous les clients
            customers = loop.run_until_complete(self.customer_service.get_all())

            # Ajouter les clients au combo
            self.customer_combo.clear()
            self.customer_combo.addItem("Client anonyme", None)

            for customer in customers:
                if customer.active:  # Ne pas afficher les clients inactifs
                    self.customer_combo.addItem(customer.name, customer.id)

        except Exception as e:
            print(f"Erreur lors du chargement des clients: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def load_sale_data(self):
        """Charge les données d'une vente existante"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Récupérer la vente
            self.sale = loop.run_until_complete(self.service.get(self.sale_id))
            if not self.sale:
                QMessageBox.critical(self, "Erreur", "Vente non trouvée")
                self.reject()
                return

            # Remplir les champs
            self.number_edit.setText(self.sale.number)
            self.date_edit.setDate(self.sale.date.date())

            # Charger les clients
            customers = loop.run_until_complete(self.customer_service.get_all())

            # Ajouter les clients au combo
            self.customer_combo.clear()
            self.customer_combo.addItem("Client anonyme", None)

            for customer in customers:
                if customer.active:  # Ne pas afficher les clients inactifs
                    self.customer_combo.addItem(customer.name, customer.id)

            # Sélectionner le client
            if self.sale.customer_id:
                index = self.customer_combo.findData(self.sale.customer_id)
                if index >= 0:
                    self.customer_combo.setCurrentIndex(index)

            # Sélectionner le statut
            index = self.status_combo.findData(self.sale.status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)

            # Sélectionner la méthode de paiement
            if self.sale.payment_method:
                index = self.payment_method_combo.findData(self.sale.payment_method)
                if index >= 0:
                    self.payment_method_combo.setCurrentIndex(index)

            # Sélectionner le statut de paiement
            index = self.payment_status_combo.findData(self.sale.payment_status)
            if index >= 0:
                self.payment_status_combo.setCurrentIndex(index)

            # Facture
            self.invoice_check.setChecked(self.sale.is_invoice)

            # Notes
            if self.sale.notes:
                self.notes_edit.setText(self.sale.notes)

            # Charger les articles
            self.sale_items = []
            for item in self.sale.items:
                self.sale_items.append({
                    'product_id': item.product_id,
                    'name': item.product.name,
                    'unit_price': item.unit_price,
                    'quantity': item.quantity,
                    'discount_percent': item.discount_percent,
                    'tax_percent': item.tax_percent,
                    'total': item.total_amount
                })

            # Remplir le tableau des articles
            self._refresh_items_table()

            # Mettre à jour les totaux
            self.discount_spin.setValue(0)  # La remise est déjà incluse dans les articles
            self.update_totals()

            # Initialiser le widget de paiements avec l'ID de la vente
            self.payments_widget.sale_id = self.sale_id
            self.payments_widget.load_data()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de la vente: {str(e)}")
            print(f"Erreur lors du chargement de la vente: {e}")
            import traceback
            traceback.print_exc()
            self.reject()
        finally:
            loop.close()

    def _refresh_items_table(self):
        """Rafraîchit le tableau des articles"""
        self.items_table.setRowCount(0)

        for i, item in enumerate(self.sale_items):
            self.items_table.insertRow(i)

            # Nom du produit
            self.items_table.setItem(i, 0, QTableWidgetItem(item['name']))

            # Prix unitaire
            price_item = QTableWidgetItem(f"{item['unit_price']:.2f} DA")
            price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.items_table.setItem(i, 1, price_item)

            # Quantité
            quantity_spin = QSpinBox()
            quantity_spin.setRange(1, 1000)
            quantity_spin.setValue(int(item['quantity']))
            quantity_spin.valueChanged.connect(lambda value, row=i: self._update_item_quantity(row, value))
            self.items_table.setCellWidget(i, 2, quantity_spin)

            # Remise
            discount_spin = QDoubleSpinBox()
            discount_spin.setRange(0, 100)
            discount_spin.setSuffix(" %")
            discount_spin.setValue(item['discount_percent'])
            discount_spin.valueChanged.connect(lambda value, row=i: self._update_item_discount(row, value))
            self.items_table.setCellWidget(i, 3, discount_spin)

            # TVA
            tax_spin = QDoubleSpinBox()
            tax_spin.setRange(0, 100)
            tax_spin.setSuffix(" %")
            tax_spin.setValue(item['tax_percent'])
            tax_spin.valueChanged.connect(lambda value, row=i: self._update_item_tax(row, value))
            self.items_table.setCellWidget(i, 4, tax_spin)

            # Total
            total_item = QTableWidgetItem(f"{item['total']:.2f} DA")
            total_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.items_table.setItem(i, 5, total_item)

            # Bouton de suppression
            delete_button = QPushButton()
            delete_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
            delete_button.setFlat(True)
            delete_button.clicked.connect(lambda _, row=i: self._remove_item(row))
            self.items_table.setCellWidget(i, 6, delete_button)

    def _update_item_quantity(self, row, value):
        """Met à jour la quantité d'un article"""
        if 0 <= row < len(self.sale_items):
            self.sale_items[row]['quantity'] = value
            self._update_item_row(row)
            self.update_totals()

    def _update_item_discount(self, row, value):
        """Met à jour la remise d'un article"""
        if 0 <= row < len(self.sale_items):
            self.sale_items[row]['discount_percent'] = value
            self._update_item_row(row)
            self.update_totals()

    def _update_item_tax(self, row, value):
        """Met à jour la TVA d'un article"""
        if 0 <= row < len(self.sale_items):
            self.sale_items[row]['tax_percent'] = value
            self._update_item_row(row)
            self.update_totals()

    def _update_item_row(self, row):
        """Met à jour l'affichage d'une ligne du tableau"""
        if 0 <= row < len(self.sale_items):
            item = self.sale_items[row]

            # Calculer le total
            subtotal = item['unit_price'] * item['quantity']
            discount = subtotal * (item['discount_percent'] / 100)
            tax = (subtotal - discount) * (item['tax_percent'] / 100)
            item['total'] = subtotal - discount + tax

            # Mettre à jour le total dans le tableau
            total_item = QTableWidgetItem(f"{item['total']:.2f} DA")
            total_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.items_table.setItem(row, 5, total_item)

    def _remove_item(self, row):
        """Supprime un article de la vente"""
        if 0 <= row < len(self.sale_items):
            # Supprimer l'article de la liste
            self.sale_items.pop(row)

            # Rafraîchir le tableau
            self._refresh_items_table()

            # Mettre à jour les totaux
            self.update_totals()

    def update_totals(self):
        """Met à jour les totaux de la vente"""
        # Calculer le sous-total
        subtotal = sum(item['unit_price'] * item['quantity'] for item in self.sale_items)

        # Calculer la remise des articles
        items_discount = sum(
            item['unit_price'] * item['quantity'] * (item['discount_percent'] / 100)
            for item in self.sale_items
        )

        # Calculer la remise globale
        discount_percent = self.discount_spin.value()
        discount_amount = (subtotal - items_discount) * (discount_percent / 100)
        total_discount = items_discount + discount_amount

        # Calculer la TVA
        tax_amount = sum(
            (item['unit_price'] * item['quantity'] - item['unit_price'] * item['quantity'] * (item['discount_percent'] / 100)) * (item['tax_percent'] / 100)
            for item in self.sale_items
        )

        # Calculer le total
        total = subtotal - total_discount + tax_amount

        # Mettre à jour les labels
        self.subtotal_label.setText(f"{subtotal:.2f} DA")
        self.discount_label.setText(f"{total_discount:.2f} DA")
        self.tax_label.setText(f"{tax_amount:.2f} DA")
        self.total_label.setText(f"{total:.2f} DA")

        # Si la vente existe et que le widget de paiements est initialisé, mettre à jour les données
        if self.sale and hasattr(self, 'payments_widget') and self.payments_widget.sale_id:
            # Rafraîchir le widget de paiements
            self.payments_widget.load_data()

    def show_product_selection(self):
        """Affiche la boîte de dialogue de sélection de produit"""
        dialog = ProductSelectionDialog(self)
        dialog.product_selected.connect(self.add_product)
        dialog.exec()

    def add_product(self, product_data):
        """Ajoute un produit à la vente"""
        # Ajouter le produit à la liste
        self.sale_items.append(product_data)

        # Rafraîchir le tableau
        self._refresh_items_table()

        # Mettre à jour les totaux
        self.update_totals()

    def show_payment_dialog(self):
        """Affiche la boîte de dialogue d'ajout de paiement"""
        if not self.sale_id:
            QMessageBox.warning(self, "Attention", "Veuillez d'abord enregistrer la vente avant d'ajouter un paiement.")
            return

        # Utiliser le widget de paiements pour afficher la boîte de dialogue
        self.payments_widget.add_payment()

    def on_payment_added(self):
        """Gère l'ajout d'un paiement"""
        # Recharger les données de la vente
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Récupérer la vente
            self.sale = loop.run_until_complete(self.service.get(self.sale_id))

            # Mettre à jour les totaux
            self.update_totals()

        except Exception as e:
            print(f"Erreur lors du rechargement de la vente: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def on_invoice_toggled(self, checked):
        """Gère le changement d'état de la case à cocher de facture"""
        if checked and not self.sale_id:
            # Si c'est une nouvelle vente et qu'on active la facture,
            # définir le statut sur COMPLETED
            self.status_combo.setCurrentIndex(self.status_combo.findData(SaleStatus.COMPLETED))

    def accept(self):
        """Enregistre les données et ferme la boîte de dialogue"""
        if not self.sale_items:
            QMessageBox.warning(self, "Erreur", "Aucun article dans la vente")
            return

        # Récupérer les données de la vente
        customer_id = self.customer_combo.currentData()
        status = self.status_combo.currentData()
        payment_method = self.payment_method_combo.currentData()
        payment_status = self.payment_status_combo.currentData()
        notes = self.notes_edit.toPlainText()
        is_invoice = self.invoice_check.isChecked()

        # Créer ou mettre à jour la vente
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            if self.sale_id:
                # Mettre à jour la vente existante
                sale_data = {
                    'customer_id': customer_id,
                    'user_id': 1,  # TODO: Récupérer l'ID de l'utilisateur connecté
                    'status': status,
                    'payment_method': payment_method,
                    'payment_status': payment_status,
                    'notes': notes,
                    'is_invoice': is_invoice
                }

                # Mettre à jour la vente
                sale = loop.run_until_complete(self.service.update_sale(self.sale_id, sale_data, self.sale_items))

                # Afficher un message de succès
                QMessageBox.information(self, "Succès", f"La vente #{sale.number} a été mise à jour avec succès")
            else:
                # Créer une nouvelle vente
                sale_data = {
                    'customer_id': customer_id,
                    'user_id': 1,  # TODO: Récupérer l'ID de l'utilisateur connecté
                    'status': status,
                    'payment_method': payment_method,
                    'payment_status': payment_status,
                    'notes': notes,
                    'is_invoice': is_invoice
                }

                # Créer la vente
                sale = loop.run_until_complete(self.service.create_sale(sale_data, self.sale_items))

                # Afficher un message de succès
                QMessageBox.information(self, "Succès", f"La vente #{sale.number} a été créée avec succès")

            # Fermer la boîte de dialogue
            super().accept()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement de la vente: {str(e)}")
            print(f"Erreur lors de l'enregistrement de la vente: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()
