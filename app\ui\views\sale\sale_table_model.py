from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant
from PyQt6.QtGui import QColor, QBrush
import asyncio
from datetime import datetime, timezone
from sqlalchemy.orm import joinedload

from app.core.models.sale import Sale, SaleStatus, PaymentStatus
from app.core.services.sale_service import SaleService
from app.utils.database import SessionLocal

class SaleTableModel(QAbstractTableModel):
    """Modèle de table pour les ventes"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.service = SaleService(self.db)
        self.sales = []
        self.headers = [
            "Numéro", "Date", "Client", "Montant", "Payé", "Reste", "Statut", "Paiement"
        ]
        
    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("SaleTableModel: Session de base de données fermée")
        
    async def load_data(self):
        """Charge les données des ventes"""
        try:
            # Récupérer toutes les ventes avec les relations préchargées
            self.sales = (
                self.db.query(Sale)
                .options(joinedload(Sale.customer))
                .all()
            )
            
            # Trier les ventes par date (les plus récentes en premier)
            self.sales.sort(key=lambda x: x.date, reverse=True)
            
            # Notifier le modèle que les données ont changé
            self.layoutChanged.emit()
        except Exception as e:
            print(f"Erreur lors du chargement des données: {str(e)}")
            self.sales = []
            self.layoutChanged.emit()
        
    def rowCount(self, parent=QModelIndex()):
        """Retourne le nombre de lignes"""
        return len(self.sales)
        
    def columnCount(self, parent=QModelIndex()):
        """Retourne le nombre de colonnes"""
        return len(self.headers)
        
    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        """Retourne les en-têtes du tableau"""
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return QVariant()
        
    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        """Retourne les données pour l'affichage"""
        if not index.isValid() or index.row() >= len(self.sales):
            return QVariant()
            
        sale = self.sales[index.row()]
        column = index.column()
        
        if role == Qt.ItemDataRole.DisplayRole:
            # Données à afficher
            if column == 0:  # Numéro
                return sale.number
            elif column == 1:  # Date
                return sale.date.strftime("%d/%m/%Y %H:%M")
            elif column == 2:  # Client
                return sale.customer.name if sale.customer else "Client anonyme"
            elif column == 3:  # Montant
                return f"{sale.final_amount:.2f} DA"
            elif column == 4:  # Payé
                return f"{sale.total_paid:.2f} DA"
            elif column == 5:  # Reste
                return f"{(sale.final_amount - sale.total_paid):.2f} DA"
            elif column == 6:  # Statut
                return sale.status.value.capitalize()
            elif column == 7:  # Paiement
                return sale.payment_status.value.capitalize()
                
        elif role == Qt.ItemDataRole.TextAlignmentRole:
            # Alignement du texte
            if column in [3, 4, 5]:  # Montants
                return int(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            return int(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
                
        elif role == Qt.ItemDataRole.ForegroundRole:
            # Couleur du texte
            if column == 5:  # Reste
                remaining = sale.final_amount - sale.total_paid
                if remaining > 0:
                    return QBrush(QColor(255, 0, 0))  # Rouge pour les montants restants
                return QBrush(QColor(0, 128, 0))  # Vert pour les montants payés
            elif column == 6:  # Statut
                if sale.status == SaleStatus.COMPLETED:
                    return QBrush(QColor(0, 128, 0))  # Vert pour les ventes complétées
                elif sale.status == SaleStatus.CANCELLED:
                    return QBrush(QColor(255, 0, 0))  # Rouge pour les ventes annulées
                return QBrush(QColor(128, 128, 128))  # Gris pour les brouillons
            elif column == 7:  # Paiement
                if sale.payment_status == PaymentStatus.PAID:
                    return QBrush(QColor(0, 128, 0))  # Vert pour les paiements complets
                elif sale.payment_status == PaymentStatus.OVERDUE:
                    return QBrush(QColor(255, 0, 0))  # Rouge pour les paiements en retard
                elif sale.payment_status == PaymentStatus.PARTIAL:
                    return QBrush(QColor(255, 165, 0))  # Orange pour les paiements partiels
                return QBrush(QColor(128, 128, 128))  # Gris pour les paiements en attente
                
        elif role == Qt.ItemDataRole.BackgroundRole:
            # Couleur de fond
            if sale.status == SaleStatus.CANCELLED:
                return QBrush(QColor(255, 200, 200))  # Fond rouge clair pour les ventes annulées
                
        elif role == Qt.ItemDataRole.UserRole:
            # Données brutes pour le filtrage
            if column == 0:  # Numéro
                return sale.number
            elif column == 1:  # Date
                return sale.date
            elif column == 2:  # Client
                return sale.customer.name if sale.customer else ""
            elif column == 3:  # Montant
                return sale.final_amount
            elif column == 4:  # Payé
                return sale.total_paid
            elif column == 5:  # Reste
                return sale.final_amount - sale.total_paid
            elif column == 6:  # Statut
                return sale.status
            elif column == 7:  # Paiement
                return sale.payment_status
                
        return QVariant()
        
    def get_sale_id(self, row):
        """Retourne l'ID de la vente à la ligne spécifiée"""
        if 0 <= row < len(self.sales):
            return self.sales[row].id
        return None
