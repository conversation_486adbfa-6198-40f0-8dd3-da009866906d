from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QSplitter,
    QLabel, QPushButton, QTableView, QFrame, QMessageBox,
    QTabWidget, QComboBox, QDateEdit, QLineEdit, QStackedWidget,
    QScrollArea
)
from PyQt6.QtCore import Qt, QTimer, QDate, pyqtSignal
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime, timedelta, timezone

from .widgets.quick_sale_widget import QuickSaleWidget
from .sale_table_model import SaleTableModel
from .dialogs.sale_dialog import SaleDialog
from .dialogs.quote_dialog import QuoteDialog
from .dialogs.payment_dialog import PaymentDialog
from ...components.custom_widgets import LoadingOverlay, FilterComboBox
from ...components.custom_filter_proxy_model import CustomFilterProxyModel
from app.core.models.sale import SaleStatus, PaymentStatus, PaymentMethod
from app.core.services.sale_service import SaleService
from app.utils.database import SessionLocal

class SaleView(QWidget):
    """Vue principale pour la gestion des ventes"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = SaleService(self.db)

        # Configuration de l'interface
        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.init_data()
        self._permissions_applied = False

    def apply_permissions(self, auth_controller):
        """Active/désactive le bouton paiement selon les permissions utilisateur."""
        if not hasattr(auth_controller, 'has_permission'):
            return
        # Si la permission sales.payment existe, on l'utilise, sinon fallback
        if auth_controller.has_permission('sales.payment'):
            self.payment_button.setEnabled(True)
        elif auth_controller.has_permission('repair.view') and auth_controller.has_permission('customer.view'):
            self.payment_button.setEnabled(True)
        else:
            self.payment_button.setEnabled(False)
        self._permissions_applied = True

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("SaleView: Session de base de données fermée")

    def init_data(self):
        """Initialise le chargement des données"""
        print("Initialisation des données de vente...")
        # Utiliser QTimer pour planifier correctement le chargement asynchrone
        QTimer.singleShot(0, self._load_data_wrapper)

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)  # Réduire les marges
        main_layout.setSpacing(5)  # Réduire l'espacement

        # L'en-tête a été supprimé pour gagner de l'espace

        # En-tête modernisé (aligné sur Inventaire)
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: #fff;
                border-bottom: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px 18px 12px 18px;
                margin-bottom: 8px;
            }
        """)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setSpacing(18)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Style boutons (même que Inventaire)
        button_style = (
            "QPushButton { background: #f5f7fa; color: #1976D2; border: 1px solid #e0e0e0; border-radius: 6px; padding: 8px 18px; font-weight: 600; } "
            "QPushButton:hover { background: #e3f0fc; color: #1565c0; border: 1px solid #90caf9; } "
            "QPushButton:pressed { background: #bbdefb; color: #0d47a1; } "
            "QPushButton:disabled { background: #f5f5f5; color: #bdbdbd; border: 1px solid #eeeeee; } "
        )

        # Boutons d'action
        self.new_sale_button = QPushButton("Nouvelle Vente")
        self.new_sale_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.new_sale_button.setStyleSheet(button_style)
        header_layout.addWidget(self.new_sale_button)

        self.new_quote_button = QPushButton("Nouveau Devis")
        self.new_quote_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.new_quote_button.setStyleSheet(button_style)
        header_layout.addWidget(self.new_quote_button)

        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))
        self.export_button.setStyleSheet(button_style)
        header_layout.addWidget(self.export_button)

        self.refresh_button = QPushButton("Actualiser")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.setStyleSheet(button_style)
        header_layout.addWidget(self.refresh_button)

        # Espace flexible
        header_layout.addStretch(1)

        # Filtres (style aligné Inventaire)
        filters_widget = QWidget()
        filters_layout = QHBoxLayout(filters_widget)
        filters_layout.setContentsMargins(0, 0, 0, 0)
        filters_layout.setSpacing(10)

        # Style contrôles (aligné aux combos de l'inventaire)
        combo_style = (
            "QComboBox { background: #f5f7fa; border: 1.5px solid #e0e0e0; border-radius: 16px; padding: 6px 24px 6px 14px; font-size: 14px; color: #1976D2; } "
            "QComboBox:focus { border: 1.5px solid #1976D2; background: #fff; } "
            "QComboBox::drop-down { border: none; } "
            "QComboBox QAbstractItemView { background: #fff; border-radius: 8px; } "
        )
        date_style = (
            "QDateEdit { background: #f5f7fa; border: 1.5px solid #e0e0e0; border-radius: 16px; padding: 4px 12px; font-size: 14px; } "
            "QDateEdit:focus { border: 1.5px solid #1976D2; background: #fff; } "
        )

        # Date
        date_label = QLabel("Date:")
        date_label.setStyleSheet("font-size: 13px; font-weight: 600;")
        filters_layout.addWidget(date_label)

        self.date_filter = QDateEdit()
        self.date_filter.setDate(QDate.currentDate())
        self.date_filter.setCalendarPopup(True)
        self.date_filter.setDisplayFormat("dd/MM/yyyy")
        self.date_filter.setFixedHeight(32)
        self.date_filter.setFixedWidth(140)
        self.date_filter.setStyleSheet(date_style)
        filters_layout.addWidget(self.date_filter)

        # Statut
        status_label = QLabel("Statut:")
        status_label.setStyleSheet("font-size: 13px; font-weight: 600;")
        filters_layout.addWidget(status_label)

        self.status_filter = FilterComboBox()
        self.status_filter.addItem("Tous les statuts", None)
        for status in SaleStatus:
            self.status_filter.addItem(status.value.capitalize(), status)
        self.status_filter.setFixedHeight(32)
        self.status_filter.setFixedWidth(170)
        self.status_filter.setStyleSheet(combo_style)
        filters_layout.addWidget(self.status_filter)

        # Paiement
        payment_label = QLabel("Paiement:")
        payment_label.setStyleSheet("font-size: 13px; font-weight: 600;")
        filters_layout.addWidget(payment_label)

        self.payment_filter = FilterComboBox()
        self.payment_filter.addItem("Tous les paiements", None)
        for status in PaymentStatus:
            self.payment_filter.addItem(status.value.capitalize(), status)
        self.payment_filter.setFixedHeight(32)
        self.payment_filter.setFixedWidth(170)
        self.payment_filter.setStyleSheet(combo_style)
        filters_layout.addWidget(self.payment_filter)

        header_layout.addWidget(filters_widget)

        # Ajouter l'en-tête au layout principal
        main_layout.addWidget(header_frame)

        # Section caisse rapide maximisée (50% de l'espace) - PLEINE LARGEUR
        # Caisse rapide utilise tout l'espace disponible - 50% de l'espace
        self.quick_sale_widget = QuickSaleWidget(self.service)
        # Supprimer les contraintes de taille pour permettre l'utilisation de tout l'espace
        self.quick_sale_widget.setMinimumHeight(250)  # Hauteur minimum seulement

        # Ajouter directement au layout principal pour utiliser toute la largeur
        main_layout.addWidget(self.quick_sale_widget)

        # Espace supplémentaire sous la caisse rapide
        main_layout.addSpacing(40)  # Espacement augmenté pour bien séparer

        # Section du tableau des ventes (principale)
        table_layout = QVBoxLayout()
        table_layout.setContentsMargins(0, 5, 0, 0)  # Marge minimale
        table_layout.setSpacing(8)



        # Tableau des ventes maximisé (tout l'espace restant)
        self.table_view = QTableView()
        self.table_view.setObjectName("saleTable")
        self.table_model = SaleTableModel()
        self.proxy_model = CustomFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.table_view.setModel(self.proxy_model)

        # Configuration du tableau
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.horizontalHeader().setStretchLastSection(True)

        # Activer les lignes alternées pour une meilleure lisibilité
        self.table_view.setAlternatingRowColors(True)

        # Pas de limitation de hauteur - le tableau prend tout l'espace restant
        self.table_view.setMinimumHeight(200)  # Hauteur minimum raisonnable

        table_layout.addWidget(self.table_view)

        # Boutons d'action pour les ventes sélectionnées
        action_layout = QHBoxLayout()
        action_layout.setContentsMargins(0, 10, 0, 0)  # Marge supérieure augmentée
        action_layout.setSpacing(10)

        self.view_button = QPushButton("Voir détails")
        self.view_button.setIcon(QIcon("app/ui/resources/icons/view.svg"))
        self.view_button.setEnabled(False)
        action_layout.addWidget(self.view_button)

        self.payment_button = QPushButton("Enregistrer paiement")
        self.payment_button.setIcon(QIcon("app/ui/resources/icons/payment.svg"))
        self.payment_button.setEnabled(False)
        action_layout.addWidget(self.payment_button)

        self.invoice_button = QPushButton("Facture")
        self.invoice_button.setIcon(QIcon("app/ui/resources/icons/invoice.svg"))
        self.invoice_button.setEnabled(False)
        action_layout.addWidget(self.invoice_button)

        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.setIcon(QIcon("app/ui/resources/icons/cancel.svg"))
        self.cancel_button.setEnabled(False)
        action_layout.addWidget(self.cancel_button)

        # Espace extensible pour aligner les boutons à gauche
        action_layout.addStretch()

        table_layout.addLayout(action_layout)

        # Ajouter la section tableau au layout principal (sans espace libre)
        main_layout.addLayout(table_layout)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        # Boutons de la barre d'outils
        self.new_sale_button.clicked.connect(self.show_new_sale_dialog)
        self.new_quote_button.clicked.connect(self.show_new_quote_dialog)
        self.export_button.clicked.connect(self.export_sales)

        # Filtres
        self.date_filter.dateChanged.connect(self.filter_sales)
        self.status_filter.currentIndexChanged.connect(self.filter_sales)
        self.payment_filter.currentIndexChanged.connect(self.filter_sales)

        # Tableau des ventes
        self.table_view.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.table_view.doubleClicked.connect(self.show_sale_details)

        # Boutons d'action
        self.view_button.clicked.connect(self.show_sale_details)
        self.payment_button.clicked.connect(self.show_payment_dialog)
        self.invoice_button.clicked.connect(self.show_invoice)
        self.cancel_button.clicked.connect(self.cancel_sale)

        # Widget de caisse rapide
        self.quick_sale_widget.sale_completed.connect(self._load_data_wrapper)
        self.quick_sale_widget.refresh_data.connect(self._load_data_wrapper)

        # Bouton de rafraîchissement
        self.refresh_button.clicked.connect(self._load_data_wrapper)

    def on_selection_changed(self, selected, deselected):
        """Gère le changement de sélection dans le tableau"""
        has_selection = len(self.table_view.selectionModel().selectedRows()) > 0
        self.view_button.setEnabled(has_selection)

        if has_selection:
            # Récupérer la vente sélectionnée
            sale = self.get_selected_sale()
            if sale:
                # Activer/désactiver les boutons en fonction du statut de la vente
                self.payment_button.setEnabled(
                    sale.status == SaleStatus.COMPLETED and
                    sale.payment_status in [PaymentStatus.PENDING, PaymentStatus.PARTIAL]
                )
                self.invoice_button.setEnabled(
                    sale.status == SaleStatus.COMPLETED
                )
                self.cancel_button.setEnabled(
                    sale.status in [SaleStatus.DRAFT, SaleStatus.COMPLETED]
                )
        else:
            self.payment_button.setEnabled(False)
            self.invoice_button.setEnabled(False)
            self.cancel_button.setEnabled(False)

    def get_selected_sale(self):
        """Récupère la vente sélectionnée"""
        selected_rows = self.table_view.selectionModel().selectedRows()
        if not selected_rows:
            return None

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(selected_rows[0])
        sale_id = self.table_model.get_sale_id(source_index.row())

        # Récupérer la vente
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            sale = loop.run_until_complete(self.service.get(sale_id))
            return sale
        except Exception as e:
            print(f"Erreur lors de la récupération de la vente: {e}")
            return None
        finally:
            loop.close()

    def filter_sales(self):
        """Applique les filtres sur le tableau des ventes"""
        # Récupérer les valeurs des filtres
        date = self.date_filter.date().toPyDate()
        status = self.status_filter.currentData()
        payment_status = self.payment_filter.currentData()

        # Créer un dictionnaire de filtres
        filters = {}

        # Ajouter les filtres si nécessaire
        if status:
            filters["status"] = status

        if payment_status:
            filters["payment_status"] = payment_status

        # Appliquer les filtres
        self.proxy_model.set_filters(filters)

        # Appliquer le filtre de texte (si nécessaire)
        # self.proxy_model.set_filter_text(self.search_edit.text())

        # Appliquer les filtres
        self.proxy_model.invalidateFilter()

    def show_new_sale_dialog(self):
        """Affiche la boîte de dialogue de nouvelle vente"""
        dialog = SaleDialog(self)
        if dialog.exec():
            self._load_data_wrapper()

    def show_new_quote_dialog(self):
        """Affiche la boîte de dialogue de nouveau devis"""
        dialog = QuoteDialog(self)
        if dialog.exec():
            self._load_data_wrapper()

    def show_sale_details(self, index=None):
        """Affiche les détails d'une vente"""
        sale = self.get_selected_sale()
        if not sale:
            return

        dialog = SaleDialog(self, sale_id=sale.id)
        if dialog.exec():
            self._load_data_wrapper()

    def show_payment_dialog(self):
        """Affiche la boîte de dialogue de paiement"""
        sale = self.get_selected_sale()
        if not sale:
            return

        dialog = PaymentDialog(self, sale_id=sale.id)
        if dialog.exec():
            self._load_data_wrapper()

    def show_invoice(self):
        """Affiche la facture d'une vente"""
        sale = self.get_selected_sale()
        if not sale:
            return

        # TODO: Implémenter l'affichage de la facture
        QMessageBox.information(self, "Information", "Fonctionnalité d'affichage de facture non implémentée.")

    def cancel_sale(self):
        """Annule une vente"""
        sale = self.get_selected_sale()
        if not sale:
            return

        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            f"Êtes-vous sûr de vouloir annuler la vente #{sale.number} ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Annuler la vente
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(
                    self.service.update_sale_status(sale.id, SaleStatus.CANCELLED)
                )
                self._load_data_wrapper()
                QMessageBox.information(self, "Succès", f"La vente #{sale.number} a été annulée.")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de l'annulation de la vente: {str(e)}")
            finally:
                loop.close()

    def export_sales(self):
        """Exporte les données des ventes"""
        # TODO: Implémenter l'export des ventes
        QMessageBox.information(self, "Information", "Fonctionnalité d'export non implémentée.")



    async def load_data(self):
        """Charge les données des ventes"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau
            print("SaleView: Chargement des données...")
            await self.table_model.load_data()
            print(f"SaleView: {self.table_model.rowCount()} ventes chargées")



            # Mettre à jour la date du filtre
            self.date_filter.setDate(QDate.currentDate())

            # Appliquer les filtres
            self.filter_sales()
        except Exception as e:
            print(f"SaleView: Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.loading_overlay.hide()

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la coroutine dans la boucle
            loop.run_until_complete(self.load_data())

            # Fermer la boucle après utilisation
            loop.close()

        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
