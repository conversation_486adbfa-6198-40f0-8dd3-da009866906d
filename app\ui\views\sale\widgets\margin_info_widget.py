"""
Widget pour afficher les informations de marge dans le module de vente.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QComboBox, QSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QColor
import asyncio
from datetime import datetime

from app.core.services.cogs_service import COGSService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class MarginInfoWidget(QWidget):
    """Widget pour afficher les informations de marge"""

    # Signal émis lorsqu'un produit est sélectionné
    product_selected = pyqtSignal(int)

    def __init__(self, parent=None):
        super().__init__(parent)

        # Service
        self.db = SessionLocal()
        self.cogs_service = COGSService(self.db)

        # Configuration de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Définir une taille maximale pour le widget
        self.setMaximumHeight(400)

        # Charger les données
        self.load_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        title_layout = QHBoxLayout()
        title_label = QLabel("Analyse des marges")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        title_layout.addWidget(title_label)

        # Période
        period_layout = QHBoxLayout()
        period_label = QLabel("Période:")
        self.period_combo = QComboBox()
        self.period_combo.addItem("7 derniers jours", 7)
        self.period_combo.addItem("30 derniers jours", 30)
        self.period_combo.addItem("90 derniers jours", 90)
        self.period_combo.addItem("Année en cours", 365)
        self.period_combo.setCurrentIndex(1)  # 30 jours par défaut
        self.period_combo.currentIndexChanged.connect(self.on_period_changed)

        period_layout.addWidget(period_label)
        period_layout.addWidget(self.period_combo)
        title_layout.addLayout(period_layout)

        # Bouton de rafraîchissement
        self.refresh_button = QPushButton()
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.setToolTip("Rafraîchir")
        self.refresh_button.setFixedSize(24, 24)
        self.refresh_button.clicked.connect(self.load_data)
        title_layout.addWidget(self.refresh_button)

        main_layout.addLayout(title_layout)

        # Cadre pour les informations globales
        global_frame = QFrame()
        global_frame.setFrameShape(QFrame.Shape.StyledPanel)
        global_frame.setStyleSheet("background-color: #f5f5f5; border-radius: 5px;")
        global_layout = QVBoxLayout(global_frame)

        # Titre du cadre
        global_title = QLabel("Marge globale")
        global_title.setStyleSheet("font-weight: bold;")
        global_layout.addWidget(global_title)

        # Informations globales
        info_layout = QHBoxLayout()

        # Ventes totales
        sales_layout = QVBoxLayout()
        sales_label = QLabel("Ventes totales:")
        sales_label.setStyleSheet("color: #666;")
        self.sales_value = QLabel("0")
        self.sales_value.setStyleSheet("font-weight: bold; font-size: 16px;")
        sales_layout.addWidget(sales_label)
        sales_layout.addWidget(self.sales_value)
        info_layout.addLayout(sales_layout)

        # Chiffre d'affaires
        revenue_layout = QVBoxLayout()
        revenue_label = QLabel("Chiffre d'affaires:")
        revenue_label.setStyleSheet("color: #666;")
        self.revenue_value = QLabel("0.00 DA")
        self.revenue_value.setStyleSheet("font-weight: bold; font-size: 16px;")
        revenue_layout.addWidget(revenue_label)
        revenue_layout.addWidget(self.revenue_value)
        info_layout.addLayout(revenue_layout)

        # Coût total
        cost_layout = QVBoxLayout()
        cost_label = QLabel("Coût total:")
        cost_label.setStyleSheet("color: #666;")
        self.cost_value = QLabel("0.00 DA")
        self.cost_value.setStyleSheet("font-weight: bold; font-size: 16px;")
        cost_layout.addWidget(cost_label)
        cost_layout.addWidget(self.cost_value)
        info_layout.addLayout(cost_layout)

        # Marge brute
        margin_layout = QVBoxLayout()
        margin_label = QLabel("Marge brute:")
        margin_label.setStyleSheet("color: #666;")
        self.margin_value = QLabel("0.00 DA")
        self.margin_value.setStyleSheet("font-weight: bold; font-size: 16px;")
        margin_layout.addWidget(margin_label)
        margin_layout.addWidget(self.margin_value)
        info_layout.addLayout(margin_layout)

        # Pourcentage de marge
        percent_layout = QVBoxLayout()
        percent_label = QLabel("% Marge:")
        percent_label.setStyleSheet("color: #666;")
        self.percent_value = QLabel("0.00%")
        self.percent_value.setStyleSheet("font-weight: bold; font-size: 16px;")
        percent_layout.addWidget(percent_label)
        percent_layout.addWidget(self.percent_value)
        info_layout.addLayout(percent_layout)

        global_layout.addLayout(info_layout)
        main_layout.addWidget(global_frame)

        # Tableau des produits avec les meilleures marges
        top_margin_layout = QVBoxLayout()
        top_margin_label = QLabel("Produits avec les meilleures marges")
        top_margin_label.setStyleSheet("font-weight: bold;")
        top_margin_layout.addWidget(top_margin_label)

        self.top_margin_table = QTableWidget()
        self.top_margin_table.setColumnCount(5)
        self.top_margin_table.setHorizontalHeaderLabels([
            "Produit", "Quantité vendue", "CA", "Marge", "% Marge"
        ])
        self.top_margin_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.top_margin_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.top_margin_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.top_margin_table.cellDoubleClicked.connect(self.on_product_selected)
        self.top_margin_table.setMaximumHeight(120)  # Limiter la hauteur du tableau

        top_margin_layout.addWidget(self.top_margin_table)
        main_layout.addLayout(top_margin_layout)

        # Tableau des produits avec les marges faibles
        low_margin_layout = QVBoxLayout()
        low_margin_label = QLabel("Produits avec des marges faibles")
        low_margin_label.setStyleSheet("font-weight: bold;")
        low_margin_layout.addWidget(low_margin_label)

        self.low_margin_table = QTableWidget()
        self.low_margin_table.setColumnCount(5)
        self.low_margin_table.setHorizontalHeaderLabels([
            "Produit", "Quantité vendue", "CA", "Marge", "% Marge"
        ])
        self.low_margin_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.low_margin_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.low_margin_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.low_margin_table.cellDoubleClicked.connect(self.on_product_selected)
        self.low_margin_table.setMaximumHeight(120)  # Limiter la hauteur du tableau

        low_margin_layout.addWidget(self.low_margin_table)
        main_layout.addLayout(low_margin_layout)

    def load_data(self):
        """Charge les données"""
        self.loading_overlay.show()

        # Utiliser QTimer pour exécuter le chargement de manière asynchrone
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter le chargement des données"""
        # Utiliser directement la version synchrone pour éviter les problèmes avec asyncio
        self._load_data_sync()

    def _load_data_sync(self):
        """Version synchrone de _load_data_async"""
        try:
            # Récupérer la période sélectionnée
            period_days = self.period_combo.currentData()

            # Récupérer les informations globales (version synchrone)
            overall_margins = self.cogs_service.calculate_overall_margins_sync(period_days)

            # Mettre à jour les informations globales
            self.sales_value.setText(str(overall_margins["total_sales"]))
            self.revenue_value.setText(f"{overall_margins['total_revenue']:.2f} DA")
            self.cost_value.setText(f"{overall_margins['total_cost']:.2f} DA")
            self.margin_value.setText(f"{overall_margins['gross_margin']:.2f} DA")
            self.percent_value.setText(f"{overall_margins['margin_percent']:.2f}%")

            # Colorer le pourcentage de marge
            if overall_margins["margin_percent"] < 10:
                self.percent_value.setStyleSheet("font-weight: bold; font-size: 16px; color: #F44336;")  # Rouge
            elif overall_margins["margin_percent"] < 20:
                self.percent_value.setStyleSheet("font-weight: bold; font-size: 16px; color: #FF9800;")  # Orange
            else:
                self.percent_value.setStyleSheet("font-weight: bold; font-size: 16px; color: #4CAF50;")  # Vert

            # Récupérer les produits avec les meilleures marges (version synchrone)
            top_margin_products = self.cogs_service.get_top_margin_products_sync(limit=5, period_days=period_days)

            # Mettre à jour le tableau des produits avec les meilleures marges
            self.top_margin_table.setRowCount(len(top_margin_products))
            for i, product in enumerate(top_margin_products):
                self.top_margin_table.setItem(i, 0, QTableWidgetItem(product["product_name"]))
                self.top_margin_table.setItem(i, 1, QTableWidgetItem(str(product["total_quantity_sold"])))
                self.top_margin_table.setItem(i, 2, QTableWidgetItem(f"{product['total_revenue']:.2f} DA"))
                self.top_margin_table.setItem(i, 3, QTableWidgetItem(f"{product['gross_margin']:.2f} DA"))

                # Créer un item pour le pourcentage de marge
                percent_item = QTableWidgetItem(f"{product['margin_percent']:.2f}%")

                # Colorer le pourcentage de marge
                if product["margin_percent"] < 10:
                    percent_item.setForeground(QColor("#F44336"))  # Rouge
                elif product["margin_percent"] < 20:
                    percent_item.setForeground(QColor("#FF9800"))  # Orange
                else:
                    percent_item.setForeground(QColor("#4CAF50"))  # Vert

                self.top_margin_table.setItem(i, 4, percent_item)

                # Stocker l'ID du produit dans les données de l'item
                for j in range(5):
                    self.top_margin_table.item(i, j).setData(Qt.ItemDataRole.UserRole, product["product_id"])

            # Récupérer les produits avec des marges faibles (version synchrone)
            low_margin_products = self.cogs_service.get_low_margin_products_sync(threshold=20, period_days=period_days)

            # Mettre à jour le tableau des produits avec des marges faibles
            self.low_margin_table.setRowCount(len(low_margin_products))
            for i, product in enumerate(low_margin_products):
                self.low_margin_table.setItem(i, 0, QTableWidgetItem(product["product_name"]))
                self.low_margin_table.setItem(i, 1, QTableWidgetItem(str(product["total_quantity_sold"])))
                self.low_margin_table.setItem(i, 2, QTableWidgetItem(f"{product['total_revenue']:.2f} DA"))
                self.low_margin_table.setItem(i, 3, QTableWidgetItem(f"{product['gross_margin']:.2f} DA"))

                # Créer un item pour le pourcentage de marge
                percent_item = QTableWidgetItem(f"{product['margin_percent']:.2f}%")

                # Colorer le pourcentage de marge
                if product["margin_percent"] < 10:
                    percent_item.setForeground(QColor("#F44336"))  # Rouge
                elif product["margin_percent"] < 20:
                    percent_item.setForeground(QColor("#FF9800"))  # Orange
                else:
                    percent_item.setForeground(QColor("#4CAF50"))  # Vert

                self.low_margin_table.setItem(i, 4, percent_item)

                # Stocker l'ID du produit dans les données de l'item
                for j in range(5):
                    self.low_margin_table.item(i, j).setData(Qt.ItemDataRole.UserRole, product["product_id"])

        except Exception as e:
            print(f"Erreur lors du chargement des données: {str(e)}")
        finally:
            self.loading_overlay.hide()

    async def _load_data_async(self):
        """Charge les données de manière asynchrone"""
        try:
            # Récupérer la période sélectionnée
            period_days = self.period_combo.currentData()

            # Récupérer les informations globales
            overall_margins = await self.cogs_service.calculate_overall_margins(period_days)

            # Mettre à jour les informations globales
            self.sales_value.setText(str(overall_margins["total_sales"]))
            self.revenue_value.setText(f"{overall_margins['total_revenue']:.2f} DA")
            self.cost_value.setText(f"{overall_margins['total_cost']:.2f} DA")
            self.margin_value.setText(f"{overall_margins['gross_margin']:.2f} DA")
            self.percent_value.setText(f"{overall_margins['margin_percent']:.2f}%")

            # Colorer le pourcentage de marge
            if overall_margins["margin_percent"] < 10:
                self.percent_value.setStyleSheet("font-weight: bold; font-size: 16px; color: #F44336;")  # Rouge
            elif overall_margins["margin_percent"] < 20:
                self.percent_value.setStyleSheet("font-weight: bold; font-size: 16px; color: #FF9800;")  # Orange
            else:
                self.percent_value.setStyleSheet("font-weight: bold; font-size: 16px; color: #4CAF50;")  # Vert

            # Récupérer les produits avec les meilleures marges
            top_margin_products = await self.cogs_service.get_top_margin_products(limit=5, period_days=period_days)

            # Mettre à jour le tableau des produits avec les meilleures marges
            self.top_margin_table.setRowCount(len(top_margin_products))
            for i, product in enumerate(top_margin_products):
                self.top_margin_table.setItem(i, 0, QTableWidgetItem(product["product_name"]))
                self.top_margin_table.setItem(i, 1, QTableWidgetItem(str(product["total_quantity_sold"])))
                self.top_margin_table.setItem(i, 2, QTableWidgetItem(f"{product['total_revenue']:.2f} DA"))
                self.top_margin_table.setItem(i, 3, QTableWidgetItem(f"{product['gross_margin']:.2f} DA"))

                # Créer un item pour le pourcentage de marge
                percent_item = QTableWidgetItem(f"{product['margin_percent']:.2f}%")

                # Colorer le pourcentage de marge
                if product["margin_percent"] < 10:
                    percent_item.setForeground(QColor("#F44336"))  # Rouge
                elif product["margin_percent"] < 20:
                    percent_item.setForeground(QColor("#FF9800"))  # Orange
                else:
                    percent_item.setForeground(QColor("#4CAF50"))  # Vert

                self.top_margin_table.setItem(i, 4, percent_item)

                # Stocker l'ID du produit dans les données de l'item
                for j in range(5):
                    self.top_margin_table.item(i, j).setData(Qt.ItemDataRole.UserRole, product["product_id"])

            # Récupérer les produits avec des marges faibles
            low_margin_products = await self.cogs_service.get_low_margin_products(threshold=20, period_days=period_days)

            # Mettre à jour le tableau des produits avec des marges faibles
            self.low_margin_table.setRowCount(len(low_margin_products))
            for i, product in enumerate(low_margin_products):
                self.low_margin_table.setItem(i, 0, QTableWidgetItem(product["product_name"]))
                self.low_margin_table.setItem(i, 1, QTableWidgetItem(str(product["total_quantity_sold"])))
                self.low_margin_table.setItem(i, 2, QTableWidgetItem(f"{product['total_revenue']:.2f} DA"))
                self.low_margin_table.setItem(i, 3, QTableWidgetItem(f"{product['gross_margin']:.2f} DA"))

                # Créer un item pour le pourcentage de marge
                percent_item = QTableWidgetItem(f"{product['margin_percent']:.2f}%")

                # Colorer le pourcentage de marge
                if product["margin_percent"] < 10:
                    percent_item.setForeground(QColor("#F44336"))  # Rouge
                elif product["margin_percent"] < 20:
                    percent_item.setForeground(QColor("#FF9800"))  # Orange
                else:
                    percent_item.setForeground(QColor("#4CAF50"))  # Vert

                self.low_margin_table.setItem(i, 4, percent_item)

                # Stocker l'ID du produit dans les données de l'item
                for j in range(5):
                    self.low_margin_table.item(i, j).setData(Qt.ItemDataRole.UserRole, product["product_id"])

        except Exception as e:
            print(f"Erreur lors du chargement des données: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def on_period_changed(self, index):
        """Gère le changement de période"""
        self.load_data()

    def on_product_selected(self, row, column):
        """Gère la sélection d'un produit"""
        # Déterminer quelle table a été cliquée
        sender = self.sender()
        if sender == self.top_margin_table:
            product_id = self.top_margin_table.item(row, column).data(Qt.ItemDataRole.UserRole)
        else:
            product_id = self.low_margin_table.item(row, column).data(Qt.ItemDataRole.UserRole)

        # Émettre le signal avec l'ID du produit
        self.product_selected.emit(product_id)
