from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QLabel, QPushButton, QHeaderView, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime

from app.core.models.sale import PaymentMethod
from app.core.services.sale_service import SaleService
from app.utils.database import SessionLocal

class PaymentsWidget(QWidget):
    """Widget pour afficher l'historique des paiements d'une vente"""

    # Signal émis lorsqu'un paiement est ajouté
    payment_added = pyqtSignal()

    def __init__(self, parent=None, sale_id=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = SaleService(self.db)

        # Données
        self.sale_id = sale_id
        self.sale = None
        self.payments = []

        # Configuration de l'interface
        self.setup_ui()

        # Charger les données
        if sale_id:
            self.load_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("PaymentsWidget: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # En-tête
        header_layout = QHBoxLayout()

        self.title_label = QLabel("Historique des paiements")
        self.title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        header_layout.addWidget(self.title_label)

        header_layout.addStretch()

        self.add_button = QPushButton("Ajouter un paiement")
        self.add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_button.clicked.connect(self.add_payment)
        header_layout.addWidget(self.add_button)

        main_layout.addLayout(header_layout)

        # Tableau des paiements
        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(6)
        self.payments_table.setHorizontalHeaderLabels([
            "Date", "Montant", "Méthode", "Référence", "Notes", "Traité par"
        ])

        # Configuration du tableau
        self.payments_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        self.payments_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        self.payments_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.payments_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        self.payments_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        self.payments_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)

        # Style pour mettre en évidence la sélection
        self.payments_table.setStyleSheet("""
            QTableWidget {
                selection-background-color: #2a82da;
                selection-color: white;
                alternate-background-color: #f5f5f5;
            }
            QTableWidget::item:selected {
                background-color: #2a82da;
                color: white;
                font-weight: bold;
            }
            QTableWidget::item:hover {
                background-color: #e0e0e0;
            }
        """)

        # Activer les lignes alternées pour une meilleure lisibilité
        self.payments_table.setAlternatingRowColors(True)

        main_layout.addWidget(self.payments_table)

        # Résumé
        summary_layout = QHBoxLayout()

        self.total_label = QLabel("Total des paiements: 0.00 DA")
        self.total_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(self.total_label)

        summary_layout.addStretch()

        self.remaining_label = QLabel("Montant restant: 0.00 DA")
        self.remaining_label.setStyleSheet("font-weight: bold; color: red;")
        summary_layout.addWidget(self.remaining_label)

        main_layout.addLayout(summary_layout)

    def load_data(self):
        """Charge les données des paiements"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Récupérer la vente
            self.sale = loop.run_until_complete(self.service.get(self.sale_id))
            if not self.sale:
                print(f"Vente avec ID {self.sale_id} non trouvée")
                return

            # Récupérer les paiements
            self.payments = loop.run_until_complete(self.service.get_sale_payments(self.sale_id))

            # Mettre à jour le tableau
            self._refresh_payments_table()

            # Mettre à jour le résumé
            self._update_summary()

        except Exception as e:
            print(f"Erreur lors du chargement des paiements: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def _refresh_payments_table(self):
        """Rafraîchit le tableau des paiements"""
        self.payments_table.setRowCount(0)

        for i, payment in enumerate(self.payments):
            self.payments_table.insertRow(i)

            # Date
            date_item = QTableWidgetItem(payment.payment_date.strftime("%d/%m/%Y %H:%M"))
            self.payments_table.setItem(i, 0, date_item)

            # Montant
            amount_item = QTableWidgetItem(f"{payment.amount:.2f} DA")
            amount_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)

            # Colorer en fonction du montant (positif ou négatif)
            if payment.amount < 0:
                amount_item.setForeground(Qt.GlobalColor.red)

            self.payments_table.setItem(i, 1, amount_item)

            # Méthode de paiement
            method_item = QTableWidgetItem(self._get_payment_method_display(payment.payment_method))
            self.payments_table.setItem(i, 2, method_item)

            # Référence
            reference_item = QTableWidgetItem(payment.reference or "")
            self.payments_table.setItem(i, 3, reference_item)

            # Notes
            notes_item = QTableWidgetItem(payment.notes or "")
            self.payments_table.setItem(i, 4, notes_item)

            # Traité par
            processor_item = QTableWidgetItem(str(payment.processed_by or ""))
            self.payments_table.setItem(i, 5, processor_item)

    def _update_summary(self):
        """Met à jour le résumé des paiements"""
        if not self.sale:
            return

        # Calculer le total des paiements
        total_payments = sum(payment.amount for payment in self.payments)

        # Mettre à jour les labels
        self.total_label.setText(f"Total des paiements: {total_payments:.2f} DA")

        # Calculer le montant restant
        remaining = self.sale.final_amount - self.sale.total_paid
        self.remaining_label.setText(f"Montant restant: {remaining:.2f} DA")

        # Colorer le montant restant en fonction de sa valeur
        if remaining <= 0:
            self.remaining_label.setStyleSheet("font-weight: bold; color: green;")
        else:
            self.remaining_label.setStyleSheet("font-weight: bold; color: red;")

    def add_payment(self):
        """Affiche la boîte de dialogue d'ajout de paiement"""
        if not self.sale:
            return

        # Importer la boîte de dialogue de paiement
        from ..dialogs.payment_dialog import PaymentDialog

        dialog = PaymentDialog(self, sale_id=self.sale_id)
        if dialog.exec():
            # Recharger les données
            self.load_data()

            # Émettre le signal
            self.payment_added.emit()

    def _get_payment_method_display(self, method):
        """Retourne l'affichage de la méthode de paiement"""
        method_display = {
            PaymentMethod.cash: "Espèces",
            PaymentMethod.card: "Carte bancaire",
            PaymentMethod.transfer: "Virement",
            PaymentMethod.mobile: "Paiement mobile",
            PaymentMethod.credit: "Crédit",
            PaymentMethod.mixed: "Mixte"
        }
        return method_display.get(method, str(method))
