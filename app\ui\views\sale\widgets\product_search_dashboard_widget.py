"""
Widget de recherche de produits pour le tableau de bord des ventes
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QComboBox, QDateEdit, QGroupBox
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal, QDateTime
from PyQt6.QtGui import QIcon, QPainter
from PyQt6.QtCharts import QChart, QChartView, QLineSeries, QBarSeries, QBarSet, QDateTimeAxis, QValueAxis
import asyncio
from datetime import datetime, timedelta

from app.core.services.sale_service import SaleService
from app.core.services.inventory_service import InventoryService
from app.utils.database import SessionLocal

class ProductSearchDashboardWidget(QWidget):
    """Widget de recherche de produits pour le tableau de bord des ventes"""

    # Signal émis lorsqu'un produit est sélectionné
    product_selected = pyqtSignal(object)

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.sale_service = SaleService(self.db)
        self.inventory_service = InventoryService(self.db)

        # Variables
        self.products = []
        self.selected_product = None
        self.period_days = 30  # Par défaut, 30 jours

        # Configuration de l'interface
        self.setup_ui()

        # Connexions
        self.setup_connections()

        # Définir une taille maximale pour le widget
        self.setMaximumHeight(500)

        # Charger les données
        self.load_data()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        title = QLabel("Recherche de produits")
        title.setObjectName("sectionTitle")
        title.setStyleSheet("""
            #sectionTitle {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title)

        # Barre de recherche
        search_layout = QHBoxLayout()

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher un produit par nom, référence ou code-barres...")
        self.search_input.setClearButtonEnabled(True)
        search_layout.addWidget(self.search_input, 1)

        self.search_button = QPushButton()
        self.search_button.setIcon(QIcon("app/ui/resources/icons/search.svg"))
        self.search_button.setToolTip("Rechercher")
        search_layout.addWidget(self.search_button)

        self.barcode_button = QPushButton()
        self.barcode_button.setIcon(QIcon("app/ui/resources/icons/barcode.svg"))
        self.barcode_button.setToolTip("Scanner un code-barres")
        search_layout.addWidget(self.barcode_button)

        main_layout.addLayout(search_layout)

        # Filtres
        filters_layout = QHBoxLayout()

        # Période
        self.period_combo = QComboBox()
        self.period_combo.addItem("7 derniers jours", 7)
        self.period_combo.addItem("30 derniers jours", 30)
        self.period_combo.addItem("90 derniers jours", 90)
        self.period_combo.addItem("Cette année", 365)
        self.period_combo.addItem("Personnalisé", -1)
        self.period_combo.setCurrentIndex(1)  # 30 jours par défaut
        filters_layout.addWidget(QLabel("Période:"))
        filters_layout.addWidget(self.period_combo)

        # Dates personnalisées (cachées par défaut)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setVisible(False)
        filters_layout.addWidget(QLabel("Du:"))
        filters_layout.addWidget(self.start_date_edit)

        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setVisible(False)
        filters_layout.addWidget(QLabel("Au:"))
        filters_layout.addWidget(self.end_date_edit)

        main_layout.addLayout(filters_layout)

        # Tableau des résultats
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels([
            "Produit", "Référence", "Quantité vendue", "Chiffre d'affaires", "Marge"
        ])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.results_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.results_table.setMaximumHeight(150)  # Limiter la hauteur du tableau
        main_layout.addWidget(self.results_table)

        # Détails du produit sélectionné
        details_group = QGroupBox("Détails du produit")
        details_group.setVisible(False)  # Caché par défaut
        self.details_group = details_group

        details_layout = QVBoxLayout(details_group)

        # Nom du produit
        self.product_name_label = QLabel()
        self.product_name_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        details_layout.addWidget(self.product_name_label)

        # Informations générales
        info_layout = QHBoxLayout()

        # Référence
        self.sku_label = QLabel()
        info_layout.addWidget(QLabel("Référence:"))
        info_layout.addWidget(self.sku_label)

        # Prix d'achat
        self.purchase_price_label = QLabel()
        info_layout.addWidget(QLabel("Prix d'achat:"))
        info_layout.addWidget(self.purchase_price_label)

        # Prix de vente
        self.selling_price_label = QLabel()
        info_layout.addWidget(QLabel("Prix de vente:"))
        info_layout.addWidget(self.selling_price_label)

        # Stock actuel
        self.stock_label = QLabel()
        info_layout.addWidget(QLabel("Stock:"))
        info_layout.addWidget(self.stock_label)

        details_layout.addLayout(info_layout)

        # Statistiques de vente
        stats_layout = QHBoxLayout()

        # Quantité vendue
        self.quantity_sold_label = QLabel()
        stats_layout.addWidget(QLabel("Quantité vendue:"))
        stats_layout.addWidget(self.quantity_sold_label)

        # Chiffre d'affaires
        self.revenue_label = QLabel()
        stats_layout.addWidget(QLabel("Chiffre d'affaires:"))
        stats_layout.addWidget(self.revenue_label)

        # Marge
        self.margin_label = QLabel()
        stats_layout.addWidget(QLabel("Marge:"))
        stats_layout.addWidget(self.margin_label)

        # Marge en pourcentage
        self.margin_percent_label = QLabel()
        stats_layout.addWidget(QLabel("Marge %:"))
        stats_layout.addWidget(self.margin_percent_label)

        details_layout.addLayout(stats_layout)

        # Tableau des ventes récentes
        recent_sales_group = QGroupBox("Ventes récentes")
        recent_sales_layout = QVBoxLayout(recent_sales_group)

        self.recent_sales_table = QTableWidget()
        self.recent_sales_table.setColumnCount(5)
        self.recent_sales_table.setHorizontalHeaderLabels([
            "Date", "Numéro", "Quantité", "Prix unitaire", "Montant"
        ])
        self.recent_sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.recent_sales_table.setAlternatingRowColors(True)
        self.recent_sales_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.recent_sales_table.setMaximumHeight(100)  # Limiter la hauteur du tableau
        recent_sales_layout.addWidget(self.recent_sales_table)

        details_layout.addWidget(recent_sales_group)

        # Graphique des ventes
        chart_group = QGroupBox("Évolution des ventes")
        chart_layout = QVBoxLayout(chart_group)

        self.sales_chart = QChart()
        self.sales_chart.setTitle("Évolution des ventes")
        self.sales_chart.setAnimationOptions(QChart.AnimationOption.SeriesAnimations)

        chart_view = QChartView(self.sales_chart)
        chart_view.setRenderHint(QPainter.RenderHint.Antialiasing)
        chart_view.setMinimumHeight(150)
        chart_view.setMaximumHeight(150)

        chart_layout.addWidget(chart_view)

        details_layout.addWidget(chart_group)

        main_layout.addWidget(details_group)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.search_input.returnPressed.connect(self.search_products)
        self.search_button.clicked.connect(self.search_products)
        self.barcode_button.clicked.connect(self.scan_barcode)
        self.period_combo.currentIndexChanged.connect(self.on_period_changed)
        self.start_date_edit.dateChanged.connect(self.on_custom_date_changed)
        self.end_date_edit.dateChanged.connect(self.on_custom_date_changed)
        self.results_table.itemSelectionChanged.connect(self.on_product_selected)

    def load_data(self):
        """Charge les données initiales"""
        try:
            # Utiliser une approche synchrone pour charger les produits
            from sqlalchemy import select
            from app.core.models.inventory import InventoryItem

            # Requête directe à la base de données
            query = select(InventoryItem).where(InventoryItem.is_active == True)
            result = self.db.execute(query)
            self.products = list(result.scalars().all())

            print(f"ProductSearchDashboardWidget: {len(self.products)} produits chargés")
        except Exception as e:
            print(f"Erreur lors du chargement des produits: {e}")
            self.products = []

    def search_products(self):
        """Recherche des produits et affiche leurs statistiques de vente"""
        search_text = self.search_input.text().strip().lower()

        if not search_text:
            return

        # Filtrer les produits
        filtered_products = []
        for product in self.products:
            if (search_text in product.name.lower() or
                (hasattr(product, 'sku') and product.sku and search_text in product.sku.lower()) or
                (hasattr(product, 'barcode') and product.barcode and search_text in product.barcode.lower())):
                filtered_products.append(product)

        # Afficher les résultats
        self.display_results(filtered_products)

    def scan_barcode(self):
        """Ouvre une boîte de dialogue pour scanner un code-barres"""
        from app.ui.components.barcode_scanner_dialog import BarcodeScannerDialog

        dialog = BarcodeScannerDialog(self)
        dialog.barcode_detected.connect(self.process_scanned_barcode)

        dialog.exec()

    def process_scanned_barcode(self, barcode):
        """Traite un code-barres scanné"""
        if not barcode:
            return

        # Mettre le code-barres dans la barre de recherche
        self.search_input.setText(barcode)

        # Lancer la recherche
        self.search_products()

    def on_period_changed(self, index):
        """Gère le changement de période"""
        period = self.period_combo.currentData()

        # Afficher/masquer les champs de date personnalisée
        custom_period = period == -1
        self.start_date_edit.setVisible(custom_period)
        self.end_date_edit.setVisible(custom_period)

        # Mettre à jour la période
        if not custom_period:
            self.period_days = period

            # Si un produit est sélectionné, mettre à jour ses statistiques
            if self.selected_product:
                self.update_product_stats(self.selected_product)

    def on_custom_date_changed(self, date):
        """Gère le changement de date personnalisée"""
        # Calculer le nombre de jours entre les deux dates
        start_date = self.start_date_edit.date().toPyDate()
        end_date = self.end_date_edit.date().toPyDate()

        # Vérifier que la date de début est antérieure à la date de fin
        if start_date > end_date:
            # Inverser les dates
            self.start_date_edit.setDate(QDate.fromString(end_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
            self.end_date_edit.setDate(QDate.fromString(start_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
            return

        # Calculer le nombre de jours
        delta = end_date - start_date
        self.period_days = delta.days + 1  # +1 pour inclure le jour de fin

        # Si un produit est sélectionné, mettre à jour ses statistiques
        if self.selected_product:
            self.update_product_stats(self.selected_product)

    def on_product_selected(self):
        """Gère la sélection d'un produit dans le tableau"""
        selected_items = self.results_table.selectedItems()
        if not selected_items:
            return

        # Récupérer l'ID du produit sélectionné
        row = selected_items[0].row()
        product_id = self.results_table.item(row, 0).data(Qt.ItemDataRole.UserRole)

        # Trouver le produit
        for product in self.products:
            if product.id == product_id:
                self.selected_product = product
                self.update_product_stats(product)
                self.product_selected.emit(product)
                break

    def display_results(self, products):
        """Affiche les résultats de la recherche"""
        # Effacer le tableau
        self.results_table.setRowCount(0)

        # Ajouter les produits
        for product in products:
            row = self.results_table.rowCount()
            self.results_table.insertRow(row)

            # Nom du produit
            name_item = QTableWidgetItem(product.name)
            name_item.setData(Qt.ItemDataRole.UserRole, product.id)
            self.results_table.setItem(row, 0, name_item)

            # Référence
            sku = getattr(product, 'sku', '')
            sku_item = QTableWidgetItem(sku)
            self.results_table.setItem(row, 1, sku_item)

            # Récupérer les statistiques de vente
            try:
                # Créer une nouvelle boucle d'événements
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # Récupérer les statistiques
                stats = loop.run_until_complete(self.get_product_sales_stats(product.id))

                # Quantité vendue
                quantity_item = QTableWidgetItem(str(stats['quantity_sold']))
                quantity_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.results_table.setItem(row, 2, quantity_item)

                # Chiffre d'affaires
                revenue_item = QTableWidgetItem(f"{stats['revenue']:.2f} DA")
                revenue_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.results_table.setItem(row, 3, revenue_item)

                # Marge
                margin_item = QTableWidgetItem(f"{stats['margin']:.2f} DA")
                margin_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.results_table.setItem(row, 4, margin_item)

                # Fermer la boucle après utilisation
                loop.close()

            except Exception as e:
                print(f"Erreur lors de la récupération des statistiques de vente: {e}")

                # Valeurs par défaut
                self.results_table.setItem(row, 2, QTableWidgetItem("0"))
                self.results_table.setItem(row, 3, QTableWidgetItem("0.00 DA"))
                self.results_table.setItem(row, 4, QTableWidgetItem("0.00 DA"))

        # Sélectionner la première ligne si disponible
        if self.results_table.rowCount() > 0:
            self.results_table.selectRow(0)

    def update_product_stats(self, product):
        """Met à jour les statistiques d'un produit"""
        # Afficher le groupe de détails
        self.details_group.setVisible(True)

        # Mettre à jour les informations générales
        self.product_name_label.setText(product.name)
        self.sku_label.setText(getattr(product, 'sku', 'N/A'))
        self.purchase_price_label.setText(f"{getattr(product, 'purchase_price', 0):.2f} DA")
        # Pour le prix de vente, on garde unit_price car c'est effectivement le prix de vente
        self.selling_price_label.setText(f"{getattr(product, 'unit_price', 0):.2f} DA")
        self.stock_label.setText(str(getattr(product, 'quantity', 0)))

        # Récupérer les statistiques de vente
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Récupérer les statistiques
            stats = loop.run_until_complete(self.get_product_sales_stats(product.id))

            # Mettre à jour les statistiques
            self.quantity_sold_label.setText(str(stats['quantity_sold']))
            self.revenue_label.setText(f"{stats['revenue']:.2f} DA")
            self.margin_label.setText(f"{stats['margin']:.2f} DA")
            self.margin_percent_label.setText(f"{stats['margin_percent']:.2f}%")

            # Mettre à jour le tableau des ventes récentes
            self.update_recent_sales_table(stats.get('recent_sales', []))

            # Mettre à jour le graphique des ventes
            self.update_sales_chart(stats.get('sales_by_day', []))

            # Fermer la boucle après utilisation
            loop.close()

        except Exception as e:
            print(f"Erreur lors de la récupération des statistiques de vente: {e}")

            # Valeurs par défaut
            self.quantity_sold_label.setText("0")
            self.revenue_label.setText("0.00 DA")
            self.margin_label.setText("0.00 DA")
            self.margin_percent_label.setText("0.00%")

            # Vider le tableau et le graphique
            self.update_recent_sales_table([])
            self.update_sales_chart([])

    def update_recent_sales_table(self, recent_sales):
        """Met à jour le tableau des ventes récentes"""
        # Vérifier si le tableau existe
        if not hasattr(self, 'recent_sales_table'):
            return

        # Effacer le tableau
        self.recent_sales_table.setRowCount(0)

        # Ajouter les ventes récentes
        for i, sale in enumerate(recent_sales):
            row = self.recent_sales_table.rowCount()
            self.recent_sales_table.insertRow(row)

            # Date
            date_item = QTableWidgetItem(sale['date'])
            self.recent_sales_table.setItem(row, 0, date_item)

            # Numéro de vente
            number_item = QTableWidgetItem(sale['sale_number'])
            self.recent_sales_table.setItem(row, 1, number_item)

            # Quantité
            quantity_item = QTableWidgetItem(str(sale['quantity']))
            quantity_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.recent_sales_table.setItem(row, 2, quantity_item)

            # Prix unitaire
            price_item = QTableWidgetItem(f"{sale['unit_price']:.2f} DA")
            price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.recent_sales_table.setItem(row, 3, price_item)

            # Montant total
            amount_item = QTableWidgetItem(f"{sale['total_amount']:.2f} DA")
            amount_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.recent_sales_table.setItem(row, 4, amount_item)

    def update_sales_chart(self, sales_by_day):
        """Met à jour le graphique des ventes"""
        # Vérifier si le graphique existe
        if not hasattr(self, 'sales_chart'):
            return

        # Effacer le graphique
        self.sales_chart.clear()

        # Si aucune donnée, ne rien faire
        if not sales_by_day:
            return

        # Créer les séries de données

        # Série pour les quantités
        quantity_series = QLineSeries()
        quantity_series.setName("Quantité vendue")

        # Série pour les montants
        amount_series = QBarSet("Montant")

        # Ajouter les données
        dates = []
        for day_data in sales_by_day:
            # Convertir la date
            date = QDateTime.fromString(day_data['day'], "yyyy-MM-dd")
            dates.append(date)

            # Ajouter les données aux séries
            quantity_series.append(date.toMSecsSinceEpoch(), day_data['quantity'])
            amount_series.append(day_data['amount'])

        # Créer la série de barres
        bar_series = QBarSeries()
        bar_series.append(amount_series)

        # Ajouter les séries au graphique
        self.sales_chart.addSeries(quantity_series)
        self.sales_chart.addSeries(bar_series)

        # Créer les axes
        date_axis = QDateTimeAxis()
        date_axis.setFormat("dd/MM")
        date_axis.setTitleText("Date")

        value_axis = QValueAxis()
        value_axis.setTitleText("Quantité")

        amount_axis = QValueAxis()
        amount_axis.setTitleText("Montant (DA)")

        # Ajouter les axes au graphique
        self.sales_chart.addAxis(date_axis, Qt.AlignmentFlag.AlignBottom)
        self.sales_chart.addAxis(value_axis, Qt.AlignmentFlag.AlignLeft)
        self.sales_chart.addAxis(amount_axis, Qt.AlignmentFlag.AlignRight)

        # Attacher les séries aux axes
        quantity_series.attachAxis(date_axis)
        quantity_series.attachAxis(value_axis)

        bar_series.attachAxis(date_axis)
        bar_series.attachAxis(amount_axis)

        # Configurer les axes
        if dates:
            min_date = min(dates)
            max_date = max(dates)
            date_axis.setRange(min_date, max_date)

    async def get_product_sales_stats(self, product_id):
        """Récupère les statistiques de vente d'un produit"""
        # Calculer les dates de début et de fin
        end_date = datetime.now()
        start_date = end_date - timedelta(days=self.period_days)

        # Utiliser le service de vente pour récupérer les statistiques
        try:
            stats = await self.sale_service.get_product_sales_stats(product_id, start_date, end_date)
            return stats
        except Exception as e:
            print(f"Erreur lors de la récupération des statistiques de vente: {e}")

            # Valeurs par défaut en cas d'erreur
            return {
                'quantity_sold': 0,
                'revenue': 0,
                'cost': 0,
                'margin': 0,
                'margin_percent': 0,
                'sale_count': 0,
                'recent_sales': [],
                'sales_by_day': []
            }

    def closeEvent(self, event):
        """Gère l'événement de fermeture du widget"""
        # Fermer la session de base de données
        if hasattr(self, 'db') and self.db:
            self.db.close()

        super().closeEvent(event)
