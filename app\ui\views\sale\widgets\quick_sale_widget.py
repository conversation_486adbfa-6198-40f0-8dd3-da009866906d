from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem,
    QComboBox, QDoubleSpinBox, QMessageBox, QHeaderView,
    QFrame, QLineEdit, QSpinBox, QSizePolicy, QSplitter,
    QInputDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QFont
import asyncio
from datetime import datetime, timezone

from app.core.models.sale import SaleStatus, PaymentMethod, PaymentStatus
from app.core.services.customer_service import CustomerService
from app.core.services.inventory_service import InventoryService
from app.utils.database import SessionLocal
from ..dialogs.product_selection_dialog import ProductSelectionDialog

class QuickSaleWidget(QWidget):
    """Widget de caisse rapide pour les ventes"""

    # Signal émis lorsqu'une vente est complétée
    sale_completed = pyqtSignal()
    # Signal émis pour rafraîchir les données
    refresh_data = pyqtSignal()

    def __init__(self, sale_service, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.sale_service = sale_service
        self.customer_service = CustomerService(self.db)
        self.inventory_service = InventoryService(self.db)

        # Données de la vente
        self.sale_items = []
        self.customer_id = None

        # Configuration de l'interface
        self.setup_ui()
        self.setup_connections()

        # Charger les données initiales
        self.init_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("QuickSaleWidget: Session de base de données fermée")

    def init_data(self):
        """Initialise le chargement des données"""
        QTimer.singleShot(0, self._load_customers)

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Forcer le widget à prendre tout l'espace disponible
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.setMinimumWidth(1200)  # Largeur minimum pour forcer l'expansion

        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(0, 0, 0, 0)  # Supprimer les marges pour utiliser tout l'espace



        # Layout horizontal avec étirement forcé
        content_widget = QWidget()
        content_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        content_layout = QHBoxLayout(content_widget)
        content_layout.setSpacing(15)  # Espacement réduit pour plus de fluidité
        content_layout.setContentsMargins(10, 10, 10, 10)  # Marges réduites
        # Aligner les panneaux en haut pour éviter que les widgets du bas soient masqués
        content_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # === SECTION GAUCHE : COMMANDES RÉORGANISÉES ===
        left_panel = QWidget()
        left_panel.setFixedWidth(550)  # Augmenté à 550px pour que le texte soit lisible
        left_panel.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)

        left_layout = QVBoxLayout(left_panel)
        # Réduire les marges et l'espacement pour remonter les éléments
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(12)

        # === NOUVELLE APPROCHE : WIDGETS DIRECTEMENT DANS LE LAYOUT PRINCIPAL ===

        # 1. COMBOBOX CLIENT - Directement dans left_layout


        self.customer_combo = QComboBox()
        self.customer_combo.setMinimumHeight(40)
        self.customer_combo.setStyleSheet("""
            QComboBox {
                font-size: 13px;
                padding: 8px;
                border: 2px solid #3498db;
                border-radius: 6px;
                background-color: white;
            }
        """)
        left_layout.addWidget(self.customer_combo)

        # Espacement réduit
        left_layout.addSpacing(8)

        # 2. BOUTON F3 - Directement dans left_layout


        self.add_button = QPushButton("🔍 Rechercher produit (F3)")
        self.add_button.setShortcut("F3")
        self.add_button.setMinimumHeight(45)
        self.add_button.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                padding: 10px;
                border: 2px solid #27ae60;
                border-radius: 6px;
                background-color: #27ae60;
                color: white;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        left_layout.addWidget(self.add_button)

        # Espacement réduit
        left_layout.addSpacing(8)

        # 3. BOUTON SUPPRIMER - Directement dans left_layout


        self.remove_selected_button = QPushButton("🗑️ Supprimer dernier")
        self.remove_selected_button.setMinimumHeight(35)
        self.remove_selected_button.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                padding: 8px;
                border: 2px solid #e74c3c;
                border-radius: 6px;
                background-color: #e74c3c;
                color: white;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        left_layout.addWidget(self.remove_selected_button)

        # Espacement réduit
        left_layout.addSpacing(6)

        # 4. SPINBOX REMISE - Directement dans left_layout

        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setRange(0, 100)
        self.discount_spin.setSuffix(" %")
        self.discount_spin.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.discount_spin.setMinimumHeight(35)
        self.discount_spin.setStyleSheet("""
            QDoubleSpinBox {
                font-size: 13px;
                padding: 8px;
                border: 2px solid #f39c12;
                border-radius: 6px;
                background-color: white;
            }
        """)
        left_layout.addWidget(self.discount_spin)
        left_layout.addStretch()  # Pousser tout vers le haut

        # === SECTION DROITE : INFORMATIONS PRODUITS ===
        right_panel = QWidget()
        right_panel.setMinimumWidth(500)  # Réduit pour laisser plus d'espace au panneau gauche
        right_panel.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        right_panel.setStyleSheet("QWidget { border: 2px solid #3498db; border-radius: 8px; background-color: white; }")
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(15, 15, 15, 15)

        # Titre du tableau
        # Zone d'information simple pour les produits
        info_title = QLabel("🛒 Produits dans le panier")
        info_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2980b9;
            margin-bottom: 10px;
            padding: 8px;
        """)
        right_layout.addWidget(info_title)

        # Informations sur les produits ajoutés (texte simple)
        self.products_info = QLabel("Aucun produit sélectionné")
        self.products_info.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #7f8c8d;
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                min-height: 150px;
            }
        """)
        self.products_info.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)
        self.products_info.setWordWrap(True)
        right_layout.addWidget(self.products_info)

        # === SECTION BOUTONS ET TOTAL DÉPLACÉE DANS LE PANNEAU DROIT ===
        # Espacement avant les boutons
        right_layout.addSpacing(20)

        # Layout pour total et boutons
        bottom_right_layout = QHBoxLayout()
        bottom_right_layout.setSpacing(15)

        # Total à gauche dans le panneau droit
        self.total_label = QLabel("Total: 0.00 DA")
        self.total_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #27ae60;
                background-color: #f8f9fa;
                padding: 12px 20px;
                border-radius: 6px;
                border: 2px solid #27ae60;
                min-width: 150px;
            }
        """)
        self.total_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        bottom_right_layout.addWidget(self.total_label)

        # Boutons d'action à droite dans le panneau droit
        self.cancel_button = QPushButton("❌ Annuler")
        self.cancel_button.setMinimumSize(100, 40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        bottom_right_layout.addWidget(self.cancel_button)

        self.save_button = QPushButton("💾 Enregistrer")
        self.save_button.setMinimumSize(100, 40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        bottom_right_layout.addWidget(self.save_button)

        self.complete_button = QPushButton("✅ Terminer")
        self.complete_button.setMinimumSize(100, 40)
        self.complete_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        bottom_right_layout.addWidget(self.complete_button)

        # Ajouter le layout des boutons au panneau droit
        right_layout.addLayout(bottom_right_layout)

        # Ajouter les panneaux avec étirement explicite
        content_layout.addWidget(left_panel, 0)   # 0 = pas d'étirement
        content_layout.addWidget(right_panel, 1)  # 1 = étirement maximum

        main_layout.addWidget(content_widget)



    def setup_connections(self):
        """Configure les connexions des signaux"""
        # Boutons d'action
        self.add_button.clicked.connect(self.show_product_selection)
        self.cancel_button.clicked.connect(self.clear_sale)
        self.save_button.clicked.connect(self.save_sale)
        self.complete_button.clicked.connect(self.complete_sale)
        self.remove_selected_button.clicked.connect(self.remove_selected_item)

        # Calculs
        self.discount_spin.valueChanged.connect(self.update_totals)



    def _load_customers(self):
        """Charge la liste des clients"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Récupérer tous les clients
            customers = loop.run_until_complete(self.customer_service.get_all())

            # Ajouter les clients au combo
            self.customer_combo.clear()
            self.customer_combo.addItem("Client anonyme", None)

            for customer in customers:
                if customer.active:  # Ne pas afficher les clients inactifs
                    self.customer_combo.addItem(customer.name, customer.id)

        except Exception as e:
            print(f"Erreur lors du chargement des clients: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()



    def add_product(self, product_id, quantity=1):
        """Ajoute un produit à la vente"""
        # Vérifier si le produit existe déjà dans la vente
        for i, item in enumerate(self.sale_items):
            if item['product_id'] == product_id:
                # Vérifier la disponibilité du stock pour la nouvelle quantité totale
                new_quantity = item['quantity'] + quantity
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    # Vérifier la disponibilité du stock
                    stock_check = loop.run_until_complete(
                        self.inventory_service.check_stock_availability(product_id, new_quantity)
                    )

                    if not stock_check["available"]:
                        # Afficher un avertissement si le stock est insuffisant
                        QMessageBox.warning(
                            self,
                            "Stock insuffisant",
                            stock_check["message"]
                        )
                        return

                    # Mettre à jour la quantité
                    item['quantity'] = new_quantity
                    self._update_products_display()
                    self.update_totals()

                    # Afficher un avertissement si le stock est bas
                    if stock_check["current_stock"] <= new_quantity + 5:
                        QMessageBox.information(
                            self,
                            "Stock bas",
                            f"Attention: Il ne reste que {stock_check['current_stock'] - new_quantity} unités en stock après cette vente."
                        )
                finally:
                    loop.close()
                return

        # Récupérer les informations du produit
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Vérifier la disponibilité du stock
            stock_check = loop.run_until_complete(
                self.inventory_service.check_stock_availability(product_id, quantity)
            )

            if not stock_check["available"]:
                # Afficher un avertissement si le stock est insuffisant
                QMessageBox.warning(
                    self,
                    "Stock insuffisant",
                    stock_check["message"]
                )
                return

            product = stock_check["product"]
            if not product:
                QMessageBox.warning(self, "Erreur", "Produit non trouvé")
                return

            # Ajouter le produit à la liste
            item = {
                'product_id': product_id,
                'name': product.name,
                'unit_price': product.unit_price,
                'quantity': quantity,
                'discount_percent': 0,
                'total': product.unit_price * quantity,
                'current_stock': stock_check["current_stock"]
            }

            self.sale_items.append(item)

            # Mettre à jour l'affichage des produits
            self._update_products_display()

            # Mettre à jour les totaux
            self.update_totals()

            # Afficher un avertissement si le stock est bas
            if stock_check["current_stock"] <= quantity + 5:
                QMessageBox.information(
                    self,
                    "Stock bas",
                    f"Attention: Il ne reste que {stock_check['current_stock'] - quantity} unités en stock après cette vente."
                )

        except Exception as e:
            print(f"Erreur lors de l'ajout du produit: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()



    def _update_products_display(self):
        """Met à jour l'affichage textuel des produits"""
        if not self.sale_items:
            self.products_info.setText("Aucun produit sélectionné")
            return

        # Construire le texte d'affichage
        text_lines = []
        for i, item in enumerate(self.sale_items, 1):
            line = f"{i}. {item['name']}"
            line += f"\n   Quantité: {item['quantity']}"
            line += f" | Prix: {item['unit_price']:.2f} DA"
            line += f" | Total: {item['unit_price'] * item['quantity']:.2f} DA"
            text_lines.append(line)

        display_text = "\n\n".join(text_lines)
        self.products_info.setText(display_text)

    def _remove_item(self, row):
        """Supprime un article de la vente"""
        if 0 <= row < len(self.sale_items):
            # Supprimer l'article de la liste
            self.sale_items.pop(row)

            # Mettre à jour l'affichage
            self._update_products_display()

            # Mettre à jour les totaux
            self.update_totals()

    def remove_selected_item(self):
        """Supprime le dernier article ajouté"""
        if self.sale_items:
            self._remove_item(len(self.sale_items) - 1)

    def update_totals(self):
        """Met à jour les totaux de la vente"""
        # Calculer le sous-total
        subtotal = sum(item['unit_price'] * item['quantity'] for item in self.sale_items)

        # Calculer la remise globale
        discount_percent = self.discount_spin.value()
        discount_amount = subtotal * (discount_percent / 100)

        # Calculer le total
        total = subtotal - discount_amount

        # Mettre à jour le label total simplifié
        self.total_label.setText(f"Total: {total:.2f} DA")

        # Effet visuel selon le montant
        if total > 0:
            self.total_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #27ae60;
                    background-color: #f8f9fa;
                    padding: 10px 15px;
                    border-radius: 8px;
                    border: 2px solid #27ae60;
                }
            """)
        else:
            self.total_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #95a5a6;
                    background-color: #f8f9fa;
                    padding: 10px 15px;
                    border-radius: 8px;
                    border: 2px solid #bdc3c7;
                }
            """)

    def show_product_selection(self):
        """Affiche la boîte de dialogue de sélection de produit"""
        dialog = ProductSelectionDialog(self)
        dialog.product_selected.connect(self.on_product_selected)
        dialog.exec()

    def on_product_selected(self, product_data):
        """Gère la sélection d'un produit"""
        # Ajouter le produit à la vente
        self.add_product(product_data['product_id'], product_data['quantity'])

    def clear_sale(self):
        """Efface la vente en cours"""
        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir annuler cette vente ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Effacer les articles
            self.sale_items = []
            self._update_products_display()

            # Réinitialiser les totaux
            self.discount_spin.setValue(0)
            self.update_totals()

            # Réinitialiser le client
            self.customer_combo.setCurrentIndex(0)

    def save_sale(self):
        """Enregistre la vente comme brouillon"""
        if not self.sale_items:
            QMessageBox.warning(self, "Erreur", "Aucun article dans la vente")
            return

        # Récupérer les données de la vente
        customer_id = self.customer_combo.currentData()

        # Créer la vente
        sale_data = {
            'customer_id': customer_id,
            'user_id': 1,  # TODO: Récupérer l'ID de l'utilisateur connecté
            'status': SaleStatus.DRAFT,
            'notes': "Vente rapide"
        }

        # Créer la vente
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.sale_service.create_sale(sale_data, self.sale_items))

            # Afficher un message de succès
            QMessageBox.information(self, "Succès", "La vente a été enregistrée comme brouillon")

            # Effacer la vente
            self.clear_sale()

            # Émettre les signaux
            self.sale_completed.emit()
            self.refresh_data.emit()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement de la vente: {str(e)}")
            print(f"Erreur lors de l'enregistrement de la vente: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def complete_sale(self):
        """Termine la vente et enregistre le paiement"""
        if not self.sale_items:
            QMessageBox.warning(self, "Erreur", "Aucun article dans la vente")
            return

        # Récupérer les données de la vente
        customer_id = self.customer_combo.currentData()
        total_text = self.total_label.text().replace("Total: ", "").replace(" DA", "")
        total = float(total_text)

        # Vérifier la disponibilité du stock pour tous les articles
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Vérifier le stock pour chaque article
            stock_issues = []
            for item in self.sale_items:
                stock_check = loop.run_until_complete(
                    self.inventory_service.check_stock_availability(
                        item['product_id'],
                        item['quantity']
                    )
                )
                if not stock_check["available"]:
                    stock_issues.append({
                        "product": stock_check["product"].name,
                        "message": stock_check["message"]
                    })

            # S'il y a des problèmes de stock, afficher un message d'erreur
            if stock_issues:
                error_message = "Impossible de compléter la vente en raison de problèmes de stock:\n\n"
                for issue in stock_issues:
                    error_message += f"- {issue['product']}: {issue['message']}\n"
                QMessageBox.critical(self, "Erreur de stock", error_message)
                return
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la vérification du stock: {str(e)}")
            print(f"Erreur lors de la vérification du stock: {e}")
            import traceback
            traceback.print_exc()
            return
        finally:
            loop.close()

        # Demander le mode de paiement à l'utilisateur
        payment_methods = {
            "Espèces": PaymentMethod.cash,
            "Carte": PaymentMethod.card,
            "Chèque": PaymentMethod.cheque
        }
        method_names = list(payment_methods.keys())
        selected_method, ok = QInputDialog.getItem(
            self,
            "Mode de paiement",
            "Sélectionnez le mode de paiement:",
            method_names,
            0,
            False
        )
        if not ok:
            return
        payment_method = payment_methods[selected_method]

        # Demander le montant reçu
        amount_received, ok = QInputDialog.getDouble(
            self,
            "Montant reçu",
            "Entrez le montant reçu:",
            total,
            0,
            1000000,
            2
        )
        if not ok:
            return

        # Créer la vente
        sale_data = {
            'customer_id': customer_id,
            'user_id': 1,  # TODO: Récupérer l'ID de l'utilisateur connecté
            'status': SaleStatus.COMPLETED,
            'payment_method': payment_method,
            'payment_status': PaymentStatus.PAID if amount_received >= total else PaymentStatus.PARTIAL,
            'notes': "Vente rapide",
            'is_invoice': True,
            'payment': {
                'amount': min(amount_received, total),
                'payment_method': payment_method,
                'reference': None
            }
        }

        # Créer la vente
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Créer la vente (pas besoin de vérifier à nouveau le stock car on l'a déjà fait)
            sale = loop.run_until_complete(
                self.sale_service.create_sale(sale_data, self.sale_items, check_stock=False)
            )

            # Afficher un message de succès
            message = f"La vente #{sale.number} a été complétée avec succès"

            # Afficher le rendu de monnaie si nécessaire
            if payment_method == PaymentMethod.cash and amount_received > total:
                change = amount_received - total
                message += f"\n\nRendu de monnaie: {change:.2f} DA"

            QMessageBox.information(self, "Succès", message)

            # Effacer la vente
            self.clear_sale()

            # Émettre les signaux
            self.sale_completed.emit()
            self.refresh_data.emit()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement de la vente: {str(e)}")
            print(f"Erreur lors de l'enregistrement de la vente: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()
