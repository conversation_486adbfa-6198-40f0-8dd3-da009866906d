from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QLabel, QFrame, QSizePolicy
)
from PyQt6.QtCore import Qt
from datetime import datetime

class StatCard(QFrame):
    """Carte de statistique pour le tableau de bord - Version ultra-compacte et horizontale"""

    def __init__(self, title, value, color="#2a82da", parent=None):
        super().__init__(parent)

        # Configuration du style minimaliste
        self.setFrameShape(QFrame.Shape.NoFrame)
        self.setObjectName("statCard")
        self.setStyleSheet(f"""
            #statCard {{
                background-color: #f8f9fa;
                border-left: 3px solid {color};
                border-radius: 2px;
            }}
        """)

        # Mise en page horizontale pour économiser de l'espace vertical
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)  # Marges minimales
        layout.setSpacing(5)  # Espacement minimal

        # Titre
        title_label = QLabel(title + ":")
        title_label.setObjectName("statCardTitle")
        title_label.setStyleSheet("""
            #statCardTitle {
                font-size: 10px;
                color: #555;
                margin: 0;
                padding: 0;
            }
        """)
        layout.addWidget(title_label)

        # Espacement extensible
        layout.addStretch()

        # Valeur
        value_label = QLabel(value)
        value_label.setObjectName("statCardValue")
        value_label.setStyleSheet(f"""
            #statCardValue {{
                font-size: 12px;
                font-weight: bold;
                color: {color};
                margin: 0;
                padding: 0;
            }}
        """)
        layout.addWidget(value_label)

        # Définir une taille ultra-compacte
        self.setFixedHeight(24)  # Hauteur fixe minimale
        self.setMinimumWidth(80)  # Largeur minimale
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

class SaleDashboardWidget(QWidget):
    """Widget de tableau de bord des ventes"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Configuration de l'interface
        self.setup_ui()

        # Définir une politique de taille adaptative au lieu d'une hauteur maximale fixe
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)  # Supprimer les marges
        main_layout.setSpacing(2)  # Espacement minimal

        # Titre et date sur la même ligne, très compact
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(2, 2, 2, 2)
        header_layout.setSpacing(5)

        # Titre minimaliste
        title = QLabel("Ventes")
        title.setStyleSheet("font-size: 10px; font-weight: bold;")
        header_layout.addWidget(title)

        # Date du jour (alignée à droite)
        self.date_label = QLabel(datetime.now().strftime("%d/%m/%Y"))
        self.date_label.setStyleSheet("font-size: 9px; color: #666;")
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        header_layout.addWidget(self.date_label)

        main_layout.addLayout(header_layout)

        # Cartes de statistiques en grille (1x4) pour une organisation horizontale
        stats_layout = QHBoxLayout()
        stats_layout.setContentsMargins(2, 2, 2, 2)
        stats_layout.setSpacing(4)  # Espacement minimal entre les cartes

        # Ventes du jour
        self.sales_card = StatCard(
            "Ventes",
            "0",
            "#2a82da"
        )
        stats_layout.addWidget(self.sales_card)

        # Chiffre d'affaires
        self.revenue_card = StatCard(
            "C.A.",  # Abréviation pour économiser de l'espace
            "0 DA",
            "#28a745"
        )
        stats_layout.addWidget(self.revenue_card)

        # Paiements
        self.payments_card = StatCard(
            "Encaissé",
            "0 DA",
            "#ffc107"
        )
        stats_layout.addWidget(self.payments_card)

        # Crédit
        self.credit_card = StatCard(
            "Crédit",
            "0 DA",
            "#dc3545"
        )
        stats_layout.addWidget(self.credit_card)

        main_layout.addLayout(stats_layout)

        # Créer un layout en onglets pour les sections d'information
        self.info_tabs = QTabWidget()
        self.info_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                margin: 0px;
                padding: 0px;
            }
            QTabBar::tab {
                padding: 2px 5px;
                height: 16px;
                font-size: 9px;
            }
        """)

        # Section 1: Méthodes de paiement
        payment_widget = QWidget()
        payment_layout = QVBoxLayout(payment_widget)
        payment_layout.setContentsMargins(2, 2, 2, 2)
        payment_layout.setSpacing(0)

        self.payment_methods_layout = QVBoxLayout()
        self.payment_methods_layout.setContentsMargins(0, 0, 0, 0)
        self.payment_methods_layout.setSpacing(0)
        payment_layout.addLayout(self.payment_methods_layout)
        payment_layout.addStretch()

        self.info_tabs.addTab(payment_widget, "Paiements")

        # Section 2: Meilleures ventes
        sales_widget = QWidget()
        sales_layout = QVBoxLayout(sales_widget)
        sales_layout.setContentsMargins(2, 2, 2, 2)
        sales_layout.setSpacing(0)

        self.top_products_layout = QVBoxLayout()
        self.top_products_layout.setContentsMargins(0, 0, 0, 0)
        self.top_products_layout.setSpacing(0)
        sales_layout.addLayout(self.top_products_layout)
        sales_layout.addStretch()

        self.info_tabs.addTab(sales_widget, "Meilleures ventes")

        # Section 3: Meilleurs clients
        customers_widget = QWidget()
        customers_layout = QVBoxLayout(customers_widget)
        customers_layout.setContentsMargins(2, 2, 2, 2)
        customers_layout.setSpacing(0)

        self.top_customers_layout = QVBoxLayout()
        self.top_customers_layout.setContentsMargins(0, 0, 0, 0)
        self.top_customers_layout.setSpacing(0)
        customers_layout.addLayout(self.top_customers_layout)
        customers_layout.addStretch()

        self.info_tabs.addTab(customers_widget, "Meilleurs clients")

        # Ajouter les onglets au layout principal
        main_layout.addWidget(self.info_tabs)

        # Ajouter un espace extensible en bas
        main_layout.addStretch()

    async def load_data(self, service):
        """Charge les données du tableau de bord"""
        try:
            # Réinitialiser les widgets avec des valeurs par défaut
            self._reset_dashboard_widgets()

            # Mettre à jour la date (toujours afficher la date actuelle)
            self.date_label.setText(datetime.now().strftime("%A %d %B %Y"))

            # Récupérer le résumé des ventes du jour
            try:
                summary = await service.get_daily_sales_summary()
            except Exception as db_error:
                print(f"Erreur lors de la récupération des données de vente: {db_error}")
                # Créer un résumé vide en cas d'erreur
                summary = self._create_empty_summary()

            # Mettre à jour les cartes de statistiques
            self._update_stat_cards(summary)

            # Mettre à jour les sections d'information
            self._update_payment_methods(summary)
            self._update_top_products(summary)
            self._update_top_customers(summary)

        except Exception as e:
            print(f"Erreur lors du chargement des données du tableau de bord: {e}")
            import traceback
            traceback.print_exc()

    def _reset_dashboard_widgets(self):
        """Réinitialise tous les widgets du tableau de bord avec des valeurs par défaut"""
        # Réinitialiser les cartes de statistiques
        self.sales_card.findChild(QLabel, "statCardValue").setText("0")
        self.revenue_card.findChild(QLabel, "statCardValue").setText("0 DA")
        self.payments_card.findChild(QLabel, "statCardValue").setText("0 DA")
        self.credit_card.findChild(QLabel, "statCardValue").setText("0 DA")

        # Effacer les layouts existants
        self._clear_layout(self.payment_methods_layout)
        self._clear_layout(self.top_products_layout)
        self._clear_layout(self.top_customers_layout)

    def _create_empty_summary(self):
        """Crée un résumé vide pour les cas où la base de données est vide"""
        return {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'total_sales': 0,
            'total_amount': 0,
            'total_paid': 0,
            'payment_methods': {},
            'top_products': [],
            'top_customers': []
        }

    def _update_stat_cards(self, summary):
        """Met à jour les cartes de statistiques avec les données du résumé"""
        # Mettre à jour le nombre de ventes
        self.sales_card.findChild(QLabel, "statCardValue").setText(str(summary['total_sales']))

        # Formater les montants pour qu'ils soient plus compacts
        total_amount = summary['total_amount']
        if total_amount >= 1000000:
            self.revenue_card.findChild(QLabel, "statCardValue").setText(f"{total_amount/1000000:.1f}M DA")
        elif total_amount >= 1000:
            self.revenue_card.findChild(QLabel, "statCardValue").setText(f"{total_amount/1000:.1f}K DA")
        else:
            self.revenue_card.findChild(QLabel, "statCardValue").setText(f"{total_amount:.0f} DA")

        # Même chose pour les paiements
        total_paid = summary['total_paid']
        if total_paid >= 1000000:
            self.payments_card.findChild(QLabel, "statCardValue").setText(f"{total_paid/1000000:.1f}M DA")
        elif total_paid >= 1000:
            self.payments_card.findChild(QLabel, "statCardValue").setText(f"{total_paid/1000:.1f}K DA")
        else:
            self.payments_card.findChild(QLabel, "statCardValue").setText(f"{total_paid:.0f} DA")

        # Et pour le crédit
        credit = summary['total_amount'] - summary['total_paid']
        if credit >= 1000000:
            self.credit_card.findChild(QLabel, "statCardValue").setText(f"{credit/1000000:.1f}M DA")
        elif credit >= 1000:
            self.credit_card.findChild(QLabel, "statCardValue").setText(f"{credit/1000:.1f}K DA")
        else:
            self.credit_card.findChild(QLabel, "statCardValue").setText(f"{credit:.0f} DA")

    def _update_payment_methods(self, summary):
        """Met à jour la section des méthodes de paiement"""
        # S'assurer que le layout est vide
        self._clear_layout(self.payment_methods_layout)

        if summary['payment_methods']:
            for method, amount in summary['payment_methods'].items():
                # Créer un widget pour chaque méthode
                method_widget = QWidget()
                method_layout = QHBoxLayout(method_widget)
                method_layout.setContentsMargins(0, 0, 0, 0)
                method_layout.setSpacing(2)

                # Nom de la méthode (abrégé si nécessaire)
                method_name = QLabel(method[:8])  # Limiter à 8 caractères
                method_name.setStyleSheet("font-size: 9px; color: #444;")
                method_layout.addWidget(method_name)

                # Espacement extensible
                method_layout.addStretch()

                # Montant formaté de manière ultra-compacte
                if amount >= 1000000:
                    amount_text = f"{amount/1000000:.1f}M"
                elif amount >= 1000:
                    amount_text = f"{amount/1000:.1f}K"
                else:
                    amount_text = f"{amount:.0f}"

                method_amount = QLabel(f"{amount_text}")
                method_amount.setStyleSheet("font-size: 9px; font-weight: bold;")
                method_amount.setAlignment(Qt.AlignmentFlag.AlignRight)
                method_layout.addWidget(method_amount)

                # Hauteur ultra-compacte
                method_widget.setFixedHeight(14)

                self.payment_methods_layout.addWidget(method_widget)
        else:
            # Message si aucune méthode de paiement
            no_data_label = QLabel("-")
            no_data_label.setStyleSheet("color: #999; font-size: 9px;")
            no_data_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.payment_methods_layout.addWidget(no_data_label)

    def _update_top_products(self, summary):
        """Met à jour la section des meilleures ventes"""
        # S'assurer que le layout est vide
        self._clear_layout(self.top_products_layout)

        if summary['top_products']:
            for product in summary['top_products']:
                # Créer un widget pour chaque produit
                product_widget = QWidget()
                product_layout = QHBoxLayout(product_widget)
                product_layout.setContentsMargins(0, 0, 0, 0)
                product_layout.setSpacing(2)

                # Nom du produit (abrégé si nécessaire)
                name = product['name']
                if len(name) > 8:
                    name = name[:6] + ".."

                product_name = QLabel(name)
                product_name.setStyleSheet("font-size: 9px; color: #444;")
                product_layout.addWidget(product_name)

                # Espacement extensible
                product_layout.addStretch()

                # Quantité et montant formatés de manière ultra-compacte
                amount = product['amount']
                if amount >= 1000000:
                    amount_text = f"{amount/1000000:.1f}M"
                elif amount >= 1000:
                    amount_text = f"{amount/1000:.1f}K"
                else:
                    amount_text = f"{amount:.0f}"

                product_info = QLabel(f"{product['quantity']}u-{amount_text}")
                product_info.setStyleSheet("font-size: 9px; font-weight: bold;")
                product_info.setAlignment(Qt.AlignmentFlag.AlignRight)
                product_layout.addWidget(product_info)

                # Hauteur ultra-compacte
                product_widget.setFixedHeight(14)

                self.top_products_layout.addWidget(product_widget)
        else:
            # Message si aucun produit
            no_data_label = QLabel("-")
            no_data_label.setStyleSheet("color: #999; font-size: 9px;")
            no_data_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.top_products_layout.addWidget(no_data_label)

    def _update_top_customers(self, summary):
        """Met à jour la section des meilleurs clients"""
        # S'assurer que le layout est vide
        self._clear_layout(self.top_customers_layout)

        if summary['top_customers']:
            for customer in summary['top_customers']:
                # Créer un widget pour chaque client
                customer_widget = QWidget()
                customer_layout = QHBoxLayout(customer_widget)
                customer_layout.setContentsMargins(0, 0, 0, 0)
                customer_layout.setSpacing(2)

                # Nom du client (abrégé si nécessaire)
                name = customer['name']
                if len(name) > 8:
                    name = name[:6] + ".."

                customer_name = QLabel(name)
                customer_name.setStyleSheet("font-size: 9px; color: #444;")
                customer_layout.addWidget(customer_name)

                # Espacement extensible
                customer_layout.addStretch()

                # Nombre de ventes et montant formatés de manière ultra-compacte
                amount = customer['amount']
                if amount >= 1000000:
                    amount_text = f"{amount/1000000:.1f}M"
                elif amount >= 1000:
                    amount_text = f"{amount/1000:.1f}K"
                else:
                    amount_text = f"{amount:.0f}"

                customer_info = QLabel(f"{customer['sales']}v-{amount_text}")
                customer_info.setStyleSheet("font-size: 9px; font-weight: bold;")
                customer_info.setAlignment(Qt.AlignmentFlag.AlignRight)
                customer_layout.addWidget(customer_info)

                # Hauteur ultra-compacte
                customer_widget.setFixedHeight(14)

                self.top_customers_layout.addWidget(customer_widget)
        else:
            # Message si aucun client
            no_data_label = QLabel("-")
            no_data_label.setStyleSheet("color: #999; font-size: 9px;")
            no_data_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.top_customers_layout.addWidget(no_data_label)

    def _clear_layout(self, layout):
        """Efface tous les widgets d'un layout"""
        if layout is not None:
            while layout.count():
                item = layout.takeAt(0)
                widget = item.widget()
                if widget is not None:
                    widget.deleteLater()
                else:
                    self._clear_layout(item.layout())
