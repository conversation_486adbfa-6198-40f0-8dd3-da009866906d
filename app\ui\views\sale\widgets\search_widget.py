from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon
import asyncio

from app.core.services.inventory_service import InventoryService
from app.utils.database import SessionLocal

class SearchWidget(QWidget):
    """Widget de recherche de produits"""
    
    # Signal émis lorsqu'un produit est sélectionné
    product_selected = pyqtSignal(int, int)  # product_id, quantity
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Services
        self.db = SessionLocal()
        self.service = InventoryService(self.db)
        
        # Configuration de l'interface
        self.setup_ui()
        self.setup_connections()
        
    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("SearchWidget: Session de base de données fermée")
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Titre
        title = QLabel("Recherche de produits")
        title.setObjectName("searchTitle")
        title.setStyleSheet("""
            #searchTitle {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title)
        
        # Barre de recherche
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher par code-barres, référence ou nom...")
        search_layout.addWidget(self.search_input)
        
        self.search_button = QPushButton()
        self.search_button.setIcon(QIcon("app/ui/resources/icons/search.svg"))
        search_layout.addWidget(self.search_button)
        
        main_layout.addLayout(search_layout)
        
        # Tableau des résultats
        self.results_frame = QFrame()
        self.results_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.results_frame.setFrameShadow(QFrame.Shadow.Raised)
        self.results_frame.setObjectName("resultsFrame")
        self.results_frame.setStyleSheet("""
            #resultsFrame {
                background-color: white;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        results_layout = QVBoxLayout(self.results_frame)
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels([
            "Référence", "Nom", "Prix", "Stock", ""
        ])
        self.results_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.results_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        self.results_table.setColumnWidth(4, 40)  # Largeur de la colonne d'ajout
        results_layout.addWidget(self.results_table)
        
        main_layout.addWidget(self.results_frame)
        
    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.search_button.clicked.connect(self.search_products)
        self.search_input.returnPressed.connect(self.search_products)
        
    def search_products(self):
        """Recherche des produits"""
        query = self.search_input.text().strip()
        if not query:
            return
            
        # Rechercher les produits
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Rechercher par code-barres, référence ou nom
            products = loop.run_until_complete(self.service.search_products(query))
            
            # Afficher les résultats
            self.results_table.setRowCount(0)
            
            for product in products:
                row = self.results_table.rowCount()
                self.results_table.insertRow(row)
                
                # Référence
                self.results_table.setItem(row, 0, QTableWidgetItem(product.reference or ""))
                
                # Nom
                self.results_table.setItem(row, 1, QTableWidgetItem(product.name))
                
                # Prix
                price_item = QTableWidgetItem(f"{product.unit_price:.2f} DA")
                price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.results_table.setItem(row, 2, price_item)
                
                # Stock
                stock_item = QTableWidgetItem(str(product.current_stock))
                stock_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.results_table.setItem(row, 3, stock_item)
                
                # Bouton d'ajout
                add_button = QPushButton()
                add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
                add_button.setFlat(True)
                add_button.clicked.connect(lambda _, p_id=product.id: self.product_selected.emit(p_id, 1))
                self.results_table.setCellWidget(row, 4, add_button)
                
        except Exception as e:
            print(f"Erreur lors de la recherche de produits: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()
