"""
Boîte de dialogue pour créer une sauvegarde.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QTextEdit, QDialogButtonBox, QMessageBox, QCheckBox,
    QFormLayout
)
from PyQt6.QtCore import Qt, QTimer
import asyncio
import os
from datetime import datetime

from app.core.services.settings_service import BackupService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class BackupDialog(QDialog):
    """Boîte de dialogue pour créer une sauvegarde"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.backup_service = BackupService(self.db)

        # Configuration de la fenêtre
        self.setWindowTitle("Créer une sauvegarde")
        self.setMinimumWidth(400)

        # Initialisation de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("BackupDialog: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Formulaire
        form_layout = QFormLayout()

        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("Description de la sauvegarde...")
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow("Description:", self.description_edit)

        # Options
        self.include_attachments_check = QCheckBox("Inclure les pièces jointes")
        self.include_attachments_check.setChecked(True)
        form_layout.addRow("", self.include_attachments_check)

        self.compress_backup_check = QCheckBox("Compresser la sauvegarde")
        self.compress_backup_check.setChecked(True)
        form_layout.addRow("", self.compress_backup_check)

        main_layout.addLayout(form_layout)

        # Boutons
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.create_backup)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)

    def create_backup(self):
        """Crée une sauvegarde"""
        description = self.description_edit.toPlainText().strip()
        include_attachments = self.include_attachments_check.isChecked()
        compress_backup = self.compress_backup_check.isChecked()

        # Utiliser QTimer pour exécuter la méthode dans le thread principal
        QTimer.singleShot(0, lambda: self._create_backup_wrapper(
            description, include_attachments, compress_backup
        ))

    def _create_backup_wrapper(self, description, include_attachments, compress_backup):
        """Wrapper pour exécuter create_backup_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.create_backup_async(
                description, include_attachments, compress_backup
            ))
        finally:
            loop.close()

    async def create_backup_async(self, description, include_attachments, compress_backup):
        """Crée une sauvegarde de manière asynchrone"""
        self.loading_overlay.show()
        try:
            # Mettre à jour les paramètres de sauvegarde
            await self.backup_service.settings_service.set_setting(
                "backup.include_attachments", include_attachments
            )
            await self.backup_service.settings_service.set_setting(
                "backup.compress_backup", compress_backup
            )

            # Fermer la session actuelle
            self.db.close()

            # Créer une nouvelle session
            from app.utils.database import SessionLocal
            new_db = SessionLocal()

            try:
                # Créer un nouveau service avec la nouvelle session
                from app.core.services.settings_service import BackupService
                new_backup_service = BackupService(new_db)

                # Créer la sauvegarde avec le nouveau service
                backup_info = await new_backup_service.create_backup(
                    description=description,
                    user_id=1,  # Utiliser l'ID 1 comme utilisateur par défaut
                    is_auto=False
                )

                if backup_info:
                    # Récupérer les informations de la sauvegarde depuis la base de données
                    # pour éviter les problèmes de DetachedInstanceError
                    from app.core.models.settings import BackupInfo

                    # Récupérer la sauvegarde fraîchement créée
                    fresh_backup = new_db.query(BackupInfo).get(backup_info.id)

                    if fresh_backup:
                        # Afficher un message complet car les notifications sont désactivées
                        QMessageBox.information(
                            self,
                            "Sauvegarde créée",
                            f"La sauvegarde a été créée avec succès.\n\nFichier: {fresh_backup.filename}\nTaille: {self._format_size(fresh_backup.size)}"
                        )
                    else:
                        # Fallback au cas où la sauvegarde n'est pas trouvée
                        QMessageBox.information(
                            self,
                            "Sauvegarde créée",
                            "La sauvegarde a été créée avec succès."
                        )

                    self.accept()
                else:
                    QMessageBox.warning(
                        self,
                        "Échec de la sauvegarde",
                        "La création de la sauvegarde a échoué."
                    )
            finally:
                # Fermer la nouvelle session
                new_db.close()

                # Recréer la session originale
                self.db = SessionLocal()
                self.backup_service = BackupService(self.db)
        except Exception as e:
            print(f"Erreur lors de la création de la sauvegarde: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def _format_size(self, size):
        """Formate la taille en format lisible (KB, MB, etc.)"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.2f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.2f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.2f} GB"
