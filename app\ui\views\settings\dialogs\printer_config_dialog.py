"""
Boîte de dialogue pour la configuration des imprimantes.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
    QPushButton, QTabWidget, QWidget, QFormLayout, QLineEdit,
    QSpinBox, QCheckBox, QMessageBox, QGroupBox, QTextEdit,
    QListWidget, QListWidgetItem, QSplitter, QFileDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QSettings
from PyQt6.QtGui import QIcon
import win32print
import json
import os
from typing import Dict, Any, List, Optional

try:
    from app.utils.escpos_printer import ESCPOSPrinter
except ImportError:
    from app.utils.escpos_printer_simple import ESCPOSPrinter
from app.utils.print_template_manager import PrintTemplateManager, PrintTemplate

class PrinterConfigDialog(QDialog):
    """
    Boîte de dialogue pour la configuration des imprimantes.
    """

    # Signal émis lorsque la configuration est modifiée
    configChanged = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configuration des imprimantes")
        self.setMinimumSize(800, 600)

        # Initialiser les gestionnaires
        self.template_manager = PrintTemplateManager()

        # Charger la configuration
        self.config = self._load_config()

        # Configurer l'interface
        self._setup_ui()

    def _setup_ui(self):
        """Configure l'interface utilisateur."""
        layout = QVBoxLayout(self)

        # Onglets
        tabs = QTabWidget()

        # Onglet des imprimantes
        printers_tab = QWidget()
        tabs.addTab(printers_tab, "Imprimantes")
        self._setup_printers_tab(printers_tab)

        # Onglet des modèles
        templates_tab = QWidget()
        tabs.addTab(templates_tab, "Modèles")
        self._setup_templates_tab(templates_tab)

        # Onglet de test
        test_tab = QWidget()
        tabs.addTab(test_tab, "Test")
        self._setup_test_tab(test_tab)

        layout.addWidget(tabs)

        # Boutons
        button_layout = QHBoxLayout()

        save_button = QPushButton("Enregistrer")
        save_button.clicked.connect(self._save_config)
        button_layout.addWidget(save_button)

        cancel_button = QPushButton("Annuler")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

    def _setup_printers_tab(self, tab):
        """Configure l'onglet des imprimantes."""
        layout = QVBoxLayout(tab)

        # Liste des imprimantes
        printers_group = QGroupBox("Imprimantes disponibles")
        printers_layout = QVBoxLayout(printers_group)

        self.printer_combo = QComboBox()
        self._refresh_printers()
        printers_layout.addWidget(self.printer_combo)

        refresh_button = QPushButton("Actualiser")
        refresh_button.clicked.connect(self._refresh_printers)
        printers_layout.addWidget(refresh_button)

        layout.addWidget(printers_group)

        # Configuration de l'imprimante
        config_group = QGroupBox("Configuration de l'imprimante")
        config_layout = QFormLayout(config_group)

        self.connection_type_combo = QComboBox()
        self.connection_type_combo.addItems(["windows", "usb", "network", "serial", "file"])
        self.connection_type_combo.currentTextChanged.connect(self._on_connection_type_changed)
        config_layout.addRow("Type de connexion:", self.connection_type_combo)

        # Paramètres spécifiques au type de connexion
        self.connection_params_widget = QWidget()
        self.connection_params_layout = QFormLayout(self.connection_params_widget)
        config_layout.addRow(self.connection_params_widget)

        # Paramètres par défaut
        self.default_printer_check = QCheckBox("Définir comme imprimante par défaut")
        config_layout.addRow(self.default_printer_check)

        layout.addWidget(config_group)

        # Charger la configuration actuelle
        self._load_printer_config()

    def _setup_templates_tab(self, tab):
        """Configure l'onglet des modèles."""
        layout = QVBoxLayout(tab)

        splitter = QSplitter(Qt.Orientation.Horizontal)

        # Liste des modèles
        templates_widget = QWidget()
        templates_layout = QVBoxLayout(templates_widget)

        self.templates_list = QListWidget()
        self.templates_list.currentItemChanged.connect(self._on_template_selected)
        templates_layout.addWidget(self.templates_list)

        templates_buttons = QHBoxLayout()

        add_template_button = QPushButton("Ajouter")
        add_template_button.clicked.connect(self._add_template)
        templates_buttons.addWidget(add_template_button)

        remove_template_button = QPushButton("Supprimer")
        remove_template_button.clicked.connect(self._remove_template)
        templates_buttons.addWidget(remove_template_button)

        templates_layout.addLayout(templates_buttons)

        splitter.addWidget(templates_widget)

        # Éditeur de modèle
        editor_widget = QWidget()
        editor_layout = QVBoxLayout(editor_widget)

        form_layout = QFormLayout()

        self.template_name_edit = QLineEdit()
        form_layout.addRow("Nom:", self.template_name_edit)

        self.template_type_combo = QComboBox()
        self.template_type_combo.addItems(["escpos", "pdf", "html"])
        form_layout.addRow("Type:", self.template_type_combo)

        editor_layout.addLayout(form_layout)

        self.template_content_edit = QTextEdit()
        self.template_content_edit.setPlaceholderText("Contenu du modèle")
        editor_layout.addWidget(self.template_content_edit)

        save_template_button = QPushButton("Enregistrer le modèle")
        save_template_button.clicked.connect(self._save_template)
        editor_layout.addWidget(save_template_button)

        splitter.addWidget(editor_widget)

        layout.addWidget(splitter)

        # Charger les modèles
        self._refresh_templates()

    def _setup_test_tab(self, tab):
        """Configure l'onglet de test."""
        layout = QVBoxLayout(tab)

        # Sélection du modèle
        form_layout = QFormLayout()

        self.test_template_combo = QComboBox()
        form_layout.addRow("Modèle:", self.test_template_combo)

        self.test_printer_combo = QComboBox()
        form_layout.addRow("Imprimante:", self.test_printer_combo)

        layout.addLayout(form_layout)

        # Bouton de test
        test_button = QPushButton("Tester l'impression")
        test_button.clicked.connect(self._test_print)
        layout.addWidget(test_button)

        # Rafraîchir les listes
        self._refresh_test_combos()

    def _refresh_printers(self):
        """Rafraîchit la liste des imprimantes."""
        self.printer_combo.clear()

        try:
            for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS):
                self.printer_combo.addItem(printer[2])
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la récupération des imprimantes: {str(e)}")

    def _on_connection_type_changed(self, connection_type):
        """Appelé lorsque le type de connexion change."""
        # Effacer les paramètres actuels
        while self.connection_params_layout.count():
            item = self.connection_params_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Ajouter les paramètres spécifiques au type de connexion
        if connection_type == "usb":
            self.vendor_id_edit = QLineEdit()
            self.vendor_id_edit.setPlaceholderText("0x0416")
            self.connection_params_layout.addRow("Vendor ID:", self.vendor_id_edit)

            self.product_id_edit = QLineEdit()
            self.product_id_edit.setPlaceholderText("0x5011")
            self.connection_params_layout.addRow("Product ID:", self.product_id_edit)

            self.in_ep_edit = QLineEdit()
            self.in_ep_edit.setPlaceholderText("0x81")
            self.connection_params_layout.addRow("In EP:", self.in_ep_edit)

            self.out_ep_edit = QLineEdit()
            self.out_ep_edit.setPlaceholderText("0x03")
            self.connection_params_layout.addRow("Out EP:", self.out_ep_edit)
        elif connection_type == "network":
            self.host_edit = QLineEdit()
            self.host_edit.setPlaceholderText("*************")
            self.connection_params_layout.addRow("Hôte:", self.host_edit)

            self.port_edit = QSpinBox()
            self.port_edit.setRange(1, 65535)
            self.port_edit.setValue(9100)
            self.connection_params_layout.addRow("Port:", self.port_edit)
        elif connection_type == "serial":
            self.devfile_edit = QLineEdit()
            self.devfile_edit.setPlaceholderText("/dev/ttyS0")
            self.connection_params_layout.addRow("Périphérique:", self.devfile_edit)

            self.baudrate_edit = QSpinBox()
            self.baudrate_edit.setRange(1200, 115200)
            self.baudrate_edit.setValue(9600)
            self.connection_params_layout.addRow("Baudrate:", self.baudrate_edit)
        elif connection_type == "file":
            self.device_path_edit = QLineEdit()
            self.device_path_edit.setPlaceholderText("/dev/usb/lp0")
            self.connection_params_layout.addRow("Chemin:", self.device_path_edit)

            browse_button = QPushButton("Parcourir")
            browse_button.clicked.connect(self._browse_device_path)
            self.connection_params_layout.addRow("", browse_button)

    def _browse_device_path(self):
        """Ouvre une boîte de dialogue pour sélectionner un fichier."""
        file_path, _ = QFileDialog.getOpenFileName(self, "Sélectionner un périphérique")
        if file_path:
            self.device_path_edit.setText(file_path)

    def _load_config(self) -> Dict[str, Any]:
        """Charge la configuration des imprimantes."""
        config_path = os.path.join(os.getcwd(), "app", "config", "printers.json")

        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                QMessageBox.warning(self, "Avertissement", f"Erreur lors du chargement de la configuration: {str(e)}")

        # Configuration par défaut
        return {
            "printers": {},
            "default_printer": None
        }

    def _save_config(self):
        """Enregistre la configuration des imprimantes."""
        # Récupérer la configuration de l'imprimante actuelle
        self._save_current_printer_config()

        # Enregistrer la configuration
        config_dir = os.path.join(os.getcwd(), "app", "config")
        os.makedirs(config_dir, exist_ok=True)

        config_path = os.path.join(config_dir, "printers.json")

        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2)

            # Émettre le signal de changement de configuration
            self.configChanged.emit()

            QMessageBox.information(self, "Information", "Configuration enregistrée avec succès.")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement de la configuration: {str(e)}")

    def _load_printer_config(self):
        """Charge la configuration de l'imprimante sélectionnée."""
        printer_name = self.printer_combo.currentText()

        if not printer_name:
            return

        # Vérifier si l'imprimante est déjà configurée
        if printer_name in self.config.get("printers", {}):
            printer_config = self.config["printers"][printer_name]

            # Charger le type de connexion
            connection_type = printer_config.get("connection_type", "windows")
            self.connection_type_combo.setCurrentText(connection_type)

            # Charger les paramètres de connexion
            self._on_connection_type_changed(connection_type)
            connection_params = printer_config.get("connection_params", {})

            if connection_type == "usb":
                if hasattr(self, "vendor_id_edit"):
                    self.vendor_id_edit.setText(str(connection_params.get("vendor_id", "")))
                if hasattr(self, "product_id_edit"):
                    self.product_id_edit.setText(str(connection_params.get("product_id", "")))
                if hasattr(self, "in_ep_edit"):
                    self.in_ep_edit.setText(str(connection_params.get("in_ep", "")))
                if hasattr(self, "out_ep_edit"):
                    self.out_ep_edit.setText(str(connection_params.get("out_ep", "")))
            elif connection_type == "network":
                if hasattr(self, "host_edit"):
                    self.host_edit.setText(connection_params.get("host", ""))
                if hasattr(self, "port_edit"):
                    self.port_edit.setValue(connection_params.get("port", 9100))
            elif connection_type == "serial":
                if hasattr(self, "devfile_edit"):
                    self.devfile_edit.setText(connection_params.get("devfile", ""))
                if hasattr(self, "baudrate_edit"):
                    self.baudrate_edit.setValue(connection_params.get("baudrate", 9600))
            elif connection_type == "file":
                if hasattr(self, "device_path_edit"):
                    self.device_path_edit.setText(connection_params.get("device_path", ""))

            # Charger l'imprimante par défaut
            self.default_printer_check.setChecked(self.config.get("default_printer") == printer_name)
        else:
            # Configuration par défaut
            self.connection_type_combo.setCurrentText("windows")
            self._on_connection_type_changed("windows")
            self.default_printer_check.setChecked(False)

    def _save_current_printer_config(self):
        """Enregistre la configuration de l'imprimante actuelle."""
        printer_name = self.printer_combo.currentText()

        if not printer_name:
            return

        # Créer la configuration de l'imprimante
        connection_type = self.connection_type_combo.currentText()
        connection_params = {}

        if connection_type == "usb":
            if hasattr(self, "vendor_id_edit") and self.vendor_id_edit.text():
                connection_params["vendor_id"] = int(self.vendor_id_edit.text(), 16) if self.vendor_id_edit.text().startswith("0x") else int(self.vendor_id_edit.text())
            if hasattr(self, "product_id_edit") and self.product_id_edit.text():
                connection_params["product_id"] = int(self.product_id_edit.text(), 16) if self.product_id_edit.text().startswith("0x") else int(self.product_id_edit.text())
            if hasattr(self, "in_ep_edit") and self.in_ep_edit.text():
                connection_params["in_ep"] = int(self.in_ep_edit.text(), 16) if self.in_ep_edit.text().startswith("0x") else int(self.in_ep_edit.text())
            if hasattr(self, "out_ep_edit") and self.out_ep_edit.text():
                connection_params["out_ep"] = int(self.out_ep_edit.text(), 16) if self.out_ep_edit.text().startswith("0x") else int(self.out_ep_edit.text())
        elif connection_type == "network":
            if hasattr(self, "host_edit") and self.host_edit.text():
                connection_params["host"] = self.host_edit.text()
            if hasattr(self, "port_edit"):
                connection_params["port"] = self.port_edit.value()
        elif connection_type == "serial":
            if hasattr(self, "devfile_edit") and self.devfile_edit.text():
                connection_params["devfile"] = self.devfile_edit.text()
            if hasattr(self, "baudrate_edit"):
                connection_params["baudrate"] = self.baudrate_edit.value()
        elif connection_type == "file":
            if hasattr(self, "device_path_edit") and self.device_path_edit.text():
                connection_params["device_path"] = self.device_path_edit.text()

        # Enregistrer la configuration
        if "printers" not in self.config:
            self.config["printers"] = {}

        self.config["printers"][printer_name] = {
            "connection_type": connection_type,
            "connection_params": connection_params
        }

        # Définir l'imprimante par défaut
        if self.default_printer_check.isChecked():
            self.config["default_printer"] = printer_name
        elif self.config.get("default_printer") == printer_name:
            self.config["default_printer"] = None

    def _refresh_templates(self):
        """Rafraîchit la liste des modèles."""
        self.templates_list.clear()

        for template_name, template in self.template_manager.templates.items():
            item = QListWidgetItem(template_name)
            item.setData(Qt.ItemDataRole.UserRole, template_name)
            self.templates_list.addItem(item)

    def _on_template_selected(self, current, previous):
        """Appelé lorsqu'un modèle est sélectionné."""
        if not current:
            return

        template_name = current.data(Qt.ItemDataRole.UserRole)
        template = self.template_manager.get_template(template_name)

        if template:
            self.template_name_edit.setText(template.name)
            self.template_type_combo.setCurrentText(template.template_type)
            self.template_content_edit.setText(template.content)

    def _add_template(self):
        """Ajoute un nouveau modèle."""
        # Créer un nouveau modèle
        template = PrintTemplate(
            name="nouveau_modele",
            template_type="escpos",
            content="# Nouveau modèle\n\n# Utiliser {{ variable }} pour les variables\n\n# Initialiser l'imprimante\nESC @\n\n# Centrer le texte\nESC a 1\n\n# Texte normal\nNouveau modèle\n\n# Couper le papier\nGS V A"
        )

        # Enregistrer le modèle
        self.template_manager.save_template(template)

        # Rafraîchir la liste
        self._refresh_templates()

        # Sélectionner le nouveau modèle
        for i in range(self.templates_list.count()):
            item = self.templates_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == template.name:
                self.templates_list.setCurrentItem(item)
                break

    def _remove_template(self):
        """Supprime le modèle sélectionné."""
        current_item = self.templates_list.currentItem()

        if not current_item:
            return

        template_name = current_item.data(Qt.ItemDataRole.UserRole)

        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            f"Voulez-vous vraiment supprimer le modèle '{template_name}' ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Supprimer le modèle
            self.template_manager.delete_template(template_name)

            # Rafraîchir la liste
            self._refresh_templates()

    def _save_template(self):
        """Enregistre le modèle actuel."""
        # Récupérer les données du modèle
        name = self.template_name_edit.text()
        template_type = self.template_type_combo.currentText()
        content = self.template_content_edit.toPlainText()

        if not name:
            QMessageBox.warning(self, "Avertissement", "Veuillez saisir un nom pour le modèle.")
            return

        # Créer le modèle
        template = PrintTemplate(
            name=name,
            template_type=template_type,
            content=content
        )

        # Enregistrer le modèle
        self.template_manager.save_template(template)

        # Rafraîchir la liste
        self._refresh_templates()

        QMessageBox.information(self, "Information", f"Modèle '{name}' enregistré avec succès.")

    def _refresh_test_combos(self):
        """Rafraîchit les listes de l'onglet de test."""
        # Rafraîchir la liste des modèles
        self.test_template_combo.clear()

        for template_name in self.template_manager.templates.keys():
            self.test_template_combo.addItem(template_name)

        # Rafraîchir la liste des imprimantes
        self.test_printer_combo.clear()

        for printer_name in self.config.get("printers", {}).keys():
            self.test_printer_combo.addItem(printer_name)

    def _test_print(self):
        """Teste l'impression avec le modèle et l'imprimante sélectionnés."""
        template_name = self.test_template_combo.currentText()
        printer_name = self.test_printer_combo.currentText()

        if not template_name:
            QMessageBox.warning(self, "Avertissement", "Veuillez sélectionner un modèle.")
            return

        if not printer_name:
            QMessageBox.warning(self, "Avertissement", "Veuillez sélectionner une imprimante.")
            return

        # Récupérer la configuration de l'imprimante
        printer_config = self.config.get("printers", {}).get(printer_name, {})

        # Créer l'imprimante
        printer = ESCPOSPrinter(
            printer_name=printer_name,
            connection_type=printer_config.get("connection_type", "windows"),
            connection_params=printer_config.get("connection_params", {})
        )

        # Données de test
        test_data = {
            "repair": {
                "number": "12345",
                "created_at": "2023-01-01",
                "issue": "Écran cassé",
                "expected_completion_date": "2023-01-10"
            },
            "customer": {
                "name": "Client Test",
                "phone": "0123456789",
                "address": "123 Rue Test"
            },
            "device": {
                "brand": "Marque Test",
                "model": "Modèle Test",
                "imei": "123456789012345"
            },
            "invoice": {
                "number": "INV-12345",
                "date": "2023-01-01",
                "subtotal": "100.00",
                "discount": "10.00",
                "tax": "20.00",
                "total": "110.00",
                "payment_method": "Espèces"
            },
            "items": [
                {
                    "description": "Réparation écran",
                    "quantity": 1,
                    "unit_price": "100.00",
                    "total": "100.00"
                }
            ],
            "payment": {
                "number": "PAY-12345",
                "date": "2023-01-01",
                "amount": "110.00",
                "method": "Espèces"
            }
        }

        # Imprimer le test
        success = printer.print_from_template(template_name, test_data)

        if success:
            QMessageBox.information(self, "Information", "Test d'impression réussi.")
        else:
            QMessageBox.warning(self, "Avertissement", "Erreur lors du test d'impression.")

    @staticmethod
    def get_printer_config() -> Dict[str, Any]:
        """
        Récupère la configuration des imprimantes.

        Returns:
            Configuration des imprimantes
        """
        config_path = os.path.join(os.getcwd(), "app", "config", "printers.json")

        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                pass

        # Configuration par défaut
        return {
            "printers": {},
            "default_printer": None
        }

    @staticmethod
    def get_default_printer() -> Optional[Dict[str, Any]]:
        """
        Récupère la configuration de l'imprimante par défaut.

        Returns:
            Configuration de l'imprimante par défaut, ou None si aucune imprimante par défaut n'est définie
        """
        config = PrinterConfigDialog.get_printer_config()

        default_printer_name = config.get("default_printer")

        if default_printer_name and default_printer_name in config.get("printers", {}):
            printer_config = config["printers"][default_printer_name]
            printer_config["name"] = default_printer_name
            return printer_config

        return None
