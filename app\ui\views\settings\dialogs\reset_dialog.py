"""
Boîte de dialogue pour réinitialiser la base de données.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QDialogButtonBox, QMessageBox, QCheckBox,
    QFormLayout
)
from PyQt6.QtCore import Qt, QTimer
import asyncio
import os
from datetime import datetime

from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class ResetDialog(QDialog):
    """Boîte de dialogue pour réinitialiser la base de données"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Configuration de la fenêtre
        self.setWindowTitle("Réinitialiser la base de données")
        self.setMinimumWidth(500)

        # Initialisation de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Avertissement
        warning_label = QLabel(
            "ATTENTION: La réinitialisation de la base de données supprimera TOUTES les données. "
            "Cette action est IRRÉVERSIBLE. Assurez-vous de créer une sauvegarde de vos données avant de continuer."
        )
        warning_label.setWordWrap(True)
        warning_label.setStyleSheet("color: red; font-weight: bold;")
        main_layout.addWidget(warning_label)

        # Confirmation
        confirmation_layout = QFormLayout()

        self.confirmation_edit = QLineEdit()
        self.confirmation_edit.setPlaceholderText("Tapez 'RÉINITIALISER' pour confirmer")
        confirmation_layout.addRow("Confirmation:", self.confirmation_edit)

        main_layout.addLayout(confirmation_layout)

        # Options
        self.create_backup_check = QCheckBox("Créer une sauvegarde avant la réinitialisation")
        self.create_backup_check.setChecked(True)
        main_layout.addWidget(self.create_backup_check)

        self.preserve_users_check = QCheckBox("Préserver les utilisateurs, rôles et permissions")
        self.preserve_users_check.setChecked(True)
        self.preserve_users_check.setToolTip("Si cette option est cochée, les utilisateurs, rôles et permissions seront préservés lors de la réinitialisation")
        main_layout.addWidget(self.preserve_users_check)

        # Boutons
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.button(QDialogButtonBox.StandardButton.Ok).setText("Réinitialiser")
        self.button_box.button(QDialogButtonBox.StandardButton.Ok).setStyleSheet("background-color: red; color: #FFFFFF; font-weight: bold; border: 1px solid #c0392b;")
        self.button_box.accepted.connect(self.reset_database)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)

    def reset_database(self):
        """Réinitialise la base de données"""
        # Vérifier la confirmation
        confirmation = self.confirmation_edit.text()
        if confirmation != "réinitialiser":
            QMessageBox.warning(
                self,
                "Confirmation incorrecte",
                "Veuillez taper 'réinitialiser' pour confirmer la réinitialisation."
            )
            return

        # Demander confirmation
        preserve_users = self.preserve_users_check.isChecked()
        message = "Êtes-vous ABSOLUMENT sûr de vouloir réinitialiser la base de données ?\n\n"

        if preserve_users:
            message += "Les utilisateurs, rôles et permissions seront préservés, mais toutes les autres données seront perdues définitivement."
        else:
            message += "TOUTES les données, y compris les utilisateurs, seront perdues définitivement."

        result = QMessageBox.warning(
            self,
            "Confirmation",
            message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if result != QMessageBox.StandardButton.Yes:
            return

        # Utiliser QTimer pour exécuter la méthode dans le thread principal
        create_backup = self.create_backup_check.isChecked()
        preserve_users = self.preserve_users_check.isChecked()
        QTimer.singleShot(0, lambda: self._reset_database_wrapper(create_backup, preserve_users))

    def _reset_database_wrapper(self, create_backup, preserve_users):
        """Wrapper pour exécuter reset_database_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.reset_database_async(create_backup, preserve_users))
        finally:
            loop.close()

    async def reset_database_async(self, create_backup, preserve_users):
        """Réinitialise la base de données de manière asynchrone"""
        self.loading_overlay.show()
        try:
            # Créer une sauvegarde si demandé
            if create_backup:
                from app.core.services.settings_service import BackupService
                db = SessionLocal()
                backup_service = BackupService(db)

                try:
                    await backup_service.create_backup(
                        description="Sauvegarde automatique avant réinitialisation",
                        user_id=None,  # TODO: Récupérer l'ID de l'utilisateur connecté
                        is_auto=True
                    )
                except Exception as e:
                    response = QMessageBox.warning(
                        self,
                        "Échec de la sauvegarde",
                        f"La création de la sauvegarde a échoué: {str(e)}\n\nVoulez-vous continuer la réinitialisation sans sauvegarde ?",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.No
                    )
                    if response != QMessageBox.StandardButton.Yes:
                        return
                finally:
                    db.close()

            # Réinitialiser la base de données
            from app.utils.database_reset import reset_database
            success = await reset_database(preserve_users=preserve_users)

            if success:
                success_message = "La base de données a été réinitialisée avec succès."
                if preserve_users:
                    success_message += " Les utilisateurs, rôles et permissions ont été préservés."
                success_message += " L'application va maintenant redémarrer."

                QMessageBox.information(
                    self,
                    "Réinitialisation réussie",
                    success_message
                )
                self.accept()

                # Redémarrer l'application
                import sys
                import os
                os.execl(sys.executable, sys.executable, *sys.argv)
            else:
                QMessageBox.warning(
                    self,
                    "Échec de la réinitialisation",
                    "La réinitialisation de la base de données a échoué."
                )
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()
