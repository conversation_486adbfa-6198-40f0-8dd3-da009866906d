"""
Boîte de dialogue pour restaurer une sauvegarde.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QDialogButtonBox, QMessageBox, QCheckBox,
    QFormLayout, QGroupBox
)
from PyQt6.QtCore import Qt, QTimer
import asyncio
import os
import sys
from datetime import datetime
from app.utils.database_reset import register_existing_backups

from app.core.services.settings_service import BackupService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class RestoreDialog(QDialog):
    """Boîte de dialogue pour restaurer une sauvegarde"""

    def __init__(self, parent=None, backup_id=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.backup_service = BackupService(self.db)

        # Données
        self.backup_id = backup_id
        self.backup_info = None

        # Configuration de la fenêtre
        self.setWindowTitle("Restaurer une sauvegarde")
        self.setMinimumWidth(500)

        # Initialisation de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les informations de la sauvegarde
        if backup_id:
            self._load_backup_info_wrapper()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("RestoreDialog: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Informations de la sauvegarde
        info_group = QGroupBox("Informations de la sauvegarde")
        info_layout = QFormLayout(info_group)

        self.filename_label = QLabel()
        info_layout.addRow("Fichier:", self.filename_label)

        self.size_label = QLabel()
        info_layout.addRow("Taille:", self.size_label)

        self.date_label = QLabel()
        info_layout.addRow("Date de création:", self.date_label)

        self.description_label = QLabel()
        self.description_label.setWordWrap(True)
        info_layout.addRow("Description:", self.description_label)

        self.version_label = QLabel()
        info_layout.addRow("Version:", self.version_label)

        main_layout.addWidget(info_group)

        # Avertissement
        warning_label = QLabel(
            "Attention: La restauration d'une sauvegarde remplacera toutes les données actuelles. "
            "Cette action est irréversible. Assurez-vous de créer une sauvegarde de vos données actuelles avant de continuer."
        )
        warning_label.setWordWrap(True)
        warning_label.setStyleSheet("color: red;")
        main_layout.addWidget(warning_label)

        # Options
        options_group = QGroupBox("Options de restauration")
        options_layout = QVBoxLayout(options_group)

        self.restore_attachments_check = QCheckBox("Restaurer les pièces jointes")
        self.restore_attachments_check.setChecked(True)
        options_layout.addWidget(self.restore_attachments_check)

        main_layout.addWidget(options_group)

        # Boutons
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.button(QDialogButtonBox.StandardButton.Ok).setText("Restaurer")
        self.button_box.accepted.connect(self.restore_backup)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)

    def _load_backup_info_wrapper(self):
        """Wrapper pour exécuter load_backup_info de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_backup_info())
        except Exception as e:
            print(f"Erreur lors du chargement des informations de la sauvegarde: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def load_backup_info(self):
        """Charge les informations de la sauvegarde"""
        self.loading_overlay.show()
        try:
            # Récupérer la sauvegarde par ID
            self.backup_info = await self.backup_service.get(self.backup_id)

            if not self.backup_info:
                # Essayer de trouver la sauvegarde dans la base de données par d'autres moyens
                try:
                    # Récupérer toutes les sauvegardes
                    all_backups = await self.backup_service.get_all()

                    # Vérifier si l'ID est valide
                    if not isinstance(self.backup_id, int) or self.backup_id <= 0:
                        QMessageBox.critical(
                            self,
                            "Erreur",
                            f"ID de sauvegarde invalide: {self.backup_id}"
                        )
                        self.reject()
                        return

                    # Vérifier si l'ID est dans la plage des sauvegardes disponibles
                    if all_backups and len(all_backups) > 0:
                        # Afficher un message avec les IDs disponibles
                        available_ids = [b.id for b in all_backups]
                        QMessageBox.critical(
                            self,
                            "Erreur",
                            f"Sauvegarde avec ID {self.backup_id} non trouvée.\n\n"
                            f"IDs disponibles: {available_ids}"
                        )
                    else:
                        QMessageBox.critical(
                            self,
                            "Erreur",
                            f"Sauvegarde avec ID {self.backup_id} non trouvée.\n\n"
                            "Aucune sauvegarde n'est disponible dans la base de données."
                        )
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "Erreur",
                        f"Sauvegarde avec ID {self.backup_id} non trouvée.\n\n"
                        f"Erreur lors de la recherche d'alternatives: {str(e)}"
                    )

                self.reject()
                return

            # Vérifier que le fichier de sauvegarde existe
            if not os.path.exists(self.backup_info.path):
                QMessageBox.critical(
                    self,
                    "Erreur",
                    f"Le fichier de sauvegarde '{self.backup_info.path}' n'existe pas."
                )
                self.reject()
                return

            # Mettre à jour l'interface
            self.filename_label.setText(self.backup_info.filename)
            self.size_label.setText(self._format_size(self.backup_info.size))
            self.date_label.setText(self.backup_info.created_at.strftime("%d/%m/%Y %H:%M"))
            self.description_label.setText(self.backup_info.description or "")
            self.version_label.setText(self.backup_info.version or "")

            # Vérifier si la sauvegarde contient des pièces jointes
            if self.backup_info.backup_metadata and "include_attachments" in self.backup_info.backup_metadata:
                self.restore_attachments_check.setEnabled(self.backup_info.backup_metadata["include_attachments"])
                self.restore_attachments_check.setChecked(self.backup_info.backup_metadata["include_attachments"])
            else:
                self.restore_attachments_check.setEnabled(False)
                self.restore_attachments_check.setChecked(False)
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Une erreur est survenue lors du chargement des informations de la sauvegarde: {str(e)}"
            )
            import traceback
            traceback.print_exc()
            self.reject()
        finally:
            self.loading_overlay.hide()

    def restore_backup(self):
        """Restaure la sauvegarde"""
        # Demander confirmation
        result = QMessageBox.warning(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir restaurer cette sauvegarde ? Toutes les données actuelles seront remplacées.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if result != QMessageBox.StandardButton.Yes:
            return

        # Utiliser QTimer pour exécuter la méthode dans le thread principal
        QTimer.singleShot(0, self._restore_backup_wrapper)

    def _restore_backup_wrapper(self):
        """Wrapper pour exécuter restore_backup_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.restore_backup_async())
        finally:
            loop.close()

    async def restore_backup_async(self):
        """Restaure la sauvegarde de manière asynchrone"""
        self.loading_overlay.show()
        try:
            # Restaurer la sauvegarde
            success = await self.backup_service.restore_backup(
                self.backup_id,
                user_id=None  # TODO: Récupérer l'ID de l'utilisateur connecté
            )

            if success:
                # Enregistrer les sauvegardes existantes
                from app.utils.database import engine
                db_path = engine.url.database
                if db_path.startswith('/'):
                    db_path = db_path[1:]  # Supprimer le premier '/' pour les chemins absolus

                # Enregistrer les sauvegardes existantes
                register_existing_backups(db_path)

                QMessageBox.information(
                    self,
                    "Restauration réussie",
                    "La sauvegarde a été restaurée avec succès. L'application va maintenant redémarrer."
                )
                self.accept()

                # Redémarrer l'application
                os.execl(sys.executable, sys.executable, *sys.argv)
            else:
                QMessageBox.warning(
                    self,
                    "Échec de la restauration",
                    "La restauration de la sauvegarde a échoué."
                )
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def _format_size(self, size):
        """Formate la taille en format lisible (KB, MB, etc.)"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.2f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.2f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.2f} GB"
