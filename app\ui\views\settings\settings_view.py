"""
Vue principale du module de paramètres.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QPushButton, QTableView, QComboBox, QDateEdit, QLineEdit,
    QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox, QTextEdit,
    QMessageBox, QDialog, QDialogButtonBox, QFileDialog, QFrame,
    QSplitter, QStackedWidget, QGridLayout, QScrollArea, QCheckBox,
    QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, QDate, QTimer, pyqtSignal, pyqtSlot, QSortFilterProxyModel
from PyQt6.QtGui import QIcon, QFont
import asyncio
import os
from datetime import datetime, timedelta

from app.core.services.settings_service import SettingsService, BackupService
from app.core.models.settings import SettingCategory, BackupFrequency, BackupInfo
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from app.ui.theme.theme_manager import ThemeManager
from .dialogs.backup_dialog import BackupDialog
from .dialogs.restore_dialog import RestoreDialog
from .dialogs.reset_dialog import ResetDialog
from .dialogs.printer_config_dialog import PrinterConfigDialog
from .widgets.backup_table_model import BackupTableModel

class SettingsView(QWidget):
    """Vue principale du module de paramètres"""

    # Signal émis lorsque le thème est modifié
    themeChanged = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.settings_service = SettingsService(self.db)
        self.backup_service = BackupService(self.db)
        self.theme_manager = ThemeManager()

        # Configuration de l'interface
        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les paramètres
        self.load_settings()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("SettingsView: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Marges et espacement global
        main_layout.setContentsMargins(12, 12, 12, 12)
        main_layout.setSpacing(12)

        # Onglets
        self.tab_widget = QTabWidget()

        # Style pour éviter les titres QGroupBox tronqués et améliorer l'espacement
        self.setStyleSheet(
            """
            QGroupBox {
                margin-top: 16px;            /* espace au-dessus du cadre */
                padding-top: 8px;            /* espace interne en haut */
                font-weight: 600;
            }
            QGroupBox::title {
                subcontrol-origin: margin;    /* position du titre relative aux marges */
                left: 10px;                   /* décalage horizontal du titre */
                padding: 0 6px;               /* espace autour du texte du titre */
                background: transparent;
            }
            """
        )

        # Onglet Général
        self.general_tab = self._create_general_tab()
        self.tab_widget.addTab(self.general_tab, "Général")

        # Onglet Apparence
        self.appearance_tab = self._create_appearance_tab()
        self.tab_widget.addTab(self.appearance_tab, "Apparence")

        # Onglet Base de données
        self.database_tab = self._create_database_tab()
        self.tab_widget.addTab(self.database_tab, "Base de données")

        # Onglet Sauvegardes
        self.backup_tab = self._create_backup_tab()
        self.tab_widget.addTab(self.backup_tab, "Sauvegardes")

        # Onglet Sécurité
        self.security_tab = self._create_security_tab()
        self.tab_widget.addTab(self.security_tab, "Sécurité")

        # Onglet Notifications
        self.notifications_tab = self._create_notifications_tab()
        self.tab_widget.addTab(self.notifications_tab, "Notifications")

        # Onglet Impression
        self.printing_tab = self._create_printing_tab()
        self.tab_widget.addTab(self.printing_tab, "Impression")

        # Onglet Avancé
        self.advanced_tab = self._create_advanced_tab()
        self.tab_widget.addTab(self.advanced_tab, "Avancé")

        main_layout.addWidget(self.tab_widget)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.save_button = QPushButton("Enregistrer")
        self.save_button.setIcon(QIcon("app/ui/resources/icons/save.svg"))
        buttons_layout.addWidget(self.save_button)

        self.reset_button = QPushButton("Réinitialiser")
        self.reset_button.setIcon(QIcon("app/ui/resources/icons/reset.svg"))
        buttons_layout.addWidget(self.reset_button)

        buttons_layout.addStretch()

        main_layout.addLayout(buttons_layout)

    def _create_general_tab(self):
        """Crée l'onglet Général"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(10)

        # Informations de l'entreprise
        company_group = QGroupBox("Informations de l'entreprise")
        company_layout = QFormLayout(company_group)

        self.company_name_edit = QLineEdit()
        company_layout.addRow("Nom de l'entreprise:", self.company_name_edit)

        self.company_address_edit = QLineEdit()
        company_layout.addRow("Adresse:", self.company_address_edit)

        self.company_phone_edit = QLineEdit()
        company_layout.addRow("Téléphone:", self.company_phone_edit)

        self.company_email_edit = QLineEdit()
        company_layout.addRow("Email:", self.company_email_edit)

        self.company_website_edit = QLineEdit()
        company_layout.addRow("Site web:", self.company_website_edit)

        self.company_logo_edit = QLineEdit()
        self.company_logo_edit.setReadOnly(True)
        logo_layout = QHBoxLayout()
        logo_layout.addWidget(self.company_logo_edit)
        self.browse_logo_button = QPushButton("Parcourir...")
        self.browse_logo_button.clicked.connect(self.browse_logo)
        logo_layout.addWidget(self.browse_logo_button)
        company_layout.addRow("Logo:", logo_layout)

        layout.addRow(company_group)

        # Paramètres financiers
        finance_group = QGroupBox("Paramètres financiers")
        finance_layout = QFormLayout(finance_group)

        self.default_currency_edit = QLineEdit()
        finance_layout.addRow("Devise par défaut:", self.default_currency_edit)

        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setRange(0, 100)
        self.tax_rate_spin.setSuffix(" %")
        finance_layout.addRow("Taux de TVA:", self.tax_rate_spin)

        self.fiscal_year_start_edit = QLineEdit()
        self.fiscal_year_start_edit.setPlaceholderText("MM-DD")
        finance_layout.addRow("Début de l'année fiscale:", self.fiscal_year_start_edit)

        layout.addRow(finance_group)

        return tab

    def _create_appearance_tab(self):
        """Crée l'onglet Apparence"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(10)

        # Thème
        self.theme_combo = QComboBox()
        self.theme_combo.addItem("Clair", "light")
        self.theme_combo.addItem("Sombre", "dark")
        layout.addRow("Thème:", self.theme_combo)

        # Taille de police
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        layout.addRow("Taille de police:", self.font_size_spin)

        # Langue
        self.language_combo = QComboBox()
        self.language_combo.addItem("Français", "fr")
        self.language_combo.addItem("Anglais", "en")
        self.language_combo.addItem("Arabe", "ar")
        layout.addRow("Langue:", self.language_combo)

        # Options d'affichage
        self.show_welcome_check = QCheckBox("Afficher l'écran de bienvenue")
        layout.addRow("", self.show_welcome_check)

        self.compact_mode_check = QCheckBox("Mode compact")
        layout.addRow("", self.compact_mode_check)

        self.enable_animations_check = QCheckBox("Activer les animations")
        layout.addRow("", self.enable_animations_check)

        # Aperçu du thème
        preview_group = QGroupBox("Aperçu")
        preview_layout = QVBoxLayout(preview_group)

        self.theme_preview = QLabel("Ceci est un aperçu du thème sélectionné.")
        preview_layout.addWidget(self.theme_preview)

        layout.addRow(preview_group)

        return tab

    def _create_database_tab(self):
        """Crée l'onglet Base de données"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(10)

        # Informations sur la base de données
        info_group = QGroupBox("Informations")
        info_layout = QFormLayout(info_group)

        self.db_path_label = QLabel()
        info_layout.addRow("Chemin:", self.db_path_label)

        self.db_size_label = QLabel()
        info_layout.addRow("Taille:", self.db_size_label)

        layout.addWidget(info_group)

        # Optimisation
        optimize_group = QGroupBox("Optimisation")
        optimize_layout = QVBoxLayout(optimize_group)

        self.auto_optimize_check = QCheckBox("Optimisation automatique")
        optimize_layout.addWidget(self.auto_optimize_check)

        optimize_freq_layout = QHBoxLayout()
        optimize_freq_layout.addWidget(QLabel("Fréquence:"))
        self.optimize_frequency_combo = QComboBox()
        self.optimize_frequency_combo.addItem("Quotidienne", BackupFrequency.DAILY)
        self.optimize_frequency_combo.addItem("Hebdomadaire", BackupFrequency.WEEKLY)
        self.optimize_frequency_combo.addItem("Mensuelle", BackupFrequency.MONTHLY)
        optimize_freq_layout.addWidget(self.optimize_frequency_combo)
        optimize_layout.addLayout(optimize_freq_layout)

        self.optimize_button = QPushButton("Optimiser maintenant")
        self.optimize_button.clicked.connect(self.optimize_database)
        optimize_layout.addWidget(self.optimize_button)

        layout.addWidget(optimize_group)

        # Réinitialisation
        reset_group = QGroupBox("Réinitialisation")
        reset_layout = QVBoxLayout(reset_group)

        reset_warning = QLabel("Attention: La réinitialisation de la base de données supprimera toutes les données. Cette action est irréversible.")
        reset_warning.setWordWrap(True)
        reset_warning.setStyleSheet("color: red;")
        reset_layout.addWidget(reset_warning)

        self.reset_db_button = QPushButton("Réinitialiser la base de données")
        self.reset_db_button.setIcon(QIcon("app/ui/resources/icons/warning.svg"))
        self.reset_db_button.clicked.connect(self.reset_database)
        reset_layout.addWidget(self.reset_db_button)

        layout.addWidget(reset_group)

        layout.addStretch()

        return tab

    def _create_backup_tab(self):
        """Crée l'onglet Sauvegardes"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(10)

        # Paramètres de sauvegarde
        settings_group = QGroupBox("Paramètres de sauvegarde")
        settings_layout = QFormLayout(settings_group)

        self.auto_backup_check = QCheckBox()
        settings_layout.addRow("Sauvegarde automatique:", self.auto_backup_check)

        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItem("Quotidienne", BackupFrequency.DAILY)
        self.backup_frequency_combo.addItem("Hebdomadaire", BackupFrequency.WEEKLY)
        self.backup_frequency_combo.addItem("Mensuelle", BackupFrequency.MONTHLY)
        settings_layout.addRow("Fréquence:", self.backup_frequency_combo)

        backup_path_layout = QHBoxLayout()
        self.backup_path_edit = QLineEdit()
        backup_path_layout.addWidget(self.backup_path_edit)
        self.browse_backup_path_button = QPushButton("Parcourir...")
        self.browse_backup_path_button.clicked.connect(self.browse_backup_path)
        backup_path_layout.addWidget(self.browse_backup_path_button)
        settings_layout.addRow("Chemin de sauvegarde:", backup_path_layout)

        self.max_backups_spin = QSpinBox()
        self.max_backups_spin.setRange(1, 100)
        settings_layout.addRow("Nombre maximum de sauvegardes:", self.max_backups_spin)

        self.include_attachments_check = QCheckBox()
        settings_layout.addRow("Inclure les pièces jointes:", self.include_attachments_check)

        self.compress_backup_check = QCheckBox()
        settings_layout.addRow("Compresser la sauvegarde:", self.compress_backup_check)

        layout.addWidget(settings_group)

        # Liste des sauvegardes
        backups_group = QGroupBox("Sauvegardes existantes")
        backups_layout = QVBoxLayout(backups_group)

        self.backups_table = QTableView()
        self.backups_model = BackupTableModel()
        self.backups_table.setModel(self.backups_model)

        # Configuration du tableau pour le focus
        self.backups_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.backups_table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.backups_table.setAlternatingRowColors(True)
        self.backups_table.setShowGrid(True)
        self.backups_table.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        # Ajuster les colonnes pour qu'elles s'adaptent au contenu
        self.backups_table.horizontalHeader().setStretchLastSection(True)
        from PyQt6.QtWidgets import QHeaderView
        self.backups_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

        # Connecter le signal de sélection
        self.backups_table.selectionModel().selectionChanged.connect(self.on_backup_selection_changed)

        backups_layout.addWidget(self.backups_table)

        buttons_layout = QHBoxLayout()

        self.create_backup_button = QPushButton("Créer une sauvegarde")
        self.create_backup_button.setIcon(QIcon("app/ui/resources/icons/backup.svg"))
        self.create_backup_button.clicked.connect(self.create_backup)
        buttons_layout.addWidget(self.create_backup_button)

        self.restore_backup_button = QPushButton("Restaurer")
        self.restore_backup_button.setIcon(QIcon("app/ui/resources/icons/restore.svg"))
        self.restore_backup_button.clicked.connect(self.restore_backup)
        self.restore_backup_button.setEnabled(False)  # Désactivé par défaut
        buttons_layout.addWidget(self.restore_backup_button)

        self.delete_backup_button = QPushButton("Supprimer")
        self.delete_backup_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
        self.delete_backup_button.clicked.connect(self.delete_backup)
        self.delete_backup_button.setEnabled(False)  # Désactivé par défaut
        buttons_layout.addWidget(self.delete_backup_button)

        backups_layout.addLayout(buttons_layout)

        layout.addWidget(backups_group)

        return tab

    def _create_security_tab(self):
        """Crée l'onglet Sécurité"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Paramètres de sécurité
        security_group = QGroupBox("Paramètres de sécurité")
        security_layout = QFormLayout(security_group)

        self.token_expiration_spin = QSpinBox()
        self.token_expiration_spin.setRange(1, 24)
        self.token_expiration_spin.setSuffix(" heures")
        security_layout.addRow("Expiration des tokens:", self.token_expiration_spin)

        self.encryption_key_edit = QLineEdit()
        self.encryption_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        security_layout.addRow("Clé de chiffrement:", self.encryption_key_edit)

        layout.addWidget(security_group)

        # Paramètres d'authentification
        auth_group = QGroupBox("Authentification")
        auth_layout = QFormLayout(auth_group)

        self.min_password_length_spin = QSpinBox()
        self.min_password_length_spin.setRange(6, 16)
        auth_layout.addRow("Longueur minimale du mot de passe:", self.min_password_length_spin)

        self.require_special_chars_check = QCheckBox()
        auth_layout.addRow("Exiger des caractères spéciaux:", self.require_special_chars_check)

        self.require_numbers_check = QCheckBox()
        auth_layout.addRow("Exiger des chiffres:", self.require_numbers_check)

        self.require_uppercase_check = QCheckBox()
        auth_layout.addRow("Exiger des majuscules:", self.require_uppercase_check)

        self.max_login_attempts_spin = QSpinBox()
        self.max_login_attempts_spin.setRange(3, 10)
        auth_layout.addRow("Nombre maximum de tentatives de connexion:", self.max_login_attempts_spin)

        layout.addWidget(auth_group)

        layout.addStretch()

        return tab

    def _create_notifications_tab(self):
        """Crée l'onglet Notifications"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Paramètres de notifications
        notifications_group = QGroupBox("Paramètres de notifications")
        notifications_layout = QFormLayout(notifications_group)

        self.notification_cleanup_interval_spin = QSpinBox()
        self.notification_cleanup_interval_spin.setRange(1, 168)
        self.notification_cleanup_interval_spin.setSuffix(" heures")
        notifications_layout.addRow("Intervalle de nettoyage:", self.notification_cleanup_interval_spin)

        self.notification_expiry_days_spin = QSpinBox()
        self.notification_expiry_days_spin.setRange(1, 365)
        self.notification_expiry_days_spin.setSuffix(" jours")
        notifications_layout.addRow("Expiration par défaut:", self.notification_expiry_days_spin)

        layout.addWidget(notifications_group)

        # Types de notifications
        types_group = QGroupBox("Types de notifications")
        types_layout = QVBoxLayout(types_group)

        self.notify_inventory_check = QCheckBox("Notifications d'inventaire")
        types_layout.addWidget(self.notify_inventory_check)

        self.notify_repair_check = QCheckBox("Notifications de réparation")
        types_layout.addWidget(self.notify_repair_check)

        self.notify_customer_check = QCheckBox("Notifications de client")
        types_layout.addWidget(self.notify_customer_check)

        self.notify_supplier_check = QCheckBox("Notifications de fournisseur")
        types_layout.addWidget(self.notify_supplier_check)

        self.notify_purchase_check = QCheckBox("Notifications d'achat")
        types_layout.addWidget(self.notify_purchase_check)

        self.notify_sale_check = QCheckBox("Notifications de vente")
        types_layout.addWidget(self.notify_sale_check)

        self.notify_system_check = QCheckBox("Notifications système")
        types_layout.addWidget(self.notify_system_check)

        layout.addWidget(types_group)

        layout.addStretch()

        return tab

    def _create_printing_tab(self):
        """Crée l'onglet Impression"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Paramètres d'impression
        printing_group = QGroupBox("Paramètres d'impression")
        printing_layout = QFormLayout(printing_group)

        self.default_printer_combo = QComboBox()
        # TODO: Charger la liste des imprimantes
        printing_layout.addRow("Imprimante par défaut:", self.default_printer_combo)

        self.paper_size_combo = QComboBox()
        self.paper_size_combo.addItem("A4")
        self.paper_size_combo.addItem("A5")
        self.paper_size_combo.addItem("Letter")
        self.paper_size_combo.addItem("Legal")
        printing_layout.addRow("Format de papier:", self.paper_size_combo)

        self.orientation_combo = QComboBox()
        self.orientation_combo.addItem("Portrait")
        self.orientation_combo.addItem("Paysage")
        printing_layout.addRow("Orientation:", self.orientation_combo)

        layout.addWidget(printing_group)

        # Imprimantes thermiques
        thermal_group = QGroupBox("Imprimantes thermiques (ESC/POS)")
        thermal_layout = QVBoxLayout(thermal_group)

        thermal_description = QLabel("Configurez les imprimantes thermiques pour l'impression des reçus, factures et autres documents.")
        thermal_description.setWordWrap(True)
        thermal_layout.addWidget(thermal_description)

        self.configure_thermal_button = QPushButton("Configurer les imprimantes thermiques")
        self.configure_thermal_button.clicked.connect(self.configure_thermal_printers)
        thermal_layout.addWidget(self.configure_thermal_button)

        layout.addWidget(thermal_group)

        # Marges
        margins_group = QGroupBox("Marges (mm)")
        margins_layout = QFormLayout(margins_group)

        self.margin_top_spin = QSpinBox()
        self.margin_top_spin.setRange(0, 50)
        margins_layout.addRow("Haut:", self.margin_top_spin)

        self.margin_bottom_spin = QSpinBox()
        self.margin_bottom_spin.setRange(0, 50)
        margins_layout.addRow("Bas:", self.margin_bottom_spin)

        self.margin_left_spin = QSpinBox()
        self.margin_left_spin.setRange(0, 50)
        margins_layout.addRow("Gauche:", self.margin_left_spin)

        self.margin_right_spin = QSpinBox()
        self.margin_right_spin.setRange(0, 50)
        margins_layout.addRow("Droite:", self.margin_right_spin)

        layout.addWidget(margins_group)

        # En-tête et pied de page
        header_footer_group = QGroupBox("En-tête et pied de page")
        header_footer_layout = QFormLayout(header_footer_group)

        self.header_text_edit = QLineEdit()
        header_footer_layout.addRow("Texte d'en-tête:", self.header_text_edit)

        self.footer_text_edit = QLineEdit()
        header_footer_layout.addRow("Texte de pied de page:", self.footer_text_edit)

        self.show_logo_check = QCheckBox()
        header_footer_layout.addRow("Afficher le logo:", self.show_logo_check)

        layout.addWidget(header_footer_group)

        layout.addStretch()

        return tab

    def _create_advanced_tab(self):
        """Crée l'onglet Avancé"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Paramètres avancés
        advanced_group = QGroupBox("Paramètres avancés")
        advanced_layout = QFormLayout(advanced_group)

        self.debug_mode_check = QCheckBox()
        advanced_layout.addRow("Mode debug:", self.debug_mode_check)

        self.log_level_combo = QComboBox()
        self.log_level_combo.addItem("DEBUG")
        self.log_level_combo.addItem("INFO")
        self.log_level_combo.addItem("WARNING")
        self.log_level_combo.addItem("ERROR")
        self.log_level_combo.addItem("CRITICAL")
        advanced_layout.addRow("Niveau de log:", self.log_level_combo)

        layout.addWidget(advanced_group)

        # API
        api_group = QGroupBox("API")
        api_layout = QFormLayout(api_group)

        self.enable_api_check = QCheckBox()
        api_layout.addRow("Activer l'API:", self.enable_api_check)

        self.api_port_spin = QSpinBox()
        self.api_port_spin.setRange(1024, 65535)
        api_layout.addRow("Port API:", self.api_port_spin)

        self.enable_remote_access_check = QCheckBox()
        api_layout.addRow("Activer l'accès distant:", self.enable_remote_access_check)

        self.remote_access_port_spin = QSpinBox()
        self.remote_access_port_spin.setRange(1024, 65535)
        api_layout.addRow("Port d'accès distant:", self.remote_access_port_spin)

        layout.addWidget(api_group)

        # Mises à jour
        updates_group = QGroupBox("Mises à jour")
        updates_layout = QFormLayout(updates_group)

        self.enable_auto_update_check = QCheckBox()
        updates_layout.addRow("Activer les mises à jour automatiques:", self.enable_auto_update_check)

        layout.addWidget(updates_group)

        layout.addStretch()

        return tab

    def setup_connections(self):
        """Configure les connexions entre les widgets"""
        # Boutons d'action
        self.save_button.clicked.connect(self.save_settings)
        self.reset_button.clicked.connect(self.reset_settings)

        # Onglet Apparence
        self.theme_combo.currentIndexChanged.connect(self.preview_theme)

        # Onglet Base de données
        self.optimize_button.clicked.connect(self.optimize_database)
        self.reset_db_button.clicked.connect(self.reset_database)

        # Onglet Sauvegardes
        self.browse_backup_path_button.clicked.connect(self.browse_backup_path)
        self.create_backup_button.clicked.connect(self.create_backup)
        self.restore_backup_button.clicked.connect(self.restore_backup)
        self.delete_backup_button.clicked.connect(self.delete_backup)

        # Onglet Général
        self.browse_logo_button.clicked.connect(self.browse_logo)

    def load_settings(self):
        """Charge les paramètres depuis la base de données"""
        self.loading_overlay.show()
        QTimer.singleShot(0, self._load_settings_wrapper)

    def _load_settings_wrapper(self):
        """Wrapper pour exécuter load_settings_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_settings_async())
        except Exception as e:
            print(f"Erreur lors du chargement des paramètres: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def load_settings_async(self):
        """Charge les paramètres de manière asynchrone"""
        try:
            # Onglet Général
            self.company_name_edit.setText(await self.settings_service.get_setting_value("general.company_name", ""))
            self.company_address_edit.setText(await self.settings_service.get_setting_value("general.company_address", ""))
            self.company_phone_edit.setText(await self.settings_service.get_setting_value("general.company_phone", ""))
            self.company_email_edit.setText(await self.settings_service.get_setting_value("general.company_email", ""))
            self.company_website_edit.setText(await self.settings_service.get_setting_value("general.company_website", ""))
            self.company_logo_edit.setText(await self.settings_service.get_setting_value("general.company_logo", ""))
            self.default_currency_edit.setText(await self.settings_service.get_setting_value("general.default_currency", "DA"))
            self.tax_rate_spin.setValue(float(await self.settings_service.get_setting_value("general.tax_rate", "19.0")))
            self.fiscal_year_start_edit.setText(await self.settings_service.get_setting_value("general.fiscal_year_start", "01-01"))

            # Onglet Apparence
            theme = await self.settings_service.get_setting_value("appearance.theme", "light")
            index = self.theme_combo.findData(theme)
            if index >= 0:
                self.theme_combo.setCurrentIndex(index)

            self.font_size_spin.setValue(int(await self.settings_service.get_setting_value("appearance.font_size", "12")))

            language = await self.settings_service.get_setting_value("appearance.language", "fr")
            index = self.language_combo.findData(language)
            if index >= 0:
                self.language_combo.setCurrentIndex(index)

            self.show_welcome_check.setChecked(await self.settings_service.get_setting_value("appearance.show_welcome_screen", True))
            self.compact_mode_check.setChecked(await self.settings_service.get_setting_value("appearance.compact_mode", False))
            self.enable_animations_check.setChecked(await self.settings_service.get_setting_value("appearance.enable_animations", True))

            # Onglet Base de données
            # Récupérer le chemin de la base de données
            from app.utils.database import engine
            db_path = engine.url.database
            if db_path.startswith('/'):
                db_path = db_path[1:]  # Supprimer le premier '/' pour les chemins absolus

            self.db_path_label.setText(db_path)

            # Calculer la taille de la base de données
            if os.path.exists(db_path):
                size = os.path.getsize(db_path)
                if size < 1024:
                    size_str = f"{size} B"
                elif size < 1024 * 1024:
                    size_str = f"{size / 1024:.2f} KB"
                elif size < 1024 * 1024 * 1024:
                    size_str = f"{size / (1024 * 1024):.2f} MB"
                else:
                    size_str = f"{size / (1024 * 1024 * 1024):.2f} GB"
                self.db_size_label.setText(size_str)
            else:
                self.db_size_label.setText("Fichier non trouvé")

            self.auto_optimize_check.setChecked(await self.settings_service.get_setting_value("database.auto_optimize", True))

            optimize_frequency = await self.settings_service.get_setting_value("database.optimize_frequency", "weekly")
            if optimize_frequency == "daily":
                self.optimize_frequency_combo.setCurrentIndex(0)
            elif optimize_frequency == "weekly":
                self.optimize_frequency_combo.setCurrentIndex(1)
            elif optimize_frequency == "monthly":
                self.optimize_frequency_combo.setCurrentIndex(2)

            # Onglet Sauvegardes
            self.auto_backup_check.setChecked(await self.settings_service.get_setting_value("backup.auto_backup", False))

            backup_frequency = await self.settings_service.get_setting_value("backup.backup_frequency", "weekly")
            if backup_frequency == "daily":
                self.backup_frequency_combo.setCurrentIndex(0)
            elif backup_frequency == "weekly":
                self.backup_frequency_combo.setCurrentIndex(1)
            elif backup_frequency == "monthly":
                self.backup_frequency_combo.setCurrentIndex(2)

            self.backup_path_edit.setText(await self.settings_service.get_setting_value("backup.backup_path", "./backups"))
            self.max_backups_spin.setValue(int(await self.settings_service.get_setting_value("backup.max_backups", "10")))
            self.include_attachments_check.setChecked(await self.settings_service.get_setting_value("backup.include_attachments", True))
            self.compress_backup_check.setChecked(await self.settings_service.get_setting_value("backup.compress_backup", True))

            # Charger la liste des sauvegardes
            await self.backups_model.load_data()

            # Sélectionner la première sauvegarde si disponible
            self.select_first_backup()

            # Onglet Sécurité
            self.token_expiration_spin.setValue(int(await self.settings_service.get_setting_value("security.token_expiration", "1")))
            self.encryption_key_edit.setText(await self.settings_service.get_setting_value("security.encryption_key", ""))
            self.min_password_length_spin.setValue(int(await self.settings_service.get_setting_value("security.min_password_length", "8")))
            self.require_special_chars_check.setChecked(await self.settings_service.get_setting_value("security.require_special_chars", True))
            self.require_numbers_check.setChecked(await self.settings_service.get_setting_value("security.require_numbers", True))
            self.require_uppercase_check.setChecked(await self.settings_service.get_setting_value("security.require_uppercase", True))
            self.max_login_attempts_spin.setValue(int(await self.settings_service.get_setting_value("security.max_login_attempts", "5")))

            # Onglet Notifications
            self.notification_cleanup_interval_spin.setValue(int(await self.settings_service.get_setting_value("notifications.cleanup_interval_hours", "24")))
            self.notification_expiry_days_spin.setValue(int(await self.settings_service.get_setting_value("notifications.default_expiry_days", "30")))
            self.notify_inventory_check.setChecked(await self.settings_service.get_setting_value("notifications.notify_inventory", True))
            self.notify_repair_check.setChecked(await self.settings_service.get_setting_value("notifications.notify_repair", True))
            self.notify_customer_check.setChecked(await self.settings_service.get_setting_value("notifications.notify_customer", True))
            self.notify_supplier_check.setChecked(await self.settings_service.get_setting_value("notifications.notify_supplier", True))
            self.notify_purchase_check.setChecked(await self.settings_service.get_setting_value("notifications.notify_purchase", True))
            self.notify_sale_check.setChecked(await self.settings_service.get_setting_value("notifications.notify_sale", True))
            self.notify_system_check.setChecked(await self.settings_service.get_setting_value("notifications.notify_system", True))

            # Onglet Impression
            self.paper_size_combo.setCurrentText(await self.settings_service.get_setting_value("printing.paper_size", "A4"))
            self.orientation_combo.setCurrentText(await self.settings_service.get_setting_value("printing.orientation", "Portrait"))
            self.margin_top_spin.setValue(int(await self.settings_service.get_setting_value("printing.margin_top", "20")))
            self.margin_bottom_spin.setValue(int(await self.settings_service.get_setting_value("printing.margin_bottom", "20")))
            self.margin_left_spin.setValue(int(await self.settings_service.get_setting_value("printing.margin_left", "20")))
            self.margin_right_spin.setValue(int(await self.settings_service.get_setting_value("printing.margin_right", "20")))
            self.header_text_edit.setText(await self.settings_service.get_setting_value("printing.header_text", ""))
            self.footer_text_edit.setText(await self.settings_service.get_setting_value("printing.footer_text", ""))
            self.show_logo_check.setChecked(await self.settings_service.get_setting_value("printing.show_logo", True))

            # Charger la configuration des imprimantes thermiques
            self._refresh_printer_config()

            # Onglet Avancé
            self.debug_mode_check.setChecked(await self.settings_service.get_setting_value("advanced.debug_mode", False))
            self.log_level_combo.setCurrentText(await self.settings_service.get_setting_value("advanced.log_level", "INFO"))
            self.enable_api_check.setChecked(await self.settings_service.get_setting_value("advanced.enable_api", False))
            self.api_port_spin.setValue(int(await self.settings_service.get_setting_value("advanced.api_port", "8000")))
            self.enable_remote_access_check.setChecked(await self.settings_service.get_setting_value("advanced.enable_remote_access", False))
            self.remote_access_port_spin.setValue(int(await self.settings_service.get_setting_value("advanced.remote_access_port", "8080")))
            self.enable_auto_update_check.setChecked(await self.settings_service.get_setting_value("advanced.enable_auto_update", True))

        except Exception as e:
            print(f"Erreur lors du chargement des paramètres: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.loading_overlay.hide()

    def save_settings(self):
        """Enregistre les paramètres dans la base de données"""
        self.loading_overlay.show()
        QTimer.singleShot(0, self._save_settings_wrapper)

    def _save_settings_wrapper(self):
        """Wrapper pour exécuter save_settings_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.save_settings_async())
        except Exception as e:
            print(f"Erreur lors de l'enregistrement des paramètres: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def save_settings_async(self):
        """Enregistre les paramètres de manière asynchrone"""
        try:
            # Onglet Général
            await self.settings_service.set_setting("general.company_name", self.company_name_edit.text())
            await self.settings_service.set_setting("general.company_address", self.company_address_edit.text())
            await self.settings_service.set_setting("general.company_phone", self.company_phone_edit.text())
            await self.settings_service.set_setting("general.company_email", self.company_email_edit.text())
            await self.settings_service.set_setting("general.company_website", self.company_website_edit.text())
            await self.settings_service.set_setting("general.company_logo", self.company_logo_edit.text())
            await self.settings_service.set_setting("general.default_currency", self.default_currency_edit.text())
            await self.settings_service.set_setting("general.tax_rate", str(self.tax_rate_spin.value()))
            await self.settings_service.set_setting("general.fiscal_year_start", self.fiscal_year_start_edit.text())

            # Onglet Apparence
            theme = self.theme_combo.currentData()
            await self.settings_service.set_setting("appearance.theme", theme)
            await self.settings_service.set_setting("appearance.font_size", str(self.font_size_spin.value()))
            await self.settings_service.set_setting("appearance.language", self.language_combo.currentData())
            await self.settings_service.set_setting("appearance.show_welcome_screen", str(self.show_welcome_check.isChecked()))
            await self.settings_service.set_setting("appearance.compact_mode", str(self.compact_mode_check.isChecked()))
            await self.settings_service.set_setting("appearance.enable_animations", str(self.enable_animations_check.isChecked()))

            # Onglet Base de données
            await self.settings_service.set_setting("database.auto_optimize", str(self.auto_optimize_check.isChecked()))
            await self.settings_service.set_setting("database.optimize_frequency", self.optimize_frequency_combo.currentData().name.lower())

            # Onglet Sauvegardes
            await self.settings_service.set_setting("backup.auto_backup", str(self.auto_backup_check.isChecked()))
            await self.settings_service.set_setting("backup.backup_frequency", self.backup_frequency_combo.currentData().name.lower())
            await self.settings_service.set_setting("backup.backup_path", self.backup_path_edit.text())
            await self.settings_service.set_setting("backup.max_backups", str(self.max_backups_spin.value()))
            await self.settings_service.set_setting("backup.include_attachments", str(self.include_attachments_check.isChecked()))
            await self.settings_service.set_setting("backup.compress_backup", str(self.compress_backup_check.isChecked()))

            # Onglet Sécurité
            await self.settings_service.set_setting("security.token_expiration", str(self.token_expiration_spin.value()))
            await self.settings_service.set_setting("security.encryption_key", self.encryption_key_edit.text())
            await self.settings_service.set_setting("security.min_password_length", str(self.min_password_length_spin.value()))
            await self.settings_service.set_setting("security.require_special_chars", str(self.require_special_chars_check.isChecked()))
            await self.settings_service.set_setting("security.require_numbers", str(self.require_numbers_check.isChecked()))
            await self.settings_service.set_setting("security.require_uppercase", str(self.require_uppercase_check.isChecked()))
            await self.settings_service.set_setting("security.max_login_attempts", str(self.max_login_attempts_spin.value()))

            # Onglet Notifications
            await self.settings_service.set_setting("notifications.cleanup_interval_hours", str(self.notification_cleanup_interval_spin.value()))
            await self.settings_service.set_setting("notifications.default_expiry_days", str(self.notification_expiry_days_spin.value()))
            await self.settings_service.set_setting("notifications.notify_inventory", str(self.notify_inventory_check.isChecked()))
            await self.settings_service.set_setting("notifications.notify_repair", str(self.notify_repair_check.isChecked()))
            await self.settings_service.set_setting("notifications.notify_customer", str(self.notify_customer_check.isChecked()))
            await self.settings_service.set_setting("notifications.notify_supplier", str(self.notify_supplier_check.isChecked()))
            await self.settings_service.set_setting("notifications.notify_purchase", str(self.notify_purchase_check.isChecked()))
            await self.settings_service.set_setting("notifications.notify_sale", str(self.notify_sale_check.isChecked()))
            await self.settings_service.set_setting("notifications.notify_system", str(self.notify_system_check.isChecked()))

            # Onglet Impression
            await self.settings_service.set_setting("printing.paper_size", self.paper_size_combo.currentText())
            await self.settings_service.set_setting("printing.orientation", self.orientation_combo.currentText())
            await self.settings_service.set_setting("printing.margin_top", str(self.margin_top_spin.value()))
            await self.settings_service.set_setting("printing.margin_bottom", str(self.margin_bottom_spin.value()))
            await self.settings_service.set_setting("printing.margin_left", str(self.margin_left_spin.value()))
            await self.settings_service.set_setting("printing.margin_right", str(self.margin_right_spin.value()))
            await self.settings_service.set_setting("printing.header_text", self.header_text_edit.text())
            await self.settings_service.set_setting("printing.footer_text", self.footer_text_edit.text())
            await self.settings_service.set_setting("printing.show_logo", str(self.show_logo_check.isChecked()))

            # Onglet Avancé
            await self.settings_service.set_setting("advanced.debug_mode", str(self.debug_mode_check.isChecked()))
            await self.settings_service.set_setting("advanced.log_level", self.log_level_combo.currentText())
            await self.settings_service.set_setting("advanced.enable_api", str(self.enable_api_check.isChecked()))
            await self.settings_service.set_setting("advanced.api_port", str(self.api_port_spin.value()))
            await self.settings_service.set_setting("advanced.enable_remote_access", str(self.enable_remote_access_check.isChecked()))
            await self.settings_service.set_setting("advanced.remote_access_port", str(self.remote_access_port_spin.value()))
            await self.settings_service.set_setting("advanced.enable_auto_update", str(self.enable_auto_update_check.isChecked()))

            # Émettre le signal de changement de thème
            self.themeChanged.emit(theme)

            # Afficher un message de confirmation
            QMessageBox.information(self, "Paramètres enregistrés", "Les paramètres ont été enregistrés avec succès.")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'enregistrement des paramètres: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def reset_settings(self):
        """Réinitialise les paramètres aux valeurs par défaut"""
        # Demander confirmation
        result = QMessageBox.question(
            self,
            "Réinitialiser les paramètres",
            "Êtes-vous sûr de vouloir réinitialiser tous les paramètres aux valeurs par défaut ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if result == QMessageBox.StandardButton.Yes:
            self.load_settings()

    def preview_theme(self, index):
        """Affiche un aperçu du thème sélectionné"""
        theme = self.theme_combo.itemData(index)

        try:
            # Charger les données du thème
            theme_data = self.theme_manager.load_theme(theme)

            # Mettre à jour l'aperçu
            if theme == "light":
                self.theme_preview.setStyleSheet("background-color: #FFFFFF; color: #212121; padding: 10px;")
            else:
                self.theme_preview.setStyleSheet("background-color: #121212; color: #FFFFFF; padding: 10px;")

            # Mettre à jour le texte de l'aperçu
            self.theme_preview.setText(f"Aperçu du thème {theme_data['name']}")

        except Exception as e:
            print(f"Erreur lors de l'aperçu du thème: {e}")

    def browse_logo(self):
        """Ouvre une boîte de dialogue pour sélectionner un logo"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Sélectionner un logo",
            "",
            "Images (*.png *.jpg *.jpeg *.svg)"
        )

        if file_path:
            self.company_logo_edit.setText(file_path)

    def browse_backup_path(self):
        """Ouvre une boîte de dialogue pour sélectionner un dossier de sauvegarde"""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            "Sélectionner un dossier de sauvegarde",
            self.backup_path_edit.text()
        )

        if folder_path:
            self.backup_path_edit.setText(folder_path)

    def create_backup(self):
        """Crée une sauvegarde"""
        dialog = BackupDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Rafraîchir la liste des sauvegardes
            self.backups_model.refresh()

            # Sélectionner la première sauvegarde (la plus récente)
            self.select_first_backup()

    def restore_backup(self):
        """Restaure une sauvegarde"""
        # Récupérer la sauvegarde sélectionnée
        selected_indexes = self.backups_table.selectedIndexes()
        if not selected_indexes:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner une sauvegarde à restaurer.")
            return

        # Récupérer l'ID de la sauvegarde
        row = selected_indexes[0].row()
        backup = self.backups_model.get_backup_at_row(row)
        if not backup:
            QMessageBox.warning(self, "Erreur", "Impossible de récupérer les informations de la sauvegarde.")
            return

        # Vérifier que la sauvegarde a un ID valide
        if not hasattr(backup, 'id') or backup.id is None or not isinstance(backup.id, int) or backup.id <= 0:
            QMessageBox.warning(
                self,
                "Erreur",
                f"La sauvegarde '{backup.filename}' n'a pas d'ID valide. Veuillez rafraîchir la liste des sauvegardes et réessayer."
            )
            # Rafraîchir la liste des sauvegardes
            self.backups_model.refresh()
            return

        # Vérifier que le fichier de sauvegarde existe
        if not os.path.exists(backup.path):
            QMessageBox.warning(
                self,
                "Erreur",
                f"Le fichier de sauvegarde '{backup.path}' n'existe pas. Veuillez vérifier que le fichier existe et réessayer."
            )
            return

        # Ouvrir la boîte de dialogue de restauration
        dialog = RestoreDialog(self, backup.id)
        dialog.exec()

    def delete_backup(self):
        """Supprime une sauvegarde"""
        # Récupérer la sauvegarde sélectionnée
        selected_indexes = self.backups_table.selectedIndexes()
        if not selected_indexes:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner une sauvegarde à supprimer.")
            return

        # Récupérer l'ID de la sauvegarde
        row = selected_indexes[0].row()
        backup = self.backups_model.get_backup_at_row(row)
        if not backup:
            QMessageBox.warning(self, "Erreur", "Impossible de récupérer les informations de la sauvegarde.")
            return

        # Demander confirmation
        result = QMessageBox.question(
            self,
            "Supprimer la sauvegarde",
            f"Êtes-vous sûr de vouloir supprimer la sauvegarde '{backup.filename}' ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if result == QMessageBox.StandardButton.Yes:
            self.loading_overlay.show()
            QTimer.singleShot(0, lambda: self._delete_backup_wrapper(backup.id))

    def _delete_backup_wrapper(self, backup_id):
        """Wrapper pour exécuter delete_backup_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.delete_backup_async(backup_id))
        except Exception as e:
            print(f"Erreur lors de la suppression de la sauvegarde: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def delete_backup_async(self, backup_id):
        """Supprime une sauvegarde de manière asynchrone"""
        try:
            # Fermer la session actuelle
            self.db.close()

            # Créer une nouvelle session
            from app.utils.database import SessionLocal
            new_db = SessionLocal()

            try:
                # Récupérer les informations de la sauvegarde
                backup_info = new_db.query(BackupInfo).get(backup_id)
                if not backup_info:
                    raise ValueError(f"Sauvegarde avec ID {backup_id} non trouvée")

                # Récupérer le chemin du fichier avant de supprimer l'objet
                backup_path = backup_info.path

                # Supprimer le fichier de sauvegarde
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                    print(f"Fichier de sauvegarde supprimé: {backup_path}")
                else:
                    print(f"Fichier de sauvegarde introuvable: {backup_path}")

                # Supprimer l'entrée de la base de données
                new_db.delete(backup_info)
                new_db.commit()
                print(f"Sauvegarde supprimée de la base de données: {backup_id}")

                # Afficher un message de confirmation
                QMessageBox.information(self, "Sauvegarde supprimée", "La sauvegarde a été supprimée avec succès.")
            finally:
                # Fermer la nouvelle session
                new_db.close()

                # Recréer la session originale
                self.db = SessionLocal()
                self.backup_service = BackupService(self.db)

            # Rafraîchir la liste des sauvegardes
            await self.backups_model.load_data()
        except Exception as e:
            print(f"Erreur lors de la suppression de la sauvegarde: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de la suppression de la sauvegarde: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def optimize_database(self):
        """Optimise la base de données"""
        self.loading_overlay.show()
        QTimer.singleShot(0, self._optimize_database_wrapper)

    def _optimize_database_wrapper(self):
        """Wrapper pour exécuter optimize_database_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.optimize_database_async())
        except Exception as e:
            print(f"Erreur lors de l'optimisation de la base de données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    async def optimize_database_async(self):
        """Optimise la base de données de manière asynchrone"""
        try:
            # Exécuter la commande VACUUM
            from sqlalchemy import text
            from app.utils.database import engine

            with engine.connect() as conn:
                conn.execute(text("VACUUM"))
                conn.execute(text("ANALYZE"))

            # Mettre à jour la taille de la base de données
            db_path = engine.url.database
            if db_path.startswith('/'):
                db_path = db_path[1:]  # Supprimer le premier '/' pour les chemins absolus

            if os.path.exists(db_path):
                size = os.path.getsize(db_path)
                if size < 1024:
                    size_str = f"{size} B"
                elif size < 1024 * 1024:
                    size_str = f"{size / 1024:.2f} KB"
                elif size < 1024 * 1024 * 1024:
                    size_str = f"{size / (1024 * 1024):.2f} MB"
                else:
                    size_str = f"{size / (1024 * 1024 * 1024):.2f} GB"
                self.db_size_label.setText(size_str)

            QMessageBox.information(self, "Optimisation terminée", "La base de données a été optimisée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'optimisation de la base de données: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def reset_database(self):
        """Réinitialise la base de données"""
        dialog = ResetDialog(self)
        dialog.exec()

    def on_backup_selection_changed(self, selected, deselected):
        """Gère le changement de sélection dans le tableau des sauvegardes"""
        # Activer/désactiver les boutons en fonction de la sélection
        has_selection = len(self.backups_table.selectionModel().selectedRows()) > 0
        self.restore_backup_button.setEnabled(has_selection)
        self.delete_backup_button.setEnabled(has_selection)

        # Mettre le focus sur la ligne sélectionnée
        if has_selection:
            row = self.backups_table.selectionModel().selectedRows()[0].row()
            self.backups_table.scrollTo(self.backups_model.index(row, 0))

            # Mettre en évidence la ligne sélectionnée
            self.backups_table.selectRow(row)

    def select_first_backup(self):
        """Sélectionne la première sauvegarde dans le tableau si disponible"""
        if self.backups_model.rowCount() > 0:
            self.backups_table.selectRow(0)
            self.backups_table.setFocus()
        else:
            # Désactiver les boutons si aucune sauvegarde n'est disponible
            self.restore_backup_button.setEnabled(False)
            self.delete_backup_button.setEnabled(False)

    def configure_thermal_printers(self):
        """Ouvre la boîte de dialogue de configuration des imprimantes thermiques"""
        dialog = PrinterConfigDialog(self)
        dialog.configChanged.connect(self._refresh_printer_config)
        dialog.exec()

    def _refresh_printer_config(self):
        """Rafraîchit la configuration des imprimantes"""
        # Charger la configuration des imprimantes
        printer_config = PrinterConfigDialog.get_printer_config()

        # Mettre à jour la liste des imprimantes
        self.default_printer_combo.clear()

        # Ajouter les imprimantes configurées
        for printer_name in printer_config.get("printers", {}).keys():
            self.default_printer_combo.addItem(printer_name)

        # Sélectionner l'imprimante par défaut
        default_printer = printer_config.get("default_printer")
        if default_printer:
            index = self.default_printer_combo.findText(default_printer)
            if index >= 0:
                self.default_printer_combo.setCurrentIndex(index)