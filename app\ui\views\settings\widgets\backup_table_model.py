"""
Modèle de table pour l'affichage des sauvegardes.
"""
from PyQt6.QtCore import Qt, QAbstractTableModel
from typing import List, Any
import os
from datetime import datetime
import asyncio

from app.core.services.settings_service import BackupService
from app.core.models.settings import BackupInfo

class BackupTableModel(QAbstractTableModel):
    """Modèle de table pour l'affichage des sauvegardes"""

    def __init__(self):
        super().__init__()
        # Créer une nouvelle session pour le modèle de table
        from app.utils.database import SessionLocal
        self.db = SessionLocal()
        self.service = BackupService(self.db)  # Utiliser la même session pour le service
        self.backups = []
        self.headers = [
            "Nom",
            "Taille",
            "Date de création",
            "Description",
            "Version",
            "Auto"
        ]

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("BackupTableModel: Session de base de données fermée")

    async def load_data(self):
        """Charge les données depuis le service et le système de fichiers"""
        self.beginResetModel()
        try:
            # Fermer l'ancienne session si elle existe
            if hasattr(self, 'db') and self.db:
                self.db.close()
                print("BackupTableModel: Ancienne session fermée")

            # Créer une nouvelle session
            from app.utils.database import SessionLocal
            self.db = SessionLocal()
            self.service = BackupService(self.db)

            # Charger les données avec la nouvelle session
            try:
                # Récupérer les sauvegardes directement depuis la base de données
                # au lieu d'utiliser le service pour éviter les problèmes de session
                from app.core.models.settings import BackupInfo
                from sqlalchemy import desc

                # Récupérer toutes les sauvegardes triées par date de création (la plus récente en premier)
                self.backups = self.db.query(BackupInfo).order_by(desc(BackupInfo.created_at)).all()

                print(f"Sauvegardes chargées depuis la base de données: {len(self.backups)}")
            except Exception as e:
                print(f"Erreur lors du chargement des sauvegardes depuis la base de données: {e}")
                import traceback
                traceback.print_exc()
                self.backups = []

            # Si aucune sauvegarde n'a été chargée depuis la base de données,
            # essayer de les charger à partir du système de fichiers
            if not self.backups:
                print("Aucune sauvegarde trouvée dans la base de données, chargement à partir du système de fichiers...")
                self.load_backups_from_filesystem()
        except Exception as e:
            print(f"Erreur lors du chargement des sauvegardes: {e}")
            import traceback
            traceback.print_exc()
            self.backups = []
        self.endResetModel()

    def load_backups_from_filesystem(self):
        """Charge les sauvegardes à partir du système de fichiers et les enregistre dans la base de données"""
        try:
            # Chemin du répertoire de sauvegarde
            backup_dir = os.path.join(os.getcwd(), "backups")
            if not os.path.exists(backup_dir):
                print(f"Le répertoire de sauvegarde {backup_dir} n'existe pas")
                return

            # Récupérer tous les fichiers de sauvegarde
            backup_files = []
            for f in os.listdir(backup_dir):
                if f.startswith("backup_") and (f.endswith(".zip") or f.endswith(".db")):
                    backup_files.append(os.path.join(backup_dir, f))

            if not backup_files:
                print("Aucun fichier de sauvegarde trouvé")
                return

            print(f"Chargement de {len(backup_files)} fichiers de sauvegarde depuis le système de fichiers")

            # Créer des objets BackupInfo à partir des fichiers et les enregistrer dans la base de données
            for file_path in backup_files:
                filename = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)

                # Vérifier si la sauvegarde existe déjà dans la base de données
                existing_backup = self.db.query(BackupInfo).filter(BackupInfo.filename == filename).first()
                if existing_backup:
                    print(f"La sauvegarde {filename} existe déjà dans la base de données avec l'ID {existing_backup.id}")
                    self.backups.append(existing_backup)
                    continue

                # Extraire la date de création à partir du nom de fichier
                import re
                from datetime import datetime as dt
                match = re.search(r'backup_(\d{8}_\d{6})', filename)
                if match:
                    timestamp_str = match.group(1)
                    try:
                        created_at = dt.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                    except ValueError:
                        created_at = dt.now()
                else:
                    created_at = dt.now()

                # Extraire la version à partir du nom de fichier
                match = re.search(r'_v(\d+\.\d+\.\d+)', filename)
                version = match.group(1) if match else "1.0.0"

                try:
                    # Créer et enregistrer directement dans la base de données
                    new_backup = BackupInfo(
                        filename=filename,
                        path=file_path,
                        size=file_size,
                        created_at=created_at,
                        description="Sauvegarde importée automatiquement",
                        version=version,
                        is_auto=False,
                        backup_metadata={
                            "compress": filename.endswith(".zip"),
                            "include_attachments": True,
                            "db_path": os.path.join(os.getcwd(), "data", "app.db")
                        }
                    )

                    # Ajouter à la base de données
                    self.db.add(new_backup)
                    self.db.commit()

                    print(f"Sauvegarde {filename} enregistrée dans la base de données avec ID {new_backup.id}")

                    # Ajouter l'objet à la liste
                    self.backups.append(new_backup)
                except Exception as e:
                    print(f"Erreur lors de l'enregistrement de la sauvegarde {filename} dans la base de données: {e}")
                    self.db.rollback()
                    import traceback
                    traceback.print_exc()

            print(f"Sauvegardes chargées depuis le système de fichiers et enregistrées dans la base de données: {len(self.backups)}")
        except Exception as e:
            print(f"Erreur lors du chargement des sauvegardes depuis le système de fichiers: {e}")
            import traceback
            traceback.print_exc()

    def rowCount(self, parent=None):
        return len(self.backups)

    def columnCount(self, parent=None):
        return len(self.headers)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.backups):
            return None

        backup = self.backups[index.row()]

        if role == Qt.ItemDataRole.DisplayRole:
            col = index.column()
            try:
                if col == 0:  # Nom
                    # Vérifier si l'objet est détaché
                    try:
                        return backup.filename
                    except Exception:
                        # Si l'objet est détaché, essayer de le rafraîchir
                        try:
                            # Récupérer l'objet depuis la base de données
                            if hasattr(backup, 'id') and backup.id:
                                refreshed_backup = self.db.query(BackupInfo).get(backup.id)
                                if refreshed_backup:
                                    # Remplacer l'objet détaché par l'objet rafraîchi
                                    self.backups[index.row()] = refreshed_backup
                                    return refreshed_backup.filename
                        except Exception as e:
                            print(f"Erreur lors du rafraîchissement de l'objet: {e}")
                        return "Erreur: Objet détaché"
                elif col == 1:  # Taille
                    # Convertir la taille en format lisible (KB, MB, etc.)
                    try:
                        size = backup.size
                        if size < 1024:
                            return f"{size} B"
                        elif size < 1024 * 1024:
                            return f"{size / 1024:.2f} KB"
                        elif size < 1024 * 1024 * 1024:
                            return f"{size / (1024 * 1024):.2f} MB"
                        else:
                            return f"{size / (1024 * 1024 * 1024):.2f} GB"
                    except Exception:
                        return "Erreur: Objet détaché"
                elif col == 2:  # Date de création
                    try:
                        return backup.created_at.strftime("%d/%m/%Y %H:%M")
                    except Exception:
                        return "Erreur: Objet détaché"
                elif col == 3:  # Description
                    try:
                        return backup.description or ""
                    except Exception:
                        return "Erreur: Objet détaché"
                elif col == 4:  # Version
                    try:
                        return backup.version or ""
                    except Exception:
                        return "Erreur: Objet détaché"
                elif col == 5:  # Auto
                    try:
                        return "Oui" if backup.is_auto else "Non"
                    except Exception:
                        return "Erreur: Objet détaché"
            except Exception as e:
                print(f"Erreur lors de l'accès aux données: {e}")
                return "Erreur"

        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return None

    def get_backup_at_row(self, row):
        """
        Récupère la sauvegarde à la ligne spécifiée.
        Si la sauvegarde n'est pas dans la base de données, l'enregistre.
        """
        if 0 <= row < len(self.backups):
            backup = self.backups[row]

            # Vérifier si l'objet est détaché
            try:
                # Essayer d'accéder à un attribut pour vérifier si l'objet est détaché
                test = backup.filename
            except Exception as e:
                print(f"Objet détaché détecté: {e}")
                # Si l'objet est détaché, essayer de le rafraîchir
                try:
                    # Récupérer l'objet depuis la base de données
                    if hasattr(backup, 'id') and backup.id:
                        refreshed_backup = self.db.query(BackupInfo).get(backup.id)
                        if refreshed_backup:
                            # Remplacer l'objet détaché par l'objet rafraîchi
                            self.backups[row] = refreshed_backup
                            backup = refreshed_backup
                            print(f"Objet rafraîchi avec succès: {backup.id}")
                        else:
                            print(f"Impossible de rafraîchir l'objet: ID {backup.id} non trouvé")
                            return None
                    else:
                        print("Impossible de rafraîchir l'objet: pas d'ID")
                        return None
                except Exception as e:
                    print(f"Erreur lors du rafraîchissement de l'objet: {e}")
                    return None

            # Vérifier si la sauvegarde est déjà dans la base de données
            if not hasattr(backup, 'id') or backup.id is None or backup.id <= 0 or isinstance(backup.id, int) and backup.id <= 0:
                # La sauvegarde n'est pas dans la base de données, l'enregistrer
                try:
                    print(f"Enregistrement de la sauvegarde {backup.filename} dans la base de données...")

                    # Vérifier d'abord si la sauvegarde existe déjà dans la base de données par son nom de fichier
                    existing_backup = self.db.query(BackupInfo).filter(BackupInfo.filename == backup.filename).first()
                    if existing_backup:
                        print(f"La sauvegarde {backup.filename} existe déjà dans la base de données avec l'ID {existing_backup.id}")
                        # Mettre à jour l'ID de la sauvegarde dans notre liste
                        backup.id = existing_backup.id
                        return backup

                    # Créer l'entrée dans la base de données
                    try:
                        # Utiliser l'ORM SQLAlchemy au lieu de SQL brut
                        new_backup = BackupInfo(
                            filename=backup.filename,
                            path=backup.path,
                            size=backup.size,
                            created_at=backup.created_at,
                            description=backup.description,
                            version=backup.version,
                            is_auto=backup.is_auto,
                            backup_metadata=backup.backup_metadata
                        )
                        self.db.add(new_backup)
                        self.db.commit()

                        # Récupérer l'ID de la sauvegarde
                        backup.id = new_backup.id
                        print(f"Sauvegarde {backup.filename} enregistrée avec ID {backup.id}")
                    except Exception as e:
                        print(f"Erreur lors de l'enregistrement de la sauvegarde avec l'ORM: {e}")
                        self.db.rollback()

                        # Essayer avec SQL brut en cas d'échec
                        try:
                            cursor = self.db.execute(
                                """
                                INSERT INTO backup_info (
                                    filename, path, size, created_at, updated_at, description, version, is_auto, backup_metadata
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                """,
                                (
                                    backup.filename,
                                    backup.path,
                                    backup.size,
                                    backup.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                                    backup.created_at.strftime("%Y-%m-%d %H:%M:%S"),  # updated_at = created_at
                                    backup.description,
                                    backup.version,
                                    backup.is_auto,
                                    str(backup.backup_metadata)  # Convertir en chaîne pour éviter les problèmes de JSON
                                )
                            )
                            self.db.commit()

                            # Récupérer l'ID de la sauvegarde
                            backup.id = cursor.lastrowid
                            print(f"Sauvegarde {backup.filename} enregistrée avec SQL brut, ID {backup.id}")
                        except Exception as e2:
                            print(f"Erreur lors de l'enregistrement de la sauvegarde avec SQL brut: {e2}")
                            self.db.rollback()
                            import traceback
                            traceback.print_exc()
                except Exception as e:
                    print(f"Erreur lors de l'enregistrement de la sauvegarde {backup.filename}: {e}")
                    import traceback
                    traceback.print_exc()

            # Vérifier que l'ID est valide
            if not hasattr(backup, 'id') or backup.id is None or not isinstance(backup.id, int) or backup.id <= 0:
                print(f"ATTENTION: La sauvegarde {backup.filename} n'a pas d'ID valide après tentative d'enregistrement")
                # Essayer de récupérer l'ID à partir de la base de données
                try:
                    existing_backup = self.db.query(BackupInfo).filter(BackupInfo.filename == backup.filename).first()
                    if existing_backup:
                        backup.id = existing_backup.id
                        print(f"ID récupéré de la base de données: {backup.id}")
                    else:
                        print(f"La sauvegarde {backup.filename} n'existe pas dans la base de données")
                except Exception as e:
                    print(f"Erreur lors de la récupération de l'ID: {e}")

            return backup
        return None

    def refresh(self):
        """Rafraîchit les données du modèle"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Sauvegarder les IDs des sauvegardes actuelles pour préserver la sélection
            current_backup_ids = [backup.id for backup in self.backups if hasattr(backup, 'id') and backup.id]

            # Charger les nouvelles données
            loop.run_until_complete(self.load_data())

            # Retourner les IDs des sauvegardes pour permettre de restaurer la sélection
            return current_backup_ids
        except Exception as e:
            print(f"Erreur lors du rafraîchissement des données: {e}")
            import traceback
            traceback.print_exc()
            return []
        finally:
            loop.close()
