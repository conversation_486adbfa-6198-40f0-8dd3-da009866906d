from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QFormLayout, QHBoxLayout,
    QLineEdit, QTextEdit, QCheckBox, QDialogButtonBox,
    QLabel, QPushButton, QMessageBox, QComboBox, QSpinBox
)
from PyQt6.QtCore import Qt
import asyncio
from app.core.services.supplier_service import SupplierService
from app.core.models.supplier import SupplierRating

class SupplierDialog(QDialog):
    """Boîte de dialogue pour ajouter ou modifier un fournisseur"""

    def __init__(self, parent=None, supplier_id=None):
        super().__init__(parent)
        self.supplier_id = supplier_id
        self.service = SupplierService()
        self.supplier = None

        self.setWindowTitle("Nouveau Fournisseur" if supplier_id is None else "Modifier Fournisseur")
        self.setMinimumWidth(500)

        self.setup_ui()
        self.setup_connections()

        # Charger les données si on est en mode édition
        if supplier_id is not None:
            self.load_supplier_data()

    def setup_ui(self):
        """Configure l'interface utilisateur du dialogue"""
        main_layout = QVBoxLayout(self)

        # Formulaire principal
        form_layout = QFormLayout()

        # Nom du fournisseur
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Nom de l'entreprise")
        form_layout.addRow("Nom*:", self.name_edit)

        # Personne de contact
        self.contact_edit = QLineEdit()
        self.contact_edit.setPlaceholderText("Personne à contacter")
        form_layout.addRow("Contact:", self.contact_edit)

        # Téléphone
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("Numéro de téléphone")
        form_layout.addRow("Téléphone:", self.phone_edit)

        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("Adresse email")
        form_layout.addRow("Email*:", self.email_edit)

        # Adresse
        self.address_edit = QLineEdit()
        self.address_edit.setPlaceholderText("Adresse postale")
        form_layout.addRow("Adresse*:", self.address_edit)

        # Commune
        self.commune_edit = QLineEdit()
        self.commune_edit.setPlaceholderText("Commune")
        form_layout.addRow("Commune:", self.commune_edit)

        # Ville
        self.city_edit = QLineEdit()
        self.city_edit.setPlaceholderText("Ville")
        form_layout.addRow("Ville:", self.city_edit)

        # Code postal
        self.postal_code_edit = QLineEdit()
        self.postal_code_edit.setPlaceholderText("Code postal")
        form_layout.addRow("Code postal:", self.postal_code_edit)

        # Numéro fiscal
        self.tax_id_edit = QLineEdit()
        self.tax_id_edit.setPlaceholderText("Numéro d'identification fiscale")
        form_layout.addRow("N° Fiscal:", self.tax_id_edit)

        # Note
        self.rating_combo = QComboBox()
        for rating in SupplierRating:
            self.rating_combo.addItem(f"{rating.value} - {self._get_rating_description(rating)}", rating)
        form_layout.addRow("Note:", self.rating_combo)

        # Conditions de paiement
        self.payment_terms_edit = QLineEdit()
        self.payment_terms_edit.setPlaceholderText("Ex: 30 jours, à la livraison, etc.")
        form_layout.addRow("Conditions de paiement:", self.payment_terms_edit)

        # Délais de livraison
        delivery_layout = QHBoxLayout()

        # Délai moyen
        self.avg_delivery_spin = QSpinBox()
        self.avg_delivery_spin.setRange(1, 365)
        self.avg_delivery_spin.setValue(7)
        self.avg_delivery_spin.setSuffix(" jours")
        delivery_layout.addWidget(QLabel("Moyen:"))
        delivery_layout.addWidget(self.avg_delivery_spin)

        # Délai minimum
        self.min_delivery_spin = QSpinBox()
        self.min_delivery_spin.setRange(1, 365)
        self.min_delivery_spin.setValue(3)
        self.min_delivery_spin.setSuffix(" jours")
        delivery_layout.addWidget(QLabel("Min:"))
        delivery_layout.addWidget(self.min_delivery_spin)

        # Délai maximum
        self.max_delivery_spin = QSpinBox()
        self.max_delivery_spin.setRange(1, 365)
        self.max_delivery_spin.setValue(14)
        self.max_delivery_spin.setSuffix(" jours")
        delivery_layout.addWidget(QLabel("Max:"))
        delivery_layout.addWidget(self.max_delivery_spin)

        form_layout.addRow("Délais de livraison:", delivery_layout)

        # Fiabilité de livraison
        self.reliability_spin = QSpinBox()
        self.reliability_spin.setRange(0, 100)
        self.reliability_spin.setValue(80)
        self.reliability_spin.setSuffix(" %")
        form_layout.addRow("Fiabilité de livraison:", self.reliability_spin)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes ou commentaires sur ce fournisseur")
        form_layout.addRow("Notes:", self.notes_edit)

        # Actif
        self.active_check = QCheckBox("Fournisseur actif")
        self.active_check.setChecked(True)
        form_layout.addRow("", self.active_check)

        main_layout.addLayout(form_layout)

        # Note sur les champs obligatoires
        required_note = QLabel("* Champs obligatoires")
        required_note.setStyleSheet("color: red;")
        main_layout.addWidget(required_note)

        # Boutons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save |
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.validate_and_accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def _get_rating_description(self, rating):
        """Retourne la description d'une note"""
        descriptions = {
            SupplierRating.EXCELLENT: "Excellent",
            SupplierRating.GOOD: "Bon",
            SupplierRating.AVERAGE: "Moyen",
            SupplierRating.FAIR: "Médiocre",
            SupplierRating.POOR: "Mauvais"
        }
        return descriptions.get(rating, "")

    def setup_connections(self):
        """Configure les connexions des signaux"""
        pass

    def load_supplier_data(self):
        """Charge les données du fournisseur pour l'édition"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la méthode de chargement
            loop.run_until_complete(self._load_supplier_async())

            # Fermer la boucle
            loop.close()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors du chargement des données du fournisseur: {e}")
            import traceback
            traceback.print_exc()

    async def _load_supplier_async(self):
        """Charge les données de manière asynchrone"""
        self.supplier = await self.service.get(self.supplier_id)
        if self.supplier:
            self.name_edit.setText(self.supplier.name)
            if self.supplier.contact_person:
                self.contact_edit.setText(self.supplier.contact_person)
            if self.supplier.phone:
                self.phone_edit.setText(self.supplier.phone)
            self.email_edit.setText(self.supplier.email)
            self.address_edit.setText(self.supplier.address)
            if self.supplier.commune:
                self.commune_edit.setText(self.supplier.commune)
            if self.supplier.city:
                self.city_edit.setText(self.supplier.city)
            if self.supplier.postal_code:
                self.postal_code_edit.setText(self.supplier.postal_code)
            if self.supplier.tax_id:
                self.tax_id_edit.setText(self.supplier.tax_id)

            # Sélectionner la note
            index = self.rating_combo.findData(self.supplier.rating)
            if index >= 0:
                self.rating_combo.setCurrentIndex(index)

            if self.supplier.payment_terms:
                self.payment_terms_edit.setText(self.supplier.payment_terms)

            # Délais de livraison
            if hasattr(self.supplier, 'avg_delivery_time') and self.supplier.avg_delivery_time is not None:
                self.avg_delivery_spin.setValue(self.supplier.avg_delivery_time)

            if hasattr(self.supplier, 'min_delivery_time') and self.supplier.min_delivery_time is not None:
                self.min_delivery_spin.setValue(self.supplier.min_delivery_time)

            if hasattr(self.supplier, 'max_delivery_time') and self.supplier.max_delivery_time is not None:
                self.max_delivery_spin.setValue(self.supplier.max_delivery_time)

            # Fiabilité de livraison
            if hasattr(self.supplier, 'delivery_reliability') and self.supplier.delivery_reliability is not None:
                self.reliability_spin.setValue(int(self.supplier.delivery_reliability))

            # Notes et statut actif
            if hasattr(self.supplier, 'notes') and self.supplier.notes:
                self.notes_edit.setText(self.supplier.notes)
            self.active_check.setChecked(self.supplier.active)

    def validate_and_accept(self):
        """Valide les données avant d'accepter"""
        # Vérifier les champs obligatoires
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Validation", "Le nom du fournisseur est obligatoire.")
            self.name_edit.setFocus()
            return

        if not self.email_edit.text().strip():
            QMessageBox.warning(self, "Validation", "L'email est obligatoire.")
            self.email_edit.setFocus()
            return

        if not self.address_edit.text().strip():
            QMessageBox.warning(self, "Validation", "L'adresse est obligatoire.")
            self.address_edit.setFocus()
            return

        self.accept()

    def accept(self):
        """Enregistre les données et ferme la boîte de dialogue"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la méthode d'enregistrement
            loop.run_until_complete(self._save_supplier_async())

            # Fermer la boucle
            loop.close()

            # Accepter le dialogue
            super().accept()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors de l'enregistrement du fournisseur: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement du fournisseur: {str(e)}")

    async def _save_supplier_async(self):
        """Enregistre les données de manière asynchrone"""
        # Récupérer les données du formulaire
        data = {
            "name": self.name_edit.text().strip(),
            "contact_person": self.contact_edit.text().strip() or None,
            "phone": self.phone_edit.text().strip() or None,
            "email": self.email_edit.text().strip(),
            "address": self.address_edit.text().strip(),
            "commune": self.commune_edit.text().strip() or None,
            "city": self.city_edit.text().strip() or None,
            "postal_code": self.postal_code_edit.text().strip() or None,
            "tax_id": self.tax_id_edit.text().strip() or None,
            "rating": self.rating_combo.currentData(),
            "payment_terms": self.payment_terms_edit.text().strip() or None,
            "notes": self.notes_edit.toPlainText().strip() or None,
            "active": self.active_check.isChecked(),
            "evaluation_scores": {},  # Champ par défaut

            # Nouveaux champs pour les délais de livraison
            "avg_delivery_time": self.avg_delivery_spin.value(),
            "min_delivery_time": self.min_delivery_spin.value(),
            "max_delivery_time": self.max_delivery_spin.value(),
            "delivery_reliability": float(self.reliability_spin.value())
        }

        # Import ici pour éviter les imports circulaires
        from app.core.models.supplier import SupplierPydantic

        if self.supplier_id is None:
            # Création d'un nouveau fournisseur
            # Ne pas spécifier d'ID pour laisser SQLite le générer automatiquement
            supplier_model = SupplierPydantic(**data)
            await self.service.create(supplier_model)
        else:
            # Mise à jour d'un fournisseur existant
            await self.service.update(self.supplier_id, data)


