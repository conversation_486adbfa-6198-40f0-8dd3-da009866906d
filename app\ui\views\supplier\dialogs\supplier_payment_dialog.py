"""
Boîte de dialogue pour enregistrer un paiement fournisseur.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, QLineEdit,
    QPushButton, QDateEdit, QComboBox, QTextEdit, QDialogButtonBox,
    QMessageBox, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime

from app.core.models.supplier_finance import PaymentMethod
from app.core.services.supplier_finance_service import SupplierFinanceService
from app.core.services.supplier_service import SupplierService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class SupplierPaymentDialog(QDialog):
    """Boîte de dialogue pour enregistrer un paiement fournisseur"""

    # Signal émis lorsqu'un paiement est enregistré
    payment_saved = pyqtSignal(int)

    def __init__(self, parent=None, supplier_id=None, invoice_id=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = SupplierFinanceService(self.db)
        self.supplier_service = SupplierService(self.db)

        # Données
        self.supplier_id = supplier_id
        self.invoice_id = invoice_id
        self.suppliers = []
        self.invoices = []

        # Configuration de l'interface
        self.setWindowTitle("Enregistrer un paiement")
        self.resize(500, 400)
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.load_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Formulaire principal
        form_layout = QFormLayout()

        # Fournisseur
        self.supplier_combo = QComboBox()
        form_layout.addRow("Fournisseur:", self.supplier_combo)

        # Facture
        self.invoice_combo = QComboBox()
        self.invoice_combo.addItem("Aucune", None)
        form_layout.addRow("Facture:", self.invoice_combo)

        # Date de paiement
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setCalendarPopup(True)
        self.payment_date_edit.setDate(QDate.currentDate())
        form_layout.addRow("Date de paiement:", self.payment_date_edit)

        # Montant
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0, 1000000000)
        self.amount_spin.setDecimals(2)
        self.amount_spin.setSuffix(" DA")
        form_layout.addRow("Montant:", self.amount_spin)

        # Méthode de paiement
        self.payment_method_combo = QComboBox()
        for method in PaymentMethod:
            self.payment_method_combo.addItem(self._get_payment_method_display(method), method.value)
        form_layout.addRow("Méthode de paiement:", self.payment_method_combo)

        # Référence
        self.reference_edit = QLineEdit()
        self.reference_edit.setPlaceholderText("Numéro de chèque, référence de virement, etc.")
        form_layout.addRow("Référence:", self.reference_edit)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes supplémentaires...")
        form_layout.addRow("Notes:", self.notes_edit)

        main_layout.addLayout(form_layout)

        # Boutons
        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
        self.button_box.accepted.connect(self.save_payment)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)

        # Connexions
        self.supplier_combo.currentIndexChanged.connect(self.on_supplier_changed)
        self.invoice_combo.currentIndexChanged.connect(self.on_invoice_changed)

    def load_data(self):
        """Charge les données"""
        self.loading_overlay.show()

        # Utiliser asyncio pour les opérations asynchrones
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Charger les fournisseurs
            suppliers = loop.run_until_complete(self.supplier_service.get_all())
            self.suppliers = suppliers

            # Remplir le combo des fournisseurs
            self.supplier_combo.clear()
            for supplier in suppliers:
                if supplier.active:
                    self.supplier_combo.addItem(supplier.name, supplier.id)

            # Si un fournisseur est spécifié, le sélectionner
            if self.supplier_id:
                index = self.supplier_combo.findData(self.supplier_id)
                if index >= 0:
                    self.supplier_combo.setCurrentIndex(index)

            # Charger les factures du fournisseur
            self.load_supplier_invoices()

            # Si une facture est spécifiée, la sélectionner
            if self.invoice_id:
                index = self.invoice_combo.findData(self.invoice_id)
                if index >= 0:
                    self.invoice_combo.setCurrentIndex(index)

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des données: {str(e)}"
            )
            self.reject()
        finally:
            loop.close()
            self.loading_overlay.hide()

    def load_supplier_invoices(self):
        """Charge les factures du fournisseur sélectionné"""
        supplier_id = self.supplier_combo.currentData()
        if not supplier_id:
            return

        self.loading_overlay.show()

        # Utiliser asyncio pour les opérations asynchrones
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Charger les factures du fournisseur
            invoices = loop.run_until_complete(self.service.get_supplier_invoices(supplier_id))
            self.invoices = invoices

            # Remplir le combo des factures
            self.invoice_combo.clear()
            self.invoice_combo.addItem("Aucune", None)

            for invoice in invoices:
                # N'afficher que les factures non payées ou partiellement payées
                if invoice.status in ["pending", "partial"]:
                    self.invoice_combo.addItem(
                        f"{invoice.invoice_number} - {invoice.total_amount} {invoice.currency}",
                        invoice.id
                    )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des factures: {str(e)}"
            )
        finally:
            loop.close()
            self.loading_overlay.hide()

    def on_supplier_changed(self, index):
        """Gère le changement de fournisseur"""
        self.load_supplier_invoices()

    def on_invoice_changed(self, index):
        """Gère le changement de facture"""
        if index <= 0:  # "Aucune" sélectionné
            return

        # Récupérer la facture sélectionnée
        invoice_id = self.invoice_combo.currentData()
        if not invoice_id:
            return

        # Trouver la facture dans la liste
        invoice = next((inv for inv in self.invoices if inv.id == invoice_id), None)
        if not invoice:
            return

        # Calculer le montant restant à payer
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Récupérer le solde de la facture
            invoice_balance = loop.run_until_complete(self.service.get_invoice_balance(invoice_id))

            # Mettre à jour le montant
            self.amount_spin.setValue(invoice_balance["balance"])

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du calcul du solde: {str(e)}"
            )
        finally:
            loop.close()

    def save_payment(self):
        """Enregistre le paiement"""
        # Vérifier les champs obligatoires
        if self.supplier_combo.currentIndex() < 0:
            QMessageBox.warning(
                self,
                "Champs obligatoires",
                "Veuillez sélectionner un fournisseur"
            )
            return

        if self.amount_spin.value() <= 0:
            QMessageBox.warning(
                self,
                "Champs obligatoires",
                "Le montant doit être supérieur à 0"
            )
            return

        # Préparer les données
        payment_data = {
            "supplier_id": self.supplier_combo.currentData(),
            "invoice_id": self.invoice_combo.currentData(),
            "payment_date": datetime.combine(
                self.payment_date_edit.date().toPyDate(),
                datetime.min.time()
            ),
            "amount": self.amount_spin.value(),
            "payment_method": self.payment_method_combo.currentData(),
            "reference": self.reference_edit.text().strip(),
            "notes": self.notes_edit.toPlainText().strip(),
            "processed_by": 1  # TODO: Récupérer l'ID de l'utilisateur connecté
        }

        # Afficher l'overlay de chargement
        self.loading_overlay.show()

        # Utiliser asyncio pour les opérations asynchrones
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Enregistrer le paiement
            payment = loop.run_until_complete(
                self.service.record_payment(payment_data)
            )

            # Émettre le signal
            self.payment_saved.emit(payment.id)

            # Afficher un message de confirmation
            QMessageBox.information(
                self,
                "Succès",
                "Paiement enregistré avec succès"
            )

            # Fermer la boîte de dialogue
            self.accept()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors de l'enregistrement du paiement: {str(e)}"
            )
        finally:
            loop.close()
            self.loading_overlay.hide()

    def _get_payment_method_display(self, method):
        """Retourne l'affichage de la méthode de paiement"""
        method_display = {
            PaymentMethod.cash: "Espèces",
            PaymentMethod.bank_transfer: "Virement bancaire",
            PaymentMethod.check: "Chèque",
            PaymentMethod.credit_card: "Carte de crédit",
            PaymentMethod.other: "Autre"
        }
        return method_display.get(method, str(method))
