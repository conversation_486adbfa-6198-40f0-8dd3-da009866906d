"""
Vue pour gérer les finances fournisseurs.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView, QHeaderView,
    QPushButton, QLineEdit, QComboBox, QLabel, QFrame, QMessageBox,
    QTabWidget, QSplitter
)
from PyQt6.QtCore import Qt, QSortFilterProxyModel, QTimer, pyqtSlot
from PyQt6.QtGui import QIcon
import asyncio

from app.core.services.supplier_finance_service import SupplierFinanceService
from app.core.services.supplier_service import SupplierService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from app.ui.models.supplier_finance_table_model import SupplierInvoiceTableModel, SupplierPaymentTableModel
from .dialogs.supplier_invoice_dialog import SupplierInvoiceDialog
from .dialogs.supplier_payment_dialog import SupplierPaymentDialog
from .widgets.supplier_balance_widget import SupplierBalanceWidget

class SupplierFinanceView(QWidget):
    """Vue pour gérer les finances fournisseurs"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = SupplierFinanceService(self.db)
        self.supplier_service = SupplierService(self.db)

        # Configuration de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Timer pour rafraîchir automatiquement le widget de solde
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_balance_widget)
        self.refresh_timer.start(30000)  # Rafraîchir toutes les 30 secondes

        # Charger les données
        self.load_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        title_label = QLabel("Finances fournisseurs")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Splitter principal
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Panneau de gauche (liste des fournisseurs et solde)
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Filtre des fournisseurs
        filter_layout = QHBoxLayout()

        self.supplier_filter = QLineEdit()
        self.supplier_filter.setPlaceholderText("Rechercher un fournisseur...")
        self.supplier_filter.textChanged.connect(self.filter_suppliers)
        filter_layout.addWidget(self.supplier_filter)

        left_layout.addLayout(filter_layout)

        # Liste des fournisseurs
        self.supplier_combo = QComboBox()
        self.supplier_combo.currentIndexChanged.connect(self.on_supplier_changed)
        left_layout.addWidget(self.supplier_combo)

        # Widget de solde fournisseur
        self.balance_widget = SupplierBalanceWidget()
        left_layout.addWidget(self.balance_widget)

        # Boutons d'action
        action_layout = QHBoxLayout()

        self.new_invoice_button = QPushButton("Nouvelle facture")
        self.new_invoice_button.setIcon(QIcon("app/ui/resources/icons/invoice.svg"))
        self.new_invoice_button.clicked.connect(self.show_new_invoice_dialog)
        action_layout.addWidget(self.new_invoice_button)

        self.new_payment_button = QPushButton("Nouveau paiement")
        self.new_payment_button.setIcon(QIcon("app/ui/resources/icons/payment.svg"))
        self.new_payment_button.clicked.connect(self.show_new_payment_dialog)
        action_layout.addWidget(self.new_payment_button)

        left_layout.addLayout(action_layout)

        # Ajouter le panneau de gauche au splitter
        main_splitter.addWidget(left_widget)

        # Panneau de droite (onglets factures et paiements)
        right_widget = QTabWidget()

        # Onglet factures
        invoices_tab = QWidget()
        invoices_layout = QVBoxLayout(invoices_tab)

        # Filtre des factures
        invoice_filter_layout = QHBoxLayout()

        self.invoice_filter = QLineEdit()
        self.invoice_filter.setPlaceholderText("Rechercher une facture...")
        self.invoice_filter.textChanged.connect(self.filter_invoices)
        invoice_filter_layout.addWidget(self.invoice_filter)

        self.invoice_status_filter = QComboBox()
        self.invoice_status_filter.addItem("Tous les statuts", None)
        self.invoice_status_filter.addItem("En attente", "pending")
        self.invoice_status_filter.addItem("Partiellement payée", "partial")
        self.invoice_status_filter.addItem("Payée", "paid")
        self.invoice_status_filter.addItem("Annulée", "cancelled")
        self.invoice_status_filter.addItem("Contestée", "disputed")
        self.invoice_status_filter.currentIndexChanged.connect(self.filter_invoices)
        invoice_filter_layout.addWidget(self.invoice_status_filter)

        invoices_layout.addLayout(invoice_filter_layout)

        # Tableau des factures
        self.invoices_table = QTableView()
        self.invoices_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.doubleClicked.connect(self.on_invoice_double_clicked)

        # Modèle de données pour les factures
        self.invoices_model = SupplierInvoiceTableModel()
        self.invoices_proxy_model = QSortFilterProxyModel()
        self.invoices_proxy_model.setSourceModel(self.invoices_model)
        self.invoices_proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.invoices_table.setModel(self.invoices_proxy_model)

        # Configurer les colonnes
        self.invoices_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        invoices_layout.addWidget(self.invoices_table)

        # Ajouter l'onglet factures
        right_widget.addTab(invoices_tab, "Factures")

        # Onglet paiements
        payments_tab = QWidget()
        payments_layout = QVBoxLayout(payments_tab)

        # Filtre des paiements
        payment_filter_layout = QHBoxLayout()

        self.payment_filter = QLineEdit()
        self.payment_filter.setPlaceholderText("Rechercher un paiement...")
        self.payment_filter.textChanged.connect(self.filter_payments)
        payment_filter_layout.addWidget(self.payment_filter)

        self.payment_method_filter = QComboBox()
        self.payment_method_filter.addItem("Toutes les méthodes", None)
        self.payment_method_filter.addItem("Espèces", "cash")
        self.payment_method_filter.addItem("Virement bancaire", "bank_transfer")
        self.payment_method_filter.addItem("Chèque", "check")
        self.payment_method_filter.addItem("Carte de crédit", "credit_card")
        self.payment_method_filter.addItem("Autre", "other")
        self.payment_method_filter.currentIndexChanged.connect(self.filter_payments)
        payment_filter_layout.addWidget(self.payment_method_filter)

        payments_layout.addLayout(payment_filter_layout)

        # Tableau des paiements
        self.payments_table = QTableView()
        self.payments_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.payments_table.setAlternatingRowColors(True)

        # Modèle de données pour les paiements
        self.payments_model = SupplierPaymentTableModel()
        self.payments_proxy_model = QSortFilterProxyModel()
        self.payments_proxy_model.setSourceModel(self.payments_model)
        self.payments_proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.payments_table.setModel(self.payments_proxy_model)

        # Configurer les colonnes
        self.payments_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        payments_layout.addWidget(self.payments_table)

        # Ajouter l'onglet paiements
        right_widget.addTab(payments_tab, "Paiements")

        # Ajouter le panneau de droite au splitter
        main_splitter.addWidget(right_widget)

        # Configurer le splitter
        main_splitter.setStretchFactor(0, 1)
        main_splitter.setStretchFactor(1, 3)

        # Ajouter le splitter au layout principal
        main_layout.addWidget(main_splitter)

    def load_data(self):
        """Charge les données"""
        self.loading_overlay.show()

        # Utiliser QTimer pour exécuter le chargement de manière asynchrone
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data_async de manière asynchrone"""
        try:
            # Essayer d'utiliser le loop existant
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Si le loop tourne déjà, utiliser create_task
                task = asyncio.create_task(self._load_data_async())
                # Connecter le callback pour la fin de la tâche
                task.add_done_callback(self._on_load_complete)
            else:
                # Si pas de loop en cours, en créer un nouveau
                loop.run_until_complete(self._load_data_async())
        except RuntimeError:
            # Fallback: créer un nouveau loop dans un thread séparé
            import threading
            thread = threading.Thread(target=self._load_data_in_thread)
            thread.daemon = True
            thread.start()

    def _on_load_complete(self, task):
        """Callback appelé quand la tâche asynchrone est terminée"""
        try:
            # Récupérer le résultat ou l'exception
            task.result()
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Masquer l'overlay de chargement dans le thread principal
            QTimer.singleShot(0, self.loading_overlay.hide)

    def _load_data_in_thread(self):
        """Charge les données dans un thread séparé"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self._load_data_async())
            finally:
                loop.close()
        except Exception as e:
            print(f"Erreur lors du chargement des données dans le thread: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Masquer l'overlay de chargement dans le thread principal
            QTimer.singleShot(0, self.loading_overlay.hide)

    async def _load_data_async(self):
        """Charge les données de manière asynchrone"""
        try:
            # Charger les fournisseurs
            suppliers = await self.supplier_service.get_all()

            # Remplir le combo des fournisseurs
            self.supplier_combo.clear()
            self.supplier_combo.addItem("Tous les fournisseurs", None)

            for supplier in suppliers:
                if supplier.active:
                    self.supplier_combo.addItem(supplier.name, supplier.id)

            # Charger les factures avec leurs commandes associées
            invoices = await self.service.get_all_with_orders()
            self.invoices_model.set_data(invoices)

            # Charger les paiements
            payments = await self.service.get_all_payments()
            self.payments_model.set_data(payments)

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des données: {str(e)}"
            )
        finally:
            self.loading_overlay.hide()

    def filter_suppliers(self):
        """Filtre la liste des fournisseurs"""
        filter_text = self.supplier_filter.text().lower()

        for i in range(self.supplier_combo.count()):
            item_text = self.supplier_combo.itemText(i).lower()
            self.supplier_combo.setItemVisible(i, filter_text in item_text)

    def filter_invoices(self):
        """Filtre la liste des factures"""
        filter_text = self.invoice_filter.text()
        self.invoices_proxy_model.setFilterFixedString(filter_text)

        # Filtrer par statut
        status = self.invoice_status_filter.currentData()
        if status:
            self.invoices_model.filter_by_status(status)
        else:
            self.invoices_model.clear_status_filter()

    def filter_payments(self):
        """Filtre la liste des paiements"""
        filter_text = self.payment_filter.text()
        self.payments_proxy_model.setFilterFixedString(filter_text)

        # Filtrer par méthode de paiement
        method = self.payment_method_filter.currentData()
        if method:
            self.payments_model.filter_by_method(method)
        else:
            self.payments_model.clear_method_filter()

    def on_supplier_changed(self, index):
        """Gère le changement de fournisseur"""
        supplier_id = self.supplier_combo.currentData()

        # Filtrer les factures et paiements par fournisseur
        self.invoices_model.filter_by_supplier(supplier_id)
        self.payments_model.filter_by_supplier(supplier_id)

        # Mettre à jour le widget de solde
        if supplier_id:
            self.balance_widget.set_supplier(supplier_id)
        else:
            self.balance_widget.clear()

    def on_invoice_double_clicked(self, index):
        """Gère le double-clic sur une facture"""
        # Récupérer l'ID de la facture
        row = self.invoices_proxy_model.mapToSource(index).row()
        invoice_id = self.invoices_model.get_id_at_row(row)

        if invoice_id:
            self.show_edit_invoice_dialog(invoice_id)

    def show_new_invoice_dialog(self):
        """Affiche la boîte de dialogue de création de facture"""
        dialog = SupplierInvoiceDialog(self)
        dialog.invoice_saved.connect(self.on_invoice_saved)

        if dialog.exec():
            self.load_data()

    def show_edit_invoice_dialog(self, invoice_id):
        """Affiche la boîte de dialogue de modification de facture"""
        dialog = SupplierInvoiceDialog(self, invoice_id)
        dialog.invoice_saved.connect(self.on_invoice_saved)

        if dialog.exec():
            self.load_data()

    def show_new_payment_dialog(self):
        """Affiche la boîte de dialogue de création de paiement"""
        # Récupérer le fournisseur sélectionné
        supplier_id = self.supplier_combo.currentData()

        dialog = SupplierPaymentDialog(self, supplier_id)
        dialog.payment_saved.connect(self.on_payment_saved)

        if dialog.exec():
            self.load_data()

    @pyqtSlot(int)
    def on_invoice_saved(self, invoice_id):
        """Gère l'événement de sauvegarde d'une facture"""
        self.load_data()

        # Rafraîchir le widget de solde si un fournisseur est sélectionné
        supplier_id = self.supplier_combo.currentData()
        if supplier_id:
            self.balance_widget.refresh_balance()

    @pyqtSlot(int)
    def on_payment_saved(self, payment_id):
        """Gère l'événement de sauvegarde d'un paiement"""
        self.load_data()

        # Rafraîchir le widget de solde si un fournisseur est sélectionné
        supplier_id = self.supplier_combo.currentData()
        if supplier_id:
            self.balance_widget.refresh_balance()

    def refresh_balance_widget(self):
        """Rafraîchit le widget de solde automatiquement"""
        supplier_id = self.supplier_combo.currentData()
        if supplier_id:
            self.balance_widget.refresh_balance()
            print(f"Widget de solde rafraîchi automatiquement pour le fournisseur {supplier_id}")
