from PyQt6.QtCore import Qt, QAbstractTableModel
from app.core.services.supplier_service import SupplierService

class SupplierTableModel(QAbstractTableModel):
    """Modèle de table pour l'affichage des fournisseurs"""

    def __init__(self):
        super().__init__()
        # Créer une nouvelle session pour le modèle de table
        from app.utils.database import SessionLocal
        self.db = SessionLocal()
        self.service = SupplierService(self.db)  # Utiliser la même session pour le service
        self.suppliers = []
        self.headers = [
            "ID",
            "Nom",
            "Contact",
            "Téléphone",
            "Email",
            "Adresse",
            "Commune",
            "Ville",
            "Note"
        ]

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("SupplierTableModel: Session de base de données fermée")

    async def load_data(self):
        """Charge les données depuis le service"""
        self.beginResetModel()
        try:
            # Fermer l'ancienne session si elle existe
            if hasattr(self, 'db') and self.db:
                self.db.close()
                print("SupplierTableModel: Ancienne session fermée")

            # Créer une nouvelle session
            from app.utils.database import SessionLocal
            self.db = SessionLocal()
            self.service = SupplierService(self.db)

            # Charger les données avec la nouvelle session
            self.suppliers = await self.service.get_active_suppliers()
            print(f"Fournisseurs chargés: {len(self.suppliers)}")
        except Exception as e:
            print(f"Erreur lors du chargement des fournisseurs: {e}")
            self.suppliers = []
        self.endResetModel()

    def rowCount(self, parent=None):
        return len(self.suppliers)

    def columnCount(self, parent=None):
        return len(self.headers)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return None

        if role == Qt.ItemDataRole.DisplayRole:
            supplier = self.suppliers[index.row()]
            column = index.column()

            # Formatage des données selon la colonne
            if column == 0:
                return str(supplier.id)
            elif column == 1:
                return supplier.name
            elif column == 2:
                return supplier.contact_person or ""
            elif column == 3:
                return supplier.phone or ""
            elif column == 4:
                return supplier.email
            elif column == 5:
                return supplier.address
            elif column == 6:
                return supplier.commune or ""
            elif column == 7:
                return supplier.city or ""
            elif column == 8:
                return supplier.rating.value

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            # Alignement des colonnes
            if index.column() == 0:  # ID
                return Qt.AlignmentFlag.AlignCenter
            elif index.column() == 3:  # Téléphone
                return Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter
            elif index.column() == 8:  # Note
                return Qt.AlignmentFlag.AlignCenter

        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            return self.headers[section]
        return None

    def get_supplier_id(self, row: int) -> int:
        """Retourne l'ID du fournisseur à la ligne spécifiée"""
        return self.suppliers[row].id
