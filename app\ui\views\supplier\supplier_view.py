from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView,
    QPushButton, QLineEdit, QComboBox, QLabel,
    QFrame, QMessageBox, QDialog, QDialogButtonBox,
    QTabWidget, QMenu, QHeaderView
)
from PyQt6.QtCore import Qt, QSortFilterProxyModel, QTimer
from PyQt6.QtGui import QIcon
import asyncio

from .supplier_table_model import SupplierTableModel
from .dialogs.supplier_dialog import SupplierDialog
from ...components.custom_widgets import SearchBar, LoadingOverlay
from app.core.models.supplier import SupplierRating
from app.core.services.supplier_finance_service import SupplierFinanceService
from app.utils.database import SessionLocal

class SupplierView(QWidget):
    """Vue principale pour la gestion des fournisseurs"""

    def __init__(self):
        super().__init__()

        # Initialiser les services
        self.db = SessionLocal()
        self.finance_service = SupplierFinanceService(self.db)

        self.setup_ui()
        self.setup_connections()
        # Utiliser QTimer pour planifier correctement le chargement asynchrone
        QTimer.singleShot(0, self._init_data)

    def _init_data(self):
        """Initialise le chargement des données"""
        print("Initialisation des données fournisseurs...")
        # Utiliser notre wrapper pour exécuter load_data de manière asynchrone
        self._load_data_wrapper()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)



        # Barre d'outils (alignée sur Clients)
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(18)

        button_style = (
            "QPushButton { background: #f5f7fa; color: #1976D2; border: 1px solid #e0e0e0; border-radius: 6px; padding: 8px 18px; font-weight: 600; } "
            "QPushButton:hover { background: #e3f0fc; color: #1565c0; border: 1px solid #90caf9; } "
            "QPushButton:pressed { background: #bbdefb; color: #0d47a1; } "
            "QPushButton:disabled { background: #f5f5f5; color: #bdbdbd; border: 1px solid #eeeeee; } "
        )

        # Bouton d'ajout
        self.add_button = QPushButton("Nouveau Fournisseur")
        self.add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.add_button)

        # Bouton de modification
        self.edit_button = QPushButton("Modifier")
        self.edit_button.setIcon(QIcon("app/ui/resources/icons/edit.svg"))
        self.edit_button.setEnabled(False)  # Désactivé par défaut
        self.edit_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.edit_button)

        # Bouton de suppression
        self.delete_button = QPushButton("Supprimer")
        self.delete_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
        self.delete_button.setEnabled(False)  # Désactivé par défaut
        self.delete_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.delete_button)





        # Bouton d'export
        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))
        self.export_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.export_button)

        # Espacement
        toolbar_layout.addStretch(1)

        main_layout.addLayout(toolbar_layout)

        # Filtres (style aligné Clients)
        filters_layout = QHBoxLayout()

        # Barre de recherche
        self.search_bar = SearchBar()
        self.search_bar.setPlaceholderText("Rechercher un fournisseur...")
        filters_layout.addWidget(self.search_bar)

        # Combobox des notes de fournisseurs à côté de la barre de recherche
        combo_style = (
            "QComboBox { background: #f5f7fa; border: 1.5px solid #e0e0e0; border-radius: 16px; padding: 6px 24px 6px 14px; font-size: 15px; color: #1976D2; min-width: 160px; } "
            "QComboBox:focus { border: 1.5px solid #1976D2; background: #fff; } "
            "QComboBox::drop-down { border: none; } "
            "QComboBox QAbstractItemView { background: #fff; border-radius: 8px; } "
        )
        self.rating_filter = QComboBox()
        self.rating_filter.setStyleSheet(combo_style)
        self.rating_filter.setFixedHeight(36)
        self.rating_filter.addItem("Toutes les notes", None)
        for rating in SupplierRating:
            self.rating_filter.addItem(f"{rating.value} - {self._get_rating_description(rating)}", rating)
        filters_layout.addWidget(QLabel("Note:"))
        filters_layout.addWidget(self.rating_filter)

        # Espacement
        filters_layout.addStretch()

        main_layout.addLayout(filters_layout)

        # Contenu principal
        content_layout = QVBoxLayout()

        # Onglets de détails (similaires à Clients)
        from app.ui.views.supplier.widgets.supplier_balance_widget import SupplierBalanceWidget
        from app.ui.views.supplier.widgets.supplier_info_widget import SupplierInfoWidget
        from app.ui.views.supplier.widgets.supplier_payments_widget import SupplierPaymentsWidget
        from app.ui.views.supplier.widgets.supplier_history_widget import SupplierHistoryWidget
        self.details_tabs = QTabWidget()
        self.details_tabs.setObjectName("supplierDetailsTabs")
        try:
            self.details_tabs.setStyleSheet("QTabWidget::pane { background: #FFFFFF; } QStackedWidget, QStackedWidget > QWidget { background: #FFFFFF; color: #212121; }")
        except Exception:
            pass
        self.info_tab = SupplierInfoWidget()
        self.finances_tab = SupplierBalanceWidget()
        self.payments_tab = SupplierPaymentsWidget()
        self.history_tab = SupplierHistoryWidget()
        self.details_tabs.addTab(self.info_tab, "Informations")
        self.details_tabs.addTab(self.finances_tab, "Finances")
        self.details_tabs.addTab(self.payments_tab, "Paiements")
        self.details_tabs.addTab(self.history_tab, "Historique")
        content_layout.addWidget(self.details_tabs)

        # Tableau des fournisseurs
        self.table_view = QTableView()
        self.table_view.setObjectName("supplierTable")
        self.table_model = SupplierTableModel()
        from ...components.custom_filter_proxy_model import CustomFilterProxyModel
        self.proxy_model = CustomFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.table_view.setModel(self.proxy_model)

        # Configuration du tableau
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        # Auto-ajustement des colonnes pour afficher tout le texte
        header = self.table_view.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setStretchLastSection(True)

        # Style modernisé (aligné sur Clients/Réparation)
        self.table_view.setStyleSheet(
            """
            QTableView {
                background: #fff;
                border-radius: 12px;
                border: 1.5px solid #e0e0e0;
                font-size: 15px;
                color: #222;
                selection-background-color: #1976D2;
                selection-color: #fff;
                alternate-background-color: #f5f7fa;
                gridline-color: #e0e0e0;
                qproperty-alignment: 'AlignCenter';
            }
            QHeaderView::section {
                background: #f5f7fa;
                color: #1976D2;
                font-weight: bold;
                font-size: 15px;
                border: none;
                border-bottom: 2px solid #e0e0e0;
                border-radius: 8px 8px 0 0;
                padding: 8px 0;
                qproperty-alignment: 'AlignCenter';
                text-align: center;
            }
            QTableView::item { text-align: center; }
            QTableView::item:selected { background-color: #1976D2; color: #fff; font-weight: bold; }
            QTableView::item:hover { background-color: #e3f0fc; }
            """
        )

        # Activer les lignes alternées pour une meilleure lisibilité
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setMinimumHeight(180)
        self.table_view.setMaximumHeight(350)

        content_layout.addWidget(self.table_view)

        main_layout.addLayout(content_layout)

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()

    def _get_rating_description(self, rating):
        """Retourne la description d'une note"""
        descriptions = {
            SupplierRating.EXCELLENT: "Excellent",
            SupplierRating.GOOD: "Bon",
            SupplierRating.AVERAGE: "Moyen",
            SupplierRating.FAIR: "Médiocre",
            SupplierRating.POOR: "Mauvais"
        }
        return descriptions.get(rating, "")

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.add_button.clicked.connect(self.show_add_dialog)
        self.edit_button.clicked.connect(self.edit_selected_supplier)
        self.delete_button.clicked.connect(self.delete_supplier)
        self.export_button.clicked.connect(self.export_suppliers)

        self.search_bar.textChanged.connect(self.filter_suppliers)
        self.rating_filter.currentIndexChanged.connect(self.filter_suppliers)

        self.table_view.doubleClicked.connect(self.show_edit_dialog)
        self.table_view.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table_view.customContextMenuRequested.connect(self.show_context_menu)
        # Mettre à jour le panneau de détails selon la sélection
        self.table_view.selectionModel().selectionChanged.connect(self.update_details_panel)

    async def load_data(self):
        """Charge les données des fournisseurs"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau
            print("SupplierView: Chargement des données...")
            await self.table_model.load_data()
            print(f"SupplierView: {self.table_model.rowCount()} fournisseurs chargés")
        except Exception as e:
            print(f"SupplierView: Erreur lors du chargement des données: {e}")
        finally:
            # Auto-sélectionner la première ligne si disponible pour afficher les détails
            try:
                if self.proxy_model.rowCount() > 0:
                    self.table_view.selectRow(0)
            except Exception as sel_err:
                print(f"SupplierView: Erreur lors de la sélection par défaut: {sel_err}")
            # Après chargement, forcer le recalcul des largeurs de colonnes
            try:
                self.table_view.resizeColumnsToContents()
            except Exception:
                pass
            self.loading_overlay.hide()

    def filter_suppliers(self):
        """Applique les filtres sur le tableau"""
        search_text = self.search_bar.text()
        rating = self.rating_filter.currentData()

        # Utiliser le proxy de filtre personnalisé pour unifier la logique
        filters = {}
        if search_text:
            filters['search'] = search_text
        if rating is not None:
            filters['supplier_rating'] = rating
        self.proxy_model.set_filters(filters)

    def show_add_dialog(self):
        """Affiche la boîte de dialogue d'ajout de fournisseur"""
        dialog = SupplierDialog(self)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

    def show_edit_dialog(self, index):
        """Affiche la boîte de dialogue d'édition de fournisseur"""
        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(index)
        supplier_id = self.table_model.get_supplier_id(source_index.row())

        dialog = SupplierDialog(self, supplier_id=supplier_id)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_data())
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def export_suppliers(self):
        """Exporte les données des fournisseurs"""
        # TODO: Implémenter l'export des données
        QMessageBox.information(self, "Export", "Fonctionnalité d'export non implémentée.")
        pass

    def on_selection_changed(self, selected, deselected):
        """Gère le changement de sélection dans le tableau"""
        # Activer/désactiver les boutons en fonction de la sélection
        has_selection = len(self.table_view.selectionModel().selectedRows()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)

    def update_details_panel(self, selected, deselected):
        """Met à jour les onglets de détails en fonction du fournisseur sélectionné"""
        supplier_id = self.get_selected_supplier_id()
        if not supplier_id:
            # Aucun fournisseur sélectionné, remettre à zéro
            for w in (getattr(self, 'finances_tab', None), getattr(self, 'info_tab', None), getattr(self, 'payments_tab', None), getattr(self, 'history_tab', None)):
                if hasattr(w, 'clear'):
                    w.clear()
            return

        # Propager l'ID au widgets si méthode disponible
        if hasattr(self.info_tab, 'set_supplier_id'):
            self.info_tab.set_supplier_id(supplier_id)
        if hasattr(self.finances_tab, 'set_supplier_id'):
            self.finances_tab.set_supplier_id(supplier_id)
        if hasattr(self.payments_tab, 'set_supplier_id'):
            self.payments_tab.set_supplier_id(supplier_id)
        if hasattr(self.history_tab, 'set_supplier_id'):
            self.history_tab.set_supplier_id(supplier_id)

    def get_selected_supplier_id(self):
        """Récupère l'ID du fournisseur sélectionné"""
        selected_rows = self.table_view.selectionModel().selectedRows()
        if not selected_rows:
            return None

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(selected_rows[0])
        return self.table_model.get_supplier_id(source_index.row())

    def edit_selected_supplier(self):
        """Édite le fournisseur sélectionné"""
        supplier_id = self.get_selected_supplier_id()
        if not supplier_id:
            return

        dialog = SupplierDialog(self, supplier_id=supplier_id)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

            # Afficher un message de succès
            QMessageBox.information(self, "Succès", "Le fournisseur a été modifié avec succès.")

    def delete_supplier(self):
        """Supprime le fournisseur sélectionné"""
        supplier_id = self.get_selected_supplier_id()
        if not supplier_id:
            return

        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir supprimer ce fournisseur ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Supprimer le fournisseur
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self._delete_supplier_async(supplier_id))
                # Recharger les données
                self._load_data_wrapper()
                # Afficher un message de succès
                QMessageBox.information(self, "Succès", "Le fournisseur a été supprimé avec succès.")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du fournisseur: {str(e)}")
                print(f"Erreur lors de la suppression du fournisseur: {e}")
                import traceback
                traceback.print_exc()
            finally:
                loop.close()

    async def _delete_supplier_async(self, supplier_id):
        """Supprime un fournisseur de manière asynchrone"""
        await self.table_model.service.update(supplier_id, {"active": False})

    def show_financial_dialog(self):
        """Affiche la boîte de dialogue de gestion financière"""
        supplier_id = self.get_selected_supplier_id()
        if not supplier_id:
            return

        # Créer une instance de la vue de gestion financière des fournisseurs
        from app.ui.views.supplier.supplier_finance_view import SupplierFinanceView

        # Créer une fenêtre de dialogue pour contenir la vue
        dialog = QDialog(self)
        dialog.setWindowTitle("Gestion financière du fournisseur")
        dialog.setMinimumSize(800, 600)

        # Créer la vue de gestion financière
        finance_view = SupplierFinanceView(dialog)

        # Définir le fournisseur sélectionné
        # Utiliser QTimer pour s'assurer que la vue est complètement initialisée
        QTimer.singleShot(100, lambda: finance_view.supplier_combo.setCurrentIndex(
            finance_view.supplier_combo.findData(supplier_id)
        ))

        # Ajouter la vue à la boîte de dialogue
        layout = QVBoxLayout(dialog)
        layout.addWidget(finance_view)

        # Ajouter des boutons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Afficher la boîte de dialogue
        dialog.exec()

    def show_context_menu(self, position):
        """Affiche le menu contextuel"""
        # Vérifier si une ligne est sélectionnée
        if not self.table_view.selectionModel().hasSelection():
            return

        # Créer le menu contextuel
        context_menu = QMenu(self)

        # Ajouter les actions
        edit_action = context_menu.addAction(QIcon("app/ui/resources/icons/edit.svg"), "Modifier")
        delete_action = context_menu.addAction(QIcon("app/ui/resources/icons/delete.svg"), "Supprimer")
        finance_action = context_menu.addAction(QIcon("app/ui/resources/icons/finance.svg"), "Finances")

        # Afficher le menu et récupérer l'action sélectionnée
        action = context_menu.exec(self.table_view.mapToGlobal(position))

        # Exécuter l'action sélectionnée
        if action == edit_action:
            self.edit_selected_supplier()
        elif action == delete_action:
            self.delete_supplier()
        elif action == finance_action:
            self.show_financial_dialog()



