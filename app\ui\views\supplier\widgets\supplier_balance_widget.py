"""
Widget pour afficher le solde d'un fournisseur.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QProgressBar, QPushButton
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon
import asyncio

from app.core.services.supplier_finance_service import SupplierFinanceService
from app.core.models.supplier_finance import InvoiceStatus
from app.utils.database import SessionLocal

class SupplierBalanceWidget(QWidget):
    """Widget pour afficher le solde d'un fournisseur"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Service
        self.db = SessionLocal()
        self.service = SupplierFinanceService(self.db)

        # Données
        self.supplier_id = None
        self.balance_data = None

        # Configuration de l'interface
        self.setup_ui()

    # Alias pour compatibilité avec SupplierView.update_details_panel
    def set_supplier_id(self, supplier_id: int):
        """Compatibilité: redirige vers set_supplier."""
        self.set_supplier(supplier_id)

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Cadre principal
        frame = QFrame()
        frame.setFrameShape(QFrame.Shape.StyledPanel)
        frame.setFrameShadow(QFrame.Shadow.Raised)
        frame.setStyleSheet("background-color: #F5F5F5; border-radius: 5px;")

        frame_layout = QVBoxLayout(frame)

        # Titre
        title_layout = QHBoxLayout()

        self.title_label = QLabel("Solde fournisseur")
        self.title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        title_layout.addWidget(self.title_label)

        self.refresh_button = QPushButton()
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.setToolTip("Rafraîchir")
        self.refresh_button.setFixedSize(24, 24)
        self.refresh_button.clicked.connect(self.refresh_balance)
        title_layout.addWidget(self.refresh_button)

        frame_layout.addLayout(title_layout)

        # Informations de solde
        self.supplier_name_label = QLabel("Sélectionnez un fournisseur")
        self.supplier_name_label.setStyleSheet("font-size: 12px;")
        frame_layout.addWidget(self.supplier_name_label)

        # Montants
        amounts_layout = QHBoxLayout()

        # Montant dû
        due_layout = QVBoxLayout()
        due_title = QLabel("Montant dû")
        due_title.setStyleSheet("font-size: 11px; color: #666;")
        due_layout.addWidget(due_title)

        self.due_amount_label = QLabel("0.00 DA")
        self.due_amount_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #F44336;")
        due_layout.addWidget(self.due_amount_label)

        amounts_layout.addLayout(due_layout)

        # Montant payé
        paid_layout = QVBoxLayout()
        paid_title = QLabel("Montant payé")
        paid_title.setStyleSheet("font-size: 11px; color: #666;")
        paid_layout.addWidget(paid_title)

        self.paid_amount_label = QLabel("0.00 DA")
        self.paid_amount_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #4CAF50;")
        paid_layout.addWidget(self.paid_amount_label)

        amounts_layout.addLayout(paid_layout)

        frame_layout.addLayout(amounts_layout)

        # Barre de progression
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p% payé")
        frame_layout.addWidget(self.progress_bar)

        # Factures en attente
        self.pending_invoices_label = QLabel("0 facture(s) en attente")
        self.pending_invoices_label.setStyleSheet("font-size: 11px; color: #666;")
        frame_layout.addWidget(self.pending_invoices_label)

        main_layout.addWidget(frame)

    def set_supplier(self, supplier_id):
        """Définit le fournisseur et charge son solde"""
        self.supplier_id = supplier_id
        self.refresh_balance()

    def clear(self):
        """Efface les données de solde"""
        self.supplier_id = None
        self.balance_data = None

        # Réinitialiser l'interface
        self.supplier_name_label.setText("Sélectionnez un fournisseur")
        self.due_amount_label.setText("0.00 DA")
        self.paid_amount_label.setText("0.00 DA")
        self.progress_bar.setValue(0)
        self.pending_invoices_label.setText("0 facture(s) en attente")

    def refresh_balance(self):
        """Rafraîchit les données de solde"""
        if not self.supplier_id:
            return

        # Utiliser QTimer pour exécuter le chargement de manière asynchrone
        QTimer.singleShot(0, self._refresh_balance_wrapper)

    def _refresh_balance_wrapper(self):
        """Wrapper pour exécuter refresh_balance_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._refresh_balance_async())
        finally:
            loop.close()

    async def _refresh_balance_async(self):
        """Rafraîchit les données de solde de manière asynchrone"""
        try:
            # Récupérer le solde du fournisseur
            self.balance_data = await self.service.get_supplier_balance(self.supplier_id)

            # Mettre à jour l'interface
            self.update_ui()

        except Exception as e:
            print(f"Erreur lors du rafraîchissement du solde: {str(e)}")

    def update_ui(self):
        """Met à jour l'interface avec les données de solde"""
        if not self.balance_data:
            return

        # Nom du fournisseur
        self.supplier_name_label.setText(self.balance_data["supplier_name"])

        # Montants
        self.due_amount_label.setText(f"{self.balance_data['total_due']:.2f} DA")
        self.paid_amount_label.setText(f"{self.balance_data['total_paid']:.2f} DA")

        # Barre de progression
        if self.balance_data["total_due"] > 0:
            progress = (self.balance_data["total_paid"] / self.balance_data["total_due"]) * 100
            self.progress_bar.setValue(int(min(progress, 100)))
        else:
            self.progress_bar.setValue(100)

        # Factures en attente
        pending_invoices = [
            invoice for invoice in self.balance_data["invoices"]
            if invoice.status in [InvoiceStatus.PENDING, InvoiceStatus.PARTIAL]
        ]
        self.pending_invoices_label.setText(f"{len(pending_invoices)} facture(s) en attente")
