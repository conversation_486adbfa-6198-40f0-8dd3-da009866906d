from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QTableView, QHBoxLayout,
    QPushButton, QHeaderView, QMessageBox, QComboBox
)
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from app.core.services.supplier_finance_service import SupplierFinanceService
from app.core.services.purchasing_service import PurchasingService
from app.core.models.supplier_finance import SupplierInvoice, SupplierPayment
from app.core.models.purchasing import PurchaseOrder
from app.core.enums.purchasing import OrderStatus
from app.core.models.supplier_finance import InvoiceStatus
from app.utils.database import SessionLocal


class SupplierHistoryTableModel(QAbstractTableModel):
    """Modèle de table pour l'historique fournisseur"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.history_items: List[Dict[str, Any]] = []
        self.headers = [
            "Date", "Type", "Référence", "Description", "Montant", "Statut"
        ]

    def rowCount(self, parent=QModelIndex()):
        return len(self.history_items)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            return self.headers[section]
        return QVariant()

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.history_items):
            return QVariant()

        item = self.history_items[index.row()]
        column = index.column()

        if role == Qt.ItemDataRole.DisplayRole:
            if column == 0:  # Date
                return item.get("date", "N/A")
            elif column == 1:  # Type
                return item.get("type", "N/A")
            elif column == 2:  # Référence
                return item.get("reference", "N/A")
            elif column == 3:  # Description
                return item.get("description", "N/A")
            elif column == 4:  # Montant
                amount = item.get("amount")
                return f"{amount:.2f} DA" if amount is not None else "N/A"
            elif column == 5:  # Statut
                return item.get("status", "N/A")

        elif role == Qt.ItemDataRole.UserRole:
            return item.get("id")

        return QVariant()

    def update_history(self, history_items: List[Dict[str, Any]]):
        """Met à jour la liste de l'historique"""
        self.beginResetModel()
        self.history_items = history_items
        self.endResetModel()


class SupplierHistoryWidget(QWidget):
    """Widget d'historique fournisseur avec vraies données."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._supplier_id = None

        # Services
        self.db = SessionLocal()
        self.finance_service = SupplierFinanceService(self.db)
        self.purchasing_service = PurchasingService(self.db)

        self._setup_ui()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def _setup_ui(self):
        layout = QVBoxLayout(self)

        # En-tête avec filtres
        header_layout = QHBoxLayout()
        self.title_label = QLabel("Historique fournisseur")
        self.title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #1976D2;")
        header_layout.addWidget(self.title_label)

        header_layout.addStretch()

        # Filtre par type
        type_label = QLabel("Type:")
        header_layout.addWidget(type_label)

        self.type_filter = QComboBox()
        self.type_filter.addItem("Tous", "all")
        self.type_filter.addItem("Commandes", "orders")
        self.type_filter.addItem("Factures", "invoices")
        self.type_filter.addItem("Paiements", "payments")
        self.type_filter.currentTextChanged.connect(self.apply_filter)
        header_layout.addWidget(self.type_filter)

        # Bouton de rafraîchissement
        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.clicked.connect(self.refresh_data)
        header_layout.addWidget(self.refresh_button)

        layout.addLayout(header_layout)

        # Tableau de l'historique
        self.table_view = QTableView()
        self.table_model = SupplierHistoryTableModel()
        self.table_view.setModel(self.table_model)

        # Configuration du tableau
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSortingEnabled(True)

        # Ajuster les colonnes
        header = self.table_view.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        layout.addWidget(self.table_view)

        # Label d'information
        self.info_label = QLabel("Aucun fournisseur sélectionné")
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.info_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.info_label)

        # Stocker l'historique complet pour le filtrage
        self.full_history = []

    def _get_order_status_display(self, status):
        """Retourne l'affichage du statut de commande en français"""
        status_display = {
            OrderStatus.DRAFT: "Brouillon",
            OrderStatus.PENDING: "En attente",
            OrderStatus.SUBMITTED: "Soumis",
            OrderStatus.APPROVED: "Approuvé",
            OrderStatus.ORDERED: "Commandé",
            OrderStatus.PARTIALLY_RECEIVED: "Partiellement reçu",
            OrderStatus.COMPLETED: "Terminé",
            OrderStatus.CANCELLED: "Annulé",
        }
        return status_display.get(status, str(status))

    def _get_invoice_status_display(self, status):
        """Retourne l'affichage du statut de facture en français"""
        status_display = {
            InvoiceStatus.PENDING: "En attente",
            InvoiceStatus.PARTIAL: "Partiellement payée",
            InvoiceStatus.PAID: "Payée",
            InvoiceStatus.CANCELLED: "Annulée",
            InvoiceStatus.DISPUTED: "Contestée"
        }
        return status_display.get(status, str(status))

    def _get_payment_method_display(self, payment_method):
        """Retourne l'affichage de la méthode de paiement en français"""
        if not payment_method:
            return "N/A"

        method_display = {
            "cash": "Espèces",
            "card": "Carte bancaire",
            "transfer": "Virement",
            "mobile": "Paiement mobile",
            "credit": "Crédit",
            "mixed": "Mixte",
            "check": "Chèque"
        }

        # Si c'est un enum, utiliser sa valeur
        method_value = payment_method.value if hasattr(payment_method, 'value') else str(payment_method)
        return method_display.get(method_value, method_value)

    def set_supplier_id(self, supplier_id: int):
        self._supplier_id = supplier_id
        if supplier_id:
            self.info_label.setText(f"Chargement de l'historique...")
            self.load_history()
        else:
            self.clear()

    def clear(self):
        self._supplier_id = None
        self.table_model.update_history([])
        self.full_history = []
        self.info_label.setText("Aucun fournisseur sélectionné")

    def refresh_data(self):
        """Rafraîchit les données"""
        if self._supplier_id:
            self.load_history()

    def apply_filter(self):
        """Applique le filtre sélectionné"""
        filter_type = self.type_filter.currentData()

        if filter_type == "all":
            filtered_history = self.full_history
        else:
            filtered_history = [item for item in self.full_history if item.get("category") == filter_type]

        self.table_model.update_history(filtered_history)

        # Mettre à jour le label d'information
        if filtered_history:
            self.info_label.setText(f"{len(filtered_history)} élément(s) d'historique")
        else:
            self.info_label.setText("Aucun élément trouvé pour ce filtre")

    def load_history(self):
        """Charge l'historique du fournisseur"""
        if not self._supplier_id:
            return

        try:
            # Utiliser la boucle existante si elle est en cours, sinon exécuter directement
            try:
                loop = asyncio.get_running_loop()
                loop.create_task(self._load_history_async())
            except RuntimeError:
                asyncio.run(self._load_history_async())
        except Exception as e:
            print(f"Erreur lors du chargement de l'historique: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de l'historique: {str(e)}")

    async def _load_history_async(self):
        """Charge l'historique de manière asynchrone"""
        try:
            history_items = []

            # Récupérer les commandes d'achat
            orders = await self.purchasing_service.get_supplier_orders(self._supplier_id)
            for order in orders:
                history_items.append({
                    "id": f"order_{order.id}",
                    "date": order.order_date.strftime("%d/%m/%Y") if order.order_date else "N/A",
                    "type": "Commande",
                    "reference": order.po_number,
                    "description": f"Commande d'achat - {len(order.items) if hasattr(order, 'items') else 0} article(s)",
                    "amount": float(order.total_amount) if order.total_amount else 0,
                    "status": self._get_order_status_display(order.status) if order.status else "N/A",
                    "category": "orders"
                })

            # Récupérer les factures
            invoices = await self.finance_service.get_supplier_invoices(self._supplier_id)
            for invoice in invoices:
                history_items.append({
                    "id": f"invoice_{invoice.id}",
                    "date": invoice.invoice_date.strftime("%d/%m/%Y") if invoice.invoice_date else "N/A",
                    "type": "Facture",
                    "reference": invoice.invoice_number,
                    "description": f"Facture fournisseur",
                    "amount": float(invoice.total_amount) if invoice.total_amount else 0,
                    "status": self._get_invoice_status_display(invoice.status) if invoice.status else "N/A",
                    "category": "invoices"
                })

            # Récupérer les paiements
            payments = await self.finance_service.get_supplier_payments(self._supplier_id)
            for payment in payments:
                payment_method_display = self._get_payment_method_display(payment.payment_method)
                history_items.append({
                    "id": f"payment_{payment.id}",
                    "date": payment.payment_date.strftime("%d/%m/%Y") if payment.payment_date else "N/A",
                    "type": "Paiement",
                    "reference": payment.reference or f"PAY-{payment.id}",
                    "description": f"Paiement {payment_method_display}",
                    "amount": float(payment.amount) if payment.amount else 0,
                    "status": "Effectué",
                    "category": "payments"
                })

            # Trier par date (plus récent en premier)
            history_items.sort(key=lambda x: datetime.strptime(x["date"], "%d/%m/%Y") if x["date"] != "N/A" else datetime.min, reverse=True)

            # Stocker l'historique complet
            self.full_history = history_items

            # Appliquer le filtre actuel
            self.apply_filter()

        except Exception as e:
            print(f"Erreur lors du chargement de l'historique: {e}")
            self.info_label.setText("Erreur lors du chargement de l'historique")