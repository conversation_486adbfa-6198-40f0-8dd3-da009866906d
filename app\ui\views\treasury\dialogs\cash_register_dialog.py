"""
Boîte de dialogue pour créer/modifier une caisse.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QComboBox, QDoubleSpinBox, QTextEdit, QDialogButtonBox,
    QFormLayout, QCheckBox, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer
import asyncio
from decimal import Decimal, InvalidOperation, ROUND_HALF_UP

from app.core.models.treasury import CashRegister, CashRegisterType
from app.core.services.treasury_service import TreasuryService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from app.ui.components.decimal_spinbox import DecimalSpinBox

class CashRegisterDialog(QDialog):
    """Boîte de dialogue pour créer/modifier une caisse"""
    
    def __init__(self, parent=None, register_id=None):
        super().__init__(parent)
        
        # Services
        self.db = SessionLocal()
        self.service = TreasuryService(self.db)
        
        # Données
        self.register_id = register_id
        self.register = None
        
        # Configuration de la fenêtre
        self.setWindowTitle("Caisse" if register_id else "Nouvelle caisse")
        self.setMinimumWidth(400)
        
        # Initialisation de l'interface
        self.setup_ui()
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        
        # Charger les données si on modifie une caisse existante
        if register_id:
            self.load_register_data()
    
    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("CashRegisterDialog: Session de base de données fermée")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Formulaire
        form_layout = QFormLayout()
        
        # Nom
        self.name_edit = QLineEdit()
        form_layout.addRow("Nom:", self.name_edit)
        
        # Type
        self.type_combo = QComboBox()
        for type_value in CashRegisterType:
            self.type_combo.addItem(self._get_type_display(type_value), type_value)
        form_layout.addRow("Type:", self.type_combo)
        
        # Solde initial
        self.initial_balance_spin = DecimalSpinBox()
        self.initial_balance_spin.setRange(Decimal("0.00"), Decimal("1000000.00"))
        self.initial_balance_spin.setDecimals(2)
        self.initial_balance_spin.setSuffix(" DA")
        form_layout.addRow("Solde initial:", self.initial_balance_spin)
        
        # Actif
        self.is_active_check = QCheckBox("Caisse active")
        self.is_active_check.setChecked(True)
        form_layout.addRow("", self.is_active_check)
        
        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes...")
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)
        
        main_layout.addLayout(form_layout)
        
        # Boutons
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)
    
    def load_register_data(self):
        """Charge les données de la caisse"""
        self.loading_overlay.show()
        QTimer.singleShot(0, self._load_register_data_wrapper)
    
    def _load_register_data_wrapper(self):
        """Wrapper pour charger les données de la caisse"""
        try:
            # Récupérer la caisse
            self.register = self.db.query(CashRegister).get(self.register_id)
            
            if not self.register:
                QMessageBox.critical(self, "Erreur", f"Caisse avec ID {self.register_id} non trouvée")
                self.reject()
                return
            
            # Remplir les champs
            self.name_edit.setText(self.register.name)
            
            # Sélectionner le type
            index = self.type_combo.findData(self.register.type)
            if index >= 0:
                self.type_combo.setCurrentIndex(index)
            
            # Solde initial
            self.initial_balance_spin.setValue(Decimal(str(self.register.initial_balance)))
            
            # Actif
            self.is_active_check.setChecked(self.register.is_active)
            
            # Notes
            if self.register.notes:
                self.notes_edit.setText(self.register.notes)
            
            # Désactiver certains champs en mode édition
            self.type_combo.setEnabled(False)
            self.initial_balance_spin.setEnabled(False)
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
        finally:
            self.loading_overlay.hide()
    
    def accept(self):
        """Gère l'acceptation du dialogue"""
        # Vérifier les données
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "Avertissement", "Le nom de la caisse est obligatoire.")
            return
        
        # Récupérer les données
        register_type = self.type_combo.currentData()
        initial_balance = self.initial_balance_spin.value()
        is_active = self.is_active_check.isChecked()
        notes = self.notes_edit.toPlainText().strip()
        
        # Préparer les données
        data = {
            "name": name,
            "type": register_type,
            "initial_balance": initial_balance,
            "is_active": is_active,
            "notes": notes
        }
        
        # Enregistrer les données
        self.loading_overlay.show()
        QTimer.singleShot(0, lambda: self._save_register_wrapper(data))
    
    def _save_register_wrapper(self, data):
        """Wrapper pour enregistrer les données de la caisse"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            if self.register_id:
                # Mettre à jour la caisse existante
                self.register.name = data["name"]
                self.register.is_active = data["is_active"]
                self.register.notes = data["notes"]
                
                self.db.commit()
                QMessageBox.information(self, "Succès", "Caisse mise à jour avec succès.")
            else:
                # Créer une nouvelle caisse
                register = loop.run_until_complete(self.service.create_cash_register(data))
                QMessageBox.information(self, "Succès", "Caisse créée avec succès.")
            
            loop.close()
            super().accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
        finally:
            self.loading_overlay.hide()
    
    def _get_type_display(self, register_type: CashRegisterType) -> str:
        """Retourne le nom d'affichage du type de caisse"""
        types = {
            CashRegisterType.MAIN: "Caisse principale",
            CashRegisterType.REPAIR: "Caisse réparations",
            CashRegisterType.SALES: "Caisse ventes",
            CashRegisterType.PURCHASE: "Caisse achats",
            CashRegisterType.EXPENSE: "Caisse dépenses"
        }
        return types.get(register_type, "Caisse")
