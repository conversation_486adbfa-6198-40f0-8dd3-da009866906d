"""
Boîte de dialogue pour réconcilier une caisse.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QDoubleSpinBox, QTextEdit, QDialogButtonBox,
    QFormLayout, QMessageBox, QFrame
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont
import asyncio

from app.core.models.treasury import CashRegister
from app.core.services.treasury_service import TreasuryService
from app.core.services.user_service import UserService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay

class ReconciliationDialog(QDialog):
    """Boîte de dialogue pour réconcilier une caisse"""
    
    def __init__(self, parent=None, register_id=None):
        super().__init__(parent)
        
        # Services
        self.db = SessionLocal()
        self.service = TreasuryService(self.db)
        self.user_service = UserService(self.db)
        
        # Données
        self.register_id = register_id
        self.register = None
        
        if not register_id:
            QMessageBox.critical(self, "Erreur", "ID de caisse non spécifié")
            self.reject()
            return
        
        # Configuration de la fenêtre
        self.setWindowTitle("Réconciliation de caisse")
        self.setMinimumWidth(400)
        
        # Initialisation de l'interface
        self.setup_ui()
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        
        # Charger les données de la caisse
        self.load_register_data()
    
    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("ReconciliationDialog: Session de base de données fermée")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Informations sur la caisse
        self.info_frame = QFrame()
        self.info_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.info_frame.setFrameShadow(QFrame.Shadow.Raised)
        info_layout = QVBoxLayout(self.info_frame)
        
        self.name_label = QLabel()
        name_font = QFont()
        name_font.setBold(True)
        name_font.setPointSize(12)
        self.name_label.setFont(name_font)
        info_layout.addWidget(self.name_label)
        
        self.balance_label = QLabel()
        balance_font = QFont()
        balance_font.setBold(True)
        balance_font.setPointSize(14)
        self.balance_label.setFont(balance_font)
        info_layout.addWidget(self.balance_label)
        
        main_layout.addWidget(self.info_frame)
        
        # Formulaire
        form_layout = QFormLayout()
        
        # Montant compté
        self.counted_amount_spin = QDoubleSpinBox()
        self.counted_amount_spin.setRange(0, 1000000)
        self.counted_amount_spin.setDecimals(2)
        self.counted_amount_spin.setSuffix(" DA")
        self.counted_amount_spin.valueChanged.connect(self.update_difference)
        form_layout.addRow("Montant compté:", self.counted_amount_spin)
        
        # Différence
        self.difference_label = QLabel("0.00 DA")
        difference_font = QFont()
        difference_font.setBold(True)
        self.difference_label.setFont(difference_font)
        form_layout.addRow("Différence:", self.difference_label)
        
        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes...")
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)
        
        main_layout.addLayout(form_layout)
        
        # Boutons
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)
    
    def load_register_data(self):
        """Charge les données de la caisse"""
        self.loading_overlay.show()
        QTimer.singleShot(0, self._load_register_data_wrapper)
    
    def _load_register_data_wrapper(self):
        """Wrapper pour charger les données de la caisse"""
        try:
            # Récupérer la caisse
            self.register = self.db.query(CashRegister).get(self.register_id)
            
            if not self.register:
                QMessageBox.critical(self, "Erreur", f"Caisse avec ID {self.register_id} non trouvée")
                self.reject()
                return
            
            # Mettre à jour l'interface
            self.name_label.setText(f"Caisse: {self.register.name}")
            self.balance_label.setText(f"Solde actuel: {float(self.register.current_balance):.2f} DA")

            # Initialiser le montant compté avec le solde actuel
            self.counted_amount_spin.setValue(float(self.register.current_balance))
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
        finally:
            self.loading_overlay.hide()
    
    def update_difference(self):
        """Met à jour l'affichage de la différence"""
        if not self.register:
            return

        from decimal import Decimal

        counted = Decimal(str(self.counted_amount_spin.value()))
        current_balance = Decimal(str(self.register.current_balance))
        difference = counted - current_balance
        
        self.difference_label.setText(f"{float(difference):.2f} DA")
        
        # Colorer en fonction de la différence
        if difference < 0:
            self.difference_label.setStyleSheet("color: red;")
        elif difference > 0:
            self.difference_label.setStyleSheet("color: green;")
        else:
            self.difference_label.setStyleSheet("")
    
    def accept(self):
        """Gère l'acceptation du dialogue"""
        if not self.register:
            QMessageBox.critical(self, "Erreur", "Caisse non chargée")
            return
        
        from decimal import Decimal

        counted_amount = Decimal(str(self.counted_amount_spin.value()))
        notes = self.notes_edit.toPlainText().strip()
        
        # Récupérer l'utilisateur courant
        current_user = self.user_service.get_current_user()
        if not current_user:
            QMessageBox.critical(self, "Erreur", "Utilisateur non connecté")
            return
        
        # Effectuer la réconciliation
        self.loading_overlay.show()
        QTimer.singleShot(0, lambda: self._reconcile_wrapper(self.register_id, counted_amount, current_user.id, notes))
    
    def _reconcile_wrapper(self, register_id, counted_amount, user_id, notes):
        """Wrapper pour effectuer la réconciliation"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Effectuer la réconciliation
            result = loop.run_until_complete(
                self.service.reconcile_cash_register(register_id, counted_amount, user_id, notes)
            )
            
            loop.close()
            
            # Afficher un message différent en fonction de la différence
            difference = result["difference"]
            if difference == 0:
                QMessageBox.information(self, "Succès", "Réconciliation effectuée avec succès. Aucune différence constatée.")
            else:
                QMessageBox.information(
                    self,
                    "Succès",
                    f"Réconciliation effectuée avec succès. Différence de {difference:.2f} DA enregistrée."
                )
            
            super().accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la réconciliation: {str(e)}")
        finally:
            self.loading_overlay.hide()
