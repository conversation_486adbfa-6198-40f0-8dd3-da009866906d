"""
Dialogue pour la génération de rapports de trésorerie.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QComboBox, QDateEdit, QLineEdit, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QFormLayout,
    QGroupBox, QCheckBox, QSpinBox, QProgressBar, QMessageBox,
    QFileDialog, QSplitter, QTextEdit
)
from PyQt6.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import <PERSON><PERSON>ont
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional
import asyncio

from app.core.services.treasury_report_service import TreasuryReportService, ReportFilter, ReportData
from app.core.services.export_service import ExportService
from app.core.models.treasury import CashRegister, TransactionCategory, PaymentMethod
from app.utils.database import SessionLocal
from app.ui.components.decimal_spinbox import DecimalSpinBox
from app.utils.event_bus import event_bus


class ReportGenerationThread(QThread):
    """Thread pour la génération de rapports en arrière-plan"""
    
    report_ready = pyqtSignal(object)  # ReportData
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    
    def __init__(self, report_service: TreasuryReportService, report_type: str, 
                 filters: ReportFilter, page: int = 1, page_size: int = 50):
        super().__init__()
        self.report_service = report_service
        self.report_type = report_type
        self.filters = filters
        self.page = page
        self.page_size = page_size
    
    def run(self):
        """Génère le rapport"""
        try:
            self.progress_updated.emit(25)
            
            if self.report_type == "transactions":
                report_data = self.report_service.get_transactions_report(
                    self.filters, self.page, self.page_size
                )
            elif self.report_type == "expenses":
                report_data = self.report_service.get_expenses_report(
                    self.filters, self.page, self.page_size
                )
            elif self.report_type == "cash_registers":
                report_data = self.report_service.get_cash_registers_report(self.filters)
            elif self.report_type == "daily_closures":
                report_data = self.report_service.get_daily_closures_report(
                    self.filters, self.page, self.page_size
                )
            elif self.report_type == "period_locks":
                report_data = self.report_service.get_period_locks_report(self.filters)
            elif self.report_type == "variance_analysis":
                report_data = self.report_service.get_variance_analysis_report(self.filters)
            else:
                raise ValueError(f"Type de rapport non supporté: {self.report_type}")
            
            self.progress_updated.emit(100)
            self.report_ready.emit(report_data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class ReportsDialog(QDialog):
    """Dialogue pour la génération et l'export de rapports"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setWindowTitle("Rapports de Trésorerie")
        self.setModal(True)
        self.resize(1200, 800)
        
        # Services
        self.db = SessionLocal()
        self.report_service = TreasuryReportService(self.db)
        self.export_service = ExportService()
        
        # État
        self.current_report_data = None
        self.current_page = 1
        self.page_size = 50
        
        # Interface
        self.setup_ui()
        self.load_initial_data()
        
        # Thread de génération
        self.generation_thread = None
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(10)

        # Style pour éviter les titres QGroupBox tronqués et améliorer l'espacement
        self.setStyleSheet(
            """
            QGroupBox {
                margin-top: 16px;
                padding-top: 8px;
                font-weight: 600;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 6px;
                background: transparent;
            }
            """
        )
        
        # Splitter principal
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Panneau de filtres (gauche)
        filters_widget = self.create_filters_panel()
        splitter.addWidget(filters_widget)
        
        # Panneau de résultats (droite)
        results_widget = self.create_results_panel()
        splitter.addWidget(results_widget)
        
        # Définir les proportions
        splitter.setSizes([300, 900])
        
        # Barre de progression
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        self.export_button = QPushButton("Exporter")
        self.export_button.setEnabled(False)
        self.export_button.clicked.connect(self.show_export_options)
        buttons_layout.addWidget(self.export_button)
        
        buttons_layout.addStretch()
        
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.close)
        buttons_layout.addWidget(close_button)
        
        layout.addLayout(buttons_layout)
    
    def create_filters_panel(self) -> QWidget:
        """Crée le panneau de filtres"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # Type de rapport
        report_group = QGroupBox("Type de rapport")
        report_layout = QVBoxLayout(report_group)
        
        self.report_type_combo = QComboBox()
        report_types = [
            ("transactions", "Transactions"),
            ("expenses", "Dépenses"),
            ("cash_registers", "Caisses"),
            ("daily_closures", "Clôtures Journalières"),
            ("period_locks", "Verrouillages de Période"),
            ("variance_analysis", "Analyse des Écarts")
        ]

        for value, text in report_types:
            self.report_type_combo.addItem(text, value)

        self.report_type_combo.currentTextChanged.connect(self.on_report_type_changed)
        report_layout.addWidget(self.report_type_combo)
        
        layout.addWidget(report_group)
        
        # Filtres de date
        date_group = QGroupBox("Période")
        date_layout = QFormLayout(date_group)
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        date_layout.addRow("Du:", self.start_date_edit)
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        date_layout.addRow("Au:", self.end_date_edit)
        
        layout.addWidget(date_group)
        
        # Filtres de caisses
        registers_group = QGroupBox("Caisses")
        registers_layout = QVBoxLayout(registers_group)
        
        self.registers_list = QWidget()
        self.registers_layout = QVBoxLayout(self.registers_list)
        registers_layout.addWidget(self.registers_list)
        
        layout.addWidget(registers_group)
        
        # Filtres de montant
        amount_group = QGroupBox("Montants")
        amount_layout = QFormLayout(amount_group)
        
        self.min_amount_spin = DecimalSpinBox()
        self.min_amount_spin.setRange(Decimal("0.00"), Decimal("999999.99"))
        amount_layout.addRow("Minimum:", self.min_amount_spin)
        
        self.max_amount_spin = DecimalSpinBox()
        self.max_amount_spin.setRange(Decimal("0.00"), Decimal("999999.99"))
        self.max_amount_spin.setValue(Decimal("999999.99"))
        amount_layout.addRow("Maximum:", self.max_amount_spin)
        
        layout.addWidget(amount_group)
        
        # Recherche textuelle
        search_group = QGroupBox("Recherche")
        search_layout = QVBoxLayout(search_group)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Rechercher dans les descriptions...")
        search_layout.addWidget(self.search_edit)
        
        layout.addWidget(search_group)
        
        # Pagination
        pagination_group = QGroupBox("Pagination")
        pagination_layout = QFormLayout(pagination_group)
        
        self.page_size_spin = QSpinBox()
        self.page_size_spin.setRange(10, 1000)
        self.page_size_spin.setValue(50)
        pagination_layout.addRow("Éléments par page:", self.page_size_spin)
        
        layout.addWidget(pagination_group)
        
        # Bouton de génération
        self.generate_button = QPushButton("Générer le rapport")
        self.generate_button.clicked.connect(self.generate_report)
        layout.addWidget(self.generate_button)
        
        layout.addStretch()
        
        return widget
    
    def create_results_panel(self) -> QWidget:
        """Crée le panneau de résultats"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # Onglets pour les résultats
        self.results_tabs = QTabWidget()
        
        # Onglet tableau
        self.table_tab = self.create_table_tab()
        self.results_tabs.addTab(self.table_tab, "Tableau")
        
        # Onglet résumé
        self.summary_tab = self.create_summary_tab()
        self.results_tabs.addTab(self.summary_tab, "Résumé")
        
        layout.addWidget(self.results_tabs)
        
        # Contrôles de pagination
        pagination_layout = QHBoxLayout()
        
        self.prev_button = QPushButton("← Précédent")
        self.prev_button.setEnabled(False)
        self.prev_button.clicked.connect(self.previous_page)
        pagination_layout.addWidget(self.prev_button)
        
        self.page_label = QLabel("Page 1 sur 1")
        pagination_layout.addWidget(self.page_label)
        
        self.next_button = QPushButton("Suivant →")
        self.next_button.setEnabled(False)
        self.next_button.clicked.connect(self.next_page)
        pagination_layout.addWidget(self.next_button)
        
        pagination_layout.addStretch()
        
        self.total_label = QLabel("0 éléments")
        pagination_layout.addWidget(self.total_label)
        
        layout.addLayout(pagination_layout)
        
        return widget
    
    def create_table_tab(self) -> QWidget:
        """Crée l'onglet tableau"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        self.results_table = QTableWidget()
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.results_table)
        
        return widget
    
    def create_summary_tab(self) -> QWidget:
        """Crée l'onglet résumé"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        self.summary_text = QTextEdit()
        self.summary_text.setReadOnly(True)
        layout.addWidget(self.summary_text)
        
        return widget
    
    def load_initial_data(self):
        """Charge les données initiales"""
        try:
            # Charger les caisses
            cash_registers = self.db.query(CashRegister).filter(CashRegister.is_active == True).all()
            
            # Créer les checkboxes pour les caisses
            for register in cash_registers:
                checkbox = QCheckBox(register.name)
                checkbox.setChecked(True)
                checkbox.setProperty("register_id", register.id)
                self.registers_layout.addWidget(checkbox)
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {e}")
    
    def get_current_filters(self) -> ReportFilter:
        """Récupère les filtres actuels"""
        # Dates
        start_date = self.start_date_edit.date().toPyDate()
        end_date = self.end_date_edit.date().toPyDate()
        
        # Caisses sélectionnées
        selected_registers = []
        for i in range(self.registers_layout.count()):
            widget = self.registers_layout.itemAt(i).widget()
            if isinstance(widget, QCheckBox) and widget.isChecked():
                selected_registers.append(widget.property("register_id"))
        
        # Montants
        min_amount = self.min_amount_spin.value() if self.min_amount_spin.value() > 0 else None
        max_amount = self.max_amount_spin.value() if self.max_amount_spin.value() < Decimal("999999.99") else None
        
        # Recherche
        search_text = self.search_edit.text().strip() or None
        
        return ReportFilter(
            start_date=datetime.combine(start_date, datetime.min.time()),
            end_date=datetime.combine(end_date, datetime.max.time()),
            cash_register_ids=selected_registers if selected_registers else None,
            min_amount=min_amount,
            max_amount=max_amount,
            search_text=search_text
        )
    
    def generate_report(self):
        """Génère le rapport"""
        if self.generation_thread and self.generation_thread.isRunning():
            return
        
        # Récupérer les paramètres
        report_type = self.report_type_combo.currentData() or "transactions"
        filters = self.get_current_filters()
        self.page_size = self.page_size_spin.value()
        self.current_page = 1
        
        # Désactiver l'interface
        self.generate_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # Lancer la génération
        self.generation_thread = ReportGenerationThread(
            self.report_service, report_type, filters, self.current_page, self.page_size
        )
        self.generation_thread.report_ready.connect(self.on_report_ready)
        self.generation_thread.error_occurred.connect(self.on_report_error)
        self.generation_thread.progress_updated.connect(self.progress_bar.setValue)
        self.generation_thread.finished.connect(self.on_generation_finished)
        self.generation_thread.start()
    
    def on_report_ready(self, report_data: ReportData):
        """Gère la réception du rapport"""
        self.current_report_data = report_data
        self.display_report(report_data)
        self.export_button.setEnabled(True)
        
        # Notifier via l'event bus
        event_bus.show_success(f"Rapport généré: {len(report_data.data)} éléments")
    
    def on_report_error(self, error_message: str):
        """Gère les erreurs de génération"""
        QMessageBox.critical(self, "Erreur", f"Erreur lors de la génération du rapport:\n{error_message}")
        event_bus.show_error(f"Erreur de génération de rapport: {error_message}")
    
    def on_generation_finished(self):
        """Gère la fin de génération"""
        self.generate_button.setEnabled(True)
        self.progress_bar.setVisible(False)
    
    def display_report(self, report_data: ReportData):
        """Affiche le rapport"""
        # Mettre à jour le tableau
        if report_data.data:
            headers = list(report_data.data[0].keys())
            self.results_table.setColumnCount(len(headers))
            self.results_table.setHorizontalHeaderLabels([h.replace('_', ' ').title() for h in headers])
            self.results_table.setRowCount(len(report_data.data))
            
            for row, data_row in enumerate(report_data.data):
                for col, (key, value) in enumerate(data_row.items()):
                    if isinstance(value, datetime):
                        display_value = value.strftime('%d/%m/%Y %H:%M')
                    elif isinstance(value, (int, float, Decimal)):
                        if 'amount' in key.lower() or 'balance' in key.lower():
                            display_value = f"{value:,.2f} DA"
                        else:
                            display_value = f"{value:,}"
                    else:
                        display_value = str(value) if value is not None else ''
                    
                    item = QTableWidgetItem(display_value)
                    self.results_table.setItem(row, col, item)
        
        # Mettre à jour le résumé
        summary_html = f"<h2>{report_data.title}</h2>"
        summary_html += f"<p><i>{report_data.subtitle}</i></p>"
        summary_html += f"<p>Généré le {report_data.generated_at.strftime('%d/%m/%Y à %H:%M')}</p>"
        
        if report_data.summary:
            summary_html += "<h3>Résumé</h3><ul>"
            for key, value in report_data.summary.items():
                if isinstance(value, (int, float, Decimal)):
                    if 'amount' in key.lower() or 'balance' in key.lower():
                        value_str = f"{value:,.2f} DA"
                    else:
                        value_str = f"{value:,}"
                else:
                    value_str = str(value)
                summary_html += f"<li><b>{key.replace('_', ' ').title()}:</b> {value_str}</li>"
            summary_html += "</ul>"
        
        self.summary_text.setHtml(summary_html)
        
        # Mettre à jour la pagination
        self.update_pagination_controls(report_data.pagination)
    
    def update_pagination_controls(self, pagination):
        """Met à jour les contrôles de pagination"""
        self.page_label.setText(f"Page {pagination.page} sur {pagination.total_pages}")
        self.total_label.setText(f"{pagination.total_items} éléments")
        
        self.prev_button.setEnabled(pagination.has_previous)
        self.next_button.setEnabled(pagination.has_next)
    
    def previous_page(self):
        """Page précédente"""
        if self.current_page > 1:
            self.current_page -= 1
            self.generate_report()
    
    def next_page(self):
        """Page suivante"""
        if self.current_report_data and self.current_report_data.pagination.has_next:
            self.current_page += 1
            self.generate_report()
    
    def on_report_type_changed(self):
        """Gère le changement de type de rapport"""
        # Réinitialiser la page
        self.current_page = 1
        self.current_report_data = None
        self.export_button.setEnabled(False)
    
    def show_export_options(self):
        """Affiche les options d'export"""
        if not self.current_report_data:
            return
        
        # Formats disponibles
        formats = self.export_service.get_available_formats()
        
        # Dialogue de sélection
        format_dialog = QDialog(self)
        format_dialog.setWindowTitle("Exporter le rapport")
        format_dialog.setModal(True)
        
        layout = QVBoxLayout(format_dialog)
        
        layout.addWidget(QLabel("Choisissez le format d'export:"))
        
        format_combo = QComboBox()
        format_combo.addItems(formats)
        layout.addWidget(format_combo)
        
        buttons_layout = QHBoxLayout()
        
        export_btn = QPushButton("Exporter")
        export_btn.clicked.connect(lambda: self.export_report(format_combo.currentText(), format_dialog))
        buttons_layout.addWidget(export_btn)
        
        cancel_btn = QPushButton("Annuler")
        cancel_btn.clicked.connect(format_dialog.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        format_dialog.exec()
    
    def export_report(self, format_type: str, dialog: QDialog):
        """Exporte le rapport"""
        try:
            if format_type == "PDF":
                filepath = self.export_service.export_to_pdf(self.current_report_data)
            elif format_type == "Excel":
                filepath = self.export_service.export_to_excel(self.current_report_data)
            elif format_type == "CSV":
                filepath = self.export_service.export_to_csv(self.current_report_data)
            else:
                raise ValueError(f"Format non supporté: {format_type}")
            
            dialog.accept()
            
            # Demander si ouvrir le fichier
            reply = QMessageBox.question(
                self, "Export terminé",
                f"Rapport exporté vers:\n{filepath}\n\nVoulez-vous ouvrir le fichier?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                import os
                os.startfile(filepath)  # Windows
            
            event_bus.show_success(f"Rapport exporté en {format_type}")
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur d'export", f"Erreur lors de l'export:\n{e}")
            event_bus.show_error(f"Erreur d'export: {e}")
    
    def closeEvent(self, event):
        """Gère la fermeture du dialogue"""
        if self.generation_thread and self.generation_thread.isRunning():
            self.generation_thread.terminate()
            self.generation_thread.wait()
        
        if hasattr(self, 'db') and self.db:
            self.db.close()
        
        super().closeEvent(event)
