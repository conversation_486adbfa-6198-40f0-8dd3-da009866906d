"""
Boîte de dialogue pour ajouter une transaction.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QComboBox, QDoubleSpinBox, QTextEdit, QDialogButtonBox,
    QFormLayout, QDateTimeEdit, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer, QDateTime
import asyncio
from datetime import datetime
from decimal import Decimal, InvalidOperation, ROUND_HALF_UP

from app.core.models.treasury import CashRegister, TransactionCategory, PaymentMethod
from app.core.services.treasury_service import TreasuryService
from app.core.services.user_service import UserService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from app.ui.components.decimal_spinbox import DecimalSpinBox

class TransactionDialog(QDialog):
    """Boîte de dialogue pour ajouter une transaction"""

    def __init__(self, parent=None, register_id=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = TreasuryService(self.db)
        self.user_service = UserService(self.db)

        # Données
        self.register_id = register_id
        self.register = None

        # Configuration de la fenêtre
        self.setWindowTitle("Nouvelle transaction")
        self.setMinimumWidth(400)

        # Initialisation de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données de la caisse
        if register_id:
            self.load_register_data()
        else:
            self.load_registers()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("TransactionDialog: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Formulaire
        form_layout = QFormLayout()

        # Caisse
        self.register_combo = QComboBox()
        form_layout.addRow("Caisse:", self.register_combo)

        # Montant
        self.amount_spin = DecimalSpinBox()
        self.amount_spin.setRange(Decimal("-1000000.00"), Decimal("1000000.00"))
        self.amount_spin.setDecimals(2)
        self.amount_spin.setSuffix(" DA")
        form_layout.addRow("Montant:", self.amount_spin)

        # Date et heure
        self.date_time_edit = QDateTimeEdit(QDateTime.currentDateTime())
        self.date_time_edit.setCalendarPopup(True)
        form_layout.addRow("Date et heure:", self.date_time_edit)

        # Catégorie
        self.category_combo = QComboBox()
        for category in TransactionCategory:
            self.category_combo.addItem(self._get_category_display(category), category)
        form_layout.addRow("Catégorie:", self.category_combo)

        # Méthode de paiement
        self.payment_method_combo = QComboBox()
        for method in PaymentMethod:
            self.payment_method_combo.addItem(self._get_payment_method_display(method), method)
        form_layout.addRow("Méthode de paiement:", self.payment_method_combo)

        # Référence
        self.reference_edit = QLineEdit()
        form_layout.addRow("Référence:", self.reference_edit)

        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("Description...")
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow("Description:", self.description_edit)

        main_layout.addLayout(form_layout)

        # Boutons
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)

    def load_registers(self):
        """Charge la liste des caisses"""
        self.loading_overlay.show()
        QTimer.singleShot(0, self._load_registers_wrapper)

    def _load_registers_wrapper(self):
        """Wrapper pour charger la liste des caisses"""
        try:
            # Récupérer les caisses actives
            registers = self.db.query(CashRegister).filter(CashRegister.is_active == True).all()

            # Remplir le combo
            self.register_combo.clear()
            for register in registers:
                self.register_combo.addItem(register.name, register.id)

            # Sélectionner la caisse spécifiée
            if self.register_id:
                index = self.register_combo.findData(self.register_id)
                if index >= 0:
                    self.register_combo.setCurrentIndex(index)
                    self.register_combo.setEnabled(False)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des caisses: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def load_register_data(self):
        """Charge les données de la caisse"""
        self.loading_overlay.show()
        QTimer.singleShot(0, self._load_register_data_wrapper)

    def _load_register_data_wrapper(self):
        """Wrapper pour charger les données de la caisse"""
        try:
            # Récupérer la caisse
            self.register = self.db.query(CashRegister).get(self.register_id)

            if not self.register:
                QMessageBox.critical(self, "Erreur", f"Caisse avec ID {self.register_id} non trouvée")
                self.reject()
                return

            # Charger la liste des caisses
            self.load_registers()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def accept(self):
        """Gère l'acceptation du dialogue"""
        # Vérifier les données
        register_id = self.register_combo.currentData()
        if not register_id:
            QMessageBox.warning(self, "Avertissement", "Veuillez sélectionner une caisse.")
            return

        amount = self.amount_spin.value()
        if amount == 0:
            QMessageBox.warning(self, "Avertissement", "Le montant ne peut pas être zéro.")
            return

        # Récupérer les données
        transaction_date = self.date_time_edit.dateTime().toPyDateTime()
        category = self.category_combo.currentData()
        payment_method = self.payment_method_combo.currentData()
        reference = self.reference_edit.text().strip()
        description = self.description_edit.toPlainText().strip()

        # Récupérer l'utilisateur courant
        current_user = self.user_service.get_current_user()
        if not current_user:
            QMessageBox.critical(self, "Erreur", "Utilisateur non connecté")
            return

        # Préparer les données
        data = {
            "cash_register_id": register_id,
            "amount": amount,
            "transaction_date": transaction_date,
            "category": category,
            "payment_method": payment_method,
            "reference_number": reference,
            "description": description,
            "user_id": current_user.id
        }

        # Enregistrer les données
        self.loading_overlay.show()
        QTimer.singleShot(0, lambda: self._save_transaction_wrapper(data))

    def _save_transaction_wrapper(self, data):
        """Wrapper pour enregistrer les données de la transaction"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Créer la transaction
            _ = loop.run_until_complete(self.service.add_transaction(data))

            loop.close()
            QMessageBox.information(self, "Succès", "Transaction enregistrée avec succès.")
            super().accept()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def _get_category_display(self, category: TransactionCategory) -> str:
        """Retourne le nom d'affichage de la catégorie"""
        categories = {
            TransactionCategory.SALE: "Vente",
            TransactionCategory.REPAIR: "Réparation",
            TransactionCategory.PURCHASE: "Achat",
            TransactionCategory.EXPENSE: "Dépense",
            TransactionCategory.TRANSFER: "Transfert",
            TransactionCategory.DEPOSIT: "Dépôt",
            TransactionCategory.WITHDRAWAL: "Retrait",
            TransactionCategory.ADJUSTMENT: "Ajustement",
            TransactionCategory.OTHER: "Autre"
        }
        return categories.get(category, "Inconnu")

    def _get_payment_method_display(self, method: PaymentMethod) -> str:
        """Retourne le nom d'affichage de la méthode de paiement"""
        methods = {
            PaymentMethod.cash: "Espèces",
            PaymentMethod.bank_transfer: "Virement",
            PaymentMethod.check: "Chèque",
            PaymentMethod.credit_card: "Carte",
            PaymentMethod.other: "Autre"
        }
        return methods.get(method, "Inconnu")
