"""
Boîte de dialogue pour effectuer un transfert entre caisses.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QComboBox, QDoubleSpinBox, QTextEdit, QDialogButtonBox,
    QFormLayout, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer
import asyncio
from decimal import Decimal, InvalidOperation, ROUND_HALF_UP

from app.core.models.treasury import CashRegister
from app.core.services.treasury_service import TreasuryService
from app.core.services.user_service import UserService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from app.ui.components.decimal_spinbox import DecimalSpinBox

class TransferDialog(QDialog):
    """Boîte de dialogue pour effectuer un transfert entre caisses"""
    
    def __init__(self, parent=None, from_register_id=None):
        super().__init__(parent)
        
        # Services
        self.db = SessionLocal()
        self.service = TreasuryService(self.db)
        self.user_service = UserService(self.db)
        
        # Données
        self.from_register_id = from_register_id
        
        # Configuration de la fenêtre
        self.setWindowTitle("Transfert entre caisses")
        self.setMinimumWidth(400)
        
        # Initialisation de l'interface
        self.setup_ui()
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        
        # Charger les caisses
        self.load_registers()
    
    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("TransferDialog: Session de base de données fermée")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Formulaire
        form_layout = QFormLayout()
        
        # Caisse source
        self.from_register_combo = QComboBox()
        self.from_register_combo.currentIndexChanged.connect(self.update_to_registers)
        form_layout.addRow("De:", self.from_register_combo)
        
        # Caisse destination
        self.to_register_combo = QComboBox()
        form_layout.addRow("Vers:", self.to_register_combo)
        
        # Montant
        self.amount_spin = DecimalSpinBox()
        self.amount_spin.setRange(Decimal("0.01"), Decimal("1000000.00"))
        self.amount_spin.setDecimals(2)
        self.amount_spin.setSuffix(" DA")
        form_layout.addRow("Montant:", self.amount_spin)
        
        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("Description...")
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow("Description:", self.description_edit)
        
        main_layout.addLayout(form_layout)
        
        # Boutons
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)
    
    def load_registers(self):
        """Charge la liste des caisses"""
        self.loading_overlay.show()
        QTimer.singleShot(0, self._load_registers_wrapper)
    
    def _load_registers_wrapper(self):
        """Wrapper pour charger la liste des caisses"""
        try:
            # Récupérer les caisses actives
            registers = self.db.query(CashRegister).filter(CashRegister.is_active == True).all()
            
            # Remplir le combo source
            self.from_register_combo.clear()
            for register in registers:
                self.from_register_combo.addItem(register.name, register.id)
            
            # Sélectionner la caisse source spécifiée
            if self.from_register_id:
                index = self.from_register_combo.findData(self.from_register_id)
                if index >= 0:
                    self.from_register_combo.setCurrentIndex(index)
            
            # Mettre à jour le combo destination
            self.update_to_registers()
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des caisses: {str(e)}")
        finally:
            self.loading_overlay.hide()
    
    def update_to_registers(self):
        """Met à jour la liste des caisses destination en excluant la caisse source"""
        try:
            # Récupérer l'ID de la caisse source
            from_id = self.from_register_combo.currentData()
            if not from_id:
                return
            
            # Récupérer les caisses actives
            registers = self.db.query(CashRegister).filter(
                CashRegister.is_active == True,
                CashRegister.id != from_id
            ).all()
            
            # Remplir le combo destination
            self.to_register_combo.clear()
            for register in registers:
                self.to_register_combo.addItem(register.name, register.id)
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la mise à jour des caisses: {str(e)}")
    
    def accept(self):
        """Gère l'acceptation du dialogue"""
        # Vérifier les données
        from_id = self.from_register_combo.currentData()
        if not from_id:
            QMessageBox.warning(self, "Avertissement", "Veuillez sélectionner une caisse source.")
            return
        
        to_id = self.to_register_combo.currentData()
        if not to_id:
            QMessageBox.warning(self, "Avertissement", "Veuillez sélectionner une caisse destination.")
            return
        
        amount = self.amount_spin.value()
        if amount <= 0:
            QMessageBox.warning(self, "Avertissement", "Le montant doit être supérieur à zéro.")
            return
        
        description = self.description_edit.toPlainText().strip()
        
        # Récupérer l'utilisateur courant
        current_user = self.user_service.get_current_user()
        if not current_user:
            QMessageBox.critical(self, "Erreur", "Utilisateur non connecté")
            return
        
        # Effectuer le transfert
        self.loading_overlay.show()
        QTimer.singleShot(0, lambda: self._transfer_wrapper(from_id, to_id, amount, current_user.id, description))
    
    def _transfer_wrapper(self, from_id, to_id, amount, user_id, description):
        """Wrapper pour effectuer le transfert"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Effectuer le transfert
            result = loop.run_until_complete(
                self.service.transfer_between_registers(from_id, to_id, amount, user_id, description)
            )
            
            loop.close()
            QMessageBox.information(self, "Succès", "Transfert effectué avec succès.")
            super().accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du transfert: {str(e)}")
        finally:
            self.loading_overlay.hide()
