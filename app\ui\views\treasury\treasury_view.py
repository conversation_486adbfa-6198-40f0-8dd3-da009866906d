"""
Vue principale du module de trésorerie.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QPushButton, QTableView, QComboBox, QDateEdit, QLineEdit,
    QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox, QTextEdit,
    QMessageBox, QDialog, QDialogButtonBox, QFileDialog, QFrame,
    QSplitter, QStackedWidget, QGridLayout, QScrollArea, QHeaderView
)
from PyQt6.QtCore import Qt, QDate, QTimer, pyqtSignal, pyqtSlot, QSortFilterProxyModel
from PyQt6.QtGui import QIcon, QFont
import asyncio
from datetime import datetime, timedelta

from app.core.services.treasury_service import TreasuryService
from app.core.models.treasury import CashRegister, CashRegisterType, TransactionCategory, PaymentMethod
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from app.utils.event_bus import event_bus
from app.utils.treasury_updater import set_treasury_view, clear_treasury_view
from app.ui.components.toast_notification import get_toast_manager, ToastType
from .widgets.cash_register_widget import CashRegisterWidget
from .widgets.transaction_table_model import TransactionTableModel
from .widgets.expense_table_model import ExpenseTableModel
from .dialogs.cash_register_dialog import CashRegisterDialog
from .dialogs.transaction_dialog import TransactionDialog
from .dialogs.expense_dialog import ExpenseDialog
from .dialogs.transfer_dialog import TransferDialog
from .dialogs.reconciliation_dialog import ReconciliationDialog
from .dialogs.reports_dialog import ReportsDialog
from .dialogs.daily_closure_dialog import DailyClosureDialog

class TreasuryView(QWidget):
    """Vue principale du module de trésorerie"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = TreasuryService(self.db)

        # Configuration de l'interface
        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Connecter au bus d'événements
        self.connect_event_bus()

        # S'enregistrer dans l'updater de trésorerie
        set_treasury_view(self)

        # Initialiser le gestionnaire de notifications toast
        self.toast_manager = get_toast_manager(self)

        # Charger les données
        self.init_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        # Se déconnecter du bus d'événements
        try:
            event_bus.treasury_updated.disconnect(self.on_treasury_updated)
            event_bus.cash_transaction_added.disconnect(self.on_cash_transaction_added)
        except:
            pass  # Ignorer les erreurs de déconnexion

        # Effacer la référence dans l'updater
        clear_treasury_view()

        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("TreasuryView: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Barre d'outils
        self._create_toolbar(main_layout)

        # Onglets
        self.tab_widget = QTabWidget()

        # Onglet Tableau de bord
        self.dashboard_tab = self._create_dashboard_tab()
        self.tab_widget.addTab(self.dashboard_tab, "Tableau de bord")

        # Onglet Caisses
        self.registers_tab = self._create_registers_tab()
        self.tab_widget.addTab(self.registers_tab, "Caisses")

        # Onglet Transactions
        self.transactions_tab = self._create_transactions_tab()
        self.tab_widget.addTab(self.transactions_tab, "Transactions")

        # Onglet Dépenses
        self.expenses_tab = self._create_expenses_tab()
        self.tab_widget.addTab(self.expenses_tab, "Dépenses")

        main_layout.addWidget(self.tab_widget)

    def _create_toolbar(self, main_layout):
        """Crée la barre d'outils avec tous les boutons d'action"""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 8px;
                margin-bottom: 8px;
            }
        """)

        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(12, 8, 12, 8)
        toolbar_layout.setSpacing(12)

        # Bouton Nouvelle caisse
        self.new_register_button = QPushButton("Nouvelle caisse")
        self.new_register_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.new_register_button.setToolTip("Créer une nouvelle caisse")
        toolbar_layout.addWidget(self.new_register_button)

        # Séparateur
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.Shape.VLine)
        separator1.setFrameShadow(QFrame.Shadow.Sunken)
        toolbar_layout.addWidget(separator1)

        # Bouton Nouvelle transaction
        self.new_transaction_button = QPushButton("Nouvelle transaction")
        self.new_transaction_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.new_transaction_button.setToolTip("Enregistrer une nouvelle transaction")
        toolbar_layout.addWidget(self.new_transaction_button)

        # Bouton Transfert entre caisses
        self.transfer_button = QPushButton("Transfert entre caisses")
        self.transfer_button.setIcon(QIcon("app/ui/resources/icons/transfer.svg"))
        self.transfer_button.setToolTip("Effectuer un transfert entre caisses")
        toolbar_layout.addWidget(self.transfer_button)

        # Bouton Nouvelle dépense
        self.new_expense_button = QPushButton("Nouvelle dépense")
        self.new_expense_button.setIcon(QIcon("app/ui/resources/icons/expense.svg"))
        self.new_expense_button.setToolTip("Enregistrer une nouvelle dépense")
        toolbar_layout.addWidget(self.new_expense_button)

        # Séparateur
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.VLine)
        separator2.setFrameShadow(QFrame.Shadow.Sunken)
        toolbar_layout.addWidget(separator2)

        # Bouton Rafraîchir
        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.setToolTip("Actualiser les données")
        toolbar_layout.addWidget(self.refresh_button)

        # Séparateur
        separator3 = QFrame()
        separator3.setFrameShape(QFrame.Shape.VLine)
        separator3.setFrameShadow(QFrame.Shadow.Sunken)
        toolbar_layout.addWidget(separator3)

        # Bouton Rapports
        self.reports_button = QPushButton("Rapports")
        self.reports_button.setIcon(QIcon("app/ui/resources/icons/report.svg"))
        self.reports_button.setToolTip("Générer des rapports et exports")
        toolbar_layout.addWidget(self.reports_button)

        # Séparateur
        separator4 = QFrame()
        separator4.setFrameShape(QFrame.Shape.VLine)
        separator4.setFrameShadow(QFrame.Shadow.Sunken)
        toolbar_layout.addWidget(separator4)

        # Bouton Clôture Journalière
        self.daily_closure_button = QPushButton("Clôture Journalière")
        self.daily_closure_button.setIcon(QIcon("app/ui/resources/icons/lock.svg"))
        self.daily_closure_button.setToolTip("Gérer les clôtures journalières")
        self.daily_closure_button.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")
        toolbar_layout.addWidget(self.daily_closure_button)

        # Stretch pour pousser les boutons vers la gauche
        toolbar_layout.addStretch()

        main_layout.addWidget(toolbar_frame)

    def _create_dashboard_tab(self):
        """Crée l'onglet Tableau de bord"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Résumé des caisses
        registers_group = QGroupBox("Résumé des caisses")
        registers_layout = QVBoxLayout(registers_group)
        # Add top margin so the title isn't overlapped by content
        registers_layout.setContentsMargins(12, 24, 12, 12)
        registers_layout.setSpacing(8)

        # Solde total
        total_layout = QHBoxLayout()
        total_label = QLabel("Solde total:")
        total_layout.addWidget(total_label)

        self.total_balance_label = QLabel("0.00 DA")
        total_font = self.total_balance_label.font()
        total_font.setBold(True)
        total_font.setPointSize(14)
        self.total_balance_label.setFont(total_font)
        total_layout.addWidget(self.total_balance_label, 1, Qt.AlignmentFlag.AlignRight)

        registers_layout.addLayout(total_layout)

        # Totaux du jour
        today_layout = QHBoxLayout()

        # Entrées du jour
        today_in_layout = QVBoxLayout()
        today_in_title = QLabel("Entrées du jour:")
        today_in_title.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.today_in_label = QLabel("0.00 DA")
        self.today_in_label.setStyleSheet("color: green;")
        today_in_layout.addWidget(today_in_title)
        today_in_layout.addWidget(self.today_in_label)
        today_layout.addLayout(today_in_layout)

        # Sorties du jour
        today_out_layout = QVBoxLayout()
        today_out_title = QLabel("Sorties du jour:")
        today_out_title.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.today_out_label = QLabel("0.00 DA")
        self.today_out_label.setStyleSheet("color: red;")
        today_out_layout.addWidget(today_out_title)
        today_out_layout.addWidget(self.today_out_label)
        today_layout.addLayout(today_out_layout)

        # Net du jour
        today_net_layout = QVBoxLayout()
        today_net_title = QLabel("Net du jour:")
        today_net_title.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.today_net_label = QLabel("0.00 DA")
        self.today_net_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        today_net_layout.addWidget(today_net_title)
        today_net_layout.addWidget(self.today_net_label)
        today_layout.addLayout(today_net_layout)

        today_layout.addStretch()
        registers_layout.addLayout(today_layout)

        # Grille des caisses
        self.registers_scroll = QScrollArea()
        self.registers_scroll.setWidgetResizable(True)
        self.registers_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        self.registers_widget = QWidget()
        self.registers_grid = QGridLayout(self.registers_widget)
        self.registers_grid.setContentsMargins(0, 0, 0, 0)
        self.registers_grid.setSpacing(10)

        self.registers_scroll.setWidget(self.registers_widget)
        registers_layout.addWidget(self.registers_scroll)

        layout.addWidget(registers_group)

        # Transactions récentes
        transactions_group = QGroupBox("Transactions récentes")
        transactions_layout = QVBoxLayout(transactions_group)
        # Add top margin so the title isn't overlapped by the table
        transactions_layout.setContentsMargins(12, 24, 12, 12)
        transactions_layout.setSpacing(8)

        self.recent_transactions_table = QTableView()
        self.recent_transactions_model = TransactionTableModel()
        self.recent_transactions_table.setModel(self.recent_transactions_model)

        # Configuration du tableau pour une meilleure présentation
        header = self.recent_transactions_table.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Ajouter des lignes alternées pour une meilleure lisibilité
        self.recent_transactions_table.setAlternatingRowColors(True)

        # Configurer la sélection par ligne complète
        self.recent_transactions_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)

        # Désactiver l'édition directe dans le tableau
        self.recent_transactions_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.recent_transactions_table.setShowGrid(True)
        self.recent_transactions_table.setSortingEnabled(True)

        transactions_layout.addWidget(self.recent_transactions_table)

        layout.addWidget(transactions_group)

        return tab

    def _create_registers_tab(self):
        """Crée l'onglet Caisses"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        self.new_register_button2 = QPushButton("Nouvelle caisse")
        self.new_register_button2.setIcon(QIcon("app/ui/assets/icons/add.svg"))
        toolbar_layout.addWidget(self.new_register_button2)

        toolbar_layout.addStretch()

        layout.addLayout(toolbar_layout)

        # Grille des caisses
        self.registers_scroll2 = QScrollArea()
        self.registers_scroll2.setWidgetResizable(True)
        self.registers_scroll2.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        self.registers_widget2 = QWidget()
        self.registers_grid2 = QGridLayout(self.registers_widget2)
        self.registers_grid2.setContentsMargins(0, 0, 0, 0)
        self.registers_grid2.setSpacing(10)

        self.registers_scroll2.setWidget(self.registers_widget2)
        layout.addWidget(self.registers_scroll2)

        return tab

    def _create_transactions_tab(self):
        """Crée l'onglet Transactions"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        self.new_transaction_button2 = QPushButton("Nouvelle transaction")
        self.new_transaction_button2.setIcon(QIcon("app/ui/assets/icons/add.svg"))
        toolbar_layout.addWidget(self.new_transaction_button2)

        # Filtres
        toolbar_layout.addStretch()

        filter_label = QLabel("Caisse:")
        toolbar_layout.addWidget(filter_label)

        self.register_filter = QComboBox()
        self.register_filter.addItem("Toutes", None)
        toolbar_layout.addWidget(self.register_filter)

        date_label = QLabel("Date:")
        toolbar_layout.addWidget(date_label)

        self.date_filter = QDateEdit(QDate.currentDate())
        self.date_filter.setCalendarPopup(True)
        toolbar_layout.addWidget(self.date_filter)

        payment_method_label = QLabel("Méthode de paiement:")
        toolbar_layout.addWidget(payment_method_label)

        self.payment_method_filter = QComboBox()
        self.payment_method_filter.addItem("Toutes", None)
        for method in PaymentMethod:
            self.payment_method_filter.addItem(method.value.capitalize(), method)
        toolbar_layout.addWidget(self.payment_method_filter)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Rechercher...")
        toolbar_layout.addWidget(self.search_edit)

        layout.addLayout(toolbar_layout)

        # Tableau des transactions
        self.transactions_table = QTableView()
        self.transactions_model = TransactionTableModel()
        self.transactions_proxy_model = QSortFilterProxyModel()
        self.transactions_proxy_model.setSourceModel(self.transactions_model)
        self.transactions_table.setModel(self.transactions_proxy_model)

        # Configuration du tableau pour une meilleure présentation
        header = self.transactions_table.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Ajouter des lignes alternées pour une meilleure lisibilité
        self.transactions_table.setAlternatingRowColors(True)

        # Configurer la sélection par ligne complète
        self.transactions_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)

        # Désactiver l'édition directe dans le tableau
        self.transactions_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.transactions_table.setShowGrid(True)
        self.transactions_table.setSortingEnabled(True)

        layout.addWidget(self.transactions_table)

        return tab

    def _create_expenses_tab(self):
        """Crée l'onglet Dépenses"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        self.new_expense_button2 = QPushButton("Nouvelle dépense")
        self.new_expense_button2.setIcon(QIcon("app/ui/resources/icons/expense.svg"))
        toolbar_layout.addWidget(self.new_expense_button2)

        # Filtres
        toolbar_layout.addStretch()

        filter_label = QLabel("Caisse:")
        toolbar_layout.addWidget(filter_label)

        self.register_filter2 = QComboBox()
        self.register_filter2.addItem("Toutes", None)
        toolbar_layout.addWidget(self.register_filter2)

        date_label = QLabel("Date:")
        toolbar_layout.addWidget(date_label)

        self.date_filter2 = QDateEdit(QDate.currentDate())
        self.date_filter2.setCalendarPopup(True)
        toolbar_layout.addWidget(self.date_filter2)

        category_label = QLabel("Catégorie:")
        toolbar_layout.addWidget(category_label)

        self.category_filter = QComboBox()
        self.category_filter.addItem("Toutes", None)
        toolbar_layout.addWidget(self.category_filter)

        self.search_edit2 = QLineEdit()
        self.search_edit2.setPlaceholderText("Rechercher...")
        toolbar_layout.addWidget(self.search_edit2)

        layout.addLayout(toolbar_layout)

        # Tableau des dépenses
        self.expenses_table = QTableView()
        self.expenses_model = ExpenseTableModel()
        self.expenses_proxy_model = QSortFilterProxyModel()
        self.expenses_proxy_model.setSourceModel(self.expenses_model)
        self.expenses_table.setModel(self.expenses_proxy_model)

        # Configuration du tableau pour une meilleure présentation
        header = self.expenses_table.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Ajouter des lignes alternées pour une meilleure lisibilité
        self.expenses_table.setAlternatingRowColors(True)

        # Configurer la sélection par ligne complète
        self.expenses_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)

        # Désactiver l'édition directe dans le tableau
        self.expenses_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.expenses_table.setShowGrid(True)
        self.expenses_table.setSortingEnabled(True)

        layout.addWidget(self.expenses_table)

        return tab

    def setup_connections(self):
        """Configure les connexions des signaux"""
        # Boutons de la barre d'outils
        self.new_register_button.clicked.connect(self.show_new_register_dialog)
        self.refresh_button.clicked.connect(self._load_data_wrapper)
        self.transfer_button.clicked.connect(self.show_transfer_dialog)

        # Filtres
        self.date_filter.dateChanged.connect(self.filter_transactions)
        self.register_filter.currentIndexChanged.connect(self.filter_transactions)
        self.payment_method_filter.currentIndexChanged.connect(self.filter_transactions)
        self.search_edit.textChanged.connect(self.filter_transactions)

        # Tableau des transactions
        self.transactions_table.selectionModel().selectionChanged.connect(self.on_transaction_selection_changed)
        self.transactions_table.doubleClicked.connect(self.show_transaction_details)

        # Tableau des dépenses
        self.expenses_table.selectionModel().selectionChanged.connect(self.on_expense_selection_changed)
        self.expenses_table.doubleClicked.connect(self.show_expense_details)

        # Boutons d'action
        self.new_transaction_button.clicked.connect(lambda: self.show_new_transaction_dialog())
        self.new_expense_button.clicked.connect(lambda: self.show_new_expense_dialog())
        self.reports_button.clicked.connect(self.show_reports_dialog)
        self.daily_closure_button.clicked.connect(self.show_daily_closure_dialog)

    def connect_event_bus(self):
        """Connecte la vue au bus d'événements"""
        try:
            # Connecter aux signaux de mise à jour de la trésorerie
            event_bus.treasury_updated.connect(self.on_treasury_updated)
            event_bus.cash_transaction_added.connect(self.on_cash_transaction_added)

            # Connecter aux signaux de notifications toast
            event_bus.show_success_notification.connect(self.show_success_toast)
            event_bus.show_info_notification.connect(self.show_info_toast)
            event_bus.show_warning_notification.connect(self.show_warning_toast)
            event_bus.show_error_notification.connect(self.show_error_toast)

            print("TreasuryView: Connecté au bus d'événements")
        except Exception as e:
            print(f"Erreur lors de la connexion au bus d'événements: {e}")

    def on_treasury_updated(self):
        """Gère la mise à jour de la trésorerie"""
        print("TreasuryView: Signal treasury_updated reçu - Rafraîchissement des données")
        QTimer.singleShot(100, self._load_data_wrapper)  # Délai pour éviter les conflits

    def on_cash_transaction_added(self, cash_register_id):
        """Gère l'ajout d'une transaction de caisse"""
        print(f"TreasuryView: Signal cash_transaction_added reçu pour la caisse {cash_register_id}")
        QTimer.singleShot(100, self._load_data_wrapper)  # Délai pour éviter les conflits

    # Méthodes pour les notifications toast
    def show_success_toast(self, message: str):
        """Affiche une notification de succès"""
        if self.toast_manager:
            self.toast_manager.show_toast(message, ToastType.SUCCESS)

    def show_info_toast(self, message: str):
        """Affiche une notification d'information"""
        if self.toast_manager:
            self.toast_manager.show_toast(message, ToastType.INFO)

    def show_warning_toast(self, message: str):
        """Affiche une notification d'avertissement"""
        if self.toast_manager:
            self.toast_manager.show_toast(message, ToastType.WARNING)

    def show_error_toast(self, message: str):
        """Affiche une notification d'erreur"""
        if self.toast_manager:
            self.toast_manager.show_toast(message, ToastType.ERROR)

    async def load_data(self):
        """Charge les données de la trésorerie"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau de bord
            dashboard_data = await self.service.get_treasury_dashboard_data()

            # Charger toutes les transactions récentes de manière centralisée
            await self._load_all_recent_transactions(dashboard_data)

            # Mettre à jour la grille des caisses
            self._update_registers_grid(dashboard_data["cash_registers"])

            # Mettre à jour les totaux
            self.total_balance_label.setText(f"{dashboard_data['total_balance']:.2f} DA")
            self.today_in_label.setText(f"{dashboard_data['today_in']:.2f} DA")
            self.today_out_label.setText(f"{dashboard_data['today_out']:.2f} DA")
            self.today_net_label.setText(f"{dashboard_data['today_net']:.2f} DA")

            # Mettre à jour le tableau des transactions
            self.recent_transactions_model.setTransactions(dashboard_data["recent_transactions"])

            # Mettre à jour la date du filtre
            self.date_filter.setDate(QDate.currentDate())

            # Appliquer les filtres
            self.filter_transactions()

        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.loading_overlay.hide()

    async def _load_all_recent_transactions(self, dashboard_data):
        """Charge toutes les transactions récentes de manière centralisée pour éviter les doublons"""
        try:
            from datetime import datetime, timedelta
            from app.core.models.treasury import CashTransaction, TransactionCategory
            from app.core.models.repair import RepairPayment
            from app.core.models.supplier_finance import SupplierInvoice
            from app.core.models.sale import Sale
            from sqlalchemy.orm import joinedload

            # Initialiser la liste des transactions récentes
            all_transactions = []
            transaction_ids = set()  # Pour éviter les doublons

            # 1. Ajouter les transactions de caisse existantes (déjà dans dashboard_data)
            if "recent_transactions" in dashboard_data:
                for transaction in dashboard_data["recent_transactions"]:
                    if hasattr(transaction, 'id') and str(transaction.id) not in transaction_ids:
                        all_transactions.append(transaction)
                        transaction_ids.add(str(transaction.id))

            # 2. Ajouter les paiements de réparations des 30 derniers jours
            thirty_days_ago = datetime.now() - timedelta(days=30)
            repair_payments = self.db.query(RepairPayment).options(
                joinedload(RepairPayment.repair_order)
            ).filter(
                RepairPayment.payment_date >= thirty_days_ago,
                RepairPayment.is_voided == False
            ).order_by(RepairPayment.payment_date.desc()).limit(30).all()

            for payment in repair_payments:
                transaction_id = f"repair_payment_{payment.id}"
                if transaction_id not in transaction_ids:
                    transaction = CashTransaction()
                    transaction.id = transaction_id
                    transaction.amount = float(payment.amount)
                    transaction.transaction_date = payment.payment_date
                    transaction.category = TransactionCategory.REPAIR
                    transaction.description = f"Paiement réparation #{payment.repair_order.number}" if payment.repair_order else f"Paiement réparation #{payment.repair_order_id}"
                    transaction.reference_number = payment.reference_number
                    all_transactions.append(transaction)
                    transaction_ids.add(transaction_id)

            # 3. Ajouter les ventes récentes des 30 derniers jours
            sales = self.db.query(Sale).filter(
                Sale.date >= thirty_days_ago
            ).order_by(Sale.date.desc()).limit(20).all()

            for sale in sales:
                transaction_id = f"sale_{sale.id}"
                if transaction_id not in transaction_ids:
                    transaction = CashTransaction()
                    transaction.id = transaction_id
                    transaction.amount = float(sale.final_amount or 0)
                    transaction.transaction_date = sale.date
                    transaction.category = TransactionCategory.SALE
                    transaction.description = f"Vente #{sale.number}"
                    transaction.reference_number = sale.number
                    all_transactions.append(transaction)
                    transaction_ids.add(transaction_id)

            # 4. Ajouter les paiements de factures fournisseurs (seulement les factures PAYÉES)
            from app.core.models.supplier_finance import InvoiceStatus
            paid_supplier_invoices = self.db.query(SupplierInvoice).filter(
                SupplierInvoice.invoice_date >= thirty_days_ago,
                SupplierInvoice.status == InvoiceStatus.PAID  # Seulement les factures payées
            ).order_by(SupplierInvoice.invoice_date.desc()).limit(20).all()

            for invoice in paid_supplier_invoices:
                transaction_id = f"supplier_invoice_{invoice.id}"
                if transaction_id not in transaction_ids:
                    transaction = CashTransaction()
                    transaction.id = transaction_id
                    transaction.amount = -float(invoice.total_amount)  # Négatif car c'est une sortie
                    transaction.transaction_date = invoice.invoice_date
                    transaction.category = TransactionCategory.PURCHASE
                    transaction.description = f"Paiement facture fournisseur #{invoice.invoice_number}"
                    transaction.reference_number = invoice.invoice_number
                    all_transactions.append(transaction)
                    transaction_ids.add(transaction_id)

            # Trier par date décroissante
            all_transactions.sort(key=lambda x: x.transaction_date, reverse=True)

            # Limiter à 50 transactions récentes
            dashboard_data["recent_transactions"] = all_transactions[:50]

            print(f"Chargé {len(all_transactions)} transactions récentes uniques (sur {len(transaction_ids)} IDs uniques)")

        except Exception as e:
            print(f"Erreur lors du chargement des transactions récentes: {e}")
            import traceback
            traceback.print_exc()

    async def _load_repair_payments(self, dashboard_data):
        """OBSOLÈTE - Remplacé par _load_all_recent_transactions"""
        # Cette méthode n'est plus utilisée car nous avons centralisé le chargement
        # des transactions dans _load_all_recent_transactions pour éviter les doublons
        pass

    async def _load_repair_payments_old(self, dashboard_data):
        """Ancienne méthode - conservée pour référence"""
        try:
            from app.core.services.repair_payment_service import RepairPaymentService
            from app.core.models.repair import RepairPayment
            from sqlalchemy.orm import joinedload
            from datetime import datetime, timedelta

            # Récupérer les paiements de réparations des 30 derniers jours
            thirty_days_ago = datetime.now() - timedelta(days=30)

            repair_payments = self.db.query(RepairPayment).options(
                joinedload(RepairPayment.repair_order)
            ).filter(
                RepairPayment.payment_date >= thirty_days_ago,
                RepairPayment.is_voided == False
            ).order_by(RepairPayment.payment_date.desc()).limit(50).all()

            # Convertir les paiements de réparations en transactions pour l'affichage
            repair_transactions = []
            for payment in repair_payments:
                # Créer une transaction fictive pour l'affichage
                from app.core.models.treasury import CashTransaction, TransactionCategory
                transaction = CashTransaction()
                transaction.id = f"repair_{payment.id}"
                transaction.amount = float(payment.amount)
                transaction.transaction_date = payment.payment_date
                transaction.category = TransactionCategory.REPAIR
                transaction.payment_method = payment.payment_method
                transaction.description = f"Paiement réparation #{payment.repair_order.number}" if payment.repair_order else f"Paiement réparation #{payment.repair_order_id}"
                transaction.reference_number = payment.reference_number
                repair_transactions.append(transaction)

            # Ajouter les paiements de réparations aux transactions récentes
            if "recent_transactions" not in dashboard_data:
                dashboard_data["recent_transactions"] = []

            # Combiner et déduplicater les transactions
            all_transactions = list(dashboard_data["recent_transactions"])

            # Créer un set des IDs existants pour éviter les doublons
            existing_ids = set()
            for transaction in all_transactions:
                if hasattr(transaction, 'id'):
                    existing_ids.add(str(transaction.id))

            # Ajouter seulement les nouvelles transactions de réparation
            for repair_transaction in repair_transactions:
                if str(repair_transaction.id) not in existing_ids:
                    all_transactions.append(repair_transaction)
                    existing_ids.add(str(repair_transaction.id))

            # Trier par date décroissante
            all_transactions.sort(key=lambda x: x.transaction_date, reverse=True)

            # Limiter à 50 transactions récentes
            dashboard_data["recent_transactions"] = all_transactions[:50]

            print(f"Chargé {len(repair_payments)} paiements de réparations")

        except Exception as e:
            print(f"Erreur lors du chargement des paiements de réparations: {e}")
            import traceback
            traceback.print_exc()

    async def _load_financial_history(self, dashboard_data):
        """OBSOLÈTE - Remplacé par _load_all_recent_transactions"""
        # Cette méthode n'est plus utilisée car nous avons centralisé le chargement
        # des transactions dans _load_all_recent_transactions pour éviter les doublons
        pass

    async def _load_financial_history_old(self, dashboard_data):
        """Ancienne méthode - conservée pour référence"""
        try:
            from app.core.models.supplier_finance import SupplierInvoice
            # Remplacer l'import manquant par les modèles existants
            from app.core.models.sale import Sale
            from app.core.models.purchasing import PurchaseOrder
            from datetime import datetime, timedelta

            # Récupérer les factures fournisseurs PAYÉES des 90 derniers jours
            ninety_days_ago = datetime.now() - timedelta(days=90)
            from app.core.models.supplier_finance import InvoiceStatus

            supplier_invoices = self.db.query(SupplierInvoice).filter(
                SupplierInvoice.invoice_date >= ninety_days_ago,
                SupplierInvoice.status == InvoiceStatus.PAID  # Seulement les factures payées
            ).order_by(SupplierInvoice.invoice_date.desc()).limit(30).all()

            # Récupérer les ventes récentes des 90 derniers jours
            sales = self.db.query(Sale).filter(
                Sale.date >= ninety_days_ago
            ).order_by(Sale.date.desc()).limit(30).all()

            # Convertir en transactions pour l'affichage dans l'historique
            history_transactions = []

            # Ajouter les factures fournisseurs payées
            for invoice in supplier_invoices:
                from app.core.models.treasury import CashTransaction, TransactionCategory
                transaction = CashTransaction()
                transaction.id = f"supplier_invoice_{invoice.id}"
                transaction.amount = -float(invoice.total_amount)  # Négatif car c'est une sortie
                transaction.transaction_date = invoice.invoice_date
                transaction.category = TransactionCategory.PURCHASE
                transaction.description = f"Paiement facture fournisseur #{invoice.invoice_number}"
                transaction.reference_number = invoice.invoice_number
                history_transactions.append(transaction)

            # Ajouter les ventes comme transactions clients
            for sale in sales:
                from app.core.models.treasury import CashTransaction, TransactionCategory
                transaction = CashTransaction()
                transaction.id = f"sale_{sale.id}"
                transaction.amount = float(sale.final_amount or 0)
                transaction.transaction_date = sale.date
                transaction.category = TransactionCategory.SALE
                transaction.description = f"Vente #{sale.number}"
                transaction.reference_number = sale.number
                history_transactions.append(transaction)

            # Ajouter à l'historique global
            if "financial_history" not in dashboard_data:
                dashboard_data["financial_history"] = []

            # Combiner et déduplicater l'historique
            all_history = list(dashboard_data["financial_history"])

            # Créer un set des IDs existants pour éviter les doublons
            existing_history_ids = set()
            for transaction in all_history:
                if hasattr(transaction, 'id'):
                    existing_history_ids.add(str(transaction.id))

            # Ajouter seulement les nouvelles transactions d'historique
            for history_transaction in history_transactions:
                if str(history_transaction.id) not in existing_history_ids:
                    all_history.append(history_transaction)
                    existing_history_ids.add(str(history_transaction.id))

            # Trier par date décroissante
            all_history.sort(key=lambda x: x.transaction_date, reverse=True)

            # Limiter à 100 entrées d'historique
            dashboard_data["financial_history"] = all_history[:100]

            print(f"Chargé {len(supplier_invoices)} factures fournisseurs et {len(sales)} ventes dans l'historique")

        except Exception as e:
            print(f"Erreur lors du chargement de l'historique financier: {e}")
            import traceback
            traceback.print_exc()

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        try:
            from app.ui.utils.async_runner import AsyncRunner
            AsyncRunner.run_async(
                self.load_data(),
                error_message="Erreur lors du chargement des données de trésorerie",
                parent=self
            )
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()

    def _update_registers_grid(self, registers):
        """Met à jour la grille des caisses"""
        # Effacer les widgets existants
        for i in reversed(range(self.registers_grid.count())):
            self.registers_grid.itemAt(i).widget().setParent(None)

        for i in reversed(range(self.registers_grid2.count())):
            self.registers_grid2.itemAt(i).widget().setParent(None)

        # Ajouter les widgets de caisse
        row, col = 0, 0
        for register in registers:
            # Créer le widget
            widget = CashRegisterWidget(register)
            widget.reconcile_clicked.connect(self.show_reconciliation_dialog)
            widget.add_transaction_clicked.connect(self.show_new_transaction_dialog)
            widget.view_transactions_clicked.connect(self.show_register_transactions)

            # Ajouter à la grille du tableau de bord
            self.registers_grid.addWidget(widget, row, col)

            # Ajouter à la grille de l'onglet Caisses
            widget2 = CashRegisterWidget(register)
            widget2.reconcile_clicked.connect(self.show_reconciliation_dialog)
            widget2.add_transaction_clicked.connect(self.show_new_transaction_dialog)
            widget2.view_transactions_clicked.connect(self.show_register_transactions)
            self.registers_grid2.addWidget(widget2, row, col)

            # Passer à la colonne suivante
            col += 1
            if col >= 2:  # 2 colonnes maximum
                col = 0
                row += 1

    def _update_filters(self, registers):
        """Met à jour les filtres"""
        # Filtre de caisse pour les transactions
        self.register_filter.clear()
        self.register_filter.addItem("Toutes", None)
        for register in registers:
            self.register_filter.addItem(register.name, register.id)

        # Filtre de caisse pour les dépenses
        self.register_filter2.clear()
        self.register_filter2.addItem("Toutes", None)
        for register in registers:
            self.register_filter2.addItem(register.name, register.id)

    def _load_expense_categories(self):
        """Charge les catégories de dépenses"""
        # Pour l'instant, on utilise des catégories prédéfinies
        self.category_filter.clear()
        self.category_filter.addItem("Toutes", None)

        categories = [
            "Fournitures de bureau",
            "Loyer",
            "Électricité",
            "Eau",
            "Internet",
            "Téléphone",
            "Carburant",
            "Maintenance",
            "Salaires",
            "Impôts",
            "Assurance",
            "Publicité",
            "Frais bancaires",
            "Frais de livraison",
            "Urgence",
            "Autre"
        ]

        for category in categories:
            self.category_filter.addItem(category, category)

    def filter_transactions(self):
        """Filtre les transactions"""
        # TODO: Implémenter le filtrage des transactions
        pass

    def filter_expenses(self):
        """Filtre les dépenses"""
        # TODO: Implémenter le filtrage des dépenses
        pass

    def show_new_register_dialog(self):
        """Affiche la boîte de dialogue pour créer une nouvelle caisse"""
        dialog = CashRegisterDialog(self)
        if dialog.exec():
            self.init_data()

    def show_transfer_dialog(self):
        """Affiche la boîte de dialogue pour effectuer un transfert entre caisses"""
        dialog = TransferDialog(self)
        if dialog.exec():
            self.init_data()

    def show_new_transaction_dialog(self, register_id=None):
        """Affiche la boîte de dialogue pour ajouter une nouvelle transaction"""
        dialog = TransactionDialog(self, register_id)
        if dialog.exec():
            self.init_data()

    def show_new_expense_dialog(self, register_id=None):
        """Affiche la boîte de dialogue pour ajouter une nouvelle dépense"""
        dialog = ExpenseDialog(self, register_id)
        if dialog.exec():
            self.init_data()

    def show_reconciliation_dialog(self, register_id):
        """Affiche la boîte de dialogue pour réconcilier une caisse"""
        dialog = ReconciliationDialog(self, register_id)
        if dialog.exec():
            self.init_data()

    def show_register_transactions(self, register_id):
        """Affiche les transactions d'une caisse"""
        # Sélectionner l'onglet Transactions
        self.tab_widget.setCurrentIndex(2)

        # Sélectionner la caisse dans le filtre
        index = self.register_filter.findData(register_id)
        if index >= 0:
            self.register_filter.setCurrentIndex(index)

        # Appliquer le filtre
        self.filter_transactions()

    def on_transaction_selection_changed(self, selected, deselected):
        """Gère le changement de sélection dans le tableau des transactions"""
        has_selection = len(selected.indexes()) > 0
        # Activer/désactiver les boutons d'action en fonction de la sélection
        # TODO: Implémenter la logique des boutons d'action

    def on_expense_selection_changed(self, selected, deselected):
        """Gère le changement de sélection dans le tableau des dépenses"""
        has_selection = len(selected.indexes()) > 0
        # Activer/désactiver les boutons d'action en fonction de la sélection
        # TODO: Implémenter la logique des boutons d'action

    def show_transaction_details(self, index):
        """Affiche les détails d'une transaction"""
        if not index.isValid():
            return

        # Récupérer la transaction sélectionnée
        source_index = self.transactions_proxy_model.mapToSource(index)
        transaction = self.transactions_model.get_transaction(source_index.row())
        if not transaction:
            return

        # Créer et afficher la boîte de dialogue de détails
        dialog = TransactionDialog(self, transaction=transaction)
        dialog.exec()

    def show_expense_details(self, index):
        """Affiche les détails d'une dépense"""
        if not index.isValid():
            return

        # Récupérer la dépense sélectionnée
        source_index = self.expenses_proxy_model.mapToSource(index)
        expense = self.expenses_model.get_expense(source_index.row())
        if not expense:
            return

        # Créer et afficher la boîte de dialogue de détails
        dialog = ExpenseDialog(self, expense=expense)
        dialog.exec()

    def show_reports_dialog(self):
        """Affiche le dialogue de génération de rapports"""
        try:
            dialog = ReportsDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ouverture des rapports:\n{e}")
            event_bus.show_error(f"Erreur d'ouverture des rapports: {e}")

    def show_daily_closure_dialog(self):
        """Affiche le dialogue de clôture journalière"""
        try:
            dialog = DailyClosureDialog(self)
            dialog.exec()

            # Actualiser les données après fermeture du dialogue
            self.load_data()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ouverture des clôtures:\n{e}")
            event_bus.show_error(f"Erreur d'ouverture des clôtures: {e}")

    def init_data(self):
        """Initialise les données de la vue"""
        try:
            from app.ui.utils.async_runner import AsyncRunner

            # Charger les données de manière asynchrone
            AsyncRunner.run_async(
                self.load_data(),
                error_message="Erreur lors de l'initialisation des données de trésorerie",
                parent=self
            )

            # Charger les catégories de dépenses (synchrone)
            self._load_expense_categories()

        except Exception as e:
            print(f"Erreur lors de l'initialisation des données: {e}")
            import traceback
            traceback.print_exc()
