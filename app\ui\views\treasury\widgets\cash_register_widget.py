"""
Widget pour afficher les informations d'une caisse.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QFont

from app.core.models.treasury import CashRegister, CashRegisterType

class CashRegisterWidget(QWidget):
    """Widget pour afficher les informations d'une caisse"""
    
    # Signaux
    reconcile_clicked = pyqtSignal(int)  # ID de la caisse
    add_transaction_clicked = pyqtSignal(int)  # ID de la caisse
    view_transactions_clicked = pyqtSignal(int)  # ID de la caisse
    
    def __init__(self, cash_register: CashRegister, parent=None):
        super().__init__(parent)
        self.cash_register = cash_register
        self.setup_ui()
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)
        
        # Cadre avec bordure
        frame = QFrame()
        frame.setFrameShape(QFrame.Shape.StyledPanel)
        frame.setFrameShadow(QFrame.Shadow.Raised)
        frame.setLineWidth(1)
        frame_layout = QVBoxLayout(frame)
        
        # En-tête avec le nom de la caisse
        header_layout = QHBoxLayout()
        
        # Icône en fonction du type de caisse
        icon_label = QLabel()
        icon_name = self._get_icon_for_type(self.cash_register.type)
        icon_label.setPixmap(QIcon(f"app/ui/assets/icons/{icon_name}.png").pixmap(24, 24))
        header_layout.addWidget(icon_label)
        
        # Nom de la caisse
        name_label = QLabel(self.cash_register.name)
        name_font = QFont()
        name_font.setBold(True)
        name_font.setPointSize(12)
        name_label.setFont(name_font)
        header_layout.addWidget(name_label, 1)
        
        # Type de caisse
        type_label = QLabel(self._get_type_display(self.cash_register.type))
        type_label.setStyleSheet("color: gray;")
        header_layout.addWidget(type_label)
        
        frame_layout.addLayout(header_layout)
        
        # Séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        frame_layout.addWidget(separator)
        
        # Solde actuel
        balance_layout = QHBoxLayout()
        balance_label = QLabel("Solde actuel:")
        balance_layout.addWidget(balance_label)
        
        balance_value = QLabel(f"{self.cash_register.current_balance:.2f} DA")
        balance_font = QFont()
        balance_font.setBold(True)
        balance_font.setPointSize(14)
        balance_value.setFont(balance_font)
        
        # Couleur en fonction du solde
        if self.cash_register.current_balance < 0:
            balance_value.setStyleSheet("color: red;")
        elif self.cash_register.current_balance > 0:
            balance_value.setStyleSheet("color: green;")
        
        balance_layout.addWidget(balance_value, 1, Qt.AlignmentFlag.AlignRight)
        frame_layout.addLayout(balance_layout)
        
        # Dernière réconciliation
        if self.cash_register.last_reconciliation:
            reconciliation_layout = QHBoxLayout()
            reconciliation_label = QLabel("Dernière réconciliation:")
            reconciliation_layout.addWidget(reconciliation_label)
            
            reconciliation_value = QLabel(self.cash_register.last_reconciliation.strftime("%d/%m/%Y %H:%M"))
            reconciliation_layout.addWidget(reconciliation_value, 1, Qt.AlignmentFlag.AlignRight)
            frame_layout.addLayout(reconciliation_layout)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        # Bouton Réconcilier
        reconcile_button = QPushButton("Réconcilier")
        reconcile_button.setIcon(QIcon("app/ui/assets/icons/reconcile.png"))
        reconcile_button.clicked.connect(lambda: self.reconcile_clicked.emit(self.cash_register.id))
        buttons_layout.addWidget(reconcile_button)
        
        # Bouton Ajouter une transaction
        add_transaction_button = QPushButton("Ajouter transaction")
        add_transaction_button.setIcon(QIcon("app/ui/assets/icons/add_transaction.png"))
        add_transaction_button.clicked.connect(lambda: self.add_transaction_clicked.emit(self.cash_register.id))
        buttons_layout.addWidget(add_transaction_button)
        
        # Bouton Voir les transactions
        view_transactions_button = QPushButton("Voir transactions")
        view_transactions_button.setIcon(QIcon("app/ui/assets/icons/view_transactions.png"))
        view_transactions_button.clicked.connect(lambda: self.view_transactions_clicked.emit(self.cash_register.id))
        buttons_layout.addWidget(view_transactions_button)
        
        frame_layout.addLayout(buttons_layout)
        
        main_layout.addWidget(frame)
        
        # Définir une taille minimale
        self.setMinimumWidth(300)
        self.setMinimumHeight(150)
        
    def update_cash_register(self, cash_register: CashRegister):
        """Met à jour les informations de la caisse"""
        self.cash_register = cash_register
        # Recréer l'interface
        self.deleteLater()
        self.setup_ui()
        
    def _get_icon_for_type(self, register_type: CashRegisterType) -> str:
        """Retourne le nom de l'icône en fonction du type de caisse"""
        icons = {
            CashRegisterType.MAIN: "cash_register",
            CashRegisterType.REPAIR: "repair",
            CashRegisterType.SALES: "sales",
            CashRegisterType.PURCHASE: "purchase",
            CashRegisterType.EXPENSE: "expense"
        }
        return icons.get(register_type, "cash_register")
    
    def _get_type_display(self, register_type: CashRegisterType) -> str:
        """Retourne le nom d'affichage du type de caisse"""
        types = {
            CashRegisterType.MAIN: "Caisse principale",
            CashRegisterType.REPAIR: "Caisse réparations",
            CashRegisterType.SALES: "Caisse ventes",
            CashRegisterType.PURCHASE: "Caisse achats",
            CashRegisterType.EXPENSE: "Caisse dépenses"
        }
        return types.get(register_type, "Caisse")
