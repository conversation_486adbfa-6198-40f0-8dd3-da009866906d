"""
Modèle de table pour les dépenses.
"""
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant
from PyQt6.QtGui import QColor, QBrush

from app.core.models.treasury import Expense, PaymentMethod
from datetime import datetime

class ExpenseTableModel(QAbstractTableModel):
    """Modèle de table pour les dépenses"""

    def __init__(self, expenses=None, parent=None):
        super().__init__(parent)
        self.expenses = expenses or []
        self.headers = [
            "ID", "Date", "Montant", "Catégorie", "Méthode", "Référence", "Description"
        ]

    def rowCount(self, parent=QModelIndex()):
        return len(self.expenses)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return QVariant()

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.expenses):
            return QVariant()

        expense = self.expenses[index.row()]
        column = index.column()

        if role == Qt.ItemDataRole.DisplayRole:
            if column == 0:
                return str(expense.id)
            elif column == 1:
                return expense.expense_date.strftime("%d/%m/%Y %H:%M")
            elif column == 2:
                return f"{expense.amount:.2f} DA"
            elif column == 3:
                return expense.category
            elif column == 4:
                return self._get_payment_method_display(expense.payment_method)
            elif column == 5:
                return expense.reference_number or ""
            elif column == 6:
                return expense.description or ""

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            if column == 2:  # Montant
                return int(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            return int(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)

        elif role == Qt.ItemDataRole.BackgroundRole:
            # Colorer les lignes en fonction de la catégorie
            if expense.category == "Urgence":
                return QBrush(QColor(255, 200, 200))  # Rouge clair pour les urgences

        return QVariant()

    def setExpenses(self, expenses):
        """Met à jour la liste des dépenses"""
        self.beginResetModel()
        self.expenses = expenses
        self.endResetModel()

    def _get_payment_method_display(self, method: PaymentMethod) -> str:
        """Retourne le nom d'affichage de la méthode de paiement"""
        methods = {
            PaymentMethod.cash: "Espèces",
            PaymentMethod.bank_transfer: "Virement",
            PaymentMethod.check: "Chèque",
            PaymentMethod.credit_card: "Carte",
            PaymentMethod.other: "Autre"
        }
        return methods.get(method, "Inconnu")
