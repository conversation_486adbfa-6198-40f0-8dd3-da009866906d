"""
Modèle de table pour les transactions de caisse.
"""
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant
from PyQt6.QtGui import QColor, QBrush

from app.core.models.treasury import CashTransaction, TransactionCategory, PaymentMethod
from datetime import datetime

class TransactionTableModel(QAbstractTableModel):
    """Modèle de table pour les transactions de caisse"""

    def __init__(self, transactions=None, parent=None):
        super().__init__(parent)
        self.transactions = transactions or []
        self.headers = [
            "ID", "Date", "Montant", "Catégorie", "Méthode", "Référence", "Description"
        ]

    def rowCount(self, parent=QModelIndex()):
        return len(self.transactions)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return QVariant()

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.transactions):
            return QVariant()

        transaction = self.transactions[index.row()]
        column = index.column()

        if role == Qt.ItemDataRole.DisplayRole:
            if column == 0:
                return str(transaction.id)
            elif column == 1:
                return transaction.transaction_date.strftime("%d/%m/%Y %H:%M")
            elif column == 2:
                return f"{transaction.amount:.2f} DA"
            elif column == 3:
                return self._get_category_display(transaction.category)
            elif column == 4:
                return self._get_payment_method_display(transaction.payment_method)
            elif column == 5:
                return transaction.reference_number or ""
            elif column == 6:
                return transaction.description or ""

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            if column == 2:  # Montant
                return int(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            return int(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)

        elif role == Qt.ItemDataRole.BackgroundRole:
            # Colorer les lignes en fonction du montant
            if transaction.amount < 0:
                return QBrush(QColor(255, 200, 200))  # Rouge clair pour les sorties
            elif transaction.amount > 0:
                return QBrush(QColor(200, 255, 200))  # Vert clair pour les entrées

        return QVariant()

    def setTransactions(self, transactions):
        """Met à jour la liste des transactions"""
        self.beginResetModel()
        self.transactions = transactions
        self.endResetModel()

    def _get_category_display(self, category: TransactionCategory) -> str:
        """Retourne le nom d'affichage de la catégorie"""
        categories = {
            TransactionCategory.SALE: "Vente",
            TransactionCategory.REPAIR: "Réparation",
            TransactionCategory.PURCHASE: "Achat",
            TransactionCategory.EXPENSE: "Dépense",
            TransactionCategory.TRANSFER: "Transfert",
            TransactionCategory.DEPOSIT: "Dépôt",
            TransactionCategory.WITHDRAWAL: "Retrait",
            TransactionCategory.ADJUSTMENT: "Ajustement",
            TransactionCategory.OTHER: "Autre"
        }
        return categories.get(category, "Inconnu")

    def _get_payment_method_display(self, method: PaymentMethod) -> str:
        """Retourne le nom d'affichage de la méthode de paiement"""
        methods = {
            PaymentMethod.cash: "Espèces",
            PaymentMethod.bank_transfer: "Virement",
            PaymentMethod.check: "Chèque",
            PaymentMethod.credit_card: "Carte",
            PaymentMethod.other: "Autre"
        }
        return methods.get(method, "Inconnu")
