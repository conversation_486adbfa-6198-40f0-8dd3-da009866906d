from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView, QSplitter,
    QPushButton, QLineEdit, QComboBox, QLabel, QFrame,
    QMessageBox, QTabWidget, QHeaderView, QDateEdit, QFormLayout
)
from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex, QDateTime, QTimer
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime, timedelta
import json

from app.core.models.audit import AuditActionType
from app.core.services.audit_service import AuditService
from app.core.services.user_service import UserService
from app.ui.components.custom_widgets import SearchBar, FilterComboBox, LoadingOverlay
from app.utils.database import SessionLocal

class AuditLogTableModel(QAbstractTableModel):
    """Modèle de tableau pour les journaux d'audit"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.service = AuditService(self.db)
        self.user_service = UserService(self.db)
        self.logs = []
        self.headers = ["Date/Heure", "Utilisateur", "Action", "Entité", "Détails"]
    
    async def load_data(
        self,
        user_id=None,
        action=None,
        entity_type=None,
        start_date=None,
        end_date=None
    ):
        """Charge les données des journaux d'audit"""
        self.beginResetModel()
        self.logs = await self.service.get_logs(
            user_id=user_id,
            action=action,
            entity_type=entity_type,
            start_date=start_date,
            end_date=end_date
        )
        self.endResetModel()
    
    def rowCount(self, parent=QModelIndex()):
        """Retourne le nombre de lignes"""
        return len(self.logs)
    
    def columnCount(self, parent=QModelIndex()):
        """Retourne le nombre de colonnes"""
        return len(self.headers)
    
    def data(self, index, role):
        """Retourne les données pour l'affichage"""
        if not index.isValid() or index.row() >= len(self.logs):
            return None
        
        log = self.logs[index.row()]
        
        if role == Qt.ItemDataRole.DisplayRole:
            if index.column() == 0:
                return self._format_datetime(log.timestamp)
            elif index.column() == 1:
                return self._get_user_name(log.user_id)
            elif index.column() == 2:
                return self._get_action_display(log.action)
            elif index.column() == 3:
                if log.entity_type and log.entity_id:
                    return f"{log.entity_type} #{log.entity_id}"
                return ""
            elif index.column() == 4:
                return self._format_details(log.details)
        
        elif role == Qt.ItemDataRole.TextAlignmentRole:
            if index.column() == 0:
                return int(Qt.AlignmentFlag.AlignCenter)
            elif index.column() in [1, 2, 3]:
                return int(Qt.AlignmentFlag.AlignCenter)
            else:
                return int(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        
        return None
    
    def headerData(self, section, orientation, role):
        """Retourne les en-têtes du tableau"""
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return None
    
    def get_log(self, row):
        """Retourne le journal d'audit à la ligne spécifiée"""
        if 0 <= row < len(self.logs):
            return self.logs[row]
        return None
    
    def _format_datetime(self, dt):
        """Formate une date/heure pour l'affichage"""
        if not dt:
            return ""
        
        if isinstance(dt, datetime):
            return dt.strftime("%d/%m/%Y %H:%M:%S")
        
        return str(dt)
    
    def _get_user_name(self, user_id):
        """Retourne le nom de l'utilisateur"""
        if not user_id:
            return "Système"
        
        # TODO: Implémenter un cache pour éviter de requêter la base de données à chaque fois
        user = self.db.query(self.user_service.model).filter(self.user_service.model.id == user_id).first()
        if user:
            return user.full_name
        
        return f"Utilisateur #{user_id}"
    
    def _get_action_display(self, action):
        """Retourne l'affichage de l'action"""
        action_display = {
            AuditActionType.CREATE: "Création",
            AuditActionType.READ: "Lecture",
            AuditActionType.UPDATE: "Modification",
            AuditActionType.DELETE: "Suppression",
            AuditActionType.LOGIN: "Connexion",
            AuditActionType.LOGOUT: "Déconnexion",
            AuditActionType.PASSWORD_CHANGE: "Changement de mot de passe",
            AuditActionType.PASSWORD_RESET: "Réinitialisation de mot de passe",
            AuditActionType.PERMISSION_CHANGE: "Changement de permissions",
            AuditActionType.EXPORT: "Export",
            AuditActionType.IMPORT: "Import",
            AuditActionType.OTHER: "Autre"
        }
        return action_display.get(action, str(action))
    
    def _format_details(self, details):
        """Formate les détails pour l'affichage"""
        if not details:
            return ""
        
        if isinstance(details, dict):
            # Simplifier les détails pour l'affichage
            simple_details = {}
            for key, value in details.items():
                if isinstance(value, dict):
                    simple_details[key] = "..."
                else:
                    simple_details[key] = value
            
            return json.dumps(simple_details, ensure_ascii=False)
        
        return str(details)

class AuditLogView(QWidget):
    """Vue pour les journaux d'audit"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Services
        self.db = SessionLocal()
        self.audit_service = AuditService(self.db)
        self.user_service = UserService(self.db)
        
        # Configuration de l'interface
        self.setup_ui()
        self.setup_connections()
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        
        # Charger les données
        self.init_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # En-tête
        header = QLabel("Journal d'Audit")
        header.setObjectName("sectionHeader")
        main_layout.addWidget(header)
        
        # Filtres
        filters_layout = QHBoxLayout()
        
        # Filtre par utilisateur
        self.user_filter = QComboBox()
        self.user_filter.addItem("Tous les utilisateurs", None)
        filters_layout.addWidget(QLabel("Utilisateur:"))
        filters_layout.addWidget(self.user_filter)
        
        # Filtre par action
        self.action_filter = QComboBox()
        self.action_filter.addItem("Toutes les actions", None)
        for action in AuditActionType:
            self.action_filter.addItem(self._get_action_display(action), action.value)
        filters_layout.addWidget(QLabel("Action:"))
        filters_layout.addWidget(self.action_filter)
        
        # Filtre par entité
        self.entity_filter = QComboBox()
        self.entity_filter.addItem("Toutes les entités", None)
        self.entity_filter.addItems(["user", "role", "permission", "inventory", "repair", "supplier", "customer"])
        filters_layout.addWidget(QLabel("Entité:"))
        filters_layout.addWidget(self.entity_filter)
        
        # Filtre par date
        date_filter_layout = QHBoxLayout()
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDateTime.currentDateTime().addDays(-7).date())
        date_filter_layout.addWidget(QLabel("Du:"))
        date_filter_layout.addWidget(self.start_date_edit)
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDateTime.currentDateTime().date())
        date_filter_layout.addWidget(QLabel("Au:"))
        date_filter_layout.addWidget(self.end_date_edit)
        
        filters_layout.addLayout(date_filter_layout)
        
        # Bouton de recherche
        self.search_button = QPushButton("Rechercher")
        self.search_button.setIcon(QIcon("app/ui/resources/icons/search.svg"))
        filters_layout.addWidget(self.search_button)
        
        main_layout.addLayout(filters_layout)
        
        # Tableau des journaux d'audit
        self.table_view = QTableView()
        self.table_view.setObjectName("auditLogsTable")
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.setAlternatingRowColors(True)
        
        # Modèle de données
        self.table_model = AuditLogTableModel()
        self.table_view.setModel(self.table_model)
        
        # Ajuster les colonnes
        self.table_view.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Date/Heure
        self.table_view.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Utilisateur
        self.table_view.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Action
        self.table_view.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Entité
        self.table_view.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)  # Détails
        
        main_layout.addWidget(self.table_view)
        
        # Boutons d'action
        actions_layout = QHBoxLayout()
        
        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))
        actions_layout.addWidget(self.export_button)
        
        self.purge_button = QPushButton("Purger les anciens journaux")
        self.purge_button.setIcon(QIcon("app/ui/resources/icons/trash.svg"))
        actions_layout.addWidget(self.purge_button)
        
        actions_layout.addStretch()
        
        self.refresh_button = QPushButton("Actualiser")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        actions_layout.addWidget(self.refresh_button)
        
        main_layout.addLayout(actions_layout)
    
    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.search_button.clicked.connect(self.filter_logs)
        self.refresh_button.clicked.connect(self.filter_logs)
        self.export_button.clicked.connect(self.export_logs)
        self.purge_button.clicked.connect(self.purge_logs)
    
    def init_data(self):
        """Initialise les données de la vue"""
        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)
    
    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_data())
        finally:
            loop.close()
    
    async def load_data(self):
        """Charge les données des journaux d'audit et des utilisateurs"""
        self.loading_overlay.show()
        try:
            # Charger les utilisateurs pour le filtre
            users = await self.user_service.get_all()
            
            # Mettre à jour le filtre d'utilisateurs
            self.user_filter.clear()
            self.user_filter.addItem("Tous les utilisateurs", None)
            self.user_filter.addItem("Système", 0)
            for user in users:
                self.user_filter.addItem(user.full_name, user.id)
            
            # Charger les journaux d'audit
            await self.filter_logs()
        finally:
            self.loading_overlay.hide()
    
    def filter_logs(self):
        """Applique les filtres sur le tableau des journaux d'audit"""
        user_id = self.user_filter.currentData()
        action = self.action_filter.currentData()
        entity_type = self.entity_filter.currentData()
        
        start_date = self.start_date_edit.date().toPyDate()
        start_datetime = datetime.combine(start_date, datetime.min.time())
        
        end_date = self.end_date_edit.date().toPyDate()
        end_datetime = datetime.combine(end_date, datetime.max.time())
        
        # Utiliser QTimer pour exécuter la méthode dans le thread principal
        QTimer.singleShot(0, lambda: self._filter_logs_wrapper(
            user_id, action, entity_type, start_datetime, end_datetime
        ))
    
    def _filter_logs_wrapper(self, user_id, action, entity_type, start_date, end_date):
        """Wrapper pour exécuter filter_logs_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.filter_logs_async(
                user_id, action, entity_type, start_date, end_date
            ))
        finally:
            loop.close()
    
    async def filter_logs_async(self, user_id, action, entity_type, start_date, end_date):
        """Applique les filtres sur le tableau des journaux d'audit de manière asynchrone"""
        self.loading_overlay.show()
        try:
            # Charger les journaux d'audit avec les filtres
            await self.table_model.load_data(
                user_id=user_id,
                action=action,
                entity_type=entity_type,
                start_date=start_date,
                end_date=end_date
            )
            
            # Ajuster les colonnes
            self.table_view.resizeColumnsToContents()
        finally:
            self.loading_overlay.hide()
    
    def export_logs(self):
        """Exporte les journaux d'audit"""
        # TODO: Implémenter l'export des journaux d'audit
        QMessageBox.information(self, "Export", "Fonctionnalité d'export non implémentée.")
    
    def purge_logs(self):
        """Purge les anciens journaux d'audit"""
        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Voulez-vous vraiment purger les journaux d'audit de plus d'un an ?\n"
            "Cette action est irréversible.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Utiliser QTimer pour exécuter la méthode dans le thread principal
            QTimer.singleShot(0, self._purge_logs_wrapper)
    
    def _purge_logs_wrapper(self):
        """Wrapper pour exécuter purge_logs_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.purge_logs_async())
        finally:
            loop.close()
    
    async def purge_logs_async(self):
        """Purge les anciens journaux d'audit de manière asynchrone"""
        self.loading_overlay.show()
        try:
            # Purger les journaux d'audit
            count = await self.audit_service.purge_old_logs(days=365)
            
            QMessageBox.information(
                self,
                "Purge terminée",
                f"{count} journaux d'audit ont été supprimés."
            )
            
            # Actualiser les données
            await self.filter_logs_async(
                user_id=self.user_filter.currentData(),
                action=self.action_filter.currentData(),
                entity_type=self.entity_filter.currentData(),
                start_date=self.start_date_edit.date().toPyDate(),
                end_date=self.end_date_edit.date().toPyDate()
            )
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()
    
    def _get_action_display(self, action):
        """Retourne l'affichage de l'action"""
        action_display = {
            AuditActionType.CREATE: "Création",
            AuditActionType.READ: "Lecture",
            AuditActionType.UPDATE: "Modification",
            AuditActionType.DELETE: "Suppression",
            AuditActionType.LOGIN: "Connexion",
            AuditActionType.LOGOUT: "Déconnexion",
            AuditActionType.PASSWORD_CHANGE: "Changement de mot de passe",
            AuditActionType.PASSWORD_RESET: "Réinitialisation de mot de passe",
            AuditActionType.PERMISSION_CHANGE: "Changement de permissions",
            AuditActionType.EXPORT: "Export",
            AuditActionType.IMPORT: "Import",
            AuditActionType.OTHER: "Autre"
        }
        return action_display.get(action, str(action))
