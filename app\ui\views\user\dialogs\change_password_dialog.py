from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QDialogButtonBox, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon
import asyncio

from app.core.models.user import PasswordChange
from app.core.services.user_service import UserService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay, PasswordStrengthBar

class ChangePasswordDialog(QDialog):
    """Dialogue pour changer le mot de passe d'un utilisateur"""
    
    def __init__(self, parent=None, user_id=None):
        super().__init__(parent)
        self.user_id = user_id
        
        if not user_id:
            raise ValueError("L'ID de l'utilisateur est requis")
        
        # Services
        self.db = SessionLocal()
        self.user_service = UserService(self.db)
        
        # Configuration de la fenêtre
        self.setWindowTitle("Changer le mot de passe")
        self.setMinimumWidth(400)
        
        # Initialisation de l'interface
        self.setup_ui()
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Formulaire
        form_layout = QFormLayout()
        
        # Mot de passe actuel
        self.current_password_edit = QLineEdit()
        self.current_password_edit.setPlaceholderText("Mot de passe actuel")
        self.current_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addRow("Mot de passe actuel:", self.current_password_edit)
        
        # Nouveau mot de passe
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setPlaceholderText("Nouveau mot de passe")
        self.new_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addRow("Nouveau mot de passe:", self.new_password_edit)
        
        # Barre de force du mot de passe
        self.password_strength = PasswordStrengthBar()
        self.new_password_edit.textChanged.connect(self.password_strength.update_strength)
        form_layout.addRow("Force:", self.password_strength)
        
        # Confirmation du nouveau mot de passe
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setPlaceholderText("Confirmer le nouveau mot de passe")
        self.confirm_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addRow("Confirmation:", self.confirm_password_edit)
        
        main_layout.addLayout(form_layout)
        
        # Boutons de dialogue
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | 
            QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.button(QDialogButtonBox.StandardButton.Save).setText("Changer")
        self.button_box.accepted.connect(self.change_password)
        self.button_box.rejected.connect(self.reject)
        
        main_layout.addWidget(self.button_box)
    
    def change_password(self):
        """Change le mot de passe"""
        # Vérifier les champs
        current_password = self.current_password_edit.text()
        new_password = self.new_password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        
        if not current_password:
            QMessageBox.warning(self, "Validation", "Veuillez saisir votre mot de passe actuel.")
            return
        
        if not new_password:
            QMessageBox.warning(self, "Validation", "Veuillez saisir un nouveau mot de passe.")
            return
        
        if new_password != confirm_password:
            QMessageBox.warning(self, "Validation", "Les mots de passe ne correspondent pas.")
            return
        
        if len(new_password) < 8:
            QMessageBox.warning(self, "Validation", "Le mot de passe doit contenir au moins 8 caractères.")
            return
        
        # Utiliser QTimer pour exécuter la méthode dans le thread principal
        QTimer.singleShot(0, lambda: self._change_password_wrapper(current_password, new_password, confirm_password))
    
    def _change_password_wrapper(self, current_password, new_password, confirm_password):
        """Wrapper pour exécuter change_password_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.change_password_async(current_password, new_password, confirm_password))
        finally:
            loop.close()
    
    async def change_password_async(self, current_password, new_password, confirm_password):
        """Change le mot de passe de manière asynchrone"""
        self.loading_overlay.show()
        try:
            # Créer l'objet de changement de mot de passe
            password_data = PasswordChange(
                current_password=current_password,
                new_password=new_password,
                confirm_password=confirm_password
            )
            
            # Changer le mot de passe
            success = await self.user_service.change_password(
                self.user_id,
                password_data,
                updated_by_id=self.user_id  # L'utilisateur change son propre mot de passe
            )
            
            if success:
                QMessageBox.information(
                    self,
                    "Mot de passe changé",
                    "Votre mot de passe a été changé avec succès."
                )
                self.accept()
            else:
                QMessageBox.warning(
                    self,
                    "Échec du changement",
                    "Le changement de mot de passe a échoué."
                )
        except ValueError as e:
            QMessageBox.warning(self, "Erreur de validation", str(e))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()
