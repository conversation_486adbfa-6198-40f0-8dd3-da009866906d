from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QDialogButtonBox, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon
import asyncio

from app.core.services.auth_service import AuthService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay, PasswordStrengthBar

class PasswordResetRequestDialog(QDialog):
    """Dialogue pour demander une réinitialisation de mot de passe"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Services
        self.db = SessionLocal()
        self.auth_service = AuthService(self.db)
        
        # Configuration de la fenêtre
        self.setWindowTitle("Réinitialisation de mot de passe")
        self.setMinimumWidth(400)
        
        # Initialisation de l'interface
        self.setup_ui()
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Message d'information
        info_label = QLabel(
            "Veuillez saisir votre adresse email. "
            "Un lien de réinitialisation vous sera envoyé."
        )
        info_label.setWordWrap(True)
        main_layout.addWidget(info_label)
        
        # Formulaire
        form_layout = QFormLayout()
        
        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("Votre adresse email")
        form_layout.addRow("Email:", self.email_edit)
        
        main_layout.addLayout(form_layout)
        
        # Boutons de dialogue
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.button(QDialogButtonBox.StandardButton.Ok).setText("Envoyer")
        self.button_box.accepted.connect(self.request_reset)
        self.button_box.rejected.connect(self.reject)
        
        main_layout.addWidget(self.button_box)
    
    def request_reset(self):
        """Demande une réinitialisation de mot de passe"""
        email = self.email_edit.text().strip()
        
        if not email:
            QMessageBox.warning(self, "Validation", "Veuillez saisir une adresse email.")
            return
        
        # Utiliser QTimer pour exécuter la méthode dans le thread principal
        QTimer.singleShot(0, lambda: self._request_reset_wrapper(email))
    
    def _request_reset_wrapper(self, email):
        """Wrapper pour exécuter request_reset_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.request_reset_async(email))
        finally:
            loop.close()
    
    async def request_reset_async(self, email):
        """Demande une réinitialisation de mot de passe de manière asynchrone"""
        self.loading_overlay.show()
        try:
            token = await self.auth_service.request_password_reset(email)
            
            if token:
                # Dans une application réelle, on enverrait un email avec le lien de réinitialisation
                # Pour cette démo, on affiche simplement le token
                QMessageBox.information(
                    self,
                    "Demande envoyée",
                    f"Un email de réinitialisation a été envoyé à {email}.\n\n"
                    f"Pour les besoins de la démo, voici le token: {token}"
                )
                self.accept()
            else:
                QMessageBox.warning(
                    self,
                    "Email non trouvé",
                    "Aucun compte actif n'a été trouvé avec cette adresse email."
                )
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()

class PasswordResetDialog(QDialog):
    """Dialogue pour réinitialiser un mot de passe"""
    
    def __init__(self, parent=None, token=None):
        super().__init__(parent)
        self.token = token
        
        # Services
        self.db = SessionLocal()
        self.auth_service = AuthService(self.db)
        
        # Configuration de la fenêtre
        self.setWindowTitle("Définir un nouveau mot de passe")
        self.setMinimumWidth(400)
        
        # Initialisation de l'interface
        self.setup_ui()
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        
        # Message d'information
        info_label = QLabel(
            "Veuillez définir votre nouveau mot de passe."
        )
        info_label.setWordWrap(True)
        main_layout.addWidget(info_label)
        
        # Formulaire
        form_layout = QFormLayout()
        
        # Token (si non fourni)
        if not self.token:
            self.token_edit = QLineEdit()
            self.token_edit.setPlaceholderText("Token de réinitialisation")
            form_layout.addRow("Token:", self.token_edit)
        
        # Nouveau mot de passe
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("Nouveau mot de passe")
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addRow("Nouveau mot de passe:", self.password_edit)
        
        # Barre de force du mot de passe
        self.password_strength = PasswordStrengthBar()
        self.password_edit.textChanged.connect(self.password_strength.update_strength)
        form_layout.addRow("Force:", self.password_strength)
        
        # Confirmation du mot de passe
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setPlaceholderText("Confirmer le mot de passe")
        self.confirm_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addRow("Confirmation:", self.confirm_password_edit)
        
        main_layout.addLayout(form_layout)
        
        # Boutons de dialogue
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.button(QDialogButtonBox.StandardButton.Ok).setText("Réinitialiser")
        self.button_box.accepted.connect(self.reset_password)
        self.button_box.rejected.connect(self.reject)
        
        main_layout.addWidget(self.button_box)
    
    def reset_password(self):
        """Réinitialise le mot de passe"""
        # Récupérer le token si non fourni
        token = self.token
        if not token and hasattr(self, 'token_edit'):
            token = self.token_edit.text().strip()
        
        if not token:
            QMessageBox.warning(self, "Validation", "Veuillez saisir un token de réinitialisation.")
            return
        
        # Vérifier le mot de passe
        password = self.password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        
        if not password:
            QMessageBox.warning(self, "Validation", "Veuillez saisir un mot de passe.")
            return
        
        if password != confirm_password:
            QMessageBox.warning(self, "Validation", "Les mots de passe ne correspondent pas.")
            return
        
        if len(password) < 8:
            QMessageBox.warning(self, "Validation", "Le mot de passe doit contenir au moins 8 caractères.")
            return
        
        # Utiliser QTimer pour exécuter la méthode dans le thread principal
        QTimer.singleShot(0, lambda: self._reset_password_wrapper(token, password, confirm_password))
    
    def _reset_password_wrapper(self, token, password, confirm_password):
        """Wrapper pour exécuter reset_password_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.reset_password_async(token, password, confirm_password))
        finally:
            loop.close()
    
    async def reset_password_async(self, token, password, confirm_password):
        """Réinitialise le mot de passe de manière asynchrone"""
        self.loading_overlay.show()
        try:
            success = await self.auth_service.reset_password(token, password, confirm_password)
            
            if success:
                QMessageBox.information(
                    self,
                    "Mot de passe réinitialisé",
                    "Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe."
                )
                self.accept()
            else:
                QMessageBox.warning(
                    self,
                    "Échec de la réinitialisation",
                    "La réinitialisation du mot de passe a échoué. Veuillez vérifier votre token et réessayer."
                )
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()
