from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QTabWidget,
    QLabel, QLineEdit, QTextEdit, QComboBox, QDateEdit,
    QPushButton, QDialogButtonBox, QMessageBox, QListWidget,
    QListWidgetItem, QCheckBox, QGroupBox, QWidget
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime

from app.core.models.user import UserStatus, UserCreate, UserUpdate
from app.core.services.user_service import UserService
from app.core.services.permission_service import RoleService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay, PasswordStrengthBar

class UserDialog(QDialog):
    """Dialogue pour ajouter/modifier un utilisateur"""

    def __init__(self, parent=None, user_id=None):
        super().__init__(parent)
        self.user_id = user_id
        self.is_edit_mode = user_id is not None

        # Services
        self.db = SessionLocal()
        self.user_service = UserService(self.db)
        self.role_service = RoleService(self.db)

        # Données
        self.user = None
        self.roles = []
        self.selected_role_ids = []

        # Configuration de la fenêtre
        self.setWindowTitle("Nouvel utilisateur" if not self.is_edit_mode else "Modifier l'utilisateur")
        self.setMinimumWidth(600)
        self.setMinimumHeight(500)

        # Initialisation de l'interface
        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.init_data()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Onglets
        self.tab_widget = QTabWidget()

        # Onglet Informations générales
        general_tab = QWidget()
        general_layout = QFormLayout(general_tab)

        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("Email de l'utilisateur")
        general_layout.addRow("Email:", self.email_edit)

        # Nom complet
        self.full_name_edit = QLineEdit()
        self.full_name_edit.setPlaceholderText("Nom complet de l'utilisateur")
        general_layout.addRow("Nom complet:", self.full_name_edit)

        # Mot de passe (uniquement en mode création)
        if not self.is_edit_mode:
            self.password_edit = QLineEdit()
            self.password_edit.setPlaceholderText("Mot de passe")
            self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
            general_layout.addRow("Mot de passe:", self.password_edit)

            # Barre de force du mot de passe
            self.password_strength = PasswordStrengthBar()
            general_layout.addRow("Force:", self.password_strength)

            # Confirmation du mot de passe
            self.confirm_password_edit = QLineEdit()
            self.confirm_password_edit.setPlaceholderText("Confirmer le mot de passe")
            self.confirm_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
            general_layout.addRow("Confirmation:", self.confirm_password_edit)

        # Téléphone
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("Numéro de téléphone")
        general_layout.addRow("Téléphone:", self.phone_edit)

        # Poste
        self.position_edit = QLineEdit()
        self.position_edit.setPlaceholderText("Poste ou fonction")
        general_layout.addRow("Poste:", self.position_edit)

        # Département
        self.department_combo = QComboBox()
        self.department_combo.setEditable(True)
        self.department_combo.addItems(["", "Direction", "Technique", "Commercial", "Administratif", "Logistique"])
        general_layout.addRow("Département:", self.department_combo)

        # Statut
        self.status_combo = QComboBox()
        for status in UserStatus:
            self.status_combo.addItem(self._get_status_display(status), status.value)
        general_layout.addRow("Statut:", self.status_combo)

        # Actif
        self.active_checkbox = QCheckBox("Utilisateur actif")
        self.active_checkbox.setChecked(True)
        general_layout.addRow("", self.active_checkbox)

        self.tab_widget.addTab(general_tab, "Informations générales")

        # Onglet Rôles et permissions
        roles_tab = QWidget()
        roles_layout = QVBoxLayout(roles_tab)

        # Instructions
        instructions_label = QLabel(
            "Sélectionnez un ou plusieurs rôles pour cet utilisateur. "
            "Les permissions associées à ces rôles seront affichées ci-dessous."
        )
        instructions_label.setWordWrap(True)
        roles_layout.addWidget(instructions_label)

        # Liste des rôles
        roles_group = QGroupBox("Rôles disponibles (sélectionnez-en au moins un)")
        roles_group_layout = QVBoxLayout(roles_group)

        self.roles_list = QListWidget()
        self.roles_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        self.roles_list.setMinimumHeight(150)
        roles_group_layout.addWidget(self.roles_list)

        roles_layout.addWidget(roles_group)

        # Permissions héritées
        permissions_group = QGroupBox("Permissions héritées des rôles sélectionnés")
        permissions_group_layout = QVBoxLayout(permissions_group)

        permissions_info = QLabel(
            "Ces permissions seront automatiquement accordées à l'utilisateur "
            "en fonction des rôles sélectionnés ci-dessus."
        )
        permissions_info.setWordWrap(True)
        permissions_group_layout.addWidget(permissions_info)

        self.permissions_list = QListWidget()
        self.permissions_list.setSelectionMode(QListWidget.SelectionMode.NoSelection)
        self.permissions_list.setMinimumHeight(200)
        permissions_group_layout.addWidget(self.permissions_list)

        roles_layout.addWidget(permissions_group)

        self.tab_widget.addTab(roles_tab, "Rôles et permissions")

        main_layout.addWidget(self.tab_widget)

        # Boutons de dialogue
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save |
            QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self.validate_and_accept)
        self.button_box.rejected.connect(self.reject)

        main_layout.addWidget(self.button_box)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        # Connexion pour la validation avant d'accepter
        self.button_box.accepted.disconnect()
        self.button_box.accepted.connect(self.validate_and_accept)

        # Connexion pour la mise à jour de la force du mot de passe
        if not self.is_edit_mode:
            self.password_edit.textChanged.connect(self.password_strength.update_strength)

        # Connexion pour la mise à jour des permissions lorsque les rôles changent
        self.roles_list.itemSelectionChanged.connect(self._update_selected_roles)

    def init_data(self):
        """Initialise les données du dialogue"""
        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_data())
        finally:
            loop.close()

    async def load_data(self):
        """Charge les données nécessaires"""
        self.loading_overlay.show()
        try:
            # Charger les rôles
            self.roles = await self.role_service.search_roles()

            # Remplir la liste des rôles
            self.roles_list.clear()
            for role in self.roles:
                # Créer un élément de liste avec le nom du rôle et sa description
                display_text = role.name
                if role.description:
                    display_text += f" - {role.description}"

                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, role.id)

                # Marquer les rôles système
                if role.is_system:
                    item.setBackground(Qt.GlobalColor.lightGray)
                    item.setToolTip("Rôle système")

                self.roles_list.addItem(item)

            # En mode édition, charger l'utilisateur
            if self.is_edit_mode:
                self.user = await self.user_service.get_user_by_id(self.user_id)
                if self.user:
                    self._populate_form()

            # Si aucun rôle n'est disponible, afficher un message
            if self.roles_list.count() == 0:
                QMessageBox.warning(
                    self,
                    "Aucun rôle disponible",
                    "Aucun rôle n'est disponible. Veuillez créer des rôles avant d'ajouter des utilisateurs."
                )
        finally:
            self.loading_overlay.hide()

    def _populate_form(self):
        """Remplit le formulaire avec les données de l'utilisateur"""
        if not self.user:
            return

        # Informations générales
        self.email_edit.setText(self.user.email)
        self.full_name_edit.setText(self.user.full_name)
        self.phone_edit.setText(self.user.phone or "")
        self.position_edit.setText(self.user.position or "")

        # Département
        if self.user.department:
            index = self.department_combo.findText(self.user.department)
            if index >= 0:
                self.department_combo.setCurrentIndex(index)
            else:
                self.department_combo.setCurrentText(self.user.department)

        # Statut
        status_index = self.status_combo.findData(self.user.status)
        if status_index >= 0:
            self.status_combo.setCurrentIndex(status_index)

        # Actif
        self.active_checkbox.setChecked(self.user.is_active)

        # Rôles
        self.selected_role_ids = [role.role_id for role in self.user.roles]

        # Sélectionner les rôles dans la liste
        for i in range(self.roles_list.count()):
            item = self.roles_list.item(i)
            role_id = item.data(Qt.ItemDataRole.UserRole)
            if role_id in self.selected_role_ids:
                item.setSelected(True)

        # Mettre à jour les permissions en fonction des rôles sélectionnés
        QTimer.singleShot(0, self._update_permissions_wrapper)

    def _update_selected_roles(self):
        """Met à jour la liste des rôles sélectionnés"""
        self.selected_role_ids = []
        for item in self.roles_list.selectedItems():
            role_id = item.data(Qt.ItemDataRole.UserRole)
            self.selected_role_ids.append(role_id)

        # Mettre à jour la liste des permissions en utilisant QTimer pour exécuter de manière asynchrone
        QTimer.singleShot(0, self._update_permissions_wrapper)

    def _update_permissions_wrapper(self):
        """Wrapper pour exécuter _update_permissions de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._update_permissions())
        finally:
            loop.close()

    async def _update_permissions(self):
        """Met à jour la liste des permissions en fonction des rôles sélectionnés"""
        self.loading_overlay.show()
        try:
            self.permissions_list.clear()

            if not self.selected_role_ids:
                return

            # Récupérer les permissions pour chaque rôle
            permissions = set()
            for role_id in self.selected_role_ids:
                role = await self.role_service.get_role_with_permissions(role_id)
                if role:
                    for permission in role.permissions:
                        # Ajouter le nom et la catégorie pour un meilleur affichage
                        permission_text = f"{permission.name} ({permission.category})"
                        permissions.add((permission_text, permission.category))

            # Afficher les permissions, groupées par catégorie
            categories = {}
            for permission_text, category in sorted(permissions):
                if category not in categories:
                    categories[category] = []
                categories[category].append(permission_text)

            # Ajouter les permissions par catégorie
            for category in sorted(categories.keys()):
                category_item = QListWidgetItem(f"--- {category.upper()} ---")
                category_item.setFlags(Qt.ItemFlag.NoItemFlags)  # Non sélectionnable
                category_item.setBackground(Qt.GlobalColor.lightGray)
                self.permissions_list.addItem(category_item)

                for permission in sorted(categories[category]):
                    self.permissions_list.addItem(permission)
        finally:
            self.loading_overlay.hide()

    def validate_and_accept(self):
        """Valide les données et accepte le dialogue"""
        # Vérifier que les champs obligatoires sont remplis
        if not self.email_edit.text().strip():
            QMessageBox.warning(self, "Validation", "Veuillez saisir un email.")
            return

        if not self.full_name_edit.text().strip():
            QMessageBox.warning(self, "Validation", "Veuillez saisir un nom complet.")
            return

        # En mode création, vérifier le mot de passe
        if not self.is_edit_mode:
            if not self.password_edit.text():
                QMessageBox.warning(self, "Validation", "Veuillez saisir un mot de passe.")
                return

            if self.password_edit.text() != self.confirm_password_edit.text():
                QMessageBox.warning(self, "Validation", "Les mots de passe ne correspondent pas.")
                return

            if len(self.password_edit.text()) < 8:
                QMessageBox.warning(self, "Validation", "Le mot de passe doit contenir au moins 8 caractères.")
                return

        # Vérifier qu'au moins un rôle est sélectionné
        if not self.selected_role_ids:
            QMessageBox.warning(self, "Validation", "Veuillez sélectionner au moins un rôle.")
            return

        # Sauvegarder l'utilisateur en utilisant un wrapper
        self._save_user_wrapper()

    def _save_user_wrapper(self):
        """Wrapper pour exécuter save_user de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.save_user())
        finally:
            loop.close()

    async def save_user(self):
        """Sauvegarde l'utilisateur"""
        self.loading_overlay.show()
        try:
            # Récupérer les données du formulaire
            email = self.email_edit.text().strip()
            full_name = self.full_name_edit.text().strip()
            phone = self.phone_edit.text().strip() or None
            position = self.position_edit.text().strip() or None
            department = self.department_combo.currentText().strip() or None
            status = UserStatus(self.status_combo.currentData())
            is_active = self.active_checkbox.isChecked()

            # Créer ou mettre à jour l'utilisateur
            if self.is_edit_mode:
                # Mettre à jour l'utilisateur existant
                user_data = UserUpdate(
                    email=email,
                    full_name=full_name,
                    phone=phone,
                    position=position,
                    department=department,
                    status=status,
                    is_active=is_active,
                    role_ids=self.selected_role_ids
                )

                await self.user_service.update_user(
                    self.user_id,
                    user_data,
                    updated_by_id=1  # TODO: Récupérer l'ID de l'utilisateur connecté
                )
            else:
                # Créer un nouvel utilisateur
                password = self.password_edit.text()

                user_data = UserCreate(
                    email=email,
                    password=password,
                    full_name=full_name,
                    phone=phone,
                    position=position,
                    department=department,
                    role_ids=self.selected_role_ids
                )

                await self.user_service.create_user(
                    user_data,
                    created_by_id=1  # TODO: Récupérer l'ID de l'utilisateur connecté
                )

            # Fermer le dialogue
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def _get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            UserStatus.ACTIVE: "Actif",
            UserStatus.INACTIVE: "Inactif",
            UserStatus.SUSPENDED: "Suspendu",
            UserStatus.PENDING: "En attente"
        }
        return status_display.get(status, str(status))
