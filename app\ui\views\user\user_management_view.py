from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTableWidget, QTableWidgetItem, QDialog, QFormLayout,
                           QLineEdit, QComboBox, QCheckBox, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal

class UserDialog(QDialog):
    def __init__(self, parent=None, user_data=None):
        super().__init__(parent)
        self.user_data = user_data
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("Ajouter/Modifier Utilisateur")
        layout = QFormLayout()
        
        # Champs du formulaire
        self.email_input = QLineEdit()
        self.full_name_input = QLineEdit()
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.role_combo = QComboBox()
        self.role_combo.addItems(["Admin", "Manager", "Technicien", "Commercial"])
        self.active_checkbox = QCheckBox("Compte actif")
        
        layout.addRow("Email:", self.email_input)
        layout.addRow("Nom complet:", self.full_name_input)
        layout.addRow("Mot de passe:", self.password_input)
        layout.addRow("Rôle:", self.role_combo)
        layout.addRow("", self.active_checkbox)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        save_button = QPushButton("Enregistrer")
        cancel_button = QPushButton("Annuler")
        
        save_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        layout.addRow("", buttons_layout)
        
        self.setLayout(layout)
        
        # Remplir les champs si modification
        if self.user_data:
            self.email_input.setText(self.user_data.get("email", ""))
            self.full_name_input.setText(self.user_data.get("full_name", ""))
            self.role_combo.setCurrentText(self.user_data.get("role", ""))
            self.active_checkbox.setChecked(self.user_data.get("is_active", True))
            self.password_input.setPlaceholderText("Laisser vide pour ne pas modifier")

class UserManagementView(QWidget):
    userUpdated = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Barre d'outils
        toolbar = QHBoxLayout()
        add_button = QPushButton("Ajouter")
        add_button.clicked.connect(self.add_user)
        toolbar.addWidget(add_button)
        
        # Table des utilisateurs
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(["Email", "Nom", "Rôle", "Statut", "Actions"])
        
        layout.addLayout(toolbar)
        layout.addWidget(self.table)
        
        self.setLayout(layout)
        
    def add_user(self):
        dialog = UserDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Récupérer les données et les envoyer au contrôleur
            pass
            
    def edit_user(self, user_data):
        dialog = UserDialog(self, user_data)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Récupérer les données et les envoyer au contrôleur
            pass
            
    def delete_user(self, user_id):
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Voulez-vous vraiment supprimer cet utilisateur ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Envoyer la demande de suppression au contrôleur
            pass
            
    def refresh_table(self, users):
        self.table.setRowCount(len(users))
        for row, user in enumerate(users):
            self.table.setItem(row, 0, QTableWidgetItem(user["email"]))
            self.table.setItem(row, 1, QTableWidgetItem(user["full_name"]))
            self.table.setItem(row, 2, QTableWidgetItem(user["role"]))
            self.table.setItem(row, 3, QTableWidgetItem(
                "Actif" if user["is_active"] else "Inactif"
            ))
            
            # Boutons d'action
            actions_widget = QWidget()
            actions_layout = QHBoxLayout()
            edit_button = QPushButton("Modifier")
            delete_button = QPushButton("Supprimer")
            
            edit_button.clicked.connect(lambda: self.edit_user(user))
            delete_button.clicked.connect(lambda: self.delete_user(user["id"]))
            
            actions_layout.addWidget(edit_button)
            actions_layout.addWidget(delete_button)
            actions_widget.setLayout(actions_layout)
            
            self.table.setCellWidget(row, 4, actions_widget)