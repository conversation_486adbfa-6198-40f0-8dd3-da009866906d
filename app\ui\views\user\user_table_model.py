from PyQt6.QtCore import Qt, QAbstractTableModel, QModelIndex
import asyncio
from datetime import datetime

from app.core.models.user import User, UserStatus
from app.core.services.user_service import UserService
from app.utils.database import SessionLocal
from sqlalchemy.orm import Session

class UserTableModel(QAbstractTableModel):
    """Modèle de tableau pour les utilisateurs"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.service = UserService(self.db)
        self.users = []
        self.headers = ["ID", "Nom", "Email", "Statut", "Département", "Dernière connexion"]

    def __del__(self):
        """Destructeur pour fermer proprement la session"""
        if hasattr(self, 'db') and self.db:
            try:
                self.db.close()
                print("UserTableModel: Session de base de données fermée")
            except Exception as e:
                print(f"Erreur lors de la fermeture de la session: {e}")

    async def load_data(self, search_term=None, status=None, role_id=None, department=None):
        """Charge les données des utilisateurs"""
        self.beginResetModel()

        # Forcer une nouvelle session pour éviter les problèmes de cache
        if hasattr(self, 'db') and self.db:
            self.db.close()
        self.db = SessionLocal()
        self.service = UserService(self.db)

        # Charger les utilisateurs
        self.users = await self.service.search_users(
            search_term=search_term,
            status=status,
            role_id=role_id,
            department=department
        )

        # Afficher les dates de dernière connexion pour le débogage
        for user in self.users:
            print(f"Utilisateur {user.email}: dernière connexion = {user.last_login}")

            # Forcer une requête fraîche pour chaque utilisateur
            try:
                fresh_user = self.db.query(User).filter(User.id == user.id).first()
                if fresh_user and fresh_user.last_login:
                    user.last_login = fresh_user.last_login
                    print(f"Mise à jour de la dernière connexion pour {user.email}: {user.last_login}")
            except Exception as e:
                print(f"Erreur lors de la mise à jour de la dernière connexion: {e}")

        self.endResetModel()

    def rowCount(self, parent=QModelIndex()):
        """Retourne le nombre de lignes"""
        return len(self.users)

    def columnCount(self, parent=QModelIndex()):
        """Retourne le nombre de colonnes"""
        return len(self.headers)

    def data(self, index, role):
        """Retourne les données pour l'affichage"""
        if not index.isValid() or index.row() >= len(self.users):
            return None

        user = self.users[index.row()]

        if role == Qt.ItemDataRole.DisplayRole:
            if index.column() == 0:
                # Gestion sécurisée de l'accès à l'ID pour éviter DetachedInstanceError
                try:
                    return str(user.id)
                except Exception as e:
                    print(f"Erreur lors de l'accès à l'ID utilisateur: {e}")
                    # Essayer de rafraîchir l'objet depuis la base de données
                    try:
                        if hasattr(self, 'db') and self.db:
                            fresh_user = self.db.query(User).filter(User.id == getattr(user, 'id', None)).first()
                            if fresh_user:
                                self.users[index.row()] = fresh_user
                                return str(fresh_user.id)
                    except Exception as e2:
                        print(f"Erreur lors du rafraîchissement de l'utilisateur: {e2}")
                    return "Erreur"
            elif index.column() == 1:
                try:
                    return user.full_name
                except Exception:
                    return "Erreur"
            elif index.column() == 2:
                try:
                    return user.email
                except Exception:
                    return "Erreur"
            elif index.column() == 3:
                try:
                    return self._get_status_display(user.status)
                except Exception:
                    return "Erreur"
            elif index.column() == 4:
                try:
                    return user.department or ""
                except Exception:
                    return "Erreur"
            elif index.column() == 5:
                # Récupérer la date de dernière connexion
                try:
                    # D'abord essayer avec l'objet user actuel
                    if user.last_login:
                        return self._format_datetime(user.last_login)

                    # Si pas de date, essayer de récupérer depuis la base de données
                    if hasattr(self, 'db') and self.db:
                        try:
                            fresh_user = self.db.query(User).filter(User.id == user.id).first()
                            if fresh_user and fresh_user.last_login:
                                # Date trouvée dans la base de données (mise à jour récente)
                                return self._format_datetime(fresh_user.last_login)
                        except Exception as e:
                            # Erreur lors de la récupération, mais ce n'est pas critique
                            pass

                    # Pas de date de dernière connexion : utilisateur jamais connecté
                    return "Jamais connecté"

                except Exception as e:
                    print(f"Erreur lors de la récupération de la date de dernière connexion pour {user.email}: {e}")
                    return "Erreur"

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            if index.column() == 0:
                return int(Qt.AlignmentFlag.AlignCenter)
            elif index.column() in [3, 5]:
                return int(Qt.AlignmentFlag.AlignCenter)
            else:
                return int(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)

        elif role == Qt.ItemDataRole.BackgroundRole:
            if not user.is_active:
                return Qt.GlobalColor.lightGray

        return None

    def headerData(self, section, orientation, role):
        """Retourne les en-têtes du tableau"""
        if role == Qt.ItemDataRole.DisplayRole and orientation == Qt.Orientation.Horizontal:
            return self.headers[section]
        return None

    def get_user(self, row):
        """Retourne l'utilisateur à la ligne spécifiée"""
        if 0 <= row < len(self.users):
            return self.users[row]
        return None

    def get_user_id(self, row):
        """Retourne l'ID de l'utilisateur à la ligne spécifiée"""
        user = self.get_user(row)
        if user:
            return user.id
        return None

    def _get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            UserStatus.ACTIVE: "Actif",
            UserStatus.INACTIVE: "Inactif",
            UserStatus.SUSPENDED: "Suspendu",
            UserStatus.PENDING: "En attente"
        }
        return status_display.get(status, str(status))

    def _format_datetime(self, dt):
        """Formate une date/heure pour l'affichage"""
        if dt is None:
            return "Jamais connecté"

        if isinstance(dt, datetime):
            return dt.strftime("%d/%m/%Y %H:%M")

        # Si c'est une chaîne de caractères, essayer de la convertir en datetime
        if isinstance(dt, str):
            # Essayer différents formats de date
            formats = [
                "%Y-%m-%d %H:%M:%S.%f",  # Format avec microsecondes
                "%Y-%m-%dT%H:%M:%S.%f",  # Format ISO avec microsecondes
                "%Y-%m-%d %H:%M:%S",     # Format sans microsecondes
                "%Y-%m-%dT%H:%M:%S",     # Format ISO sans microsecondes
                "%Y-%m-%d"               # Format date uniquement
            ]

            for fmt in formats:
                try:
                    parsed_dt = datetime.strptime(dt, fmt)
                    if fmt == "%Y-%m-%d":
                        return parsed_dt.strftime("%d/%m/%Y")
                    else:
                        return parsed_dt.strftime("%d/%m/%Y %H:%M")
                except ValueError:
                    continue

        # Si tout échoue, retourner la chaîne brute
        return str(dt)
