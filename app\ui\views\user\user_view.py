from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView, QSplitter,
    QPushButton, QLineEdit, QComboBox, QLabel, QFrame,
    QMessageBox, QTabWidget, QHeaderView
)
from PyQt6.QtCore import Qt, QSortFilterProxyModel, QTimer
from PyQt6.QtGui import QIcon
import asyncio

from .user_table_model import UserTableModel
from .dialogs.user_dialog import UserDialog
from .dialogs.password_reset_dialog import PasswordResetRequestDialog, PasswordResetDialog
from .dialogs.change_password_dialog import ChangePasswordDialog
from .dialogs.two_factor_dialog import TwoFactorSetupWizard, TwoFactorDisableDialog
from app.ui.components.custom_widgets import SearchBar, FilterComboBox, LoadingOverlay
from app.core.models.user import UserStatus
from app.core.services.user_service import UserService
from app.core.services.permission_service import RoleService
from app.utils.database import SessionLocal

class UserView(QWidget):
    """Vue principale pour la gestion des utilisateurs"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.user_service = UserService(self.db)
        self.role_service = RoleService(self.db)

        # Configuration de l'interface
        self.setup_ui()
        self.setup_connections()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Charger les données
        self.init_data()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # En-tête
        header = QLabel("Gestion des Utilisateurs")
        header.setObjectName("sectionHeader")
        main_layout.addWidget(header)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        # Bouton d'ajout d'utilisateur
        self.add_user_button = QPushButton("Nouvel Utilisateur")
        self.add_user_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_user_button.setObjectName("primaryButton")
        toolbar_layout.addWidget(self.add_user_button)

        # Bouton de réinitialisation de mot de passe
        self.reset_password_button = QPushButton("Réinitialiser Mot de Passe")
        self.reset_password_button.setIcon(QIcon("app/ui/resources/icons/key.svg"))
        toolbar_layout.addWidget(self.reset_password_button)

        # Espacement
        toolbar_layout.addStretch()

        # Filtres
        self.search_bar = SearchBar()
        self.search_bar.setPlaceholderText("Rechercher un utilisateur...")
        toolbar_layout.addWidget(self.search_bar)

        self.status_filter = FilterComboBox()
        self.status_filter.addItem("Tous les statuts", None)
        for status in UserStatus:
            self.status_filter.addItem(self._get_status_display(status), status.value)
        toolbar_layout.addWidget(self.status_filter)

        self.role_filter = FilterComboBox()
        self.role_filter.addItem("Tous les rôles", None)
        toolbar_layout.addWidget(self.role_filter)

        main_layout.addLayout(toolbar_layout)

        # Tableau des utilisateurs
        self.table_view = QTableView()
        self.table_view.setObjectName("usersTable")
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.setAlternatingRowColors(True)

        # Modèle de données
        self.table_model = UserTableModel()
        self.proxy_model = QSortFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.table_view.setModel(self.proxy_model)

        # Ajuster les colonnes
        self.table_view.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # ID
        self.table_view.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Nom
        self.table_view.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # Email

        main_layout.addWidget(self.table_view)

        # Boutons d'action
        actions_layout = QHBoxLayout()

        # Bouton de rafraîchissement
        self.refresh_button = QPushButton("Rafraîchir")
        self.refresh_button.setIcon(QIcon("app/ui/resources/icons/refresh.svg"))
        self.refresh_button.setStyleSheet("background-color: #3498db; color: #FFFFFF; font-weight: bold; border: 1px solid #2980b9;")
        actions_layout.addWidget(self.refresh_button)

        self.edit_user_button = QPushButton("Modifier")
        self.edit_user_button.setIcon(QIcon("app/ui/resources/icons/edit.svg"))
        actions_layout.addWidget(self.edit_user_button)

        self.change_password_button = QPushButton("Changer Mot de Passe")
        self.change_password_button.setIcon(QIcon("app/ui/resources/icons/key.svg"))
        actions_layout.addWidget(self.change_password_button)

        self.enable_2fa_button = QPushButton("Activer 2FA")
        self.enable_2fa_button.setIcon(QIcon("app/ui/resources/icons/shield.svg"))
        actions_layout.addWidget(self.enable_2fa_button)

        self.disable_2fa_button = QPushButton("Désactiver 2FA")
        self.disable_2fa_button.setIcon(QIcon("app/ui/resources/icons/shield-off.svg"))
        actions_layout.addWidget(self.disable_2fa_button)

        self.activate_button = QPushButton("Activer")
        self.activate_button.setIcon(QIcon("app/ui/resources/icons/check.svg"))
        actions_layout.addWidget(self.activate_button)

        self.deactivate_button = QPushButton("Désactiver")
        self.deactivate_button.setIcon(QIcon("app/ui/resources/icons/cancel.svg"))
        actions_layout.addWidget(self.deactivate_button)

        # Bouton de suppression
        self.delete_button = QPushButton("Supprimer")
        self.delete_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
        self.delete_button.setStyleSheet("background-color: #e74c3c; color: #FFFFFF; font-weight: bold; border: 1px solid #c0392b;")
        actions_layout.addWidget(self.delete_button)

        main_layout.addLayout(actions_layout)

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.refresh_button.clicked.connect(self.refresh_data)
        self.add_user_button.clicked.connect(self.show_add_user_dialog)
        self.edit_user_button.clicked.connect(self.show_edit_user_dialog)
        self.change_password_button.clicked.connect(self.show_change_password_dialog)
        self.enable_2fa_button.clicked.connect(self.show_enable_2fa_dialog)
        self.disable_2fa_button.clicked.connect(self.show_disable_2fa_dialog)
        self.activate_button.clicked.connect(self.activate_user)
        self.deactivate_button.clicked.connect(self.deactivate_user)
        self.delete_button.clicked.connect(self.delete_user)

        self.search_bar.textChanged.connect(self.filter_users)
        self.status_filter.currentIndexChanged.connect(self.filter_users)
        self.role_filter.currentIndexChanged.connect(self.filter_users)

        self.table_view.doubleClicked.connect(self.show_edit_user_dialog)
        self.table_view.selectionModel().selectionChanged.connect(self._update_button_states)

    def refresh_data(self):
        """Rafraîchit les données de la vue"""
        print("Rafraîchissement des données...")
        # Forcer une nouvelle session pour éviter les problèmes de cache
        if hasattr(self, 'db') and self.db:
            self.db.close()
        self.db = SessionLocal()
        self.user_service = UserService(self.db)
        self.role_service = RoleService(self.db)

        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)

    def init_data(self):
        """Initialise les données de la vue"""
        # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        try:
            # Essayer d'utiliser le loop existant
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Si le loop tourne déjà, utiliser create_task
                task = asyncio.create_task(self.load_data())
                # Connecter le callback pour la fin de la tâche
                task.add_done_callback(self._on_load_complete)
            else:
                # Si pas de loop en cours, en créer un nouveau
                loop.run_until_complete(self.load_data())
        except RuntimeError:
            # Fallback: créer un nouveau loop dans un thread séparé
            import threading
            thread = threading.Thread(target=self._load_data_in_thread)
            thread.daemon = True
            thread.start()

    def _on_load_complete(self, task):
        """Callback appelé quand la tâche asynchrone est terminée"""
        try:
            # Récupérer le résultat ou l'exception
            task.result()
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Masquer l'overlay de chargement dans le thread principal
            QTimer.singleShot(0, self.loading_overlay.hide)

    def _load_data_in_thread(self):
        """Charge les données dans un thread séparé"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.load_data())
            finally:
                loop.close()
        except Exception as e:
            print(f"Erreur lors du chargement des données dans le thread: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Masquer l'overlay de chargement dans le thread principal
            QTimer.singleShot(0, self.loading_overlay.hide)

    async def load_data(self):
        """Charge les données des utilisateurs et des rôles"""
        self.loading_overlay.show()
        try:
            # Charger les rôles pour le filtre
            roles = await self.role_service.search_roles()

            # Mettre à jour le filtre de rôles dans le thread principal
            QTimer.singleShot(0, lambda: self._update_role_filter(roles))

            # Charger les utilisateurs
            await self.table_model.load_data()

            # Ajuster les colonnes et mettre à jour l'état des boutons dans le thread principal
            QTimer.singleShot(0, self._update_ui_after_load)
        finally:
            # Masquer l'overlay dans le thread principal
            QTimer.singleShot(0, self.loading_overlay.hide)

    def _update_role_filter(self, roles):
        """Met à jour le filtre de rôles dans le thread principal"""
        self.role_filter.clear()
        self.role_filter.addItem("Tous les rôles", None)
        for role in roles:
            self.role_filter.addItem(role.name, role.id)

    def _update_ui_after_load(self):
        """Met à jour l'interface après le chargement des données"""
        # Ajuster les colonnes
        self.table_view.resizeColumnsToContents()
        # Mettre à jour l'état des boutons
        self._update_button_states()

    def filter_users(self):
        """Applique les filtres sur le tableau des utilisateurs"""
        search_text = self.search_bar.text()
        status = self.status_filter.currentData()
        role_id = self.role_filter.currentData()

        # Utiliser QTimer pour exécuter la méthode dans le thread principal
        QTimer.singleShot(0, lambda: self._filter_users_wrapper(search_text, status, role_id))

    def _filter_users_wrapper(self, search_text, status, role_id):
        """Wrapper pour exécuter filter_users_async de manière asynchrone"""
        try:
            import threading
            loop = None
            try:
                # Si un loop tourne déjà dans ce thread, planifier la coroutine dessus
                loop = asyncio.get_running_loop()
            except RuntimeError:
                loop = None

            if loop and loop.is_running():
                # Planifie l'exécution sans bloquer le loop existant
                asyncio.ensure_future(self.filter_users_async(search_text, status, role_id), loop=loop)
            else:
                # Exécute dans un nouveau loop si aucun loop actif
                new_loop = asyncio.new_event_loop()
                try:
                    asyncio.set_event_loop(new_loop)
                    new_loop.run_until_complete(self.filter_users_async(search_text, status, role_id))
                finally:
                    new_loop.close()
        except Exception as e:
            # Log minimal si besoin
            print(f"Erreur filter_users_wrapper: {e}")

    async def filter_users_async(self, search_text, status, role_id):
        """Applique les filtres sur le tableau des utilisateurs de manière asynchrone"""
        self.loading_overlay.show()
        try:
            # Charger les utilisateurs avec les filtres
            await self.table_model.load_data(
                search_term=search_text if search_text else None,
                status=status,
                role_id=role_id
            )

            # Mettre à jour l'état des boutons
            self._update_button_states()
        finally:
            self.loading_overlay.hide()

    def show_add_user_dialog(self):
        """Affiche la boîte de dialogue d'ajout d'utilisateur"""
        dialog = UserDialog(self)
        if dialog.exec():
            self.filter_users()

    def show_edit_user_dialog(self):
        """Affiche la boîte de dialogue d'édition d'utilisateur"""
        selected_row = self._get_selected_row()
        if selected_row < 0:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner un utilisateur.")
            return

        user_id = self.table_model.get_user_id(selected_row)
        dialog = UserDialog(self, user_id=user_id)
        if dialog.exec():
            self.filter_users()

    def show_reset_password_dialog(self):
        """Affiche la boîte de dialogue de réinitialisation de mot de passe"""
        dialog = PasswordResetRequestDialog(self)
        dialog.exec()

    def show_change_password_dialog(self):
        """Affiche la boîte de dialogue de changement de mot de passe"""
        selected_row = self._get_selected_row()
        if selected_row < 0:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner un utilisateur.")
            return

        user_id = self.table_model.get_user_id(selected_row)
        dialog = ChangePasswordDialog(self, user_id=user_id)
        dialog.exec()

    def show_enable_2fa_dialog(self):
        """Affiche l'assistant d'activation de l'authentification à deux facteurs"""
        selected_row = self._get_selected_row()
        if selected_row < 0:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner un utilisateur.")
            return

        user = self.table_model.get_user(selected_row)
        if user.two_factor_enabled:
            QMessageBox.information(
                self,
                "Déjà activé",
                "L'authentification à deux facteurs est déjà activée pour cet utilisateur."
            )
            return

        wizard = TwoFactorSetupWizard(self, user_id=user.id)
        wizard.exec()

    def show_disable_2fa_dialog(self):
        """Affiche la boîte de dialogue de désactivation de l'authentification à deux facteurs"""
        selected_row = self._get_selected_row()
        if selected_row < 0:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner un utilisateur.")
            return

        user = self.table_model.get_user(selected_row)
        if not user.two_factor_enabled:
            QMessageBox.information(
                self,
                "Déjà désactivé",
                "L'authentification à deux facteurs n'est pas activée pour cet utilisateur."
            )
            return

        dialog = TwoFactorDisableDialog(self, user_id=user.id)
        if dialog.exec():
            self.filter_users()

    def activate_user(self):
        """Active l'utilisateur sélectionné"""
        selected_row = self._get_selected_row()
        if selected_row < 0:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner un utilisateur.")
            return

        user = self.table_model.get_user(selected_row)
        if user.is_active:
            QMessageBox.information(
                self,
                "Déjà actif",
                "Cet utilisateur est déjà actif."
            )
            return

        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            f"Voulez-vous vraiment activer l'utilisateur {user.full_name} ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Utiliser QTimer pour exécuter la méthode dans le thread principal
            QTimer.singleShot(0, lambda: self._activate_user_wrapper(user.id))

    def _activate_user_wrapper(self, user_id):
        """Wrapper pour exécuter activate_user_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.activate_user_async(user_id))
        finally:
            loop.close()

    async def activate_user_async(self, user_id):
        """Active l'utilisateur de manière asynchrone"""
        self.loading_overlay.show()
        try:
            # Activer l'utilisateur
            success = await self.user_service.activate_user(
                user_id,
                activated_by_id=1  # TODO: Récupérer l'ID de l'utilisateur connecté
            )

            if success:
                QMessageBox.information(
                    self,
                    "Activation réussie",
                    "L'utilisateur a été activé avec succès."
                )
                self.filter_users()
            else:
                QMessageBox.warning(
                    self,
                    "Échec de l'activation",
                    "L'activation a échoué."
                )
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def deactivate_user(self):
        """Désactive l'utilisateur sélectionné"""
        selected_row = self._get_selected_row()
        if selected_row < 0:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner un utilisateur.")
            return

        user = self.table_model.get_user(selected_row)
        if not user.is_active:
            QMessageBox.information(
                self,
                "Déjà inactif",
                "Cet utilisateur est déjà inactif."
            )
            return

        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            f"Voulez-vous vraiment désactiver l'utilisateur {user.full_name} ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Utiliser QTimer pour exécuter la méthode dans le thread principal
            QTimer.singleShot(0, lambda: self._deactivate_user_wrapper(user.id))

    def _deactivate_user_wrapper(self, user_id):
        """Wrapper pour exécuter deactivate_user_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.deactivate_user_async(user_id))
        finally:
            loop.close()

    async def deactivate_user_async(self, user_id):
        """Désactive l'utilisateur de manière asynchrone"""
        self.loading_overlay.show()
        try:
            # Désactiver l'utilisateur
            success = await self.user_service.deactivate_user(
                user_id,
                deactivated_by_id=1  # TODO: Récupérer l'ID de l'utilisateur connecté
            )

            if success:
                QMessageBox.information(
                    self,
                    "Désactivation réussie",
                    "L'utilisateur a été désactivé avec succès."
                )
                self.filter_users()
            else:
                QMessageBox.warning(
                    self,
                    "Échec de la désactivation",
                    "La désactivation a échoué."
                )
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def _get_selected_row(self):
        """Retourne l'index de la ligne sélectionnée dans le modèle source"""
        selected_indexes = self.table_view.selectionModel().selectedRows()
        if not selected_indexes:
            return -1

        proxy_index = selected_indexes[0]
        source_index = self.proxy_model.mapToSource(proxy_index)
        return source_index.row()

    def delete_user(self):
        """Supprime l'utilisateur sélectionné"""
        selected_row = self._get_selected_row()
        if selected_row < 0:
            QMessageBox.warning(self, "Sélection", "Veuillez sélectionner un utilisateur.")
            return

        user = self.table_model.get_user(selected_row)

        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation de suppression",
            f"Voulez-vous vraiment supprimer l'utilisateur {user.full_name} ?\n\nCette action est irréversible.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Utiliser QTimer pour exécuter la méthode dans le thread principal
            QTimer.singleShot(0, lambda: self._delete_user_wrapper(user.id))

    def _delete_user_wrapper(self, user_id):
        """Wrapper pour exécuter delete_user_async de manière asynchrone"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.delete_user_async(user_id))
        finally:
            loop.close()

    async def delete_user_async(self, user_id):
        """Supprime l'utilisateur de manière asynchrone"""
        self.loading_overlay.show()
        try:
            # Supprimer l'utilisateur
            success = await self.user_service.delete_user(
                user_id,
                deleted_by_id=1  # TODO: Récupérer l'ID de l'utilisateur connecté
            )

            if success:
                QMessageBox.information(
                    self,
                    "Suppression réussie",
                    "L'utilisateur a été supprimé avec succès."
                )
                self.filter_users()
            else:
                QMessageBox.warning(
                    self,
                    "Échec de la suppression",
                    "La suppression a échoué."
                )
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def _update_button_states(self):
        """Met à jour l'état des boutons en fonction de la sélection"""
        selected_row = self._get_selected_row()
        has_selection = selected_row >= 0

        self.edit_user_button.setEnabled(has_selection)
        self.change_password_button.setEnabled(has_selection)
        self.enable_2fa_button.setEnabled(has_selection)
        self.disable_2fa_button.setEnabled(has_selection)
        self.activate_button.setEnabled(has_selection)
        self.deactivate_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)

        if has_selection:
            user = self.table_model.get_user(selected_row)
            self.activate_button.setEnabled(not user.is_active)
            self.deactivate_button.setEnabled(user.is_active)
            self.enable_2fa_button.setEnabled(not user.two_factor_enabled)
            self.disable_2fa_button.setEnabled(user.two_factor_enabled)

    def _get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            UserStatus.ACTIVE: "Actif",
            UserStatus.INACTIVE: "Inactif",
            UserStatus.SUSPENDED: "Suspendu",
            UserStatus.PENDING: "En attente"
        }
        return status_display.get(status, str(status))
