from PyQt6.QtWidgets import (Q<PERSON>ain<PERSON><PERSON>ow, QStackedWidget, QToolBar, QStatusBar, QMenu,
                           QWidget, QVBoxLayout, QSizePolicy, QLabel, QPushButton,
                           QHBoxLayout, QMessageBox, QToolButton, QFrame, QScrollArea)
from PyQt6.QtCore import Qt, QSize, pyqtSlot, pyqtSignal
from PyQt6.QtGui import QIcon, QAction, QFont

# Importer les icônes Unicode
from app.ui.components.unicode_icons import UnicodeIcons
from app.controllers.auth_controller import AuthController
from app.ui.views.dashboard.dashboard_view import DashboardView
from app.ui.views.inventory.inventory_view import InventoryView
from app.ui.views.inventory.category_view import CategoryView
from app.ui.views.repair.repair_view import RepairView
from app.ui.views.customer.customer_view import CustomerView
from app.ui.views.customer.customer_finance_view import CustomerFinanceView
from app.ui.views.supplier.supplier_view import SupplierView
from app.ui.views.equipment.equipment_view import EquipmentView
from app.ui.views.reporting.reporting_view import ReportingView
from app.ui.views.purchasing.purchasing_view import PurchasingView
from app.ui.views.user.user_view import UserView
from app.ui.views.sale.sale_view import SaleView
from app.ui.views.treasury.treasury_view import TreasuryView
from app.ui.views.settings.settings_view import SettingsView
from app.ui.components.notification_center import NotificationButton, NotificationCenter
from app.ui.theme.theme_manager import ThemeManager
from app.ui.window_cleanup import cleanup_toolbar
from app.ui.resources import resources_rc as _

from PyQt6.QtWidgets import QToolButton

class ModernNavigationButton(QToolButton):
    """Bouton de navigation moderne avec icône et texte sous l'icône"""
    def __init__(self, icon_path, text, parent=None):
        super().__init__(parent)
        self.setIcon(QIcon(icon_path))
        self.setText(text)
        self.setIconSize(QSize(32, 32))
        self.setFixedSize(100, 70)
        self.setCheckable(True)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        self.setStyleSheet("""
            QToolButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 6px 2px 2px 2px;
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                text-align: center;
                min-height: 70px;
                min-width: 100px;
            }
            QToolButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e3f2fd, stop:1 #bbdefb);
                border: 2px solid #2196F3;
                color: #1976D2;
            }
            QToolButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #2196F3, stop:1 #1976D2);
                border: 2px solid #1565C0;
                color: white;
            }
            QToolButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1976D2, stop:1 #1565C0);
                border: 2px solid #0D47A1;
            }
        """)

class MainWindow(QMainWindow):
    # Signaux pour la déconnexion et le changement d'utilisateur
    logoutRequested = pyqtSignal()
    switchUserRequested = pyqtSignal()

    def __init__(self, user_info=None):
        super().__init__()
        self.setWindowTitle("Nadjib-GSM - Gestionnaire de stocks et de maintenance")
        self.setMinimumSize(1200, 800)

        # Définir l'icône de la fenêtre
        self.setWindowIcon(QIcon("app/ui/resources/images/app.ico"))

        # Stocker les informations de l'utilisateur
        self.user_info = user_info

        # Créer le contrôleur d'authentification
        self.auth_controller = AuthController()
        if user_info:
            self.auth_controller.current_user = user_info

        # Configuration de l'interface
        self._setup_ui()
        self._create_modern_navigation()
        self._create_statusbar()
        self._create_user_menu()
        self._setup_navigation()

        # Chargement du thème par défaut
        self._load_theme()

        # Appliquer les restrictions de permissions
        self._apply_role_restrictions()

        # Appliquer les permissions sur les vues principales (APRÈS la création des vues)
        self._apply_view_permissions()
        
        # Sélectionner le dashboard par défaut
        self._select_navigation_button('dashboard')

    def _apply_view_permissions(self):
        """Active/désactive les boutons d'action dans chaque vue selon les permissions utilisateur."""
        auth = self.auth_controller

        # Vérifier que l'auth_controller est correctement configuré
        if not hasattr(auth, 'has_permission') or not auth.current_user:
            print("[DEBUG] AuthController non configuré correctement")
            return

        print(f"[DEBUG] Application des permissions pour l'utilisateur: {auth.current_user.get('email', 'Inconnu')}")
        print(f"[DEBUG] Permissions utilisateur: {auth.current_user.get('permissions', [])}")

        # CustomerView
        if hasattr(self, 'customer') and hasattr(self.customer, 'apply_permissions'):
            self.customer.apply_permissions(auth)
            print("[DEBUG] Permissions appliquées à CustomerView")

        # InventoryView
        if hasattr(self, 'inventory') and hasattr(self.inventory, 'apply_permissions'):
            self.inventory.apply_permissions(auth)
            print("[DEBUG] Permissions appliquées à InventoryView")

        # RepairView
        if hasattr(self, 'repair') and hasattr(self.repair, 'apply_permissions'):
            self.repair.apply_permissions(auth)
            print("[DEBUG] Permissions appliquées à RepairView")

        # SaleView
        if hasattr(self, 'sale') and hasattr(self.sale, 'apply_permissions'):
            self.sale.apply_permissions(auth)
            print("[DEBUG] Permissions appliquées à SaleView")

    def refresh_permissions(self):
        """Méthode publique pour réappliquer les permissions après une connexion ou un changement de rôle"""
        print("[DEBUG] Rafraîchissement des permissions...")
        self._apply_role_restrictions()
        self._apply_view_permissions()

    def _setup_ui(self):
        """Configure les éléments principaux de l'interface."""
        # Widget central avec layout vertical
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Zone de contenu principal
        self.central_widget = QStackedWidget()
        self.central_widget.setStyleSheet("""
            QStackedWidget {
                background: white;
                border: none;
            }
        """)

        # Ajout des vues principales
        self.dashboard = DashboardView()
        self.inventory = InventoryView()
        self.category = CategoryView()
        self.repair = RepairView()
        self.customer = CustomerView()
        self.customer_finance = CustomerFinanceView()
        self.supplier = SupplierView()
        self.equipment = EquipmentView()
        self.reporting = ReportingView()
        self.purchasing = PurchasingView()
        self.sale = SaleView()
        self.user = UserView()
        self.treasury = TreasuryView()
        self.settings = SettingsView()

        self.central_widget.addWidget(self.dashboard)
        self.central_widget.addWidget(self.inventory)
        self.central_widget.addWidget(self.category)
        self.central_widget.addWidget(self.repair)
        self.central_widget.addWidget(self.customer)
        self.central_widget.addWidget(self.customer_finance)
        self.central_widget.addWidget(self.supplier)
        self.central_widget.addWidget(self.equipment)
        self.central_widget.addWidget(self.reporting)
        self.central_widget.addWidget(self.purchasing)
        self.central_widget.addWidget(self.sale)
        self.central_widget.addWidget(self.user)
        self.central_widget.addWidget(self.treasury)
        self.central_widget.addWidget(self.settings)
        
        main_layout.addWidget(self.central_widget)

    def _create_modern_navigation(self):
        """Crée la barre de navigation moderne horizontale."""
        # Créer la barre de navigation
        nav_frame = QFrame()
        nav_frame.setObjectName("navigationFrame")
        nav_frame.setFixedHeight(100)
        nav_frame.setStyleSheet("""
            #navigationFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom: 2px solid #e9ecef;
            }
        """)
        
        # Layout horizontal pour la navigation
        nav_layout = QHBoxLayout(nav_frame)
        nav_layout.setContentsMargins(10, 5, 10, 5)
        nav_layout.setSpacing(5)
        
        # Scroll area pour la navigation
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:horizontal {
                height: 8px;
                background: #f0f0f0;
                border-radius: 4px;
            }
            QScrollBar::handle:horizontal {
                background: #c0c0c0;
                border-radius: 4px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background: #a0a0a0;
            }
        """)
        
        # Widget conteneur pour les boutons
        nav_widget = QWidget()
        nav_widget.setStyleSheet("background: transparent;")
        nav_buttons_layout = QHBoxLayout(nav_widget)
        nav_buttons_layout.setSpacing(5)
        nav_buttons_layout.setContentsMargins(0, 0, 0, 0)
        
        # Créer les boutons de navigation
        self.nav_buttons = {}
        
        # Définir les sections avec leurs icônes
        sections = [
            ("dashboard", "app/ui/resources/icons/dashboard.svg", "Tableau de bord"),
            ("inventory", "app/ui/resources/icons/inventory.svg", "Inventaire"),
            ("repair", "app/ui/resources/icons/repair.svg", "Réparations"),
            ("customer", "app/ui/resources/icons/customer.svg", "Clients"),
            ("supplier", "app/ui/resources/icons/supplier.svg", "Fournisseurs"),
            ("purchasing", "app/ui/resources/icons/purchase.svg", "Achats"),
            ("equipment", "app/ui/resources/icons/equipment.svg", "Équipements"),
            ("sale", "app/ui/resources/icons/sale.svg", "Ventes"),
            ("treasury", "app/ui/resources/icons/money.svg", "Trésorerie"),
            ("reporting", "app/ui/resources/icons/chart.svg", "Rapports"),
            ("user", "app/ui/resources/icons/user.svg", "Utilisateurs"),
        ]
        
        for section_id, icon_path, text in sections:
            button = ModernNavigationButton(icon_path, text)
            button.clicked.connect(lambda checked, sid=section_id: self._navigate_to_section(sid))
            self.nav_buttons[section_id] = button
            nav_buttons_layout.addWidget(button)
        
        # Ajouter un espace extensible
        nav_buttons_layout.addStretch()
        
        # Boutons d'action à droite
        right_actions_layout = QHBoxLayout()
        right_actions_layout.setSpacing(10)
        
        # Bouton de thème
        self.theme_button = QPushButton()
        self.theme_button.setIcon(QIcon("app/ui/resources/icons/light_mode.svg"))
        self.theme_button.setFixedSize(40, 40)
        self.theme_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.theme_button.setStyleSheet("""
            QPushButton {
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 20px;
                padding: 8px;
            }
            QPushButton:hover {
                background: #e3f2fd;
                border: 2px solid #2196F3;
            }
        """)
        self.theme_button.clicked.connect(self._toggle_theme)
        right_actions_layout.addWidget(self.theme_button)
        
        # Bouton de paramètres
        settings_button = QPushButton()
        settings_button.setIcon(QIcon("app/ui/resources/icons/settings_new.svg"))
        settings_button.setFixedSize(40, 40)
        settings_button.setCursor(Qt.CursorShape.PointingHandCursor)
        settings_button.setStyleSheet("""
            QPushButton {
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 20px;
                padding: 8px;
            }
            QPushButton:hover {
                background: #e3f2fd;
                border: 2px solid #2196F3;
            }
        """)
        settings_button.clicked.connect(lambda: self._navigate_to_section("settings"))
        right_actions_layout.addWidget(settings_button)
        
        # Bouton de notification
        self.notification_button = NotificationButton()
        self.notification_button.clicked.connect(self._toggle_notification_center)
        right_actions_layout.addWidget(self.notification_button)
        
        nav_buttons_layout.addLayout(right_actions_layout)
        
        # Ajouter le scroll area au layout principal
        scroll_area.setWidget(nav_widget)
        nav_layout.addWidget(scroll_area)
        
        # Ajouter la barre de navigation au layout principal
        central_widget = self.centralWidget()
        main_layout = central_widget.layout()
        main_layout.insertWidget(0, nav_frame)
        
        # Centre de notifications (initialement caché)
        self.notification_center = NotificationCenter()
        self.notification_center.hide()
        self.notification_center.notificationClicked.connect(self._handle_notification_click)
        self.notification_center.unreadCountChanged.connect(self._update_notification_badge)

    def _navigate_to_section(self, section_id):
        """Navigation vers une section spécifique"""
        # Vérifier les permissions avant la navigation
        if not self._check_section_permission(section_id):
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                "Accès refusé",
                f"Vous n'avez pas les permissions nécessaires pour accéder à la section '{section_id}'."
            )
            return

        # Désélectionner tous les boutons
        for button in self.nav_buttons.values():
            button.setChecked(False)

        # Sélectionner le bouton cliqué
        if section_id in self.nav_buttons:
            self.nav_buttons[section_id].setChecked(True)
        
        # Naviguer vers la section
        navigation_methods = {
            'dashboard': self.show_dashboard_view,
            'inventory': self.show_inventory_view,
            'repair': self.show_repair_view,
            'customer': self.show_customer_view,
            'supplier': self.show_supplier_view,
            'purchasing': self.show_purchase_view,
            'equipment': lambda: self._show_section(self.equipment, 'equipment'),
            'sale': lambda: self._show_section(self.sale, 'sale'),
            'treasury': lambda: self._show_section(self.treasury, 'treasury'),
            'reporting': lambda: self._show_section(self.reporting, 'reporting'),
            'user': lambda: self._show_section(self.user, 'user'),
            'settings': lambda: self._show_section(self.settings, 'settings'),
        }
        
        if section_id in navigation_methods:
            navigation_methods[section_id]()

    def _show_section(self, widget, section_id):
        """Affiche une section et sélectionne le bouton correspondant"""
        self.central_widget.setCurrentWidget(widget)
        # Sélectionner le bouton correspondant
        self._select_navigation_button(section_id)

    def _select_navigation_button(self, section_id):
        """Sélectionne le bouton de navigation correspondant à la section"""
        # Désélectionner tous les boutons
        for button in self.nav_buttons.values():
            button.setChecked(False)
        
        # Sélectionner le bouton correspondant
        if section_id in self.nav_buttons:
            self.nav_buttons[section_id].setChecked(True)

    def _create_statusbar(self):
        """Configure la barre de statut."""
        status = QStatusBar()
        self.setStatusBar(status)

        # Ajouter le label pour l'utilisateur actuel
        if self.user_info:
            user_label = QLabel(f"Utilisateur: {self.user_info.get('full_name', 'Inconnu')}")
            user_label.setStyleSheet("color: #2980b9; font-weight: bold; padding-right: 10px;")
            status.addPermanentWidget(user_label)

        status.showMessage("Prêt")

    def _create_user_menu(self):
        """Crée le menu utilisateur avec les options de déconnexion, changement d'utilisateur et quitter."""
        # Créer la barre de menu
        menubar = self.menuBar()

        # Créer le menu utilisateur
        user_menu = menubar.addMenu("&Utilisateur")

        # Créer le menu d'affichage
        view_menu = menubar.addMenu("&Affichage")

        # Sous-menu Thème
        theme_menu = view_menu.addMenu("&Thème")

        # Action pour thème clair
        light_theme_action = QAction("Thème &Clair", self)
        light_theme_action.setStatusTip("Basculer vers le thème clair")
        light_theme_action.triggered.connect(lambda: self._switch_theme("light"))
        theme_menu.addAction(light_theme_action)

        # Action pour thème sombre
        dark_theme_action = QAction("Thème &Sombre", self)
        dark_theme_action.setStatusTip("Basculer vers le thème sombre")
        dark_theme_action.triggered.connect(lambda: self._switch_theme("dark"))
        theme_menu.addAction(dark_theme_action)

        theme_menu.addSeparator()

        # Action pour basculer automatiquement
        toggle_theme_action = QAction("&Basculer le Thème", self)
        toggle_theme_action.setShortcut("Ctrl+T")
        toggle_theme_action.setStatusTip("Basculer entre thème clair et sombre")
        toggle_theme_action.triggered.connect(self._toggle_theme)
        theme_menu.addAction(toggle_theme_action)

        # Option pour afficher les informations de l'utilisateur
        if self.user_info:
            user_info_action = QAction(f"Connecté en tant que: {self.user_info.get('full_name', 'Inconnu')}", self)
            user_info_action.setEnabled(False)
            user_menu.addAction(user_info_action)

            # Afficher les rôles de l'utilisateur
            roles = self.user_info.get('roles', [])
            if isinstance(roles, str):
                roles = [roles]
            if roles:
                roles_str = ", ".join(roles)
                roles_action = QAction(f"Rôles: {roles_str}", self)
                roles_action.setEnabled(False)
                user_menu.addAction(roles_action)

            user_menu.addSeparator()

        # Option pour se déconnecter
        logout_action = QAction("&Déconnecter", self)
        logout_action.setStatusTip("Se déconnecter de l'application")
        logout_action.triggered.connect(self._handle_logout)
        user_menu.addAction(logout_action)

        # Option pour changer d'utilisateur
        switch_user_action = QAction("Changer d'&utilisateur", self)
        switch_user_action.setStatusTip("Se connecter avec un autre compte")
        switch_user_action.triggered.connect(self._handle_switch_user)
        user_menu.addAction(switch_user_action)

        user_menu.addSeparator()

        # Option pour quitter
        exit_action = QAction("&Quitter", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.setStatusTip("Quitter l'application")
        exit_action.triggered.connect(self.close)
        user_menu.addAction(exit_action)

    def _handle_logout(self):
        """Gère la déconnexion de l'utilisateur."""
        reply = QMessageBox.question(
            self,
            "Confirmation de déconnexion",
            "Voulez-vous vraiment vous déconnecter ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Émettre le signal de déconnexion
            self.logoutRequested.emit()

    def _handle_switch_user(self):
        """Gère le changement d'utilisateur."""
        reply = QMessageBox.question(
            self,
            "Confirmation de changement d'utilisateur",
            "Voulez-vous vraiment changer d'utilisateur ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Émettre le signal de changement d'utilisateur
            self.switchUserRequested.emit()

    def update_user_interface(self):
        """Met à jour l'interface utilisateur en fonction des informations de l'utilisateur."""
        # Mettre à jour le contrôleur d'authentification avec les nouvelles informations utilisateur
        if self.user_info:
            self.auth_controller.current_user = self.user_info

        # Mettre à jour le menu utilisateur
        self._create_user_menu()

        # Mettre à jour la barre d'état
        status = self.statusBar()

        # Supprimer l'ancien label utilisateur s'il existe
        for widget in status.findChildren(QLabel):
            if widget.text().startswith("Utilisateur:"):
                status.removeWidget(widget)
                widget.deleteLater()

        # Ajouter le nouveau label utilisateur
        if self.user_info:
            user_label = QLabel(f"Utilisateur: {self.user_info.get('full_name', 'Inconnu')}")
            user_label.setStyleSheet("color: #2980b9; font-weight: bold; padding-right: 10px;")
            status.addPermanentWidget(user_label)

        # Rafraîchir les permissions
        self.refresh_permissions()

    def _setup_navigation(self):
        """Configure la navigation entre les vues et applique les restrictions basées sur les rôles."""
        # Vérifier les permissions de l'utilisateur
        self._apply_role_restrictions()

    def _toggle_notification_center(self):
        """Affiche ou masque le centre de notifications"""
        if self.notification_center.isVisible():
            self.notification_center.hide()
        else:
            # Positionner le centre de notifications
            button_pos = self.notification_button.mapToGlobal(self.notification_button.rect().bottomLeft())
            self.notification_center.setGeometry(
                button_pos.x() - 300 + self.notification_button.width(),
                button_pos.y() + 10,
                350,
                500
            )
            self.notification_center.show()

            # Rafraîchir les notifications (désactivé pour éviter les doublons)
            # self.notification_center.refresh_notifications()

    def _handle_notification_click(self, notification_id, action_url):
        """Gère le clic sur une notification"""
        # Masquer le centre de notifications
        self.notification_center.hide()

        # Naviguer vers l'URL d'action si elle existe
        if action_url:
            # TODO: Implémenter la navigation vers l'URL d'action
            print(f"Navigation vers {action_url}")

    def _update_notification_badge(self, count):
        """Met à jour le badge du bouton de notification"""
        self.notification_button.set_unread_count(count)

    def _load_theme(self):
        """Charge le thème de l'application"""
        self.theme_manager = ThemeManager()

        # Charger le thème sauvegardé ou par défaut
        self.theme_manager.apply_theme_to_app()

    def _switch_theme(self, theme_name: str):
        """Bascule vers un thème spécifique"""
        try:
            self.theme_manager.switch_theme(theme_name)
            self.theme_manager.apply_theme_to_app()
            self._update_theme_icon()
            self.statusBar().showMessage(f"Thème basculé vers: {theme_name.title()}", 3000)
        except Exception as e:
            QMessageBox.warning(self, "Erreur de Thème", f"Impossible de charger le thème {theme_name}: {str(e)}")

    def _toggle_theme(self):
        """Bascule entre thème clair et sombre"""
        try:
            self.theme_manager.toggle_theme()
            self.theme_manager.apply_theme_to_app()
            self._update_theme_icon()
            current_theme = self.theme_manager.current_theme
            self.statusBar().showMessage(f"Thème basculé vers: {current_theme.title()}", 3000)
        except Exception as e:
            QMessageBox.warning(self, "Erreur de Thème", f"Impossible de basculer le thème: {str(e)}")

    def _update_theme_icon(self):
        """Met à jour l'icône du bouton de basculement de thème"""
        if hasattr(self, 'theme_manager') and hasattr(self, 'theme_button'):
            current_theme = self.theme_manager.current_theme
            if current_theme == "light":
                self.theme_button.setIcon(QIcon("app/ui/resources/icons/dark_mode.svg"))
                self.theme_button.setToolTip("Basculer vers le thème sombre")
            else:
                self.theme_button.setIcon(QIcon("app/ui/resources/icons/light_mode.svg"))
                self.theme_button.setToolTip("Basculer vers le thème clair")

    def _apply_role_restrictions(self):
        """Applique les restrictions basées sur les permissions de l'utilisateur."""
        if not self.user_info:
            return

        # Récupérer de façon robuste permissions et rôles
        permissions = self.user_info.get('permissions') or []
        roles = self.user_info.get('roles') or []
        if isinstance(permissions, str):
            # Essayer JSON puis fallback séparateur virgule
            try:
                import json
                parsed = json.loads(permissions)
                if isinstance(parsed, list):
                    permissions = [str(p) for p in parsed]
                else:
                    permissions = [str(parsed)]
            except Exception:
                permissions = [p.strip() for p in permissions.split(',')] if ',' in permissions else [permissions]
        if isinstance(roles, str):
            try:
                import json
                parsed_r = json.loads(roles)
                if isinstance(parsed_r, list):
                    roles = [str(r) for r in parsed_r]
                else:
                    roles = [str(parsed_r)]
            except Exception:
                roles = [r.strip() for r in roles.split(',')] if ',' in roles else [roles]

        # Flags admin possibles
        is_admin_flag = bool(self.user_info.get('is_admin') or self.user_info.get('is_superuser'))

        # Rôle admin ou flags -> accès total
        if is_admin_flag or any(r.lower() in ('admin', 'administrator', 'superadmin') for r in roles):
            return

        # Si l'utilisateur a des permissions d'administration, il a accès à tout
        admin_permissions = ['admin', 'user.create', 'user.delete', 'user.manage_roles', 'system.settings']
        if any(perm in permissions for perm in admin_permissions):
            return

        # Si aucune info d'autorisations fournie, ne pas restreindre
        if not permissions and not roles:
            return

        # Appliquer le mapping vendeur si permissions == ['Vendeur']
        if permissions == ['Vendeur']:
            role_permission_map = {
                'vendeur': ['repair.view', 'customer.view', 'sale.view'],
            }
            permissions = role_permission_map['vendeur']
        # Si pas de permissions explicites mais des rôles, ou permissions contient 'Vendeur', mapper quelques rôles standards
        elif not permissions and (roles or 'Vendeur' in permissions):
            role_permission_map = {
                'manager': ['inventory.view', 'repair.view', 'customer.view', 'supplier.view',
                            'purchasing.view', 'reporting.view'],
                'technician': ['repair.view', 'repair.status'],
                'sales': ['repair.view', 'customer.view', 'sale.view'],
                'vendeur': ['repair.view', 'customer.view', 'sale.view'],
            }
            mapped_roles = [r.lower() for r in roles]
            if 'Vendeur' in permissions:
                mapped_roles.append('Vendeur')
            for r in mapped_roles:
                permissions.extend(role_permission_map.get(r, []))

        # DEBUG: Afficher les infos utilisateur et permissions/rôles
        print("[DEBUG] user_info:", self.user_info)
        print("[DEBUG] permissions (final):", permissions)
        print("[DEBUG] roles:", roles)
        print("[DEBUG] is_admin_flag:", is_admin_flag)

        # Définir les restrictions basées sur les permissions
        # Mapping des permissions vers les sections autorisées
        permission_to_sections = {
            # Permissions utilisateurs
            'user.view': ['user'],
            'user.create': ['user'],
            'user.edit': ['user'],
            'user.delete': ['user'],
            'user.manage_roles': ['user'],

            # Permissions clients
            'customer.view': ['customer'],
            'customer.create': ['customer'],
            'customer.edit': ['customer'],
            'customer.delete': ['customer'],

            # Permissions fournisseurs
            'supplier.view': ['supplier'],
            'supplier.create': ['supplier'],
            'supplier.edit': ['supplier'],
            'supplier.delete': ['supplier'],

            # Permissions inventaire
            'inventory.view': ['inventory'],
            'inventory.create': ['inventory'],
            'inventory.edit': ['inventory'],
            'inventory.delete': ['inventory'],
            'inventory.movement': ['inventory'],
            'inventory.category.view': ['inventory'],
            'inventory.category.create': ['inventory'],
            'inventory.category.edit': ['inventory'],
            'inventory.category.delete': ['inventory'],

            # Permissions réparations
            'repair.view': ['repair'],
            'repair.create': ['repair'],
            'repair.edit': ['repair'],
            'repair.delete': ['repair'],
            'repair.status': ['repair'],

            # Permissions achats
            'purchasing.view': ['purchasing'],
            'purchasing.create': ['purchasing'],
            'purchasing.edit': ['purchasing'],
            'purchasing.delete': ['purchasing'],
            'purchasing.approve': ['purchasing'],

            # Permissions rapports
            'reporting.view': ['reporting'],
            'reporting.export': ['reporting'],

            # Permissions système
            'system.settings': ['settings'],
            'system.backup': ['settings'],
            'system.logs': ['settings'],
        }

        # Calculer les sections autorisées
        allowed_sections = set(['dashboard'])  # Dashboard toujours autorisé
        for permission in permissions:
            if permission in permission_to_sections:
                allowed_sections.update(permission_to_sections[permission])

        # Désactiver les boutons non autorisés
        all_sections = ['dashboard', 'inventory', 'repair', 'customer', 'supplier', 'purchasing',
                       'equipment', 'sale', 'treasury', 'reporting', 'user', 'settings']

        for section_id in all_sections:
            if section_id not in allowed_sections and section_id in self.nav_buttons:
                self.nav_buttons[section_id].setEnabled(False)
                self.nav_buttons[section_id].setToolTip(
                    "Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité"
                )
                # Ajouter un style visuel pour indiquer que c'est désactivé
                self.nav_buttons[section_id].setStyleSheet(
                    self.nav_buttons[section_id].styleSheet() + """
                    QPushButton:disabled {
                        background-color: #f5f5f5;
                        color: #cccccc;
                        border: 1px solid #e0e0e0;
                    }
                    """
                )

        # Afficher un message dans la barre d'état
        if len(allowed_sections) < len(all_sections):
            restricted_count = len(all_sections) - len(allowed_sections)
            self.statusBar().showMessage(
                f"Connecté avec permissions limitées. {restricted_count} section(s) restreinte(s)."
            )

    def _check_section_permission(self, section_id):
        print(f"[DEBUG] Vérification permission section: {section_id}")
        # permissions et section_permissions sont définis plus bas, donc on affiche après leur définition
        """Vérifie si l'utilisateur a la permission d'accéder à une section"""
        if not self.user_info:
            return False

        # Dashboard toujours autorisé
        if section_id == 'dashboard':
            return True

        # Récupérer de façon robuste permissions et rôles
        permissions = self.user_info.get('permissions') or []
        roles = self.user_info.get('roles') or []
        if isinstance(permissions, str):
            # Essayer JSON puis fallback séparateur virgule
            try:
                import json
                parsed = json.loads(permissions)
                if isinstance(parsed, list):
                    permissions = [str(p) for p in parsed]
                else:
                    permissions = [str(parsed)]
            except Exception:
                permissions = [p.strip() for p in permissions.split(',')] if ',' in permissions else [permissions]
        if isinstance(roles, str):
            try:
                import json
                parsed_r = json.loads(roles)
                if isinstance(parsed_r, list):
                    roles = [str(r) for r in parsed_r]
                else:
                    roles = [str(parsed_r)]
            except Exception:
                roles = [r.strip() for r in roles.split(',')] if ',' in roles else [roles]

        # Flags admin possibles
        is_admin_flag = bool(self.user_info.get('is_admin') or self.user_info.get('is_superuser'))

        # Rôle admin ou flags -> accès total
        if is_admin_flag or any(r.lower() in ('admin', 'administrator', 'superadmin') for r in roles):
            return True

        # Si l'utilisateur a des permissions d'administration, il a accès à tout
        admin_permissions = ['admin', 'user.create', 'user.delete', 'user.manage_roles', 'system.settings']
        if any(perm in permissions for perm in admin_permissions):
            return True

        # Mapper des permissions par défaut si seulement des rôles sont fournis
        # Si pas de permissions explicites mais des rôles, ou permissions contient 'Vendeur', mapper quelques rôles standards
        if not permissions and (roles or 'Vendeur' in permissions):
            role_permission_map = {
                'manager': ['inventory.view', 'repair.view', 'customer.view', 'supplier.view',
                            'purchasing.view', 'reporting.view'],
                'technician': ['repair.view', 'repair.status'],
                'sales': ['repair.view', 'customer.view', 'sale.view'],
                'vendeur': ['repair.view', 'customer.view', 'sale.view'],
            }
            mapped_roles = [r.lower() for r in roles]
            if 'Vendeur' in permissions:
                mapped_roles.append('vendeur')
            for r in mapped_roles:
                permissions.extend(role_permission_map.get(r, []))

        # Mapping des sections vers les permissions requises
        section_permissions = {
            'user': ['user.view', 'user.create', 'user.edit', 'user.delete', 'user.manage_roles'],
            'customer': ['customer.view', 'customer.create', 'customer.edit', 'customer.delete'],
            'supplier': ['supplier.view', 'supplier.create', 'supplier.edit', 'supplier.delete'],
            'inventory': ['inventory.view', 'inventory.create', 'inventory.edit', 'inventory.delete',
                         'inventory.movement', 'inventory.category.view', 'inventory.category.create',
                         'inventory.category.edit', 'inventory.category.delete'],
            'repair': ['repair.view', 'repair.create', 'repair.edit', 'repair.delete', 'repair.status'],
            'purchasing': ['purchasing.view', 'purchasing.create', 'purchasing.edit',
                          'purchasing.delete', 'purchasing.approve'],
            'reporting': ['reporting.view', 'reporting.export'],
            'settings': ['system.settings', 'system.backup', 'system.logs'],
            # Sections non encore implémentées - accès libre pour l'instant
            'equipment': [],
            'sale': [],
            'treasury': []
        }
        # Appliquer le mapping vendeur si permissions == ['Vendeur']
        if permissions == ['Vendeur']:
            permissions = ['repair.view', 'customer.view', 'sale.view']
        print(f"[DEBUG] Vérification permission section: {section_id}")
        print(f"[DEBUG] Permissions utilisées: {permissions}")
        print(f"[DEBUG] Permissions requises: {section_permissions.get(section_id, [])}")

        # Vérifier si l'utilisateur a au moins une permission pour cette section
        required_permissions = section_permissions.get(section_id, [])
        if not required_permissions:  # Section sans restrictions
            return True

        return any(perm in permissions for perm in required_permissions)

    def show_dashboard_view(self):
        """Affiche la vue du tableau de bord et rafraîchit les données"""
        print("Affichage du tableau de bord")
        self.central_widget.setCurrentWidget(self.dashboard)
        self._select_navigation_button('dashboard')

        # Rafraîchir les données du tableau de bord
        if hasattr(self.dashboard, 'refresh'):
            print("Rafraîchissement des données du tableau de bord")
            self.dashboard.refresh()

    def show_inventory_view(self):
        """Affiche la vue de gestion de l'inventaire"""
        print("Affichage de la vue de gestion de l'inventaire")
        self.central_widget.setCurrentWidget(self.inventory)
        self._select_navigation_button('inventory')

    def show_repair_view(self):
        """Affiche la vue de gestion des réparations"""
        print("Affichage de la vue de gestion des réparations")
        self.central_widget.setCurrentWidget(self.repair)
        self._select_navigation_button('repair')

    def show_customer_view(self):
        """Affiche la vue de gestion des clients"""
        print("Affichage de la vue de gestion des clients")
        self.central_widget.setCurrentWidget(self.customer)
        self._select_navigation_button('customer')

    def show_customer_finance_view(self):
        """Affiche la vue de gestion financière des clients"""
        print("Affichage de la vue de gestion financière des clients")
        self.central_widget.setCurrentWidget(self.customer_finance)
        self._select_navigation_button('customer_finance')

    def show_supplier_view(self):
        """Affiche la vue de gestion des fournisseurs"""
        print("Affichage de la vue de gestion des fournisseurs")
        self.central_widget.setCurrentWidget(self.supplier)
        self._select_navigation_button('supplier')

    def show_purchase_view(self):
        """Affiche la vue de gestion des achats"""
        print("Affichage de la vue de gestion des achats")
        self.central_widget.setCurrentWidget(self.purchasing)
        self._select_navigation_button('purchasing')




