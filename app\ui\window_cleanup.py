"""
Module pour nettoyer la barre d'outils de la fenêtre principale.
Ce module est utilisé pour supprimer les actions vides ou invisibles de la barre d'outils.
"""

def cleanup_toolbar(toolbar):
    """
    Nettoie la barre d'outils en supprimant les actions vides ou invisibles.
    
    Args:
        toolbar: La barre d'outils à nettoyer
    """
    # Récupérer toutes les actions de la barre d'outils
    actions = toolbar.actions()
    
    # Parcourir les actions et supprimer celles qui sont vides ou invisibles
    for action in actions:
        # Si l'action est un séparateur et qu'elle est invisible, la supprimer
        if action.isSeparator() and not action.isVisible():
            toolbar.removeAction(action)
            continue
        
        # Si l'action n'a pas de texte et pas d'icône, la supprimer
        if not action.text() and action.icon().isNull():
            toolbar.removeAction(action)
            continue
        
        # Si l'action est invisible, la supprimer
        if not action.isVisible():
            toolbar.removeAction(action)
            continue
