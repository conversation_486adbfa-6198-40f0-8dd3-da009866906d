"""Shared async scheduling utilities for PyQt + asyncio.

Provides a safe way to schedule coroutines from Qt code whether or not
an asyncio event loop is currently running.
"""
from __future__ import annotations

import threading
import asyncio
from typing import Coroutine, Any


def schedule_coro(coro: Coroutine[Any, Any, Any]) -> None:
    """Schedule a coroutine safely.

    - If an asyncio loop is running in this thread, create a task on it.
    - Otherwise, run the coroutine in a dedicated thread using asyncio.run,
      keeping the UI thread responsive.
    """
    try:
        loop = asyncio.get_running_loop()
        loop.create_task(coro)
    except Exception:
        # No running loop in current thread -> run in a background thread
        def _runner() -> None:
            try:
                asyncio.run(coro)
            except Exception:
                # Swallow to avoid crashing the UI; real errors should be signaled by callers
                pass
        threading.Thread(target=_runner, daemon=True).start()