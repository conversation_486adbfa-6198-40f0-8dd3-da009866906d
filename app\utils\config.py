from pydantic import BaseModel
from functools import lru_cache
import os
import pathlib
from typing import Optional

class Settings(BaseModel):
    """Configuration de l'application"""

    # Base de données
    database_url: str = "sqlite:///./app.db"

    # Sécurité
    secret_key: str = "your-secret-key"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # Email
    email_enabled: bool = False  # Désactivé par défaut en développement
    email_sender: str = "<EMAIL>"
    smtp_server: str = "smtp.example.com"
    smtp_port: int = 587
    smtp_use_tls: bool = True
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None

    # Notifications
    notification_cleanup_interval_hours: int = 24
    notification_default_expiry_days: int = 30

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8"
    }

@lru_cache()
def get_settings() -> Settings:
    """Récupère les paramètres de l'application"""
    return Settings()

_db_path = None

def set_db_path(new_path: str):
    """Définit dynamiquement le chemin de la base de données utilisée par l'application."""
    global _db_path
    _db_path = new_path

def get_db_path() -> str:
    """Récupère le chemin de la base de données (modifié dynamiquement si besoin)."""
    import sys
    global _db_path
    if _db_path:
        return _db_path
    if getattr(sys, 'frozen', False):
        # Si l'application est compilée avec PyInstaller
        base_dir = sys._MEIPASS
        db_path = os.path.join(base_dir, "app.db")
    else:
        # Utiliser le répertoire de l'application comme base
        base_dir = pathlib.Path(__file__).parent.parent.parent
        db_dir = base_dir / "data"

        # Créer le répertoire s'il n'existe pas
        os.makedirs(db_dir, exist_ok=True)

        # Chemin de la base de données
        db_path = str(db_dir / "app.db")

    return db_path
