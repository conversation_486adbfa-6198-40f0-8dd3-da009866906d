import sqlite3
import os
import logging
from pathlib import Path
from app.utils.migrations import fix_roles_table
from app.utils.migrations.add_item_categories_table import migrate as add_item_categories

logger = logging.getLogger(__name__)

def get_db_path():
    """Récupère le chemin de la base de données"""
    try:
        from app.utils.config import get_settings
        settings = get_settings()
        return settings.database_url.replace("sqlite:///", "")
    except ImportError:
        # Fallback if config.settings is not available
        from app.utils.database import get_database_url
        db_url = get_database_url()
        return db_url.replace("sqlite:///", "")

def add_equipment_id_to_repair_orders():
    """Ajoute la colonne equipment_id à la table repair_orders"""
    db_path = get_db_path()

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Vérifier si la colonne existe déjà
        cursor.execute("PRAGMA table_info(repair_orders)")
        columns = [column[1] for column in cursor.fetchall()]

        if "equipment_id" not in columns:
            logger.info("Ajout de la colonne equipment_id à la table repair_orders")
            cursor.execute("ALTER TABLE repair_orders ADD COLUMN equipment_id INTEGER")
            conn.commit()
            logger.info("Colonne equipment_id ajoutée avec succès")
        else:
            logger.info("La colonne equipment_id existe déjà dans la table repair_orders")

        conn.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration de la base de données: {e}")
        return False

def add_notification_tables():
    """Ajoute les tables pour le système de notifications"""
    db_path = get_db_path()

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Vérifier si la table notifications existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='notifications'")
        if not cursor.fetchone():
            logger.info("Création de la table notifications")
            cursor.execute("""
                CREATE TABLE notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    type TEXT NOT NULL,
                    priority TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    status TEXT NOT NULL,
                    data TEXT,
                    action_url TEXT,
                    icon TEXT,
                    expiry_date TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            logger.info("Table notifications créée avec succès")
        else:
            logger.info("La table notifications existe déjà")

        # Vérifier si la table notification_deliveries existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='notification_deliveries'")
        if not cursor.fetchone():
            logger.info("Création de la table notification_deliveries")
            cursor.execute("""
                CREATE TABLE notification_deliveries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    notification_id INTEGER NOT NULL,
                    channel TEXT NOT NULL,
                    recipient TEXT NOT NULL,
                    sent_at TIMESTAMP,
                    delivered_at TIMESTAMP,
                    success BOOLEAN DEFAULT 0,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (notification_id) REFERENCES notifications (id)
                )
            """)
            logger.info("Table notification_deliveries créée avec succès")
        else:
            logger.info("La table notification_deliveries existe déjà")

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la création des tables de notifications: {e}")
        return False

def add_maintenance_tables():
    """Ajoute les tables pour le système de maintenance"""
    db_path = get_db_path()

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Vérifier si la table maintenance_schedules existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='maintenance_schedules'")
        if not cursor.fetchone():
            logger.info("Création de la table maintenance_schedules")
            cursor.execute("""
                CREATE TABLE maintenance_schedules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER NOT NULL,
                    type TEXT NOT NULL,
                    frequency_days INTEGER NOT NULL,
                    last_maintenance TIMESTAMP,
                    next_date TIMESTAMP NOT NULL,
                    status TEXT NOT NULL,
                    description TEXT,
                    estimated_duration REAL,
                    technician_id INTEGER,
                    actual_duration REAL,
                    cost REAL DEFAULT 0.0,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                    FOREIGN KEY (technician_id) REFERENCES users (id)
                )
            """)
            logger.info("Table maintenance_schedules créée avec succès")
        else:
            # Vérifier si les colonnes nécessaires existent
            cursor.execute("PRAGMA table_info(maintenance_schedules)")
            columns = [column[1] for column in cursor.fetchall()]

            if "next_date" not in columns:
                logger.info("Ajout de la colonne next_date à la table maintenance_schedules")
                cursor.execute("ALTER TABLE maintenance_schedules ADD COLUMN next_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP")
                conn.commit()
                logger.info("Colonne next_date ajoutée avec succès")
            else:
                logger.info("La colonne next_date existe déjà dans la table maintenance_schedules")

            if "cost" not in columns:
                logger.info("Ajout de la colonne cost à la table maintenance_schedules")
                cursor.execute("ALTER TABLE maintenance_schedules ADD COLUMN cost REAL DEFAULT 0.0")
                conn.commit()
                logger.info("Colonne cost ajoutée avec succès")
            else:
                logger.info("La colonne cost existe déjà dans la table maintenance_schedules")

            if "actual_duration" not in columns:
                logger.info("Ajout de la colonne actual_duration à la table maintenance_schedules")
                cursor.execute("ALTER TABLE maintenance_schedules ADD COLUMN actual_duration REAL")
                conn.commit()
                logger.info("Colonne actual_duration ajoutée avec succès")
            else:
                logger.info("La colonne actual_duration existe déjà dans la table maintenance_schedules")

            if "notes" not in columns:
                logger.info("Ajout de la colonne notes à la table maintenance_schedules")
                cursor.execute("ALTER TABLE maintenance_schedules ADD COLUMN notes TEXT")
                conn.commit()
                logger.info("Colonne notes ajoutée avec succès")
            else:
                logger.info("La colonne notes existe déjà dans la table maintenance_schedules")

            if "is_active" not in columns:
                logger.info("Ajout de la colonne is_active à la table maintenance_schedules")
                cursor.execute("ALTER TABLE maintenance_schedules ADD COLUMN is_active BOOLEAN DEFAULT 1")
                conn.commit()
                logger.info("Colonne is_active ajoutée avec succès")
            else:
                logger.info("La colonne is_active existe déjà dans la table maintenance_schedules")

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la création des tables de maintenance: {e}")
        return False

def add_purchasing_tables():
    """Ajoute les tables pour le système d'achats"""
    db_path = get_db_path()

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Vérifier si la table purchase_orders existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='purchase_orders'")
        if not cursor.fetchone():
            logger.info("Création de la table purchase_orders")
            cursor.execute("""
                CREATE TABLE purchase_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    po_number TEXT UNIQUE,
                    supplier_id INTEGER NOT NULL,
                    status TEXT NOT NULL,
                    order_date TIMESTAMP,
                    expected_delivery TIMESTAMP,
                    delivery_date TIMESTAMP,
                    total_amount REAL NOT NULL DEFAULT 0,
                    currency TEXT DEFAULT 'EUR',
                    payment_terms TEXT,
                    shipping_terms TEXT,
                    notes TEXT,
                    created_by INTEGER,
                    approved_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                    FOREIGN KEY (created_by) REFERENCES users (id),
                    FOREIGN KEY (approved_by) REFERENCES users (id)
                )
            """)
            logger.info("Table purchase_orders créée avec succès")
        else:
            logger.info("La table purchase_orders existe déjà")

        # Vérifier si la table purchase_order_items existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='purchase_order_items'")
        if not cursor.fetchone():
            logger.info("Création de la table purchase_order_items")
            cursor.execute("""
                CREATE TABLE purchase_order_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    po_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    received_quantity REAL DEFAULT 0,
                    specifications TEXT,
                    delivery_date TIMESTAMP,
                    FOREIGN KEY (po_id) REFERENCES purchase_orders (id),
                    FOREIGN KEY (product_id) REFERENCES inventory_items (id)
                )
            """)
            logger.info("Table purchase_order_items créée avec succès")
        else:
            logger.info("La table purchase_order_items existe déjà")

        # Vérifier si la table supplier_quotes existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='supplier_quotes'")
        if not cursor.fetchone():
            logger.info("Création de la table supplier_quotes")
            cursor.execute("""
                CREATE TABLE supplier_quotes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    supplier_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    total_amount REAL NOT NULL,
                    delivery_time INTEGER NOT NULL,
                    validity_period INTEGER NOT NULL,
                    payment_terms TEXT NOT NULL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    valid_until TIMESTAMP NOT NULL,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                    FOREIGN KEY (product_id) REFERENCES inventory_items (id)
                )
            """)
            logger.info("Table supplier_quotes créée avec succès")
        else:
            logger.info("La table supplier_quotes existe déjà")

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la création des tables d'achats: {e}")
        return False

def add_user_management_tables():
    """Ajoute les tables pour la gestion des utilisateurs et des permissions"""
    db_path = get_db_path()

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Vérifier si la table permissions existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='permissions'")
        if not cursor.fetchone():
            logger.info("Création de la table permissions")
            cursor.execute("""
                CREATE TABLE permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    category TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("Table permissions créée avec succès")
        else:
            logger.info("La table permissions existe déjà")

        # Vérifier si la table roles existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='roles'")
        if not cursor.fetchone():
            logger.info("Création de la table roles")
            cursor.execute("""
                CREATE TABLE roles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    is_system BOOLEAN DEFAULT 0,
                    parent_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES roles (id)
                )
            """)
            logger.info("Table roles créée avec succès")
        else:
            logger.info("La table roles existe déjà")

        # Vérifier si la table role_permission existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='role_permission'")
        if not cursor.fetchone():
            logger.info("Création de la table role_permission")
            cursor.execute("""
                CREATE TABLE role_permission (
                    role_id INTEGER NOT NULL,
                    permission_id INTEGER NOT NULL,
                    PRIMARY KEY (role_id, permission_id),
                    FOREIGN KEY (role_id) REFERENCES roles (id),
                    FOREIGN KEY (permission_id) REFERENCES permissions (id)
                )
            """)
            logger.info("Table role_permission créée avec succès")
        else:
            logger.info("La table role_permission existe déjà")

        # Vérifier si la table user_roles existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_roles'")
        if not cursor.fetchone():
            logger.info("Création de la table user_roles")
            cursor.execute("""
                CREATE TABLE user_roles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    role_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (role_id) REFERENCES roles (id)
                )
            """)
            logger.info("Table user_roles créée avec succès")
        else:
            logger.info("La table user_roles existe déjà")

        # Vérifier si la table audit_logs existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='audit_logs'")
        if not cursor.fetchone():
            logger.info("Création de la table audit_logs")
            cursor.execute("""
                CREATE TABLE audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    entity_type TEXT,
                    entity_id INTEGER,
                    details TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            logger.info("Table audit_logs créée avec succès")
        else:
            logger.info("La table audit_logs existe déjà")

        # Mettre à jour la table users si elle existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if cursor.fetchone():
            # Vérifier si les colonnes existent déjà
            cursor.execute("PRAGMA table_info(users)")
            columns = [column[1] for column in cursor.fetchall()]

            # Ajouter les colonnes manquantes
            if "status" not in columns:
                logger.info("Ajout de la colonne status à la table users")
                cursor.execute("ALTER TABLE users ADD COLUMN status TEXT DEFAULT 'active'")

            if "phone" not in columns:
                logger.info("Ajout de la colonne phone à la table users")
                cursor.execute("ALTER TABLE users ADD COLUMN phone TEXT")

            if "position" not in columns:
                logger.info("Ajout de la colonne position à la table users")
                cursor.execute("ALTER TABLE users ADD COLUMN position TEXT")

            if "department" not in columns:
                logger.info("Ajout de la colonne department à la table users")
                cursor.execute("ALTER TABLE users ADD COLUMN department TEXT")

            if "profile_image" not in columns:
                logger.info("Ajout de la colonne profile_image à la table users")
                cursor.execute("ALTER TABLE users ADD COLUMN profile_image TEXT")

            if "password_reset_token" not in columns:
                logger.info("Ajout de la colonne password_reset_token à la table users")
                cursor.execute("ALTER TABLE users ADD COLUMN password_reset_token TEXT")

            if "password_reset_expires" not in columns:
                logger.info("Ajout de la colonne password_reset_expires à la table users")
                cursor.execute("ALTER TABLE users ADD COLUMN password_reset_expires TIMESTAMP")

            if "failed_login_attempts" not in columns:
                logger.info("Ajout de la colonne failed_login_attempts à la table users")
                cursor.execute("ALTER TABLE users ADD COLUMN failed_login_attempts INTEGER DEFAULT 0")

            if "last_failed_login" not in columns:
                logger.info("Ajout de la colonne last_failed_login à la table users")
                cursor.execute("ALTER TABLE users ADD COLUMN last_failed_login TIMESTAMP")

            if "preferences" not in columns:
                logger.info("Ajout de la colonne preferences à la table users")
                cursor.execute("ALTER TABLE users ADD COLUMN preferences TEXT")

        # Créer des rôles et permissions par défaut
        # Rôle Administrateur
        cursor.execute("INSERT OR IGNORE INTO roles (name, description, is_system) VALUES ('admin', 'Administrateur avec tous les droits', 1)")

        # Rôle Gestionnaire
        cursor.execute("INSERT OR IGNORE INTO roles (name, description, is_system) VALUES ('manager', 'Gestionnaire avec droits limités', 1)")

        # Rôle Technicien
        cursor.execute("INSERT OR IGNORE INTO roles (name, description, is_system) VALUES ('technician', 'Technicien de réparation', 1)")

        # Rôle Inventaire
        cursor.execute("INSERT OR IGNORE INTO roles (name, description, is_system) VALUES ('inventory', 'Gestionnaire d''inventaire', 1)")

        # Rôle Ventes
        cursor.execute("INSERT OR IGNORE INTO roles (name, description, is_system) VALUES ('sales', 'Responsable des ventes', 1)")

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la création des tables de gestion des utilisateurs: {e}")
        return False

def add_customer_technician_ids_to_repair_orders():
    """Ajoute les colonnes customer_id et technician_id à la table repair_orders"""
    from app.utils.database import SessionLocal
    from app.utils.migrations.add_repair_customer_technician_ids import run_migration

    try:
        db = SessionLocal()
        run_migration(db)
        db.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration add_customer_technician_ids_to_repair_orders: {e}")
        return False

def add_total_paid_to_repair_orders():
    """Ajoute la colonne total_paid à la table repair_orders"""
    from app.utils.migrations.add_total_paid_to_repair_orders import run_migration

    try:
        result = run_migration()
        return result
    except Exception as e:
        logger.error(f"Erreur lors de la migration add_total_paid_to_repair_orders: {e}")
        return False

def add_financial_columns_to_repair_orders():
    """Ajoute les colonnes financières à la table repair_orders"""
    try:
        db_path = get_db_path()
        from app.utils.migrations.add_financial_columns_to_repair_orders import migrate
        result = migrate(db_path, {})
        return result
    except Exception as e:
        logger.error(f"Erreur lors de la migration add_financial_columns_to_repair_orders: {e}")
        return False

def add_financial_fields_to_customers():
    """Ajoute les champs financiers à la table customers"""
    from app.utils.database import SessionLocal
    from app.utils.migrations.add_financial_fields_to_customers import run_migration

    try:
        db = SessionLocal()
        run_migration(db)
        db.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration add_financial_fields_to_customers: {e}")
        return False

def add_delivery_time_to_suppliers():
    """Ajoute les champs de délai de livraison à la table suppliers"""
    from app.utils.database import SessionLocal
    from app.utils.migrations.add_delivery_time_to_suppliers import run_migration

    try:
        db = SessionLocal()
        run_migration(db)
        db.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration add_delivery_time_to_suppliers: {e}")
        return False

def create_supplier_finance_tables():
    """Crée les tables de finance fournisseur"""
    from app.utils.migrations.create_supplier_finance_tables import run_migration

    try:
        db_path = get_db_path()
        run_migration(db_path)
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration create_supplier_finance_tables: {e}")
        return False

def add_purchase_order_id_to_supplier_payments():
    """Ajoute la colonne purchase_order_id à la table supplier_payments"""
    from app.utils.migrations.add_purchase_order_id_to_supplier_payments import run_migration

    try:
        db_path = get_db_path()
        run_migration(db_path)
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration add_purchase_order_id_to_supplier_payments: {e}")
        return False

def fix_supplier_payments():
    """Ajoute la colonne purchase_order_id à la table supplier_payments"""
    from app.utils.database import SessionLocal
    from app.utils.migrations.fix_supplier_payments import run_migration

    try:
        db = SessionLocal()
        run_migration(db)
        db.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration fix_supplier_payments: {e}")
        return False

def add_order_history_table():
    """Ajoute la table d'historique des commandes"""
    from app.utils.database import SessionLocal
    from app.utils.migrations.add_order_history_table import run_migration

    try:
        db = SessionLocal()
        run_migration(db)
        db.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration add_order_history_table: {e}")
        return False

def add_sale_id_to_customer_transactions():
    """Ajoute les colonnes sale_id et transaction_type à la table customer_transactions"""
    db_path = get_db_path()

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Vérifier si la table customer_transactions existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='customer_transactions'")
        if not cursor.fetchone():
            logger.info("La table customer_transactions n'existe pas, création de la table")
            cursor.execute("""
                CREATE TABLE customer_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER NOT NULL,
                    repair_order_id INTEGER,
                    sale_id INTEGER,
                    amount REAL NOT NULL,
                    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT,
                    reference_number TEXT,
                    processed_by TEXT,
                    transaction_type TEXT DEFAULT 'manual',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (repair_order_id) REFERENCES repair_orders (id),
                    FOREIGN KEY (sale_id) REFERENCES sales (id)
                )
            """)
            conn.commit()
            logger.info("Table customer_transactions créée avec succès")
            return True

        # Si la table existe, vérifier si les colonnes existent
        cursor.execute("PRAGMA table_info(customer_transactions)")
        columns = [column[1] for column in cursor.fetchall()]

        if "sale_id" not in columns:
            logger.info("Ajout de la colonne sale_id à la table customer_transactions")
            cursor.execute("ALTER TABLE customer_transactions ADD COLUMN sale_id INTEGER")
            conn.commit()
            logger.info("Colonne sale_id ajoutée avec succès")
        else:
            logger.info("La colonne sale_id existe déjà dans la table customer_transactions")

        if "transaction_type" not in columns:
            logger.info("Ajout de la colonne transaction_type à la table customer_transactions")
            cursor.execute("ALTER TABLE customer_transactions ADD COLUMN transaction_type TEXT DEFAULT 'manual'")
            conn.commit()
            logger.info("Colonne transaction_type ajoutée avec succès")
        else:
            logger.info("La colonne transaction_type existe déjà dans la table customer_transactions")

        conn.close()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration add_sale_id_to_customer_transactions: {e}")
        return False

def add_treasury_tables():
    """Ajoute les tables pour le module de trésorerie"""
    from app.utils.migrations.add_treasury_tables import run_migration

    try:
        run_migration()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration add_treasury_tables: {e}")
        return False

def add_settings_tables():
    """Ajoute les tables pour le module de paramètres"""
    from app.utils.migrations.add_settings_tables import run_migration

    try:
        run_migration()
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la migration add_settings_tables: {e}")
        return False

def create_repair_tables():
    """Crée les tables nécessaires pour le module de réparation"""
    db_path = get_db_path()

    try:
        from app.utils.migrations.create_repair_tables import migrate
        result = migrate(db_path)
        return result
    except Exception as e:
        logger.error(f"Erreur lors de la migration create_repair_tables: {e}")
        return False

def run_migrations():
    """Exécute toutes les migrations nécessaires"""
    logger.info("Démarrage des migrations de la base de données")

    # Ajouter ici toutes les migrations à exécuter
    migrations = [
        create_repair_tables,  # Ajouter cette migration en premier
        add_equipment_id_to_repair_orders,
        add_notification_tables,
        add_maintenance_tables,
        add_purchasing_tables,
        add_user_management_tables,
        lambda: fix_roles_table.migrate(get_db_path()),  # Add the new migration here
        add_financial_columns_to_repair_orders,
        add_customer_technician_ids_to_repair_orders,
        add_total_paid_to_repair_orders,  # Ajouter la colonne total_paid
        add_financial_fields_to_customers,  # Ajouter les champs financiers aux clients
        add_delivery_time_to_suppliers,  # Ajouter les champs de délai de livraison aux fournisseurs
        create_supplier_finance_tables,  # Créer les tables de finance fournisseur
        add_item_categories,  # Ajouter la table des catégories d'articles
        add_purchase_order_id_to_supplier_payments,  # Ajouter la colonne purchase_order_id à la table supplier_payments
        fix_supplier_payments,  # Corriger la table supplier_payments
        add_order_history_table,  # Ajouter la table d'historique des commandes
        add_sale_id_to_customer_transactions,  # Ajouter les colonnes sale_id et transaction_type à la table customer_transactions
        add_treasury_tables,  # Ajouter les tables pour le module de trésorerie
        add_settings_tables,  # Ajouter les tables pour le module de paramètres
    ]

    failed_migrations = []
    for migration in migrations:
        try:
            if not migration():
                failed_migrations.append(migration.__name__)
            else:
                logger.info(f"Migration {migration.__name__} terminée avec succès")
        except Exception as e:
            logger.error(f"Erreur lors de la migration {migration.__name__}: {str(e)}")
            failed_migrations.append(migration.__name__)

    if not failed_migrations:
        logger.info("Toutes les migrations ont été terminées avec succès")
        return True
    else:
        logger.error(f"Les migrations suivantes ont échoué: {', '.join(failed_migrations)}")
        # Ne pas retourner False ici car les tables existent et fonctionnent
        return True

if __name__ == "__main__":
    # Configuration du logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # Exécution des migrations
    run_migrations()

