"""
Utilitaires pour la gestion stricte des montants en Decimal.
"""
from decimal import Decimal, InvalidOperation, ROUND_HALF_UP, Context, localcontext
from typing import Union, Optional
import logging

logger = logging.getLogger(__name__)

# Configuration du contexte Decimal pour les calculs financiers
FINANCIAL_CONTEXT = Context(
    prec=28,  # Précision élevée
    rounding=ROUND_HALF_UP,  # Arrondi bancaire standard
    traps=[InvalidOperation]  # Lever une exception en cas d'opération invalide
)


class DecimalValidationError(ValueError):
    """Exception levée lors d'erreurs de validation de montants"""
    pass


class MoneyAmount:
    """
    Classe pour représenter et valider les montants monétaires.
    Garantit l'utilisation de Decimal avec une précision de 2 décimales.
    """
    
    def __init__(self, value: Union[str, int, float, Decimal], currency: str = "DA"):
        """
        Initialise un montant monétaire
        
        Args:
            value: Valeur du montant
            currency: Devise (par défaut "DA")
        
        Raises:
            DecimalValidationError: Si la valeur n'est pas valide
        """
        self.currency = currency
        self._amount = self._validate_and_normalize(value)
    
    def _validate_and_normalize(self, value: Union[str, int, float, Decimal]) -> Decimal:
        """
        Valide et normalise une valeur en Decimal avec 2 décimales
        
        Args:
            value: Valeur à valider
            
        Returns:
            Decimal normalisé
            
        Raises:
            DecimalValidationError: Si la valeur n'est pas valide
        """
        try:
            if isinstance(value, str):
                # Remplacer la virgule par un point
                value = value.replace(',', '.').strip()
                
                # Vérifier que c'est un nombre valide
                if not value or value in ['-', '+', '.']:
                    raise DecimalValidationError("Valeur vide ou invalide")
            
            # Convertir en Decimal avec le contexte financier
            with localcontext(FINANCIAL_CONTEXT):
                decimal_value = Decimal(str(value))

                # Vérifier les limites raisonnables
                if decimal_value.is_infinite() or decimal_value.is_nan():
                    raise DecimalValidationError("Valeur infinie ou NaN non autorisée")

                # Limiter à des valeurs raisonnables pour éviter les débordements
                max_value = Decimal("999999999.99")
                min_value = Decimal("-999999999.99")

                if decimal_value > max_value or decimal_value < min_value:
                    raise DecimalValidationError(f"Valeur hors limites ({min_value} à {max_value})")

                # Normaliser à 2 décimales
                normalized = decimal_value.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

                logger.debug(f"Montant validé et normalisé: {value} -> {normalized}")
                return normalized
                
        except (InvalidOperation, ValueError, TypeError) as e:
            raise DecimalValidationError(f"Impossible de convertir '{value}' en montant valide: {e}")
    
    @property
    def amount(self) -> Decimal:
        """Retourne le montant en Decimal"""
        return self._amount
    
    @property
    def value(self) -> Decimal:
        """Alias pour amount"""
        return self._amount
    
    def __str__(self) -> str:
        """Représentation en chaîne"""
        return f"{self._amount} {self.currency}"
    
    def __repr__(self) -> str:
        """Représentation pour le débogage"""
        return f"MoneyAmount({self._amount}, '{self.currency}')"
    
    def __eq__(self, other) -> bool:
        """Comparaison d'égalité"""
        if isinstance(other, MoneyAmount):
            return self._amount == other._amount and self.currency == other.currency
        elif isinstance(other, (Decimal, int, float, str)):
            try:
                other_amount = self._validate_and_normalize(other)
                return self._amount == other_amount
            except DecimalValidationError:
                return False
        return False
    
    def __lt__(self, other) -> bool:
        """Comparaison inférieur"""
        if isinstance(other, MoneyAmount):
            return self._amount < other._amount
        else:
            other_amount = self._validate_and_normalize(other)
            return self._amount < other_amount
    
    def __le__(self, other) -> bool:
        """Comparaison inférieur ou égal"""
        return self == other or self < other
    
    def __gt__(self, other) -> bool:
        """Comparaison supérieur"""
        return not self <= other
    
    def __ge__(self, other) -> bool:
        """Comparaison supérieur ou égal"""
        return not self < other
    
    def __add__(self, other) -> 'MoneyAmount':
        """Addition"""
        if isinstance(other, MoneyAmount):
            if self.currency != other.currency:
                raise DecimalValidationError(f"Impossible d'additionner {self.currency} et {other.currency}")
            result = self._amount + other._amount
        else:
            other_amount = self._validate_and_normalize(other)
            result = self._amount + other_amount
        
        return MoneyAmount(result, self.currency)
    
    def __sub__(self, other) -> 'MoneyAmount':
        """Soustraction"""
        if isinstance(other, MoneyAmount):
            if self.currency != other.currency:
                raise DecimalValidationError(f"Impossible de soustraire {other.currency} de {self.currency}")
            result = self._amount - other._amount
        else:
            other_amount = self._validate_and_normalize(other)
            result = self._amount - other_amount
        
        return MoneyAmount(result, self.currency)
    
    def __mul__(self, other) -> 'MoneyAmount':
        """Multiplication"""
        if isinstance(other, MoneyAmount):
            raise DecimalValidationError("Impossible de multiplier deux montants monétaires")
        
        other_decimal = Decimal(str(other))
        result = self._amount * other_decimal
        return MoneyAmount(result, self.currency)
    
    def __truediv__(self, other) -> 'MoneyAmount':
        """Division"""
        if isinstance(other, MoneyAmount):
            raise DecimalValidationError("Impossible de diviser deux montants monétaires")
        
        other_decimal = Decimal(str(other))
        if other_decimal == 0:
            raise DecimalValidationError("Division par zéro")
        
        result = self._amount / other_decimal
        return MoneyAmount(result, self.currency)
    
    def __neg__(self) -> 'MoneyAmount':
        """Négation"""
        return MoneyAmount(-self._amount, self.currency)
    
    def __abs__(self) -> 'MoneyAmount':
        """Valeur absolue"""
        return MoneyAmount(abs(self._amount), self.currency)
    
    def is_positive(self) -> bool:
        """Vérifie si le montant est positif"""
        return self._amount > 0
    
    def is_negative(self) -> bool:
        """Vérifie si le montant est négatif"""
        return self._amount < 0
    
    def is_zero(self) -> bool:
        """Vérifie si le montant est zéro"""
        return self._amount == 0


def validate_amount(value: Union[str, int, float, Decimal], 
                   min_value: Optional[Decimal] = None,
                   max_value: Optional[Decimal] = None,
                   allow_zero: bool = True,
                   allow_negative: bool = True) -> Decimal:
    """
    Valide et normalise un montant avec des contraintes optionnelles
    
    Args:
        value: Valeur à valider
        min_value: Valeur minimum autorisée
        max_value: Valeur maximum autorisée
        allow_zero: Autoriser la valeur zéro
        allow_negative: Autoriser les valeurs négatives
        
    Returns:
        Decimal validé et normalisé
        
    Raises:
        DecimalValidationError: Si la validation échoue
    """
    try:
        money = MoneyAmount(value)
        amount = money.amount
        
        # Vérifier les contraintes
        if not allow_zero and amount == 0:
            raise DecimalValidationError("La valeur zéro n'est pas autorisée")
        
        if not allow_negative and amount < 0:
            raise DecimalValidationError("Les valeurs négatives ne sont pas autorisées")
        
        if min_value is not None and amount < min_value:
            raise DecimalValidationError(f"La valeur doit être >= {min_value}")
        
        if max_value is not None and amount > max_value:
            raise DecimalValidationError(f"La valeur doit être <= {max_value}")
        
        return amount
        
    except DecimalValidationError:
        raise
    except Exception as e:
        raise DecimalValidationError(f"Erreur de validation: {e}")


def safe_decimal_operation(left, operator, right) -> Decimal:
    """
    Effectue une opération arithmétique sûre entre deux valeurs en les convertissant en Decimal

    Args:
        left: Valeur de gauche
        operator: Opérateur (+, -, *, /)
        right: Valeur de droite

    Returns:
        Résultat en Decimal

    Raises:
        DecimalValidationError: Si l'opération échoue
    """
    try:
        left_decimal = validate_amount(left) if left is not None else Decimal("0.00")
        right_decimal = validate_amount(right) if right is not None else Decimal("0.00")

        if operator == '+':
            result = left_decimal + right_decimal
        elif operator == '-':
            result = left_decimal - right_decimal
        elif operator == '*':
            result = left_decimal * right_decimal
        elif operator == '/':
            if right_decimal == 0:
                raise DecimalValidationError("Division par zéro")
            result = left_decimal / right_decimal
        else:
            raise DecimalValidationError(f"Opérateur non supporté: {operator}")

        return result.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    except Exception as e:
        raise DecimalValidationError(f"Erreur lors de l'opération {left} {operator} {right}: {e}")


def safe_decimal_sum(values, start_value: Decimal = None) -> Decimal:
    """
    Calcule la somme de valeurs en s'assurant que tout est en Decimal

    Args:
        values: Itérable de valeurs à sommer
        start_value: Valeur de départ (par défaut Decimal("0.00"))

    Returns:
        Somme en Decimal
    """
    if start_value is None:
        start_value = Decimal("0.00")

    result = start_value
    for value in values:
        if value is not None:
            result += validate_amount(value)

    return result.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)


def format_currency(amount: Union[Decimal, MoneyAmount], currency: str = "DA") -> str:
    """
    Formate un montant pour l'affichage
    
    Args:
        amount: Montant à formater
        currency: Devise
        
    Returns:
        Chaîne formatée
    """
    if isinstance(amount, MoneyAmount):
        return str(amount)
    else:
        validated = validate_amount(amount)
        return f"{validated} {currency}"
