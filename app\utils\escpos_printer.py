"""
Module pour l'impression sur les imprimantes ESC/POS.
"""
import os
import re
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import win32print
import win32ui
from escpos.printer import Usb, File, Network, Serial
from escpos.exceptions import USBNotFoundError
from app.utils.print_template_manager import PrintTemplateManager

logger = logging.getLogger(__name__)

class ESCPOSPrinter:
    """
    Classe pour gérer l'impression sur les imprimantes ESC/POS.
    """

    def __init__(self, printer_name: str = None, connection_type: str = "usb",
                 connection_params: Dict[str, Any] = None, template_dir: str = None):
        """
        Initialise l'imprimante ESC/POS.

        Args:
            printer_name: Nom de l'imprimante (pour l'impression via Windows)
            connection_type: Type de connexion ('usb', 'file', 'network', 'serial')
            connection_params: Paramètres de connexion spécifiques au type
            template_dir: Répertoire contenant les modèles d'impression
        """
        self.printer_name = printer_name
        self.connection_type = connection_type
        self.connection_params = connection_params or {}
        self.printer = None
        self.logger = logger

        # Initialiser le gestionnaire de modèles
        self.template_manager = PrintTemplateManager(template_dir)

        # Créer les modèles par défaut s'ils n'existent pas
        self.template_manager.create_default_templates()

    def connect(self) -> bool:
        """
        Établit la connexion avec l'imprimante.

        Returns:
            True si la connexion a réussi, False sinon.
        """
        try:
            if self.connection_type == "usb":
                # Paramètres par défaut pour les imprimantes USB
                vendor_id = self.connection_params.get("vendor_id", 0x0416)
                product_id = self.connection_params.get("product_id", 0x5011)
                in_ep = self.connection_params.get("in_ep", 0x81)
                out_ep = self.connection_params.get("out_ep", 0x03)

                self.printer = Usb(vendor_id, product_id, in_ep=in_ep, out_ep=out_ep)
            elif self.connection_type == "file":
                # Pour les imprimantes connectées via un fichier (comme /dev/usb/lp0)
                device_path = self.connection_params.get("device_path", "/dev/usb/lp0")
                self.printer = File(device_path)
            elif self.connection_type == "network":
                # Pour les imprimantes réseau
                host = self.connection_params.get("host", "*************")
                port = self.connection_params.get("port", 9100)
                self.printer = Network(host, port)
            elif self.connection_type == "serial":
                # Pour les imprimantes série
                devfile = self.connection_params.get("devfile", "/dev/ttyS0")
                baudrate = self.connection_params.get("baudrate", 9600)
                self.printer = Serial(devfile=devfile, baudrate=baudrate)
            elif self.connection_type == "windows":
                # Pour l'impression via le système d'impression Windows
                if not self.printer_name:
                    self.printer_name = win32print.GetDefaultPrinter()
                # Pas besoin de créer un objet printer ici, on utilisera win32print
                return True
            else:
                self.logger.error(f"Type de connexion non pris en charge: {self.connection_type}")
                return False

            return True
        except USBNotFoundError:
            self.logger.error("Imprimante USB non trouvée")
            return False
        except Exception as e:
            self.logger.error(f"Erreur lors de la connexion à l'imprimante: {str(e)}")
            return False

    def disconnect(self):
        """
        Ferme la connexion avec l'imprimante.
        """
        if self.printer and hasattr(self.printer, 'close'):
            try:
                self.printer.close()
            except Exception as e:
                self.logger.error(f"Erreur lors de la déconnexion de l'imprimante: {str(e)}")

    def print_repair_deposit_receipt(self, repair_data: Dict[str, Any]) -> bool:
        """
        Imprime un reçu de dépôt pour une réparation sur une imprimante ESC/POS.

        Args:
            repair_data: Données de la réparation

        Returns:
            True si l'impression a réussi, False sinon.
        """
        return self.print_from_template("deposit_receipt_escpos", repair_data)

    def print_repair_receipt(self, repair_data: Dict[str, Any]) -> bool:
        """
        Imprime un reçu de réparation sur une imprimante ESC/POS.

        Args:
            repair_data: Données de la réparation

        Returns:
            True si l'impression a réussi, False sinon.
        """
        return self.print_from_template("repair_receipt_escpos", repair_data)

    def _print_repair_deposit_receipt_escpos(self, repair_data: Dict[str, Any]) -> bool:
        """
        Imprime un reçu de dépôt pour une réparation sur une imprimante ESC/POS.

        Args:
            repair_data: Données de la réparation

        Returns:
            True si l'impression a réussi, False sinon.
        """
        try:
            # Titre
            self.printer.set(align='center', font='a', width=2, height=2)
            self.printer.text("REÇU DE DÉPÔT\n")
            self.printer.text("RÉPARATION\n\n")

            # Informations de base
            self.printer.set(align='center', font='a', width=1, height=1)
            self.printer.text(f"Réparation #{repair_data.get('number', 'N/A')}\n\n")

            # Date et statut
            self.printer.set(align='left', font='a', width=1, height=1)
            date_str = repair_data.get('created_at', datetime.now()).strftime("%d/%m/%Y")
            self.printer.text(f"Date de dépôt: {date_str}\n")

            # Informations du client
            customer = repair_data.get('customer', {})
            self.printer.text(f"Client: {customer.get('name', 'N/A')}\n")
            self.printer.text(f"Téléphone: {customer.get('phone', 'N/A')}\n\n")

            # Informations de l'appareil
            device = repair_data.get('device', {})
            self.printer.text(f"Appareil: {device.get('brand', 'N/A')} {device.get('model', 'N/A')}\n")
            self.printer.text(f"IMEI/SN: {device.get('imei', 'N/A')}\n\n")

            # Problème et diagnostic
            self.printer.text(f"Problème: {repair_data.get('issue', 'N/A')}\n\n")

            # Date de fin prévue
            expected_completion_date = repair_data.get('expected_completion_date', '')
            if expected_completion_date:
                if isinstance(expected_completion_date, datetime):
                    expected_completion_date = expected_completion_date.strftime("%d/%m/%Y")
                self.printer.text(f"Date de fin prévue: {expected_completion_date}\n\n")

            # Ligne de séparation
            self.printer.text("--------------------------------\n\n")

            # Signature
            self.printer.text("Signature:\n\n\n\n\n")

            # Pied de page
            self.printer.set(align='center', font='a', width=1, height=1)
            self.printer.text("Merci de votre confiance!\n\n")

            # Couper le papier
            self.printer.cut()

            return True
        except Exception as e:
            self.logger.error(f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")
            return False

    def _print_repair_deposit_receipt_windows(self, repair_data: Dict[str, Any]) -> bool:
        """
        Imprime un reçu de dépôt pour une réparation via le système d'impression Windows.

        Args:
            repair_data: Données de la réparation

        Returns:
            True si l'impression a réussi, False sinon.
        """
        try:
            # Créer un fichier temporaire avec les commandes ESC/POS
            import tempfile

            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.bin', mode='wb')
            try:
                # Commandes ESC/POS pour le reçu
                commands = []

                # Initialiser l'imprimante
                commands.append(b'\x1B\x40')  # ESC @ - Initialiser l'imprimante

                # Titre
                commands.append(b'\x1B\x61\x01')  # ESC a 1 - Centrer
                commands.append(b'\x1B\x21\x30')  # ESC ! 0x30 - Double largeur et hauteur
                commands.append(b'RECU DE DEPOT\n')
                commands.append(b'REPARATION\n\n')

                # Informations de base
                commands.append(b'\x1B\x21\x00')  # ESC ! 0x00 - Normal
                commands.append(b'\x1B\x61\x01')  # ESC a 1 - Centrer
                commands.append(f"Reparation #{repair_data.get('number', 'N/A')}\n\n".encode('cp850'))

                # Date et statut
                commands.append(b'\x1B\x61\x00')  # ESC a 0 - Aligner à gauche
                date_str = repair_data.get('created_at', datetime.now()).strftime("%d/%m/%Y")
                commands.append(f"Date de depot: {date_str}\n".encode('cp850'))

                # Informations du client
                customer = repair_data.get('customer', {})
                commands.append(f"Client: {customer.get('name', 'N/A')}\n".encode('cp850'))
                commands.append(f"Telephone: {customer.get('phone', 'N/A')}\n\n".encode('cp850'))

                # Informations de l'appareil
                device = repair_data.get('device', {})
                commands.append(f"Appareil: {device.get('brand', 'N/A')} {device.get('model', 'N/A')}\n".encode('cp850'))
                commands.append(f"IMEI/SN: {device.get('imei', 'N/A')}\n\n".encode('cp850'))

                # Problème et diagnostic
                commands.append(f"Probleme: {repair_data.get('issue', 'N/A')}\n\n".encode('cp850'))

                # Date de fin prévue
                expected_completion_date = repair_data.get('expected_completion_date', '')
                if expected_completion_date:
                    if isinstance(expected_completion_date, datetime):
                        expected_completion_date = expected_completion_date.strftime("%d/%m/%Y")
                    commands.append(f"Date de fin prevue: {expected_completion_date}\n\n".encode('cp850'))

                # Ligne de séparation
                commands.append(b'--------------------------------\n\n')

                # Signature
                commands.append(b'Signature:\n\n\n\n\n')

                # Pied de page
                commands.append(b'\x1B\x61\x01')  # ESC a 1 - Centrer
                commands.append(b'Merci de votre confiance!\n\n')

                # Couper le papier
                commands.append(b'\x1D\x56\x41')  # GS V A - Couper le papier

                # Écrire les commandes dans le fichier temporaire
                for cmd in commands:
                    temp_file.write(cmd)

                temp_file.close()

                # Imprimer le fichier via Windows
                printer_handle = win32print.OpenPrinter(self.printer_name)
                try:
                    job = win32print.StartDocPrinter(printer_handle, 1, ("Receipt", None, "RAW"))
                    try:
                        win32print.StartPagePrinter(printer_handle)
                        with open(temp_file.name, 'rb') as f:
                            data = f.read()
                            win32print.WritePrinter(printer_handle, data)
                        win32print.EndPagePrinter(printer_handle)
                    finally:
                        win32print.EndDocPrinter(printer_handle)
                finally:
                    win32print.ClosePrinter(printer_handle)

                return True
            finally:
                # Supprimer le fichier temporaire
                try:
                    os.unlink(temp_file.name)
                except:
                    pass
        except Exception as e:
            self.logger.error(f"Erreur lors de l'impression du reçu de dépôt via Windows: {str(e)}")
            return False

    def print_from_template(self, template_name: str, data: Dict[str, Any]) -> bool:
        """
        Imprime un document en utilisant un modèle.

        Args:
            template_name: Nom du modèle à utiliser
            data: Données à utiliser pour le rendu du modèle

        Returns:
            True si l'impression a réussi, False sinon
        """
        try:
            if not self.connect():
                return False

            # Récupérer le modèle
            template = self.template_manager.get_template(template_name)
            if not template:
                self.logger.error(f"Modèle non trouvé: {template_name}")
                return False

            # Rendre le modèle avec les données
            rendered_content = self.template_manager.render_template(template_name, data)
            if not rendered_content:
                self.logger.error(f"Erreur lors du rendu du modèle: {template_name}")
                return False

            # Imprimer le contenu rendu
            if self.connection_type == "windows":
                return self._print_escpos_commands_windows(rendered_content)
            else:
                return self._print_escpos_commands_direct(rendered_content)
        except Exception as e:
            self.logger.error(f"Erreur lors de l'impression avec le modèle {template_name}: {str(e)}")
            return False
        finally:
            self.disconnect()

    def _print_escpos_commands_direct(self, commands: str) -> bool:
        """
        Imprime des commandes ESC/POS directement sur l'imprimante.

        Args:
            commands: Commandes ESC/POS à imprimer

        Returns:
            True si l'impression a réussi, False sinon
        """
        try:
            # Analyser les commandes ESC/POS
            lines = commands.strip().split('\n')
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):  # Ignorer les lignes vides et les commentaires
                    continue

                # Traiter les commandes ESC/POS
                if line.startswith('ESC'):
                    parts = line.split(' ', 1)
                    if len(parts) > 1:
                        command = parts[0]
                        args = parts[1].strip()

                        if command == 'ESC' and args == '@':
                            self.printer.init()
                        elif command == 'ESC' and args.startswith('a'):
                            align = int(args.split(' ')[1])
                            self.printer.set(align=('left' if align == 0 else 'center' if align == 1 else 'right'))
                        elif command == 'ESC' and args.startswith('!'):
                            mode = int(args.split(' ')[1], 16)
                            # Interpréter les bits du mode
                            font = 'b' if (mode & 0x01) else 'a'
                            width = 2 if (mode & 0x20) else 1
                            height = 2 if (mode & 0x10) else 1
                            self.printer.set(font=font, width=width, height=height)
                elif line.startswith('GS'):
                    parts = line.split(' ', 2)
                    if len(parts) > 2 and parts[0] == 'GS' and parts[1] == 'V' and parts[2] == 'A':
                        self.printer.cut()
                else:
                    # Texte normal
                    self.printer.text(line + '\n')

            return True
        except Exception as e:
            self.logger.error(f"Erreur lors de l'impression des commandes ESC/POS: {str(e)}")
            return False

    def _print_escpos_commands_windows(self, commands: str) -> bool:
        """
        Imprime des commandes ESC/POS via Windows.

        Args:
            commands: Commandes ESC/POS à imprimer

        Returns:
            True si l'impression a réussi, False sinon
        """
        try:
            # Créer un fichier temporaire avec les commandes ESC/POS
            import tempfile

            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.bin', mode='wb')
            try:
                # Convertir les commandes ESC/POS en binaire
                binary_commands = bytearray()

                lines = commands.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith('#'):  # Ignorer les lignes vides et les commentaires
                        continue

                    # Traiter les commandes ESC/POS
                    if line.startswith('ESC'):
                        parts = line.split(' ', 1)
                        if len(parts) > 1:
                            command = parts[0]
                            args = parts[1].strip()

                            if command == 'ESC' and args == '@':
                                binary_commands.extend(b'\x1B\x40')  # ESC @ - Initialiser l'imprimante
                            elif command == 'ESC' and args.startswith('a'):
                                align = int(args.split(' ')[1])
                                binary_commands.extend(b'\x1B\x61' + bytes([align]))  # ESC a n - Alignement
                            elif command == 'ESC' and args.startswith('!'):
                                mode = int(args.split(' ')[1], 16)
                                binary_commands.extend(b'\x1B\x21' + bytes([mode]))  # ESC ! n - Mode
                    elif line.startswith('GS'):
                        parts = line.split(' ', 2)
                        if len(parts) > 2 and parts[0] == 'GS' and parts[1] == 'V' and parts[2] == 'A':
                            binary_commands.extend(b'\x1D\x56\x41')  # GS V A - Couper le papier
                    else:
                        # Texte normal
                        binary_commands.extend((line + '\n').encode('cp850'))

                # Écrire les commandes dans le fichier temporaire
                temp_file.write(binary_commands)
                temp_file.close()

                # Imprimer le fichier via Windows
                printer_handle = win32print.OpenPrinter(self.printer_name)
                try:
                    job = win32print.StartDocPrinter(printer_handle, 1, ("Receipt", None, "RAW"))
                    try:
                        win32print.StartPagePrinter(printer_handle)
                        with open(temp_file.name, 'rb') as f:
                            data = f.read()
                            win32print.WritePrinter(printer_handle, data)
                        win32print.EndPagePrinter(printer_handle)
                    finally:
                        win32print.EndDocPrinter(printer_handle)
                finally:
                    win32print.ClosePrinter(printer_handle)

                return True
            finally:
                # Supprimer le fichier temporaire
                try:
                    os.unlink(temp_file.name)
                except:
                    pass
        except Exception as e:
            self.logger.error(f"Erreur lors de l'impression des commandes ESC/POS via Windows: {str(e)}")
            return False

    def print_invoice(self, invoice_data: Dict[str, Any]) -> bool:
        """
        Imprime une facture sur une imprimante ESC/POS.

        Args:
            invoice_data: Données de la facture

        Returns:
            True si l'impression a réussi, False sinon
        """
        return self.print_from_template("invoice_escpos", invoice_data)

    def print_payment_receipt(self, payment_data: Dict[str, Any]) -> bool:
        """
        Imprime un reçu de paiement sur une imprimante ESC/POS.

        Args:
            payment_data: Données du paiement

        Returns:
            True si l'impression a réussi, False sinon
        """
        return self.print_from_template("payment_receipt_escpos", payment_data)