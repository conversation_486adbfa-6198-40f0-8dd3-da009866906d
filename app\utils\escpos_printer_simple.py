"""
Module pour l'impression sur les imprimantes ESC/POS (version simplifiée).
Cette version n'utilise pas le module escpos mais uniquement l'impression via Windows.
"""
import os
import re
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import win32print
import win32ui
from app.utils.print_template_manager import PrintTemplateManager

logger = logging.getLogger(__name__)

class ESCPOSPrinter:
    """
    Classe pour gérer l'impression sur les imprimantes ESC/POS.
    Version simplifiée qui n'utilise pas le module escpos.
    """

    def __init__(self, printer_name: str = None, template_dir: str = None):
        """
        Initialise l'imprimante ESC/POS.

        Args:
            printer_name: Nom de l'imprimante (pour l'impression via Windows)
            template_dir: Répertoire contenant les modèles d'impression
        """
        self.printer_name = printer_name or win32print.GetDefaultPrinter()
        self.logger = logger

        # Initialiser le gestionnaire de modèles
        self.template_manager = PrintTemplateManager(template_dir)

        # Créer les modèles par défaut s'ils n'existent pas
        self.template_manager.create_default_templates()

    def print_repair_deposit_receipt(self, repair_data: Dict[str, Any]) -> bool:
        """
        Imprime un reçu de dépôt pour une réparation sur une imprimante ESC/POS.

        Args:
            repair_data: Données de la réparation

        Returns:
            True si l'impression a réussi, False sinon.
        """
        return self.print_from_template("deposit_receipt_escpos", repair_data)

    def print_from_template(self, template_name: str, data: Dict[str, Any]) -> bool:
        """
        Imprime un document en utilisant un modèle.

        Args:
            template_name: Nom du modèle à utiliser
            data: Données à utiliser pour le rendu du modèle

        Returns:
            True si l'impression a réussi, False sinon
        """
        try:
            # Récupérer le modèle
            template = self.template_manager.get_template(template_name)
            if not template:
                self.logger.error(f"Modèle non trouvé: {template_name}")
                return False

            # Rendre le modèle avec les données
            rendered_content = self.template_manager.render_template(template_name, data)
            if not rendered_content:
                self.logger.error(f"Erreur lors du rendu du modèle: {template_name}")
                return False

            # Imprimer le contenu rendu
            return self._print_escpos_commands_windows(rendered_content)
        except Exception as e:
            self.logger.error(f"Erreur lors de l'impression avec le modèle {template_name}: {str(e)}")
            return False

    def _print_escpos_commands_windows(self, commands: str) -> bool:
        """
        Imprime des commandes ESC/POS via Windows.

        Args:
            commands: Commandes ESC/POS à imprimer

        Returns:
            True si l'impression a réussi, False sinon
        """
        try:
            # Créer un fichier temporaire avec les commandes ESC/POS
            import tempfile

            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.bin', mode='wb')
            try:
                # Convertir les commandes ESC/POS en binaire
                binary_commands = bytearray()

                lines = commands.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith('#'):  # Ignorer les lignes vides et les commentaires
                        continue

                    # Traiter les commandes ESC/POS
                    if line.startswith('ESC'):
                        parts = line.split(' ', 1)
                        if len(parts) > 1:
                            command = parts[0]
                            args = parts[1].strip()

                            if command == 'ESC' and args == '@':
                                binary_commands.extend(b'\x1B\x40')  # ESC @ - Initialiser l'imprimante
                            elif command == 'ESC' and args.startswith('a'):
                                align = int(args.split(' ')[1])
                                binary_commands.extend(b'\x1B\x61' + bytes([align]))  # ESC a n - Alignement
                            elif command == 'ESC' and args.startswith('!'):
                                mode = int(args.split(' ')[1], 16)
                                binary_commands.extend(b'\x1B\x21' + bytes([mode]))  # ESC ! n - Mode
                    elif line.startswith('GS'):
                        parts = line.split(' ', 2)
                        if len(parts) > 2 and parts[0] == 'GS' and parts[1] == 'V' and parts[2] == 'A':
                            binary_commands.extend(b'\x1D\x56\x41')  # GS V A - Couper le papier
                    else:
                        # Texte normal
                        binary_commands.extend((line + '\n').encode('cp850'))

                # Écrire les commandes dans le fichier temporaire
                temp_file.write(binary_commands)
                temp_file.close()

                # Imprimer le fichier via Windows
                printer_handle = win32print.OpenPrinter(self.printer_name)
                try:
                    job = win32print.StartDocPrinter(printer_handle, 1, ("Receipt", None, "RAW"))
                    try:
                        win32print.StartPagePrinter(printer_handle)
                        with open(temp_file.name, 'rb') as f:
                            data = f.read()
                            win32print.WritePrinter(printer_handle, data)
                        win32print.EndPagePrinter(printer_handle)
                    finally:
                        win32print.EndDocPrinter(printer_handle)
                finally:
                    win32print.ClosePrinter(printer_handle)

                return True
            finally:
                # Supprimer le fichier temporaire
                try:
                    os.unlink(temp_file.name)
                except:
                    pass
        except Exception as e:
            self.logger.error(f"Erreur lors de l'impression des commandes ESC/POS via Windows: {str(e)}")
            return False

    def print_invoice(self, invoice_data: Dict[str, Any]) -> bool:
        """
        Imprime une facture sur une imprimante ESC/POS.

        Args:
            invoice_data: Données de la facture

        Returns:
            True si l'impression a réussi, False sinon
        """
        return self.print_from_template("invoice_escpos", invoice_data)

    def print_payment_receipt(self, payment_data: Dict[str, Any]) -> bool:
        """
        Imprime un reçu de paiement sur une imprimante ESC/POS.

        Args:
            payment_data: Données du paiement

        Returns:
            True si l'impression a réussi, False sinon
        """
        return self.print_from_template("payment_receipt_escpos", payment_data)
