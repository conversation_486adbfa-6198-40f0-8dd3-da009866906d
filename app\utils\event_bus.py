from PyQt6.QtCore import QObject, pyqtSignal, QTimer
import logging

logger = logging.getLogger(__name__)

class EventBus(QObject):
    """Bus d'événements pour la communication entre les différentes vues"""

    # Signal émis lorsque le stock est mis à jour
    stock_updated = pyqtSignal()

    # Signal émis lorsqu'une réparation est mise à jour
    repair_updated = pyqtSignal(int)  # repair_id

    # Signal émis lorsqu'une facture est créée
    invoice_created = pyqtSignal(int)  # invoice_id

    # Signal émis lorsqu'un paiement est effectué
    payment_processed = pyqtSignal(int)  # payment_id

    # Signal émis lorsque la trésorerie est mise à jour
    treasury_updated = pyqtSignal()

    # Signal émis lorsqu'une transaction de caisse est ajoutée
    cash_transaction_added = pyqtSignal(int)  # cash_register_id

    # Signaux pour les notifications toast
    show_success_notification = pyqtSignal(str)  # message
    show_info_notification = pyqtSignal(str)  # message
    show_warning_notification = pyqtSignal(str)  # message
    show_error_notification = pyqtSignal(str)  # message

    # Signaux pour les opérations de trésorerie
    transaction_created = pyqtSignal(dict)  # transaction_data
    expense_created = pyqtSignal(dict)  # expense_data
    transfer_completed = pyqtSignal(dict)  # transfer_data
    cash_register_updated = pyqtSignal(int)  # register_id

    def force_stock_update(self):
        """Force l'émission du signal stock_updated"""
        logger.info("Forçage de l'émission du signal stock_updated")
        self.stock_updated.emit()
        logger.info("Signal stock_updated émis avec succès via force_stock_update")

    def force_treasury_update(self):
        """Force l'émission du signal treasury_updated"""
        logger.info("Forçage de l'émission du signal treasury_updated")
        self.treasury_updated.emit()
        logger.info("Signal treasury_updated émis avec succès via force_treasury_update")

    def notify_cash_transaction_added(self, cash_register_id: int):
        """Notifie qu'une transaction de caisse a été ajoutée"""
        logger.info(f"Notification d'ajout de transaction pour la caisse {cash_register_id}")
        self.cash_transaction_added.emit(cash_register_id)
        # Émettre aussi le signal général de mise à jour de la trésorerie
        self.treasury_updated.emit()
        logger.info("Signaux cash_transaction_added et treasury_updated émis")

    # Méthodes pour les notifications toast
    def show_success(self, message: str):
        """Affiche une notification de succès"""
        logger.info(f"Notification de succès: {message}")
        self.show_success_notification.emit(message)

    def show_info(self, message: str):
        """Affiche une notification d'information"""
        logger.info(f"Notification d'info: {message}")
        self.show_info_notification.emit(message)

    def show_warning(self, message: str):
        """Affiche une notification d'avertissement"""
        logger.warning(f"Notification d'avertissement: {message}")
        self.show_warning_notification.emit(message)

    def show_error(self, message: str):
        """Affiche une notification d'erreur"""
        logger.error(f"Notification d'erreur: {message}")
        self.show_error_notification.emit(message)

    # Méthodes pour les opérations de trésorerie
    def notify_transaction_created(self, transaction_data: dict):
        """Notifie qu'une transaction a été créée"""
        logger.info(f"Transaction créée: {transaction_data.get('id', 'N/A')}")
        self.transaction_created.emit(transaction_data)
        self.treasury_updated.emit()

    def notify_expense_created(self, expense_data: dict):
        """Notifie qu'une dépense a été créée"""
        logger.info(f"Dépense créée: {expense_data.get('id', 'N/A')}")
        self.expense_created.emit(expense_data)
        self.treasury_updated.emit()

    def notify_transfer_completed(self, transfer_data: dict):
        """Notifie qu'un transfert a été effectué"""
        logger.info(f"Transfert effectué: {transfer_data.get('amount', 'N/A')} DA")
        self.transfer_completed.emit(transfer_data)
        self.treasury_updated.emit()

    def notify_cash_register_updated(self, register_id: int):
        """Notifie qu'une caisse a été mise à jour"""
        logger.info(f"Caisse mise à jour: {register_id}")
        self.cash_register_updated.emit(register_id)
        self.treasury_updated.emit()

# Instance unique du bus d'événements
event_bus = EventBus()

# Connecter les signaux aux logs pour le débogage
event_bus.stock_updated.connect(lambda: logger.info("Signal stock_updated reçu"))
event_bus.repair_updated.connect(lambda repair_id: logger.info(f"Signal repair_updated reçu pour la réparation {repair_id}"))
event_bus.invoice_created.connect(lambda invoice_id: logger.info(f"Signal invoice_created reçu pour la facture {invoice_id}"))
event_bus.payment_processed.connect(lambda payment_id: logger.info(f"Signal payment_processed reçu pour le paiement {payment_id}"))
event_bus.treasury_updated.connect(lambda: logger.info("Signal treasury_updated reçu"))
event_bus.cash_transaction_added.connect(lambda cash_register_id: logger.info(f"Signal cash_transaction_added reçu pour la caisse {cash_register_id}"))
