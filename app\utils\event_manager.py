"""
Gestionnaire d'événements pour l'application.
Permet aux différentes parties de l'application de s'abonner à des événements et d'en émettre.
"""
from typing import Dict, List, Callable, Any
import logging

# Configurer le logger
logger = logging.getLogger(__name__)

class EventManager:
    """
    Gestionnaire d'événements centralisé pour l'application.
    Implémente le pattern Observer/Observable.
    """
    _instance = None
    
    def __new__(cls):
        """Implémentation du pattern Singleton"""
        if cls._instance is None:
            cls._instance = super(EventManager, cls).__new__(cls)
            cls._instance._subscribers = {}
            logger.info("EventManager: Instance créée")
        return cls._instance
    
    def __init__(self):
        """Initialise le gestionnaire d'événements"""
        # Déjà initialisé dans __new__ si c'est une nouvelle instance
        pass
    
    def subscribe(self, event_type: str, callback: Callable) -> None:
        """
        Abonne une fonction de rappel à un type d'événement.
        
        Args:
            event_type: Type d'événement auquel s'abonner
            callback: Fonction de rappel à appeler lorsque l'événement est émis
        """
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        
        if callback not in self._subscribers[event_type]:
            self._subscribers[event_type].append(callback)
            logger.debug(f"EventManager: Abonnement à l'événement '{event_type}'")
        else:
            logger.warning(f"EventManager: Tentative d'abonnement multiple à l'événement '{event_type}'")
    
    def unsubscribe(self, event_type: str, callback: Callable) -> None:
        """
        Désabonne une fonction de rappel d'un type d'événement.
        
        Args:
            event_type: Type d'événement duquel se désabonner
            callback: Fonction de rappel à désabonner
        """
        if event_type in self._subscribers and callback in self._subscribers[event_type]:
            self._subscribers[event_type].remove(callback)
            logger.debug(f"EventManager: Désabonnement de l'événement '{event_type}'")
            
            # Supprimer la liste si elle est vide
            if not self._subscribers[event_type]:
                del self._subscribers[event_type]
    
    def emit(self, event_type: str, **kwargs) -> None:
        """
        Émet un événement aux abonnés.
        
        Args:
            event_type: Type d'événement à émettre
            **kwargs: Arguments à passer aux fonctions de rappel
        """
        if event_type not in self._subscribers:
            logger.debug(f"EventManager: Aucun abonné pour l'événement '{event_type}'")
            return
        
        logger.debug(f"EventManager: Émission de l'événement '{event_type}' avec {len(self._subscribers[event_type])} abonnés")
        
        # Copier la liste des abonnés pour éviter les problèmes si un abonné se désabonne pendant l'émission
        subscribers = self._subscribers[event_type].copy()
        
        for callback in subscribers:
            try:
                callback(**kwargs)
            except Exception as e:
                logger.error(f"EventManager: Erreur lors de l'appel d'un abonné pour l'événement '{event_type}': {str(e)}")
    
    def get_subscribers(self, event_type: str = None) -> Dict[str, List[Callable]]:
        """
        Retourne la liste des abonnés pour un type d'événement ou tous les abonnés.
        
        Args:
            event_type: Type d'événement pour lequel récupérer les abonnés (None pour tous)
            
        Returns:
            Dictionnaire des abonnés par type d'événement
        """
        if event_type:
            return {event_type: self._subscribers.get(event_type, [])}
        return self._subscribers.copy()


# Définition des types d'événements
class EventType:
    """Types d'événements disponibles dans l'application"""
    
    # Événements liés à l'inventaire
    INVENTORY_ITEM_CREATED = "inventory.item.created"
    INVENTORY_ITEM_UPDATED = "inventory.item.updated"
    INVENTORY_ITEM_DELETED = "inventory.item.deleted"
    INVENTORY_STOCK_UPDATED = "inventory.stock.updated"
    INVENTORY_PRICE_UPDATED = "inventory.price.updated"
    
    # Événements liés aux achats
    PURCHASE_ORDER_CREATED = "purchase.order.created"
    PURCHASE_ORDER_UPDATED = "purchase.order.updated"
    PURCHASE_ORDER_DELETED = "purchase.order.deleted"
    PURCHASE_ORDER_RECEIVED = "purchase.order.received"
    PURCHASE_ITEM_RECEIVED = "purchase.item.received"
    
    # Événements liés aux ventes
    SALE_CREATED = "sale.created"
    SALE_UPDATED = "sale.updated"
    SALE_DELETED = "sale.deleted"
    SALE_COMPLETED = "sale.completed"
    SALE_PAYMENT_RECORDED = "sale.payment.recorded"
    
    # Événements liés aux clients
    CUSTOMER_CREATED = "customer.created"
    CUSTOMER_UPDATED = "customer.updated"
    CUSTOMER_DELETED = "customer.deleted"
    CUSTOMER_BALANCE_UPDATED = "customer.balance.updated"
    
    # Événements liés aux fournisseurs
    SUPPLIER_CREATED = "supplier.created"
    SUPPLIER_UPDATED = "supplier.updated"
    SUPPLIER_DELETED = "supplier.deleted"
    SUPPLIER_BALANCE_UPDATED = "supplier.balance.updated"
    
    # Événements liés aux réparations
    REPAIR_CREATED = "repair.created"
    REPAIR_UPDATED = "repair.updated"
    REPAIR_DELETED = "repair.deleted"
    REPAIR_STATUS_CHANGED = "repair.status.changed"
    REPAIR_PAYMENT_RECORDED = "repair.payment.recorded"


# Instance singleton du gestionnaire d'événements
event_manager = EventManager()
