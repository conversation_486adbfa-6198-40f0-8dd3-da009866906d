"""
Utilitaires pour l'exportation de rapports en différents formats.
"""
import os
import csv
import pandas as pd
from datetime import datetime
from PyQt6.QtCore import Qt, QObject, QRunnable, pyqtSignal, pyqtSlot, QThreadPool
from PyQt6.QtWidgets import QTableView, QFileDialog, QMessageBox, QTableWidget
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages

def export_table_data(table_view: QTableView, file_path: str):
    """Exports data from a QTableView to a CSV file."""
    try:
        model = table_view.model()
        with open(file_path, 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)

            # Write header
            header = [model.headerData(i, Qt.Orientation.Horizontal) for i in range(model.columnCount())]
            writer.writerow(header)

            # Write data rows
            for row in range(model.rowCount()):
                row_data = [model.data(model.index(row, col)) for col in range(model.columnCount())]
                writer.writerow(row_data)

        print(f"Data successfully exported to {file_path}")
    except Exception as e:
        print(f"Error exporting data: {e}")

class ExportSignals(QObject):
    """Signaux pour l'exportation"""
    finished = pyqtSignal()
    error = pyqtSignal(str)
    progress = pyqtSignal(int)

class ExportWorker(QRunnable):
    """Travailleur pour l'exportation en arrière-plan"""

    def __init__(self, export_func, *args, **kwargs):
        super().__init__()
        self.export_func = export_func
        self.args = args
        self.kwargs = kwargs
        self.signals = ExportSignals()

    @pyqtSlot()
    def run(self):
        """Exécute la fonction d'exportation"""
        try:
            self.export_func(*self.args, **self.kwargs)
            self.signals.finished.emit()
        except Exception as e:
            self.signals.error.emit(str(e))

def export_to_excel(data, filename=None, parent=None):
    """
    Exporte des données vers un fichier Excel

    Args:
        data: Dictionnaire de DataFrames à exporter (clé = nom de l'onglet)
        filename: Nom du fichier (optionnel)
        parent: Widget parent pour les boîtes de dialogue
    """
    if not filename:
        # Demander le nom du fichier
        default_name = f"rapport_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filename, _ = QFileDialog.getSaveFileName(
            parent,
            "Exporter vers Excel",
            default_name,
            "Fichiers Excel (*.xlsx)"
        )

    if not filename:
        return False

    try:
        # Créer un writer Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Exporter chaque DataFrame dans un onglet
            for sheet_name, df in data.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)

        return True
    except Exception as e:
        QMessageBox.critical(
            parent,
            "Erreur d'exportation",
            f"Erreur lors de l'exportation vers Excel: {str(e)}"
        )
        return False

def export_to_pdf(figures, filename=None, parent=None):
    """
    Exporte des figures Matplotlib vers un fichier PDF

    Args:
        figures: Liste de figures Matplotlib à exporter
        filename: Nom du fichier (optionnel)
        parent: Widget parent pour les boîtes de dialogue
    """
    if not filename:
        # Demander le nom du fichier
        default_name = f"rapport_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filename, _ = QFileDialog.getSaveFileName(
            parent,
            "Exporter vers PDF",
            default_name,
            "Fichiers PDF (*.pdf)"
        )

    if not filename:
        return False

    try:
        # Créer un fichier PDF
        with PdfPages(filename) as pdf:
            # Ajouter chaque figure
            for fig in figures:
                pdf.savefig(fig)

        return True
    except Exception as e:
        QMessageBox.critical(
            parent,
            "Erreur d'exportation",
            f"Erreur lors de l'exportation vers PDF: {str(e)}"
        )
        return False

def table_to_dataframe(table_widget):
    """
    Convertit un QTableWidget en DataFrame pandas

    Args:
        table_widget: QTableWidget à convertir

    Returns:
        DataFrame pandas
    """
    # Récupérer les en-têtes
    headers = []
    for i in range(table_widget.columnCount()):
        header_item = table_widget.horizontalHeaderItem(i)
        headers.append(header_item.text() if header_item else f"Column {i}")

    # Récupérer les données
    data = []
    for row in range(table_widget.rowCount()):
        row_data = []
        for col in range(table_widget.columnCount()):
            item = table_widget.item(row, col)
            row_data.append(item.text() if item else "")
        data.append(row_data)

    # Créer le DataFrame
    return pd.DataFrame(data, columns=headers)

def export_profit_loss_report(report_data, figure, parent=None):
    """
    Exporte un rapport de profits et pertes

    Args:
        report_data: Données du rapport
        figure: Figure Matplotlib
        parent: Widget parent pour les boîtes de dialogue
    """
    # Créer un DataFrame pour le résumé
    summary_data = {
        "Métrique": [
            "Revenus totaux",
            "Coût des marchandises vendues",
            "Marge brute",
            "Pourcentage de marge brute",
            "Dépenses d'achat",
            "Résultat net"
        ],
        "Valeur": [
            f"{report_data['total_revenue']:.2f} DA",
            f"{report_data['total_cogs']:.2f} DA",
            f"{report_data['gross_profit']:.2f} DA",
            f"{report_data['gross_margin_percent']:.2f}%",
            f"{report_data['total_purchase_expenses']:.2f} DA",
            f"{report_data['net_profit']:.2f} DA"
        ]
    }
    summary_df = pd.DataFrame(summary_data)

    # Exporter vers Excel
    data = {
        "Résumé": summary_df
    }

    # Demander le format d'exportation
    export_format = QMessageBox.question(
        parent,
        "Format d'exportation",
        "Choisissez le format d'exportation:",
        QMessageBox.StandardButton.Save | QMessageBox.StandardButton.Cancel,
        QMessageBox.StandardButton.Save
    )

    if export_format == QMessageBox.StandardButton.Save:
        # Demander le nom du fichier
        default_name = f"rapport_profits_pertes_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Exporter vers Excel
        excel_filename, _ = QFileDialog.getSaveFileName(
            parent,
            "Exporter vers Excel",
            default_name + ".xlsx",
            "Fichiers Excel (*.xlsx)"
        )

        if excel_filename:
            export_to_excel(data, excel_filename, parent)

        # Exporter vers PDF
        pdf_filename, _ = QFileDialog.getSaveFileName(
            parent,
            "Exporter vers PDF",
            default_name + ".pdf",
            "Fichiers PDF (*.pdf)"
        )

        if pdf_filename:
            export_to_pdf([figure], pdf_filename, parent)

    return True

def export_cash_flow_report(report_data, figure, parent=None):
    """
    Exporte un rapport de flux de trésorerie

    Args:
        report_data: Données du rapport
        figure: Figure Matplotlib
        parent: Widget parent pour les boîtes de dialogue
    """
    # Créer un DataFrame pour le résumé
    summary_data = {
        "Métrique": [
            "Entrées de trésorerie",
            "Sorties de trésorerie",
            "Flux de trésorerie net",
            "Nombre de paiements clients",
            "Nombre de paiements fournisseurs"
        ],
        "Valeur": [
            f"{report_data['cash_inflows']:.2f} DA",
            f"{report_data['cash_outflows']:.2f} DA",
            f"{report_data['net_cash_flow']:.2f} DA",
            str(report_data['customer_payments_count']),
            str(report_data['supplier_payments_count'])
        ]
    }
    summary_df = pd.DataFrame(summary_data)

    # Exporter vers Excel
    data = {
        "Résumé": summary_df
    }

    # Demander le format d'exportation
    export_format = QMessageBox.question(
        parent,
        "Format d'exportation",
        "Choisissez le format d'exportation:",
        QMessageBox.StandardButton.Save | QMessageBox.StandardButton.Cancel,
        QMessageBox.StandardButton.Save
    )

    if export_format == QMessageBox.StandardButton.Save:
        # Demander le nom du fichier
        default_name = f"rapport_flux_tresorerie_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Exporter vers Excel
        excel_filename, _ = QFileDialog.getSaveFileName(
            parent,
            "Exporter vers Excel",
            default_name + ".xlsx",
            "Fichiers Excel (*.xlsx)"
        )

        if excel_filename:
            export_to_excel(data, excel_filename, parent)

        # Exporter vers PDF
        pdf_filename, _ = QFileDialog.getSaveFileName(
            parent,
            "Exporter vers PDF",
            default_name + ".pdf",
            "Fichiers PDF (*.pdf)"
        )

        if pdf_filename:
            export_to_pdf([figure], pdf_filename, parent)

    return True

def export_margin_analysis_report(report_data, figure, top_margin_table, low_margin_table, parent=None):
    """
    Exporte un rapport d'analyse des marges

    Args:
        report_data: Données du rapport
        figure: Figure Matplotlib
        top_margin_table: QTableWidget des produits avec les meilleures marges
        low_margin_table: QTableWidget des produits avec des marges faibles
        parent: Widget parent pour les boîtes de dialogue
    """
    # Créer un DataFrame pour le résumé
    summary_data = {
        "Métrique": [
            "Marge brute moyenne",
            "Marge nette moyenne",
            "Nombre de produits à faible marge"
        ],
        "Valeur": [
            f"{report_data['overall_margins']['gross_margin_percent']:.2f}%",
            f"{report_data['overall_margins']['net_margin_percent']:.2f}%",
            str(len(report_data['low_margin_products']))
        ]
    }
    summary_df = pd.DataFrame(summary_data)

    # Convertir les tableaux en DataFrames
    top_margin_df = table_to_dataframe(top_margin_table)
    low_margin_df = table_to_dataframe(low_margin_table)

    # Exporter vers Excel
    data = {
        "Résumé": summary_df,
        "Meilleures marges": top_margin_df,
        "Marges faibles": low_margin_df
    }

    # Demander le format d'exportation
    export_format = QMessageBox.question(
        parent,
        "Format d'exportation",
        "Choisissez le format d'exportation:",
        QMessageBox.StandardButton.Save | QMessageBox.StandardButton.Cancel,
        QMessageBox.StandardButton.Save
    )

    if export_format == QMessageBox.StandardButton.Save:
        # Demander le nom du fichier
        default_name = f"rapport_analyse_marges_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Exporter vers Excel
        excel_filename, _ = QFileDialog.getSaveFileName(
            parent,
            "Exporter vers Excel",
            default_name + ".xlsx",
            "Fichiers Excel (*.xlsx)"
        )

        if excel_filename:
            export_to_excel(data, excel_filename, parent)

        # Exporter vers PDF
        pdf_filename, _ = QFileDialog.getSaveFileName(
            parent,
            "Exporter vers PDF",
            default_name + ".pdf",
            "Fichiers PDF (*.pdf)"
        )

        if pdf_filename:
            export_to_pdf([figure], pdf_filename, parent)

    return True