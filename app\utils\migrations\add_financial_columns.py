import logging
from sqlalchemy import text
from sqlalchemy.engine import Engine
from app.core.models.config import BaseDBModel

logger = logging.getLogger(__name__)

def migrate(engine: Engine):
    try:
        # Add financial columns one by one to handle existing tables
        columns = [
            ('tax_amount', 'REAL DEFAULT 0.0'),
            ('discount_amount', 'REAL DEFAULT 0.0'),
            ('final_amount', 'REAL DEFAULT 0.0'),
            ('payment_status', 'TEXT'),
            ('payment_method', 'TEXT'),
            ('payment_date', 'TIMESTAMP'),
            ('invoice_number', 'TEXT'),
            ('invoice_date', 'TIMESTAMP'),
            ('credit_terms', 'TEXT'),
            ('due_date', 'TIMESTAMP'),
            ('total_paid', 'REAL DEFAULT 0.0')
        ]
        
        with engine.connect() as connection:
            for column_name, column_type in columns:
                try:
                    sql = text(f'ALTER TABLE repair_orders ADD COLUMN {column_name} {column_type}')
                    connection.execute(sql)
                    logger.info(f"Colonne {column_name} ajoutée avec succès")
                except Exception as e:
                    if "duplicate column name" in str(e).lower():
                        logger.info(f"La colonne {column_name} existe déjà")
                    else:
                        logger.error(f"Erreur lors de l'ajout de la colonne {column_name}: {e}")
                        raise
            connection.commit()

        logger.info("Migration des colonnes financières terminée avec succès")
    except Exception as e:
        logger.error(f"Erreur lors de la migration: {e}")
        raise