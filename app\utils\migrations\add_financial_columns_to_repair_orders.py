import logging
import sqlite3
from typing import Dict, Any

logger = logging.getLogger(__name__)

def migrate(db_path: str, config: Dict[str, Any]) -> bool:
    """
    Ajoute les colonnes financières à la table repair_orders
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier si les colonnes existent déjà
        cursor.execute("PRAGMA table_info(repair_orders)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Liste des colonnes financières à ajouter
        financial_columns = {
            "tax_amount": "DECIMAL(10, 2) DEFAULT 0",
            "discount_amount": "DECIMAL(10, 2) DEFAULT 0",
            "final_amount": "DECIMAL(10, 2) DEFAULT 0",
            "payment_status": "TEXT DEFAULT 'pending'",
            "payment_method": "TEXT",
            "payment_date": "TIMESTAMP",
            "invoice_number": "TEXT",
            "invoice_date": "TIMESTAMP",
            "credit_terms": "INTEGER",
            "due_date": "TIMESTAMP",
            "total_paid": "DECIMAL(10, 2) DEFAULT 0"
        }
        
        # Ajouter les colonnes manquantes
        for column_name, column_type in financial_columns.items():
            if column_name not in columns:
                logger.info(f"Ajout de la colonne {column_name} à la table repair_orders")
                cursor.execute(f"ALTER TABLE repair_orders ADD COLUMN {column_name} {column_type}")
                conn.commit()
            else:
                logger.info(f"La colonne {column_name} existe déjà dans la table repair_orders")
        
        # Mettre à jour les valeurs par défaut
        cursor.execute("""
            UPDATE repair_orders 
            SET tax_amount = total_cost * 0.19,
                final_amount = total_cost + (total_cost * 0.19),
                payment_status = 'pending'
            WHERE tax_amount IS NULL OR final_amount IS NULL
        """)
        conn.commit()
        
        # Créer la table repair_payments si elle n'existe pas
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS repair_payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                repair_order_id INTEGER NOT NULL,
                amount DECIMAL(10, 2) NOT NULL,
                payment_method TEXT NOT NULL,
                payment_date TIMESTAMP NOT NULL,
                reference_number TEXT,
                notes TEXT,
                processed_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (repair_order_id) REFERENCES repair_orders (id) ON DELETE CASCADE,
                FOREIGN KEY (processed_by) REFERENCES users (id) ON DELETE SET NULL
            )
        """)
        conn.commit()
        
        logger.info("Migration des colonnes financières terminée avec succès")
        return True
        
    except Exception as e:
        logger.error(f"Erreur lors de la migration des colonnes financières: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()