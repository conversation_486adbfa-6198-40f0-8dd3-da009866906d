"""
Migration pour ajouter la table d'historique des commandes
"""
import logging
from sqlalchemy import text
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

def run_migration(db: Session):
    """
    Ajoute la table d'historique des commandes
    
    Args:
        db: Session de base de données
    """
    # Vérifier si la table existe déjà
    result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='order_history'")).fetchone()
    if result:
        logger.info("La table order_history existe déjà")
        return
        
    # Créer la table
    db.execute(text("""
        CREATE TABLE order_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            user_id INTEGER,
            action VARCHAR NOT NULL,
            old_status VARCHAR,
            new_status VARCHAR,
            details TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES purchase_orders (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    """))
    
    # Créer un index sur order_id pour améliorer les performances
    db.execute(text("CREATE INDEX idx_order_history_order_id ON order_history (order_id)"))
    
    db.commit()
    logger.info("Table order_history créée avec succès")
    
    logger.info("Migration terminée avec succès")
