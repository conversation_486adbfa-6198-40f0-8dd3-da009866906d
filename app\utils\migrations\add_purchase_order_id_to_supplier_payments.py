"""
Migration pour ajouter la colonne purchase_order_id à la table supplier_payments.
"""
import logging
import sqlite3

logger = logging.getLogger(__name__)

def run_migration(db_path):
    """
    Ajoute la colonne purchase_order_id à la table supplier_payments.
    
    Args:
        db_path: Chemin vers la base de données SQLite
    """
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Vérifier si la colonne existe déjà
        cursor.execute("PRAGMA table_info(supplier_payments)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if "purchase_order_id" not in column_names:
            logger.info("Ajout de la colonne purchase_order_id à la table supplier_payments")
            cursor.execute("ALTER TABLE supplier_payments ADD COLUMN purchase_order_id INTEGER REFERENCES purchase_orders(id)")
            conn.commit()
            logger.info("Colonne purchase_order_id ajoutée avec succès")
        else:
            logger.info("La colonne purchase_order_id existe déjà dans la table supplier_payments")
            
        logger.info("Migration terminée avec succès")
        
    except Exception as e:
        conn.rollback()
        logger.error(f"Erreur lors de la migration: {str(e)}")
        raise
    finally:
        conn.close()
