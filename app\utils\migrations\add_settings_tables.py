"""
Migration pour ajouter les tables de paramètres.
"""
import logging
from sqlalchemy.sql import text
from datetime import datetime

from app.utils.database import SessionLocal
from app.core.models.settings import SettingCategory

logger = logging.getLogger(__name__)

def run_migration():
    """Exécute la migration pour ajouter les tables de paramètres"""
    db = SessionLocal()
    
    try:
        # Vérifier si la table app_settings existe déjà
        result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='app_settings'"))
        if result.fetchone():
            logger.info("La table app_settings existe déjà")
        else:
            # Créer la table app_settings
            db.execute(text("""
                CREATE TABLE app_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key VARCHAR NOT NULL UNIQUE,
                    value TEXT,
                    category VARCHAR NOT NULL,
                    description TEXT,
                    is_system BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            logger.info("Table app_settings créée avec succès")
        
        # Vérifier si la table backup_info existe déjà
        result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='backup_info'"))
        if result.fetchone():
            logger.info("La table backup_info existe déjà")
        else:
            # Créer la table backup_info
            db.execute(text("""
                CREATE TABLE backup_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    filename VARCHAR NOT NULL,
                    path VARCHAR NOT NULL,
                    size INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    description TEXT,
                    version VARCHAR,
                    is_auto BOOLEAN DEFAULT 0,
                    metadata JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            logger.info("Table backup_info créée avec succès")
        
        # Insérer les paramètres par défaut
        default_settings = [
            # Paramètres généraux
            ("general.company_name", "", SettingCategory.GENERAL, "Nom de l'entreprise"),
            ("general.company_address", "", SettingCategory.GENERAL, "Adresse de l'entreprise"),
            ("general.company_phone", "", SettingCategory.GENERAL, "Téléphone de l'entreprise"),
            ("general.company_email", "", SettingCategory.GENERAL, "Email de l'entreprise"),
            ("general.company_website", "", SettingCategory.GENERAL, "Site web de l'entreprise"),
            ("general.company_logo", "", SettingCategory.GENERAL, "Logo de l'entreprise"),
            ("general.default_currency", "DA", SettingCategory.GENERAL, "Devise par défaut"),
            ("general.tax_rate", "19.0", SettingCategory.GENERAL, "Taux de TVA (%)"),
            ("general.fiscal_year_start", "01-01", SettingCategory.GENERAL, "Début de l'année fiscale (MM-DD)"),
            
            # Paramètres d'apparence
            ("appearance.theme", "light", SettingCategory.APPEARANCE, "Thème de l'application"),
            ("appearance.font_size", "12", SettingCategory.APPEARANCE, "Taille de police"),
            ("appearance.language", "fr", SettingCategory.APPEARANCE, "Langue de l'application"),
            ("appearance.show_welcome_screen", "true", SettingCategory.APPEARANCE, "Afficher l'écran de bienvenue"),
            ("appearance.compact_mode", "false", SettingCategory.APPEARANCE, "Mode compact"),
            ("appearance.enable_animations", "true", SettingCategory.APPEARANCE, "Activer les animations"),
            
            # Paramètres de base de données
            ("database.auto_optimize", "true", SettingCategory.DATABASE, "Optimisation automatique de la base de données"),
            ("database.optimize_frequency", "weekly", SettingCategory.DATABASE, "Fréquence d'optimisation"),
            ("database.connection_timeout", "30", SettingCategory.DATABASE, "Délai d'expiration de connexion (secondes)"),
            ("database.max_connections", "10", SettingCategory.DATABASE, "Nombre maximum de connexions"),
            
            # Paramètres de sauvegarde
            ("backup.auto_backup", "false", SettingCategory.BACKUP, "Sauvegarde automatique"),
            ("backup.backup_frequency", "weekly", SettingCategory.BACKUP, "Fréquence de sauvegarde"),
            ("backup.backup_path", "./backups", SettingCategory.BACKUP, "Chemin de sauvegarde"),
            ("backup.max_backups", "10", SettingCategory.BACKUP, "Nombre maximum de sauvegardes"),
            ("backup.include_attachments", "true", SettingCategory.BACKUP, "Inclure les pièces jointes"),
            ("backup.compress_backup", "true", SettingCategory.BACKUP, "Compresser la sauvegarde"),
            ("backup.encrypt_backup", "false", SettingCategory.BACKUP, "Chiffrer la sauvegarde"),
            ("backup.encryption_key", "", SettingCategory.BACKUP, "Clé de chiffrement"),
            
            # Paramètres de sécurité
            ("security.session_timeout", "30", SettingCategory.SECURITY, "Délai d'expiration de session (minutes)"),
            ("security.password_expiry", "90", SettingCategory.SECURITY, "Expiration du mot de passe (jours)"),
            ("security.min_password_length", "8", SettingCategory.SECURITY, "Longueur minimale du mot de passe"),
            ("security.require_special_chars", "true", SettingCategory.SECURITY, "Exiger des caractères spéciaux"),
            ("security.require_numbers", "true", SettingCategory.SECURITY, "Exiger des chiffres"),
            ("security.require_uppercase", "true", SettingCategory.SECURITY, "Exiger des majuscules"),
            ("security.max_login_attempts", "5", SettingCategory.SECURITY, "Nombre maximum de tentatives de connexion"),
            ("security.enable_two_factor", "false", SettingCategory.SECURITY, "Activer l'authentification à deux facteurs"),
            ("security.audit_log_retention", "90", SettingCategory.SECURITY, "Conservation des journaux d'audit (jours)"),
            
            # Paramètres de notifications
            ("notifications.enable_notifications", "true", SettingCategory.NOTIFICATIONS, "Activer les notifications"),
            ("notifications.enable_email_notifications", "false", SettingCategory.NOTIFICATIONS, "Activer les notifications par email"),
            ("notifications.enable_desktop_notifications", "true", SettingCategory.NOTIFICATIONS, "Activer les notifications de bureau"),
            ("notifications.notification_sound", "true", SettingCategory.NOTIFICATIONS, "Son de notification"),
            ("notifications.notification_retention", "30", SettingCategory.NOTIFICATIONS, "Conservation des notifications (jours)"),
            
            # Paramètres d'impression
            ("printing.default_printer", "", SettingCategory.PRINTING, "Imprimante par défaut"),
            ("printing.paper_size", "A4", SettingCategory.PRINTING, "Format de papier"),
            ("printing.orientation", "portrait", SettingCategory.PRINTING, "Orientation"),
            ("printing.margin_top", "20", SettingCategory.PRINTING, "Marge supérieure (mm)"),
            ("printing.margin_bottom", "20", SettingCategory.PRINTING, "Marge inférieure (mm)"),
            ("printing.margin_left", "20", SettingCategory.PRINTING, "Marge gauche (mm)"),
            ("printing.margin_right", "20", SettingCategory.PRINTING, "Marge droite (mm)"),
            ("printing.header_text", "", SettingCategory.PRINTING, "Texte d'en-tête"),
            ("printing.footer_text", "", SettingCategory.PRINTING, "Texte de pied de page"),
            ("printing.show_logo", "true", SettingCategory.PRINTING, "Afficher le logo"),
            
            # Paramètres avancés
            ("advanced.debug_mode", "false", SettingCategory.ADVANCED, "Mode débogage"),
            ("advanced.log_level", "INFO", SettingCategory.ADVANCED, "Niveau de journalisation"),
            ("advanced.enable_api", "false", SettingCategory.ADVANCED, "Activer l'API"),
            ("advanced.api_port", "8000", SettingCategory.ADVANCED, "Port de l'API"),
            ("advanced.enable_remote_access", "false", SettingCategory.ADVANCED, "Activer l'accès à distance"),
            ("advanced.remote_access_port", "8080", SettingCategory.ADVANCED, "Port d'accès à distance"),
            ("advanced.enable_auto_update", "true", SettingCategory.ADVANCED, "Activer la mise à jour automatique"),
        ]
        
        # Insérer les paramètres par défaut s'ils n'existent pas déjà
        for key, value, category, description in default_settings:
            result = db.execute(text("SELECT id FROM app_settings WHERE key = :key"), {"key": key})
            if not result.fetchone():
                db.execute(
                    text("""
                        INSERT INTO app_settings (key, value, category, description, is_system, created_at, updated_at)
                        VALUES (:key, :value, :category, :description, 0, :created_at, :updated_at)
                    """),
                    {
                        "key": key,
                        "value": value,
                        "category": category,
                        "description": description,
                        "created_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow()
                    }
                )
        
        db.commit()
        logger.info("Paramètres par défaut insérés avec succès")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Erreur lors de la migration des tables de paramètres: {str(e)}")
        raise
    finally:
        db.close()
