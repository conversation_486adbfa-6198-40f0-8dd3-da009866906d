import logging
from sqlalchemy import Column, Float
from app.utils.database import engine
from sqlalchemy.sql import text

logger = logging.getLogger(__name__)

def run_migration():
    """
    Ajoute la colonne total_paid à la table repair_orders
    """
    try:
        # Vérifier si la colonne existe déjà
        with engine.connect() as connection:
            result = connection.execute(text("PRAGMA table_info(repair_orders)"))
            columns = [row[1] for row in result]
            
            if "total_paid" not in columns:
                # Ajouter la colonne total_paid
                connection.execute(text("ALTER TABLE repair_orders ADD COLUMN total_paid FLOAT DEFAULT 0.0"))
                logger.info("Colonne total_paid ajoutée à la table repair_orders")
            else:
                logger.info("La colonne total_paid existe déjà dans la table repair_orders")
                
        return True
    except Exception as e:
        logger.error(f"Erreur lors de l'ajout de la colonne total_paid: {e}")
        return False
