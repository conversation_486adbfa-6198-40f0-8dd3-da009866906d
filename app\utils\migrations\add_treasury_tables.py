"""
Migration pour ajouter les tables de trésorerie.
"""
import logging
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Boolean, Text, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import text

from app.utils.database import engine, SessionLocal
from app.core.models.treasury import CashRegisterType, TransactionCategory, PaymentMethod

logger = logging.getLogger(__name__)

def run_migration():
    """Exécute la migration pour ajouter les tables de trésorerie"""
    db = SessionLocal()
    
    try:
        # Vérifier si la table cash_registers existe déjà
        result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='cash_registers'"))
        if result.fetchone():
            logger.info("La table cash_registers existe déjà")
        else:
            # Créer la table cash_registers
            db.execute(text("""
                CREATE TABLE cash_registers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR NOT NULL UNIQUE,
                    type VARCHAR NOT NULL,
                    initial_balance FLOAT DEFAULT 0.0,
                    current_balance FLOAT DEFAULT 0.0,
                    last_reconciliation TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            logger.info("Table cash_registers créée avec succès")
        
        # Vérifier si la table cash_transactions existe déjà
        result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='cash_transactions'"))
        if result.fetchone():
            logger.info("La table cash_transactions existe déjà")
        else:
            # Créer la table cash_transactions
            db.execute(text("""
                CREATE TABLE cash_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cash_register_id INTEGER NOT NULL,
                    amount FLOAT NOT NULL,
                    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    category VARCHAR NOT NULL,
                    payment_method VARCHAR NOT NULL,
                    reference_number VARCHAR,
                    description TEXT,
                    sale_id INTEGER,
                    repair_id INTEGER,
                    purchase_id INTEGER,
                    supplier_payment_id INTEGER,
                    customer_transaction_id INTEGER,
                    expense_id INTEGER,
                    user_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (cash_register_id) REFERENCES cash_registers(id),
                    FOREIGN KEY (sale_id) REFERENCES sales(id),
                    FOREIGN KEY (repair_id) REFERENCES repair_orders(id),
                    FOREIGN KEY (purchase_id) REFERENCES purchase_orders(id),
                    FOREIGN KEY (supplier_payment_id) REFERENCES supplier_payments(id),
                    FOREIGN KEY (customer_transaction_id) REFERENCES customer_transactions(id),
                    FOREIGN KEY (expense_id) REFERENCES expenses(id),
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """))
            logger.info("Table cash_transactions créée avec succès")
        
        # Vérifier si la table expenses existe déjà
        result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='expenses'"))
        if result.fetchone():
            logger.info("La table expenses existe déjà")
        else:
            # Créer la table expenses
            db.execute(text("""
                CREATE TABLE expenses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    amount FLOAT NOT NULL,
                    expense_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    category VARCHAR NOT NULL,
                    payment_method VARCHAR NOT NULL,
                    reference_number VARCHAR,
                    description TEXT,
                    receipt_image VARCHAR,
                    cash_register_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (cash_register_id) REFERENCES cash_registers(id),
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """))
            logger.info("Table expenses créée avec succès")
        
        # Créer les caisses par défaut si elles n'existent pas
        result = db.execute(text("SELECT COUNT(*) FROM cash_registers"))
        if result.fetchone()[0] == 0:
            # Créer la caisse principale
            db.execute(text("""
                INSERT INTO cash_registers (name, type, initial_balance, current_balance, is_active)
                VALUES ('Caisse Principale', 'main', 0.0, 0.0, 1)
            """))
            
            # Créer la caisse des réparations
            db.execute(text("""
                INSERT INTO cash_registers (name, type, initial_balance, current_balance, is_active)
                VALUES ('Caisse Réparations', 'repair', 0.0, 0.0, 1)
            """))
            
            # Créer la caisse des ventes
            db.execute(text("""
                INSERT INTO cash_registers (name, type, initial_balance, current_balance, is_active)
                VALUES ('Caisse Ventes', 'sales', 0.0, 0.0, 1)
            """))
            
            # Créer la caisse des achats
            db.execute(text("""
                INSERT INTO cash_registers (name, type, initial_balance, current_balance, is_active)
                VALUES ('Caisse Achats', 'purchase', 0.0, 0.0, 1)
            """))
            
            # Créer la caisse des dépenses
            db.execute(text("""
                INSERT INTO cash_registers (name, type, initial_balance, current_balance, is_active)
                VALUES ('Caisse Dépenses', 'expense', 0.0, 0.0, 1)
            """))
            
            logger.info("Caisses par défaut créées avec succès")
        
        logger.info("Migration des tables de trésorerie terminée avec succès")
        
    except Exception as e:
        logger.error(f"Erreur lors de la migration des tables de trésorerie: {str(e)}")
        raise
    finally:
        db.close()
