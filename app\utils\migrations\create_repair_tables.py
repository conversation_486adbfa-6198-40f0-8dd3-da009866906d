import logging
import sqlite3

logger = logging.getLogger(__name__)

def migrate(db_path: str) -> bool:
    """
    Crée les tables nécessaires pour le module de réparation
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier si la table repair_orders existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='repair_orders'")
        if not cursor.fetchone():
            logger.info("Création de la table repair_orders")
            cursor.execute("""
                CREATE TABLE repair_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_number TEXT UNIQUE,
                    customer_id INTEGER,
                    equipment_id INTEGER,
                    technician_id INTEGER,
                    status TEXT NOT NULL,
                    priority TEXT NOT NULL,
                    issue_description TEXT NOT NULL,
                    diagnosis TEXT,
                    solution TEXT,
                    start_date TIMESTAMP,
                    completion_date TIMESTAMP,
                    estimated_completion_date TIMESTAMP,
                    labor_hours REAL DEFAULT 0,
                    labor_cost REAL DEFAULT 0,
                    parts_cost REAL DEFAULT 0,
                    total_cost REAL DEFAULT 0,
                    tax_amount REAL DEFAULT 0,
                    discount_amount REAL DEFAULT 0,
                    final_amount REAL DEFAULT 0,
                    payment_status TEXT DEFAULT 'pending',
                    payment_method TEXT,
                    payment_date TIMESTAMP,
                    invoice_number TEXT,
                    invoice_date TIMESTAMP,
                    credit_terms INTEGER,
                    due_date TIMESTAMP,
                    total_paid REAL DEFAULT 0,
                    notes TEXT,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                    FOREIGN KEY (technician_id) REFERENCES users (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            """)
            logger.info("Table repair_orders créée avec succès")
        else:
            logger.info("La table repair_orders existe déjà")
            
        # Vérifier si la table repair_used_parts existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='repair_used_parts'")
        if not cursor.fetchone():
            logger.info("Création de la table repair_used_parts")
            cursor.execute("""
                CREATE TABLE repair_used_parts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    repair_order_id INTEGER NOT NULL,
                    inventory_item_id INTEGER NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (repair_order_id) REFERENCES repair_orders (id) ON DELETE CASCADE,
                    FOREIGN KEY (inventory_item_id) REFERENCES inventory_items (id)
                )
            """)
            logger.info("Table repair_used_parts créée avec succès")
        else:
            logger.info("La table repair_used_parts existe déjà")
            
        # Vérifier si la table repair_payments existe déjà
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='repair_payments'")
        if not cursor.fetchone():
            logger.info("Création de la table repair_payments")
            cursor.execute("""
                CREATE TABLE repair_payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    repair_order_id INTEGER NOT NULL,
                    amount REAL NOT NULL,
                    payment_method TEXT NOT NULL,
                    payment_date TIMESTAMP NOT NULL,
                    reference_number TEXT,
                    notes TEXT,
                    processed_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (repair_order_id) REFERENCES repair_orders (id) ON DELETE CASCADE,
                    FOREIGN KEY (processed_by) REFERENCES users (id) ON DELETE SET NULL
                )
            """)
            logger.info("Table repair_payments créée avec succès")
        else:
            logger.info("La table repair_payments existe déjà")
            
        conn.commit()
        logger.info("Migration des tables de réparation terminée avec succès")
        return True
        
    except Exception as e:
        logger.error(f"Erreur lors de la migration des tables de réparation: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()