"""
Script pour recréer les tables purchase_orders et purchase_order_items
"""

import os
import sqlite3
from pathlib import Path

def get_database_path():
    """Retourne le chemin de la base de données"""
    # Get the base directory of the project
    base_dir = Path(__file__).resolve().parent.parent.parent.parent

    # Default database path
    default_db_path = os.path.join(base_dir, "data", "app.db")

    return default_db_path

def run_migration():
    """Recréer les tables purchase_orders et purchase_order_items"""
    db_path = get_database_path()

    # Vérifier que la base de données existe
    if not os.path.exists(db_path):
        print(f"Erreur: Base de données non trouvée à {db_path}")
        return False

    # Connexion à la base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Sauvegarder les données existantes
        print("Sauvegarde des données existantes...")

        # Récupérer les données des commandes
        cursor.execute("SELECT * FROM purchase_orders")
        purchase_orders_data = cursor.fetchall()

        # Récupérer les colonnes des commandes
        cursor.execute("PRAGMA table_info(purchase_orders)")
        purchase_orders_columns = [column[1] for column in cursor.fetchall()]

        # Récupérer les données des articles de commande
        cursor.execute("SELECT * FROM purchase_order_items")
        purchase_order_items_data = cursor.fetchall()

        # Récupérer les colonnes des articles de commande
        cursor.execute("PRAGMA table_info(purchase_order_items)")
        purchase_order_items_columns = [column[1] for column in cursor.fetchall()]

        # Supprimer les tables existantes
        print("Suppression des tables existantes...")
        cursor.execute("DROP TABLE IF EXISTS purchase_order_items")
        cursor.execute("DROP TABLE IF EXISTS purchase_orders")

        # Créer la table purchase_orders avec toutes les colonnes
        print("Création de la table purchase_orders...")
        cursor.execute("""
        CREATE TABLE purchase_orders (
            id INTEGER PRIMARY KEY,
            po_number TEXT UNIQUE,
            supplier_id INTEGER,
            status TEXT,
            order_date TIMESTAMP,
            expected_delivery TIMESTAMP,
            delivery_date TIMESTAMP,

            subtotal_amount FLOAT DEFAULT 0,
            discount_percent FLOAT DEFAULT 0,
            discount_amount FLOAT DEFAULT 0,
            tax_percent FLOAT DEFAULT 19,
            tax_amount FLOAT DEFAULT 0,
            shipping_amount FLOAT DEFAULT 0,
            total_amount FLOAT DEFAULT 0,

            currency TEXT DEFAULT 'DA',
            payment_terms TEXT,
            shipping_terms TEXT,
            notes TEXT,
            delivery_note TEXT,

            payment_status TEXT DEFAULT 'unpaid',
            payment_due_date TIMESTAMP,
            advance_payment_amount FLOAT DEFAULT 0,

            created_by INTEGER,
            approved_by INTEGER,
            submitted_by INTEGER,
            submitted_at TIMESTAMP,
            approved_at TIMESTAMP,
            ordered_at TIMESTAMP,
            received_by INTEGER,
            received_at TIMESTAMP,
            created_at TIMESTAMP,
            updated_at TIMESTAMP,

            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (created_by) REFERENCES users (id),
            FOREIGN KEY (approved_by) REFERENCES users (id),
            FOREIGN KEY (submitted_by) REFERENCES users (id),
            FOREIGN KEY (received_by) REFERENCES users (id)
        )
        """)

        # Créer la table purchase_order_items avec toutes les colonnes
        print("Création de la table purchase_order_items...")
        cursor.execute("""
        CREATE TABLE purchase_order_items (
            id INTEGER PRIMARY KEY,
            po_id INTEGER,
            product_id INTEGER,
            quantity FLOAT,
            purchase_unit_price FLOAT,
            received_quantity FLOAT DEFAULT 0,
            specifications JSON,
            delivery_date TIMESTAMP,
            received_at TIMESTAMP,
            total_price FLOAT DEFAULT 0,
            discount_percent FLOAT DEFAULT 0,
            discount_amount FLOAT DEFAULT 0,
            tax_percent FLOAT DEFAULT 19,
            tax_amount FLOAT DEFAULT 0,
            subtotal FLOAT DEFAULT 0,
            remaining_quantity FLOAT DEFAULT 0,
            return_quantity FLOAT DEFAULT 0,
            unit_of_measure TEXT DEFAULT 'pcs',
            FOREIGN KEY (po_id) REFERENCES purchase_orders (id),
            FOREIGN KEY (product_id) REFERENCES inventory_items (id)
        )
        """)

        # Réinsérer les données des commandes
        print("Réinsertion des données des commandes...")
        for order in purchase_orders_data:
            # Créer la requête d'insertion dynamiquement en fonction des colonnes existantes
            columns = []
            placeholders = []
            values = []

            for i, column in enumerate(purchase_orders_columns):
                columns.append(column)
                placeholders.append("?")
                values.append(order[i])

            # Ajouter les colonnes manquantes avec NULL
            new_columns = [
                "delivery_note", "submitted_by", "submitted_at", "received_by", "received_at",
                "subtotal_amount", "discount_percent", "discount_amount", "tax_percent", "tax_amount",
                "shipping_amount", "payment_status", "payment_due_date", "advance_payment_amount",
                "approved_at", "ordered_at"
            ]
            for column in new_columns:
                if column not in columns:
                    columns.append(column)
                    placeholders.append("NULL")

            query = f"INSERT INTO purchase_orders ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
            cursor.execute(query, values)

        # Réinsérer les données des articles de commande
        print("Réinsertion des données des articles de commande...")
        for item in purchase_order_items_data:
            # Créer la requête d'insertion dynamiquement en fonction des colonnes existantes
            columns = []
            placeholders = []
            values = []

            for i, column in enumerate(purchase_order_items_columns):
                columns.append(column)
                placeholders.append("?")
                values.append(item[i])

            # Ajouter les colonnes manquantes avec NULL
            new_columns = [
                "received_at", "total_price", "discount_percent", "discount_amount",
                "tax_percent", "tax_amount", "subtotal", "remaining_quantity",
                "return_quantity", "unit_of_measure"
            ]
            for column in new_columns:
                if column not in columns:
                    columns.append(column)
                    placeholders.append("NULL")

            query = f"INSERT INTO purchase_order_items ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
            cursor.execute(query, values)

        # Valider les modifications
        conn.commit()
        print("Migration terminée avec succès")
        return True

    except Exception as e:
        # Annuler les modifications en cas d'erreur
        conn.rollback()
        print(f"Erreur lors de la migration: {str(e)}")
        return False

    finally:
        # Fermer la connexion
        conn.close()

if __name__ == "__main__":
    run_migration()
