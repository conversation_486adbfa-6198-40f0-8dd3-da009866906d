"""
Script de migration pour mettre à jour la structure de la base de données
pour les améliorations du système d'achat et des finances fournisseurs.
"""

import sqlite3
import os
from pathlib import Path
from datetime import datetime

def get_database_path():
    """Retourne le chemin de la base de données"""
    # Get the base directory of the project
    base_dir = Path(__file__).resolve().parent.parent.parent.parent
    
    # Default database path
    default_db_path = os.path.join(base_dir, "data", "app.db")
    
    return default_db_path

def run_migration():
    """Exécute la migration pour mettre à jour la structure de la base de données"""
    db_path = get_database_path()
    
    # Vérifier que la base de données existe
    if not os.path.exists(db_path):
        print(f"Erreur: Base de données non trouvée à {db_path}")
        return False
    
    # Connexion à la base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. Mise à jour de la table inventory_items
        print("Mise à jour de la table inventory_items...")
        
        # Vérifier si les colonnes existent déjà
        cursor.execute("PRAGMA table_info(inventory_items)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Ajouter les nouvelles colonnes
        if "purchase_price" not in columns:
            cursor.execute("ALTER TABLE inventory_items ADD COLUMN purchase_price FLOAT DEFAULT 0")
            print("Colonne purchase_price ajoutée")
            
            # Initialiser purchase_price avec unit_price pour les produits existants
            cursor.execute("UPDATE inventory_items SET purchase_price = unit_price * 0.7")
            print("Prix d'achat initialisés (70% du prix de vente)")
        
        if "margin_percent" not in columns:
            cursor.execute("ALTER TABLE inventory_items ADD COLUMN margin_percent FLOAT DEFAULT 30")
            print("Colonne margin_percent ajoutée")
        
        if "last_purchase_date" not in columns:
            cursor.execute("ALTER TABLE inventory_items ADD COLUMN last_purchase_date TIMESTAMP")
            print("Colonne last_purchase_date ajoutée")
        
        # 2. Création de la table supplier_invoices
        print("Création de la table supplier_invoices...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS supplier_invoices (
            id INTEGER PRIMARY KEY,
            invoice_number TEXT UNIQUE,
            supplier_id INTEGER,
            purchase_order_id INTEGER,
            invoice_date TIMESTAMP,
            due_date TIMESTAMP,
            total_amount FLOAT,
            tax_amount FLOAT DEFAULT 0,
            currency TEXT DEFAULT 'DA',
            status TEXT,
            notes TEXT,
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP,
            updated_at TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders (id)
        )
        """)
        
        # 3. Création de la table supplier_payments
        print("Création de la table supplier_payments...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS supplier_payments (
            id INTEGER PRIMARY KEY,
            supplier_id INTEGER,
            invoice_id INTEGER,
            payment_date TIMESTAMP,
            amount FLOAT,
            payment_method TEXT,
            reference TEXT,
            notes TEXT,
            processed_by INTEGER,
            created_at TIMESTAMP,
            updated_at TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (invoice_id) REFERENCES supplier_invoices (id),
            FOREIGN KEY (processed_by) REFERENCES users (id)
        )
        """)
        
        # 4. Création de la table price_history
        print("Création de la table price_history...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS price_history (
            id INTEGER PRIMARY KEY,
            item_id INTEGER,
            old_purchase_price FLOAT,
            new_purchase_price FLOAT,
            old_unit_price FLOAT,
            new_unit_price FLOAT,
            change_date TIMESTAMP,
            change_reason TEXT,
            purchase_order_id INTEGER,
            changed_by INTEGER,
            created_at TIMESTAMP,
            updated_at TIMESTAMP,
            FOREIGN KEY (item_id) REFERENCES inventory_items (id),
            FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders (id),
            FOREIGN KEY (changed_by) REFERENCES users (id)
        )
        """)
        
        # 5. Mise à jour de la table purchase_order_items
        print("Mise à jour de la table purchase_order_items...")
        
        # Vérifier si la colonne total_price existe déjà
        cursor.execute("PRAGMA table_info(purchase_order_items)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if "total_price" not in columns:
            cursor.execute("ALTER TABLE purchase_order_items ADD COLUMN total_price FLOAT")
            print("Colonne total_price ajoutée")
            
            # Initialiser total_price pour les articles existants
            cursor.execute("UPDATE purchase_order_items SET total_price = quantity * purchase_unit_price")
            print("Prix totaux initialisés")
        
        # Valider les modifications
        conn.commit()
        print("Migration terminée avec succès")
        return True
        
    except Exception as e:
        # Annuler les modifications en cas d'erreur
        conn.rollback()
        print(f"Erreur lors de la migration: {str(e)}")
        return False
        
    finally:
        # Fermer la connexion
        conn.close()

if __name__ == "__main__":
    run_migration()
