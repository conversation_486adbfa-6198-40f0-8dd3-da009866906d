"""
Script de migration pour mettre à jour les tables purchase_orders et purchase_order_items
avec les nouveaux champs financiers et de workflow.
"""

import sqlite3
import os
from pathlib import Path

def get_database_path():
    """Retourne le chemin de la base de données"""
    # Get the base directory of the project
    base_dir = Path(__file__).resolve().parent.parent.parent.parent
    
    # Default database path
    default_db_path = os.path.join(base_dir, "data", "app.db")
    
    return default_db_path

def run_migration():
    """Exécute la migration pour mettre à jour les tables"""
    db_path = get_database_path()
    
    # Vérifier que la base de données existe
    if not os.path.exists(db_path):
        print(f"Erreur: Base de données non trouvée à {db_path}")
        return False
    
    # Connexion à la base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. Mise à jour de la table purchase_orders
        print("Mise à jour de la table purchase_orders...")
        
        # Vérifier si les colonnes existent déjà
        cursor.execute("PRAGMA table_info(purchase_orders)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Ajouter les nouvelles colonnes pour les montants financiers
        if "subtotal_amount" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN subtotal_amount FLOAT DEFAULT 0")
            print("Colonne subtotal_amount ajoutée")
            
        if "discount_percent" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN discount_percent FLOAT DEFAULT 0")
            print("Colonne discount_percent ajoutée")
            
        if "discount_amount" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN discount_amount FLOAT DEFAULT 0")
            print("Colonne discount_amount ajoutée")
            
        if "tax_percent" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN tax_percent FLOAT DEFAULT 19")
            print("Colonne tax_percent ajoutée")
            
        if "tax_amount" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN tax_amount FLOAT DEFAULT 0")
            print("Colonne tax_amount ajoutée")
            
        if "shipping_amount" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN shipping_amount FLOAT DEFAULT 0")
            print("Colonne shipping_amount ajoutée")
            
        # Ajouter les nouvelles colonnes pour le suivi des paiements
        if "payment_status" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN payment_status TEXT DEFAULT 'unpaid'")
            print("Colonne payment_status ajoutée")
            
        if "payment_due_date" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN payment_due_date TIMESTAMP")
            print("Colonne payment_due_date ajoutée")
            
        if "advance_payment_amount" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN advance_payment_amount FLOAT DEFAULT 0")
            print("Colonne advance_payment_amount ajoutée")
            
        # Ajouter les nouvelles colonnes pour le suivi du workflow
        if "approved_at" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN approved_at TIMESTAMP")
            print("Colonne approved_at ajoutée")
            
        if "ordered_at" not in columns:
            cursor.execute("ALTER TABLE purchase_orders ADD COLUMN ordered_at TIMESTAMP")
            print("Colonne ordered_at ajoutée")
            
        # 2. Mise à jour de la table purchase_order_items
        print("Mise à jour de la table purchase_order_items...")
        
        # Vérifier si les colonnes existent déjà
        cursor.execute("PRAGMA table_info(purchase_order_items)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Ajouter les nouvelles colonnes pour les montants financiers
        if "discount_percent" not in columns:
            cursor.execute("ALTER TABLE purchase_order_items ADD COLUMN discount_percent FLOAT DEFAULT 0")
            print("Colonne discount_percent ajoutée")
            
        if "discount_amount" not in columns:
            cursor.execute("ALTER TABLE purchase_order_items ADD COLUMN discount_amount FLOAT DEFAULT 0")
            print("Colonne discount_amount ajoutée")
            
        if "tax_percent" not in columns:
            cursor.execute("ALTER TABLE purchase_order_items ADD COLUMN tax_percent FLOAT DEFAULT 19")
            print("Colonne tax_percent ajoutée")
            
        if "tax_amount" not in columns:
            cursor.execute("ALTER TABLE purchase_order_items ADD COLUMN tax_amount FLOAT DEFAULT 0")
            print("Colonne tax_amount ajoutée")
            
        if "subtotal" not in columns:
            cursor.execute("ALTER TABLE purchase_order_items ADD COLUMN subtotal FLOAT DEFAULT 0")
            print("Colonne subtotal ajoutée")
            
        # Ajouter les nouvelles colonnes pour la réception
        if "remaining_quantity" not in columns:
            cursor.execute("ALTER TABLE purchase_order_items ADD COLUMN remaining_quantity FLOAT DEFAULT 0")
            print("Colonne remaining_quantity ajoutée")
            
        if "return_quantity" not in columns:
            cursor.execute("ALTER TABLE purchase_order_items ADD COLUMN return_quantity FLOAT DEFAULT 0")
            print("Colonne return_quantity ajoutée")
            
        # Ajouter les autres colonnes
        if "unit_of_measure" not in columns:
            cursor.execute("ALTER TABLE purchase_order_items ADD COLUMN unit_of_measure TEXT DEFAULT 'pcs'")
            print("Colonne unit_of_measure ajoutée")
            
        # 3. Initialiser les valeurs pour les commandes existantes
        print("Initialisation des valeurs pour les commandes existantes...")
        
        # Récupérer toutes les commandes
        cursor.execute("SELECT id, total_amount FROM purchase_orders")
        orders = cursor.fetchall()
        
        for order_id, total_amount in orders:
            # Calculer les valeurs par défaut
            subtotal = total_amount / 1.19  # Supposer que le total est TTC avec TVA 19%
            tax_amount = total_amount - subtotal
            
            # Mettre à jour la commande
            cursor.execute("""
                UPDATE purchase_orders
                SET subtotal_amount = ?,
                    tax_amount = ?
                WHERE id = ?
            """, (subtotal, tax_amount, order_id))
            
            # Mettre à jour les articles de la commande
            cursor.execute("""
                UPDATE purchase_order_items
                SET subtotal = quantity * purchase_unit_price,
                    total_price = quantity * purchase_unit_price * 1.19,
                    tax_amount = quantity * purchase_unit_price * 0.19,
                    remaining_quantity = quantity - received_quantity
                WHERE po_id = ?
            """, (order_id,))
            
        print(f"{len(orders)} commandes mises à jour")
        
        # Valider les modifications
        conn.commit()
        print("Migration terminée avec succès")
        return True
        
    except Exception as e:
        # Annuler les modifications en cas d'erreur
        conn.rollback()
        print(f"Erreur lors de la migration: {str(e)}")
        return False
        
    finally:
        # Fermer la connexion
        conn.close()

if __name__ == "__main__":
    run_migration()
