"""
Migration pour ajouter les champs sale_id et transaction_type à la table customer_transactions.
"""
from alembic import op
import sqlalchemy as sa

# Révision identifiants utilisés par Alembic
revision = 'add_sale_id_to_customer_transactions'
down_revision = None  # Remplacer par l'ID de la dernière migration si nécessaire
branch_labels = None
depends_on = None

def upgrade():
    """Mise à jour de la base de données"""
    # Ajouter la colonne sale_id
    op.add_column('customer_transactions', sa.Column('sale_id', sa.Integer(), nullable=True))
    
    # Ajouter la colonne transaction_type
    op.add_column('customer_transactions', sa.Column('transaction_type', sa.String(), nullable=True, server_default='manual'))
    
    # Ajouter la contrainte de clé étrangère pour sale_id
    op.create_foreign_key(
        'fk_customer_transactions_sale_id',
        'customer_transactions',
        'sales',
        ['sale_id'],
        ['id']
    )

def downgrade():
    """Annulation de la mise à jour"""
    # Supprimer la contrainte de clé étrangère
    op.drop_constraint('fk_customer_transactions_sale_id', 'customer_transactions', type_='foreignkey')
    
    # Supprimer les colonnes
    op.drop_column('customer_transactions', 'transaction_type')
    op.drop_column('customer_transactions', 'sale_id')
