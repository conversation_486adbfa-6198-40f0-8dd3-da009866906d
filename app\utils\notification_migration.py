"""
Migration pour supprimer l'ancien système de notifications et le remplacer par l'event bus.
"""
import logging
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger(__name__)


class NotificationMigration:
    """
    Classe pour migrer de l'ancien système de notifications vers le nouveau système basé sur l'event bus.
    """
    
    def __init__(self, db: Session):
        """
        Initialise la migration
        
        Args:
            db: Session de base de données
        """
        self.db = db
    
    def backup_existing_notifications(self) -> List[Dict[str, Any]]:
        """
        Sauvegarde les notifications existantes avant suppression
        
        Returns:
            Liste des notifications sauvegardées
        """
        try:
            # Récupérer toutes les notifications existantes
            result = self.db.execute(text("""
                SELECT 
                    n.id,
                    n.user_id,
                    n.type,
                    n.priority,
                    n.title,
                    n.message,
                    n.status,
                    n.data,
                    n.action_url,
                    n.icon,
                    n.expiry_date,
                    n.created_at,
                    n.updated_at
                FROM notifications n
                ORDER BY n.created_at DESC
            """))
            
            notifications = []
            for row in result:
                notifications.append({
                    'id': row.id,
                    'user_id': row.user_id,
                    'type': row.type,
                    'priority': row.priority,
                    'title': row.title,
                    'message': row.message,
                    'status': row.status,
                    'data': row.data,
                    'action_url': row.action_url,
                    'icon': row.icon,
                    'expiry_date': row.expiry_date,
                    'created_at': row.created_at,
                    'updated_at': row.updated_at
                })
            
            logger.info(f"Sauvegarde de {len(notifications)} notifications")
            return notifications
            
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des notifications: {e}")
            return []
    
    def remove_notification_tables(self) -> bool:
        """
        Supprime les tables de notifications de la base de données
        
        Returns:
            True si la suppression a réussi, False sinon
        """
        try:
            # Supprimer les tables dans l'ordre correct (contraintes de clés étrangères)
            tables_to_drop = [
                'notification_deliveries',
                'notifications'
            ]
            
            for table in tables_to_drop:
                try:
                    # Vérifier si la table existe
                    result = self.db.execute(text(f"""
                        SELECT name FROM sqlite_master 
                        WHERE type='table' AND name='{table}'
                    """))
                    
                    if result.fetchone():
                        # Supprimer la table
                        self.db.execute(text(f"DROP TABLE IF EXISTS {table}"))
                        logger.info(f"Table {table} supprimée")
                    else:
                        logger.info(f"Table {table} n'existe pas")
                        
                except Exception as e:
                    logger.warning(f"Erreur lors de la suppression de la table {table}: {e}")
            
            self.db.commit()
            logger.info("Suppression des tables de notifications terminée")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la suppression des tables: {e}")
            self.db.rollback()
            return False
    
    def create_event_log_table(self) -> bool:
        """
        Crée une table pour logger les événements de l'event bus (optionnel)
        
        Returns:
            True si la création a réussi, False sinon
        """
        try:
            # Créer une table simple pour logger les événements importants
            self.db.execute(text("""
                CREATE TABLE IF NOT EXISTS event_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type VARCHAR(50) NOT NULL,
                    event_data TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_event_log_type (event_type),
                    INDEX idx_event_log_created (created_at)
                )
            """))
            
            self.db.commit()
            logger.info("Table event_log créée")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la création de la table event_log: {e}")
            self.db.rollback()
            return False
    
    def migrate_to_event_bus(self, backup_file: str = None) -> bool:
        """
        Effectue la migration complète vers le système basé sur l'event bus
        
        Args:
            backup_file: Fichier pour sauvegarder les notifications (optionnel)
            
        Returns:
            True si la migration a réussi, False sinon
        """
        try:
            logger.info("Début de la migration vers le système d'event bus")
            
            # 1. Sauvegarder les notifications existantes
            notifications = self.backup_existing_notifications()
            
            if backup_file and notifications:
                import json
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(notifications, f, indent=2, default=str)
                logger.info(f"Notifications sauvegardées dans {backup_file}")
            
            # 2. Supprimer les anciennes tables
            if not self.remove_notification_tables():
                logger.error("Échec de la suppression des tables")
                return False
            
            # 3. Créer la nouvelle table de log (optionnel)
            if not self.create_event_log_table():
                logger.warning("Échec de la création de la table event_log")
            
            logger.info("Migration vers l'event bus terminée avec succès")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la migration: {e}")
            return False
    
    def log_event(self, event_type: str, event_data: str = None, user_id: int = None):
        """
        Enregistre un événement dans la table de log
        
        Args:
            event_type: Type d'événement
            event_data: Données de l'événement (JSON)
            user_id: ID de l'utilisateur (optionnel)
        """
        try:
            self.db.execute(text("""
                INSERT INTO event_log (event_type, event_data, user_id)
                VALUES (:event_type, :event_data, :user_id)
            """), {
                'event_type': event_type,
                'event_data': event_data,
                'user_id': user_id
            })
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement de l'événement: {e}")


def run_migration(db_session: Session, backup_file: str = "notifications_backup.json"):
    """
    Lance la migration des notifications
    
    Args:
        db_session: Session de base de données
        backup_file: Fichier de sauvegarde
    """
    migration = NotificationMigration(db_session)
    
    print("=== Migration du système de notifications ===")
    print("Suppression de l'ancien système basé sur la base de données...")
    print("Remplacement par un système basé sur l'event bus...")
    
    success = migration.migrate_to_event_bus(backup_file)
    
    if success:
        print("✓ Migration terminée avec succès")
        print(f"✓ Notifications sauvegardées dans {backup_file}")
        print("✓ Nouveau système d'event bus activé")
    else:
        print("✗ Échec de la migration")
        print("Vérifiez les logs pour plus de détails")
    
    return success


if __name__ == "__main__":
    # Test de la migration (nécessite une session de base de données)
    print("Script de migration des notifications")
    print("Ce script doit être exécuté avec une session de base de données valide")
    print("Utilisez run_migration(db_session) pour lancer la migration")
