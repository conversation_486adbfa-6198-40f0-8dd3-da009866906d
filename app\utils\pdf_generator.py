"""
Module de génération de PDF pour l'application
Utilise ReportLab pour générer des PDF
"""
import os
import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import cm
from reportlab.platypus import (
    SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle,
    Image, PageBreak
)
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# Définir les styles
styles = getSampleStyleSheet()
title_style = styles['Heading1']
subtitle_style = styles['Heading2']
normal_style = styles['Normal']
bold_style = ParagraphStyle(
    'Bold', parent=normal_style, fontName='Helvetica-Bold'
)

class PDFGenerator:
    """Classe de base pour la génération de PDF"""

    def __init__(self, output_path=None):
        """Initialise le générateur de PDF"""
        self.output_path = output_path or os.path.join(os.getcwd(), "output.pdf")
        self.elements = []

    def add_title(self, title):
        """Ajoute un titre au document"""
        self.elements.append(Paragraph(title, title_style))
        self.elements.append(Spacer(1, 0.5 * cm))

    def add_subtitle(self, subtitle):
        """Ajoute un sous-titre au document"""
        self.elements.append(Paragraph(subtitle, subtitle_style))
        self.elements.append(Spacer(1, 0.3 * cm))

    def add_text(self, text, style=None):
        """Ajoute du texte au document"""
        style = style or normal_style
        self.elements.append(Paragraph(text, style))
        self.elements.append(Spacer(1, 0.2 * cm))

    def add_spacer(self, height=0.5):
        """Ajoute un espace au document"""
        self.elements.append(Spacer(1, height * cm))

    def add_table(self, data, col_widths=None, style=None):
        """Ajoute un tableau au document"""
        table = Table(data, colWidths=col_widths)

        # Style par défaut
        default_style = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]

        if style:
            default_style.extend(style)

        table.setStyle(TableStyle(default_style))
        self.elements.append(table)
        self.elements.append(Spacer(1, 0.5 * cm))

    def add_image(self, image_path, width=None, height=None):
        """Ajoute une image au document"""
        if not os.path.exists(image_path):
            return

        img = Image(image_path)
        if width:
            img.drawWidth = width
        if height:
            img.drawHeight = height

        self.elements.append(img)
        self.elements.append(Spacer(1, 0.5 * cm))

    def add_page_break(self):
        """Ajoute un saut de page au document"""
        self.elements.append(PageBreak())

    def generate(self):
        """Génère le document PDF"""
        # Créer le répertoire de sortie s'il n'existe pas
        output_dir = os.path.dirname(self.output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"Répertoire de sortie créé: {output_dir}")

        print(f"Génération du PDF: {self.output_path}")

        doc = SimpleDocTemplate(
            self.output_path,
            pagesize=A4,
            rightMargin=2 * cm,
            leftMargin=2 * cm,
            topMargin=2 * cm,
            bottomMargin=2 * cm
        )

        doc.build(self.elements)
        print(f"PDF généré avec succès: {self.output_path}")
        return self.output_path


class RepairPDFGenerator(PDFGenerator):
    """Générateur de PDF pour les réparations"""

    def generate_repair_order(self, repair_data):
        """Génère un ordre de réparation"""
        # Titre
        self.add_title("ORDRE DE RÉPARATION")

        # Informations de base
        self.add_subtitle(f"Réparation #{repair_data.get('id', 'N/A')}")

        # Date et statut
        date_str = repair_data.get('created_at', datetime.datetime.now()).strftime("%d/%m/%Y")
        self.add_text(f"Date: {date_str}", bold_style)
        self.add_text(f"Statut: {repair_data.get('status', 'N/A')}", bold_style)

        # Informations client
        self.add_subtitle("Informations client")
        customer = repair_data.get('customer', {})
        customer_info = [
            ["Nom", customer.get('name', 'N/A')],
            ["Téléphone", customer.get('phone', 'N/A')],
            ["Email", customer.get('email', 'N/A')],
            ["Adresse", customer.get('address', 'N/A')]
        ]
        self.add_table(customer_info, col_widths=[4 * cm, 12 * cm])

        # Informations appareil
        self.add_subtitle("Informations appareil")
        device_info = [
            ["Marque", repair_data.get('brand', 'N/A')],
            ["Modèle", repair_data.get('model', 'N/A')],
            ["Numéro de série", repair_data.get('serial_number', 'N/A')]
        ]
        self.add_table(device_info, col_widths=[4 * cm, 12 * cm])

        # Description du problème
        self.add_subtitle("Description du problème")
        self.add_text(repair_data.get('description', 'Aucune description fournie'))

        # Diagnostic
        self.add_subtitle("Diagnostic")
        diagnosis = repair_data.get('diagnosis', 'Aucun diagnostic fourni')
        # S'assurer que le diagnostic est une chaîne de caractères
        if diagnosis is None:
            diagnosis = 'Aucun diagnostic fourni'
        elif not isinstance(diagnosis, str):
            # Si c'est un objet, essayer d'extraire les informations pertinentes
            if hasattr(diagnosis, 'findings'):
                diagnosis = diagnosis.findings
            else:
                diagnosis = str(diagnosis)
        self.add_text(diagnosis)

        # Pièces utilisées
        self.add_subtitle("Pièces à utiliser")
        parts = repair_data.get('used_parts', [])
        if parts:
            parts_data = [["Référence", "Nom", "Quantité", "Prix unitaire", "Total"]]
            for part in parts:
                parts_data.append([
                    part.get('sku', 'N/A'),
                    part.get('name', 'N/A'),
                    str(part.get('quantity', 0)),
                    f"{part.get('unit_price', 0):.2f} DA",
                    f"{part.get('total_price', 0):.2f} DA"
                ])
            self.add_table(parts_data)
        else:
            self.add_text("Aucune pièce utilisée")

        # Estimation
        self.add_subtitle("Estimation")
        estimate_info = [
            ["Coût des pièces", f"{repair_data.get('parts_cost', 0):.2f} DA"],
            ["Coût de main d'œuvre", f"{repair_data.get('labor_cost', 0):.2f} DA"],
            ["Prix total estimé", f"{repair_data.get('estimated_price', 0):.2f} DA"]
        ]
        self.add_table(estimate_info, col_widths=[8 * cm, 8 * cm])

        # Date estimée de fin
        if repair_data.get('estimated_completion_date'):
            completion_date = repair_data['estimated_completion_date'].strftime("%d/%m/%Y")
            self.add_text(f"Date estimée de fin: {completion_date}", bold_style)

        # Signature
        self.add_spacer(2)
        self.add_text("Signature du client:", bold_style)
        self.add_spacer(2)
        self.add_text("Signature du technicien:", bold_style)

        return self.generate()

    def generate_repair_invoice(self, repair_data):
        """Génère une facture de réparation"""
        # Titre
        self.add_title("FACTURE DE RÉPARATION")

        # Informations de base
        self.add_subtitle(f"Facture #{repair_data.get('invoice_number', 'N/A')}")

        # Date et référence
        date_str = repair_data.get('invoice_date', datetime.datetime.now()).strftime("%d/%m/%Y")
        self.add_text(f"Date: {date_str}", bold_style)
        self.add_text(f"Référence réparation: #{repair_data.get('id', 'N/A')}", bold_style)

        # Informations client
        self.add_subtitle("Informations client")
        customer = repair_data.get('customer', {})
        customer_info = [
            ["Nom", customer.get('name', 'N/A')],
            ["Téléphone", customer.get('phone', 'N/A')],
            ["Email", customer.get('email', 'N/A')],
            ["Adresse", customer.get('address', 'N/A')]
        ]
        self.add_table(customer_info, col_widths=[4 * cm, 12 * cm])

        # Informations appareil
        self.add_subtitle("Informations appareil")
        device_info = [
            ["Marque", repair_data.get('brand', 'N/A')],
            ["Modèle", repair_data.get('model', 'N/A')],
            ["Numéro de série", repair_data.get('serial_number', 'N/A')]
        ]
        self.add_table(device_info, col_widths=[4 * cm, 12 * cm])

        # Description des travaux
        self.add_subtitle("Travaux effectués")
        self.add_text(repair_data.get('work_performed', 'Aucune description fournie'))

        # Détails de facturation
        self.add_subtitle("Détails de facturation")

        # Sous-total (convertir en float pour éviter les erreurs de type)
        parts_cost = float(repair_data.get('parts_cost', 0))
        labor_cost = float(repair_data.get('labor_cost', 0))
        subtotal = parts_cost + labor_cost

        # Calcul des taxes et remises (convertir en float)
        tax_amount = float(repair_data.get('tax_amount', 0))
        discount_amount = float(repair_data.get('discount_amount', 0))

        # Total final
        final_amount = float(repair_data.get('final_amount', subtotal + tax_amount - discount_amount))

        billing_info = [
            ["Description", "Montant"],
            ["Coût des pièces", f"{repair_data.get('parts_cost', 0):.2f} DA"],
            ["Coût de main d'œuvre", f"{repair_data.get('labor_cost', 0):.2f} DA"],
            ["Sous-total", f"{subtotal:.2f} DA"],
        ]

        if tax_amount > 0:
            billing_info.append(["Taxes", f"{tax_amount:.2f} DA"])

        if discount_amount > 0:
            billing_info.append(["Remise", f"-{discount_amount:.2f} DA"])

        billing_info.append(["Total", f"{final_amount:.2f} DA"])

        self.add_table(billing_info, col_widths=[8 * cm, 8 * cm])

        # Informations de paiement
        self.add_subtitle("Informations de paiement")
        payment_info = [
            ["Statut", repair_data.get('payment_status', 'Non payé')],
            ["Méthode", repair_data.get('payment_method', 'N/A')],
        ]

        if repair_data.get('payment_date'):
            payment_date = repair_data['payment_date'].strftime("%d/%m/%Y")
            payment_info.append(["Date de paiement", payment_date])

        total_paid = float(repair_data.get('total_paid', 0))
        if total_paid > 0:
            payment_info.append(["Montant payé", f"{total_paid:.2f} DA"])

        if total_paid < final_amount:
            payment_info.append(["Reste à payer", f"{final_amount - total_paid:.2f} DA"])

        self.add_table(payment_info, col_widths=[8 * cm, 8 * cm])

        # Conditions et notes
        self.add_subtitle("Conditions et notes")
        self.add_text("Garantie: Les pièces remplacées sont garanties 3 mois à compter de la date de facturation.")
        self.add_text("Paiement: Le paiement est dû à la réception de la facture.")

        # Signature
        self.add_spacer(1)
        self.add_text("Signature et cachet:", bold_style)

        return self.generate()

    def generate_repair_receipt(self, repair_data, payment_data):
        """Génère un reçu de paiement pour une réparation"""
        # Titre
        self.add_title("REÇU DE PAIEMENT")

        # Informations de base
        self.add_subtitle(f"Reçu #{payment_data.get('id', 'N/A')}")

        # Date
        date_str = payment_data.get('payment_date', datetime.datetime.now()).strftime("%d/%m/%Y")
        self.add_text(f"Date: {date_str}", bold_style)
        self.add_text(f"Référence réparation: #{repair_data.get('id', 'N/A')}", bold_style)
        self.add_text(f"Référence facture: #{repair_data.get('invoice_number', 'N/A')}", bold_style)

        # Informations client
        self.add_subtitle("Informations client")
        customer = repair_data.get('customer', {})
        customer_info = [
            ["Nom", customer.get('name', 'N/A')],
            ["Téléphone", customer.get('phone', 'N/A')]
        ]
        self.add_table(customer_info, col_widths=[4 * cm, 12 * cm])

        # Informations de paiement
        self.add_subtitle("Détails du paiement")
        payment_info = [
            ["Montant payé", f"{payment_data.get('amount', 0):.2f} DA"],
            ["Méthode de paiement", payment_data.get('payment_method', 'N/A')],
            ["Référence de paiement", payment_data.get('reference', 'N/A')]
        ]
        self.add_table(payment_info, col_widths=[8 * cm, 8 * cm])

        # Statut de la facture
        self.add_subtitle("Statut de la facture")

        # Total de la facture
        final_amount = repair_data.get('final_amount', 0)

        # Total payé (incluant ce paiement)
        total_paid = repair_data.get('total_paid', 0)

        # Reste à payer
        remaining = max(0, final_amount - total_paid)

        invoice_status = [
            ["Montant total de la facture", f"{final_amount:.2f} DA"],
            ["Total payé", f"{total_paid:.2f} DA"],
            ["Reste à payer", f"{remaining:.2f} DA"],
            ["Statut", "Payé intégralement" if remaining == 0 else "Partiellement payé"]
        ]
        self.add_table(invoice_status, col_widths=[8 * cm, 8 * cm])

        # Notes
        if payment_data.get('notes'):
            self.add_subtitle("Notes")
            self.add_text(payment_data.get('notes'))

        # Signature
        self.add_spacer(2)
        self.add_text("Signature:", bold_style)

        return self.generate()

    def generate_repair_deposit_receipt(self, repair_data):
        """Génère un reçu de dépôt pour une réparation"""
        # Titre
        self.add_title("REÇU DE DÉPÔT - RÉPARATION")

        # Informations de base
        self.add_subtitle(f"Réparation #{repair_data.get('number', 'N/A')}")

        # Date et statut
        date_str = repair_data.get('created_at', datetime.datetime.now()).strftime("%d/%m/%Y")
        self.add_text(f"Date de dépôt: {date_str}", bold_style)

        # Date prévue de fin
        scheduled_date = repair_data.get('scheduled_date')
        if scheduled_date:
            if isinstance(scheduled_date, str):
                try:
                    scheduled_date = datetime.datetime.strptime(scheduled_date, "%Y-%m-%dT%H:%M:%S")
                except ValueError:
                    try:
                        scheduled_date = datetime.datetime.strptime(scheduled_date, "%Y-%m-%d")
                    except ValueError:
                        scheduled_date = None

            if scheduled_date:
                scheduled_date_str = scheduled_date.strftime("%d/%m/%Y")
                self.add_text(f"Date prévue de fin: {scheduled_date_str}", bold_style)

        # Informations client
        self.add_subtitle("Informations client")
        customer = repair_data.get('customer', {})
        customer_info = [
            ["Nom", customer.get('name', repair_data.get('customer_name', 'N/A'))],
            ["Téléphone", customer.get('phone', 'N/A')]
        ]
        self.add_table(customer_info, col_widths=[4 * cm, 12 * cm])

        # Informations appareil
        self.add_subtitle("Informations appareil")
        device_info = [
            ["Marque", repair_data.get('brand', 'N/A')],
            ["Modèle", repair_data.get('model', 'N/A')],
            ["Numéro de série", repair_data.get('serial_number', 'N/A')]
        ]
        self.add_table(device_info, col_widths=[4 * cm, 12 * cm])

        # Description du problème
        self.add_subtitle("Description du problème")
        self.add_text(repair_data.get('reported_issue', repair_data.get('description', 'Aucune description fournie')))

        # Conditions générales
        self.add_subtitle("Conditions générales")
        self.add_text("1. Ce reçu doit être présenté lors de la récupération de l'appareil.")
        self.add_text("2. Les délais de réparation sont donnés à titre indicatif et peuvent varier selon la disponibilité des pièces.")
        self.add_text("3. Nous déclinons toute responsabilité pour les données non sauvegardées.")
        self.add_text("4. Les appareils non récupérés dans un délai de 3 mois seront considérés comme abandonnés.")

        # Signature
        self.add_spacer(1)
        self.add_text("Signature du client:", bold_style)
        self.add_spacer(2)
        self.add_text("Signature du technicien:", bold_style)

        return self.generate()

    def generate_completed_repair_receipt(self, repair_data):
        """Génère un reçu pour une réparation terminée"""
        # Titre
        self.add_title("REÇU DE RÉPARATION")

        # Informations de base
        self.add_subtitle(f"Réparation #{repair_data.get('number', 'N/A')}")

        # Date et statut
        date_str = repair_data.get('created_at', datetime.datetime.now()).strftime("%d/%m/%Y")
        self.add_text(f"Date de dépôt: {date_str}", bold_style)

        # Date de fin
        completion_date = repair_data.get('completion_date')
        if completion_date:
            if isinstance(completion_date, str):
                try:
                    completion_date = datetime.datetime.strptime(completion_date, "%Y-%m-%dT%H:%M:%S")
                except ValueError:
                    try:
                        completion_date = datetime.datetime.strptime(completion_date, "%Y-%m-%d")
                    except ValueError:
                        completion_date = None

            if completion_date:
                completion_date_str = completion_date.strftime("%d/%m/%Y")
                self.add_text(f"Date de fin: {completion_date_str}", bold_style)

        # Statut
        self.add_text(f"Statut: {repair_data.get('status', 'N/A')}", bold_style)

        # Informations client
        self.add_subtitle("Informations client")
        customer = repair_data.get('customer', {})
        customer_info = [
            ["Nom", customer.get('name', repair_data.get('customer_name', 'N/A'))],
            ["Téléphone", customer.get('phone', 'N/A')],
            ["Email", customer.get('email', 'N/A')]
        ]
        self.add_table(customer_info, col_widths=[4 * cm, 12 * cm])

        # Informations appareil
        self.add_subtitle("Informations appareil")
        device_info = [
            ["Marque", repair_data.get('brand', 'N/A')],
            ["Modèle", repair_data.get('model', 'N/A')],
            ["Numéro de série", repair_data.get('serial_number', 'N/A')]
        ]
        self.add_table(device_info, col_widths=[4 * cm, 12 * cm])

        # Description du problème
        self.add_subtitle("Description du problème")
        self.add_text(repair_data.get('reported_issue', repair_data.get('description', 'Aucune description fournie')))

        # Diagnostic
        self.add_subtitle("Diagnostic")
        diagnosis = repair_data.get('diagnosis', 'Aucun diagnostic fourni')
        # S'assurer que le diagnostic est une chaîne de caractères
        if diagnosis is None:
            diagnosis = 'Aucun diagnostic fourni'
        elif not isinstance(diagnosis, str):
            # Si c'est un objet, essayer d'extraire les informations pertinentes
            if hasattr(diagnosis, 'findings'):
                diagnosis = diagnosis.findings
            else:
                diagnosis = str(diagnosis)
        self.add_text(diagnosis)

        # Travaux effectués
        self.add_subtitle("Travaux effectués")
        work_performed = repair_data.get('work_performed', 'Aucun travail spécifié')
        if work_performed is None:
            work_performed = 'Aucun travail spécifié'
        self.add_text(work_performed)

        # Pièces utilisées
        self.add_subtitle("Pièces utilisées")
        parts = repair_data.get('used_parts', [])
        if parts:
            parts_data = [["Nom", "Quantité", "Prix unitaire", "Total"]]
            for part in parts:
                parts_data.append([
                    part.get('name', 'N/A'),
                    str(part.get('quantity', 0)),
                    f"{part.get('unit_price', 0):.2f} DA",
                    f"{part.get('total_price', 0):.2f} DA"
                ])
            self.add_table(parts_data)
        else:
            self.add_text("Aucune pièce utilisée")

        # Coûts
        self.add_subtitle("Coûts")

        # Sous-total (convertir en float pour éviter les erreurs de type)
        parts_cost = float(repair_data.get('parts_cost', 0))
        labor_cost = float(repair_data.get('labor_cost', 0))
        subtotal = parts_cost + labor_cost

        # Calcul des taxes et remises (convertir en float)
        tax_amount = float(repair_data.get('tax_amount', 0))
        discount_amount = float(repair_data.get('discount_amount', 0))

        # Total final
        final_amount = float(repair_data.get('final_amount', subtotal + tax_amount - discount_amount))

        cost_info = [
            ["Coût des pièces", f"{repair_data.get('parts_cost', 0):.2f} DA"],
            ["Coût de main d'œuvre", f"{repair_data.get('labor_cost', 0):.2f} DA"],
            ["Sous-total", f"{subtotal:.2f} DA"],
        ]

        if tax_amount > 0:
            cost_info.append(["Taxes", f"{tax_amount:.2f} DA"])

        if discount_amount > 0:
            cost_info.append(["Remise", f"-{discount_amount:.2f} DA"])

        cost_info.append(["Total", f"{final_amount:.2f} DA"])

        self.add_table(cost_info, col_widths=[8 * cm, 8 * cm])

        # Informations de paiement
        self.add_subtitle("Informations de paiement")
        payment_info = [
            ["Statut", repair_data.get('payment_status', 'Non payé')],
            ["Méthode", repair_data.get('payment_method', 'N/A')],
        ]

        if repair_data.get('payment_date'):
            payment_date = repair_data['payment_date'].strftime("%d/%m/%Y")
            payment_info.append(["Date de paiement", payment_date])

        total_paid = float(repair_data.get('total_paid', 0))
        if total_paid > 0:
            payment_info.append(["Montant payé", f"{total_paid:.2f} DA"])

        if total_paid < final_amount:
            payment_info.append(["Reste à payer", f"{final_amount - total_paid:.2f} DA"])

        self.add_table(payment_info, col_widths=[8 * cm, 8 * cm])

        # Garantie
        self.add_subtitle("Garantie")
        self.add_text("Les pièces remplacées sont garanties 3 mois à compter de la date de réparation.")
        self.add_text("La garantie couvre uniquement les défauts de fabrication des pièces remplacées.")
        self.add_text("La garantie ne couvre pas les dommages causés par une mauvaise utilisation, une chute ou un contact avec des liquides.")

        # Signature
        self.add_spacer(1)
        self.add_text("Signature du client:", bold_style)
        self.add_spacer(2)
        self.add_text("Signature du technicien:", bold_style)

        return self.generate()
