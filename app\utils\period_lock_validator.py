"""
Utilitaires pour la validation des périodes verrouillées.
"""
import logging
from datetime import datetime, date
from functools import wraps
from typing import Union, Optional, Callable, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.core.models.daily_closure import PeriodLock

logger = logging.getLogger(__name__)


class PeriodLockError(Exception):
    """Exception levée quand une opération est tentée sur une période verrouillée"""
    pass


class PeriodLockValidator:
    """Validateur pour les périodes verrouillées"""
    
    def __init__(self, db: Session):
        """
        Initialise le validateur
        
        Args:
            db: Session de base de données
        """
        self.db = db
    
    def is_date_locked(self, check_date: Union[date, datetime]) -> bool:
        """
        Vérifie si une date est verrouillée
        
        Args:
            check_date: Date à vérifier
            
        Returns:
            True si la date est verrouillée
        """
        if isinstance(check_date, datetime):
            check_date = check_date.date()
        
        lock = self.db.query(PeriodLock).filter(
            and_(
                PeriodLock.is_active == True,
                PeriodLock.start_date <= check_date,
                PeriodLock.end_date >= check_date
            )
        ).first()
        
        return lock is not None
    
    def get_lock_for_date(self, check_date: Union[date, datetime]) -> Optional[PeriodLock]:
        """
        Récupère le verrouillage pour une date donnée
        
        Args:
            check_date: Date à vérifier
            
        Returns:
            Objet PeriodLock ou None
        """
        if isinstance(check_date, datetime):
            check_date = check_date.date()
        
        return self.db.query(PeriodLock).filter(
            and_(
                PeriodLock.is_active == True,
                PeriodLock.start_date <= check_date,
                PeriodLock.end_date >= check_date
            )
        ).first()
    
    def validate_date_not_locked(self, check_date: Union[date, datetime], 
                                operation: str = "opération") -> None:
        """
        Valide qu'une date n'est pas verrouillée
        
        Args:
            check_date: Date à vérifier
            operation: Description de l'opération (pour le message d'erreur)
            
        Raises:
            PeriodLockError: Si la date est verrouillée
        """
        if isinstance(check_date, datetime):
            check_date = check_date.date()
        
        lock = self.get_lock_for_date(check_date)
        if lock:
            raise PeriodLockError(
                f"Impossible d'effectuer l'{operation} : "
                f"la période du {lock.start_date} au {lock.end_date} est verrouillée "
                f"({lock.lock_type}). Raison: {lock.reason or 'Non spécifiée'}"
            )
    
    def validate_period_not_locked(self, start_date: Union[date, datetime], 
                                  end_date: Union[date, datetime], 
                                  operation: str = "opération") -> None:
        """
        Valide qu'une période n'est pas verrouillée
        
        Args:
            start_date: Date de début
            end_date: Date de fin
            operation: Description de l'opération
            
        Raises:
            PeriodLockError: Si une partie de la période est verrouillée
        """
        if isinstance(start_date, datetime):
            start_date = start_date.date()
        if isinstance(end_date, datetime):
            end_date = end_date.date()
        
        # Chercher les verrouillages qui chevauchent avec la période
        overlapping_locks = self.db.query(PeriodLock).filter(
            and_(
                PeriodLock.is_active == True,
                PeriodLock.start_date <= end_date,
                PeriodLock.end_date >= start_date
            )
        ).all()
        
        if overlapping_locks:
            lock_descriptions = []
            for lock in overlapping_locks:
                lock_descriptions.append(
                    f"{lock.start_date} au {lock.end_date} ({lock.lock_type})"
                )
            
            raise PeriodLockError(
                f"Impossible d'effectuer l'{operation} : "
                f"la période chevauche avec des périodes verrouillées : "
                f"{'; '.join(lock_descriptions)}"
            )


def validate_period_not_locked(date_field: str = "transaction_date", 
                              operation: str = "cette opération"):
    """
    Décorateur pour valider qu'une période n'est pas verrouillée
    
    Args:
        date_field: Nom du champ de date dans les données
        operation: Description de l'opération
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Récupérer la session de base de données
            db = getattr(self, 'db', None)
            if not db:
                logger.warning(f"Pas de session DB trouvée pour {func.__name__}, validation ignorée")
                return func(self, *args, **kwargs)
            
            # Récupérer les données (premier argument généralement)
            data = args[0] if args else kwargs.get('data', {})
            
            if isinstance(data, dict) and date_field in data:
                check_date = data[date_field]
                
                # Valider la période
                validator = PeriodLockValidator(db)
                try:
                    validator.validate_date_not_locked(check_date, operation)
                except PeriodLockError as e:
                    logger.warning(f"Tentative d'opération sur période verrouillée: {e}")
                    raise
            
            return func(self, *args, **kwargs)
        
        return wrapper
    return decorator


def validate_modification_allowed(entity_date_field: str = "created_at",
                                 operation: str = "modification"):
    """
    Décorateur pour valider qu'une modification est autorisée sur une entité existante
    
    Args:
        entity_date_field: Nom du champ de date sur l'entité
        operation: Description de l'opération
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Récupérer la session de base de données
            db = getattr(self, 'db', None)
            if not db:
                logger.warning(f"Pas de session DB trouvée pour {func.__name__}, validation ignorée")
                return func(self, *args, **kwargs)
            
            # Récupérer l'entité (généralement le premier argument)
            entity = args[0] if args else None
            
            if entity and hasattr(entity, entity_date_field):
                entity_date = getattr(entity, entity_date_field)
                
                # Valider la période
                validator = PeriodLockValidator(db)
                try:
                    validator.validate_date_not_locked(entity_date, operation)
                except PeriodLockError as e:
                    logger.warning(f"Tentative de {operation} sur période verrouillée: {e}")
                    raise
            
            return func(self, *args, **kwargs)
        
        return wrapper
    return decorator


class PeriodLockMixin:
    """
    Mixin pour ajouter la validation des périodes verrouillées aux services
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._period_validator = None
    
    @property
    def period_validator(self) -> PeriodLockValidator:
        """Retourne le validateur de période (lazy loading)"""
        if self._period_validator is None:
            db = getattr(self, 'db', None)
            if db:
                self._period_validator = PeriodLockValidator(db)
            else:
                raise AttributeError("Aucune session de base de données trouvée")
        return self._period_validator
    
    def validate_date_not_locked(self, check_date: Union[date, datetime], 
                                operation: str = "opération") -> None:
        """
        Valide qu'une date n'est pas verrouillée
        
        Args:
            check_date: Date à vérifier
            operation: Description de l'opération
            
        Raises:
            PeriodLockError: Si la date est verrouillée
        """
        self.period_validator.validate_date_not_locked(check_date, operation)
    
    def is_date_locked(self, check_date: Union[date, datetime]) -> bool:
        """
        Vérifie si une date est verrouillée
        
        Args:
            check_date: Date à vérifier
            
        Returns:
            True si la date est verrouillée
        """
        return self.period_validator.is_date_locked(check_date)
    
    def get_lock_info(self, check_date: Union[date, datetime]) -> Optional[dict]:
        """
        Récupère les informations de verrouillage pour une date
        
        Args:
            check_date: Date à vérifier
            
        Returns:
            Dictionnaire avec les infos du verrouillage ou None
        """
        lock = self.period_validator.get_lock_for_date(check_date)
        if lock:
            return {
                'start_date': lock.start_date,
                'end_date': lock.end_date,
                'lock_type': lock.lock_type,
                'reason': lock.reason,
                'locked_by': lock.locked_by_user_id,
                'locked_at': lock.locked_at
            }
        return None
