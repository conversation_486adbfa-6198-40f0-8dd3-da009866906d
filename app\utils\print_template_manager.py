"""
Module pour la gestion des modèles d'impression.
"""
import os
import json
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
import jinja2

logger = logging.getLogger(__name__)

class PrintTemplate:
    """
    Classe représentant un modèle d'impression.
    """

    def __init__(self, name: str, template_type: str, content: str, metadata: Dict[str, Any] = None):
        """
        Initialise un modèle d'impression.

        Args:
            name: Nom du modèle
            template_type: Type de modèle (escpos, pdf, html)
            content: Contenu du modèle
            metadata: Métadonnées du modèle
        """
        self.name = name
        self.template_type = template_type
        self.content = content
        self.metadata = metadata or {}

    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit le modèle en dictionnaire.

        Returns:
            Dictionnaire représentant le modèle
        """
        return {
            "name": self.name,
            "template_type": self.template_type,
            "content": self.content,
            "metadata": self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PrintTemplate':
        """
        Crée un modèle à partir d'un dictionnaire.

        Args:
            data: Dictionnaire représentant le modèle

        Returns:
            Instance de PrintTemplate
        """
        return cls(
            name=data.get("name", ""),
            template_type=data.get("template_type", ""),
            content=data.get("content", ""),
            metadata=data.get("metadata", {})
        )

    @classmethod
    def from_file(cls, file_path: str) -> 'PrintTemplate':
        """
        Crée un modèle à partir d'un fichier.

        Args:
            file_path: Chemin vers le fichier

        Returns:
            Instance de PrintTemplate
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return cls.from_dict(data)
        except Exception as e:
            logger.error(f"Erreur lors du chargement du modèle depuis {file_path}: {str(e)}")
            raise

class PrintTemplateManager:
    """
    Gestionnaire de modèles d'impression.
    """

    def __init__(self, template_dir: str = None):
        """
        Initialise le gestionnaire de modèles.

        Args:
            template_dir: Répertoire contenant les modèles
        """
        self.template_dir = template_dir or os.path.join(os.getcwd(), "app", "templates", "print")
        self.templates: Dict[str, PrintTemplate] = {}
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(self.template_dir),
            autoescape=True
        )

        # Créer le répertoire des modèles s'il n'existe pas
        os.makedirs(self.template_dir, exist_ok=True)

        # Charger les modèles
        self._load_templates()

    def _load_templates(self):
        """
        Charge les modèles depuis le répertoire.
        """
        try:
            # Parcourir les fichiers JSON dans le répertoire des modèles
            for root, _, files in os.walk(self.template_dir):
                for file in files:
                    if file.endswith('.json'):
                        file_path = os.path.join(root, file)
                        try:
                            template = PrintTemplate.from_file(file_path)
                            self.templates[template.name] = template
                            logger.info(f"Modèle chargé: {template.name}")
                        except Exception as e:
                            logger.error(f"Erreur lors du chargement du modèle {file}: {str(e)}")
        except Exception as e:
            logger.error(f"Erreur lors du chargement des modèles: {str(e)}")

    def get_template(self, template_name: str) -> Optional[PrintTemplate]:
        """
        Récupère un modèle par son nom.

        Args:
            template_name: Nom du modèle

        Returns:
            Le modèle s'il existe, None sinon
        """
        return self.templates.get(template_name)

    def get_templates_by_type(self, template_type: str) -> List[PrintTemplate]:
        """
        Récupère tous les modèles d'un type donné.

        Args:
            template_type: Type de modèle

        Returns:
            Liste des modèles du type spécifié
        """
        return [t for t in self.templates.values() if t.template_type == template_type]

    def save_template(self, template: PrintTemplate) -> bool:
        """
        Enregistre un modèle.

        Args:
            template: Modèle à enregistrer

        Returns:
            True si l'enregistrement a réussi, False sinon
        """
        try:
            # Ajouter le modèle à la collection
            self.templates[template.name] = template

            # Enregistrer le modèle dans un fichier
            file_path = os.path.join(self.template_dir, f"{template.name}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(template.to_dict(), f, indent=2)

            logger.info(f"Modèle enregistré: {template.name}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement du modèle {template.name}: {str(e)}")
            return False

    def delete_template(self, template_name: str) -> bool:
        """
        Supprime un modèle.

        Args:
            template_name: Nom du modèle à supprimer

        Returns:
            True si la suppression a réussi, False sinon
        """
        try:
            # Vérifier si le modèle existe
            if template_name not in self.templates:
                logger.warning(f"Modèle non trouvé: {template_name}")
                return False

            # Supprimer le fichier
            file_path = os.path.join(self.template_dir, f"{template_name}.json")
            if os.path.exists(file_path):
                os.remove(file_path)

            # Supprimer le modèle de la collection
            del self.templates[template_name]

            logger.info(f"Modèle supprimé: {template_name}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de la suppression du modèle {template_name}: {str(e)}")
            return False

    def render_template(self, template_name: str, data: Dict[str, Any]) -> Optional[str]:
        """
        Rend un modèle avec des données.

        Args:
            template_name: Nom du modèle
            data: Données à utiliser pour le rendu

        Returns:
            Le contenu rendu, ou None en cas d'erreur
        """
        try:
            template = self.get_template(template_name)
            if not template:
                logger.warning(f"Modèle non trouvé: {template_name}")
                return None

            # Utiliser Jinja2 pour le rendu
            jinja_template = jinja2.Template(template.content)
            rendered_content = jinja_template.render(**data)

            return rendered_content
        except Exception as e:
            logger.error(f"Erreur lors du rendu du modèle {template_name}: {str(e)}")
            return None

    def create_default_templates(self):
        """
        Crée les modèles par défaut s'ils n'existent pas.
        """
        # Modèle de reçu de dépôt pour ESC/POS
        deposit_receipt_escpos = PrintTemplate(
            name="deposit_receipt_escpos",
            template_type="escpos",
            content="""
# Commandes ESC/POS pour le reçu de dépôt
# Utiliser {{ variable }} pour les variables

# Initialiser l'imprimante
ESC @

# Centrer le texte
ESC a 1

# Double largeur et hauteur
ESC ! 0x30
RECU DE DEPOT
REPARATION

# Taille normale
ESC ! 0x00
Reparation #{{ repair.number }}

# Aligner à gauche
ESC a 0
Date de depot: {{ repair.created_at.strftime('%d/%m/%Y') }}
Client: {{ customer.name }}
Telephone: {{ customer.phone }}

Appareil: {{ device.brand }} {{ device.model }}
IMEI/SN: {{ device.imei }}

Probleme: {{ repair.issue }}

{% if repair.expected_completion_date %}
Date de fin prevue: {{ repair.expected_completion_date.strftime('%d/%m/%Y') }}
{% endif %}

--------------------------------

Signature:





# Centrer le texte
ESC a 1
Merci de votre confiance!

# Couper le papier
GS V A
""",
            metadata={
                "description": "Modèle de reçu de dépôt pour imprimante ESC/POS",
                "paper_width": 80,
                "paper_height": 0  # Hauteur variable
            }
        )

        # Modèle de facture pour ESC/POS
        invoice_escpos = PrintTemplate(
            name="invoice_escpos",
            template_type="escpos",
            content="""
# Commandes ESC/POS pour la facture
# Utiliser {{ variable }} pour les variables

# Initialiser l'imprimante
ESC @

# Centrer le texte
ESC a 1

# Double largeur et hauteur
ESC ! 0x30
FACTURE
ESC ! 0x00
#{{ invoice.number }}

# Taille normale
ESC ! 0x00
Date: {{ invoice.date.strftime('%d/%m/%Y') }}

# Aligner à gauche
ESC a 0
Client: {{ customer.name }}
Adresse: {{ customer.address }}
Telephone: {{ customer.phone }}

--------------------------------
DETAILS
--------------------------------
{% for item in items %}
{{ item.description }}
{{ item.quantity }} x {{ item.unit_price }} = {{ item.total }}
{% endfor %}
--------------------------------

Sous-total: {{ invoice.subtotal }}
{% if invoice.discount > 0 %}
Remise: {{ invoice.discount }}
{% endif %}
TVA: {{ invoice.tax }}
TOTAL: {{ invoice.total }}

Mode de paiement: {{ invoice.payment_method }}

# Centrer le texte
ESC a 1
Merci pour votre achat!

# Couper le papier
GS V A
""",
            metadata={
                "description": "Modèle de facture pour imprimante ESC/POS",
                "paper_width": 80,
                "paper_height": 0  # Hauteur variable
            }
        )

        # Modèle de reçu de paiement pour ESC/POS
        payment_receipt_escpos = PrintTemplate(
            name="payment_receipt_escpos",
            template_type="escpos",
            content="""
# Commandes ESC/POS pour le reçu de paiement
# Utiliser {{ variable }} pour les variables

# Initialiser l'imprimante
ESC @

# Centrer le texte
ESC a 1

# Double largeur et hauteur
ESC ! 0x30
RECU DE PAIEMENT
ESC ! 0x00
#{{ payment.number }}

# Taille normale
ESC ! 0x00
Date: {{ payment.date.strftime('%d/%m/%Y') }}

# Aligner à gauche
ESC a 0
Client: {{ customer.name }}
Reparation #{{ repair.number }}

Montant: {{ payment.amount }}
Mode de paiement: {{ payment.method }}

# Centrer le texte
ESC a 1
Merci pour votre paiement!

# Couper le papier
GS V A
""",
            metadata={
                "description": "Modèle de reçu de paiement pour imprimante ESC/POS",
                "paper_width": 80,
                "paper_height": 0  # Hauteur variable
            }
        )

        # Modèle de reçu de réparation pour ESC/POS
        repair_receipt_escpos = PrintTemplate(
            name="repair_receipt_escpos",
            template_type="escpos",
            content="""
# Commandes ESC/POS pour le reçu de réparation
# Utiliser {{ variable }} pour les variables

# Initialiser l'imprimante
ESC @

# Centrer le texte
ESC a 1

# Double largeur et hauteur
ESC ! 0x30
RECU DE REPARATION
ESC ! 0x00
#{{ repair.number }}

# Taille normale
ESC ! 0x00
Date: {{ repair.created_at.strftime('%d/%m/%Y') }}

# Aligner à gauche
ESC a 0
Client: {{ customer.name }}
Telephone: {{ customer.phone }}

Appareil: {{ repair.brand }} {{ repair.model }}
N° de serie: {{ repair.serial_number }}

--------------------------------
DETAILS DE LA REPARATION
--------------------------------
Probleme: {{ repair.description }}

{% if repair.diagnosis %}
Diagnostic: {{ repair.diagnosis }}
{% endif %}

{% if repair.work_performed %}
Travaux effectues: {{ repair.work_performed }}
{% endif %}

--------------------------------
PIECES UTILISEES
--------------------------------
{% for part in used_parts %}
{{ part.name }} x{{ part.quantity }} = {{ part.total_price }} €
{% endfor %}

--------------------------------
COUT
--------------------------------
Pieces: {{ repair.parts_cost }} €
Main d'oeuvre: {{ repair.labor_cost }} €
{% if repair.tax_amount > 0 %}
TVA: {{ repair.tax_amount }} €
{% endif %}
{% if repair.discount_amount > 0 %}
Remise: -{{ repair.discount_amount }} €
{% endif %}
TOTAL: {{ repair.final_amount }} €

Statut: {{ repair.status }}
Date de fin: {{ repair.completion_date.strftime('%d/%m/%Y') if repair.completion_date else 'N/A' }}

# Centrer le texte
ESC a 1
Merci de votre confiance!

# Couper le papier
GS V A
""",
            metadata={
                "description": "Modèle de reçu de réparation pour imprimante ESC/POS",
                "paper_width": 80,
                "paper_height": 0  # Hauteur variable
            }
        )

        # Enregistrer les modèles par défaut
        self.save_template(deposit_receipt_escpos)
        self.save_template(invoice_escpos)
        self.save_template(payment_receipt_escpos)
        self.save_template(repair_receipt_escpos)
