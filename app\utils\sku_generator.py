#!/usr/bin/env python3
"""
Utilitaire centralisé pour la génération de SKU et codes-barres
"""
import datetime
import random
import string
from typing import Optional, List
from app.core.models.item_category import ItemCategory


class SKUGenerator:
    """Générateur centralisé de SKU pour éviter les doublons et incohérences"""
    
    @staticmethod
    def generate_sku(category_id: Optional[int] = None, 
                    category_name: Optional[str] = None,
                    categories: Optional[List[ItemCategory]] = None) -> str:
        """
        Génère un SKU unique basé sur la catégorie
        
        Args:
            category_id: ID de la catégorie
            category_name: Nom de la catégorie (fallback)
            categories: Liste des catégories disponibles
            
        Returns:
            SKU généré au format: PREFIX-YYMMDD-MMMSSRR
        """
        # Déterminer le préfixe de la catégorie
        category_code = SKUGenerator._get_category_prefix(
            category_id, category_name, categories
        )
        
        # Générer la partie temporelle
        timestamp = datetime.datetime.now().strftime("%y%m%d")
        
        # Ajouter un timestamp en millisecondes pour garantir l'unicité
        milliseconds = datetime.datetime.now().microsecond // 1000
        random_suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=2))
        
        # Format final: PREFIX-YYMMDD-MMMSSRR
        sku = f"{category_code}-{timestamp}-{milliseconds:03d}{random_suffix}"
        
        return sku
    
    @staticmethod
    def _get_category_prefix(category_id: Optional[int] = None,
                           category_name: Optional[str] = None,
                           categories: Optional[List[ItemCategory]] = None) -> str:
        """
        Détermine le préfixe de catégorie de manière cohérente
        
        Args:
            category_id: ID de la catégorie
            category_name: Nom de la catégorie
            categories: Liste des catégories disponibles
            
        Returns:
            Préfixe de catégorie (ex: "LCD", "PAR", etc.)
        """
        category_code = None
        
        # Méthode 1: Utiliser l'ID pour trouver le code de la catégorie
        if category_id and categories:
            for category in categories:
                if category.id == category_id:
                    category_code = category.code
                    break
        
        # Méthode 2: Fallback sur le nom de la catégorie
        if not category_code and category_name:
            # Utiliser les 3 premières lettres du nom de la catégorie
            category_code = category_name[0:3].upper()
        
        # Méthode 3: Fallback par défaut
        if not category_code:
            category_code = "GEN"  # Générique
        
        return category_code
    
    @staticmethod
    def generate_barcode() -> str:
        """
        Génère un code-barres EAN-13 simplifié
        
        Returns:
            Code-barres au format EAN-13
        """
        # Préfixe standard pour les produits internes
        prefix = "200"
        
        # Générer 9 chiffres aléatoires
        middle = ''.join(random.choices(string.digits, k=9))
        
        # Le dernier chiffre est un chiffre de contrôle, mais nous utilisons un chiffre aléatoire pour simplifier
        check_digit = random.choice(string.digits)
        
        barcode = f"{prefix}{middle}{check_digit}"
        
        return barcode
    
    @staticmethod
    def validate_sku(sku: str) -> bool:
        """
        Valide le format d'un SKU
        
        Args:
            sku: SKU à valider
            
        Returns:
            True si le SKU est valide, False sinon
        """
        if not sku:
            return False
        
        # Format attendu: PREFIX-YYMMDD-MMMSSRR
        parts = sku.split('-')
        
        if len(parts) != 3:
            return False
        
        prefix, date_part, suffix = parts
        
        # Vérifier le préfixe (2-4 lettres)
        if not prefix.isalpha() or len(prefix) < 2 or len(prefix) > 4:
            return False
        
        # Vérifier la partie date (6 chiffres)
        if not date_part.isdigit() or len(date_part) != 6:
            return False
        
        # Vérifier le suffixe (5 caractères alphanumériques)
        if len(suffix) != 5:
            return False
        
        return True
    
    @staticmethod
    def extract_category_from_sku(sku: str) -> Optional[str]:
        """
        Extrait le préfixe de catégorie d'un SKU
        
        Args:
            sku: SKU à analyser
            
        Returns:
            Préfixe de catégorie ou None si invalide
        """
        if not SKUGenerator.validate_sku(sku):
            return None
        
        return sku.split('-')[0]


class BarcodeGenerator:
    """Générateur de codes-barres"""
    
    @staticmethod
    def generate_ean13() -> str:
        """Génère un code-barres EAN-13"""
        return SKUGenerator.generate_barcode()
    
    @staticmethod
    def validate_ean13(barcode: str) -> bool:
        """
        Valide un code-barres EAN-13
        
        Args:
            barcode: Code-barres à valider
            
        Returns:
            True si valide, False sinon
        """
        if not barcode or len(barcode) != 13:
            return False
        
        if not barcode.isdigit():
            return False
        
        return True


# Fonctions de commodité
def generate_unique_sku(category_id: Optional[int] = None,
                       category_name: Optional[str] = None,
                       categories: Optional[List[ItemCategory]] = None) -> str:
    """Fonction de commodité pour générer un SKU unique"""
    return SKUGenerator.generate_sku(category_id, category_name, categories)


def generate_unique_barcode() -> str:
    """Fonction de commodité pour générer un code-barres unique"""
    return SKUGenerator.generate_barcode()
