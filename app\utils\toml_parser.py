"""
Module de remplacement pour tomli, utilisant configparser pour les fichiers TOML.
Ce module est une solution de contournement pour les problèmes de compilation avec PyInstaller.
"""
import configparser
import os
import json

def load(file_obj):
    """
    Charge un fichier TOML et retourne un dictionnaire.
    Cette fonction est une version simplifiée qui utilise configparser.
    
    Args:
        file_obj: Un objet fichier ouvert en mode binaire
        
    Returns:
        Un dictionnaire contenant les données du fichier TOML
    """
    # Convertir le contenu binaire en texte
    content = file_obj.read().decode('utf-8')
    
    # Créer un ConfigParser
    config = configparser.ConfigParser()
    
    # Lire le contenu
    config.read_string(content)
    
    # Convertir en dictionnaire
    result = {}
    for section in config.sections():
        result[section] = {}
        for key, value in config.items(section):
            # Essayer de convertir les valeurs en types appropriés
            try:
                # Essayer de convertir en nombre
                if value.isdigit():
                    result[section][key] = int(value)
                elif value.replace('.', '', 1).isdigit() and value.count('.') == 1:
                    result[section][key] = float(value)
                # Convertir les booléens
                elif value.lower() in ('true', 'yes', 'on'):
                    result[section][key] = True
                elif value.lower() in ('false', 'no', 'off'):
                    result[section][key] = False
                # Gérer les listes et dictionnaires (format JSON)
                elif value.startswith('[') or value.startswith('{'):
                    try:
                        result[section][key] = json.loads(value)
                    except json.JSONDecodeError:
                        result[section][key] = value
                else:
                    result[section][key] = value
            except (ValueError, TypeError):
                result[section][key] = value
    
    return result

def load_from_file(file_path):
    """
    Charge un fichier TOML à partir d'un chemin de fichier.
    
    Args:
        file_path: Chemin vers le fichier TOML
        
    Returns:
        Un dictionnaire contenant les données du fichier TOML
    """
    with open(file_path, 'rb') as f:
        return load(f)
