"""
Gestionnaire de transactions de base de données avec rollback automatique.
"""
import logging
from contextlib import contextmanager
from typing import Optional, Any, Dict, List
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy import event

logger = logging.getLogger(__name__)


class TransactionError(Exception):
    """Exception levée lors d'erreurs de transaction"""
    pass


class TransactionManager:
    """
    Gestionnaire de transactions avec rollback automatique et logging.
    """
    
    def __init__(self, session: Session):
        """
        Initialise le gestionnaire de transactions
        
        Args:
            session: Session SQLAlchemy
        """
        self.session = session
        self.transaction_stack = []
        self.rollback_hooks = []
        self.commit_hooks = []
    
    @contextmanager
    def atomic_transaction(self, description: str = None):
        """
        Context manager pour une transaction atomique
        
        Args:
            description: Description de la transaction pour le logging
            
        Yields:
            Session SQLAlchemy
            
        Raises:
            TransactionError: En cas d'erreur de transaction
        """
        transaction_id = len(self.transaction_stack) + 1
        transaction_desc = description or f"Transaction #{transaction_id}"
        
        logger.info(f"Début de transaction atomique: {transaction_desc}")
        
        # Créer un savepoint si on est dans une transaction imbriquée
        savepoint = None
        if self.session.in_transaction():
            savepoint = self.session.begin_nested()
            logger.debug(f"Savepoint créé pour transaction imbriquée: {transaction_desc}")
        else:
            self.session.begin()
            logger.debug(f"Nouvelle transaction commencée: {transaction_desc}")
        
        self.transaction_stack.append({
            'id': transaction_id,
            'description': transaction_desc,
            'savepoint': savepoint
        })
        
        try:
            yield self.session
            
            # Exécuter les hooks de commit
            for hook in self.commit_hooks:
                try:
                    hook()
                except Exception as e:
                    logger.error(f"Erreur dans hook de commit: {e}")
                    raise TransactionError(f"Erreur dans hook de commit: {e}")
            
            # Commit ou release du savepoint
            if savepoint:
                savepoint.commit()
                logger.debug(f"Savepoint committé: {transaction_desc}")
            else:
                self.session.commit()
                logger.info(f"Transaction committée avec succès: {transaction_desc}")
            
        except Exception as e:
            logger.error(f"Erreur dans transaction {transaction_desc}: {e}")
            
            try:
                # Exécuter les hooks de rollback
                for hook in self.rollback_hooks:
                    try:
                        hook()
                    except Exception as hook_error:
                        logger.error(f"Erreur dans hook de rollback: {hook_error}")
                
                # Rollback ou rollback du savepoint
                if savepoint:
                    savepoint.rollback()
                    logger.debug(f"Savepoint rollbacké: {transaction_desc}")
                else:
                    self.session.rollback()
                    logger.warning(f"Transaction rollbackée: {transaction_desc}")
                
            except SQLAlchemyError as rollback_error:
                logger.critical(f"Erreur lors du rollback de {transaction_desc}: {rollback_error}")
                raise TransactionError(f"Erreur critique lors du rollback: {rollback_error}")
            
            # Re-lever l'exception originale
            if isinstance(e, SQLAlchemyError):
                raise TransactionError(f"Erreur de base de données dans {transaction_desc}: {e}")
            else:
                raise TransactionError(f"Erreur dans {transaction_desc}: {e}")
        
        finally:
            # Nettoyer la pile des transactions
            if self.transaction_stack:
                self.transaction_stack.pop()
            
            # Nettoyer les hooks pour cette transaction
            self.rollback_hooks.clear()
            self.commit_hooks.clear()
    
    def add_rollback_hook(self, hook_func):
        """
        Ajoute une fonction à exécuter en cas de rollback
        
        Args:
            hook_func: Fonction à exécuter (sans paramètres)
        """
        self.rollback_hooks.append(hook_func)
    
    def add_commit_hook(self, hook_func):
        """
        Ajoute une fonction à exécuter avant le commit
        
        Args:
            hook_func: Fonction à exécuter (sans paramètres)
        """
        self.commit_hooks.append(hook_func)
    
    def is_in_transaction(self) -> bool:
        """Vérifie si on est dans une transaction"""
        return len(self.transaction_stack) > 0
    
    def current_transaction_info(self) -> Optional[Dict[str, Any]]:
        """Retourne les informations de la transaction courante"""
        if self.transaction_stack:
            return self.transaction_stack[-1].copy()
        return None


@contextmanager
def atomic_operation(session: Session, description: str = None):
    """
    Context manager simple pour une opération atomique
    
    Args:
        session: Session SQLAlchemy
        description: Description de l'opération
        
    Yields:
        Session SQLAlchemy
        
    Raises:
        TransactionError: En cas d'erreur
    """
    manager = TransactionManager(session)
    with manager.atomic_transaction(description) as tx_session:
        yield tx_session


class TransactionLogger:
    """
    Logger spécialisé pour les transactions de base de données
    """
    
    def __init__(self, session: Session):
        """
        Initialise le logger de transactions
        
        Args:
            session: Session SQLAlchemy
        """
        self.session = session
        self.operations_log = []
        
        # Écouter les événements SQLAlchemy
        event.listen(session, 'before_commit', self._before_commit)
        event.listen(session, 'after_commit', self._after_commit)
        event.listen(session, 'after_rollback', self._after_rollback)
    
    def log_operation(self, operation_type: str, entity_type: str, entity_id: Any, details: Dict[str, Any] = None):
        """
        Enregistre une opération
        
        Args:
            operation_type: Type d'opération (CREATE, UPDATE, DELETE)
            entity_type: Type d'entité
            entity_id: ID de l'entité
            details: Détails supplémentaires
        """
        operation = {
            'type': operation_type,
            'entity_type': entity_type,
            'entity_id': entity_id,
            'details': details or {},
            'timestamp': logger.info.__self__.name if hasattr(logger.info, '__self__') else 'unknown'
        }
        
        self.operations_log.append(operation)
        logger.debug(f"Opération enregistrée: {operation_type} {entity_type}#{entity_id}")
    
    def _before_commit(self, session):
        """Appelé avant le commit"""
        if self.operations_log:
            logger.info(f"Commit de {len(self.operations_log)} opération(s)")
            for op in self.operations_log:
                logger.debug(f"  - {op['type']} {op['entity_type']}#{op['entity_id']}")
    
    def _after_commit(self, session):
        """Appelé après le commit"""
        if self.operations_log:
            logger.info(f"Commit réussi pour {len(self.operations_log)} opération(s)")
        self.operations_log.clear()
    
    def _after_rollback(self, session):
        """Appelé après le rollback"""
        if self.operations_log:
            logger.warning(f"Rollback de {len(self.operations_log)} opération(s)")
            for op in self.operations_log:
                logger.warning(f"  - Annulé: {op['type']} {op['entity_type']}#{op['entity_id']}")
        self.operations_log.clear()


def validate_transaction_integrity(session: Session, validation_rules: List[callable] = None):
    """
    Valide l'intégrité des données avant le commit
    
    Args:
        session: Session SQLAlchemy
        validation_rules: Liste de fonctions de validation
        
    Raises:
        TransactionError: Si la validation échoue
    """
    if not validation_rules:
        return
    
    logger.debug("Validation de l'intégrité des transactions")
    
    for rule in validation_rules:
        try:
            result = rule(session)
            if result is False:
                raise TransactionError(f"Règle de validation échouée: {rule.__name__}")
            elif isinstance(result, str):
                raise TransactionError(f"Validation échouée: {result}")
        except Exception as e:
            if isinstance(e, TransactionError):
                raise
            else:
                raise TransactionError(f"Erreur lors de la validation {rule.__name__}: {e}")
    
    logger.debug("Validation de l'intégrité réussie")


# Décorateur pour les méthodes de service
def transactional(description: str = None):
    """
    Décorateur pour rendre une méthode transactionnelle
    
    Args:
        description: Description de la transaction
    """
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            if hasattr(self, 'db') and self.db:
                with atomic_operation(self.db, description or f"{func.__name__}") as session:
                    return func(self, *args, **kwargs)
            else:
                return func(self, *args, **kwargs)
        return wrapper
    return decorator
