"""
Utilitaire pour mettre à jour la vue de trésorerie depuis d'autres modules.
"""

import logging
from typing import Optional

logger = logging.getLogger(__name__)

# Variable globale pour stocker la référence à la vue de trésorerie
_treasury_view = None

def set_treasury_view(treasury_view):
    """
    Définit la vue de trésorerie à actualiser
    
    Args:
        treasury_view: Instance de TreasuryView
    """
    global _treasury_view
    _treasury_view = treasury_view
    logger.info("Vue de trésorerie définie pour les mises à jour automatiques")

def get_treasury_view():
    """
    Récupère la vue de trésorerie actuelle
    
    Returns:
        Instance de TreasuryView ou None
    """
    global _treasury_view
    return _treasury_view

def update_treasury():
    """
    Actualise les données de la trésorerie
    
    Returns:
        bool: True si l'actualisation a réussi, False sinon
    """
    global _treasury_view
    
    if _treasury_view is None:
        logger.warning("Impossible d'actualiser la trésorerie: vue de trésorerie non définie")
        return False
    
    try:
        logger.info("Actualisation des données de la trésorerie")
        _treasury_view._load_data_wrapper()
        logger.info("Trésorerie actualisée avec succès")
        return True
    except Exception as e:
        logger.error(f"Erreur lors de l'actualisation de la trésorerie: {e}")
        import traceback
        traceback.print_exc()
        return False

def update_cash_register(cash_register_id: int):
    """
    Actualise une caisse spécifique
    
    Args:
        cash_register_id: ID de la caisse à actualiser
        
    Returns:
        bool: True si l'actualisation a réussi, False sinon
    """
    global _treasury_view
    
    if _treasury_view is None:
        logger.warning(f"Impossible d'actualiser la caisse {cash_register_id}: vue de trésorerie non définie")
        return False
    
    try:
        logger.info(f"Actualisation de la caisse {cash_register_id}")
        # Pour l'instant, on actualise toute la vue
        # Dans une version future, on pourrait actualiser seulement la caisse spécifique
        _treasury_view._load_data_wrapper()
        logger.info(f"Caisse {cash_register_id} actualisée avec succès")
        return True
    except Exception as e:
        logger.error(f"Erreur lors de l'actualisation de la caisse {cash_register_id}: {e}")
        import traceback
        traceback.print_exc()
        return False

def notify_transaction_added(cash_register_id: int, transaction_amount: float, transaction_type: str = ""):
    """
    Notifie qu'une transaction a été ajoutée à une caisse
    
    Args:
        cash_register_id: ID de la caisse
        transaction_amount: Montant de la transaction
        transaction_type: Type de transaction (optionnel)
    """
    logger.info(f"Notification: Transaction de {transaction_amount} DA ajoutée à la caisse {cash_register_id} ({transaction_type})")
    
    # Émettre le signal via le bus d'événements
    try:
        from app.utils.event_bus import event_bus
        event_bus.notify_cash_transaction_added(cash_register_id)
    except Exception as e:
        logger.error(f"Erreur lors de l'émission du signal de transaction: {e}")
    
    # Actualiser la vue directement aussi
    update_cash_register(cash_register_id)

def notify_sale_payment(sale_id: int, amount: float, cash_register_id: int):
    """
    Notifie qu'un paiement de vente a été effectué
    
    Args:
        sale_id: ID de la vente
        amount: Montant du paiement
        cash_register_id: ID de la caisse de vente
    """
    logger.info(f"Notification: Paiement de vente #{sale_id} de {amount} DA dans la caisse {cash_register_id}")
    notify_transaction_added(cash_register_id, amount, "Vente")

def notify_repair_payment(repair_id: int, amount: float, cash_register_id: int):
    """
    Notifie qu'un paiement de réparation a été effectué
    
    Args:
        repair_id: ID de la réparation
        amount: Montant du paiement
        cash_register_id: ID de la caisse de réparation
    """
    logger.info(f"Notification: Paiement de réparation #{repair_id} de {amount} DA dans la caisse {cash_register_id}")
    notify_transaction_added(cash_register_id, amount, "Réparation")

def notify_expense_added(expense_id: int, amount: float, cash_register_id: int):
    """
    Notifie qu'une dépense a été ajoutée
    
    Args:
        expense_id: ID de la dépense
        amount: Montant de la dépense
        cash_register_id: ID de la caisse de dépense
    """
    logger.info(f"Notification: Dépense #{expense_id} de {amount} DA dans la caisse {cash_register_id}")
    notify_transaction_added(cash_register_id, -amount, "Dépense")  # Montant négatif pour les dépenses

def clear_treasury_view():
    """
    Efface la référence à la vue de trésorerie
    """
    global _treasury_view
    _treasury_view = None
    logger.info("Référence à la vue de trésorerie effacée")
