import os
import sys
import shutil
import subprocess
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("build_script")

def ensure_dir(directory):
    """S'assure que le répertoire existe"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"Répertoire créé: {directory}")

def copy_resources(dist_dir):
    """Copie les ressources nécessaires dans le répertoire de distribution"""
    # Créer le répertoire config dans le dossier de distribution
    config_dir = os.path.join(dist_dir, "config")
    ensure_dir(config_dir)
    
    # Copier le fichier settings.toml
    shutil.copy("config/settings.toml", os.path.join(config_dir, "settings.toml"))
    logger.info("Fichier de configuration copié")
    
    # C<PERSON>er le répertoire data
    data_dir = os.path.join(dist_dir, "data")
    ensure_dir(data_dir)
    logger.info("Répertoire de données créé")
    
    # Créer le répertoire backups
    backups_dir = os.path.join(dist_dir, "backups")
    ensure_dir(backups_dir)
    logger.info("Répertoire de sauvegardes créé")
    
    # Créer le répertoire output
    output_dir = os.path.join(dist_dir, "output")
    ensure_dir(output_dir)
    logger.info("Répertoire de sortie créé")

def build_with_nuitka():
    """Compile l'application avec Nuitka"""
    logger.info("Démarrage de la compilation avec Nuitka")
    
    # Répertoire de sortie
    output_dir = "dist"
    
    # Options de compilation Nuitka
    nuitka_options = [
        sys.executable, "-m", "nuitka",
        "--standalone",                    # Créer une application autonome
        "--follow-imports",               # Suivre tous les imports
        "--include-package=app",          # Inclure le package app
        "--include-package=config",       # Inclure le package config
        "--include-package=sqlalchemy",   # Inclure SQLAlchemy
        "--include-package=PyQt6",        # Inclure PyQt6
        "--include-package=apscheduler",  # Inclure APScheduler
        # Suppression de l'option problématique d'icône et de ressources
        "--output-dir=" + output_dir,     # Répertoire de sortie
        "main.py"                         # Fichier principal à compiler
    ]
    
    try:
        # Exécuter la commande Nuitka
        logger.info("Exécution de la commande Nuitka: " + " ".join(nuitka_options))
        subprocess.run(nuitka_options, check=True)
        
        # Chemin vers le répertoire de distribution
        dist_dir = os.path.join(output_dir, "main.dist")
        
        # Copier les ressources nécessaires
        copy_resources(dist_dir)
        
        logger.info(f"Compilation terminée avec succès. L'exécutable se trouve dans {dist_dir}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Erreur lors de la compilation: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return False

def main():
    """Fonction principale"""
    logger.info("=== Début du processus de compilation ===")
    
    # Vérifier si Nuitka est installé
    try:
        subprocess.run([sys.executable, "-m", "nuitka", "--version"], 
                       check=True, 
                       stdout=subprocess.PIPE, 
                       stderr=subprocess.PIPE)
    except subprocess.CalledProcessError:
        logger.error("Nuitka n'est pas installé. Veuillez l'installer avec 'pip install nuitka'.")
        return False
    
    # Compiler l'application
    success = build_with_nuitka()
    
    if success:
        logger.info("=== Compilation terminée avec succès ===")
    else:
        logger.error("=== Échec de la compilation ===")
    
    return success

if __name__ == "__main__":
    main()