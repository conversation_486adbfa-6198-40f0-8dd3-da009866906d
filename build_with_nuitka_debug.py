import os
import sys
import shutil
import subprocess
import logging
from pathlib import Path
import time
import platform

# Configuration du logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build_debug.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("build_debug")

def ensure_dir(directory):
    """S'assure que le répertoire existe"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"Répertoire créé: {directory}")

def check_dependencies():
    """Vérifie que toutes les dépendances nécessaires sont installées"""
    logger.info("Vérification des dépendances...")
    required_packages = [
        "nuitka",
        "PyQt6",
        "sqlalchemy",
        "apscheduler",
        "passlib",
        "toml"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package} est installé")
        except ImportError:
            logger.error(f"✗ {package} n'est pas installé")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Dépendances manquantes: {', '.join(missing_packages)}")
        logger.info("Installation des dépendances manquantes...")
        for package in missing_packages:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], check=True)
                logger.info(f"✓ {package} a été installé")
            except subprocess.CalledProcessError:
                logger.error(f"✗ Échec de l'installation de {package}")
                return False
    
    return True

def copy_resources(dist_dir):
    """Copie les ressources nécessaires dans le répertoire de distribution"""
    logger.info("Copie des ressources...")
    
    # Créer le répertoire config dans le dossier de distribution
    config_dir = os.path.join(dist_dir, "config")
    ensure_dir(config_dir)
    
    # Copier le fichier settings.toml
    settings_path = "config/settings.toml"
    if os.path.exists(settings_path):
        shutil.copy(settings_path, os.path.join(config_dir, "settings.toml"))
        logger.info("✓ Fichier de configuration copié")
    else:
        logger.warning(f"✗ Fichier de configuration non trouvé: {settings_path}")
        # Créer un fichier de configuration minimal
        with open(os.path.join(config_dir, "settings.toml"), "w", encoding="utf-8") as f:
            f.write("# Configuration par défaut\n[app]\nname = \"Nadjib-GSM\"\nversion = \"1.0.0\"\n")
        logger.info("✓ Fichier de configuration minimal créé")
    
    # Créer les répertoires nécessaires
    dirs_to_create = [
        os.path.join(dist_dir, "data"),
        os.path.join(dist_dir, "backups"),
        os.path.join(dist_dir, "output"),
        os.path.join(dist_dir, "logs")
    ]
    
    for directory in dirs_to_create:
        ensure_dir(directory)
    
    # Copier les ressources UI si elles existent
    ui_resources_src = "app/ui/resources"
    ui_resources_dst = os.path.join(dist_dir, "app/ui/resources")
    
    if os.path.exists(ui_resources_src) and os.path.isdir(ui_resources_src):
        if not os.path.exists(ui_resources_dst):
            os.makedirs(os.path.dirname(ui_resources_dst), exist_ok=True)
            shutil.copytree(ui_resources_src, ui_resources_dst)
            logger.info("✓ Ressources UI copiées")
    else:
        logger.warning(f"✗ Répertoire de ressources UI non trouvé: {ui_resources_src}")
    
    # Copier la base de données si elle existe
    db_src = "data/app.db"
    db_dst = os.path.join(dist_dir, "data/app.db")
    
    if os.path.exists(db_src):
        shutil.copy(db_src, db_dst)
        logger.info("✓ Base de données copiée")
    else:
        logger.warning(f"✗ Base de données non trouvée: {db_src}")
    
    # Copier le script de débogage
    debug_script_src = "debug_executable.py"
    debug_script_dst = os.path.join(dist_dir, "debug_executable.py")
    
    if os.path.exists(debug_script_src):
        shutil.copy(debug_script_src, debug_script_dst)
        logger.info("✓ Script de débogage copié")
    
    # Créer un fichier README
    readme_path = os.path.join(dist_dir, "README.txt")
    with open(readme_path, "w", encoding="utf-8") as f:
        f.write("""Nadjib-GSM - Application de Gestion

En cas de problème au démarrage:
1. Exécutez debug_executable.py pour diagnostiquer les problèmes
2. Vérifiez que tous les fichiers nécessaires sont présents
3. Consultez les logs dans le dossier 'logs'

Structure des dossiers:
- config/: Fichiers de configuration
- data/: Base de données
- backups/: Sauvegardes
- output/: Fichiers générés
- logs/: Journaux d'erreurs
""")
    logger.info("✓ Fichier README créé")

def build_with_nuitka():
    """Compile l'application avec Nuitka"""
    logger.info("Démarrage de la compilation avec Nuitka")
    
    # Répertoire de sortie
    output_dir = "dist"
    
    # Déterminer le fichier principal à compiler
    main_file = "main_debug.py" if os.path.exists("main_debug.py") else "main_patched.py" if os.path.exists("main_patched.py") else "main.py"
    logger.info(f"Fichier principal sélectionné pour la compilation: {main_file}")
    
    # Options de compilation Nuitka
    nuitka_options = [
        sys.executable, "-m", "nuitka",
        "--standalone",                    # Créer une application autonome
        "--enable-plugin=pyqt6",          # Activer le plugin PyQt6
        "--follow-imports",               # Suivre tous les imports
        "--include-package=app",          # Inclure le package app
        "--include-package=config",       # Inclure le package config
        "--include-package=sqlalchemy",   # Inclure SQLAlchemy
        "--include-package=PyQt6",        # Inclure PyQt6
        "--include-module=PyQt6.QtCharts", # Inclure explicitement le module QtCharts
        "--include-package=apscheduler",  # Inclure APScheduler
        "--include-package=passlib",      # Inclure passlib pour l'authentification
        "--include-package=logging",      # Inclure le module logging
        "--include-package=toml",         # Inclure le module toml
        "--include-data-dir=app/ui/resources=app/ui/resources", # Inclure les ressources UI
        "--include-data-files=config/settings.toml=config/settings.toml", # Inclure le fichier de configuration
        "--output-dir=" + output_dir,     # Répertoire de sortie
        "--show-progress",               # Afficher la progression
        "--assume-yes-for-downloads",    # Accepter automatiquement les téléchargements
        "--jobs=4",                      # Utiliser 4 processus pour la compilation
        "--lto=no",                      # Désactiver l'optimisation de temps de liaison
        "--disable-ccache",              # Désactiver ccache
        "--remove-output",               # Supprimer les sorties précédentes
    ]
    
    # Ajouter l'option pour désactiver la console en production
    if "--console" not in sys.argv:
        if platform.system() == "Windows":
            nuitka_options.append("--windows-disable-console")
        else:
            nuitka_options.append("--disable-console")
    
    # Ajouter l'icône si elle existe
    icon_path = "app/ui/resources/icon.ico"
    if os.path.exists(icon_path) and platform.system() == "Windows":
        nuitka_options.append(f"--windows-icon-from-ico={icon_path}")
    
    # Ajouter le fichier principal
    nuitka_options.append(main_file)
    
    try:
        # Exécuter la commande Nuitka
        logger.info("Exécution de la commande Nuitka: " + " ".join(nuitka_options))
        process = subprocess.Popen(
            nuitka_options,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Afficher la sortie en temps réel
        for line in process.stdout:
            line = line.strip()
            if line:
                logger.info(f"Nuitka: {line}")
        
        # Attendre la fin du processus
        return_code = process.wait()
        
        if return_code != 0:
            # Lire les erreurs
            errors = process.stderr.read()
            logger.error(f"Erreur lors de la compilation (code {return_code}): {errors}")
            return False
        
        # Chemin vers le répertoire de distribution
        dist_dir = os.path.join(output_dir, f"{os.path.splitext(main_file)[0]}.dist")
        
        # Vérifier si le répertoire de distribution existe
        if os.path.exists(dist_dir):
            # Copier les ressources nécessaires
            copy_resources(dist_dir)
            
            # Créer un fichier batch pour lancer l'application avec le débogage
            batch_file = os.path.join(dist_dir, "lancer_avec_debug.bat")
            with open(batch_file, "w", encoding="utf-8") as f:
                exe_name = f"{os.path.splitext(main_file)[0]}.exe"
                f.write(f"@echo off\necho Lancement de l'application avec le mode debug...\n\"{exe_name}\" --debug\npause")
            
            logger.info(f"Compilation terminée avec succès. L'exécutable se trouve dans {dist_dir}")
            return True
        else:
            logger.error(f"Le répertoire de distribution {dist_dir} n'a pas été créé")
            return False
    except subprocess.CalledProcessError as e:
        logger.error(f"Erreur lors de la compilation: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return False

def test_executable(dist_dir, exe_name):
    """Teste l'exécutable compilé"""
    logger.info("Test de l'exécutable compilé...")
    
    exe_path = os.path.join(dist_dir, exe_name)
    if not os.path.exists(exe_path):
        logger.error(f"Exécutable non trouvé: {exe_path}")
        return False
    
    try:
        # Exécuter avec l'option de test
        logger.info(f"Exécution de {exe_path} en mode test...")
        result = subprocess.run(
            [exe_path, "--test-mode"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=10  # Timeout de 10 secondes
        )
        
        logger.info(f"Code de sortie: {result.returncode}")
        logger.info(f"Sortie standard: {result.stdout}")
        
        if result.stderr:
            logger.warning(f"Erreur standard: {result.stderr}")
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        logger.error("Timeout lors du test de l'exécutable")
        return False
    except Exception as e:
        logger.error(f"Erreur lors du test de l'exécutable: {str(e)}")
        return False

def main():
    """Fonction principale"""
    logger.info("=== Début du processus de compilation avec débogage ===")
    
    # Vérifier les dépendances
    if not check_dependencies():
        logger.error("Échec de la vérification des dépendances")
        return 1
    
    # Compiler l'application
    if not build_with_nuitka():
        logger.error("Échec de la compilation")
        return 1
    
    # Déterminer le nom du fichier principal
    main_file = "main_debug.py" if os.path.exists("main_debug.py") else "main_patched.py" if os.path.exists("main_patched.py") else "main.py"
    dist_dir = os.path.join("dist", f"{os.path.splitext(main_file)[0]}.dist")
    exe_name = f"{os.path.splitext(main_file)[0]}.exe"
    
    # Tester l'exécutable
    if test_executable(dist_dir, exe_name):
        logger.info("Test de l'exécutable réussi")
    else:
        logger.warning("Test de l'exécutable échoué")
    
    logger.info(f"=== Compilation terminée avec succès ===")
    logger.info(f"L'exécutable se trouve dans {os.path.join(dist_dir, exe_name)}")
    logger.info("Pour déboguer l'exécutable en cas de problème, utilisez le script debug_executable.py")
    
    return 0

if __name__ == "__main__":
    start_time = time.time()
    exit_code = main()
    elapsed_time = time.time() - start_time
    logger.info(f"Temps total d'exécution: {elapsed_time:.2f} secondes")
    sys.exit(exit_code)