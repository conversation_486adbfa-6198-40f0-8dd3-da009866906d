import os
import sys
import shutil
import subprocess
import logging
from pathlib import Path
import ctypes
import time

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("build_script")

def disable_windows_defender():
    """Désactive temporairement Windows Defender"""
    try:
        # Vérifier si nous avons les droits administrateur
        if not ctypes.windll.shell32.IsUserAnAdmin():
            logger.warning("Les droits administrateur sont nécessaires pour désactiver Windows Defender")
            return False

        # Désactiver Windows Defender
        subprocess.run(["powershell", "-Command", "Set-MpPreference -DisableRealtimeMonitoring $true"], 
                      check=True, capture_output=True)
        logger.info("Windows Defender désactivé temporairement")
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la désactivation de Windows Defender: {str(e)}")
        return False

def enable_windows_defender():
    """Réactive Windows Defender"""
    try:
        if not ctypes.windll.shell32.IsUserAnAdmin():
            return False

        subprocess.run(["powershell", "-Command", "Set-MpPreference -DisableRealtimeMonitoring $false"], 
                      check=True, capture_output=True)
        logger.info("Windows Defender réactivé")
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la réactivation de Windows Defender: {str(e)}")
        return False

def ensure_dir(directory):
    """S'assure que le répertoire existe"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"Répertoire créé: {directory}")

def copy_resources(dist_dir):
    """Copie les ressources nécessaires dans le répertoire de distribution"""
    logger.info("Copie des ressources dans le répertoire de distribution...")
    
    # Créer le répertoire config dans le dossier de distribution
    config_dir = os.path.join(dist_dir, "config")
    ensure_dir(config_dir)
    
    # Copier le fichier settings.toml
    if os.path.exists("config/settings.toml"):
        shutil.copy("config/settings.toml", os.path.join(config_dir, "settings.toml"))
        logger.info("[OK] Fichier de configuration copié")
    else:
        logger.warning("⚠ Fichier de configuration non trouvé")
        # Créer un fichier de configuration minimal
        with open(os.path.join(config_dir, "settings.toml"), "w", encoding="utf-8") as f:
            f.write("# Configuration par défaut\n[app]\nname = \"Nadjib-GSM\"\nversion = \"1.0.0\"\n")
        logger.info("✓ Fichier de configuration minimal créé")
    
    # Créer les répertoires nécessaires
    dirs_to_create = [
        os.path.join(dist_dir, "data"),
        os.path.join(dist_dir, "backups"),
        os.path.join(dist_dir, "output"),
        os.path.join(dist_dir, "logs")
    ]
    
    for directory in dirs_to_create:
        ensure_dir(directory)
        logger.info("[OK] Répertoire créé: %s", os.path.basename(directory))
    
    # Copier les ressources UI si elles existent
    ui_resources_src = "app/ui/resources"
    ui_resources_dst = os.path.join(dist_dir, "app/ui/resources")
    
    if os.path.exists(ui_resources_src) and os.path.isdir(ui_resources_src):
        if not os.path.exists(ui_resources_dst):
            os.makedirs(os.path.dirname(ui_resources_dst), exist_ok=True)
            shutil.copytree(ui_resources_src, ui_resources_dst)
            logger.info("[OK] Ressources UI copiées")
    else:
        logger.warning(f"⚠ Répertoire de ressources UI non trouvé: {ui_resources_src}")
    
    # Vérifier et copier les plugins Qt si nécessaire
    qt_plugins_dir = os.path.join(dist_dir, "PyQt6", "Qt6", "plugins")
    if not os.path.exists(qt_plugins_dir) or not os.listdir(qt_plugins_dir):
        logger.warning("⚠ Répertoire des plugins Qt manquant ou vide")
        # Essayer de trouver les plugins dans l'environnement Python
        import site
        site_packages = site.getsitepackages()
        for site_pkg in site_packages:
            src_plugins = os.path.join(site_pkg, "PyQt6", "Qt6", "plugins")
            if os.path.exists(src_plugins) and os.path.isdir(src_plugins):
                logger.info(f"✓ Plugins Qt trouvés dans: {src_plugins}")
                # Créer le répertoire parent si nécessaire
                os.makedirs(os.path.dirname(qt_plugins_dir), exist_ok=True)
                # Copier les plugins
                shutil.copytree(src_plugins, qt_plugins_dir, dirs_exist_ok=True)
                logger.info("[OK] Plugins Qt copiés manuellement")
                
                # Copier également les DLLs de Qt6 si elles existent
                qt_bin_src = os.path.join(site_pkg, "PyQt6", "Qt6", "bin")
                qt_bin_dst = os.path.join(dist_dir, "PyQt6", "Qt6", "bin")
                if os.path.exists(qt_bin_src) and os.path.isdir(qt_bin_src):
                    logger.info(f"✓ DLLs Qt trouvées dans: {qt_bin_src}")
                    os.makedirs(os.path.dirname(qt_bin_dst), exist_ok=True)
                    shutil.copytree(qt_bin_src, qt_bin_dst, dirs_exist_ok=True)
                    logger.info("[OK] DLLs Qt copiées manuellement")
                break
    
    # Copier le script de lancement
    launcher_src = "launcher.py"
    launcher_dst = os.path.join(dist_dir, "launcher.py")
    if os.path.exists(launcher_src):
        shutil.copy(launcher_src, launcher_dst)
        logger.info("[OK] Script de lancement copié")
    else:
        logger.warning("⚠ Script de lancement non trouvé, création d'un script minimal")
        # Créer un script de lancement minimal
        with open(launcher_dst, "w", encoding="utf-8") as f:
            f.write("""import os
import sys
import subprocess

def main():
    # Configurer l'environnement
    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.environ[\"PATH\"] = current_dir + os.pathsep + os.environ.get(\"PATH\", \"\")
    qt_plugin_path = os.path.join(current_dir, \"PyQt6\", \"Qt6\", \"plugins\")
    if os.path.exists(qt_plugin_path):
        os.environ[\"QT_PLUGIN_PATH\"] = qt_plugin_path
    # Lancer l'application
    executable_path = os.path.join(current_dir, \"main.exe\")
    if os.path.exists(executable_path):
        subprocess.Popen([executable_path])
        return 0
    else:
        print(f\"Erreur: L'exécutable {executable_path} n'a pas été trouvé.\")
        return 1

if __name__ == \"__main__\":
    sys.exit(main())
""")
        logger.info("✓ Script de lancement minimal créé")
    
    # Créer un fichier batch pour lancer l'application
    batch_path = os.path.join(dist_dir, "Nadjib-GSM.bat")
    with open(batch_path, "w", encoding="utf-8") as f:
        f.write("@echo off\necho Démarrage de l'application Nadjib-GSM...\npython launcher.py\npause\n")
    logger.info("[OK] Fichier batch de lancement créé")
    
    # Créer un fichier README
    readme_path = os.path.join(dist_dir, "README.txt")
    with open(readme_path, "w", encoding="utf-8") as f:
        f.write("""Nadjib-GSM - Application de Gestion

En cas de problème au démarrage:
1. Vérifiez que tous les fichiers nécessaires sont présents
2. Consultez les logs dans le dossier 'logs'

Structure des dossiers:
- config/: Fichiers de configuration
- data/: Base de données
- backups/: Sauvegardes
- output/: Fichiers générés
- logs/: Journaux d'erreurs
""")
    logger.info("[OK] Fichier README créé")
    
    logger.info("Copie des ressources terminée avec succès")

def build_with_nuitka():
    """Compile l'application avec Nuitka"""
    logger.info("Démarrage de la compilation avec Nuitka")
    
    # Désactiver Windows Defender
    defender_disabled = disable_windows_defender()
    
    try:
        # Configuration de la compilation
        nuitka_args = [
            sys.executable, "-m", "nuitka",
            "--standalone",
            "--enable-plugin=pyqt6",
            "--follow-imports",
            "--include-package=app",
            "--include-package=config",
            "--include-package=sqlalchemy",
            "--include-package=PyQt6",
            "--include-module=PyQt6.QtCharts",
            "--include-module=PyQt6.QtCore",
            "--include-module=PyQt6.QtGui",
            "--include-module=PyQt6.QtWidgets",
            "--include-module=PyQt6.QtSql",
            "--include-module=PyQt6.QtPrintSupport",
            "--include-module=PyQt6.QtSvg",
            "--include-module=PyQt6.sip",
            "--include-package=apscheduler",
            "--include-package=logging",
            "--include-package=toml",
            "--include-package=passlib",
            "--include-package=bcrypt",
            "--include-module=bcrypt",
            "--include-module=bcrypt._bcrypt",
            "--include-data-dir=app/ui/resources=app/ui/resources",
            "--include-data-dir=app/ui/theme=app/ui/theme",
            "--include-data-files=config/settings.toml=config/settings.toml",
            "--include-data-files=launcher.py=launcher.py",
            "--output-dir=dist",
            "--show-progress",
            "--assume-yes-for-downloads",
            "--jobs=4",
            "--lto=no",
            "--prefer-source-code",
            "--windows-icon-from-ico=app/ui/resources/images/app.ico",
            "--include-data-files=.venv/Lib/site-packages/escpos/capabilities.json=escpos/capabilities.json",
            #            "--include-data-files=C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/escpos/capabilities.json=escpos/capabilities.json",
            "--windows-disable-console",
            "main.py"
        ]
        
        # Exécuter la commande Nuitka
        logger.info("Exécution de la commande Nuitka: " + " ".join(nuitka_args))
        subprocess.run(nuitka_args, check=True)
        
        # Chemin vers le répertoire de distribution
        dist_dir = os.path.join("dist", "main.dist")
        
        # Vérifier si le répertoire de distribution existe
        if os.path.exists(dist_dir):
            # Copier les ressources nécessaires
            copy_resources(dist_dir)
            logger.info(f"Compilation terminée avec succès. L'exécutable se trouve dans {dist_dir}")
            return True
        else:
            logger.error(f"Le répertoire de distribution {dist_dir} n'a pas été créé")
            return False
    except subprocess.CalledProcessError as e:
        logger.error(f"Erreur lors de la compilation: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        return False
    finally:
        # Réactiver Windows Defender si on l'avait désactivé
        if defender_disabled:
            enable_windows_defender()

def check_dependencies():
    """Vérifie si toutes les dépendances nécessaires sont installées"""
    dependencies = ["nuitka", "PyQt6", "sqlalchemy", "apscheduler"]
    missing = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            logger.info(f"Dépendance {dep} trouvée")
        except ImportError:
            missing.append(dep)
            logger.error(f"Dépendance {dep} manquante")
    
    if missing:
        logger.error(f"Dépendances manquantes: {', '.join(missing)}")
        logger.error("Veuillez installer les dépendances manquantes avec 'pip install <nom_dependance>'")
        return False
    
    return True

def test_executable(dist_dir):
    """Teste l'exécutable généré pour vérifier s'il fonctionne correctement"""
    executable_path = os.path.join(dist_dir, "main.exe")
    
    if not os.path.exists(executable_path):
        logger.error(f"L'exécutable {executable_path} n'existe pas")
        return False
    
    logger.info(f"Test de l'exécutable {executable_path}")
    try:
        # Exécuter avec un timeout court pour vérifier juste le démarrage
        result = subprocess.run([executable_path, "--test-mode"], 
                              timeout=2, 
                              stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE,
                              check=False)
        
        # Même si le processus est interrompu par le timeout, c'est bon signe
        # car cela signifie que l'application a démarré
        logger.info("L'exécutable semble démarrer correctement")
        return True
    except subprocess.TimeoutExpired:
        # C'est normal si l'application démarre correctement
        logger.info("L'exécutable a démarré et fonctionne (timeout normal)")
        return True
    except Exception as e:
        logger.error(f"Erreur lors du test de l'exécutable: {str(e)}")
        return False

def check_paths():
    """Vérifie automatiquement la présence de tous les chemins utilisés dans la commande Nuitka et dans copy_resources."""
    logger.info("Vérification des chemins...")
    paths_to_check = [
        "app/ui/resources",
        "app/ui/theme",
        "config/settings.toml",
        "launcher.py",
        ".venv/Lib/site-packages/escpos/capabilities.json"
    ]
    missing = []
    for path in paths_to_check:
        if not os.path.exists(path):
            missing.append(path)
            logger.error(f"Chemin manquant: {path}")
    if missing:
        logger.error("Certains chemins sont manquants. Veuillez les créer ou corriger les chemins dans la commande Nuitka.")
        return False
    logger.info("Tous les chemins sont présents.")
    return True

def main():
    """Fonction principale"""
    logger.info("=== Début du processus de compilation ===")
    
    # Vérifier si Nuitka est installé
    try:
        subprocess.run([sys.executable, "-m", "nuitka", "--version"], 
                       check=True, 
                       stdout=subprocess.PIPE, 
                       stderr=subprocess.PIPE)
        logger.info("Nuitka est installé")
    except subprocess.CalledProcessError:
        logger.error("Nuitka n'est pas installé. Veuillez l'installer avec 'pip install nuitka'.")
        return False
    
    # Vérifier les autres dépendances
    if not check_dependencies():
        return False
    
    # Vérifier les chemins
    if not check_paths():
        return False
    
    # Compiler l'application
    success = build_with_nuitka()
    
    if success:
        logger.info("=== Compilation terminée avec succès ===")
        
        # Tester l'exécutable
        dist_dir = os.path.join("dist", "main.dist")
        if test_executable(dist_dir):
            logger.info("=== Test de l'exécutable réussi ===")
        else:
            logger.error("=== Échec du test de l'exécutable ===")
            logger.info("Conseil: Vérifiez que toutes les dépendances et ressources sont correctement incluses")
    else:
        logger.error("=== Échec de la compilation ===")
    
    return success

if __name__ == "__main__":
    main()