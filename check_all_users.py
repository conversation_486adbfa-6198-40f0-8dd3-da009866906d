import sqlite3

# Connexion directe à la base de données
conn = sqlite3.connect('app.db')
cursor = conn.cursor()

print("=== Vérification de tous les utilisateurs ===")

# Récupérer tous les utilisateurs
cursor.execute("""
    SELECT id, email, full_name, status, is_active, last_login, 
           failed_login_attempts, created_at, updated_at
    FROM users 
    ORDER BY id
""")
all_users = cursor.fetchall()

print(f"Nombre total d'utilisateurs: {len(all_users)}")
print()

for user in all_users:
    print(f"ID: {user[0]}")
    print(f"  Email: {user[1]}")
    print(f"  Nom: {user[2]}")
    print(f"  Statut: {user[3]}")
    print(f"  Actif: {user[4]}")
    print(f"  Dernière connexion: {user[5]}")
    print(f"  Tentatives échouées: {user[6]}")
    print(f"  Créé le: {user[7]}")
    print(f"  Mis à jour le: {user[8]}")
    
    # Vérifier les rôles de cet utilisateur
    cursor.execute("""
        SELECT r.id, r.name, r.description 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    """, (user[0],))
    user_roles = cursor.fetchall()
    
    print(f"  Rôles ({len(user_roles)}):")
    for role in user_roles:
        print(f"    - {role[1]} (ID: {role[0]}): {role[2]}")
    
    print("-" * 50)

# Vérifier les rôles et permissions
print("\n=== Rôles disponibles ===")
cursor.execute("SELECT id, name, description FROM roles ORDER BY id")
roles = cursor.fetchall()
for role in roles:
    print(f"ID: {role[0]}, Nom: {role[1]}, Description: {role[2]}")

print("\n=== Permissions disponibles ===")
cursor.execute("SELECT id, code, name, category FROM permissions ORDER BY id")
permissions = cursor.fetchall()
for perm in permissions:
    print(f"ID: {perm[0]}, Code: {perm[1]}, Nom: {perm[2]}, Catégorie: {perm[3]}")

print("\n=== Associations rôle-permission ===")
cursor.execute("""
    SELECT r.name, p.code 
    FROM roles r
    JOIN role_permission rp ON r.id = rp.role_id
    JOIN permissions p ON p.id = rp.permission_id
    ORDER BY r.name, p.code
""")
role_perms = cursor.fetchall()
current_role = None
for rp in role_perms:
    if rp[0] != current_role:
        print(f"\nRôle: {rp[0]}")
        current_role = rp[0]
    print(f"  - {rp[1]}")

conn.close()
print("\n=== Vérification terminée ===")
