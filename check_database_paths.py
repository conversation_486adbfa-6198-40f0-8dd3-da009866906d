import os
import sqlite3
from pathlib import Path

print("=== Vérification des chemins de base de données ===")

# Vérifier les différents chemins possibles
paths_to_check = [
    "app.db",
    "data/app.db",
    "./app.db",
    "./data/app.db"
]

for path in paths_to_check:
    if os.path.exists(path):
        print(f"✅ Trouvé: {path}")
        
        # Vérifier le contenu
        try:
            conn = sqlite3.connect(path)
            cursor = conn.cursor()
            
            # Compter les utilisateurs
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            # Lister les utilisateurs
            cursor.execute("SELECT id, email, full_name FROM users")
            users = cursor.fetchall()
            
            print(f"  Utilisateurs ({user_count}):")
            for user in users:
                print(f"    ID: {user[0]}, Email: {user[1]}, Nom: {user[2]}")
            
            conn.close()
            
        except Exception as e:
            print(f"  ❌ Erreur lors de la lecture: {e}")
    else:
        print(f"❌ Non trouvé: {path}")

print("\n=== Configuration SQLAlchemy ===")

# Vérifier la configuration SQLAlchemy
try:
    import sys
    sys.path.append('.')
    from app.utils.config import get_db_path
    
    sqlalchemy_path = get_db_path()
    print(f"Chemin SQLAlchemy: {sqlalchemy_path}")
    
    if os.path.exists(sqlalchemy_path):
        print("✅ Le fichier SQLAlchemy existe")
        
        # Vérifier le contenu
        try:
            conn = sqlite3.connect(sqlalchemy_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT id, email, full_name FROM users")
            users = cursor.fetchall()
            
            print(f"  Utilisateurs SQLAlchemy ({user_count}):")
            for user in users:
                print(f"    ID: {user[0]}, Email: {user[1]}, Nom: {user[2]}")
            
            conn.close()
            
        except Exception as e:
            print(f"  ❌ Erreur lors de la lecture SQLAlchemy: {e}")
    else:
        print("❌ Le fichier SQLAlchemy n'existe pas")
        
except Exception as e:
    print(f"❌ Erreur lors de la vérification SQLAlchemy: {e}")

print("\n=== Recommandation ===")
print("Si les bases de données sont différentes, nous devons synchroniser les données.")
