#!/usr/bin/env python3
"""
Script pour vérifier l'état des colonnes dans la base de données
"""
import sqlite3
import os

def check_database_columns():
    """Vérifie les colonnes liées aux prix dans les tables principales"""
    db_path = os.path.join('data', 'app.db')

    print(f"Vérification de la base de données: {db_path}")
    print(f"Fichier existe: {os.path.exists(db_path)}")

    if not os.path.exists(db_path):
        print(f"Base de données non trouvée: {db_path}")
        return

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Vérifier quelles tables existent
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    existing_tables = [row[0] for row in cursor.fetchall()]
    print(f"Tables existantes: {existing_tables}")

    tables = ['purchase_order_items', 'supplier_quotes', 'sale_items', 'quote_items', 'used_parts']

    for table in tables:
        print(f'\n=== Table: {table} ===')
        if table not in existing_tables:
            print(f'  Table {table} n\'existe pas')
            continue

        try:
            cursor.execute(f'PRAGMA table_info({table})')
            columns = cursor.fetchall()

            print(f'  Toutes les colonnes:')
            for col in columns:
                print(f'    {col[1]} ({col[2]})')

            price_columns = []
            for col in columns:
                if 'price' in col[1].lower():
                    price_columns.append(f'  {col[1]} ({col[2]})')

            print(f'  Colonnes de prix:')
            if price_columns:
                print('\n'.join(price_columns))
            else:
                print('    Aucune colonne de prix trouvée')

        except Exception as e:
            print(f'  Erreur: {e}')

    conn.close()

if __name__ == "__main__":
    check_database_columns()
