#!/usr/bin/env python3
"""
Script pour vérifier la structure de la base de données
"""

import sqlite3
import sys
import os

def check_inventory_items_schema():
    """Vérifie la structure de la table inventory_items"""
    db_path = os.path.join("data", "app.db")
    
    if not os.path.exists(db_path):
        print(f"Base de données non trouvée à: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Obtenir les informations sur la table inventory_items
        cursor.execute("PRAGMA table_info(inventory_items)")
        columns = cursor.fetchall()
        
        print("Structure de la table inventory_items:")
        print("-" * 50)
        for col in columns:
            col_id, name, type_, not_null, default_val, pk = col
            print(f"{name:20} {type_:15} {'NOT NULL' if not_null else 'NULL':10} {'PK' if pk else ''}")
        
        print(f"\nNombre de colonnes: {len(columns)}")
        print(f"Colonnes: {[col[1] for col in columns]}")
        
        # Vérifier si la colonne condition existe
        column_names = [col[1] for col in columns]
        if 'condition' in column_names:
            print("\n✓ La colonne 'condition' existe dans la table")
        else:
            print("\n✗ La colonne 'condition' N'EXISTE PAS dans la table")
            print("Ceci est la cause du problème de lancement")
        
        conn.close()
        
    except Exception as e:
        print(f"Erreur lors de la vérification: {e}")

if __name__ == "__main__":
    check_inventory_items_schema()