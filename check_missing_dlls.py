import os
import sys
import subprocess
import re
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('missing_dlls.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("check_dlls")

def find_executable(dist_dir):
    """Trouve l'exécutable principal dans le répertoire de distribution"""
    logger.info(f"Recherche de l'exécutable dans {dist_dir}")
    
    if not os.path.exists(dist_dir) or not os.path.isdir(dist_dir):
        logger.error(f"Le répertoire {dist_dir} n'existe pas")
        return None
    
    # Chercher les fichiers .exe dans le répertoire
    exe_files = [f for f in os.listdir(dist_dir) if f.endswith('.exe')]
    
    if not exe_files:
        logger.error(f"Aucun fichier .exe trouvé dans {dist_dir}")
        return None
    
    # Prioriser les exécutables qui correspondent aux noms de fichiers principaux
    main_names = ['main.exe', 'main_debug.exe', 'main_patched.exe']
    for name in main_names:
        if name in exe_files:
            logger.info(f"Exécutable trouvé: {name}")
            return os.path.join(dist_dir, name)
    
    # Sinon, prendre le premier exécutable trouvé
    logger.info(f"Exécutable trouvé: {exe_files[0]}")
    return os.path.join(dist_dir, exe_files[0])

def check_missing_dlls(executable_path):
    """Vérifie les DLL manquantes pour un exécutable"""
    logger.info(f"Vérification des DLL manquantes pour {executable_path}")
    
    if not os.path.exists(executable_path):
        logger.error(f"L'exécutable {executable_path} n'existe pas")
        return []
    
    try:
        # Utiliser Dependency Walker (si disponible) ou dumpbin.exe (inclus avec Visual Studio)
        # Essayer d'abord avec dumpbin
        try:
            # Chercher dumpbin.exe dans les chemins courants de Visual Studio
            dumpbin_paths = [
                r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\dumpbin.exe",
                r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\dumpbin.exe",
                r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\dumpbin.exe",
                # Ajouter d'autres chemins potentiels ici
            ]
            
            dumpbin_exe = None
            for path in dumpbin_paths:
                if os.path.exists(path):
                    dumpbin_exe = path
                    break
            
            if dumpbin_exe:
                logger.info(f"Utilisation de dumpbin.exe: {dumpbin_exe}")
                result = subprocess.run(
                    [dumpbin_exe, "/DEPENDENTS", executable_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                # Analyser la sortie pour trouver les dépendances
                dependencies = []
                for line in result.stdout.splitlines():
                    line = line.strip()
                    if line.endswith(".dll"):
                        dependencies.append(line)
                
                logger.info(f"Dépendances trouvées avec dumpbin: {dependencies}")
                return dependencies
            else:
                logger.warning("dumpbin.exe non trouvé")
        except Exception as e:
            logger.warning(f"Erreur lors de l'utilisation de dumpbin: {str(e)}")
        
        # Si dumpbin n'est pas disponible, utiliser une approche alternative avec PowerShell
        logger.info("Utilisation de PowerShell pour vérifier les dépendances")
        powershell_cmd = f"Get-Item '{executable_path}' | ForEach-Object {{$_.VersionInfo.DependentAssemblies}}"
        result = subprocess.run(
            ["powershell", "-Command", powershell_cmd],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Analyser la sortie pour trouver les dépendances
        dependencies = []
        for line in result.stdout.splitlines():
            line = line.strip()
            if ".dll" in line.lower():
                # Extraire le nom de la DLL
                match = re.search(r'([\w.-]+\.dll)', line, re.IGNORECASE)
                if match:
                    dependencies.append(match.group(1))
        
        logger.info(f"Dépendances trouvées avec PowerShell: {dependencies}")
        return dependencies
    
    except Exception as e:
        logger.error(f"Erreur lors de la vérification des DLL: {str(e)}")
        return []

def check_dll_exists(dll_name, search_paths):
    """Vérifie si une DLL existe dans les chemins de recherche"""
    for path in search_paths:
        dll_path = os.path.join(path, dll_name)
        if os.path.exists(dll_path):
            return True, dll_path
    return False, None

def main():
    logger.info("=== Début de la vérification des DLL manquantes ===")
    
    # Déterminer le répertoire de distribution
    dist_dir = None
    if len(sys.argv) > 1:
        dist_dir = sys.argv[1]
    else:
        # Chercher le répertoire dist/main.dist par défaut
        default_dirs = [
            "dist/main.dist",
            "dist/main_debug.dist",
            "dist/main_patched.dist"
        ]
        for dir_path in default_dirs:
            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                dist_dir = dir_path
                break
    
    if not dist_dir:
        logger.error("Répertoire de distribution non trouvé")
        print("Erreur: Répertoire de distribution non trouvé.")
        print("Usage: python check_missing_dlls.py [chemin_du_répertoire_dist]")
        return 1
    
    # Trouver l'exécutable
    executable_path = find_executable(dist_dir)
    if not executable_path:
        logger.error("Exécutable non trouvé")
        return 1
    
    # Vérifier les DLL manquantes
    dependencies = check_missing_dlls(executable_path)
    
    if not dependencies:
        logger.warning("Aucune dépendance trouvée, cela peut indiquer un problème avec la méthode de détection")
        print("Aucune dépendance trouvée. Cela peut indiquer un problème avec la méthode de détection.")
        return 1
    
    # Chemins de recherche pour les DLL
    search_paths = [
        dist_dir,
        os.path.join(dist_dir, "PyQt6"),
        os.path.join(dist_dir, "PySide6"),
        os.path.join(dist_dir, "lib"),
        os.path.join(os.environ.get("WINDIR", "C:\\Windows"), "System32"),
        os.path.join(os.environ.get("WINDIR", "C:\\Windows"), "SysWOW64")
    ]
    
    # Vérifier chaque dépendance
    missing_dlls = []
    for dll in dependencies:
        exists, path = check_dll_exists(dll, search_paths)
        if exists:
            logger.info(f"✓ {dll} trouvé: {path}")
        else:
            logger.warning(f"✗ {dll} manquant")
            missing_dlls.append(dll)
    
    # Afficher le résultat
    if missing_dlls:
        logger.error(f"DLL manquantes ({len(missing_dlls)}): {', '.join(missing_dlls)}")
        print(f"\nDLL manquantes ({len(missing_dlls)}):")
        for dll in missing_dlls:
            print(f"  - {dll}")
        print("\nSuggestions:")
        print("1. Installez les Visual C++ Redistributable les plus récents")
        print("2. Assurez-vous que PyQt6 est correctement installé")
        print("3. Vérifiez que toutes les dépendances sont incluses dans le build")
        print("4. Utilisez l'option --enable-plugin=pyqt6 lors de la compilation avec Nuitka")
    else:
        logger.info("Toutes les DLL requises sont présentes")
        print("\nToutes les DLL requises sont présentes.")
    
    logger.info("=== Fin de la vérification des DLL manquantes ===")
    return 0

if __name__ == "__main__":
    sys.exit(main())