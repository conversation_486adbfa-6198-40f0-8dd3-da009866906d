import os
import sys
import logging
import importlib
from datetime import datetime
import traceback

# Configuration du logging
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = os.path.join(log_dir, f'missing_modules_check_{timestamp}.log')

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("missing_modules_check")

def check_module(module_name):
    """Vérifie si un module peut être importé"""
    try:
        importlib.import_module(module_name)
        logger.info(f"✓ Module {module_name} importé avec succès")
        return True
    except ImportError as e:
        logger.error(f"✗ Erreur d'importation du module {module_name}: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"✗ Erreur inattendue lors de l'importation du module {module_name}: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def check_pyqt_charts():
    """Vérifie spécifiquement le module PyQt6.QtCharts"""
    logger.info("Vérification du module PyQt6.QtCharts...")
    
    # Vérifier si PyQt6 est installé
    if not check_module("PyQt6"):
        logger.error("PyQt6 n'est pas installé, impossible de vérifier PyQt6.QtCharts")
        return False
    
    # Vérifier si PyQt6.QtCharts est installé
    if not check_module("PyQt6.QtCharts"):
        logger.error("PyQt6.QtCharts n'est pas installé")
        
        # Vérifier si le package PyQt6-Charts est installé
        try:
            import pkg_resources
            packages = [p.project_name for p in pkg_resources.working_set]
            if "PyQt6-Charts" in packages:
                logger.info("Le package PyQt6-Charts est installé, mais le module PyQt6.QtCharts n'est pas accessible")
            else:
                logger.error("Le package PyQt6-Charts n'est pas installé")
        except Exception as e:
            logger.error(f"Erreur lors de la vérification des packages installés: {str(e)}")
        
        return False
    
    # Vérifier si les classes nécessaires sont disponibles
    try:
        from PyQt6.QtCharts import QChart, QChartView
        logger.info("✓ Classes QChart et QChartView importées avec succès")
        return True
    except ImportError as e:
        logger.error(f"✗ Erreur d'importation des classes de PyQt6.QtCharts: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"✗ Erreur inattendue lors de l'importation des classes de PyQt6.QtCharts: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def check_essential_modules():
    """Vérifie les modules essentiels pour l'application"""
    logger.info("Vérification des modules essentiels...")
    
    essential_modules = [
        "PyQt6",
        "PyQt6.QtCore",
        "PyQt6.QtGui",
        "PyQt6.QtWidgets",
        "PyQt6.QtCharts",
        "sqlalchemy",
        "apscheduler",
        "passlib",
        "toml"
    ]
    
    results = {}
    for module in essential_modules:
        results[module] = check_module(module)
    
    # Afficher un résumé
    logger.info("\nRésumé des vérifications:")
    all_ok = True
    for module, result in results.items():
        status = "✓" if result else "✗"
        logger.info(f"{status} {module}")
        if not result:
            all_ok = False
    
    return all_ok

def check_pyqt_installation():
    """Vérifie l'installation de PyQt6"""
    logger.info("Vérification de l'installation de PyQt6...")
    
    try:
        import PyQt6
        logger.info(f"Version de PyQt6: {PyQt6.__version__ if hasattr(PyQt6, '__version__') else 'Inconnue'}")
        
        # Vérifier le chemin d'installation
        logger.info(f"Chemin d'installation de PyQt6: {PyQt6.__file__}")
        
        # Vérifier les sous-modules disponibles
        from PyQt6 import QtCore, QtGui, QtWidgets
        logger.info("Sous-modules de base importés avec succès")
        
        # Vérifier si QtCharts est disponible
        try:
            from PyQt6 import QtCharts
            logger.info("✓ Sous-module QtCharts importé avec succès")
        except ImportError as e:
            logger.error(f"✗ Erreur d'importation du sous-module QtCharts: {str(e)}")
            return False
        
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la vérification de l'installation de PyQt6: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def suggest_fixes():
    """Suggère des corrections pour les problèmes courants"""
    logger.info("\nSuggestions de corrections:")
    
    # Vérifier si PyQt6.QtCharts est installé
    try:
        import PyQt6.QtCharts
        logger.info("PyQt6.QtCharts est correctement installé")
    except ImportError:
        logger.info("1. Installer PyQt6.QtCharts: pip install PyQt6-Charts")
        logger.info("2. Vérifier que la version de PyQt6-Charts correspond à la version de PyQt6")
        logger.info("3. Recompiler l'application avec l'option --include-module=PyQt6.QtCharts")
    
    # Suggestions générales
    logger.info("Suggestions générales:")
    logger.info("1. Vérifier que toutes les dépendances sont installées: pip install -r requirements.txt")
    logger.info("2. Recompiler l'application avec le script build_with_nuitka_debug.py")
    logger.info("3. Vérifier les variables d'environnement: QT_DEBUG_PLUGINS=1 QT_LOGGING_RULES=qt.qpa.*=true")

def main():
    """Fonction principale"""
    logger.info("=== DÉBUT DE LA VÉRIFICATION DES MODULES MANQUANTS ===")
    
    # Afficher les informations système
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Executable: {sys.executable}")
    logger.info(f"Répertoire courant: {os.getcwd()}")
    
    # Vérifier l'installation de PyQt6
    check_pyqt_installation()
    
    # Vérifier spécifiquement PyQt6.QtCharts
    check_pyqt_charts()
    
    # Vérifier tous les modules essentiels
    all_ok = check_essential_modules()
    
    # Suggérer des corrections
    suggest_fixes()
    
    logger.info("=== FIN DE LA VÉRIFICATION DES MODULES MANQUANTS ===")
    
    if all_ok:
        logger.info("Tous les modules essentiels sont disponibles")
        return 0
    else:
        logger.error("Certains modules essentiels sont manquants")
        return 1

if __name__ == "__main__":
    sys.exit(main())