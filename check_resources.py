import os
import sys
import logging
import traceback
from datetime import datetime
import shutil

# Configuration du logging
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = os.path.join(log_dir, f'resources_check_{timestamp}.log')

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("resources_check")

def find_executable_dir():
    """Trouve le répertoire de l'exécutable compilé"""
    logger.info("Recherche du répertoire de l'exécutable compilé...")
    
    # Chemins possibles pour le répertoire de distribution
    possible_dirs = [
        "dist/main.dist",
        "dist/main_patched.dist",
        "dist/main_debug.dist"
    ]
    
    for path in possible_dirs:
        if os.path.exists(path) and os.path.isdir(path):
            logger.info(f"Répertoire de distribution trouvé: {path}")
            return path
    
    logger.error("Aucun répertoire de distribution trouvé")
    return None

def check_critical_directories(dist_dir):
    """Vérifie les répertoires critiques dans le répertoire de distribution"""
    logger.info("Vérification des répertoires critiques...")
    
    critical_dirs = [
        "config",
        "data",
        "backups",
        "output",
        "app",
        "app/ui",
        "app/ui/resources",
        "PyQt6",
        "PyQt6/Qt6",
        "PyQt6/Qt6/plugins",
        "PyQt6/Qt6/plugins/platforms"
    ]
    
    missing_dirs = []
    for dir_path in critical_dirs:
        full_path = os.path.join(dist_dir, dir_path)
        if os.path.exists(full_path) and os.path.isdir(full_path):
            # Vérifier si le répertoire est vide
            contents = os.listdir(full_path)
            if contents:
                logger.info(f"✓ {dir_path}: Existe ({len(contents)} éléments)")
            else:
                logger.warning(f"⚠ {dir_path}: Existe mais est vide")
                missing_dirs.append(dir_path)
        else:
            logger.error(f"✗ {dir_path}: N'existe pas")
            missing_dirs.append(dir_path)
    
    return missing_dirs

def check_critical_files(dist_dir):
    """Vérifie les fichiers critiques dans le répertoire de distribution"""
    logger.info("Vérification des fichiers critiques...")
    
    critical_files = [
        "config/settings.toml",
        "PyQt6/Qt6/plugins/platforms/qwindows.dll"
    ]
    
    missing_files = []
    for file_path in critical_files:
        full_path = os.path.join(dist_dir, file_path)
        if os.path.exists(full_path) and os.path.isfile(full_path):
            size = os.path.getsize(full_path)
            logger.info(f"✓ {file_path}: Existe ({size} octets)")
        else:
            logger.error(f"✗ {file_path}: N'existe pas")
            missing_files.append(file_path)
    
    return missing_files

def check_qt_plugins(dist_dir):
    """Vérifie les plugins Qt dans le répertoire de distribution"""
    logger.info("Vérification des plugins Qt...")
    
    # Répertoire des plugins Qt
    plugins_dir = os.path.join(dist_dir, "PyQt6", "Qt6", "plugins")
    if not os.path.exists(plugins_dir) or not os.path.isdir(plugins_dir):
        logger.error(f"✗ Répertoire des plugins Qt non trouvé: {plugins_dir}")
        return False
    
    # Vérifier les sous-répertoires des plugins
    plugin_types = [
        "platforms",
        "styles",
        "imageformats",
        "sqldrivers"
    ]
    
    missing_plugins = []
    for plugin_type in plugin_types:
        plugin_dir = os.path.join(plugins_dir, plugin_type)
        if os.path.exists(plugin_dir) and os.path.isdir(plugin_dir):
            plugins = os.listdir(plugin_dir)
            if plugins:
                logger.info(f"✓ Plugins {plugin_type}: {', '.join(plugins)}")
            else:
                logger.warning(f"⚠ Répertoire des plugins {plugin_type} est vide")
                missing_plugins.append(plugin_type)
        else:
            logger.warning(f"⚠ Répertoire des plugins {plugin_type} non trouvé")
            missing_plugins.append(plugin_type)
    
    # Vérifier spécifiquement le plugin qwindows.dll (crucial pour Windows)
    qwindows_path = os.path.join(plugins_dir, "platforms", "qwindows.dll")
    if os.path.exists(qwindows_path) and os.path.isfile(qwindows_path):
        logger.info(f"✓ Plugin qwindows.dll trouvé")
    else:
        logger.error(f"✗ Plugin qwindows.dll non trouvé")
        missing_plugins.append("qwindows.dll")
    
    return len(missing_plugins) == 0

def check_ui_resources(dist_dir):
    """Vérifie les ressources UI dans le répertoire de distribution"""
    logger.info("Vérification des ressources UI...")
    
    # Répertoire des ressources UI
    resources_dir = os.path.join(dist_dir, "app", "ui", "resources")
    if not os.path.exists(resources_dir) or not os.path.isdir(resources_dir):
        logger.error(f"✗ Répertoire des ressources UI non trouvé: {resources_dir}")
        return False
    
    # Vérifier le contenu du répertoire des ressources
    resources = os.listdir(resources_dir)
    if resources:
        logger.info(f"✓ Ressources UI trouvées ({len(resources)} fichiers)")
        
        # Vérifier les sous-répertoires courants
        subdirs = ["icons", "images", "styles"]
        for subdir in subdirs:
            subdir_path = os.path.join(resources_dir, subdir)
            if os.path.exists(subdir_path) and os.path.isdir(subdir_path):
                files = os.listdir(subdir_path)
                logger.info(f"✓ Sous-répertoire {subdir} trouvé ({len(files)} fichiers)")
            else:
                logger.info(f"ℹ Sous-répertoire {subdir} non trouvé (peut ne pas être nécessaire)")
        
        return True
    else:
        logger.warning(f"⚠ Répertoire des ressources UI est vide")
        return False

def fix_missing_resources(dist_dir, source_dir="."):
    """Tente de corriger les ressources manquantes"""
    logger.info("Tentative de correction des ressources manquantes...")
    
    # Vérifier les répertoires critiques
    missing_dirs = check_critical_directories(dist_dir)
    
    # Créer les répertoires manquants
    for dir_path in missing_dirs:
        full_path = os.path.join(dist_dir, dir_path)
        try:
            os.makedirs(full_path, exist_ok=True)
            logger.info(f"✓ Répertoire créé: {dir_path}")
        except Exception as e:
            logger.error(f"✗ Erreur lors de la création du répertoire {dir_path}: {str(e)}")
    
    # Vérifier les fichiers critiques
    missing_files = check_critical_files(dist_dir)
    
    # Copier les fichiers manquants depuis le répertoire source
    for file_path in missing_files:
        source_file = os.path.join(source_dir, file_path)
        dest_file = os.path.join(dist_dir, file_path)
        
        if os.path.exists(source_file) and os.path.isfile(source_file):
            try:
                # Créer le répertoire parent si nécessaire
                os.makedirs(os.path.dirname(dest_file), exist_ok=True)
                
                # Copier le fichier
                shutil.copy2(source_file, dest_file)
                logger.info(f"✓ Fichier copié: {file_path}")
            except Exception as e:
                logger.error(f"✗ Erreur lors de la copie du fichier {file_path}: {str(e)}")
        else:
            logger.warning(f"⚠ Fichier source non trouvé: {source_file}")
    
    # Vérifier les ressources UI
    resources_dir_src = os.path.join(source_dir, "app", "ui", "resources")
    resources_dir_dst = os.path.join(dist_dir, "app", "ui", "resources")
    
    if os.path.exists(resources_dir_src) and os.path.isdir(resources_dir_src):
        try:
            # Supprimer le répertoire de destination s'il existe
            if os.path.exists(resources_dir_dst):
                shutil.rmtree(resources_dir_dst)
            
            # Copier le répertoire des ressources
            shutil.copytree(resources_dir_src, resources_dir_dst)
            logger.info(f"✓ Répertoire des ressources UI copié")
        except Exception as e:
            logger.error(f"✗ Erreur lors de la copie des ressources UI: {str(e)}")
    else:
        logger.warning(f"⚠ Répertoire des ressources UI source non trouvé: {resources_dir_src}")
    
    # Vérifier à nouveau après les corrections
    logger.info("Vérification après corrections...")
    missing_dirs_after = check_critical_directories(dist_dir)
    missing_files_after = check_critical_files(dist_dir)
    
    if not missing_dirs_after and not missing_files_after:
        logger.info("✓ Toutes les ressources ont été corrigées avec succès")
        return True
    else:
        logger.warning(f"⚠ Certaines ressources n'ont pas pu être corrigées")
        if missing_dirs_after:
            logger.warning(f"⚠ Répertoires toujours manquants: {', '.join(missing_dirs_after)}")
        if missing_files_after:
            logger.warning(f"⚠ Fichiers toujours manquants: {', '.join(missing_files_after)}")
        return False

def main():
    logger.info("=== DÉBUT DE LA VÉRIFICATION DES RESSOURCES ===")
    
    # Trouver le répertoire de l'exécutable
    dist_dir = find_executable_dir()
    if not dist_dir:
        print("\nERREUR: Impossible de trouver le répertoire de distribution.")
        print("Veuillez d'abord compiler l'application avec build_with_nuitka.py ou build_with_nuitka_debug.py.")
        return 1
    
    # Vérifier les répertoires critiques
    missing_dirs = check_critical_directories(dist_dir)
    
    # Vérifier les fichiers critiques
    missing_files = check_critical_files(dist_dir)
    
    # Vérifier les plugins Qt
    qt_plugins_ok = check_qt_plugins(dist_dir)
    
    # Vérifier les ressources UI
    ui_resources_ok = check_ui_resources(dist_dir)
    
    # Afficher le résultat
    print("\n=== RÉSULTAT DE LA VÉRIFICATION DES RESSOURCES ===")
    print(f"Répertoire vérifié: {dist_dir}")
    
    if not missing_dirs and not missing_files and qt_plugins_ok and ui_resources_ok:
        print("\n✓ Toutes les ressources nécessaires sont présentes.")
        logger.info("✓ Toutes les ressources nécessaires sont présentes")
    else:
        print("\n⚠ Des ressources sont manquantes ou incorrectes:")
        
        if missing_dirs:
            print(f"  - Répertoires manquants: {', '.join(missing_dirs)}")
        
        if missing_files:
            print(f"  - Fichiers manquants: {', '.join(missing_files)}")
        
        if not qt_plugins_ok:
            print(f"  - Problèmes avec les plugins Qt")
        
        if not ui_resources_ok:
            print(f"  - Problèmes avec les ressources UI")
        
        # Proposer de corriger les problèmes
        print("\nVoulez-vous tenter de corriger ces problèmes? (o/n)")
        choice = input().lower()
        
        if choice == 'o' or choice == 'oui':
            logger.info("Tentative de correction des ressources manquantes...")
            if fix_missing_resources(dist_dir):
                print("\n✓ Les ressources ont été corrigées avec succès.")
                print("  Veuillez tester à nouveau l'exécutable.")
            else:
                print("\n⚠ Certaines ressources n'ont pas pu être corrigées.")
                print("  Veuillez recompiler l'application avec build_with_nuitka_debug.py.")
    
    print(f"\nLog complet disponible dans: {log_file}")
    logger.info("=== FIN DE LA VÉRIFICATION DES RESSOURCES ===")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        logger.critical(f"Erreur fatale: {str(e)}")
        logger.critical(traceback.format_exc())
        print(f"\nERREUR FATALE: {str(e)}")
        print(f"Log disponible dans: {log_file}")
        sys.exit(1)