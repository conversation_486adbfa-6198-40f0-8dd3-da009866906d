"""
Module de configuration de l'application.
Ce module fournit les paramètres de configuration pour l'application.
"""
import os
import sys
from pydantic import BaseModel
from functools import lru_cache
from typing import Optional
from app.utils.toml_parser import load_from_file

# Déterminer le chemin du fichier de configuration
if getattr(sys, 'frozen', False):
    # Si l'application est compilée avec PyInstaller
    base_dir = sys._MEIPASS
    config_path = os.path.join(base_dir, 'config', 'settings.toml')
else:
    # Si l'application est exécutée normalement
    config_path = os.path.join(os.path.dirname(__file__), "settings.toml")

# Chargement de la configuration
toml_config = load_from_file(config_path)

class Settings(BaseModel):
    """Configuration de l'application"""

    # Application
    app_name: str = toml_config["application"]["name"]
    app_version: str = toml_config["application"]["version"]
    debug: bool = toml_config["application"]["debug"]

    # Base de données
    database_url: str = toml_config["database"]["url"]
    pool_size: int = toml_config["database"]["pool_size"]
    max_overflow: int = toml_config["database"]["max_overflow"]

    # Interface utilisateur
    theme: str = toml_config["ui"]["theme"]
    language: str = toml_config["ui"]["language"]
    font_size: int = toml_config["ui"]["font_size"]

    # API
    api_host: str = toml_config["api"]["host"]
    api_port: int = toml_config["api"]["port"]
    api_debug: bool = toml_config["api"]["debug"]

    # Logging
    log_level: str = toml_config["logging"]["level"]
    log_file: str = toml_config["logging"]["file"]

    # Sécurité
    token_expiration: int = toml_config["security"]["token_expiration"]
    encryption_key: str = toml_config["security"]["encryption_key"]

    # Email (valeurs par défaut)
    email_enabled: bool = False
    email_sender: str = "<EMAIL>"
    smtp_server: str = "smtp.example.com"
    smtp_port: int = 587
    smtp_use_tls: bool = True
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None

    # Notifications
    notification_cleanup_interval_hours: int = 24
    notification_default_expiry_days: int = 30

    # Treasury
    default_repair_cash_register_id: Optional[int] = toml_config.get("treasury", {}).get("default_repair_cash_register_id")

@lru_cache()
def get_settings() -> Settings:
    """Récupère les paramètres de l'application"""
    return Settings()
