[application]
name = "Logiciel de Gestion"
version = "1.0.0"
debug = false

[database]
url = "sqlite:///app.db"
# Pour PostgreSQL : url = "postgresql://user:password@localhost/dbname"
pool_size = 5
max_overflow = 10

[ui]
theme = "light"
language = "fr"
font_size = 12

[api]
host = "localhost"
port = 8000
debug = false

[logging]
level = "INFO"
file = "app.log"

[security]
token_expiration = 3600  # secondes
encryption_key = "your-secret-key"

[treasury]
# ID de la caisse de réparation par défaut (optionnel)
# Si défini, sera utilisé pour les paiements de réparations lorsqu'aucun cash_register_id n'est fourni
default_repair_cash_register_id = 1