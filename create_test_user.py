import sqlite3
from datetime import datetime
import hashlib

# Connexion directe à la base de données
conn = sqlite3.connect('app.db')
cursor = conn.cursor()

print("=== Création d'un utilisateur de test ===")

# Vérifier les tables existantes
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print(f"Tables trouvées: {[table[0] for table in tables]}")

# Vérifier la structure de la table users
try:
    cursor.execute("PRAGMA table_info(users)")
    columns = cursor.fetchall()
    print(f"\nStructure de la table users:")
    for col in columns:
        print(f"  {col[1]} ({col[2]}) - Nullable: {not col[3]}")
except Exception as e:
    print(f"Erreur lors de la vérification de la table users: {e}")

# Créer un hash de mot de passe simple (pour test uniquement)
def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

# Créer l'utilisateur de test
try:
    now = datetime.now().isoformat()
    hashed_password = hash_password("password123")  # Mot de passe de test

    cursor.execute("""
        INSERT INTO users (
            email, hashed_password, full_name, status, is_active,
            last_login, phone, position, department,
            two_factor_enabled, failed_login_attempts,
            created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        '<EMAIL>',
        hashed_password,
        'Nadjib Test User',
        'active',
        1,  # True
        now,
        '+33123456789',
        'Administrateur',
        'IT',
        0,  # False
        0,
        now,  # created_at
        now   # updated_at
    ))
    
    conn.commit()
    print(f"\n✅ Utilisateur créé avec succès:")
    print(f"  Email: <EMAIL>")
    print(f"  Mot de passe: password123")
    print(f"  Date de dernière connexion: {now}")
    
    # Vérifier la création
    cursor.execute("SELECT id, email, last_login, status FROM users WHERE email = ?", ('<EMAIL>',))
    result = cursor.fetchone()
    if result:
        print(f"\nVérification:")
        print(f"  ID: {result[0]}")
        print(f"  Email: {result[1]}")
        print(f"  Last_login: {result[2]}")
        print(f"  Status: {result[3]}")
    
except Exception as e:
    print(f"❌ Erreur lors de la création de l'utilisateur: {e}")
    
    # Essayer de mettre à jour s'il existe déjà
    try:
        now = datetime.now().isoformat()
        cursor.execute("""
            UPDATE users
            SET last_login = ?, status = 'active', is_active = 1, updated_at = ?
            WHERE email = ?
        """, (now, now, '<EMAIL>'))
        
        if cursor.rowcount > 0:
            conn.commit()
            print(f"✅ Utilisateur existant mis à jour avec la date: {now}")
        else:
            print("❌ Aucun utilisateur trouvé à mettre à jour")
            
    except Exception as e2:
        print(f"❌ Erreur lors de la mise à jour: {e2}")

# Vérifier tous les utilisateurs après création/mise à jour
cursor.execute("SELECT id, email, last_login, status FROM users")
all_users = cursor.fetchall()
print(f"\nTous les utilisateurs après opération ({len(all_users)}):")
for user in all_users:
    print(f"  ID={user[0]}, Email={user[1]}, Last_login={user[2]}, Status={user[3]}")

conn.close()
print("\n=== Opération terminée ===")
