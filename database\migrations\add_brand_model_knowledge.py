"""
Migration pour ajouter les tables de base de connaissances des marques et modèles
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text

# Configuration de base de données
DATABASE_URL = "sqlite:///./maintenance_app.db"


def create_brand_model_tables():
    """Crée les tables pour la base de connaissances des marques et modèles"""

    engine = create_engine(DATABASE_URL)
    
    # SQL pour créer les tables
    sql_commands = [
        # Table des marques
        """
        CREATE TABLE IF NOT EXISTS brands (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR NOT NULL UNIQUE,
            normalized_name VARCHAR,
            usage_count INTEGER DEFAULT 1,
            last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_verified BOOLEAN DEFAULT FALSE,
            category VARCHAR,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        """,
        
        # Index pour les marques
        """
        CREATE INDEX IF NOT EXISTS idx_brands_normalized_name ON brands(normalized_name);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_brands_usage ON brands(usage_count, last_used);
        """,
        
        # Table des modèles
        """
        CREATE TABLE IF NOT EXISTS device_models (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            brand_id INTEGER NOT NULL,
            name VARCHAR NOT NULL,
            normalized_name VARCHAR,
            full_name VARCHAR,
            usage_count INTEGER DEFAULT 1,
            last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_verified BOOLEAN DEFAULT FALSE,
            specifications TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE CASCADE
        );
        """,
        
        # Index pour les modèles
        """
        CREATE INDEX IF NOT EXISTS idx_device_models_normalized_name ON device_models(normalized_name);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_brand_model ON device_models(brand_id, normalized_name);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_model_usage ON device_models(usage_count, last_used);
        """,
        
        # Table des suggestions
        """
        CREATE TABLE IF NOT EXISTS brand_model_suggestions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            brand_id INTEGER NOT NULL,
            model_id INTEGER NOT NULL,
            suggestion_text VARCHAR,
            search_pattern VARCHAR,
            relevance_score INTEGER DEFAULT 0,
            click_count INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE CASCADE,
            FOREIGN KEY (model_id) REFERENCES device_models(id) ON DELETE CASCADE
        );
        """,
        
        # Index pour les suggestions
        """
        CREATE INDEX IF NOT EXISTS idx_suggestions_text ON brand_model_suggestions(suggestion_text);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_suggestions_pattern ON brand_model_suggestions(search_pattern);
        """,
    ]
    
    with engine.connect() as connection:
        for sql in sql_commands:
            try:
                connection.execute(text(sql))
                print(f"✅ Commande SQL exécutée avec succès")
            except Exception as e:
                print(f"❌ Erreur lors de l'exécution SQL: {e}")
        
        connection.commit()
    
    print("✅ Migration des tables de base de connaissances terminée")


def populate_initial_data():
    """Peuple la base avec des données initiales communes"""

    engine = create_engine(DATABASE_URL)
    
    # Données initiales communes
    initial_brands = [
        # Smartphones
        ("Apple", "apple", "smartphone"),
        ("Samsung", "samsung", "smartphone"),
        ("Huawei", "huawei", "smartphone"),
        ("Xiaomi", "xiaomi", "smartphone"),
        ("OnePlus", "oneplus", "smartphone"),
        ("Google", "google", "smartphone"),
        ("Sony", "sony", "smartphone"),
        ("LG", "lg", "smartphone"),
        ("Motorola", "motorola", "smartphone"),
        ("Nokia", "nokia", "smartphone"),
        
        # Ordinateurs
        ("Dell", "dell", "ordinateur"),
        ("HP", "hp", "ordinateur"),
        ("Lenovo", "lenovo", "ordinateur"),
        ("Asus", "asus", "ordinateur"),
        ("Acer", "acer", "ordinateur"),
        ("MSI", "msi", "ordinateur"),
        ("Toshiba", "toshiba", "ordinateur"),
        ("Microsoft", "microsoft", "ordinateur"),
        
        # Tablettes
        ("iPad", "ipad", "tablette"),
        ("Surface", "surface", "tablette"),
        
        # Consoles
        ("PlayStation", "playstation", "console"),
        ("Xbox", "xbox", "console"),
        ("Nintendo", "nintendo", "console"),
        
        # Électroménager
        ("Whirlpool", "whirlpool", "electromenager"),
        ("Bosch", "bosch", "electromenager"),
        ("Siemens", "siemens", "electromenager"),
        ("LG", "lg", "electromenager"),
        ("Samsung", "samsung", "electromenager"),
    ]
    
    with engine.connect() as connection:
        for name, normalized, category in initial_brands:
            try:
                # Vérifier si la marque existe déjà
                result = connection.execute(text(
                    "SELECT id FROM brands WHERE normalized_name = :normalized"
                ), {"normalized": normalized})
                
                if not result.fetchone():
                    # Insérer la nouvelle marque
                    connection.execute(text("""
                        INSERT INTO brands (name, normalized_name, category, usage_count, is_verified)
                        VALUES (:name, :normalized, :category, 1, TRUE)
                    """), {
                        "name": name,
                        "normalized": normalized,
                        "category": category
                    })
                    print(f"✅ Marque ajoutée: {name}")
                
            except Exception as e:
                print(f"❌ Erreur lors de l'ajout de {name}: {e}")
        
        connection.commit()
    
    print("✅ Données initiales ajoutées")


def migrate_existing_data():
    """Migre les données existantes de repair_orders vers la base de connaissances"""

    engine = create_engine(DATABASE_URL)
    
    with engine.connect() as connection:
        try:
            # Récupérer toutes les paires marque-modèle existantes
            result = connection.execute(text("""
                SELECT DISTINCT brand, model, COUNT(*) as usage_count
                FROM repair_orders 
                WHERE brand IS NOT NULL AND brand != ''
                GROUP BY brand, model
                ORDER BY usage_count DESC
            """))
            
            existing_pairs = result.fetchall()
            
            print(f"📊 Trouvé {len(existing_pairs)} paires marque-modèle existantes")
            
            for brand, model, usage_count in existing_pairs:
                if not brand:
                    continue
                
                # Normaliser
                brand_normalized = brand.lower().strip()
                model_normalized = model.lower().strip() if model else ""
                
                # Vérifier/créer la marque
                brand_result = connection.execute(text(
                    "SELECT id FROM brands WHERE normalized_name = :normalized"
                ), {"normalized": brand_normalized})
                
                brand_row = brand_result.fetchone()
                if brand_row:
                    brand_id = brand_row[0]
                    # Mettre à jour le compteur d'usage
                    connection.execute(text("""
                        UPDATE brands 
                        SET usage_count = usage_count + :count
                        WHERE id = :id
                    """), {"count": usage_count, "id": brand_id})
                else:
                    # Créer nouvelle marque
                    connection.execute(text("""
                        INSERT INTO brands (name, normalized_name, usage_count)
                        VALUES (:name, :normalized, :usage_count)
                    """), {
                        "name": brand,
                        "normalized": brand_normalized,
                        "usage_count": usage_count
                    })
                    # Récupérer l'ID de la marque créée
                    brand_result = connection.execute(text(
                        "SELECT id FROM brands WHERE normalized_name = :normalized"
                    ), {"normalized": brand_normalized})
                    brand_id = brand_result.fetchone()[0]
                
                # Gérer le modèle si présent
                if model and model.strip():
                    model_result = connection.execute(text("""
                        SELECT id FROM device_models 
                        WHERE brand_id = :brand_id AND normalized_name = :normalized
                    """), {"brand_id": brand_id, "normalized": model_normalized})
                    
                    if model_result.fetchone():
                        # Mettre à jour le compteur
                        connection.execute(text("""
                            UPDATE device_models 
                            SET usage_count = usage_count + :count
                            WHERE brand_id = :brand_id AND normalized_name = :normalized
                        """), {
                            "count": usage_count,
                            "brand_id": brand_id,
                            "normalized": model_normalized
                        })
                    else:
                        # Créer nouveau modèle
                        full_name = f"{brand} {model}".strip()
                        connection.execute(text("""
                            INSERT INTO device_models 
                            (brand_id, name, normalized_name, full_name, usage_count)
                            VALUES (:brand_id, :name, :normalized, :full_name, :usage_count)
                        """), {
                            "brand_id": brand_id,
                            "name": model,
                            "normalized": model_normalized,
                            "full_name": full_name,
                            "usage_count": usage_count
                        })
                
                print(f"✅ Migré: {brand} {model} (usage: {usage_count})")
            
            connection.commit()
            print("✅ Migration des données existantes terminée")
            
        except Exception as e:
            print(f"❌ Erreur lors de la migration: {e}")
            connection.rollback()


def main():
    """Fonction principale de migration"""
    print("🚀 Début de la migration de la base de connaissances")
    
    # 1. Créer les tables
    create_brand_model_tables()
    
    # 2. Ajouter les données initiales
    populate_initial_data()
    
    # 3. Migrer les données existantes
    migrate_existing_data()
    
    print("✅ Migration terminée avec succès!")


if __name__ == "__main__":
    main()
