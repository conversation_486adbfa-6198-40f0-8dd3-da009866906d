import asyncio
import sys
sys.path.append('.')

from app.core.services.user_service import UserService
from app.core.services.auth_service import AuthService
from app.utils.database import SessionLocal

async def debug_admin_permissions():
    """Debug des permissions de l'utilisateur admin"""
    
    print("=== Debug des permissions admin ===")
    
    # Créer une session de base de données
    db = SessionLocal()
    
    try:
        # Créer les services
        user_service = UserService(db)
        auth_service = AuthService(db)
        
        # Chercher l'utilisateur admin principal
        admin_email = "<EMAIL>"  # L'utilisateur principal
        
        print(f"Recherche de l'utilisateur: {admin_email}")
        admin_user = await user_service.get_user_by_email(admin_email)
        
        if not admin_user:
            print(f"❌ Utilisateur {admin_email} non trouvé")
            return
        
        print(f"✅ Utilisateur trouvé:")
        print(f"  ID: {admin_user.id}")
        print(f"  Email: {admin_user.email}")
        print(f"  Nom: {admin_user.full_name}")
        print(f"  Statut: {admin_user.status}")
        print(f"  Actif: {admin_user.is_active}")
        
        # Récupérer les rôles
        user_roles = await user_service.get_user_roles(admin_user.id)
        print(f"\nRôles de l'utilisateur ({len(user_roles)}):")
        for role in user_roles:
            print(f"  - {role.name} (ID: {role.id})")
        
        # Récupérer les permissions
        user_permissions = await user_service.get_user_permissions(admin_user.id)
        print(f"\nPermissions de l'utilisateur ({len(user_permissions)}):")
        for perm in sorted(user_permissions):
            print(f"  • {perm}")
        
        # Vérifier les permissions d'admin spécifiques
        print(f"\n=== Vérification des permissions d'admin ===")
        admin_permissions_to_check = [
            'user.create', 
            'user.delete', 
            'user.manage_roles', 
            'system.settings',
            'user.view',
            'user.edit'
        ]
        
        for perm in admin_permissions_to_check:
            has_perm = perm in user_permissions
            status = "✅ OUI" if has_perm else "❌ NON"
            print(f"  {perm}: {status}")
        
        # Test d'authentification pour récupérer les infos comme dans l'app
        print(f"\n=== Test d'authentification ===")
        try:
            success, token, auth_info = await auth_service.authenticate_user(admin_email, "password123")
            
            if success:
                print(f"✅ Authentification réussie")
                print(f"Permissions dans auth_info ({len(auth_info.get('permissions', []))}):")
                auth_permissions = auth_info.get('permissions', [])
                for perm in sorted(auth_permissions):
                    print(f"  • {perm}")
                
                # Vérifier si les permissions d'admin sont présentes
                admin_perms_in_auth = [perm for perm in admin_permissions_to_check if perm in auth_permissions]
                print(f"\nPermissions d'admin trouvées dans auth_info: {admin_perms_in_auth}")
                
                # Simuler la logique de l'interface
                print(f"\n=== Simulation de la logique UI ===")
                admin_permissions = ['user.create', 'user.delete', 'user.manage_roles', 'system.settings']
                is_admin = any(perm in auth_permissions for perm in admin_permissions)
                print(f"Est considéré comme admin: {is_admin}")
                
                if is_admin:
                    print("✅ L'utilisateur devrait avoir accès à tout")
                else:
                    print("❌ L'utilisateur sera limité par les permissions")
                    print("Permissions manquantes pour être admin:")
                    for perm in admin_permissions:
                        if perm not in auth_permissions:
                            print(f"  - {perm}")
            else:
                print(f"❌ Authentification échouée: {auth_info}")
                
        except Exception as e:
            print(f"❌ Erreur lors de l'authentification: {e}")
        
    except Exception as e:
        print(f"❌ Erreur lors du debug: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
        print("\n✅ Session de base de données fermée")

if __name__ == "__main__":
    asyncio.run(debug_admin_permissions())
