#!/usr/bin/env python3
"""
Script pour déboguer l'erreur unit_price dans l'application
"""
import sys
import os
import traceback

def patch_builtins():
    """Patch les builtins pour capturer les erreurs NameError avec unit_price"""
    original_getattr = getattr
    original_setattr = setattr
    
    def debug_getattr(obj, name, default=None):
        if name == 'unit_price':
            print(f"DEBUG: Tentative d'accès à l'attribut 'unit_price' sur {type(obj)}")
            print(f"DEBUG: Stack trace:")
            traceback.print_stack()
        return original_getattr(obj, name, default) if default is not None else original_getattr(obj, name)
    
    def debug_setattr(obj, name, value):
        if name == 'unit_price':
            print(f"DEBUG: Tentative de définition de l'attribut 'unit_price' sur {type(obj)} = {value}")
            print(f"DEBUG: Stack trace:")
            traceback.print_stack()
        return original_setattr(obj, name, value)
    
    # Remplacer les fonctions
    import builtins
    builtins.getattr = debug_getattr
    builtins.setattr = debug_setattr

def test_import_modules():
    """Teste l'import des modules principaux"""
    try:
        print("Testing module imports...")
        
        # Importer les modules un par un pour identifier le problème
        print("Importing database...")
        from app.utils.database import SessionLocal
        
        print("Importing services...")
        from app.core.services.purchasing_service import PurchasingService
        from app.core.services.inventory_service import InventoryService
        
        print("Importing models...")
        from app.core.models.purchasing import PurchaseOrderItem
        
        print("Importing dialogs...")
        from app.ui.views.purchasing.dialogs.order_item_dialog import OrderItemDialog
        from app.ui.views.purchasing.dialogs.purchase_order_dialog import PurchaseOrderDialog
        
        print("SUCCESS: All modules imported successfully")
        return True
        
    except Exception as e:
        print(f"ERROR: Error importing modules: {e}")
        traceback.print_exc()
        return False

def test_create_purchase_order_item():
    """Teste la création d'un PurchaseOrderItem"""
    try:
        print("Testing PurchaseOrderItem creation...")
        
        from app.core.models.purchasing import PurchaseOrderItem
        
        # Tester avec purchase_unit_price
        item = PurchaseOrderItem(
            product_id=1,
            quantity=3,
            purchase_unit_price=25.50
        )
        
        print(f"SUCCESS: PurchaseOrderItem created with purchase_unit_price: {item.purchase_unit_price}")
        
        # Tester l'accès aux attributs
        print(f"Product ID: {item.product_id}")
        print(f"Quantity: {item.quantity}")
        print(f"Purchase unit price: {item.purchase_unit_price}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error creating PurchaseOrderItem: {e}")
        traceback.print_exc()
        return False

def test_dialog_creation():
    """Teste la création des dialogues sans QApplication"""
    try:
        print("Testing dialog classes...")
        
        # Tester l'import des classes de dialogue
        from app.ui.views.purchasing.dialogs.order_item_dialog import OrderItemDialog
        from app.ui.views.purchasing.dialogs.purchase_order_dialog import PurchaseOrderDialog
        
        print("SUCCESS: Dialog classes imported successfully")
        
        # Tester les méthodes statiques si elles existent
        # Note: On ne peut pas créer les dialogues sans QApplication
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error with dialog classes: {e}")
        traceback.print_exc()
        return False

def test_service_methods():
    """Teste les méthodes des services"""
    try:
        print("Testing service methods...")
        
        from app.core.services.purchasing_service import PurchasingService
        from app.utils.database import SessionLocal
        
        # Créer une session de base de données
        db = SessionLocal()
        service = PurchasingService(db)
        
        print("SUCCESS: PurchasingService created successfully")
        
        # Tester si les méthodes existent
        if hasattr(service, 'get_all'):
            print("SUCCESS: get_all method exists")
        
        if hasattr(service, 'create'):
            print("SUCCESS: create method exists")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"ERROR: Error with service methods: {e}")
        traceback.print_exc()
        return False

def main():
    """Fonction principale de débogage"""
    print("=== DÉBOGAGE AVANCÉ DE L'ERREUR unit_price ===")
    print("=" * 50)
    
    # Activer le patch de débogage
    # patch_builtins()
    
    success = True
    
    # Test d'import des modules
    if not test_import_modules():
        success = False
    
    # Test de création d'objets
    if not test_create_purchase_order_item():
        success = False
    
    # Test des dialogues
    if not test_dialog_creation():
        success = False
    
    # Test des services
    if not test_service_methods():
        success = False
    
    if success:
        print("\nSUCCESS: Tous les tests de débogage sont passés!")
        print("L'erreur pourrait venir d'une interaction spécifique dans l'interface utilisateur")
    else:
        print("\nERROR: Certains tests ont échoué")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
