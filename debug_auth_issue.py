import asyncio
import sys
sys.path.append('.')

from app.core.services.auth_service import AuthService
from app.core.services.user_service import UserService
from app.core.services.permission_service import RoleService
from app.core.models.user import UserCreate
from app.utils.database import Session<PERSON><PERSON>al
from app.utils.security import verify_password, get_password_hash

async def debug_auth_issue():
    """Debug détaillé du problème d'authentification"""
    
    print("=== Debug du problème d'authentification ===")
    
    # Créer une session de base de données
    db = SessionLocal()
    
    try:
        # Créer les services
        auth_service = AuthService(db)
        user_service = UserService(db)
        role_service = RoleService(db)
        
        # Récupérer un rôle existant
        roles = await role_service.get_all()
        test_role = roles[0]
        print(f"✅ Rôle trouvé: {test_role.name} (ID: {test_role.id})")
        
        # Créer un utilisateur de test
        test_email = "<EMAIL>"
        test_password = "password123"
        
        # Supprimer l'utilisateur s'il existe déjà
        existing_user = await user_service.get_user_by_email(test_email)
        if existing_user:
            print(f"🗑️  Suppression de l'utilisateur existant: {test_email}")
            await user_service.delete_user(existing_user.id, deleted_by_id=1)
        
        # Créer un nouvel utilisateur
        user_data = UserCreate(
            email=test_email,
            password=test_password,
            full_name="Debug Auth User",
            phone="0123456789",
            position="Testeur",
            department="IT",
            role_ids=[test_role.id]
        )
        
        print(f"📝 Création de l'utilisateur: {test_email}")
        new_user = await user_service.create_user(user_data, created_by_id=1)
        
        print(f"✅ Utilisateur créé:")
        print(f"  ID: {new_user.id}")
        print(f"  Email: {new_user.email}")
        print(f"  Hash du mot de passe: {new_user.hashed_password[:50]}...")
        
        # Test direct de la vérification du mot de passe
        print(f"\n=== Test direct de verify_password ===")
        
        # Test avec le bon mot de passe
        is_valid_correct = verify_password(test_password, new_user.hashed_password)
        print(f"verify_password('{test_password}', hash) = {is_valid_correct}")
        
        # Test avec un mauvais mot de passe
        is_valid_wrong = verify_password("mauvais_password", new_user.hashed_password)
        print(f"verify_password('mauvais_password', hash) = {is_valid_wrong}")
        
        # Test de création d'un nouveau hash
        print(f"\n=== Test de création de hash ===")
        new_hash = get_password_hash(test_password)
        print(f"Nouveau hash créé: {new_hash[:50]}...")
        
        # Vérifier le nouveau hash
        is_new_hash_valid = verify_password(test_password, new_hash)
        print(f"verify_password avec nouveau hash = {is_new_hash_valid}")
        
        # Test de la méthode authenticate_user étape par étape
        print(f"\n=== Test étape par étape de authenticate_user ===")
        
        # Étape 1: Récupérer l'utilisateur
        user_from_db = await user_service.get_user_by_email(test_email)
        print(f"Utilisateur récupéré: {user_from_db.email if user_from_db else 'None'}")
        
        if user_from_db:
            print(f"  Hash en base: {user_from_db.hashed_password[:50]}...")
            print(f"  Statut: {user_from_db.status}")
            print(f"  Actif: {user_from_db.is_active}")
            
            # Étape 2: Vérifier le mot de passe
            password_check = verify_password(test_password, user_from_db.hashed_password)
            print(f"  Vérification mot de passe: {password_check}")
            
            # Étape 3: Vérifier les conditions
            print(f"  user exists: {user_from_db is not None}")
            print(f"  password valid: {password_check}")
            print(f"  user active: {user_from_db.is_active}")
            print(f"  status active: {user_from_db.status}")
            
            # Condition complète
            auth_should_succeed = (
                user_from_db and 
                verify_password(test_password, user_from_db.hashed_password) and
                user_from_db.is_active and
                str(user_from_db.status) == "active"
            )
            print(f"  Authentification devrait réussir: {auth_should_succeed}")
        
        # Test avec bon mot de passe
        print(f"\n=== Test authenticate_user avec bon mot de passe ===")
        try:
            # Modifier temporairement la méthode pour éviter l'erreur JWT
            original_method = auth_service.authenticate_user
            
            async def test_auth_without_jwt(email, password, totp_code=None, ip_address=None, user_agent=None):
                user = await auth_service.user_service.get_user_by_email(email)
                
                print(f"  Utilisateur trouvé: {user is not None}")
                if user:
                    print(f"  Hash: {user.hashed_password[:50]}...")
                    password_valid = verify_password(password, user.hashed_password)
                    print(f"  Mot de passe valide: {password_valid}")
                    print(f"  Utilisateur actif: {user.is_active}")
                    print(f"  Statut: {user.status}")
                    
                    if not user or not password_valid:
                        return False, None, {"error": "Invalid credentials"}
                    
                    if not user.is_active or str(user.status) != "active":
                        return False, None, {"error": "Account is inactive or suspended"}
                    
                    # Simuler le succès sans JWT
                    return True, "fake_token", {"user_id": user.id}
                
                return False, None, {"error": "User not found"}
            
            success, token, info = await test_auth_without_jwt(test_email, test_password)
            print(f"  Résultat: success={success}, token={token}, info={info}")
            
        except Exception as e:
            print(f"  Erreur: {e}")
            import traceback
            traceback.print_exc()
        
        # Test avec mauvais mot de passe
        print(f"\n=== Test authenticate_user avec mauvais mot de passe ===")
        try:
            success, token, info = await test_auth_without_jwt(test_email, "mauvais_password")
            print(f"  Résultat: success={success}, token={token}, info={info}")
        except Exception as e:
            print(f"  Erreur: {e}")
        
        # Nettoyer
        print(f"\n=== Nettoyage ===")
        await user_service.delete_user(new_user.id, deleted_by_id=1)
        print(f"🗑️  Utilisateur de test supprimé")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
        print("✅ Session de base de données fermée")

if __name__ == "__main__":
    asyncio.run(debug_auth_issue())
