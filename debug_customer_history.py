#!/usr/bin/env python3
"""
Script de diagnostic pour l'historique client
"""

import sys
import os
from datetime import datetime, timezone

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.customer_service import CustomerService
from app.core.models.customer import Customer, CustomerTransaction
from app.core.models.sale import Sale
from app.core.models.repair import RepairOrder
from sqlalchemy import text


def debug_customer_history():
    """Diagnostique les données d'historique client"""
    print("🔍 Diagnostic de l'historique client")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        customer_service = CustomerService(db)
        
        # 1. Lister tous les clients
        print("\n1. Clients disponibles:")
        customers = db.query(Customer).all()
        print(f"   Nombre total de clients: {len(customers)}")
        
        for customer in customers[:5]:  # Afficher les 5 premiers
            print(f"   - ID: {customer.id}, Nom: {customer.name}, Solde: {customer.current_balance:.2f} DA")
        
        if not customers:
            print("   ❌ Aucun client trouvé dans la base de données")
            return
        
        # Prendre un client avec des données (solde non nul)
        test_customer = None
        for customer in customers:
            if customer.current_balance != 0:
                test_customer = customer
                break

        if not test_customer:
            test_customer = customers[0]  # Fallback au premier client

        customer_id = test_customer.id
        
        print(f"\n2. Test avec le client ID: {customer_id} ({test_customer.name})")
        
        # 2. Vérifier les ventes
        print("\n   📊 Ventes:")
        sales = db.execute(
            text("""
            SELECT id, date, final_amount, total_paid, payment_status
            FROM sales
            WHERE customer_id = :cid
            ORDER BY date DESC
            LIMIT 10
            """),
            {"cid": customer_id}
        ).fetchall()
        
        print(f"      Nombre de ventes: {len(sales)}")
        for sale in sales:
            print(f"      - Vente {sale[0]}: {sale[1]}, {sale[2]:.2f} DA, payé: {sale[3]:.2f} DA, statut: {sale[4]}")
        
        # 3. Vérifier les réparations
        print("\n   🔧 Réparations:")
        repairs = db.execute(
            text("""
            SELECT id, created_at, final_amount, total_paid, payment_status
            FROM repair_orders
            WHERE customer_id = :cid
            ORDER BY created_at DESC
            LIMIT 10
            """),
            {"cid": customer_id}
        ).fetchall()
        
        print(f"      Nombre de réparations: {len(repairs)}")
        for repair in repairs:
            print(f"      - Réparation {repair[0]}: {repair[1]}, {repair[2]:.2f} DA, payé: {repair[3]:.2f} DA, statut: {repair[4]}")
        
        # 4. Vérifier les transactions clients
        print("\n   💰 Transactions clients:")
        transactions = db.execute(
            text("""
            SELECT id, transaction_date, amount, reference_number, description, transaction_type
            FROM customer_transactions
            WHERE customer_id = :cid
            ORDER BY transaction_date DESC
            LIMIT 10
            """),
            {"cid": customer_id}
        ).fetchall()
        
        print(f"      Nombre de transactions: {len(transactions)}")
        for tx in transactions:
            print(f"      - Transaction {tx[0]}: {tx[1]}, {tx[2]:.2f} DA, ref: {tx[3]}, type: {tx[5]}")
            print(f"        Description: {tx[4]}")
        
        # 5. Test avec les méthodes du service
        print("\n3. Test avec les méthodes du service:")
        
        try:
            import asyncio
            
            async def test_service_methods():
                # Transactions via service
                service_transactions = await customer_service.get_customer_transactions(customer_id, limit=10)
                print(f"   📊 Transactions via service: {len(service_transactions)}")
                
                for tx in service_transactions:
                    print(f"      - {tx.id}: {tx.transaction_date}, {tx.amount:.2f} DA, {tx.description}")
                
                # Ventes via service
                service_sales = await customer_service.get_customer_sales(customer_id, limit=10)
                print(f"   📊 Ventes via service: {len(service_sales)}")
                
                for sale in service_sales:
                    print(f"      - {sale.id}: {sale.date}, {sale.final_amount:.2f} DA, statut: {sale.payment_status}")
            
            # Exécuter les tests async
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(test_service_methods())
            loop.close()
            
        except Exception as e:
            print(f"   ❌ Erreur lors du test des méthodes du service: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. Vérifier la structure des tables
        print("\n4. Vérification de la structure des tables:")
        
        # Structure table sales
        try:
            sales_columns = db.execute("PRAGMA table_info(sales)").fetchall()
            print(f"   📊 Colonnes table 'sales': {len(sales_columns)}")
            for col in sales_columns:
                print(f"      - {col[1]} ({col[2]})")
        except Exception as e:
            print(f"   ❌ Erreur structure table sales: {e}")
        
        # Structure table repair_orders
        try:
            repairs_columns = db.execute("PRAGMA table_info(repair_orders)").fetchall()
            print(f"   🔧 Colonnes table 'repair_orders': {len(repairs_columns)}")
            for col in repairs_columns:
                print(f"      - {col[1]} ({col[2]})")
        except Exception as e:
            print(f"   ❌ Erreur structure table repair_orders: {e}")
        
        # Structure table customer_transactions
        try:
            tx_columns = db.execute("PRAGMA table_info(customer_transactions)").fetchall()
            print(f"   💰 Colonnes table 'customer_transactions': {len(tx_columns)}")
            for col in tx_columns:
                print(f"      - {col[1]} ({col[2]})")
        except Exception as e:
            print(f"   ❌ Erreur structure table customer_transactions: {e}")
        
        print("\n" + "=" * 60)
        print("✅ Diagnostic terminé")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


if __name__ == "__main__":
    debug_customer_history()
