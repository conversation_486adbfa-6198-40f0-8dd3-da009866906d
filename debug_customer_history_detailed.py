#!/usr/bin/env python3
"""
Debug détaillé de l'historique client pour identifier pourquoi il n'affiche rien
"""

import sys
import os
import asyncio
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.customer_service import CustomerService
from app.core.models.customer import Customer
from app.core.models.sale import Sale
from app.core.models.repair import RepairOrder
from app.core.models.customer import CustomerTransaction
from sqlalchemy import text


async def debug_customer_history():
    """Debug complet de l'historique client"""
    print("🔍 Debug détaillé de l'historique client")
    print("=" * 60)
    
    db = SessionLocal()
    service = CustomerService(db)
    
    try:
        # 1. Vérifier les clients disponibles
        print("1️⃣ Clients disponibles:")
        customers = db.query(Customer).limit(10).all()
        print(f"   📊 Nombre de clients: {len(customers)}")
        
        if not customers:
            print("   ❌ Aucun client trouvé dans la base de données")
            return
        
        # Prendre le premier client avec des données
        customer = None
        for c in customers:
            print(f"   👤 Client {c.id}: {c.name}")

            # Vérifier s'il a des données
            sales_count = db.query(Sale).filter(Sale.customer_id == c.id).count()
            repairs_count = db.query(RepairOrder).filter(RepairOrder.customer_id == c.id).count()
            transactions_count = db.query(CustomerTransaction).filter(CustomerTransaction.customer_id == c.id).count()

            total_data = sales_count + repairs_count + transactions_count
            print(f"      📈 Ventes: {sales_count}, Réparations: {repairs_count}, Transactions: {transactions_count}")
            print(f"      📊 Total données: {total_data}")

            if total_data > 0 and not customer:
                customer = c
                print(f"      ✅ Sélectionné pour le test")
            print()

        if not customer:
            print("❌ Aucun client avec des données trouvé")
            # Prendre le premier client même sans données pour tester
            if customers:
                customer = customers[0]
                print(f"⚠️  Utilisation du premier client sans données pour test: {customer.name}")
            else:
                return

        print(f"🎯 Test avec le client: {customer.name} (ID: {customer.id})")
        print("=" * 60)
        
        # 2. Test des méthodes du service
        print("2️⃣ Test des méthodes du service:")
        
        # Test get_customer_sales
        try:
            sales = await service.get_customer_sales(customer.id, limit=500)
            print(f"   ✅ get_customer_sales: {len(sales)} ventes trouvées")
            for sale in sales[:3]:  # Afficher les 3 premières
                print(f"      - Vente {sale.id}: {sale.date}, montant: {sale.final_amount}")
        except Exception as e:
            print(f"   ❌ get_customer_sales: Erreur - {e}")
            import traceback
            traceback.print_exc()
        
        # Test get_customer_transactions
        try:
            transactions = await service.get_customer_transactions(customer.id, limit=500)
            print(f"   ✅ get_customer_transactions: {len(transactions)} transactions trouvées")
            for tx in transactions[:3]:  # Afficher les 3 premières
                print(f"      - Transaction {tx.id}: {tx.transaction_date}, montant: {tx.amount}")
        except Exception as e:
            print(f"   ❌ get_customer_transactions: Erreur - {e}")
            import traceback
            traceback.print_exc()
        
        # Test requête réparations
        try:
            repairs = db.execute(
                text("""
                SELECT id, created_at, final_amount, total_paid, payment_status
                FROM repair_orders
                WHERE customer_id = :cid
                ORDER BY created_at DESC
                LIMIT 500
                """),
                {"cid": customer.id}
            ).fetchall()
            print(f"   ✅ Requête réparations: {len(repairs)} réparations trouvées")
            for repair in repairs[:3]:  # Afficher les 3 premières
                print(f"      - Réparation {repair[0]}: {repair[1]}, montant: {repair[2]}")
        except Exception as e:
            print(f"   ❌ Requête réparations: Erreur - {e}")
            import traceback
            traceback.print_exc()
        
        # 3. Simulation du processus de refresh du widget
        print(f"\n3️⃣ Simulation du processus refresh du widget:")
        
        try:
            # Récupérer toutes les données comme dans le widget
            sales = await service.get_customer_sales(customer.id, limit=500)
            transactions = await service.get_customer_transactions(customer.id, limit=500)
            repairs = db.execute(
                text("""
                SELECT id, created_at, final_amount, total_paid, payment_status
                FROM repair_orders
                WHERE customer_id = :cid
                ORDER BY created_at DESC
                LIMIT 500
                """),
                {"cid": customer.id}
            ).fetchall()
            
            print(f"   📊 Données récupérées:")
            print(f"      - Ventes: {len(sales)}")
            print(f"      - Transactions: {len(transactions)}")
            print(f"      - Réparations: {len(repairs)}")
            
            # Fusionner en une liste normalisée comme dans le widget
            rows = []
            
            # Traiter les ventes (objets Sale)
            for sale in sales:
                due = (sale.final_amount or 0.0) - (sale.total_paid or 0.0)
                rows.append({
                    'type': 'Vente',
                    'id': sale.id,
                    'date': sale.date,
                    'reference': f"SALE-{sale.id}",
                    'description': f"Vente - dû: {due:.2f} DA",
                    'amount': float(sale.final_amount or 0.0),
                    'status': str(sale.payment_status) if sale.payment_status else ""
                })
            
            # Traiter les réparations (tuples SQL)
            for r in repairs:
                due = (r[2] or 0.0) - (r[3] or 0.0)
                rows.append({
                    'type': 'Réparation',
                    'id': r[0],
                    'date': r[1],
                    'reference': f"REPAIR-{r[0]}",
                    'description': f"Réparation - dû: {due:.2f} DA",
                    'amount': float(r[2] or 0.0),
                    'status': str(r[4]) if r[4] is not None else ""
                })
            
            # Traiter les transactions (objets CustomerTransaction)
            for tx in transactions:
                rows.append({
                    'type': 'Versement',
                    'id': tx.id,
                    'date': tx.transaction_date,
                    'reference': tx.reference_number or f"TX-{tx.id}",
                    'description': tx.description or tx.transaction_type or "Transaction",
                    'amount': float(tx.amount or 0.0),
                    'status': tx.transaction_type or ""
                })
            
            # Trier par date desc
            rows.sort(key=lambda x: x.get('date') or datetime.min, reverse=True)
            
            print(f"   📋 Lignes d'historique créées: {len(rows)}")
            
            if rows:
                print(f"   ✅ Historique généré avec succès!")
                print(f"   📝 Aperçu des 5 premières entrées:")
                for i, row in enumerate(rows[:5]):
                    print(f"      {i+1}. {row['type']} - {row['reference']} - {row['amount']:.2f} DA")
            else:
                print(f"   ❌ Aucune ligne d'historique générée")
                print(f"   🔍 Vérification des données individuelles:")
                
                if sales:
                    sale = sales[0]
                    print(f"      Vente exemple: ID={sale.id}, date={sale.date}, final_amount={sale.final_amount}")
                
                if repairs:
                    repair = repairs[0]
                    print(f"      Réparation exemple: ID={repair[0]}, date={repair[1]}, final_amount={repair[2]}")
                
                if transactions:
                    tx = transactions[0]
                    print(f"      Transaction exemple: ID={tx.id}, date={tx.transaction_date}, amount={tx.amount}")
            
        except Exception as e:
            print(f"   ❌ Erreur lors de la simulation: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Vérification des tables directement
        print(f"\n4️⃣ Vérification directe des tables:")
        
        # Vérifier la table sales
        try:
            sales_direct = db.execute(
                text("SELECT id, date, final_amount, customer_id FROM sales WHERE customer_id = :cid LIMIT 5"),
                {"cid": customer.id}
            ).fetchall()
            print(f"   📊 Table sales: {len(sales_direct)} entrées")
            for sale in sales_direct:
                print(f"      - ID: {sale[0]}, Date: {sale[1]}, Montant: {sale[2]}")
        except Exception as e:
            print(f"   ❌ Erreur table sales: {e}")
        
        # Vérifier la table repair_orders
        try:
            repairs_direct = db.execute(
                text("SELECT id, created_at, final_amount, customer_id FROM repair_orders WHERE customer_id = :cid LIMIT 5"),
                {"cid": customer.id}
            ).fetchall()
            print(f"   📊 Table repair_orders: {len(repairs_direct)} entrées")
            for repair in repairs_direct:
                print(f"      - ID: {repair[0]}, Date: {repair[1]}, Montant: {repair[2]}")
        except Exception as e:
            print(f"   ❌ Erreur table repair_orders: {e}")
        
        # Vérifier la table customer_transactions
        try:
            transactions_direct = db.execute(
                text("SELECT id, transaction_date, amount, customer_id FROM customer_transactions WHERE customer_id = :cid LIMIT 5"),
                {"cid": customer.id}
            ).fetchall()
            print(f"   📊 Table customer_transactions: {len(transactions_direct)} entrées")
            for tx in transactions_direct:
                print(f"      - ID: {tx[0]}, Date: {tx[1]}, Montant: {tx[2]}")
        except Exception as e:
            print(f"   ❌ Erreur table customer_transactions: {e}")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


def main():
    """Fonction principale"""
    print("🚀 Debug de l'historique client")
    print("=" * 70)
    
    # Exécuter le debug
    asyncio.run(debug_customer_history())
    
    print("\n" + "=" * 70)
    print("🎯 Résumé du debug terminé")
    print("\n📋 Points à vérifier:")
    print("   1. Y a-t-il des clients avec des données?")
    print("   2. Les méthodes du service fonctionnent-elles?")
    print("   3. Les requêtes SQL retournent-elles des résultats?")
    print("   4. Le processus de fusion des données fonctionne-t-il?")
    print("   5. Y a-t-il des erreurs dans la logique du widget?")


if __name__ == "__main__":
    main()
