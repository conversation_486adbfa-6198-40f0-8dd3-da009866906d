import os
import sys
import traceback
import logging
from pathlib import Path

# Configuration du logging avancé
def setup_debug_logging():
    log_file = 'debug_executable.log'
    try:
        logging.basicConfig(
            level=logging.DEBUG,  # Niveau DEBUG pour capturer tous les détails
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    except Exception as e:
        print(f"Erreur lors de la configuration du logging: {str(e)}")
        # Fallback en cas d'erreur
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )

# Fonction pour vérifier l'environnement d'exécution
def check_environment():
    logger = logging.getLogger("debug")
    
    # Vérifier le répertoire de travail actuel
    cwd = os.getcwd()
    logger.info(f"Répertoire de travail actuel: {cwd}")
    
    # Vérifier le chemin de l'exécutable
    executable_path = sys.executable
    logger.info(f"Chemin de l'exécutable: {executable_path}")
    
    # Vérifier les variables d'environnement importantes
    logger.info("Variables d'environnement:")
    for var in ['PATH', 'PYTHONPATH', 'TEMP', 'TMP']:
        logger.info(f"  {var}: {os.environ.get(var, 'Non défini')}")
    
    # Vérifier les chemins de recherche Python
    logger.info("Chemins de recherche Python:")
    for path in sys.path:
        logger.info(f"  {path}")

# Fonction pour vérifier les fichiers et répertoires critiques
def check_critical_files():
    logger = logging.getLogger("debug")
    
    # Liste des chemins critiques à vérifier
    critical_paths = [
        "config/settings.toml",
        "data",
        "backups",
        "output",
        "app/ui/resources"
    ]
    
    logger.info("Vérification des fichiers et répertoires critiques:")
    for path in critical_paths:
        full_path = os.path.abspath(path)
        exists = os.path.exists(full_path)
        type_str = "Répertoire" if os.path.isdir(full_path) else "Fichier" if os.path.isfile(full_path) else "Inconnu"
        logger.info(f"  {path}: {'Existe' if exists else 'N\'existe pas'} ({type_str})")
        
        # Si c'est un répertoire qui existe, lister son contenu
        if exists and os.path.isdir(full_path):
            try:
                contents = os.listdir(full_path)
                if contents:
                    logger.info(f"    Contenu ({len(contents)} éléments): {', '.join(contents[:5])}{'...' if len(contents) > 5 else ''}")
                else:
                    logger.info(f"    Répertoire vide")
            except Exception as e:
                logger.error(f"    Erreur lors de la lecture du répertoire: {str(e)}")

# Fonction pour vérifier les dépendances Python
def check_dependencies():
    logger = logging.getLogger("debug")
    
    # Liste des dépendances critiques
    critical_deps = [
        "PyQt6",
        "sqlalchemy",
        "apscheduler",
        "passlib",
        "toml"
    ]
    
    logger.info("Vérification des dépendances Python:")
    for dep in critical_deps:
        try:
            module = __import__(dep)
            logger.info(f"  {dep}: Importé avec succès (version: {getattr(module, '__version__', 'Inconnue')})")
        except ImportError as e:
            logger.error(f"  {dep}: Échec de l'importation - {str(e)}")
        except Exception as e:
            logger.error(f"  {dep}: Erreur inattendue - {str(e)}")

# Fonction pour tester l'initialisation de PyQt
def test_pyqt_initialization():
    logger = logging.getLogger("debug")
    
    logger.info("Test d'initialisation de PyQt:")
    try:
        from PyQt6.QtWidgets import QApplication
        app = QApplication([])
        logger.info("  PyQt6.QtWidgets.QApplication: Initialisé avec succès")
        
        # Vérifier les styles disponibles
        from PyQt6.QtWidgets import QStyleFactory
        logger.info(f"  Styles disponibles: {', '.join(QStyleFactory.keys())}")
        
        # Vérifier si on peut créer un widget simple
        from PyQt6.QtWidgets import QLabel
        label = QLabel("Test")
        logger.info("  Création d'un QLabel: Réussi")
        
        app.quit()
    except Exception as e:
        logger.error(f"  Erreur lors de l'initialisation de PyQt: {str(e)}")
        logger.error(traceback.format_exc())

# Fonction pour tester l'accès à la base de données
def test_database_access():
    logger = logging.getLogger("debug")
    
    logger.info("Test d'accès à la base de données:")
    try:
        # Vérifier si le fichier de base de données existe
        db_path = os.path.abspath("data/app.db")
        if os.path.exists(db_path):
            logger.info(f"  Fichier de base de données trouvé: {db_path}")
            
            # Tester la connexion avec SQLite directement
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT sqlite_version()")
            version = cursor.fetchone()
            logger.info(f"  Connexion SQLite réussie (version: {version[0]})")
            
            # Vérifier quelques tables importantes
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            logger.info(f"  Tables trouvées: {', '.join([t[0] for t in tables][:10])}{'...' if len(tables) > 10 else ''}")
            
            conn.close()
        else:
            logger.error(f"  Fichier de base de données non trouvé: {db_path}")
    except Exception as e:
        logger.error(f"  Erreur lors de l'accès à la base de données: {str(e)}")
        logger.error(traceback.format_exc())

# Fonction principale
def main():
    # Configurer le logging
    setup_debug_logging()
    logger = logging.getLogger("debug")
    
    try:
        logger.info("=== DÉBUT DU DIAGNOSTIC DE L'EXÉCUTABLE ===")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Plateforme: {sys.platform}")
        
        # Vérifier l'environnement
        check_environment()
        
        # Vérifier les fichiers critiques
        check_critical_files()
        
        # Vérifier les dépendances
        check_dependencies()
        
        # Tester PyQt
        test_pyqt_initialization()
        
        # Tester la base de données
        test_database_access()
        
        logger.info("=== FIN DU DIAGNOSTIC ===")
        
        print("\nDiagnostic terminé. Veuillez consulter le fichier 'debug_executable.log' pour les détails.")
        
    except Exception as e:
        logger.critical(f"Erreur fatale lors du diagnostic: {str(e)}")
        logger.critical(traceback.format_exc())
        print(f"\nERREUR FATALE: {str(e)}")
        print("Veuillez consulter le fichier 'debug_executable.log' pour les détails.")

if __name__ == "__main__":
    main()