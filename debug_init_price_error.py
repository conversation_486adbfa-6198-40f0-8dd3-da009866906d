#!/usr/bin/env python3
"""
Script de débogage pour tracer l'erreur 'init_price is not defined'
"""
import sys
import os
import re

def search_init_price_usage():
    """Recherche tous les usages de 'init_price'"""
    print("Recherche de tous les usages de 'init_price'...")
    
    # Répertoires à analyser
    directories = [
        'app',
        'scripts',
        'migrations'
    ]
    
    problematic_files = []
    
    for directory in directories:
        if os.path.exists(directory):
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                lines = content.split('\n')
                                
                                for i, line in enumerate(lines, 1):
                                    if 'init_price' in line:
                                        problematic_files.append({
                                            'file': file_path,
                                            'line': i,
                                            'content': line.strip()
                                        })
                                        print(f"FOUND: {file_path}:{i} - {line.strip()}")
                        
                        except Exception as e:
                            print(f"Erreur lors de la lecture de {file_path}: {e}")
    
    return problematic_files

def search_threading_issues():
    """Recherche les problèmes de threading Qt"""
    print("\nRecherche des problèmes de threading Qt...")
    
    # Fichiers susceptibles d'avoir des problèmes de threading
    files_to_check = [
        'app/ui/views/purchasing/dialogs/order_item_dialog.py',
        'app/ui/views/purchasing/dialogs/purchase_order_dialog.py',
        'app/ui/views/repair/dialogs/used_parts_dialog.py',
        'app/ui/views/repair/widgets/used_parts_widget.py',
        'app/ui/components/notification_center.py',
        'app/utils/async_runner.py'
    ]
    
    threading_issues = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n=== Analyse de {file_path} ===")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        # Rechercher les patterns problématiques de threading
                        problematic_patterns = [
                            r'asyncio\.new_event_loop\(\)',
                            r'asyncio\.run\(',
                            r'loop\.run_until_complete\(',
                            r'QTimer\.singleShot\(0,'
                        ]
                        
                        for pattern in problematic_patterns:
                            if re.search(pattern, line):
                                threading_issues.append({
                                    'file': file_path,
                                    'line': i,
                                    'content': line.strip(),
                                    'pattern': pattern
                                })
                                print(f"  Threading issue: Ligne {i}: {line.strip()}")
            except Exception as e:
                print(f"Erreur: {e}")
    
    return threading_issues

def search_variable_errors():
    """Recherche les erreurs de variables non définies"""
    print("\nRecherche des erreurs de variables non définies...")
    
    # Patterns de variables qui pourraient causer des erreurs
    error_patterns = [
        r'\binit_price\b',
        r'\bunit_price\s*=',
        r'=\s*unit_price\b',
        r'\bprice\s*=',
        r'=\s*price\b'
    ]
    
    files_to_check = [
        'app/ui/views/purchasing/dialogs/order_item_dialog.py',
        'app/ui/views/purchasing/dialogs/purchase_order_dialog.py',
        'app/ui/views/repair/dialogs/used_parts_dialog.py',
        'app/core/services/purchasing_service.py',
        'app/core/services/repair_service.py'
    ]
    
    variable_errors = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n=== Analyse de {file_path} ===")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        if line.strip().startswith('#'):
                            continue
                            
                        for pattern in error_patterns:
                            if re.search(pattern, line):
                                # Vérifier que ce n'est pas un attribut
                                if not re.search(r'\w+\.' + pattern.replace(r'\b', '').replace('\\', ''), line):
                                    variable_errors.append({
                                        'file': file_path,
                                        'line': i,
                                        'content': line.strip(),
                                        'pattern': pattern
                                    })
                                    print(f"  Variable error: Ligne {i}: {line.strip()}")
            except Exception as e:
                print(f"Erreur: {e}")
    
    return variable_errors

def check_specific_methods():
    """Vérifie des méthodes spécifiques qui pourraient causer des erreurs"""
    print("\nVérification de méthodes spécifiques...")
    
    # Vérifier _update_price_from_product dans order_item_dialog.py
    file_path = 'app/ui/views/purchasing/dialogs/order_item_dialog.py'
    if os.path.exists(file_path):
        print(f"\n=== Analyse de _update_price_from_product ===")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Rechercher la méthode _update_price_from_product
                method_start = content.find('def _update_price_from_product')
                if method_start != -1:
                    # Trouver la fin de la méthode
                    lines = content[method_start:].split('\n')
                    method_lines = []
                    indent_level = None
                    
                    for line in lines:
                        if line.strip() == '':
                            method_lines.append(line)
                            continue
                            
                        current_indent = len(line) - len(line.lstrip())
                        
                        if indent_level is None and line.strip().startswith('def '):
                            indent_level = current_indent
                            method_lines.append(line)
                        elif indent_level is not None:
                            if current_indent > indent_level or line.strip() == '':
                                method_lines.append(line)
                            else:
                                break
                    
                    method_content = '\n'.join(method_lines)
                    print("Contenu de _update_price_from_product:")
                    print(method_content)
                    
                    # Rechercher des variables problématiques
                    if 'init_price' in method_content:
                        print("❌ TROUVÉ: 'init_price' dans _update_price_from_product")
                    if 'unit_price' in method_content and 'purchase_unit_price' not in method_content:
                        print("❌ TROUVÉ: 'unit_price' sans 'purchase_unit_price' dans _update_price_from_product")
                else:
                    print("Méthode _update_price_from_product non trouvée")
        except Exception as e:
            print(f"Erreur: {e}")

def main():
    """Fonction principale de débogage"""
    print("=== DÉBOGAGE DES ERREURS init_price ET THREADING ===")
    print("=" * 60)
    
    # Recherche init_price
    init_price_files = search_init_price_usage()
    
    # Recherche problèmes de threading
    threading_issues = search_threading_issues()
    
    # Recherche erreurs de variables
    variable_errors = search_variable_errors()
    
    # Vérification de méthodes spécifiques
    check_specific_methods()
    
    print(f"\n=== RÉSUMÉ ===")
    print(f"Usages de 'init_price' trouvés: {len(init_price_files)}")
    print(f"Problèmes de threading trouvés: {len(threading_issues)}")
    print(f"Erreurs de variables trouvées: {len(variable_errors)}")
    
    if init_price_files:
        print("\nFichiers avec 'init_price':")
        for item in init_price_files:
            print(f"  - {item['file']}:{item['line']} - {item['content']}")
    
    if threading_issues:
        print("\nProblèmes de threading:")
        for item in threading_issues:
            print(f"  - {item['file']}:{item['line']} - {item['content']}")
    
    if variable_errors:
        print("\nErreurs de variables:")
        for item in variable_errors:
            print(f"  - {item['file']}:{item['line']} - {item['content']}")

if __name__ == "__main__":
    main()
