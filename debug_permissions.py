#!/usr/bin/env python3
"""
Script de diagnostic pour vérifier les permissions des utilisateurs
"""
import asyncio
import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.user_service import UserService
from app.core.services.auth_service import AuthService
from app.core.services.audit_service import AuditService

async def debug_permissions():
    """Diagnostique les permissions des utilisateurs"""
    
    print("=== DIAGNOSTIC DES PERMISSIONS ===\n")
    
    # Créer une session de base de données
    db = SessionLocal()
    
    try:
        # Créer les services
        auth_service = AuthService(db)
        user_service = auth_service.user_service

        # Tester la connexion avec un utilisateur admin
        print("1. Test de connexion avec un utilisateur admin...")
        success, token, user_info = await auth_service.authenticate_user(
            email="<EMAIL>",
            password="password123",
            ip_address="127.0.0.1",
            user_agent="Debug Script"
        )
        
        if success:
            print(f"✅ Connexion réussie pour: {user_info['email']}")
            print(f"   Nom: {user_info['full_name']}")
            print(f"   Permissions: {user_info['permissions']}")
            print(f"   Type des permissions: {type(user_info['permissions'])}")
            
            # Tester des permissions spécifiques
            test_permissions = [
                'inventory.create',
                'repair.create', 
                'customer.create',
                'user.create',
                'system.settings'
            ]
            
            print(f"\n2. Test de permissions spécifiques:")
            for perm in test_permissions:
                has_perm = await user_service.check_permission(user_info['id'], perm)
                status = "✅ AUTORISÉ" if has_perm else "❌ REFUSÉ"
                print(f"   {perm}: {status}")
                
        else:
            print(f"❌ Échec de connexion: {user_info}")
            
        # Tester avec un utilisateur vendeur si il existe
        print(f"\n3. Test avec un utilisateur vendeur...")
        try:
            success_v, token_v, user_info_v = await auth_service.authenticate_user(
                email="<EMAIL>",
                password="password123",
                ip_address="127.0.0.1",
                user_agent="Debug Script"
            )
            
            if success_v:
                print(f"✅ Connexion vendeur réussie pour: {user_info_v['email']}")
                print(f"   Nom: {user_info_v['full_name']}")
                print(f"   Permissions: {user_info_v['permissions']}")
                print(f"   Type des permissions: {type(user_info_v['permissions'])}")
                
                print(f"\n4. Test de permissions vendeur:")
                for perm in test_permissions:
                    has_perm = await user_service.check_permission(user_info_v['id'], perm)
                    status = "✅ AUTORISÉ" if has_perm else "❌ REFUSÉ"
                    print(f"   {perm}: {status}")
            else:
                print(f"❌ Pas d'utilisateur vendeur ou échec de connexion")
                
        except Exception as e:
            print(f"❌ Erreur lors du test vendeur: {e}")
            
        # Lister tous les utilisateurs et leurs rôles
        print(f"\n5. Liste de tous les utilisateurs:")
        users = await user_service.get_all()
        for user in users:
            print(f"   - {user.email} ({user.full_name})")
            permissions = await user_service.get_user_permissions(user.id)
            print(f"     Permissions ({len(permissions)}): {permissions[:5]}{'...' if len(permissions) > 5 else ''}")
            
    except Exception as e:
        print(f"❌ Erreur lors du diagnostic: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_permissions())
