import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QToolBar, QWidget, QVBoxLayout, QLabel
from PyQt6.QtCore import Qt

# Importer la classe MainWindow
from app.ui.window import MainWindow
from app.controllers.auth_controller import AuthController

def debug_toolbar(window):
    """Affiche des informations de débogage sur la barre d'outils"""
    print("Débogage de la barre d'outils")
    
    # Trouver toutes les barres d'outils
    toolbars = window.findChildren(QToolBar)
    print(f"Nombre de barres d'outils: {len(toolbars)}")
    
    for i, toolbar in enumerate(toolbars):
        print(f"\nBarre d'outils #{i+1}: {toolbar.objectName()}")
        print(f"Orientation: {toolbar.orientation().name}")
        print(f"Nombre d'actions: {len(toolbar.actions())}")
        
        # Afficher les informations sur chaque action
        for j, action in enumerate(toolbar.actions()):
            print(f"  Action #{j+1}: {action.text() or 'Sans texte'} (Visible: {action.isVisible()}, Activée: {action.isEnabled()})")
            if action.isSeparator():
                print("    Type: Séparateur")
            else:
                print(f"    Type: Action normale")
                print(f"    Icône: {action.icon().isNull() and 'Aucune' or 'Présente'}")
        
        # Afficher les informations sur chaque widget
        widgets = []
        for j in range(toolbar.layout().count()):
            widget = toolbar.layout().itemAt(j).widget()
            if widget:
                widgets.append(widget)
        
        print(f"Nombre de widgets: {len(widgets)}")
        for j, widget in enumerate(widgets):
            print(f"  Widget #{j+1}: {widget.__class__.__name__} (Visible: {widget.isVisible()})")
            print(f"    Taille: {widget.size().width()}x{widget.size().height()}")
            print(f"    Position: ({widget.pos().x()}, {widget.pos().y()})")

# Fonction principale
def main():
    app = QApplication(sys.argv)
    
    # Créer une instance de AuthController (nécessaire pour MainWindow)
    auth_controller = AuthController()
    
    # Créer une instance de MainWindow
    window = MainWindow(auth_controller)
    
    # Déboguer la barre d'outils
    debug_toolbar(window)
    
    # Afficher la fenêtre
    window.show()
    
    # Exécuter l'application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
