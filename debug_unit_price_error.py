#!/usr/bin/env python3
"""
Script de débogage pour tracer l'erreur 'unit_price is not defined'
"""
import sys
import os
import ast
import re

def search_unit_price_usage():
    """Recherche tous les usages de 'unit_price' comme variable (pas attribut)"""
    print("Recherche de tous les usages de 'unit_price' comme variable...")
    
    # Répertoires à analyser
    directories = [
        'app/ui/views/purchasing',
        'app/ui/views/repair', 
        'app/core/services',
        'app/core/models'
    ]
    
    problematic_files = []
    
    for directory in directories:
        if os.path.exists(directory):
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                lines = content.split('\n')
                                
                                for i, line in enumerate(lines, 1):
                                    # Rechercher 'unit_price' utilisé comme variable (pas comme attribut)
                                    # Patterns problématiques:
                                    # - unit_price = ...
                                    # - ... = unit_price
                                    # - function(unit_price)
                                    # - unit_price.something (mais pas object.unit_price)
                                    
                                    # Exclure les commentaires
                                    if line.strip().startswith('#'):
                                        continue
                                    
                                    # Rechercher les patterns problématiques
                                    patterns = [
                                        r'\bunit_price\s*=',  # unit_price = ...
                                        r'=\s*unit_price\b',  # = unit_price
                                        r'\(.*unit_price.*\)',  # function(unit_price)
                                        r'\bunit_price\s*\+',  # unit_price +
                                        r'\bunit_price\s*\*',  # unit_price *
                                        r'\bunit_price\s*\-',  # unit_price -
                                        r'\bunit_price\s*/',   # unit_price /
                                        r'unit_price\s*,',     # unit_price,
                                    ]
                                    
                                    for pattern in patterns:
                                        if re.search(pattern, line):
                                            # Vérifier que ce n'est pas un attribut (object.unit_price)
                                            if not re.search(r'\w+\.unit_price', line):
                                                problematic_files.append({
                                                    'file': file_path,
                                                    'line': i,
                                                    'content': line.strip(),
                                                    'pattern': pattern
                                                })
                                                print(f"FOUND: {file_path}:{i} - {line.strip()}")
                        
                        except Exception as e:
                            print(f"Erreur lors de la lecture de {file_path}: {e}")
    
    return problematic_files

def search_in_specific_files():
    """Recherche dans des fichiers spécifiques qui pourraient causer le problème"""
    specific_files = [
        'app/ui/views/purchasing/dialogs/order_item_dialog.py',
        'app/ui/views/purchasing/dialogs/purchase_order_dialog.py',
        'app/ui/views/repair/dialogs/used_parts_dialog.py',
        'app/core/services/purchasing_service.py',
        'app/core/services/repair_service.py'
    ]
    
    print("\nRecherche dans des fichiers spécifiques...")
    
    for file_path in specific_files:
        if os.path.exists(file_path):
            print(f"\n=== Analyse de {file_path} ===")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        if 'unit_price' in line and not line.strip().startswith('#'):
                            print(f"  Ligne {i}: {line.strip()}")
            except Exception as e:
                print(f"Erreur: {e}")

def check_imports_and_dependencies():
    """Vérifie les imports et dépendances qui pourraient causer le problème"""
    print("\n=== Vérification des imports et dépendances ===")
    
    # Vérifier si des modules importent des fonctions qui utilisent unit_price
    files_to_check = [
        'app/ui/views/purchasing/dialogs/order_item_dialog.py',
        'app/ui/views/purchasing/dialogs/purchase_order_dialog.py'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\nImports dans {file_path}:")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # Extraire les imports
                    import_lines = [line for line in content.split('\n') if line.strip().startswith(('import ', 'from '))]
                    for import_line in import_lines:
                        print(f"  {import_line}")
                        
            except Exception as e:
                print(f"Erreur: {e}")

def main():
    """Fonction principale de débogage"""
    print("=== DÉBOGAGE DE L'ERREUR 'unit_price is not defined' ===")
    print("=" * 60)
    
    # Recherche générale
    problematic_files = search_unit_price_usage()
    
    # Recherche spécifique
    search_in_specific_files()
    
    # Vérification des imports
    check_imports_and_dependencies()
    
    print(f"\n=== RÉSUMÉ ===")
    print(f"Fichiers problématiques trouvés: {len(problematic_files)}")
    
    if problematic_files:
        print("\nFichiers à corriger:")
        for item in problematic_files:
            print(f"  - {item['file']}:{item['line']} - {item['content']}")
    else:
        print("Aucun usage problématique de 'unit_price' trouvé dans les fichiers analysés.")
        print("L'erreur pourrait venir d'un autre endroit ou d'une dépendance externe.")

if __name__ == "__main__":
    main()
