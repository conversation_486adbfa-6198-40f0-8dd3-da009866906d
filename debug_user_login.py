#!/usr/bin/env python3
"""
Script de diagnostic pour le problème de dernière connexion des utilisateurs
"""

import sys
import os
sys.path.append('.')

from datetime import datetime
import sqlite3
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

def check_database_direct():
    """Vérifier directement la base de données SQLite"""
    print("=== Vérification directe de la base de données SQLite ===")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # Vérifier la structure de la table users
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        print("Structure de la table users:")
        for col in columns:
            print(f"  {col[1]} ({col[2]}) - Nullable: {not col[3]}")
        
        # Vérifier l'utilisateur spécifique
        cursor.execute("SELECT id, email, last_login FROM users WHERE email = ?", ('<EMAIL>',))
        result = cursor.fetchone()
        
        if result:
            print(f"\nUtilisateur trouvé:")
            print(f"  ID: {result[0]}")
            print(f"  Email: {result[1]}")
            print(f"  Last_login: {result[2]} (type: {type(result[2])})")
        else:
            print("\nUtilisateur '<EMAIL>' non trouvé")
        
        # Vérifier tous les utilisateurs
        cursor.execute("SELECT id, email, last_login FROM users")
        all_users = cursor.fetchall()
        print(f"\nTous les utilisateurs ({len(all_users)}):")
        for user in all_users:
            print(f"  ID={user[0]}, Email={user[1]}, Last_login={user[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"Erreur lors de la vérification directe: {e}")

def check_database_sqlalchemy():
    """Vérifier via SQLAlchemy"""
    print("\n=== Vérification via SQLAlchemy ===")
    
    try:
        from app.utils.database import get_db
        from app.core.models.user import User
        
        # Créer une session
        db = next(get_db())
        
        # Vérifier l'utilisateur spécifique
        user = db.query(User).filter(User.email == '<EMAIL>').first()
        
        if user:
            print(f"Utilisateur trouvé:")
            print(f"  ID: {user.id}")
            print(f"  Email: {user.email}")
            print(f"  Last_login: {user.last_login} (type: {type(user.last_login)})")
            print(f"  Status: {user.status}")
            print(f"  Is_active: {user.is_active}")
        else:
            print("Utilisateur '<EMAIL>' non trouvé via SQLAlchemy")
        
        # Vérifier tous les utilisateurs
        all_users = db.query(User).all()
        print(f"\nTous les utilisateurs via SQLAlchemy ({len(all_users)}):")
        for user in all_users:
            print(f"  ID={user.id}, Email={user.email}, Last_login={user.last_login}")
        
        db.close()
        
    except Exception as e:
        print(f"Erreur lors de la vérification SQLAlchemy: {e}")
        import traceback
        traceback.print_exc()

def test_datetime_formatting():
    """Tester le formatage des dates"""
    print("\n=== Test du formatage des dates ===")
    
    # Tester différents types de valeurs
    test_values = [
        None,
        datetime.now(),
        "2024-01-15 10:30:00",
        "2024-01-15 10:30:00.123456",
        "2024-01-15",
        "invalid_date"
    ]
    
    for value in test_values:
        print(f"\nTest avec: {value} (type: {type(value)})")
        try:
            result = format_datetime_test(value)
            print(f"  Résultat: {result}")
        except Exception as e:
            print(f"  Erreur: {e}")

def format_datetime_test(dt):
    """Version de test de la méthode _format_datetime"""
    if dt is None:
        return "Jamais"
    
    if isinstance(dt, datetime):
        return dt.strftime("%d/%m/%Y %H:%M")
    
    if isinstance(dt, str):
        try:
            parsed_dt = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S.%f")
            return parsed_dt.strftime("%d/%m/%Y %H:%M")
        except ValueError:
            try:
                parsed_dt = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")
                return parsed_dt.strftime("%d/%m/%Y %H:%M")
            except ValueError:
                try:
                    parsed_dt = datetime.strptime(dt, "%Y-%m-%d")
                    return parsed_dt.strftime("%d/%m/%Y")
                except ValueError:
                    pass
    
    return str(dt)

def update_user_login():
    """Mettre à jour la date de dernière connexion pour test"""
    print("\n=== Mise à jour de la date de dernière connexion ===")
    
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        now = datetime.now()
        cursor.execute(
            "UPDATE users SET last_login = ? WHERE email = ?",
            (now.isoformat(), '<EMAIL>')
        )
        
        if cursor.rowcount > 0:
            print(f"Date de dernière connexion mise à <NAME_EMAIL>: {now}")
            conn.commit()
        else:
            print("Aucun utilisateur mis à jour")
        
        conn.close()
        
    except Exception as e:
        print(f"Erreur lors de la mise à jour: {e}")

if __name__ == "__main__":
    print("Diagnostic du problème de dernière connexion")
    print("=" * 50)
    
    check_database_direct()
    check_database_sqlalchemy()
    test_datetime_formatting()
    
    # Demander si on veut mettre à jour la date
    response = input("\nVoulez-vous mettre à jour la date de dernière connexion pour test? (y/N): ")
    if response.lower() == 'y':
        update_user_login()
        print("\nVérification après mise à jour:")
        check_database_direct()
