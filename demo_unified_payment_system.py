#!/usr/bin/env python3
"""
Démonstration du système unifié de paiements avec références et traçabilité
"""

import sys
import os
import asyncio
from datetime import datetime, timezone

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.finance_service import FinanceService
from app.core.models.customer import Customer
from app.core.models.repair import RepairOrder, PaymentMethod as RepairPaymentMethod
from app.core.models.sale import Sale, PaymentMethod as SalePaymentMethod
from app.core.models.treasury import PaymentMethod as TreasuryPaymentMethod


async def demo_unified_payment_workflow():
    """Démonstration complète du workflow de paiement unifié"""
    print("🎯 Démonstration du système unifié de paiements")
    print("=" * 70)
    
    db = SessionLocal()
    try:
        finance_service = FinanceService(db)
        
        # Trouver des entités pour la démo
        customer = db.query(Customer).first()
        repair = db.query(RepairOrder).first()
        sale = db.query(Sale).first()
        
        if not customer or not repair:
            print("❌ Données insuffisantes pour la démonstration")
            return
        
        print(f"👤 Client: {customer.name} (ID: {customer.id})")
        print(f"🔧 Réparation: #{repair.number} (ID: {repair.id})")
        if sale:
            print(f"🛒 Vente: ID {sale.id}")
        
        print("\n" + "=" * 70)
        print("📋 SCÉNARIO: Paiement d'une réparation depuis l'interface client")
        print("=" * 70)
        
        # Étape 1: État initial
        print(f"\n1️⃣  État initial:")
        print(f"   💰 Solde client: {customer.current_balance:.2f} DA")
        print(f"   🔧 Statut réparation: {repair.payment_status}")
        print(f"   💵 Total payé réparation: {repair.total_paid or 0:.2f} DA")
        print(f"   💸 Montant final réparation: {repair.final_amount or 0:.2f} DA")
        
        # Étape 2: Paiement via interface client
        print(f"\n2️⃣  Paiement de 200 DA via interface client...")
        
        try:
            customer_transaction, repair_payment = await finance_service.pay_customer_repair(
                customer_id=customer.id,
                repair_id=repair.id,
                amount=200.0,
                method=TreasuryPaymentMethod.cash,
                processed_by=1,
                reference_number=None  # Génération automatique
            )
            
            print(f"   ✅ Transaction client créée:")
            print(f"      - ID: {customer_transaction.id}")
            print(f"      - Référence: {customer_transaction.reference_number}")
            print(f"      - Montant: {customer_transaction.amount:.2f} DA")
            
            print(f"   ✅ Paiement réparation créé:")
            print(f"      - ID: {repair_payment.id}")
            print(f"      - Référence: {repair_payment.reference_number}")
            print(f"      - Montant: {repair_payment.amount:.2f} DA")
            
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            return
        
        # Étape 3: État après paiement
        print(f"\n3️⃣  État après paiement:")
        db.refresh(customer)
        db.refresh(repair)
        
        print(f"   💰 Nouveau solde client: {customer.current_balance:.2f} DA")
        print(f"   🔧 Nouveau statut réparation: {repair.payment_status}")
        print(f"   💵 Nouveau total payé réparation: {repair.total_paid or 0:.2f} DA")
        
        # Étape 4: Traçabilité
        print(f"\n4️⃣  Traçabilité des paiements:")
        
        # Rechercher par référence de transaction client
        payment_info = finance_service.find_payment_by_reference(customer_transaction.reference_number)
        if payment_info:
            print(f"   🔍 Transaction trouvée par référence {customer_transaction.reference_number}:")
            print(f"      - Type: {payment_info['type']}")
            print(f"      - Entités liées: {payment_info['related_entities']}")
        
        # Rechercher les paiements liés
        related_payments = finance_service.get_related_payments(repair_payment.reference_number)
        print(f"   🔗 Paiements liés trouvés: {len(related_payments)}")
        for i, payment in enumerate(related_payments, 1):
            print(f"      {i}. Type: {payment['type']}")
            if hasattr(payment['payment'], 'reference_number'):
                ref = payment['payment'].reference_number
            elif hasattr(payment['payment'], 'reference'):
                ref = payment['payment'].reference
            else:
                ref = "N/A"
            print(f"         Référence: {ref}")
            print(f"         Montant: {payment['payment'].amount:.2f} DA")
        
        # Étape 5: Vérification de cohérence
        print(f"\n5️⃣  Vérification de cohérence:")
        
        # Synchroniser les statuts (déjà fait automatiquement, mais on peut vérifier)
        repair_sync = await finance_service.sync_payment_status('repair', repair.id)
        print(f"   🔄 Synchronisation réparation: {'✅' if repair_sync else '❌'}")
        
        if sale:
            sale_sync = await finance_service.sync_payment_status('sale', sale.id)
            print(f"   🔄 Synchronisation vente: {'✅' if sale_sync else '❌'}")
        
        print("\n" + "=" * 70)
        print("🎉 Démonstration terminée avec succès !")
        print("=" * 70)
        
        print("\n📊 Résumé des fonctionnalités démontrées:")
        print("   ✅ Génération automatique de références unifiées")
        print("   ✅ Paiement depuis interface client avec double écriture")
        print("   ✅ Synchronisation automatique des statuts")
        print("   ✅ Traçabilité complète des opérations")
        print("   ✅ Cohérence entre paiements, clients et trésorerie")
        print("   ✅ Prévention des doublons par référence")
        
    except Exception as e:
        print(f"❌ Erreur lors de la démonstration: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def demo_reference_traceability():
    """Démonstration de la traçabilité par références"""
    print("\n🔍 Démonstration de la traçabilité par références")
    print("=" * 70)
    
    db = SessionLocal()
    try:
        finance_service = FinanceService(db)
        
        # Créer plusieurs paiements liés pour démontrer la traçabilité
        customer = db.query(Customer).first()
        repair = db.query(RepairOrder).first()
        
        if not customer or not repair:
            print("❌ Données insuffisantes")
            return
        
        print(f"📋 Création de plusieurs paiements pour la réparation #{repair.number}")
        
        # Paiement 1: Acompte
        print(f"\n1️⃣  Acompte de 100 DA...")
        payment1 = await finance_service.pay_repair(
            repair_id=repair.id,
            amount=100.0,
            method=RepairPaymentMethod.cash,
            processed_by=1
        )
        print(f"   ✅ Référence: {payment1.reference_number}")
        
        # Paiement 2: Complément
        print(f"\n2️⃣  Complément de 150 DA...")
        payment2 = await finance_service.pay_repair(
            repair_id=repair.id,
            amount=150.0,
            method=RepairPaymentMethod.credit_card,
            processed_by=1
        )
        print(f"   ✅ Référence: {payment2.reference_number}")
        
        # Transaction client liée
        print(f"\n3️⃣  Transaction client de 50 DA...")
        transaction = await finance_service.record_customer_transaction(
            customer_id=customer.id,
            amount=50.0,
            description="Ajustement pour réparation",
            repair_order_id=repair.id,
            processed_by=1
        )
        print(f"   ✅ Référence: {transaction.reference_number}")
        
        # Démonstration de la traçabilité
        print(f"\n🔍 Traçabilité complète:")
        
        # Rechercher tous les paiements liés à la réparation
        all_related = finance_service.get_related_payments(payment1.reference_number)
        print(f"   📊 Total d'opérations liées à cette réparation: {len(all_related)}")
        
        total_amount = 0.0
        for i, payment in enumerate(all_related, 1):
            amount = float(payment['payment'].amount)
            total_amount += amount
            
            if hasattr(payment['payment'], 'reference_number'):
                ref = payment['payment'].reference_number
            elif hasattr(payment['payment'], 'reference'):
                ref = payment['payment'].reference
            else:
                ref = "N/A"
            
            print(f"      {i}. {payment['type']}: {amount:.2f} DA (Réf: {ref})")
        
        print(f"   💰 Montant total tracé: {total_amount:.2f} DA")
        
        # Vérifier la cohérence
        db.refresh(repair)
        print(f"   🔧 Total payé dans la réparation: {repair.total_paid:.2f} DA")
        print(f"   ✅ Cohérence: {'OK' if abs(float(repair.total_paid) - total_amount) < 0.01 else 'ERREUR'}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def main():
    """Fonction principale de démonstration"""
    await demo_unified_payment_workflow()
    await demo_reference_traceability()
    
    print("\n" + "=" * 70)
    print("🚀 Le système unifié de paiements est opérationnel !")
    print("=" * 70)
    print("\n🎯 Avantages du système:")
    print("   • Références uniques pour tous les paiements")
    print("   • Traçabilité complète des opérations")
    print("   • Prévention automatique des doublons")
    print("   • Synchronisation des statuts en temps réel")
    print("   • Cohérence entre toutes les interfaces")
    print("   • Écriture automatique en trésorerie")


if __name__ == "__main__":
    asyncio.run(main())
