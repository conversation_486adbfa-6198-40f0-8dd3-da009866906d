#!/usr/bin/env python3
"""
Script de migration pour ajouter la colonne 'condition' à la table inventory_items
"""

import sqlite3
import sys
import os

def add_condition_column():
    """Ajoute la colonne 'condition' à la table inventory_items"""
    db_path = os.path.join("data", "app.db")
    
    if not os.path.exists(db_path):
        print(f"Base de données non trouvée à: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier si la colonne condition existe déjà
        cursor.execute("PRAGMA table_info(inventory_items)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'condition' in column_names:
            print("✓ La colonne 'condition' existe déjà")
            return True
        
        print("Ajout de la colonne 'condition' à la table inventory_items...")
        
        # Ajouter la colonne condition avec une valeur par défaut
        cursor.execute("ALTER TABLE inventory_items ADD COLUMN condition VARCHAR(10) DEFAULT 'new'")
        
        # Mettre à jour les valeurs existantes avec 'new' par défaut
        cursor.execute("UPDATE inventory_items SET condition = 'new' WHERE condition IS NULL")
        
        conn.commit()
        print("✓ Colonne 'condition' ajoutée avec succès")
        
        # Vérifier que l'ajout a fonctionné
        cursor.execute("PRAGMA table_info(inventory_items)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'condition' in column_names:
            print("✓ Vérification réussie: la colonne 'condition' est maintenant présente")
            return True
        else:
            print("✗ Erreur: la colonne 'condition' n'a pas été ajoutée")
            return False
            
        conn.close()
        
    except Exception as e:
        print(f"Erreur lors de l'ajout de la colonne: {e}")
        return False

if __name__ == "__main__":
    print("Migration: Ajout de la colonne 'condition' à inventory_items")
    print("=" * 60)
    
    if add_condition_column():
        print("\n✓ Migration réussie!")
        print("Vous pouvez maintenant relancer l'application.")
    else:
        print("\n✗ Migration échouée!")
        sys.exit(1)