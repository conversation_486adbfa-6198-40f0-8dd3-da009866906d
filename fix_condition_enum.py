#!/usr/bin/env python3
"""
Script pour corriger les valeurs de la colonne 'condition' dans inventory_items
pour correspondre aux valeurs de l'énumération ItemCondition
"""

import sqlite3
import sys
import os

def fix_condition_enum_values():
    """Corrige les valeurs de condition pour correspondre à l'énumération"""
    db_path = os.path.join("data", "app.db")
    
    if not os.path.exists(db_path):
        print(f"Base de données non trouvée à: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Correction des valeurs de condition dans inventory_items...")
        
        # Vérifier les valeurs actuelles
        cursor.execute("SELECT DISTINCT condition FROM inventory_items WHERE condition IS NOT NULL")
        current_values = [row[0] for row in cursor.fetchall()]
        print(f"Valeurs actuelles: {current_values}")
        
        # Corriger 'new' -> 'NEW'
        cursor.execute("UPDATE inventory_items SET condition = 'NEW' WHERE condition = 'new' OR condition IS NULL")
        updated_new = cursor.rowcount
        print(f"✓ {updated_new} enregistrements mis à jour de 'new' vers 'NEW'")
        
        # Corriger 'used' -> 'USED' s'il y en a
        cursor.execute("UPDATE inventory_items SET condition = 'USED' WHERE condition = 'used'")
        updated_used = cursor.rowcount
        print(f"✓ {updated_used} enregistrements mis à jour de 'used' vers 'USED'")
        
        # Vérifier les valeurs après correction
        cursor.execute("SELECT DISTINCT condition FROM inventory_items WHERE condition IS NOT NULL")
        new_values = [row[0] for row in cursor.fetchall()]
        print(f"Nouvelles valeurs: {new_values}")
        
        # Compter les enregistrements par condition
        cursor.execute("SELECT condition, COUNT(*) FROM inventory_items GROUP BY condition")
        counts = cursor.fetchall()
        print("Répartition des conditions:")
        for condition, count in counts:
            print(f"  - {condition}: {count} articles")
        
        conn.commit()
        print("✓ Corrections appliquées avec succès")
        return True
        
    except Exception as e:
        print(f"Erreur lors de la correction: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("Correction des valeurs d'énumération ItemCondition")
    print("=" * 50)
    
    if fix_condition_enum_values():
        print("\n✓ Correction réussie!")
        print("L'application devrait maintenant fonctionner correctement.")
    else:
        print("\n✗ Correction échouée!")
        sys.exit(1)