import sqlite3
from datetime import datetime
from passlib.context import CryptContext

# Utiliser la bonne base de données (celle utilisée par SQLAlchemy)
db_path = "data/app.db"
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print(f"=== Correction de la base de données {db_path} ===")

# Créer le contexte bcrypt
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Récupérer l'utilisateur <EMAIL>
cursor.execute("SELECT id, email, full_name, last_login, hashed_password FROM users WHERE email = ?", ('<EMAIL>',))
user = cursor.fetchone()

if not user:
    print("❌ Utilisateur <EMAIL> non trouvé")
    conn.close()
    exit()

user_id, email, full_name, last_login, current_hash = user
print(f"✅ Utilisateur trouvé: ID={user_id}, Email={email}, Nom={full_name}")

# Corriger le mot de passe avec bcrypt
password = "password123"
correct_hash = pwd_context.hash(password)

print("🔐 Mise à jour du mot de passe...")
cursor.execute("UPDATE users SET hashed_password = ?, updated_at = ? WHERE id = ?", 
               (correct_hash, datetime.now().isoformat(), user_id))

# Vérifier les rôles actuels
cursor.execute("""
    SELECT r.id, r.name, r.description 
    FROM roles r
    JOIN user_roles ur ON r.id = ur.role_id
    WHERE ur.user_id = ?
""", (user_id,))
current_roles = cursor.fetchall()

print(f"Rôles actuels ({len(current_roles)}):")
for role in current_roles:
    print(f"  - {role[1]} (ID: {role[0]}): {role[2]}")

# Récupérer le rôle admin
cursor.execute("SELECT id, name FROM roles WHERE name = 'admin'")
admin_role = cursor.fetchone()

if not admin_role:
    print("❌ Rôle admin non trouvé")
    conn.close()
    exit()

admin_role_id, admin_role_name = admin_role
print(f"✅ Rôle admin trouvé: ID={admin_role_id}")

# Supprimer tous les rôles existants de l'utilisateur
cursor.execute("DELETE FROM user_roles WHERE user_id = ?", (user_id,))
print(f"🗑️  {cursor.rowcount} rôle(s) existant(s) supprimé(s)")

# Ajouter le rôle admin
now = datetime.now().isoformat()
cursor.execute("""
    INSERT INTO user_roles (user_id, role_id, created_at, updated_at)
    VALUES (?, ?, ?, ?)
""", (user_id, admin_role_id, now, now))
print("✅ Rôle admin ajouté")

# Mettre à jour la date de dernière connexion
cursor.execute("UPDATE users SET last_login = ?, updated_at = ? WHERE id = ?", 
               (now, now, user_id))
print("✅ Date de dernière connexion mise à jour")

# Valider les modifications
conn.commit()
print("✅ Modifications sauvegardées")

# Vérification finale
print("\n=== Vérification finale ===")

# Vérifier l'utilisateur mis à jour
cursor.execute("SELECT id, email, full_name, last_login FROM users WHERE id = ?", (user_id,))
updated_user = cursor.fetchone()
print(f"Utilisateur: {updated_user[1]}")
print(f"  ID: {updated_user[0]}")
print(f"  Nom: {updated_user[2]}")
print(f"  Dernière connexion: {updated_user[3]}")

# Tester le mot de passe
cursor.execute("SELECT hashed_password FROM users WHERE id = ?", (user_id,))
stored_hash = cursor.fetchone()[0]
is_valid = pwd_context.verify(password, stored_hash)
print(f"  Test mot de passe: {'✅ RÉUSSI' if is_valid else '❌ ÉCHOUÉ'}")

# Vérifier les nouveaux rôles
cursor.execute("""
    SELECT r.id, r.name, r.description 
    FROM roles r
    JOIN user_roles ur ON r.id = ur.role_id
    WHERE ur.user_id = ?
""", (user_id,))
final_roles = cursor.fetchall()

print(f"Rôles finaux ({len(final_roles)}):")
for role in final_roles:
    print(f"  - {role[1]} (ID: {role[0]}): {role[2]}")
    
    # Vérifier les permissions de ce rôle
    cursor.execute("""
        SELECT p.code, p.name 
        FROM permissions p
        JOIN role_permission rp ON p.id = rp.permission_id
        WHERE rp.role_id = ?
    """, (role[0],))
    permissions = cursor.fetchall()
    
    print(f"    Permissions ({len(permissions)}):")
    for perm in permissions:
        print(f"      - {perm[0]}: {perm[1]}")

conn.close()
print("\n✅ Correction terminée avec succès")
print(f"La base de données {db_path} a été corrigée.")
