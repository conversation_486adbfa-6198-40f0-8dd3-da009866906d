#!/usr/bin/env python3
"""
Script pour corriger la date de dernière connexion des utilisateurs
"""

import sys
import os
sys.path.append('.')

from datetime import datetime
from app.utils.database import get_db
from app.core.models.user import User

def fix_user_login_date(email: str = None):
    """
    Corriger la date de dernière connexion pour un utilisateur spécifique ou tous les utilisateurs
    
    Args:
        email: Email de l'utilisateur (optionnel, si None traite tous les utilisateurs)
    """
    print("=== Correction des dates de dernière connexion ===")
    
    try:
        # Créer une session de base de données
        db = next(get_db())
        
        if email:
            # Traiter un utilisateur spécifique
            user = db.query(User).filter(User.email == email).first()
            if not user:
                print(f"Utilisateur avec l'email {email} non trouvé")
                return
            
            users_to_fix = [user]
        else:
            # Traiter tous les utilisateurs sans date de dernière connexion
            users_to_fix = db.query(User).filter(User.last_login.is_(None)).all()
        
        print(f"Nombre d'utilisateurs à traiter: {len(users_to_fix)}")
        
        for user in users_to_fix:
            print(f"\nTraitement de l'utilisateur: {user.email}")
            print(f"  Date actuelle: {user.last_login}")
            
            if user.last_login is None:
                # Définir une date par défaut (date de création du compte ou maintenant)
                default_date = user.created_at if hasattr(user, 'created_at') and user.created_at else datetime.utcnow()
                user.last_login = default_date
                
                print(f"  Nouvelle date: {user.last_login}")
                
                # Sauvegarder les modifications
                db.add(user)
        
        # Valider toutes les modifications
        db.commit()
        print(f"\n✅ Correction terminée avec succès pour {len(users_to_fix)} utilisateur(s)")
        
        # Vérifier les modifications
        print("\n=== Vérification ===")
        for user in users_to_fix:
            db.refresh(user)
            print(f"  {user.email}: {user.last_login}")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction: {e}")
        import traceback
        traceback.print_exc()

def check_user_status(email: str):
    """
    Vérifier le statut d'un utilisateur
    
    Args:
        email: Email de l'utilisateur
    """
    print(f"=== Vérification du statut de {email} ===")
    
    try:
        db = next(get_db())
        
        user = db.query(User).filter(User.email == email).first()
        if not user:
            print(f"Utilisateur avec l'email {email} non trouvé")
            return
        
        print(f"ID: {user.id}")
        print(f"Email: {user.email}")
        print(f"Nom complet: {user.full_name}")
        print(f"Statut: {user.status}")
        print(f"Actif: {user.is_active}")
        print(f"Dernière connexion: {user.last_login}")
        print(f"Tentatives échouées: {user.failed_login_attempts}")
        print(f"Dernière tentative échouée: {user.last_failed_login}")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "fix":
            email = sys.argv[2] if len(sys.argv) > 2 else None
            fix_user_login_date(email)
        elif command == "check":
            if len(sys.argv) > 2:
                check_user_status(sys.argv[2])
            else:
                print("Usage: python fix_user_login_date.py check <email>")
        else:
            print("Commandes disponibles:")
            print("  fix [email]  - Corriger la date de dernière connexion (pour un utilisateur ou tous)")
            print("  check <email> - Vérifier le statut d'un utilisateur")
    else:
        print("Usage:")
        print("  python fix_user_login_date.py fix [email]")
        print("  python fix_user_login_date.py check <email>")
        print("  python fix_user_login_date.py fix  # pour tous les utilisateurs")
