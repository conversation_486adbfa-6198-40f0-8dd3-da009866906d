import sqlite3
from datetime import datetime
from passlib.context import CryptContext

# Connexion directe à la base de données
conn = sqlite3.connect('app.db')
cursor = conn.cursor()

print("=== Correction du mot de passe utilisateur ===")

# C<PERSON>er le contexte bcrypt comme dans l'application
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Générer le hash correct pour le mot de passe
password = "password123"
correct_hash = pwd_context.hash(password)

print(f"Mot de passe: {password}")
print(f"Hash bcrypt généré: {correct_hash[:50]}...")

# Mettre à jour l'utilisateur avec le bon hash
try:
    now = datetime.now().isoformat()
    cursor.execute("""
        UPDATE users 
        SET hashed_password = ?, updated_at = ?
        WHERE email = ?
    """, (correct_hash, now, '<EMAIL>'))
    
    if cursor.rowcount > 0:
        conn.commit()
        print(f"✅ Mot de passe mis à <NAME_EMAIL>")
        
        # Vérifier la mise à jour
        cursor.execute("SELECT email, hashed_password FROM users WHERE email = ?", ('<EMAIL>',))
        result = cursor.fetchone()
        if result:
            print(f"✅ Vérification: utilisateur {result[0]} avec hash {result[1][:50]}...")
            
            # Tester la vérification du mot de passe
            is_valid = pwd_context.verify(password, result[1])
            print(f"✅ Test de vérification du mot de passe: {'RÉUSSI' if is_valid else 'ÉCHOUÉ'}")
        
    else:
        print("❌ Aucun utilisateur trouvé à mettre à jour")
        
except Exception as e:
    print(f"❌ Erreur lors de la mise à jour: {e}")
    conn.rollback()

conn.close()
print("\n=== Correction terminée ===")
