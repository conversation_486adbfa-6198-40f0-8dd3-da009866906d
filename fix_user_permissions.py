#!/usr/bin/env python3
"""
Script pour corriger les permissions de l'utilisateur <EMAIL>
"""

import sys
import os
sys.path.append('.')

from datetime import datetime

def fix_user_permissions():
    """
    Corriger les permissions de l'utilisateur <EMAIL>
    """
    print("=== Correction des permissions utilisateur ===")
    
    try:
        from app.utils.database import SessionLocal
        from app.core.models.user import User
        from app.core.models.permission import DBRole
        from app.core.models.user_role import UserRole
        
        # Créer une session de base de données
        db = SessionLocal()
        
        # Récupérer l'utilisateur
        user = db.query(User).filter(User.email == '<EMAIL>').first()
        if not user:
            print("❌ Utilisateur <EMAIL> non trouvé")
            return
        
        print(f"✅ Utilisateur trouvé: ID={user.id}, Email={user.email}, Nom={user.full_name}")
        
        # Vérifier les rôles actuels
        current_roles = db.query(UserRole).filter(UserRole.user_id == user.id).all()
        print(f"Rôles actuels ({len(current_roles)}):")
        for ur in current_roles:
            role = db.query(DBRole).get(ur.role_id)
            if role:
                print(f"  - {role.name} (ID: {role.id})")
        
        # Récupérer le rôle admin
        admin_role = db.query(DBRole).filter(DBRole.name == 'admin').first()
        if not admin_role:
            print("❌ Rôle admin non trouvé")
            return
        
        print(f"✅ Rôle admin trouvé: ID={admin_role.id}")
        
        # Vérifier si l'utilisateur a déjà le rôle admin
        existing_admin_role = db.query(UserRole).filter(
            UserRole.user_id == user.id,
            UserRole.role_id == admin_role.id
        ).first()
        
        if existing_admin_role:
            print("ℹ️  L'utilisateur a déjà le rôle admin")
        else:
            # Supprimer tous les rôles existants
            db.query(UserRole).filter(UserRole.user_id == user.id).delete()
            print("🗑️  Rôles existants supprimés")
            
            # Ajouter le rôle admin
            now = datetime.now()
            new_user_role = UserRole(
                user_id=user.id,
                role_id=admin_role.id,
                created_at=now,
                updated_at=now
            )
            db.add(new_user_role)
            print("✅ Rôle admin ajouté")
        
        # Mettre à jour la date de dernière connexion si elle est None
        if user.last_login is None:
            user.last_login = datetime.now()
            db.add(user)
            print("✅ Date de dernière connexion mise à jour")
        
        # Valider les modifications
        db.commit()
        print("✅ Modifications sauvegardées")
        
        # Vérification finale
        print("\n=== Vérification finale ===")
        
        # Rafraîchir l'utilisateur
        db.refresh(user)
        print(f"Utilisateur: {user.email}")
        print(f"  ID: {user.id}")
        print(f"  Nom: {user.full_name}")
        print(f"  Dernière connexion: {user.last_login}")
        
        # Vérifier les nouveaux rôles
        final_roles = db.query(UserRole).filter(UserRole.user_id == user.id).all()
        print(f"Rôles finaux ({len(final_roles)}):")
        for ur in final_roles:
            role = db.query(DBRole).get(ur.role_id)
            if role:
                print(f"  - {role.name} (ID: {role.id})")
                
                # Vérifier les permissions de ce rôle
                permissions = [p.code for p in role.permissions]
                print(f"    Permissions: {permissions}")
        
        db.close()
        print("\n✅ Correction terminée avec succès")
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_user_permissions()
