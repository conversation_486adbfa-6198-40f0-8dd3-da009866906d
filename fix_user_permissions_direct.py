import sqlite3
from datetime import datetime

# Connexion directe à la base de données
conn = sqlite3.connect('app.db')
cursor = conn.cursor()

print("=== Correction des permissions utilisateur (SQLite direct) ===")

# Récupérer l'utilisateur <EMAIL>
cursor.execute("SELECT id, email, full_name, last_login FROM users WHERE email = ?", ('<EMAIL>',))
user = cursor.fetchone()

if not user:
    print("❌ Utilisateur <EMAIL> non trouvé")
    conn.close()
    exit()

user_id, email, full_name, last_login = user
print(f"✅ Utilisateur trouvé: ID={user_id}, Email={email}, Nom={full_name}")

# Vérifier les rôles actuels
cursor.execute("""
    SELECT r.id, r.name, r.description 
    FROM roles r
    JOIN user_roles ur ON r.id = ur.role_id
    WHERE ur.user_id = ?
""", (user_id,))
current_roles = cursor.fetchall()

print(f"Rôles actuels ({len(current_roles)}):")
for role in current_roles:
    print(f"  - {role[1]} (ID: {role[0]}): {role[2]}")

# Récupérer le rôle admin
cursor.execute("SELECT id, name FROM roles WHERE name = 'admin'")
admin_role = cursor.fetchone()

if not admin_role:
    print("❌ Rôle admin non trouvé")
    conn.close()
    exit()

admin_role_id, admin_role_name = admin_role
print(f"✅ Rôle admin trouvé: ID={admin_role_id}")

# Vérifier si l'utilisateur a déjà le rôle admin
cursor.execute("SELECT 1 FROM user_roles WHERE user_id = ? AND role_id = ?", (user_id, admin_role_id))
has_admin_role = cursor.fetchone()

if has_admin_role:
    print("ℹ️  L'utilisateur a déjà le rôle admin")
else:
    # Supprimer tous les rôles existants de l'utilisateur
    cursor.execute("DELETE FROM user_roles WHERE user_id = ?", (user_id,))
    print(f"🗑️  {cursor.rowcount} rôle(s) existant(s) supprimé(s)")
    
    # Ajouter le rôle admin
    now = datetime.now().isoformat()
    cursor.execute("""
        INSERT INTO user_roles (user_id, role_id, created_at, updated_at)
        VALUES (?, ?, ?, ?)
    """, (user_id, admin_role_id, now, now))
    print("✅ Rôle admin ajouté")

# Mettre à jour la date de dernière connexion si elle est None
if last_login is None:
    now = datetime.now().isoformat()
    cursor.execute("UPDATE users SET last_login = ?, updated_at = ? WHERE id = ?", (now, now, user_id))
    print("✅ Date de dernière connexion mise à jour")

# Valider les modifications
conn.commit()
print("✅ Modifications sauvegardées")

# Vérification finale
print("\n=== Vérification finale ===")

# Vérifier l'utilisateur mis à jour
cursor.execute("SELECT id, email, full_name, last_login FROM users WHERE id = ?", (user_id,))
updated_user = cursor.fetchone()
print(f"Utilisateur: {updated_user[1]}")
print(f"  ID: {updated_user[0]}")
print(f"  Nom: {updated_user[2]}")
print(f"  Dernière connexion: {updated_user[3]}")

# Vérifier les nouveaux rôles
cursor.execute("""
    SELECT r.id, r.name, r.description 
    FROM roles r
    JOIN user_roles ur ON r.id = ur.role_id
    WHERE ur.user_id = ?
""", (user_id,))
final_roles = cursor.fetchall()

print(f"Rôles finaux ({len(final_roles)}):")
for role in final_roles:
    print(f"  - {role[1]} (ID: {role[0]}): {role[2]}")
    
    # Vérifier les permissions de ce rôle
    cursor.execute("""
        SELECT p.code, p.name 
        FROM permissions p
        JOIN role_permission rp ON p.id = rp.permission_id
        WHERE rp.role_id = ?
    """, (role[0],))
    permissions = cursor.fetchall()
    
    print(f"    Permissions ({len(permissions)}):")
    for perm in permissions:
        print(f"      - {perm[0]}: {perm[1]}")

conn.close()
print("\n✅ Correction terminée avec succès")
