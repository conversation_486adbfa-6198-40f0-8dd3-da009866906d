from sqlalchemy import create_engine, text
from app.core.models.config import BaseDBModel
from sqlalchemy.orm import sessionmaker
import os
import sys
from pathlib import Path

# Ajouter le répertoire parent au chemin de recherche des modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# Importer tous les modèles pour s'assurer qu'ils sont enregistrés auprès de SQLAlchemy
from app.core.models import (
    user, customer, supplier, permission, user_role, audit, inventory, repair, repair_note, repair_photo, repair_status_history, maintenance, notification, purchasing, sale, supplier_finance
)

# Créer la connexion à la base de données
SQLALCHEMY_DATABASE_URL = "sqlite:///app.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    """Crée toutes les tables dans la base de données via la base déclarative principale"""
    print(f"Chemin de la base de données utilisé : {engine.url}")
    print("Création de toutes les tables...")
    try:
        # Créer toutes les tables
        BaseDBModel.metadata.create_all(bind=engine)
        print("Toutes les tables ont été créées avec succès.")
        
        # Vérifier si la table repair_notes existe
        with engine.connect() as conn:
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='repair_notes'"))
            if result.fetchone() is None:
                print("La table repair_notes n'existe pas, création explicite...")
                conn.execute(text("""
                    CREATE TABLE repair_notes (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        repair_id INTEGER NOT NULL,
                        title VARCHAR NOT NULL,
                        content TEXT NOT NULL,
                        note_type VARCHAR(20) NOT NULL DEFAULT 'other',
                        is_private BOOLEAN DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        created_by INTEGER,
                        updated_at DATETIME,
                        updated_by INTEGER,
                        FOREIGN KEY (repair_id) REFERENCES repair_orders (id),
                        FOREIGN KEY (created_by) REFERENCES users (id),
                        FOREIGN KEY (updated_by) REFERENCES users (id)
                    )
                """))
                conn.commit()
                print("Table repair_notes créée avec succès.")
            else:
                print("La table repair_notes existe déjà.")
    except Exception as e:
        print(f"Erreur lors de la création des tables: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_tables() 