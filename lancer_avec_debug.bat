@echo off
echo ===== DEMARRAGE DE L'APPLICATION AVEC DEBOGAGE =====
echo.

:: Définir les variables d'environnement pour le débogage Qt
set QT_DEBUG_PLUGINS=1
set QT_LOGGING_RULES=qt.qpa.*=true

:: Créer le répertoire de logs si nécessaire
if not exist logs mkdir logs

:: Vérifier si PyQt6.QtCharts est installé
echo Verification des dependances PyQt6...
python -c "import PyQt6.QtCharts" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Module PyQt6.QtCharts non trouve. Installation en cours...
    pip install PyQt6-Charts
    if %ERRORLEVEL% NEQ 0 (
        echo ERREUR: Impossible d'installer PyQt6.QtCharts
        pause
        exit /b 1
    )
    echo Module PyQt6.QtCharts installe avec succes.
) else (
    echo Module PyQt6.QtCharts deja installe.
)

:: D<PERSON>terminer le chemin de l'exécutable
set EXE_PATH=
if exist dist\main.dist\main.exe (
    set EXE_PATH=dist\main.dist\main.exe
) else if exist dist\main_patched.dist\main_patched.exe (
    set EXE_PATH=dist\main_patched.dist\main_patched.exe
) else if exist dist\main_debug.dist\main_debug.exe (
    set EXE_PATH=dist\main_debug.dist\main_debug.exe
)

if "%EXE_PATH%"=="" (
    echo ERREUR: Executable non trouve!
    echo Veuillez d'abord compiler l'application avec build_with_nuitka_debug.py
    pause
    exit /b 1
)

echo Executable trouve: %EXE_PATH%
echo.

:: Créer un fichier de log avec horodatage
set TIMESTAMP=%date:~6,4%%date:~3,2%%date:~0,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set LOG_FILE=logs\app_debug_%TIMESTAMP%.log

echo Demarrage de l'application avec options de debogage...
echo Les logs seront enregistres dans %LOG_FILE%
echo.

:: Lancer l'exécutable avec redirection des sorties vers le fichier de log
echo ===== DEBUT DU LOG (%date% %time%) ===== > %LOG_FILE%
echo Executable: %EXE_PATH% >> %LOG_FILE%
echo Options: --debug >> %LOG_FILE%
echo. >> %LOG_FILE%

%EXE_PATH% --debug >> %LOG_FILE% 2>&1

set EXIT_CODE=%ERRORLEVEL%
echo. >> %LOG_FILE%
echo ===== FIN DU LOG (Code de sortie: %EXIT_CODE%) ===== >> %LOG_FILE%

if %EXIT_CODE% NEQ 0 (
    echo.
    echo ATTENTION: L'application s'est terminee avec le code d'erreur %EXIT_CODE%
    echo Consultez le fichier de log pour plus de details: %LOG_FILE%
    echo.
    echo Execution des scripts de diagnostic...
    
    :: Exécuter les scripts de diagnostic
    python debug_executable.py
    python test_pyqt_initialization.py
    python check_resources.py
    python check_missing_modules.py
) else (
    echo.
    echo L'application s'est terminee normalement.
    echo Consultez le fichier de log pour plus de details: %LOG_FILE%
)

echo.
echo ===== FIN DU DEMARRAGE =====
pause