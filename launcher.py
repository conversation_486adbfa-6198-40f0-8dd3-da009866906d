import os
import sys
import subprocess
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('launcher.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("launcher")

def setup_environment():
    """Configure l'environnement pour l'exécution de l'application"""
    logger.info("Configuration de l'environnement...")
    
    # Obtenir le chemin du répertoire courant
    current_dir = os.path.dirname(os.path.abspath(__file__))
    logger.info(f"Répertoire courant: {current_dir}")
    
    # Ajouter le répertoire courant au PATH
    os.environ["PATH"] = current_dir + os.pathsep + os.environ.get("PATH", "")
    
    # Ajouter les chemins des DLLs Qt au PATH
    qt_dirs = [
        os.path.join(current_dir, "PyQt6"),
        os.path.join(current_dir, "PyQt6", "Qt6", "bin"),
        os.path.join(current_dir, "PyQt6", "Qt6", "plugins"),
    ]
    
    for qt_dir in qt_dirs:
        if os.path.exists(qt_dir):
            logger.info(f"Ajout au PATH: {qt_dir}")
            os.environ["PATH"] = qt_dir + os.pathsep + os.environ["PATH"]
    
    # Configurer QT_PLUGIN_PATH
    qt_plugin_path = os.path.join(current_dir, "PyQt6", "Qt6", "plugins")
    if os.path.exists(qt_plugin_path):
        logger.info(f"Configuration de QT_PLUGIN_PATH: {qt_plugin_path}")
        os.environ["QT_PLUGIN_PATH"] = qt_plugin_path
    
    # Vérifier les répertoires essentiels
    essential_dirs = ["config", "data", "logs", "backups", "output"]
    for dir_name in essential_dirs:
        dir_path = os.path.join(current_dir, dir_name)
        if not os.path.exists(dir_path):
            logger.warning(f"Création du répertoire manquant: {dir_name}")
            os.makedirs(dir_path, exist_ok=True)
    
    logger.info("Configuration de l'environnement terminée")
    return True

def launch_application():
    """Lance l'application principale"""
    logger.info("Lancement de l'application...")
    
    # Chemin de l'exécutable
    executable_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.exe")
    
    if not os.path.exists(executable_path):
        logger.error(f"L'exécutable {executable_path} n'existe pas")
        print(f"Erreur: L'exécutable {executable_path} n'a pas été trouvé.")
        return False
    
    try:
        logger.info(f"Exécution de {executable_path}")
        # Lancer l'application avec les variables d'environnement configurées
        process = subprocess.Popen([executable_path])
        return True
    except Exception as e:
        logger.error(f"Erreur lors du lancement de l'application: {str(e)}")
        print(f"Erreur lors du lancement de l'application: {str(e)}")
        return False

def main():
    """Fonction principale"""
    print("Démarrage de l'application Nadjib-GSM...")
    
    # Configurer l'environnement
    if not setup_environment():
        print("Erreur lors de la configuration de l'environnement.")
        input("Appuyez sur Entrée pour quitter...")
        return 1
    
    # Lancer l'application
    if not launch_application():
        print("Erreur lors du lancement de l'application.")
        print("Consultez le fichier launcher.log pour plus d'informations.")
        input("Appuyez sur Entrée pour quitter...")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        logger.error(f"Erreur non gérée: {str(e)}")
        print(f"Erreur non gérée: {str(e)}")
        input("Appuyez sur Entrée pour quitter...")
        sys.exit(1)