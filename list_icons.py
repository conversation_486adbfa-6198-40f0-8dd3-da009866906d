import os

def list_files(directory):
    """List all files in a directory and its subdirectories."""
    for root, dirs, files in os.walk(directory):
        level = root.replace(directory, '').count(os.sep)
        indent = ' ' * 4 * level
        print(f"{indent}{os.path.basename(root)}/")
        sub_indent = ' ' * 4 * (level + 1)
        for file in files:
            print(f"{sub_indent}{file}")

# List all files in the icons directory
print("Icons directory structure:")
list_files("app/ui/resources/icons")

# List all files in the assets directory if it exists
if os.path.exists("app/ui/assets"):
    print("\nAssets directory structure:")
    list_files("app/ui/assets")
