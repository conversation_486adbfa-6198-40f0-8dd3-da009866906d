2025-05-18 10:23:54,317 - missing_modules_check - INFO - === DÉBUT DE LA VÉRIFICATION DES MODULES MANQUANTS ===
2025-05-18 10:23:54,331 - missing_modules_check - INFO - Python version: 3.12.0 (tags/v3.12.0:0fb18b0, Oct  2 2023, 13:03:39) [MSC v.1935 64 bit (AMD64)]
2025-05-18 10:23:54,347 - missing_modules_check - INFO - Executable: C:\Users\<USER>\Desktop\Gestion_v3\.venv\Scripts\python.exe
2025-05-18 10:23:54,349 - missing_modules_check - INFO - Répertoire courant: C:\Users\<USER>\Desktop\Gestion_v3
2025-05-18 10:23:54,367 - missing_modules_check - INFO - Vérification de l'installation de PyQt6...
2025-05-18 10:23:54,380 - missing_modules_check - INFO - Version de PyQt6: Inconnue
2025-05-18 10:23:54,381 - missing_modules_check - INFO - Chemin d'installation de PyQt6: C:\Users\<USER>\Desktop\Gestion_v3\.venv\Lib\site-packages\PyQt6\__init__.py
2025-05-18 10:23:54,416 - missing_modules_check - INFO - Sous-modules de base importés avec succès
2025-05-18 10:23:54,416 - missing_modules_check - INFO - ✓ Sous-module QtCharts importé avec succès
2025-05-18 10:23:54,416 - missing_modules_check - INFO - Vérification du module PyQt6.QtCharts...
2025-05-18 10:23:54,416 - missing_modules_check - INFO - ✓ Module PyQt6 importé avec succès
2025-05-18 10:23:54,416 - missing_modules_check - INFO - ✓ Module PyQt6.QtCharts importé avec succès
2025-05-18 10:23:54,416 - missing_modules_check - INFO - ✓ Classes QChart et QChartView importées avec succès
2025-05-18 10:23:54,416 - missing_modules_check - INFO - Vérification des modules essentiels...
2025-05-18 10:23:54,416 - missing_modules_check - INFO - ✓ Module PyQt6 importé avec succès
2025-05-18 10:23:54,416 - missing_modules_check - INFO - ✓ Module PyQt6.QtCore importé avec succès
2025-05-18 10:23:54,416 - missing_modules_check - INFO - ✓ Module PyQt6.QtGui importé avec succès
2025-05-18 10:23:54,416 - missing_modules_check - INFO - ✓ Module PyQt6.QtWidgets importé avec succès
2025-05-18 10:23:54,416 - missing_modules_check - INFO - ✓ Module PyQt6.QtCharts importé avec succès
2025-05-18 10:23:54,748 - missing_modules_check - INFO - ✓ Module sqlalchemy importé avec succès
2025-05-18 10:23:54,761 - missing_modules_check - INFO - ✓ Module apscheduler importé avec succès
2025-05-18 10:23:54,761 - missing_modules_check - INFO - ✓ Module passlib importé avec succès
2025-05-18 10:23:54,761 - missing_modules_check - ERROR - ✗ Erreur d'importation du module toml: No module named 'toml'
2025-05-18 10:23:54,770 - missing_modules_check - INFO - 
Résumé des vérifications:
2025-05-18 10:23:54,770 - missing_modules_check - INFO - ✓ PyQt6
2025-05-18 10:23:54,771 - missing_modules_check - INFO - ✓ PyQt6.QtCore
2025-05-18 10:23:54,771 - missing_modules_check - INFO - ✓ PyQt6.QtGui
2025-05-18 10:23:54,771 - missing_modules_check - INFO - ✓ PyQt6.QtWidgets
2025-05-18 10:23:54,771 - missing_modules_check - INFO - ✓ PyQt6.QtCharts
2025-05-18 10:23:54,771 - missing_modules_check - INFO - ✓ sqlalchemy
2025-05-18 10:23:54,772 - missing_modules_check - INFO - ✓ apscheduler
2025-05-18 10:23:54,773 - missing_modules_check - INFO - ✓ passlib
2025-05-18 10:23:54,773 - missing_modules_check - INFO - ✗ toml
2025-05-18 10:23:54,773 - missing_modules_check - INFO - 
Suggestions de corrections:
2025-05-18 10:23:54,773 - missing_modules_check - INFO - PyQt6.QtCharts est correctement installé
2025-05-18 10:23:54,773 - missing_modules_check - INFO - Suggestions générales:
2025-05-18 10:23:54,773 - missing_modules_check - INFO - 1. Vérifier que toutes les dépendances sont installées: pip install -r requirements.txt
2025-05-18 10:23:54,773 - missing_modules_check - INFO - 2. Recompiler l'application avec le script build_with_nuitka_debug.py
2025-05-18 10:23:54,773 - missing_modules_check - INFO - 3. Vérifier les variables d'environnement: QT_DEBUG_PLUGINS=1 QT_LOGGING_RULES=qt.qpa.*=true
2025-05-18 10:23:54,773 - missing_modules_check - INFO - === FIN DE LA VÉRIFICATION DES MODULES MANQUANTS ===
2025-05-18 10:23:54,773 - missing_modules_check - ERROR - Certains modules essentiels sont manquants
