import sys
from PyQt6.QtWidgets import QApplication
from app.utils.database import init_db
from app.utils.db_migration import run_migrations
from app.utils.init_permissions import run_init
from apscheduler.schedulers.background import BackgroundScheduler
from app.core.tasks import schedule_tasks
from app.app_manager import AppManager
import logging

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('app.log'),
            logging.StreamHandler()
        ]
    )

def main():
    # Configuration du logging
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Démarrage de l'application")

    try:
        # Initialisation de la base de données
        init_db()

        # Exécution des migrations de la base de données
        run_migrations()

        # Initialisation des permissions et rôles
        run_init()
        logger.info("Permissions et rôles initialisés")

        # L'initialisation de SQLAlchemy est déjà faite dans init_db()

        # Initialisation du planificateur de tâches
        scheduler = BackgroundScheduler()
        schedule_tasks(scheduler)
        scheduler.start()
        logger.info("Planificateur de tâches démarré")

        # Initialisation de l'API REST
        # init_api()

        # Création de l'application Qt
        app = QApplication(sys.argv)

        # Définir l'icône de l'application
        from PyQt6.QtGui import QIcon
        app_icon = QIcon("app/ui/resources/images/app.ico")
        app.setWindowIcon(app_icon)

        # Création du gestionnaire d'application
        app_manager = AppManager(app)

        # Démarrer l'application
        app_manager.start()
        logger.info("Application démarrée")

        # Exécution de l'application
        sys.exit(app.exec())

    except Exception as e:
        logger.error(f"Erreur lors du démarrage: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()











