import sys
import os
import traceback
import logging
from datetime import datetime

# Vérifier si nous sommes dans un environnement compilé
def is_frozen():
    return getattr(sys, 'frozen', False)

# Configuration du logging avancé
def setup_logging():
    # Créer un répertoire logs si nécessaire
    logs_dir = 'logs'
    if not os.path.exists(logs_dir):
        try:
            os.makedirs(logs_dir)
        except Exception:
            logs_dir = ''
    
    # Nom du fichier de log avec timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(logs_dir, f'app_{timestamp}.log') if logs_dir else f'app_{timestamp}.log'
    
    try:
        logging.basicConfig(
            level=logging.DEBUG if '--debug' in sys.argv else logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return log_file
    except Exception as e:
        # Fallback en cas d'erreur
        print(f"Erreur lors de la configuration du logging: {str(e)}")
        logging.basicConfig(
            level=logging.DEBUG if '--debug' in sys.argv else logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        return None

# Fonction pour afficher une boîte de dialogue d'erreur
def show_error_dialog(message, details=None):
    try:
        from PyQt6.QtWidgets import QApplication, QMessageBox
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
            
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle("Erreur")
        msg_box.setText(message)
        if details:
            msg_box.setDetailedText(details)
        msg_box.exec()
    except Exception as e:
        print(f"Erreur lors de l'affichage de la boîte de dialogue: {str(e)}")
        print(f"Message d'erreur original: {message}")
        if details:
            print(f"Détails: {details}")

# Fonction pour vérifier l'environnement d'exécution
def check_environment(logger):
    logger.info("=== Informations sur l'environnement ===")
    logger.info(f"Exécutable: {sys.executable}")
    logger.info(f"Répertoire courant: {os.getcwd()}")
    logger.info(f"Chemin du script: {os.path.abspath(__file__)}")
    logger.info(f"Environnement compilé: {'Oui' if is_frozen() else 'Non'}")
    logger.info(f"Arguments: {sys.argv}")
    
    # Vérifier les chemins critiques
    critical_paths = [
        "config",
        "config/settings.toml",
        "data",
        "app",
        "app/ui/resources"
    ]
    
    for path in critical_paths:
        full_path = os.path.abspath(path)
        exists = os.path.exists(full_path)
        logger.info(f"Chemin '{path}': {'Existe' if exists else 'N\'existe pas'}")

# Fonction principale
def main():
    # Vérifier le mode test
    test_mode = "--test-mode" in sys.argv
    debug_mode = "--debug" in sys.argv
    
    # Configuration du logging
    log_file = setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=== Démarrage de l'application ===")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Mode test: {test_mode}")
    logger.info(f"Mode debug: {debug_mode}")
    
    if test_mode:
        logger.info("Application démarrée en mode test")
        return 0
    
    # Vérifier l'environnement
    check_environment(logger)
    
    try:
        # Vérifier les chemins critiques
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config", "settings.toml")
        if not os.path.exists(config_path):
            error_msg = f"Fichier de configuration non trouvé: {config_path}"
            logger.error(error_msg)
            show_error_dialog("Erreur de configuration", error_msg)
            return 1
        
        logger.info("Importation des modules nécessaires...")
        try:
            from app.utils.database import init_db
            from app.utils.db_migration import run_migrations
            from app.utils.init_permissions import run_init
            from apscheduler.schedulers.background import BackgroundScheduler
            from app.core.tasks import schedule_tasks
            from app.app_manager import AppManager
            from PyQt6.QtWidgets import QApplication
            
            logger.info("Tous les modules ont été importés avec succès")
        except ImportError as e:
            error_msg = f"Erreur lors de l'importation des modules: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("Erreur d'importation", error_msg)
            return 1
        
        # Initialisation de la base de données
        logger.info("Initialisation de la base de données...")
        try:
            init_db()
            logger.info("Base de données initialisée avec succès")
        except Exception as e:
            error_msg = f"Erreur lors de l'initialisation de la base de données: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("Erreur de base de données", error_msg)
            return 1

        # Exécution des migrations de la base de données
        logger.info("Exécution des migrations...")
        try:
            run_migrations()
            logger.info("Migrations exécutées avec succès")
        except Exception as e:
            error_msg = f"Erreur lors des migrations: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("Erreur de migration", error_msg)
            return 1

        # Initialisation des permissions et rôles
        logger.info("Initialisation des permissions et rôles...")
        try:
            run_init()
            logger.info("Permissions et rôles initialisés avec succès")
        except Exception as e:
            error_msg = f"Erreur lors de l'initialisation des permissions: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("Erreur d'initialisation", error_msg)
            return 1

        # Initialisation du planificateur de tâches
        logger.info("Initialisation du planificateur de tâches...")
        try:
            scheduler = BackgroundScheduler()
            schedule_tasks(scheduler)
            scheduler.start()
            logger.info("Planificateur de tâches démarré avec succès")
        except Exception as e:
            error_msg = f"Erreur lors de l'initialisation du planificateur: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("Erreur de planification", error_msg)
            return 1

        # Création de l'application Qt
        logger.info("Création de l'application Qt...")
        try:
            app = QApplication(sys.argv)
            logger.info("Application Qt créée avec succès")
        except Exception as e:
            error_msg = f"Erreur lors de la création de l'application Qt: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("Erreur d'interface", error_msg)
            return 1

        # Création du gestionnaire d'application
        logger.info("Création du gestionnaire d'application...")
        try:
            app_manager = AppManager(app)
            logger.info("Gestionnaire d'application créé avec succès")
        except Exception as e:
            error_msg = f"Erreur lors de la création du gestionnaire: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("Erreur de gestionnaire", error_msg)
            return 1

        # Démarrer l'application
        logger.info("Démarrage de l'application...")
        try:
            app_manager.start()
            logger.info("Application démarrée avec succès")
        except Exception as e:
            error_msg = f"Erreur lors du démarrage de l'application: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("Erreur de démarrage", error_msg)
            return 1

        # Exécution de l'application
        logger.info("Exécution de l'application...")
        try:
            return app.exec()
        except Exception as e:
            error_msg = f"Erreur lors de l'exécution de l'application: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            show_error_dialog("Erreur d'exécution", error_msg)
            return 1

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Erreur non gérée: {str(e)}")
        logger.error(error_details)
        show_error_dialog(f"Erreur non gérée: {str(e)}", error_details)
        return 1
    finally:
        logger.info(f"Fichier de log: {log_file}")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)