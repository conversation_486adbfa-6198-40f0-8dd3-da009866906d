import sys
import os
import traceback
from PyQt6.QtWidgets import QApplication, QMessageBox
from app.utils.database import init_db
from app.utils.db_migration import run_migrations
from app.utils.init_permissions import run_init
from apscheduler.schedulers.background import BackgroundScheduler
from app.core.tasks import schedule_tasks
from app.app_manager import AppManager
import logging

def setup_logging():
    log_file = 'app.log'
    try:
        # Assurer que le répertoire du fichier de log existe
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    except Exception as e:
        # Fallback en cas d'erreur avec le fichier de log
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler()
            ]
        )
        print(f"Erreur lors de la configuration du logging: {str(e)}")

def show_error_dialog(message, details=None):
    """Affiche une boîte de dialogue d'erreur"""
    try:
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
            
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle("Erreur")
        msg_box.setText(message)
        if details:
            msg_box.setDetailedText(details)
        msg_box.exec()
    except Exception as e:
        print(f"Erreur lors de l'affichage de la boîte de dialogue: {str(e)}")
        print(f"Message d'erreur original: {message}")
        if details:
            print(f"Détails: {details}")

def main():
    # Vérifier le mode test
    test_mode = "--test-mode" in sys.argv
    if test_mode:
        print("Application démarrée en mode test")
        return 0
        
    # Configuration du logging
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Démarrage de l'application")

    try:
        # Vérifier les chemins critiques
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config", "settings.toml")
        if not os.path.exists(config_path):
            error_msg = f"Fichier de configuration non trouvé: {config_path}"
            logger.error(error_msg)
            show_error_dialog("Erreur de configuration", error_msg)
            return 1
            
        # Initialisation de la base de données
        init_db()

        # Exécution des migrations de la base de données
        run_migrations()

        # Initialisation des permissions et rôles
        run_init()
        logger.info("Permissions et rôles initialisés")

        # Initialisation du planificateur de tâches
        scheduler = BackgroundScheduler()
        schedule_tasks(scheduler)
        scheduler.start()
        logger.info("Planificateur de tâches démarré")

        # Création de l'application Qt
        app = QApplication(sys.argv)

        # Création du gestionnaire d'application
        app_manager = AppManager(app)

        # Démarrer l'application
        app_manager.start()
        logger.info("Application démarrée")

        # Exécution de l'application
        return app.exec()

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Erreur lors du démarrage: {str(e)}")
        logger.error(error_details)
        show_error_dialog(f"Erreur lors du démarrage: {str(e)}", error_details)
        return 1

if __name__ == "__main__":
    sys.exit(main())