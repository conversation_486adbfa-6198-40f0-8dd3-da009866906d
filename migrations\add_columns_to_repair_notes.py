"""
Migration compatible SQLite pour ajouter toutes les colonnes attendues à la table repair_notes.
"""
import os
from glob import glob
import sqlite3

# Dossiers à scanner
DB_PATHS = [
    'app.db',
    os.path.join('data', '*.db'),
    os.path.join('backups', '*.db'),
]

# Colonnes à ajouter (nom, type SQL)
COLUMNS = [
    ('note_type', "VARCHAR(20) DEFAULT 'other'"),
    ('is_private', 'BOOLEAN DEFAULT FALSE'),
    ('created_by', 'INTEGER'),
    ('created_at', 'DATETIME'),
    ('updated_by', 'INTEGER'),
    ('updated_at', 'DATETIME'),
]

def get_db_files():
    db_files = set()
    for path in DB_PATHS:
        db_files.update(glob(path))
    return sorted(db_files)

def column_exists(cursor, table, column):
    cursor.execute(f"PRAGMA table_info({table})")
    return any(row[1] == column for row in cursor.fetchall())

def add_column(cursor, table, column, col_type):
    print(f"  - Ajout de la colonne {column} à {table}...")
    cursor.execute(f"ALTER TABLE {table} ADD COLUMN {column} {col_type}")

def migrate_db(db_path):
    print(f"\nTraitement de la base : {db_path}")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    try:
        for column, col_type in COLUMNS:
            if not column_exists(cursor, 'repair_notes', column):
                add_column(cursor, 'repair_notes', column, col_type)
            else:
                print(f"  - Colonne {column} déjà présente.")
        conn.commit()
        print("  -> Migration terminée.")
    except Exception as e:
        print(f"  !! Erreur : {e}")
    finally:
        conn.close()

def main():
    db_files = get_db_files()
    if not db_files:
        print("Aucune base de données trouvée.")
        return
    for db_path in db_files:
        migrate_db(db_path)
    print("\nMigration terminée pour toutes les bases.")

if __name__ == "__main__":
    main()