"""
Migration SQLite pour ajouter champs de paiements robustes à repair_payments.
- record_type (ENUM simulé par VARCHAR)
- idempotency_key (UNIQUE)
- original_payment_id (FK optionnelle)
- is_voided, void_reason, voided_at, voided_by
Compatible avec fichiers DB dans racines: app.db, data/*.db, backups/*.db
"""
import os
from glob import glob
import sqlite3

DB_PATHS = [
    'app.db',
    os.path.join('data', '*.db'),
    os.path.join('backups', '*.db'),
]

# Colonnes à ajouter: (name, type)
COLUMNS = [
    ('record_type', "VARCHAR(20) DEFAULT 'PAYMENT'"),
    ('idempotency_key', 'VARCHAR(128)'),
    ('original_payment_id', 'INTEGER'),
    ('is_voided', 'BOOLEAN DEFAULT 0'),
    ('void_reason', 'VARCHAR(255)'),
    ('voided_at', 'DATETIME'),
    ('voided_by', 'INTEGER'),
]

INDEXES = [
    ("idx_repair_payments_idempotency_key", "CREATE UNIQUE INDEX IF NOT EXISTS idx_repair_payments_idempotency_key ON repair_payments(idempotency_key)")
]

FKS = [
    # SQLite ne permet pas d'ajouter une FK via ALTER TABLE facilement sans recréer la table.
    # On laisse sans contrainte FK matérielle, ou on gère via recréation si nécessaire.
    # (original_payment_id) -> repair_payments(id)
]

def get_db_files():
    db_files = set()
    for path in DB_PATHS:
        db_files.update(glob(path))
    return sorted(db_files)


def column_exists(cursor, table, column):
    cursor.execute(f"PRAGMA table_info({table})")
    return any(row[1] == column for row in cursor.fetchall())


def add_column(cursor, table, column, col_type):
    print(f"  - Ajout colonne {column} à {table}...")
    cursor.execute(f"ALTER TABLE {table} ADD COLUMN {column} {col_type}")


def create_index(cursor, sql):
    cursor.execute(sql)


def migrate_db(db_path):
    print(f"\nTraitement de la base : {db_path}")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    try:
        # Activer contraintes FK si plus tard on recrée la table
        cursor.execute("PRAGMA foreign_keys = ON")

        # Ajouter colonnes si manquantes
        for column, col_type in COLUMNS:
            try:
                if not column_exists(cursor, 'repair_payments', column):
                    add_column(cursor, 'repair_payments', column, col_type)
                else:
                    print(f"  - Colonne {column} déjà présente.")
            except Exception as e:
                print(f"    ! Erreur ajout colonne {column}: {e}")

        # Index unique sur idempotency_key
        try:
            for _name, sql in INDEXES:
                create_index(cursor, sql)
            print("  - Index idempotency_key OK.")
        except Exception as e:
            print(f"    ! Erreur création index: {e}")

        conn.commit()
        print("  -> Migration terminée.")
    except Exception as e:
        print(f"  !! Erreur : {e}")
    finally:
        conn.close()


def main():
    db_files = get_db_files()
    if not db_files:
        print("Aucune base de données trouvée.")
        return
    for db_path in db_files:
        migrate_db(db_path)
    print("\nMigration terminée pour toutes les bases.")

if __name__ == "__main__":
    main()