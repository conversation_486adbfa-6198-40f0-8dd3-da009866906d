"""Script pour corriger les valeurs de record_type dans la table repair_payments.

Le problème est que la migration a ajouté une colonne 'record_type' avec une valeur par défaut 'payment' (en minuscules),
mais l'énumération PaymentRecordType dans le modèle utilise 'PAYMENT' (en majuscules).

Ce script met à jour toutes les valeurs 'payment' en 'PAYMENT' et 'refund' en 'REFUND' pour correspondre à l'énumération.
"""

import os
from glob import glob
import sqlite3

DB_PATHS = [
    'app.db',
    os.path.join('data', '*.db'),
    os.path.join('backups', '*.db'),
]

def get_db_files():
    db_files = set()
    for path in DB_PATHS:
        db_files.update(glob(path))
    return sorted(db_files)

def fix_record_type_values(db_path):
    print(f"\nTraitement de la base : {db_path}")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    try:
        # Vérifier si la table repair_payments existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='repair_payments'")
        if not cursor.fetchone():
            print(f"  - La table repair_payments n'existe pas dans {db_path}")
            return
        
        # Vérifier si la colonne record_type existe
        cursor.execute("PRAGMA table_info(repair_payments)")
        columns = [row[1] for row in cursor.fetchall()]
        if 'record_type' not in columns:
            print(f"  - La colonne record_type n'existe pas dans la table repair_payments de {db_path}")
            return
        
        # Compter les enregistrements à mettre à jour
        cursor.execute("SELECT COUNT(*) FROM repair_payments WHERE record_type = 'payment'")
        payment_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM repair_payments WHERE record_type = 'refund'")
        refund_count = cursor.fetchone()[0]
        
        # Mettre à jour les valeurs
        if payment_count > 0:
            cursor.execute("UPDATE repair_payments SET record_type = 'PAYMENT' WHERE record_type = 'payment'")
            print(f"  - {payment_count} enregistrements mis à jour de 'payment' à 'PAYMENT'")
        
        if refund_count > 0:
            cursor.execute("UPDATE repair_payments SET record_type = 'REFUND' WHERE record_type = 'refund'")
            print(f"  - {refund_count} enregistrements mis à jour de 'refund' à 'REFUND'")
        
        conn.commit()
        print("  -> Correction terminée.")
    except Exception as e:
        print(f"  !! Erreur : {e}")
    finally:
        conn.close()

def main():
    db_files = get_db_files()
    if not db_files:
        print("Aucune base de données trouvée.")
        return
    for db_path in db_files:
        fix_record_type_values(db_path)
    print("\nCorrection terminée pour toutes les bases.")

if __name__ == "__main__":
    main()