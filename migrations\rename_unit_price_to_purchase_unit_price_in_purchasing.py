"""
Migration SQLite: renommer unit_price -> purchase_unit_price
- Tables: purchase_order_items, supplier_quotes
- Colonne: unit_price -> purchase_unit_price
Base ciblée: data/app.db (selon votre choix)
Cette migration est idempotente: elle ne fait rien si la colonne cible existe déjà.
"""
import os
import sqlite3

DB_PATH = os.path.join('data', 'app.db')

RENAME_TARGETS = [
    ("purchase_order_items", "unit_price", "purchase_unit_price", "FLOAT"),
    ("supplier_quotes", "unit_price", "purchase_unit_price", "FLOAT"),
]

def column_exists(cursor, table, column):
    cursor.execute(f"PRAGMA table_info({table})")
    return any(row[1] == column for row in cursor.fetchall())


def rename_column(conn, table, old_col, new_col, col_type):
    cursor = conn.cursor()
    # SQLite n'a pas un ALTER COLUMN RENAME portable avant 3.25+; stratégie: recréer la table
    cursor.execute(f"PRAGMA foreign_keys = OFF")

    # Récupérer schéma actuel
    cursor.execute(f"PRAGMA table_info({table})")
    cols = cursor.fetchall()
    col_defs = []
    select_cols = []
    for cid, name, ctype, notnull, dflt, pk in cols:
        if name == old_col:
            col_defs.append(f"{new_col} {col_type}")
            select_cols.append(f"{old_col} AS {new_col}")
        else:
            # conserver définition telle quelle si possible
            t = ctype or ""
            nn = " NOT NULL" if notnull else ""
            df = f" DEFAULT {dflt}" if dflt is not None else ""
            pkc = " PRIMARY KEY" if pk else ""
            col_defs.append(f"{name} {t}{nn}{df}{pkc}")
            select_cols.append(name)

    tmp_table = f"{table}__tmp_ren_upp"
    cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name=?", (table,))
    create_sql_row = cursor.fetchone()
    if not create_sql_row:
        raise RuntimeError(f"Table {table} introuvable")

    # Construire CREATE TABLE pour la nouvelle table
    create_sql = f"CREATE TABLE {tmp_table} ({', '.join(col_defs)})"
    cursor.execute(create_sql)

    # Copier les données
    cursor.execute(f"INSERT INTO {tmp_table} SELECT {', '.join(select_cols)} FROM {table}")

    # Remplacer l'ancienne table
    cursor.execute(f"DROP TABLE {table}")
    cursor.execute(f"ALTER TABLE {tmp_table} RENAME TO {table}")

    cursor.execute("PRAGMA foreign_keys = ON")
    conn.commit()


def migrate():
    if not os.path.exists(DB_PATH):
        print(f"Base introuvable: {DB_PATH}")
        return
    conn = sqlite3.connect(DB_PATH)
    try:
        cur = conn.cursor()
        cur.execute("PRAGMA foreign_keys = ON")
        for table, old_col, new_col, ctype in RENAME_TARGETS:
            print(f"\nTable: {table}")
            if column_exists(cur, table, new_col):
                print(f"  - {new_col} existe déjà, rien à faire.")
                continue
            if not column_exists(cur, table, old_col):
                print(f"  - {old_col} introuvable, rien à faire.")
                continue
            print(f"  - Renommage {old_col} -> {new_col}")
            rename_column(conn, table, old_col, new_col, ctype)
            print("  - OK.")
        print("\nMigration terminée.")
    finally:
        conn.close()

if __name__ == "__main__":
    migrate()