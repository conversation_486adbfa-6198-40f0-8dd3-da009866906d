"""
Migration SQLite: renommer unit_price -> purchase_unit_price pour ventes et réparations
- Tables: sale_items, quote_items, used_parts
- Colonne: unit_price -> purchase_unit_price (FLOAT)
Base ciblée: data/app.db
Idempotent: ne fait rien si la colonne cible existe déjà ou l'ancienne n'existe plus.
"""
import os
import sqlite3

DB_PATH = os.path.join('data', 'app.db')

RENAME_TARGETS = [
    ("sale_items", "unit_price", "purchase_unit_price", "FLOAT"),
    ("quote_items", "unit_price", "purchase_unit_price", "FLOAT"),
    ("used_parts", "unit_price", "purchase_unit_price", "FLOAT"),
]

def column_exists(cursor, table, column):
    cursor.execute(f"PRAGMA table_info({table})")
    return any(row[1] == column for row in cursor.fetchall())


def rename_column(conn, table, old_col, new_col, col_type):
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA foreign_keys = OFF")

    # Lire schéma
    cursor.execute(f"PRAGMA table_info({table})")
    cols = cursor.fetchall()
    col_defs = []
    select_cols = []
    for cid, name, ctype, notnull, dflt, pk in cols:
        if name == old_col:
            col_defs.append(f"{new_col} {col_type}")
            select_cols.append(f"{old_col} AS {new_col}")
        else:
            t = ctype or ""
            nn = " NOT NULL" if notnull else ""
            df = f" DEFAULT {dflt}" if dflt is not None else ""
            pkc = " PRIMARY KEY" if pk else ""
            col_defs.append(f"{name} {t}{nn}{df}{pkc}")
            select_cols.append(name)

    tmp_table = f"{table}__tmp_ren_upp"
    cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name=?", (table,))
    if not cursor.fetchone():
        raise RuntimeError(f"Table {table} introuvable")

    cursor.execute(f"CREATE TABLE {tmp_table} ({', '.join(col_defs)})")
    cursor.execute(f"INSERT INTO {tmp_table} SELECT {', '.join(select_cols)} FROM {table}")
    cursor.execute(f"DROP TABLE {table}")
    cursor.execute(f"ALTER TABLE {tmp_table} RENAME TO {table}")

    cursor.execute("PRAGMA foreign_keys = ON")
    conn.commit()


def migrate():
    if not os.path.exists(DB_PATH):
        print(f"Base introuvable: {DB_PATH}")
        return
    conn = sqlite3.connect(DB_PATH)
    try:
        cur = conn.cursor()
        cur.execute("PRAGMA foreign_keys = ON")
        for table, old_col, new_col, ctype in RENAME_TARGETS:
            print(f"\nTable: {table}")
            if column_exists(cur, table, new_col):
                print(f"  - {new_col} existe déjà, rien à faire.")
                continue
            if not column_exists(cur, table, old_col):
                print(f"  - {old_col} introuvable, rien à faire.")
                continue
            print(f"  - Renommage {old_col} -> {new_col}")
            rename_column(conn, table, old_col, new_col, ctype)
            print("  - OK.")
        print("\nMigration terminée.")
    finally:
        conn.close()

if __name__ == "__main__":
    migrate()