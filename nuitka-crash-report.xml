<?xml version='1.0' encoding='utf8'?>
<nuitka-compilation-report nuitka_version="2.7.3" nuitka_commercial_version="not installed" mode="standalone" completion="error exit message (1)" exit_message="pyqt6: Conflict between user and plugin decision for module 'PySide6'.">
  <performance>
    <memory_usage name="after_launch" value="41738240" />
  </performance>
  <data_file name="config\settings.toml" source="${cwd}\config\settings.toml" size="446" reason="specified data file 'config/settings.toml=config/settings.toml' on command line" tags="user,copy" />
  <data_file name="launcher.py" source="${cwd}\launcher.py" size="3840" reason="specified data file 'launcher.py=launcher.py' on command line" tags="user,copy" />
  <data_file name="app\ui\resources\resources.qrc" source="${cwd}\app\ui\resources\resources.qrc" size="927" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\add.svg" source="${cwd}\app\ui\resources\icons\add.svg" size="205" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\add_transaction.svg" source="${cwd}\app\ui\resources\icons\add_transaction.svg" size="365" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\alert.svg" source="${cwd}\app\ui\resources\icons\alert.svg" size="358" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\backup.svg" source="${cwd}\app\ui\resources\icons\backup.svg" size="511" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\barcode.svg" source="${cwd}\app\ui\resources\icons\barcode.svg" size="614" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\cancel.svg" source="${cwd}\app\ui\resources\icons\cancel.svg" size="320" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\category.svg" source="${cwd}\app\ui\resources\icons\category.svg" size="376" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\chart.svg" source="${cwd}\app\ui\resources\icons\chart.svg" size="416" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\check.svg" source="${cwd}\app\ui\resources\icons\check.svg" size="276" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\customer.svg" source="${cwd}\app\ui\resources\icons\customer.svg" size="309" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\dashboard.svg" source="${cwd}\app\ui\resources\icons\dashboard.svg" size="183" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\delete.svg" source="${cwd}\app\ui\resources\icons\delete.svg" size="451" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\delivery.svg" source="${cwd}\app\ui\resources\icons\delivery.svg" size="438" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\edit.svg" source="${cwd}\app\ui\resources\icons\edit.svg" size="646" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\equipment.svg" source="${cwd}\app\ui\resources\icons\equipment.svg" size="1025" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\expense.svg" source="${cwd}\app\ui\resources\icons\expense.svg" size="301" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\export.svg" source="${cwd}\app\ui\resources\icons\export.svg" size="246" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\finance.svg" source="${cwd}\app\ui\resources\icons\finance.svg" size="431" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\help.svg" source="${cwd}\app\ui\resources\icons\help.svg" size="379" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\history.svg" source="${cwd}\app\ui\resources\icons\history.svg" size="321" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\inventory.svg" source="${cwd}\app\ui\resources\icons\inventory.svg" size="247" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\invoice.svg" source="${cwd}\app\ui\resources\icons\invoice.svg" size="495" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\key.svg" source="${cwd}\app\ui\resources\icons\key.svg" size="627" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\maintenance.svg" source="${cwd}\app\ui\resources\icons\maintenance.svg" size="808" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\money.svg" source="${cwd}\app\ui\resources\icons\money.svg" size="351" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\more.svg" source="${cwd}\app\ui\resources\icons\more.svg" size="964" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\movement.svg" source="${cwd}\app\ui\resources\icons\movement.svg" size="235" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\notification.svg" source="${cwd}\app\ui\resources\icons\notification.svg" size="339" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\order.svg" source="${cwd}\app\ui\resources\icons\order.svg" size="443" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\parts.svg" source="${cwd}\app\ui\resources\icons\parts.svg" size="732" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\payment.svg" source="${cwd}\app\ui\resources\icons\payment.svg" size="472" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\print.svg" source="${cwd}\app\ui\resources\icons\print.svg" size="425" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\purchase.svg" source="${cwd}\app\ui\resources\icons\purchase.svg" size="395" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\reconcile.svg" source="${cwd}\app\ui\resources\icons\reconcile.svg" size="324" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\refresh.svg" source="${cwd}\app\ui\resources\icons\refresh.svg" size="379" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\repair.svg" source="${cwd}\app\ui\resources\icons\repair.svg" size="391" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\reset.svg" source="${cwd}\app\ui\resources\icons\reset.svg" size="422" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\restore.svg" source="${cwd}\app\ui\resources\icons\restore.svg" size="475" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\sale.svg" source="${cwd}\app\ui\resources\icons\sale.svg" size="1044" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\save.svg" source="${cwd}\app\ui\resources\icons\save.svg" size="413" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\search.svg" source="${cwd}\app\ui\resources\icons\search.svg" size="329" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\settings.svg" source="${cwd}\app\ui\resources\icons\settings.svg" size="1144" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\shield-off.svg" source="${cwd}\app\ui\resources\icons\shield-off.svg" size="561" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\shield.svg" source="${cwd}\app\ui\resources\icons\shield.svg" size="680" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\supplier.svg" source="${cwd}\app\ui\resources\icons\supplier.svg" size="196" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\transfer.svg" source="${cwd}\app\ui\resources\icons\transfer.svg" size="335" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\user.svg" source="${cwd}\app\ui\resources\icons\user.svg" size="807" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\view.svg" source="${cwd}\app\ui\resources\icons\view.svg" size="335" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\view_transactions.svg" source="${cwd}\app\ui\resources\icons\view_transactions.svg" size="405" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_file name="app\ui\resources\icons\warning.svg" source="${cwd}\app\ui\resources\icons\warning.svg" size="435" reason="specified data dir 'app/ui/resources' on command line" tags="user,copy,data-dir-contents" />
  <data_composer blob_size="None" />
  <command_line>
    <option value="--standalone" />
    <option value="--enable-plugin=pyqt6" />
    <option value="--follow-imports" />
    <option value="--include-package=app" />
    <option value="--include-package=config" />
    <option value="--include-package=sqlalchemy" />
    <option value="--include-package=PyQt6" />
    <option value="--include-package=PySide6" />
    <option value="--include-module=PyQt6.QtCharts" />
    <option value="--include-module=PyQt6.QtCore" />
    <option value="--include-module=PyQt6.QtGui" />
    <option value="--include-module=PyQt6.QtWidgets" />
    <option value="--include-module=PyQt6.QtSql" />
    <option value="--include-module=PyQt6.QtPrintSupport" />
    <option value="--include-module=PyQt6.QtSvg" />
    <option value="--include-module=PyQt6.sip" />
    <option value="--include-module=PySide6.QtCore" />
    <option value="--include-module=PySide6.QtGui" />
    <option value="--include-module=PySide6.QtWidgets" />
    <option value="--include-module=PySide6.QtSql" />
    <option value="--include-module=PySide6.QtPrintSupport" />
    <option value="--include-module=PySide6.QtSvg" />
    <option value="--include-module=PySide6.shiboken" />
    <option value="--include-package=apscheduler" />
    <option value="--include-package=logging" />
    <option value="--include-package=toml" />
    <option value="--include-package=passlib" />
    <option value="--include-data-dir=app/ui/resources=app/ui/resources" />
    <option value="--include-data-files=config/settings.toml=config/settings.toml" />
    <option value="--include-data-files=launcher.py=launcher.py" />
    <option value="--output-dir=dist" />
    <option value="--show-progress" />
    <option value="--assume-yes-for-downloads" />
    <option value="--jobs=4" />
    <option value="--lto=no" />
    <option value="--windows-dependency-tool=pefile" />
    <option value="--prefer-source-code" />
    <option value="main.py" />
  </command_line>
  <plugins>
    <plugin name="anti-bloat" user_enabled="no" />
    <plugin name="data-files" user_enabled="no" />
    <plugin name="delvewheel" user_enabled="no" />
    <plugin name="dll-files" user_enabled="no" />
    <plugin name="eventlet" user_enabled="no" />
    <plugin name="gevent" user_enabled="no" />
    <plugin name="gi" user_enabled="no" />
    <plugin name="glfw" user_enabled="no" />
    <plugin name="implicit-imports" user_enabled="no" />
    <plugin name="kivy" user_enabled="no" />
    <plugin name="matplotlib" user_enabled="no" />
    <plugin name="multiprocessing" user_enabled="no" />
    <plugin name="options-nanny" user_enabled="no" />
    <plugin name="pbr-compat" user_enabled="no" />
    <plugin name="pkg-resources" user_enabled="no" />
    <plugin name="playwright" user_enabled="no" />
    <plugin name="pyqt6" user_enabled="yes" />
    <plugin name="pywebview" user_enabled="no" />
    <plugin name="spacy" user_enabled="no" />
    <plugin name="transformers" user_enabled="no" />
  </plugins>
  <distributions />
  <python python_exe="${sys.prefix}\Scripts\python.exe" python_flavor="CPython Official" python_version="3.12.0" os_name="Windows" os_release="11" arch_name="x86_64" filesystem_encoding="utf-8">
    <search_path>
      <path value="${cwd}" />
      <path value="${sys.real_prefix}\DLLs" />
      <path value="${sys.real_prefix}\Lib" />
      <path value="${sys.real_prefix}" />
      <path value="${sys.prefix}" />
      <path value="${sys.prefix}\Lib\site-packages" />
    </search_path>
  </python>
  <output run_filename="${cwd}\dist\main.dist\main.exe" />
</nuitka-compilation-report>
