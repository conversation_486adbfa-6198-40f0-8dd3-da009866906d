"""
Script pour créer les tables dans la base de données.
"""
import os
import sys
import inspect

# Ajouter le répertoire parent au chemin de recherche des modules
current_dir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# Créer la connexion à la base de données
SQLALCHEMY_DATABASE_URL = "sqlite:///./app.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Créer la base
Base = declarative_base()

# Importer les modèles
from app.core.models.repair_photo import RepairPhoto
from app.core.models.repair_note import RepairNote
from app.core.models.repair_status_history import RepairStatusHistory

def create_tables():
    """Crée les tables dans la base de données"""
    print("Création des tables...")
    
    try:
        # Créer les tables
        Base.metadata.create_all(bind=engine)
        
        print("Tables créées avec succès.")
    except Exception as e:
        print(f"Erreur lors de la création des tables: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_tables()
