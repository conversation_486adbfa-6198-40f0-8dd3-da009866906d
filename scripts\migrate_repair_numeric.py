"""
Migration: Normalize monetary columns to 2 decimals and align with Numeric(12,2)
- Targets tables: repair_orders, used_parts, repair_payments
- Operations (SQLite-friendly):
  * Backup DB file
  * Round monetary fields to 2 decimals
  * Verify counts
Note: On SQLite, column affinity isn't strictly enforced; this script focuses on data normalization.
"""

import os
import shutil
from datetime import datetime
from sqlalchemy import text
import sys
from pathlib import Path

# Ensure project root on sys.path
PROJECT_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(PROJECT_ROOT))


def main():
    # Import inside to ensure app pathing is correct
    from app.core.models.config import engine
    from app.utils.config import get_db_path

    db_path = get_db_path()

    # 1) Backup DB
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = os.path.join(os.path.dirname(os.path.dirname(db_path)), "backups")
    os.makedirs(backup_dir, exist_ok=True)
    backup_path = os.path.join(backup_dir, f"app.db.backup_before_numeric_{ts}.sqlite")
    shutil.copy(db_path, backup_path)
    print(f"Backup created: {backup_path}")

    # 2) Execute rounding updates
    stmts = [
        # repair_orders monetary fields
        """
        UPDATE repair_orders SET
            total_cost = ROUND(COALESCE(total_cost, 0), 2),
            labor_cost = ROUND(COALESCE(labor_cost, 0), 2),
            parts_cost = ROUND(COALESCE(parts_cost, 0), 2),
            tax_amount = ROUND(COALESCE(tax_amount, 0), 2),
            discount_amount = ROUND(COALESCE(discount_amount, 0), 2),
            final_amount = ROUND(COALESCE(final_amount, 0), 2),
            total_paid = ROUND(COALESCE(total_paid, 0), 2)
        """,
        # used_parts
        """
        UPDATE used_parts SET
            purchase_unit_price = ROUND(COALESCE(purchase_unit_price, 0), 2),
            total_price = ROUND(COALESCE(total_price, 0), 2)
        """,
        # repair_payments
        """
        UPDATE repair_payments SET
            amount = ROUND(COALESCE(amount, 0), 2)
        """,
    ]

    with engine.begin() as conn:
        # Check existence of tables first (SQLite PRAGMA)
        res = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
        tables = {row[0] for row in res.fetchall()}

        targets = {
            "repair_orders": [
                "total_cost",
                "labor_cost",
                "parts_cost",
                "tax_amount",
                "discount_amount",
                "final_amount",
                "total_paid",
            ],
            "used_parts": ["purchase_unit_price", "total_price"],
            "repair_payments": ["amount"],
        }

        for stmt in stmts:
            conn.execute(text(stmt))

        # 3) Simple verification: select min/max decimals
        def dec_info(table, cols):
            info = {}
            for c in cols:
                q = text(f"SELECT COUNT(*) FROM {table} WHERE CAST(ROUND(COALESCE({c},0),2) AS TEXT) NOT LIKE '%.%' AND COALESCE({c},0) != 0")
                cnt = conn.execute(q).scalar() or 0
                info[c] = int(cnt)
            return info

        report = {}
        for t, cols in targets.items():
            if t in tables:
                report[t] = dec_info(t, cols)

    print("Normalization complete. Verification (counts of non-decimal text representations):")
    for t, cols in report.items():
        print(f"- {t}:")
        for c, cnt in cols.items():
            print(f"  {c}: {cnt}")

    print("Done.")


if __name__ == "__main__":
    main()