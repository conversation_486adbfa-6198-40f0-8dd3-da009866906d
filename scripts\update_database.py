"""
Script pour mettre à jour la base de données avec les nouveaux modèles.
"""
import os
import sys
import inspect

# Ajouter le répertoire parent au chemin de recherche des modules
current_dir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from app.utils.database import Base, engine
from app.core.models.repair_photo import RepairPhoto
from app.core.models.repair_note import RepairNote
from app.core.models.repair_status_history import RepairStatusHistory

def update_database():
    """Met à jour la base de données avec les nouveaux modèles"""
    print("Mise à jour de la base de données...")

    try:
        # Créer les tables pour les nouveaux modèles
        Base.metadata.create_all(bind=engine, tables=[
            RepairPhoto.__table__,
            RepairNote.__table__,
            RepairStatusHistory.__table__
        ])

        print("Base de données mise à jour avec succès.")
    except Exception as e:
        print(f"Erreur lors de la mise à jour de la base de données: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    update_database()
