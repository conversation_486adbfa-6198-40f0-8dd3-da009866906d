import sqlite3
from datetime import datetime
from passlib.context import CryptContext

# Utiliser la bonne base de données (celle utilisée par SQLAlchemy)
db_path = "data/app.db"
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print(f"=== Configuration complète de la base de données {db_path} ===")

# Vérifier les rôles existants
cursor.execute("SELECT id, name, description FROM roles ORDER BY id")
existing_roles = cursor.fetchall()
print(f"\nRôles existants ({len(existing_roles)}):")
for role in existing_roles:
    print(f"  ID: {role[0]}, Nom: {role[1]}, Description: {role[2]}")

# Vérifier les permissions existantes
cursor.execute("SELECT id, code, name, category FROM permissions ORDER BY id")
existing_permissions = cursor.fetchall()
print(f"\nPermissions existantes ({len(existing_permissions)}):")
for perm in existing_permissions:
    print(f"  ID: {perm[0]}, Code: {perm[1]}, Nom: {perm[2]}, Catégorie: {perm[3]}")

# Créer le rôle admin s'il n'existe pas
cursor.execute("SELECT id FROM roles WHERE name = 'admin'")
admin_role = cursor.fetchone()

if not admin_role:
    print("\n🔧 Création du rôle admin...")
    cursor.execute("""
        INSERT INTO roles (name, description, is_system, parent_id)
        VALUES ('admin', 'Administrateur avec tous les droits', 1, NULL)
    """)
    admin_role_id = cursor.lastrowid
    print(f"✅ Rôle admin créé avec l'ID: {admin_role_id}")
else:
    admin_role_id = admin_role[0]
    print(f"✅ Rôle admin existant trouvé: ID={admin_role_id}")

# Créer les permissions admin si elles n'existent pas
admin_permissions = [
    ('user.view', 'Voir les utilisateurs', 'Permet de voir la liste des utilisateurs', 'user'),
    ('user.create', 'Créer des utilisateurs', 'Permet de créer de nouveaux utilisateurs', 'user'),
    ('user.edit', 'Modifier les utilisateurs', 'Permet de modifier les utilisateurs existants', 'user'),
    ('user.delete', 'Supprimer les utilisateurs', 'Permet de supprimer des utilisateurs', 'user'),
    ('role.view', 'Voir les rôles', 'Permet de voir la liste des rôles', 'role'),
    ('role.manage', 'Gérer les rôles', 'Permet de créer/modifier/supprimer des rôles', 'role'),
    ('system.admin', 'Administration système', 'Accès complet au système', 'system')
]

print("\n🔧 Création des permissions admin...")
created_permissions = []

for code, name, description, category in admin_permissions:
    # Vérifier si la permission existe déjà
    cursor.execute("SELECT id FROM permissions WHERE code = ?", (code,))
    existing = cursor.fetchone()
    
    if not existing:
        cursor.execute("""
            INSERT INTO permissions (code, name, description, category)
            VALUES (?, ?, ?, ?)
        """, (code, name, description, category))
        perm_id = cursor.lastrowid
        print(f"  ✅ Permission créée: {code} (ID: {perm_id})")
        created_permissions.append(perm_id)
    else:
        perm_id = existing[0]
        print(f"  ℹ️  Permission existante: {code} (ID: {perm_id})")
        created_permissions.append(perm_id)

# Assigner toutes les permissions admin au rôle admin
print(f"\n🔧 Attribution des permissions au rôle admin...")
cursor.execute("SELECT id FROM permissions")
all_permissions = cursor.fetchall()

for perm in all_permissions:
    perm_id = perm[0]
    
    # Vérifier si l'association existe déjà
    cursor.execute("SELECT 1 FROM role_permission WHERE role_id = ? AND permission_id = ?", 
                   (admin_role_id, perm_id))
    existing = cursor.fetchone()
    
    if not existing:
        cursor.execute("INSERT INTO role_permission (role_id, permission_id) VALUES (?, ?)",
                       (admin_role_id, perm_id))
        print(f"  ✅ Permission {perm_id} assignée au rôle admin")
    else:
        print(f"  ℹ️  Permission {perm_id} déjà assignée au rôle admin")

# Corriger l'utilisateur <EMAIL>
print(f"\n🔧 Correction de l'utilisateur <EMAIL>...")

# Créer le contexte bcrypt
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Récupérer l'utilisateur
cursor.execute("SELECT id, email, full_name FROM users WHERE email = ?", ('<EMAIL>',))
user = cursor.fetchone()

if user:
    user_id, email, full_name = user
    print(f"✅ Utilisateur trouvé: ID={user_id}, Email={email}, Nom={full_name}")
    
    # Corriger le mot de passe
    password = "password123"
    correct_hash = pwd_context.hash(password)
    now = datetime.now().isoformat()
    
    cursor.execute("UPDATE users SET hashed_password = ?, last_login = ?, updated_at = ? WHERE id = ?", 
                   (correct_hash, now, now, user_id))
    print("✅ Mot de passe et date de dernière connexion mis à jour")
    
    # Supprimer tous les rôles existants de l'utilisateur
    cursor.execute("DELETE FROM user_roles WHERE user_id = ?", (user_id,))
    print(f"🗑️  Rôles existants supprimés")
    
    # Ajouter le rôle admin
    cursor.execute("""
        INSERT INTO user_roles (user_id, role_id, created_at, updated_at)
        VALUES (?, ?, ?, ?)
    """, (user_id, admin_role_id, now, now))
    print("✅ Rôle admin assigné")
    
else:
    print("❌ Utilisateur <EMAIL> non trouvé")

# Valider toutes les modifications
conn.commit()
print("\n✅ Toutes les modifications sauvegardées")

# Vérification finale
print("\n=== Vérification finale ===")

# Vérifier l'utilisateur
cursor.execute("SELECT id, email, full_name, last_login FROM users WHERE email = ?", ('<EMAIL>',))
final_user = cursor.fetchone()
if final_user:
    print(f"Utilisateur: {final_user[1]}")
    print(f"  ID: {final_user[0]}")
    print(f"  Nom: {final_user[2]}")
    print(f"  Dernière connexion: {final_user[3]}")
    
    # Vérifier les rôles
    cursor.execute("""
        SELECT r.id, r.name, r.description 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    """, (final_user[0],))
    user_roles = cursor.fetchall()
    
    print(f"  Rôles ({len(user_roles)}):")
    for role in user_roles:
        print(f"    - {role[1]} (ID: {role[0]}): {role[2]}")
        
        # Vérifier les permissions
        cursor.execute("""
            SELECT p.code, p.name 
            FROM permissions p
            JOIN role_permission rp ON p.id = rp.permission_id
            WHERE rp.role_id = ?
        """, (role[0],))
        permissions = cursor.fetchall()
        
        print(f"      Permissions ({len(permissions)}):")
        for perm in permissions:
            print(f"        - {perm[0]}: {perm[1]}")

conn.close()
print(f"\n✅ Configuration terminée avec succès pour {db_path}")
