"""
Script pour configurer le système de clôture journalière.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.database.migrations.add_daily_closure_tables import run_migration
from app.core.services.daily_closure_service import DailyClosureService
from app.core.services.treasury_report_service import TreasuryReportService
from datetime import date, timedelta


def setup_daily_closure_system():
    """Configure le système de clôture journalière"""
    print("=== Configuration du système de clôture journalière ===\n")
    
    # 1. Appliquer la migration
    print("1. Application de la migration de base de données...")
    try:
        db = SessionLocal()
        success = run_migration(db, "upgrade")
        if success:
            print("   ✓ Migration appliquée avec succès")
        else:
            print("   ✗ Échec de la migration")
            return False
        db.close()
    except Exception as e:
        print(f"   ✗ Erreur lors de la migration: {e}")
        return False
    
    # 2. Vérifier les services
    print("\n2. Vérification des services...")
    try:
        db = SessionLocal()
        
        # Service de clôture
        closure_service = DailyClosureService(db)
        print("   ✓ Service de clôture journalière initialisé")
        
        # Service de rapports
        report_service = TreasuryReportService(db)
        print("   ✓ Service de rapports étendu")
        
        db.close()
    except Exception as e:
        print(f"   ✗ Erreur lors de l'initialisation des services: {e}")
        return False
    
    # 3. Test de fonctionnement
    print("\n3. Test de fonctionnement...")
    try:
        db = SessionLocal()
        closure_service = DailyClosureService(db)
        
        # Tester la vérification de date
        yesterday = date.today() - timedelta(days=1)
        can_close, reason = closure_service.can_close_date(yesterday)
        print(f"   ✓ Test de vérification de date: {reason}")
        
        # Tester la récupération de l'historique
        history = closure_service.get_closure_history(limit=5)
        print(f"   ✓ Récupération de l'historique: {len(history)} clôtures trouvées")
        
        # Tester les verrouillages
        locks = closure_service.get_period_locks()
        print(f"   ✓ Récupération des verrouillages: {len(locks)} verrouillages actifs")
        
        db.close()
    except Exception as e:
        print(f"   ✗ Erreur lors des tests: {e}")
        return False
    
    # 4. Configuration recommandée
    print("\n4. Configuration recommandée...")
    print("   ✓ Planifier les clôtures journalières automatiques")
    print("   ✓ Configurer les notifications de rappel")
    print("   ✓ Définir les seuils d'écart acceptables")
    print("   ✓ Former les utilisateurs aux nouvelles procédures")
    
    print("\n=== Configuration terminée avec succès ===")
    return True


def show_usage_guide():
    """Affiche le guide d'utilisation"""
    print("\n=== Guide d'utilisation du système de clôture journalière ===\n")
    
    print("1. ACCÈS AU SYSTÈME")
    print("   - Ouvrir l'application de gestion")
    print("   - Aller dans la vue Trésorerie")
    print("   - Cliquer sur le bouton 'Clôture Journalière' (orange)")
    
    print("\n2. NOUVELLE CLÔTURE")
    print("   - Sélectionner la date à clôturer (généralement la veille)")
    print("   - Vérifier le statut de la date")
    print("   - Cliquer sur 'Prévisualiser' pour voir les données")
    print("   - Vérifier les écarts dans le tableau")
    print("   - Cliquer sur 'Exécuter la Clôture' pour finaliser")
    
    print("\n3. GESTION DES ÉCARTS")
    print("   - Écarts verts (≤ 0.01 DA) : Aucune action requise")
    print("   - Écarts jaunes (≤ 1 DA) : Vérification recommandée")
    print("   - Écarts rouges (> 1 DA) : Investigation obligatoire")
    
    print("\n4. HISTORIQUE ET RAPPORTS")
    print("   - Onglet 'Historique' : Voir toutes les clôtures passées")
    print("   - Onglet 'Verrouillages' : Voir les périodes verrouillées")
    print("   - Menu 'Rapports' : Générer des rapports détaillés")
    
    print("\n5. SÉCURITÉ")
    print("   - Les périodes clôturées sont automatiquement verrouillées")
    print("   - Aucune modification possible sur les périodes verrouillées")
    print("   - Toutes les actions sont auditées et tracées")
    
    print("\n6. BONNES PRATIQUES")
    print("   - Effectuer les clôtures quotidiennement")
    print("   - Vérifier les écarts avant validation")
    print("   - Conserver les justificatifs des ajustements")
    print("   - Effectuer des rapprochements réguliers")


def show_troubleshooting():
    """Affiche le guide de dépannage"""
    print("\n=== Guide de dépannage ===\n")
    
    problems = [
        {
            'problem': "Impossible de clôturer une date",
            'solutions': [
                "Vérifier que la date n'est pas dans le futur",
                "Vérifier qu'une clôture n'existe pas déjà",
                "Vérifier que la période n'est pas déjà verrouillée",
                "S'assurer qu'il y a des caisses actives"
            ]
        },
        {
            'problem': "Écarts importants détectés",
            'solutions': [
                "Vérifier les transactions de la journée",
                "Contrôler les soldes physiques des caisses",
                "Rechercher les transactions manquantes",
                "Documenter les ajustements nécessaires"
            ]
        },
        {
            'problem': "Erreur lors de l'exécution",
            'solutions': [
                "Vérifier la connexion à la base de données",
                "S'assurer que toutes les caisses sont accessibles",
                "Redémarrer l'application si nécessaire",
                "Contacter l'administrateur système"
            ]
        },
        {
            'problem': "Modification bloquée sur période verrouillée",
            'solutions': [
                "Vérifier la date de la transaction",
                "Demander un déverrouillage si justifié",
                "Créer une écriture de correction",
                "Documenter la raison de la modification"
            ]
        }
    ]
    
    for i, item in enumerate(problems, 1):
        print(f"{i}. PROBLÈME: {item['problem']}")
        print("   SOLUTIONS:")
        for solution in item['solutions']:
            print(f"   - {solution}")
        print()


def main():
    """Fonction principale"""
    print("Script de configuration du système de clôture journalière\n")
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "setup":
            setup_daily_closure_system()
        elif command == "guide":
            show_usage_guide()
        elif command == "troubleshoot":
            show_troubleshooting()
        else:
            print(f"Commande inconnue: {command}")
            print("Commandes disponibles: setup, guide, troubleshoot")
    else:
        print("Commandes disponibles:")
        print("  python setup_daily_closure.py setup        - Configure le système")
        print("  python setup_daily_closure.py guide        - Affiche le guide d'utilisation")
        print("  python setup_daily_closure.py troubleshoot - Affiche le guide de dépannage")
        print()
        
        # Configuration par défaut
        setup_daily_closure_system()


if __name__ == "__main__":
    main()
