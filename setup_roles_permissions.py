import sqlite3
from datetime import datetime

# Connexion directe à la base de données
conn = sqlite3.connect('app.db')
cursor = conn.cursor()

print("=== Configuration des rôles et permissions ===")

# Vérifier les tables de rôles et permissions
cursor.execute("PRAGMA table_info(roles)")
roles_columns = cursor.fetchall()
print(f"\nStructure de la table roles:")
for col in roles_columns:
    print(f"  {col[1]} ({col[2]}) - Nullable: {not col[3]}")

cursor.execute("PRAGMA table_info(permissions)")
permissions_columns = cursor.fetchall()
print(f"\nStructure de la table permissions:")
for col in permissions_columns:
    print(f"  {col[1]} ({col[2]}) - Nullable: {not col[3]}")

cursor.execute("PRAGMA table_info(user_roles)")
user_roles_columns = cursor.fetchall()
print(f"\nStructure de la table user_roles:")
for col in user_roles_columns:
    print(f"  {col[1]} ({col[2]}) - Nullable: {not col[3]}")

# Vérifier les données existantes
cursor.execute("SELECT * FROM roles")
existing_roles = cursor.fetchall()
print(f"\nRôles existants ({len(existing_roles)}):")
for role in existing_roles:
    print(f"  {role}")

cursor.execute("SELECT * FROM permissions")
existing_permissions = cursor.fetchall()
print(f"\nPermissions existantes ({len(existing_permissions)}):")
for perm in existing_permissions:
    print(f"  {perm}")

cursor.execute("SELECT * FROM user_roles")
existing_user_roles = cursor.fetchall()
print(f"\nRôles utilisateur existants ({len(existing_user_roles)}):")
for ur in existing_user_roles:
    print(f"  {ur}")

# Créer des rôles de base s'ils n'existent pas
now = datetime.now().isoformat()

roles_to_create = [
    ('admin', 'Administrateur', 'Accès complet au système', 1, None),
    ('manager', 'Gestionnaire', 'Gestion des opérations', 0, None),
    ('technician', 'Technicien', 'Réparations et maintenance', 0, None),
    ('user', 'Utilisateur', 'Accès de base', 0, None)
]

print(f"\n=== Création des rôles ===")
for role_name, description, role_desc, is_system, parent_id in roles_to_create:
    try:
        # Vérifier si le rôle existe déjà
        cursor.execute("SELECT id FROM roles WHERE name = ?", (role_name,))
        existing = cursor.fetchone()
        
        if not existing:
            cursor.execute("""
                INSERT INTO roles (name, description, is_system, parent_id)
                VALUES (?, ?, ?, ?)
            """, (role_name, role_desc, is_system, parent_id))
            print(f"✅ Rôle créé: {role_name}")
        else:
            print(f"ℹ️  Rôle existe déjà: {role_name} (ID: {existing[0]})")
    except Exception as e:
        print(f"❌ Erreur lors de la création du rôle {role_name}: {e}")

# Créer des permissions de base
permissions_to_create = [
    ('user.view', 'Voir les utilisateurs', 'Permet de voir la liste des utilisateurs', 'user'),
    ('user.create', 'Créer des utilisateurs', 'Permet de créer de nouveaux utilisateurs', 'user'),
    ('user.edit', 'Modifier les utilisateurs', 'Permet de modifier les utilisateurs existants', 'user'),
    ('user.delete', 'Supprimer les utilisateurs', 'Permet de supprimer des utilisateurs', 'user'),
    ('role.view', 'Voir les rôles', 'Permet de voir la liste des rôles', 'role'),
    ('role.manage', 'Gérer les rôles', 'Permet de créer/modifier/supprimer des rôles', 'role'),
    ('system.admin', 'Administration système', 'Accès complet au système', 'system')
]

print(f"\n=== Création des permissions ===")
for code, name, description, category in permissions_to_create:
    try:
        # Vérifier si la permission existe déjà
        cursor.execute("SELECT id FROM permissions WHERE code = ?", (code,))
        existing = cursor.fetchone()
        
        if not existing:
            cursor.execute("""
                INSERT INTO permissions (code, name, description, category)
                VALUES (?, ?, ?, ?)
            """, (code, name, description, category))
            print(f"✅ Permission créée: {code}")
        else:
            print(f"ℹ️  Permission existe déjà: {code} (ID: {existing[0]})")
    except Exception as e:
        print(f"❌ Erreur lors de la création de la permission {code}: {e}")

# Assigner toutes les permissions au rôle admin
print(f"\n=== Attribution des permissions au rôle admin ===")
try:
    # Récupérer l'ID du rôle admin
    cursor.execute("SELECT id FROM roles WHERE name = 'admin'")
    admin_role = cursor.fetchone()
    
    if admin_role:
        admin_role_id = admin_role[0]
        
        # Récupérer toutes les permissions
        cursor.execute("SELECT id FROM permissions")
        all_permissions = cursor.fetchall()
        
        for perm in all_permissions:
            perm_id = perm[0]
            
            # Vérifier si l'association existe déjà
            cursor.execute("SELECT 1 FROM role_permission WHERE role_id = ? AND permission_id = ?", 
                         (admin_role_id, perm_id))
            existing = cursor.fetchone()
            
            if not existing:
                cursor.execute("INSERT INTO role_permission (role_id, permission_id) VALUES (?, ?)",
                             (admin_role_id, perm_id))
                print(f"✅ Permission {perm_id} assignée au rôle admin")
            else:
                print(f"ℹ️  Permission {perm_id} déjà assignée au rôle admin")
    else:
        print("❌ Rôle admin non trouvé")
        
except Exception as e:
    print(f"❌ Erreur lors de l'attribution des permissions: {e}")

# Assigner le rôle admin à l'utilisateur
print(f"\n=== Attribution du rôle admin à l'utilisateur ===")
try:
    # Récupérer l'ID de l'utilisateur
    cursor.execute("SELECT id FROM users WHERE email = '<EMAIL>'")
    user = cursor.fetchone()
    
    if user and admin_role:
        user_id = user[0]
        admin_role_id = admin_role[0]
        
        # Vérifier si l'association existe déjà
        cursor.execute("SELECT 1 FROM user_roles WHERE user_id = ? AND role_id = ?", 
                     (user_id, admin_role_id))
        existing = cursor.fetchone()
        
        if not existing:
            cursor.execute("""
                INSERT INTO user_roles (user_id, role_id, created_at, updated_at) 
                VALUES (?, ?, ?, ?)
            """, (user_id, admin_role_id, now, now))
            print(f"✅ Rôle admin assigné à l'utilisateur <EMAIL>")
        else:
            print(f"ℹ️  Rôle admin déjà assigné à l'utilisateur")
    else:
        print("❌ Utilisateur ou rôle admin non trouvé")
        
except Exception as e:
    print(f"❌ Erreur lors de l'attribution du rôle: {e}")

# Valider toutes les modifications
conn.commit()

# Vérification finale
print(f"\n=== Vérification finale ===")
cursor.execute("SELECT COUNT(*) FROM roles")
roles_count = cursor.fetchone()[0]
print(f"Nombre de rôles: {roles_count}")

cursor.execute("SELECT COUNT(*) FROM permissions")
permissions_count = cursor.fetchone()[0]
print(f"Nombre de permissions: {permissions_count}")

cursor.execute("SELECT COUNT(*) FROM user_roles")
user_roles_count = cursor.fetchone()[0]
print(f"Nombre d'associations utilisateur-rôle: {user_roles_count}")

cursor.execute("SELECT COUNT(*) FROM role_permission")
role_permissions_count = cursor.fetchone()[0]
print(f"Nombre d'associations rôle-permission: {role_permissions_count}")

conn.close()
print("\n=== Configuration terminée ===")
