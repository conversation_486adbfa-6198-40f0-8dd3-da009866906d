import sqlite3
from datetime import datetime

# Connexion directe à la base de données
conn = sqlite3.connect('app.db')
cursor = conn.cursor()

print("=== Vérification de la base de données ===")

# Vérifier l'utilisateur spécifique
cursor.execute("SELECT id, email, last_login, status, is_active FROM users WHERE email = ?", ('<EMAIL>',))
result = cursor.fetchone()

if result:
    print(f"Utilisateur trouvé:")
    print(f"  ID: {result[0]}")
    print(f"  Email: {result[1]}")
    print(f"  Last_login: {result[2]}")
    print(f"  Status: {result[3]}")
    print(f"  Is_active: {result[4]}")
    
    # Si pas de date de dernière connexion, en ajouter une
    if result[2] is None:
        print("\nAucune date de dernière connexion trouvée. Ajout d'une date par défaut...")
        now = datetime.utcnow().isoformat()
        cursor.execute("UPDATE users SET last_login = ? WHERE email = ?", (now, '<EMAIL>'))
        conn.commit()
        print(f"Date de dernière connexion mise à jour: {now}")
        
        # Vérifier la mise à jour
        cursor.execute("SELECT last_login FROM users WHERE email = ?", ('<EMAIL>',))
        new_result = cursor.fetchone()
        print(f"Vérification: nouvelle date = {new_result[0]}")
else:
    print("Utilisateur '<EMAIL>' non trouvé")

# Vérifier tous les utilisateurs
cursor.execute("SELECT id, email, last_login FROM users")
all_users = cursor.fetchall()
print(f"\nTous les utilisateurs ({len(all_users)}):")
for user in all_users:
    print(f"  ID={user[0]}, Email={user[1]}, Last_login={user[2]}")

conn.close()
print("\n=== Vérification terminée ===")
