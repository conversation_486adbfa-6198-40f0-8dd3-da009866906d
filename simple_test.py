#!/usr/bin/env python3
import sys
import os

# Test simple d'import
try:
    from app.core.models.sale import SaleItem
    from app.core.models.purchasing import PurchaseOrderItem
    print("SUCCESS: Models imported")
    
    # Vérifier les colonnes
    sale_columns = [c.name for c in SaleItem.__table__.columns if 'price' in c.name]
    purchase_columns = [c.name for c in PurchaseOrderItem.__table__.columns if 'price' in c.name]
    
    print(f"SaleItem price columns: {sale_columns}")
    print(f"PurchaseOrderItem price columns: {purchase_columns}")
    
    # Vérifier que purchase_unit_price existe
    if 'purchase_unit_price' in sale_columns:
        print("SUCCESS: SaleItem has purchase_unit_price")
    else:
        print("ERROR: SaleItem missing purchase_unit_price")
        
    if 'purchase_unit_price' in purchase_columns:
        print("SUCCESS: PurchaseOrderItem has purchase_unit_price")
    else:
        print("ERROR: PurchaseOrderItem missing purchase_unit_price")
        
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
