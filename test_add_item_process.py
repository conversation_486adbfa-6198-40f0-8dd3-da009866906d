#!/usr/bin/env python3
"""
Test spécifique pour vérifier le processus d'ajout d'article dans nouvelle commande
"""
import sys
import os

def test_order_item_dialog_imports():
    """Teste que le dialogue d'ajout d'article peut être importé"""
    try:
        print("Testing order item dialog imports...")
        
        from app.ui.views.purchasing.dialogs.order_item_dialog import OrderItemDialog
        from app.ui.components.product_search_widget import ProductSearchWidget
        
        print("SUCCESS: Order item dialog and search widget imported")
        return True
        
    except Exception as e:
        print(f"ERROR: Error importing order item dialog: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_product_search_widget():
    """Teste que le widget de recherche de produits fonctionne"""
    try:
        print("Testing product search widget...")
        
        # Créer un produit de test
        class MockProduct:
            def __init__(self):
                self.id = 1
                self.name = "Produit Test"
                self.sku = "TEST-001"
                self.purchase_price = 25.50
                self.unit_price = 35.75  # Prix de vente
                self.quantity = 10
        
        product = MockProduct()
        
        # Tester l'accès aux attributs comme le fait le widget
        purchase_price = getattr(product, 'purchase_price', getattr(product, 'unit_price', 0))
        print(f"SUCCESS: Purchase price retrieved: {purchase_price}")
        
        if purchase_price == 25.50:
            print("SUCCESS: Purchase price correctly prioritized")
        else:
            print("ERROR: Purchase price not correctly prioritized")
            return False
            
        return True
        
    except Exception as e:
        print(f"ERROR: Error testing product search widget: {e}")
        return False

def test_item_data_structure():
    """Teste la structure des données d'article"""
    try:
        print("Testing item data structure...")
        
        # Simuler les données retournées par get_item_data()
        item_data = {
            "product_id": 1,
            "product": None,  # Sera rempli par le produit sélectionné
            "quantity": 5,
            "purchase_unit_price": 25.50,  # Doit être purchase_unit_price, pas unit_price
            "delivery_date": None,
            "specifications": {},
            "received_quantity": 0
        }
        
        # Vérifier que la clé correcte est utilisée
        if "purchase_unit_price" in item_data:
            print("SUCCESS: Item data uses purchase_unit_price")
        else:
            print("ERROR: Item data missing purchase_unit_price")
            return False
            
        if "unit_price" not in item_data:
            print("SUCCESS: Item data does not use deprecated unit_price")
        else:
            print("WARNING: Item data still contains deprecated unit_price")
            
        return True
        
    except Exception as e:
        print(f"ERROR: Error testing item data structure: {e}")
        return False

def test_purchase_order_item_creation():
    """Teste la création d'un PurchaseOrderItem avec les bonnes données"""
    try:
        print("Testing PurchaseOrderItem creation...")
        
        from app.core.models.purchasing import PurchaseOrderItem
        
        # Simuler la création d'un article comme dans add_item()
        item_data = {
            "product_id": 1,
            "quantity": 5,
            "purchase_unit_price": 25.50,
            "delivery_date": None,
            "specifications": {},
            "received_quantity": 0
        }
        
        # Créer l'article comme dans le dialogue
        item = PurchaseOrderItem(
            product_id=item_data["product_id"],
            quantity=item_data["quantity"],
            purchase_unit_price=item_data["purchase_unit_price"],
            delivery_date=item_data["delivery_date"],
            specifications=item_data["specifications"],
            received_quantity=item_data["received_quantity"]
        )
        
        print(f"SUCCESS: PurchaseOrderItem created with purchase_unit_price: {item.purchase_unit_price}")
        
        if item.purchase_unit_price == 25.50:
            print("SUCCESS: Purchase unit price correctly set")
        else:
            print("ERROR: Purchase unit price not correctly set")
            return False
            
        return True
        
    except Exception as e:
        print(f"ERROR: Error creating PurchaseOrderItem: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("Test du processus d'ajout d'article - unit_price -> purchase_unit_price")
    print("=" * 75)
    
    success = True
    
    # Test des imports
    if not test_order_item_dialog_imports():
        success = False
    
    # Test du widget de recherche
    if not test_product_search_widget():
        success = False
    
    # Test de la structure des données
    if not test_item_data_structure():
        success = False
    
    # Test de création d'article
    if not test_purchase_order_item_creation():
        success = False
    
    if success:
        print("\nSUCCESS: Tous les tests du processus d'ajout d'article sont passes!")
        print("Le processus utilise correctement purchase_unit_price")
    else:
        print("\nERROR: Certains tests ont echoue")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
