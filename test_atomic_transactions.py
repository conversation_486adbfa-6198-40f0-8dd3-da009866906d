"""
Test des transactions atomiques pour la trésorerie.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from decimal import Decimal
from app.utils.transaction_manager import atomic_operation, TransactionError
from app.utils.decimal_utils import validate_amount, DecimalValidationError


def test_atomic_operations():
    """Test des opérations atomiques"""
    print("=== Test des transactions atomiques ===")
    
    # Simuler une session de base de données
    class MockSession:
        def __init__(self):
            self.data = {}
            self.in_transaction_flag = False
            self.committed = False
            self.rolled_back = False
        
        def begin(self):
            self.in_transaction_flag = True
            print("  Transaction commencée")
        
        def commit(self):
            self.committed = True
            self.in_transaction_flag = False
            print("  Transaction committée")
        
        def rollback(self):
            self.rolled_back = True
            self.in_transaction_flag = False
            print("  Transaction rollbackée")
        
        def in_transaction(self):
            return self.in_transaction_flag
        
        def begin_nested(self):
            print("  Savepoint créé")
            return MockSavepoint()
        
        def add(self, obj):
            print(f"  Objet ajouté: {obj}")
        
        def flush(self):
            print("  Flush effectué")
        
        def refresh(self, obj):
            print(f"  Objet rafraîchi: {obj}")
    
    class MockSavepoint:
        def commit(self):
            print("  Savepoint committé")
        
        def rollback(self):
            print("  Savepoint rollbacké")
    
    # Test 1: Transaction réussie
    print("\n--- Test 1: Transaction réussie ---")
    session = MockSession()
    
    try:
        with atomic_operation(session, "Test transaction réussie") as tx_session:
            print("  Opération dans la transaction")
            # Simuler une opération réussie
            tx_session.add("test_object")
            tx_session.flush()
        
        print("✓ Transaction réussie")
        assert session.committed, "La transaction devrait être committée"
        assert not session.rolled_back, "La transaction ne devrait pas être rollbackée"
        
    except TransactionError as e:
        print(f"✗ Erreur inattendue: {e}")
    
    # Test 2: Transaction avec erreur et rollback
    print("\n--- Test 2: Transaction avec erreur ---")
    session = MockSession()
    
    try:
        with atomic_operation(session, "Test transaction avec erreur") as tx_session:
            print("  Opération dans la transaction")
            tx_session.add("test_object")
            # Simuler une erreur
            raise ValueError("Erreur simulée")
        
        print("✗ La transaction n'aurait pas dû réussir")
        
    except TransactionError as e:
        print(f"✓ Erreur capturée correctement: {e}")
        assert session.rolled_back, "La transaction devrait être rollbackée"
        assert not session.committed, "La transaction ne devrait pas être committée"
    
    # Test 3: Validation des montants
    print("\n--- Test 3: Validation des montants ---")
    
    try:
        # Montant valide
        amount1 = validate_amount("123.45", min_value=Decimal("0.01"), allow_zero=False)
        print(f"✓ Montant valide: {amount1}")
        
        # Montant invalide (négatif non autorisé)
        try:
            validate_amount("-10.00", allow_negative=False)
            print("✗ Montant négatif aurait dû être rejeté")
        except DecimalValidationError:
            print("✓ Montant négatif correctement rejeté")
        
        # Montant invalide (zéro non autorisé)
        try:
            validate_amount("0.00", allow_zero=False)
            print("✗ Montant zéro aurait dû être rejeté")
        except DecimalValidationError:
            print("✓ Montant zéro correctement rejeté")
        
        # Montant invalide (format incorrect)
        try:
            validate_amount("abc")
            print("✗ Format invalide aurait dû être rejeté")
        except DecimalValidationError:
            print("✓ Format invalide correctement rejeté")
        
    except Exception as e:
        print(f"✗ Erreur lors de la validation: {e}")


def test_treasury_transaction_simulation():
    """Simulation d'une transaction de trésorerie complète"""
    print("\n=== Simulation transaction trésorerie ===")
    
    class MockCashRegister:
        def __init__(self, id, name, balance):
            self.id = id
            self.name = name
            self.current_balance = balance
    
    class MockTransaction:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
            self.id = 123  # ID simulé
    
    class MockSession:
        def __init__(self):
            self.registers = {
                1: MockCashRegister(1, "Caisse principale", Decimal("1000.00")),
                2: MockCashRegister(2, "Caisse vente", Decimal("500.00"))
            }
            self.transactions = []
            self.committed = False
            self.rolled_back = False
        
        def query(self, model):
            return self
        
        def get(self, id):
            return self.registers.get(id)
        
        def add(self, obj):
            if isinstance(obj, MockTransaction):
                self.transactions.append(obj)
        
        def flush(self):
            pass
        
        def refresh(self, obj):
            pass
        
        def begin(self):
            pass
        
        def commit(self):
            self.committed = True
        
        def rollback(self):
            self.rolled_back = True
        
        def in_transaction(self):
            return False
    
    # Test de transfert entre caisses
    print("\n--- Test transfert entre caisses ---")
    session = MockSession()
    
    try:
        with atomic_operation(session, "Transfert entre caisses") as tx_session:
            # Récupérer les caisses
            from_register = tx_session.query(MockCashRegister).get(1)
            to_register = tx_session.query(MockCashRegister).get(2)
            
            if not from_register or not to_register:
                raise ValueError("Caisse non trouvée")
            
            # Valider le montant
            amount = validate_amount("100.00", min_value=Decimal("0.01"), allow_zero=False)
            
            # Vérifier le solde
            from_balance = validate_amount(from_register.current_balance)
            if from_balance < amount:
                raise ValueError("Solde insuffisant")
            
            # Créer les transactions
            out_transaction = MockTransaction(
                cash_register_id=from_register.id,
                amount=-amount,
                description="Transfert sortant"
            )
            
            in_transaction = MockTransaction(
                cash_register_id=to_register.id,
                amount=amount,
                description="Transfert entrant"
            )
            
            tx_session.add(out_transaction)
            tx_session.add(in_transaction)
            
            # Mettre à jour les soldes
            from_register.current_balance = from_balance - amount
            to_register.current_balance = validate_amount(to_register.current_balance) + amount
            
            print(f"  Transfert de {amount} DA")
            print(f"  Caisse source: {from_register.current_balance} DA")
            print(f"  Caisse destination: {to_register.current_balance} DA")
            print(f"  Transactions créées: {len(tx_session.transactions)}")
        
        print("✓ Transfert réussi")
        
    except (TransactionError, ValueError, DecimalValidationError) as e:
        print(f"✗ Erreur lors du transfert: {e}")
    
    # Test avec solde insuffisant
    print("\n--- Test solde insuffisant ---")
    session = MockSession()
    
    try:
        with atomic_operation(session, "Transfert avec solde insuffisant") as tx_session:
            from_register = tx_session.query(MockCashRegister).get(1)
            amount = validate_amount("2000.00")  # Plus que le solde disponible
            
            from_balance = validate_amount(from_register.current_balance)
            if from_balance < amount:
                raise ValueError("Solde insuffisant")
        
        print("✗ Le transfert n'aurait pas dû réussir")
        
    except (TransactionError, ValueError) as e:
        print(f"✓ Erreur correctement détectée: {e}")


if __name__ == "__main__":
    print("Test des transactions atomiques pour la trésorerie\n")
    
    test_atomic_operations()
    test_treasury_transaction_simulation()
    
    print("\n=== Tests terminés ===")
