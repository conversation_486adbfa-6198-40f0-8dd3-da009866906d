#!/usr/bin/env python3
"""
Script pour tester le contrôleur d'authentification et les permissions
"""
import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.controllers.auth_controller import AuthController
from app.utils.database import SessionLocal
from app.core.services.user_service import UserService

def test_auth_controller():
    """Teste le contrôleur d'authentification"""
    
    print("=== TEST DU CONTRÔLEUR D'AUTHENTIFICATION ===\n")
    
    # Créer une session de base de données
    db = SessionLocal()
    
    try:
        # Créer le service utilisateur
        user_service = UserService(db)
        
        # Simuler un utilisateur connecté avec des permissions
        auth_controller = AuthController()
        
        # Simuler les données d'un utilisateur admin
        admin_user_info = {
            'id': 1,
            'email': '<EMAIL>',
            'full_name': 'Nadjib',
            'permissions': [
                'inventory.create', 'repair.create', 'customer.create',
                'user.create', 'system.settings', 'admin'
            ],
            'roles': ['Administrateur'],
            'is_admin': True
        }
        
        # Simuler les données d'un utilisateur vendeur
        vendeur_user_info = {
            'id': 3,
            'email': '<EMAIL>',
            'full_name': 'Vendeur',
            'permissions': [
                'customer.create', 'repair.view', 'repair.create',
                'customer.edit', 'inventory.view'
            ],
            'roles': ['Vendeur']
        }
        
        # Test avec l'utilisateur admin
        print("1. Test avec utilisateur admin:")
        auth_controller.current_user = admin_user_info
        
        test_permissions = [
            'inventory.create',
            'repair.create', 
            'customer.create',
            'user.create',
            'system.settings'
        ]
        
        for perm in test_permissions:
            has_perm = auth_controller.has_permission(perm)
            status = "✅ AUTORISÉ" if has_perm else "❌ REFUSÉ"
            print(f"   {perm}: {status}")
            
        # Test avec l'utilisateur vendeur
        print(f"\n2. Test avec utilisateur vendeur:")
        auth_controller.current_user = vendeur_user_info
        
        for perm in test_permissions:
            has_perm = auth_controller.has_permission(perm)
            status = "✅ AUTORISÉ" if has_perm else "❌ REFUSÉ"
            print(f"   {perm}: {status}")
            
        # Test avec permissions sous forme de chaîne JSON (problème potentiel)
        print(f"\n3. Test avec permissions sous forme de chaîne JSON:")
        vendeur_user_info_json = {
            'id': 3,
            'email': '<EMAIL>',
            'full_name': 'Vendeur',
            'permissions': '["customer.create", "repair.view", "repair.create", "customer.edit", "inventory.view"]',
            'roles': '["Vendeur"]'
        }
        
        auth_controller.current_user = vendeur_user_info_json
        
        for perm in test_permissions:
            has_perm = auth_controller.has_permission(perm)
            status = "✅ AUTORISÉ" if has_perm else "❌ REFUSÉ"
            print(f"   {perm}: {status}")
            
        # Test avec permissions sous forme de chaîne simple
        print(f"\n4. Test avec permissions sous forme de chaîne simple:")
        vendeur_user_info_str = {
            'id': 3,
            'email': '<EMAIL>',
            'full_name': 'Vendeur',
            'permissions': 'customer.create,repair.view,repair.create,customer.edit,inventory.view',
            'roles': 'Vendeur'
        }
        
        auth_controller.current_user = vendeur_user_info_str
        
        for perm in test_permissions:
            has_perm = auth_controller.has_permission(perm)
            status = "✅ AUTORISÉ" if has_perm else "❌ REFUSÉ"
            print(f"   {perm}: {status}")
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    test_auth_controller()
