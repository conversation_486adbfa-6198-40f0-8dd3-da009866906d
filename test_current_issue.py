import asyncio
import sys
sys.path.append('.')

from app.core.services.user_service import UserService
from app.core.services.permission_service import RoleService
from app.core.models.user import UserCreate
from app.utils.database import SessionLocal
from app.ui.views.user.user_table_model import UserTableModel

async def test_current_issue():
    """Test pour reproduire le problème actuel avec les nouveaux utilisateurs"""
    
    print("=== Test du problème actuel ===")
    
    # Créer une session de base de données
    db = SessionLocal()
    
    try:
        # Créer les services
        user_service = UserService(db)
        role_service = RoleService(db)
        
        # Récupérer un rôle existant
        roles = await role_service.get_all()
        if not roles:
            print("❌ Aucun rôle trouvé dans la base de données")
            return
        
        test_role = roles[0]  # Prendre le premier rôle disponible
        print(f"✅ Rôle trouvé: {test_role.name} (ID: {test_role.id})")
        
        # Créer un nouvel utilisateur de test
        test_email = "<EMAIL>"
        
        # Supprimer l'utilisateur s'il existe déjà
        existing_user = await user_service.get_user_by_email(test_email)
        if existing_user:
            print(f"🗑️  Suppression de l'utilisateur existant: {test_email}")
            await user_service.delete_user(existing_user.id, deleted_by_id=1)
        
        # Créer les données du nouvel utilisateur
        user_data = UserCreate(
            email=test_email,
            password="password123",
            full_name="Test Problème Utilisateur",
            phone="0123456789",
            position="Testeur",
            department="IT",
            role_ids=[test_role.id]
        )
        
        print(f"📝 Création de l'utilisateur: {user_data.email}")
        
        # Créer l'utilisateur
        new_user = await user_service.create_user(user_data, created_by_id=1)
        
        print(f"✅ Utilisateur créé:")
        print(f"  ID: {new_user.id}")
        print(f"  Email: {new_user.email}")
        print(f"  Nom: {new_user.full_name}")
        print(f"  Dernière connexion: {new_user.last_login}")
        
        # Tester l'affichage avec UserTableModel
        print(f"\n=== Test de l'affichage dans UserTableModel ===")
        
        # Créer le modèle de table
        table_model = UserTableModel()
        
        # Charger les utilisateurs
        await table_model.load_data()
        
        print(f"Nombre d'utilisateurs chargés: {len(table_model.users)}")
        
        # Chercher notre utilisateur
        user_found = False
        for i, user in enumerate(table_model.users):
            if user.email == test_email:
                user_found = True
                print(f"✅ Utilisateur trouvé à la ligne {i}")
                
                # Tester spécifiquement la colonne "Dernière connexion" (colonne 5)
                from PyQt6.QtCore import Qt
                index = table_model.index(i, 5)  # Colonne dernière connexion
                value = table_model.data(index, Qt.ItemDataRole.DisplayRole)
                print(f"  Valeur affichée pour 'Dernière connexion': '{value}'")
                
                # Vérifier si c'est le message d'erreur
                if "Aucune date de dernière connexion trouvée" in str(value):
                    print("❌ PROBLÈME DÉTECTÉ: Le message d'erreur apparaît encore")
                elif value == "Jamais connecté":
                    print("✅ CORRECT: Affichage 'Jamais connecté'")
                else:
                    print(f"⚠️  INATTENDU: Valeur '{value}'")
                
                break
        
        if not user_found:
            print("❌ Utilisateur non trouvé dans la table")
        
        # Tester directement la méthode _format_datetime
        print(f"\n=== Test direct de _format_datetime ===")
        formatted_none = table_model._format_datetime(None)
        print(f"_format_datetime(None) = '{formatted_none}'")
        
        # Nettoyer
        print(f"\n=== Nettoyage ===")
        await user_service.delete_user(new_user.id, deleted_by_id=1)
        print(f"🗑️  Utilisateur de test supprimé")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
        print("✅ Session de base de données fermée")

if __name__ == "__main__":
    asyncio.run(test_current_issue())
