#!/usr/bin/env python3
"""
Script de test pour vérifier l'intégration FinanceService avec les opérations financières clients
"""

import asyncio
import sys
import os
from datetime import datetime, timezone
from decimal import Decimal

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.finance_service import FinanceService
from app.core.services.customer_service import CustomerService
from app.core.models.customer import Customer, CustomerTransaction
from app.core.models.repair import RepairOrder
from app.core.models.treasury import CashRegister, CashRegisterType, PaymentMethod as TreasuryPaymentMethod
from app.core.models.treasury import CashTransaction


async def test_customer_finance_integration():
    """Test l'intégration FinanceService avec les opérations clients"""
    print("🧪 Test de l'intégration FinanceService - Clients")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        finance_service = FinanceService(db)
        customer_service = CustomerService(db)
        
        # Test 1: Créer un client de test
        print("\n1. Création d'un client de test...")
        test_customer = Customer(
            name="Client Test Finance",
            email="<EMAIL>",
            phone="0123456789",
            address="123 Rue Test",
            credit_limit=1000.0,
            current_balance=0.0,
            default_payment_terms=30
        )
        db.add(test_customer)
        db.commit()
        db.refresh(test_customer)
        print(f"✅ Client créé avec ID: {test_customer.id}")
        print(f"   Solde initial: {test_customer.current_balance:.2f} DA")
        
        # Test 2: Enregistrer une transaction client via FinanceService
        print("\n2. Test de transaction client via FinanceService...")
        try:
            # Trouver une caisse active
            cash_register = db.query(CashRegister).filter(CashRegister.is_active == True).first()
            if not cash_register:
                print("❌ Aucune caisse active trouvée")
                return
            
            transaction = await finance_service.record_customer_transaction(
                customer_id=test_customer.id,
                amount=150.0,  # Versement client
                description="Versement test via FinanceService",
                transaction_type="payment",
                reference_number="TEST-CUST-001",
                processed_by=1,
                cash_register_id=cash_register.id,
                payment_method=TreasuryPaymentMethod.cash,
            )
            print(f"✅ Transaction client créée avec ID: {transaction.id}")
            
            # Vérifier la mise à jour du solde
            db.refresh(test_customer)
            print(f"✅ Solde client mis à jour: {test_customer.current_balance:.2f} DA")
            
            # Vérifier l'écriture en trésorerie
            treasury_transaction = db.query(CashTransaction).filter(
                CashTransaction.cash_register_id == cash_register.id,
                CashTransaction.amount == 150.0
            ).order_by(CashTransaction.created_at.desc()).first()
            
            if treasury_transaction:
                print(f"✅ Écriture trésorerie créée avec ID: {treasury_transaction.id}")
            else:
                print("⚠️  Aucune écriture trésorerie trouvée")
            
        except Exception as e:
            print(f"❌ Erreur lors de la transaction client: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 3: Créer une réparation de test pour le client
        print("\n3. Création d'une réparation de test pour le client...")
        from app.core.models.repair import RepairStatus, RepairPriority, PaymentStatus

        test_repair = RepairOrder(
            number="REP-CUST-001",
            customer_id=test_customer.id,
            customer_name=test_customer.name,
            brand="Test Brand",
            model="Test Model",
            description="Test repair for customer finance",
            reported_issue="Test issue for customer finance",
            status=RepairStatus.COMPLETED,
            priority=RepairPriority.NORMAL,
            total_cost=200.0,
            labor_cost=150.0,
            parts_cost=50.0,
            final_amount=200.0,
            total_paid=0.0,
            payment_status=PaymentStatus.PENDING,
        )
        db.add(test_repair)
        db.commit()
        db.refresh(test_repair)
        print(f"✅ Réparation créée avec ID: {test_repair.id}")
        
        # Test 4: Paiement de réparation via interface client
        print("\n4. Test de paiement de réparation via interface client...")
        try:
            customer_transaction, repair_payment = await finance_service.pay_customer_repair(
                customer_id=test_customer.id,
                repair_id=test_repair.id,
                amount=200.0,
                method=TreasuryPaymentMethod.cash,
                processed_by=1,
                reference_number="TEST-REPAIR-PAY-001",
                cash_register_id=cash_register.id,
            )
            
            print(f"✅ Transaction client créée avec ID: {customer_transaction.id}")
            print(f"✅ Paiement réparation créé avec ID: {repair_payment.id}")
            
            # Vérifier les mises à jour
            db.refresh(test_customer)
            db.refresh(test_repair)
            
            print(f"✅ Nouveau solde client: {test_customer.current_balance:.2f} DA")
            print(f"✅ Statut paiement réparation: {test_repair.payment_status}")
            print(f"✅ Montant payé réparation: {test_repair.total_paid:.2f} DA")
            
        except Exception as e:
            print(f"❌ Erreur lors du paiement réparation client: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 5: Test via CustomerService (compatibilité)
        print("\n5. Test de compatibilité via CustomerService...")
        try:
            from app.core.models.customer import CustomerTransactionPydantic
            
            transaction_data = CustomerTransactionPydantic(
                customer_id=test_customer.id,
                amount=-50.0,  # Débit (ajustement)
                description="Ajustement via CustomerService",
                transaction_type="adjustment",
                reference_number="TEST-ADJ-001",
                processed_by=1
            )
            
            compat_transaction = await customer_service.record_transaction(transaction_data)
            print(f"✅ Transaction via CustomerService créée avec ID: {compat_transaction.id}")
            
            # Vérifier la mise à jour du solde
            db.refresh(test_customer)
            print(f"✅ Solde final client: {test_customer.current_balance:.2f} DA")
            
        except Exception as e:
            print(f"❌ Erreur lors du test de compatibilité: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("✅ Tests d'intégration client-FinanceService terminés!")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def test_customer_finance_consistency():
    """Test la cohérence des opérations financières clients"""
    print("\n🔍 Test de cohérence des finances clients")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Compter les transactions clients récentes
        recent_customer_transactions = db.query(CustomerTransaction).filter(
            CustomerTransaction.transaction_date >= datetime.now(timezone.utc).replace(hour=0, minute=0, second=0)
        ).count()
        
        # Compter les écritures de trésorerie récentes
        recent_treasury_transactions = db.query(CashTransaction).filter(
            CashTransaction.transaction_date >= datetime.now(timezone.utc).replace(hour=0, minute=0, second=0)
        ).count()
        
        print(f"📊 Transactions clients récentes: {recent_customer_transactions}")
        print(f"📊 Écritures trésorerie récentes: {recent_treasury_transactions}")
        
        # Vérifier les soldes clients
        customers_with_transactions = db.query(Customer).filter(
            Customer.current_balance != 0
        ).all()
        
        print(f"📊 Clients avec solde non nul: {len(customers_with_transactions)}")
        
        for customer in customers_with_transactions[:5]:  # Afficher les 5 premiers
            transactions_sum = db.query(CustomerTransaction).filter(
                CustomerTransaction.customer_id == customer.id
            ).all()
            
            calculated_balance = sum(t.amount for t in transactions_sum)
            
            print(f"   Client {customer.id}: Solde DB={customer.current_balance:.2f}, Calculé={calculated_balance:.2f}")
            
            if abs(customer.current_balance - calculated_balance) > 0.01:
                print(f"   ⚠️  Incohérence détectée pour le client {customer.id}")
            else:
                print(f"   ✅ Cohérence vérifiée pour le client {customer.id}")
        
        print("\n✅ Vérification de cohérence terminée")
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de cohérence: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 Démarrage des tests d'intégration client-FinanceService")
    
    # Exécuter les tests
    asyncio.run(test_customer_finance_integration())
    asyncio.run(test_customer_finance_consistency())
    
    print("\n🎉 Tous les tests clients sont terminés!")
