#!/usr/bin/env python3
"""
Test final pour vérifier que l'historique client fonctionne correctement
"""

import sys
import os
from datetime import datetime, timezone

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.customer_service import CustomerService
from app.core.models.customer import Customer
from sqlalchemy import text


def test_customer_history_widget_simulation():
    """Simule le fonctionnement du widget d'historique client"""
    print("🧪 Test de simulation du widget d'historique client")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        customer_service = CustomerService(db)
        
        # Trouver un client avec des données
        customers = db.query(Customer).filter(Customer.current_balance != 0).all()
        if not customers:
            print("❌ Aucun client avec des données trouvé")
            return
        
        test_customer = customers[0]
        customer_id = test_customer.id
        
        print(f"📊 Test avec le client ID: {customer_id} ({test_customer.name})")
        print(f"   Solde actuel: {test_customer.current_balance:.2f} DA")
        
        # Simuler la méthode refresh() du widget d'historique
        print("\n1. Simulation de la méthode refresh() du widget...")
        
        import asyncio
        
        async def load_data():
            # Récupérer ventes via service
            sales = await customer_service.get_customer_sales(customer_id, limit=500)
            
            # Récupérer transactions via service
            transactions = await customer_service.get_customer_transactions(customer_id, limit=500)
            
            # Récupérer réparations via requête SQL
            repairs = db.execute(
                text("""
                SELECT id, created_at, final_amount, total_paid, payment_status
                FROM repair_orders
                WHERE customer_id = :cid
                ORDER BY created_at DESC
                LIMIT 500
                """),
                {"cid": customer_id}
            ).fetchall()
            
            return sales, transactions, repairs
        
        # Exécuter de manière asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        sales, transactions, repairs = loop.run_until_complete(load_data())
        loop.close()
        
        print(f"   📊 Ventes récupérées: {len(sales)}")
        print(f"   💰 Transactions récupérées: {len(transactions)}")
        print(f"   🔧 Réparations récupérées: {len(repairs)}")
        
        # Fusionner en une liste normalisée (comme dans le widget)
        print("\n2. Fusion des données...")
        rows = []
        
        # Traiter les ventes (objets Sale)
        for sale in sales:
            due = (sale.final_amount or 0.0) - (sale.total_paid or 0.0)
            rows.append({
                'type': 'Vente',
                'id': sale.id,
                'date': sale.date,
                'reference': f"SALE-{sale.id}",
                'description': f"Vente - dû: {due:.2f} DA",
                'amount': float(sale.final_amount or 0.0),
                'status': str(sale.payment_status) if sale.payment_status else ""
            })
        
        # Traiter les réparations (tuples SQL)
        for r in repairs:
            due = (r[2] or 0.0) - (r[3] or 0.0)
            rows.append({
                'type': 'Réparation',
                'id': r[0],
                'date': r[1],
                'reference': f"REPAIR-{r[0]}",
                'description': f"Réparation - dû: {due:.2f} DA",
                'amount': float(r[2] or 0.0),
                'status': str(r[4]) if r[4] is not None else ""
            })
        
        # Traiter les transactions (objets CustomerTransaction)
        for tx in transactions:
            rows.append({
                'type': 'Versement',
                'id': tx.id,
                'date': tx.transaction_date,
                'reference': tx.reference_number or f"TX-{tx.id}",
                'description': tx.description or tx.transaction_type or "Transaction",
                'amount': float(tx.amount or 0.0),
                'status': tx.transaction_type or ""
            })
        
        # Trier par date desc
        rows.sort(key=lambda x: x.get('date') or datetime.min, reverse=True)
        
        print(f"   📋 Total d'entrées dans l'historique: {len(rows)}")
        
        # Afficher les entrées comme dans le widget
        print("\n3. Contenu de l'historique (comme affiché dans le widget):")
        print("   " + "-" * 80)
        print("   | Type        | Référence      | Montant      | Description")
        print("   " + "-" * 80)
        
        for i, row in enumerate(rows[:10]):  # Afficher les 10 premiers
            type_str = row['type'][:10].ljust(10)
            ref_str = row['reference'][:12].ljust(12)
            amount_str = f"{row['amount']:.2f} DA".rjust(10)
            desc_str = row['description'][:30]
            print(f"   | {type_str} | {ref_str} | {amount_str} | {desc_str}")
        
        if len(rows) > 10:
            print(f"   | ... et {len(rows) - 10} autres entrées")
        
        print("   " + "-" * 80)
        
        # Test du widget de paiements
        print("\n4. Test du widget de paiements clients...")
        
        async def load_payment_transactions():
            return await customer_service.get_customer_transactions(customer_id, limit=100)
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        payment_transactions = loop.run_until_complete(load_payment_transactions())
        loop.close()
        
        print(f"   💰 Transactions de paiement: {len(payment_transactions)}")
        
        if payment_transactions:
            print("   Dernières transactions:")
            for tx in payment_transactions[:5]:
                print(f"      - {tx.transaction_date}: {tx.amount:.2f} DA ({tx.transaction_type}) - {tx.description}")
        
        # Résumé
        print("\n" + "=" * 60)
        if len(rows) > 0:
            print("✅ L'historique client fonctionne correctement!")
            print(f"   📊 {len(rows)} entrées d'historique disponibles")
            print(f"   💰 {len(payment_transactions)} transactions de paiement")
            print("   🎯 Les widgets devraient maintenant afficher les données")
        else:
            print("⚠️  Aucune donnée d'historique trouvée")
            print("   💡 Créez des ventes, réparations ou transactions pour ce client")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


def test_all_customers_summary():
    """Affiche un résumé de tous les clients avec leurs données"""
    print("\n📊 Résumé de tous les clients")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        customers = db.query(Customer).all()
        
        print(f"Nombre total de clients: {len(customers)}")
        print("\nDétails par client:")
        
        for customer in customers:
            # Compter les données pour chaque client
            sales_count = db.execute(
                text("SELECT COUNT(*) FROM sales WHERE customer_id = :cid"),
                {"cid": customer.id}
            ).scalar()
            
            repairs_count = db.execute(
                text("SELECT COUNT(*) FROM repair_orders WHERE customer_id = :cid"),
                {"cid": customer.id}
            ).scalar()
            
            transactions_count = db.execute(
                text("SELECT COUNT(*) FROM customer_transactions WHERE customer_id = :cid"),
                {"cid": customer.id}
            ).scalar()
            
            total_data = sales_count + repairs_count + transactions_count
            
            print(f"   Client {customer.id}: {customer.name}")
            print(f"      Solde: {customer.current_balance:.2f} DA")
            print(f"      Ventes: {sales_count}, Réparations: {repairs_count}, Transactions: {transactions_count}")
            print(f"      Total données historique: {total_data}")
            
            if total_data > 0:
                print("      ✅ Données disponibles pour l'historique")
            else:
                print("      ⚠️  Aucune donnée d'historique")
            print()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 Test final de l'historique client")
    
    # Test principal
    test_customer_history_widget_simulation()
    
    # Résumé de tous les clients
    test_all_customers_summary()
    
    print("\n🎉 Test terminé!")
