#!/usr/bin/env python3
"""
Script de test pour vérifier que l'historique client fonctionne après correction
"""

import sys
import os
from datetime import datetime, timezone

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.customer_service import CustomerService
from app.core.services.finance_service import FinanceService
from app.core.models.customer import Customer
from app.core.models.sale import Sale, PaymentStatus as SalePaymentStatus
from app.core.models.repair import RepairOrder, RepairStatus, RepairPriority, PaymentStatus as RepairPaymentStatus
from app.core.models.treasury import PaymentMethod as TreasuryPaymentMethod, CashRegister
from sqlalchemy import text


async def test_customer_history_data():
    """Test la création de données pour l'historique client"""
    print("🧪 Test de l'historique client - Création de données")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        finance_service = FinanceService(db)
        customer_service = CustomerService(db)
        
        # 1. Créer un client de test complet
        print("\n1. Création d'un client de test complet...")
        test_customer = Customer(
            name="Client Historique Test",
            email="<EMAIL>",
            phone="0123456789",
            address="123 Rue Historique",
            credit_limit=2000.0,
            current_balance=0.0,
            default_payment_terms=30
        )
        db.add(test_customer)
        db.commit()
        db.refresh(test_customer)
        print(f"✅ Client créé avec ID: {test_customer.id}")
        
        # 2. Créer une vente pour ce client
        print("\n2. Création d'une vente...")
        test_sale = Sale(
            customer_id=test_customer.id,
            date=datetime.now(timezone.utc),
            final_amount=500.0,
            total_paid=0.0,
            payment_status=SalePaymentStatus.PENDING,
            notes="Vente test pour historique client"
        )
        db.add(test_sale)
        db.commit()
        db.refresh(test_sale)
        print(f"✅ Vente créée avec ID: {test_sale.id}")
        
        # 3. Créer une réparation pour ce client
        print("\n3. Création d'une réparation...")
        test_repair = RepairOrder(
            number=f"REP-HIST-{test_customer.id}",
            customer_id=test_customer.id,
            customer_name=test_customer.name,
            brand="Test Brand",
            model="Test Model",
            description="Réparation test pour historique",
            reported_issue="Problème test",
            status=RepairStatus.COMPLETED,
            priority=RepairPriority.NORMAL,
            total_cost=300.0,
            labor_cost=200.0,
            parts_cost=100.0,
            final_amount=300.0,
            total_paid=0.0,
            payment_status=RepairPaymentStatus.PENDING,
        )
        db.add(test_repair)
        db.commit()
        db.refresh(test_repair)
        print(f"✅ Réparation créée avec ID: {test_repair.id}")
        
        # 4. Effectuer un paiement de vente via FinanceService
        print("\n4. Paiement de vente via FinanceService...")
        try:
            # Trouver une caisse active
            cash_register = db.query(CashRegister).filter(CashRegister.is_active == True).first()
            if cash_register:
                sale_payment = await finance_service.pay_sale(
                    sale_id=test_sale.id,
                    amount=250.0,
                    method="cash",
                    processed_by=1,
                    reference_number="HIST-SALE-PAY-001",
                )
                print(f"✅ Paiement vente créé avec ID: {sale_payment.id}")
            else:
                print("⚠️  Aucune caisse active trouvée pour le paiement de vente")
        except Exception as e:
            print(f"❌ Erreur paiement vente: {e}")
        
        # 5. Effectuer un paiement de réparation via FinanceService
        print("\n5. Paiement de réparation via FinanceService...")
        try:
            if cash_register:
                customer_transaction, repair_payment = await finance_service.pay_customer_repair(
                    customer_id=test_customer.id,
                    repair_id=test_repair.id,
                    amount=150.0,
                    method=TreasuryPaymentMethod.cash,
                    processed_by=1,
                    reference_number="HIST-REPAIR-PAY-001",
                    cash_register_id=cash_register.id,
                )
                print(f"✅ Transaction client créée avec ID: {customer_transaction.id}")
                print(f"✅ Paiement réparation créé avec ID: {repair_payment.id}")
            else:
                print("⚠️  Aucune caisse active trouvée pour le paiement de réparation")
        except Exception as e:
            print(f"❌ Erreur paiement réparation: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. Ajouter une transaction client manuelle
        print("\n6. Transaction client manuelle...")
        try:
            manual_transaction = await finance_service.record_customer_transaction(
                customer_id=test_customer.id,
                amount=100.0,
                description="Versement manuel pour test historique",
                transaction_type="payment",
                reference_number="HIST-MANUAL-001",
                processed_by=1,
            )
            print(f"✅ Transaction manuelle créée avec ID: {manual_transaction.id}")
        except Exception as e:
            print(f"❌ Erreur transaction manuelle: {e}")
        
        print(f"\n✅ Données de test créées pour le client ID: {test_customer.id}")
        return test_customer.id
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        db.close()


def test_customer_history_display(customer_id):
    """Test l'affichage de l'historique client"""
    print(f"\n🔍 Test de l'affichage de l'historique client ID: {customer_id}")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Simuler le code du widget d'historique
        print("\n1. Test des requêtes SQL de l'historique...")
        
        # Récupérer ventes
        sales = db.execute(
            text("""
            SELECT id, date, final_amount, total_paid, payment_status
            FROM sales
            WHERE customer_id = :cid
            ORDER BY date DESC
            LIMIT 500
            """),
            {"cid": customer_id}
        ).fetchall()
        
        print(f"   📊 Ventes trouvées: {len(sales)}")
        for sale in sales:
            due = (sale[2] or 0.0) - (sale[3] or 0.0)
            print(f"      - Vente {sale[0]}: {sale[1]}, {sale[2]:.2f} DA, payé: {sale[3]:.2f} DA, dû: {due:.2f} DA")
        
        # Récupérer réparations
        repairs = db.execute(
            text("""
            SELECT id, created_at, final_amount, total_paid, payment_status
            FROM repair_orders
            WHERE customer_id = :cid
            ORDER BY created_at DESC
            LIMIT 500
            """),
            {"cid": customer_id}
        ).fetchall()
        
        print(f"   🔧 Réparations trouvées: {len(repairs)}")
        for repair in repairs:
            due = (repair[2] or 0.0) - (repair[3] or 0.0)
            print(f"      - Réparation {repair[0]}: {repair[1]}, {repair[2]:.2f} DA, payé: {repair[3]:.2f} DA, dû: {due:.2f} DA")
        
        # Récupérer transactions
        transactions = db.execute(
            text("""
            SELECT id, transaction_date, amount, reference_number, description, transaction_type
            FROM customer_transactions
            WHERE customer_id = :cid
            ORDER BY transaction_date DESC
            LIMIT 500
            """),
            {"cid": customer_id}
        ).fetchall()
        
        print(f"   💰 Transactions trouvées: {len(transactions)}")
        for tx in transactions:
            print(f"      - Transaction {tx[0]}: {tx[1]}, {tx[2]:.2f} DA, ref: {tx[3]}, type: {tx[5]}")
            print(f"        Description: {tx[4]}")
        
        # 2. Simuler la fusion des données comme dans le widget
        print("\n2. Simulation de la fusion des données...")
        rows = []
        
        for s in sales:
            due = (s[2] or 0.0) - (s[3] or 0.0)
            rows.append({
                'type': 'Vente',
                'id': s[0],
                'date': s[1],
                'reference': f"SALE-{s[0]}",
                'description': f"Vente - dû: {due:.2f}",
                'amount': float(s[2] or 0.0),
                'status': str(s[4]) if s[4] is not None else ""
            })
        
        for r in repairs:
            due = (r[2] or 0.0) - (r[3] or 0.0)
            rows.append({
                'type': 'Réparation',
                'id': r[0],
                'date': r[1],
                'reference': f"REPAIR-{r[0]}",
                'description': f"Réparation - dû: {due:.2f}",
                'amount': float(r[2] or 0.0),
                'status': str(r[4]) if r[4] is not None else ""
            })
        
        for t in transactions:
            rows.append({
                'type': 'Versement',
                'id': t[0],
                'date': t[1],
                'reference': t[3] or f"TX-{t[0]}",
                'description': t[4] or (t[5] or ""),
                'amount': float(t[2] or 0.0),
                'status': t[5] or ""
            })
        
        # Trier par date desc
        rows.sort(key=lambda x: x.get('date') or datetime.min, reverse=True)
        
        print(f"   📋 Total d'entrées dans l'historique: {len(rows)}")
        for i, row in enumerate(rows):
            print(f"      {i+1}. {row['type']} - {row['reference']} - {row['amount']:.2f} DA - {row['description']}")
        
        if len(rows) > 0:
            print("\n✅ L'historique client devrait maintenant afficher des données!")
        else:
            print("\n❌ Aucune donnée dans l'historique")
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'affichage: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def main():
    """Fonction principale"""
    print("🚀 Test de correction de l'historique client")
    
    # Créer des données de test
    customer_id = await test_customer_history_data()
    
    if customer_id:
        # Tester l'affichage
        test_customer_history_display(customer_id)
    
    print("\n🎉 Test terminé!")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
