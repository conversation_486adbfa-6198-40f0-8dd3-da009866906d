#!/usr/bin/env python3
"""
Test final pour vérifier que l'historique client fonctionne après correction
"""

import sys
import os
import asyncio
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.customer_service import CustomerService
from app.core.models.customer import Customer
from app.core.models.sale import Sale
from app.core.models.repair import RepairOrder
from app.core.models.customer import CustomerTransaction
from sqlalchemy import text


async def test_customer_history_widget_logic():
    """Test de la logique du widget d'historique client"""
    print("🧪 Test de la logique du widget d'historique client")
    print("=" * 60)
    
    db = SessionLocal()
    service = CustomerService(db)
    
    try:
        # Trouver un client avec des données
        customers = db.query(Customer).limit(10).all()
        customer = None
        
        for c in customers:
            sales_count = db.query(Sale).filter(Sale.customer_id == c.id).count()
            repairs_count = db.query(RepairOrder).filter(RepairOrder.customer_id == c.id).count()
            transactions_count = db.query(CustomerTransaction).filter(CustomerTransaction.customer_id == c.id).count()
            
            if sales_count + repairs_count + transactions_count > 0:
                customer = c
                break
        
        if not customer and customers:
            customer = customers[0]  # Prendre le premier même sans données
        
        if not customer:
            print("❌ Aucun client trouvé")
            return
        
        print(f"👤 Client testé: {customer.name} (ID: {customer.id})")
        
        # Simuler exactement la logique du widget refresh()
        print(f"\n1️⃣ Récupération des données...")
        
        # Récupérer ventes via service
        sales = await service.get_customer_sales(customer.id, limit=500)
        print(f"   📊 Ventes: {len(sales)}")
        
        # Récupérer transactions via service
        transactions = await service.get_customer_transactions(customer.id, limit=500)
        print(f"   📊 Transactions: {len(transactions)}")
        
        # Récupérer réparations via requête SQL
        repairs = db.execute(
            text("""
            SELECT id, created_at, final_amount, total_paid, payment_status
            FROM repair_orders
            WHERE customer_id = :cid
            ORDER BY created_at DESC
            LIMIT 500
            """),
            {"cid": customer.id}
        ).fetchall()
        print(f"   📊 Réparations: {len(repairs)}")
        
        # Fusionner en une liste normalisée
        print(f"\n2️⃣ Fusion des données...")
        rows = []
        
        # Traiter les ventes (objets Sale)
        for sale in sales:
            due = (sale.final_amount or 0.0) - (sale.total_paid or 0.0)
            rows.append({
                'type': 'Vente',
                'id': sale.id,
                'date': sale.date,
                'reference': f"SALE-{sale.id}",
                'description': f"Vente - dû: {due:.2f} DA",
                'amount': float(sale.final_amount or 0.0),
                'status': str(sale.payment_status) if sale.payment_status else ""
            })
            print(f"   ✅ Vente ajoutée: {sale.id}, date: {sale.date} ({type(sale.date)})")
        
        # Traiter les réparations (tuples SQL)
        for r in repairs:
            due = (r[2] or 0.0) - (r[3] or 0.0)
            rows.append({
                'type': 'Réparation',
                'id': r[0],
                'date': r[1],
                'reference': f"REPAIR-{r[0]}",
                'description': f"Réparation - dû: {due:.2f} DA",
                'amount': float(r[2] or 0.0),
                'status': str(r[4]) if r[4] is not None else ""
            })
            print(f"   ✅ Réparation ajoutée: {r[0]}, date: {r[1]} ({type(r[1])})")
        
        # Traiter les transactions (objets CustomerTransaction)
        for tx in transactions:
            rows.append({
                'type': 'Versement',
                'id': tx.id,
                'date': tx.transaction_date,
                'reference': tx.reference_number or f"TX-{tx.id}",
                'description': tx.description or tx.transaction_type or "Transaction",
                'amount': float(tx.amount or 0.0),
                'status': tx.transaction_type or ""
            })
            print(f"   ✅ Transaction ajoutée: {tx.id}, date: {tx.transaction_date} ({type(tx.transaction_date)})")
        
        print(f"\n   📋 Total lignes créées: {len(rows)}")
        
        # Test du tri avec la nouvelle fonction de normalisation
        print(f"\n3️⃣ Test du tri des données...")
        
        def normalize_date(date_value):
            if not date_value:
                return datetime.min
            if isinstance(date_value, datetime):
                return date_value
            if isinstance(date_value, str):
                try:
                    return datetime.fromisoformat(date_value.replace('Z', '+00:00'))
                except:
                    try:
                        return datetime.strptime(date_value, "%Y-%m-%d %H:%M:%S.%f")
                    except:
                        try:
                            return datetime.strptime(date_value, "%Y-%m-%d %H:%M:%S")
                        except:
                            return datetime.min
            return datetime.min
        
        try:
            # Tester la normalisation des dates
            print(f"   🔍 Test de normalisation des dates:")
            for i, row in enumerate(rows[:3]):  # Tester les 3 premières
                original_date = row.get('date')
                normalized_date = normalize_date(original_date)
                print(f"      {i+1}. Original: {original_date} ({type(original_date)}) → Normalisé: {normalized_date}")
            
            # Effectuer le tri
            rows.sort(key=lambda x: normalize_date(x.get('date')), reverse=True)
            print(f"   ✅ Tri réussi!")
            
        except Exception as e:
            print(f"   ❌ Erreur lors du tri: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # Afficher le résultat final
        print(f"\n4️⃣ Résultat final de l'historique:")
        
        if rows:
            print(f"   ✅ {len(rows)} entrées d'historique générées")
            print(f"   📝 Aperçu des entrées (triées par date desc):")
            
            for i, row in enumerate(rows[:10]):  # Afficher les 10 premières
                date_str = row['date'].strftime("%Y-%m-%d %H:%M") if isinstance(row['date'], datetime) else str(row['date'])
                print(f"      {i+1:2d}. {date_str} | {row['type']:12} | {row['reference']:15} | {row['amount']:8.2f} DA")
            
            if len(rows) > 10:
                print(f"      ... et {len(rows) - 10} autres entrées")
            
            print(f"\n   🎯 L'historique client devrait maintenant s'afficher correctement!")
            
        else:
            print(f"   ⚠️  Aucune entrée d'historique générée")
            print(f"   💡 Le client n'a peut-être pas de données d'historique")
        
        return len(rows) > 0
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def test_date_normalization():
    """Test spécifique de la fonction de normalisation des dates"""
    print(f"\n🔧 Test de la fonction de normalisation des dates")
    print("=" * 60)
    
    def normalize_date(date_value):
        if not date_value:
            return datetime.min
        if isinstance(date_value, datetime):
            return date_value
        if isinstance(date_value, str):
            try:
                return datetime.fromisoformat(date_value.replace('Z', '+00:00'))
            except:
                try:
                    return datetime.strptime(date_value, "%Y-%m-%d %H:%M:%S.%f")
                except:
                    try:
                        return datetime.strptime(date_value, "%Y-%m-%d %H:%M:%S")
                    except:
                        return datetime.min
        return datetime.min
    
    # Test avec différents formats de dates
    test_dates = [
        datetime.now(),
        "2025-09-10 14:39:24.628621",
        "2025-09-10 14:39:24",
        "2025-09-10T14:39:24.628621",
        "2025-09-10T14:39:24Z",
        None,
        "",
        "invalid_date"
    ]
    
    print("📋 Tests de normalisation:")
    for i, test_date in enumerate(test_dates):
        try:
            result = normalize_date(test_date)
            print(f"   {i+1}. {str(test_date):30} → {result} ✅")
        except Exception as e:
            print(f"   {i+1}. {str(test_date):30} → ERREUR: {e} ❌")
    
    print("✅ Test de normalisation terminé")


def main():
    """Fonction principale"""
    print("🚀 Test final de l'historique client")
    print("=" * 70)
    
    # Test de la normalisation des dates
    test_date_normalization()
    
    # Test de la logique complète du widget
    success = asyncio.run(test_customer_history_widget_logic())
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Test réussi ! L'historique client devrait maintenant fonctionner")
        print("\n✅ Corrections appliquées:")
        print("   - Fonction de normalisation des dates ajoutée")
        print("   - Tri corrigé pour gérer les types de dates mixtes")
        print("   - Gestion des chaînes de caractères et objets datetime")
    else:
        print("⚠️  Test échoué ou aucune donnée d'historique")
    
    print(f"\n📝 Note: Si l'historique ne s'affiche toujours pas dans l'interface,")
    print(f"    vérifiez que le client sélectionné a des données (ventes, réparations, transactions)")


if __name__ == "__main__":
    main()
