#!/usr/bin/env python3
"""
Script de test pour vérifier les améliorations du tableau historique client.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt, QDate

# Ajouter le répertoire parent au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.ui.views.customer.widgets.customer_history_widget import CustomerHistoryWidget
from datetime import datetime
from PyQt6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test des améliorations du tableau historique client")
        self.setGeometry(100, 100, 1200, 700)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Créer le widget d'historique client
        self.history_widget = CustomerHistoryWidget()
        layout.addWidget(self.history_widget)
        
        # Ajouter des données de test
        self.add_test_data()
    
    def add_test_data(self):
        """Ajoute des données de test au tableau historique"""
        # Accéder directement au tableau pour ajouter des données de test
        table = self.history_widget.table
        
        # Données de test pour l'historique client
        test_data = [
            {
                "date": "15/01/2024 10:30",
                "type": "Vente",
                "reference": "SALE-001",
                "description": "Vente d'équipements informatiques - dû: 1250.75 DA",
                "amount": "15750.00",
                "status": "Partiellement payé"
            },
            {
                "date": "12/01/2024 14:15",
                "type": "Versement",
                "reference": "PAY-001",
                "description": "Paiement partiel pour SALE-001",
                "amount": "5000.00",
                "status": "Validé"
            },
            {
                "date": "10/01/2024 09:45",
                "type": "Réparation",
                "reference": "REP-001",
                "description": "Réparation ordinateur portable - Remplacement écran",
                "amount": "8500.00",
                "status": "Terminé"
            },
            {
                "date": "08/01/2024 16:20",
                "type": "Vente",
                "reference": "SALE-002",
                "description": "Vente accessoires bureau - dû: 0.00 DA",
                "amount": "2750.50",
                "status": "Payé"
            },
            {
                "date": "05/01/2024 11:10",
                "type": "Versement",
                "reference": "PAY-002",
                "description": "Paiement complet pour SALE-002",
                "amount": "2750.50",
                "status": "Validé"
            },
            {
                "date": "03/01/2024 13:30",
                "type": "Réparation",
                "reference": "REP-002",
                "description": "Maintenance préventive serveur",
                "amount": "4200.00",
                "status": "Facturé"
            },
            {
                "date": "28/12/2023 15:45",
                "type": "Vente",
                "reference": "SALE-003",
                "description": "Vente logiciels - dû: 3500.00 DA",
                "amount": "12000.00",
                "status": "En attente"
            },
            {
                "date": "25/12/2023 10:00",
                "type": "Versement",
                "reference": "PAY-003",
                "description": "Acompte pour SALE-003",
                "amount": "8500.00",
                "status": "Validé"
            }
        ]
        
        # Vider le tableau et ajouter les données de test
        table.setRowCount(0)
        
        for i, data in enumerate(test_data):
            table.insertRow(i)
            
            # Date
            date_item = QTableWidgetItem(data["date"])
            table.setItem(i, 0, date_item)
            
            # Type
            type_item = QTableWidgetItem(data["type"])
            table.setItem(i, 1, type_item)
            
            # Référence
            ref_item = QTableWidgetItem(data["reference"])
            table.setItem(i, 2, ref_item)
            
            # Description
            desc_item = QTableWidgetItem(data["description"])
            table.setItem(i, 3, desc_item)
            
            # Montant
            amount_item = QTableWidgetItem(data["amount"])
            table.setItem(i, 4, amount_item)
            
            # Statut
            status_item = QTableWidgetItem(data["status"])
            table.setItem(i, 5, status_item)
        
        # Activer le bouton détails
        self.history_widget.details_btn.setEnabled(True)
        
        print(f"Ajouté {len(test_data)} entrées d'historique de test")

def main():
    app = QApplication(sys.argv)
    
    # Créer et afficher la fenêtre de test
    window = TestWindow()
    window.show()
    
    print("Test des améliorations du tableau historique client:")
    print("- Colonnes ajustées automatiquement selon le contenu")
    print("- Dernière colonne (Statut) étirée pour utiliser tout l'espace")
    print("- Lignes alternées pour une meilleure lisibilité")
    print("- Sélection par ligne complète")
    print("- Édition désactivée")
    print("- Grille visible et tri activé")
    print("- Données d'historique complètes (ventes, réparations, versements)")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
