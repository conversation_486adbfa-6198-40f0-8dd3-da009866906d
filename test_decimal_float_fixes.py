"""
Test pour vérifier toutes les corrections des erreurs Decimal/float.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from decimal import Decimal


def test_reconciliation_dialog_fix():
    """Test des corrections du dialogue de réconciliation"""
    print("=== Test du dialogue de réconciliation ===")
    
    # Simuler les données comme dans le dialogue
    class MockRegister:
        def __init__(self, name, current_balance):
            self.name = name
            self.current_balance = current_balance  # Peut être Decimal
    
    # Test avec différents types de soldes
    test_registers = [
        MockRegister("Caisse 1", Decimal("1500.50")),
        MockRegister("Caisse 2", 1200.75),  # float
        MockRegister("Caisse 3", "800.25"),  # string
    ]
    
    for register in test_registers:
        print(f"\n--- Test avec {register.name} ---")
        
        try:
            # Simuler les opérations du dialogue de réconciliation
            
            # 1. Affichage du solde actuel (ligne 137)
            balance_display = f"Solde actuel: {float(register.current_balance):.2f} DA"
            print(f"  ✓ Affichage solde: {balance_display}")
            
            # 2. Initialisation du spin box (ligne 139)
            spin_value = float(register.current_balance)
            print(f"  ✓ Valeur spin box: {spin_value}")
            
            # 3. Calcul de différence (ligne 152)
            counted = Decimal(str(1600.00))  # Montant compté
            current_balance = Decimal(str(register.current_balance))
            difference = counted - current_balance
            print(f"  ✓ Différence calculée: {float(difference):.2f} DA")
            
            # 4. Affichage de la différence (ligne 157)
            difference_display = f"{float(difference):.2f} DA"
            print(f"  ✓ Affichage différence: {difference_display}")
            
            # 5. Préparation pour enregistrement (ligne 175)
            counted_amount = Decimal(str(1600.00))
            print(f"  ✓ Montant pour enregistrement: {counted_amount}")
            
        except Exception as e:
            print(f"  ✗ ERREUR: {e}")
            import traceback
            traceback.print_exc()


def test_safe_decimal_operation():
    """Test de la nouvelle fonction safe_decimal_operation"""
    print("\n=== Test de safe_decimal_operation ===")
    
    try:
        from app.utils.decimal_utils import safe_decimal_operation
        
        test_cases = [
            {
                'name': 'Addition Decimal + float',
                'left': Decimal("100.50"),
                'operator': '+',
                'right': 50.25,
                'expected': Decimal("150.75")
            },
            {
                'name': 'Soustraction float - Decimal',
                'left': 200.00,
                'operator': '-',
                'right': Decimal("75.50"),
                'expected': Decimal("124.50")
            },
            {
                'name': 'Multiplication Decimal * int',
                'left': Decimal("10.50"),
                'operator': '*',
                'right': 3,
                'expected': Decimal("31.50")
            },
            {
                'name': 'Division string / float',
                'left': "100.00",
                'operator': '/',
                'right': 4.0,
                'expected': Decimal("25.00")
            },
            {
                'name': 'Soustraction avec None',
                'left': Decimal("100.00"),
                'operator': '-',
                'right': None,
                'expected': Decimal("100.00")
            }
        ]
        
        for test_case in test_cases:
            print(f"\n--- {test_case['name']} ---")
            try:
                result = safe_decimal_operation(
                    test_case['left'], 
                    test_case['operator'], 
                    test_case['right']
                )
                print(f"  Opération: {test_case['left']} {test_case['operator']} {test_case['right']}")
                print(f"  Résultat: {result} (type: {type(result)})")
                print(f"  Attendu: {test_case['expected']}")
                
                if abs(result - test_case['expected']) < Decimal("0.01"):
                    print("  ✓ PASS")
                else:
                    print("  ✗ FAIL - Résultat incorrect")
                    
            except Exception as e:
                print(f"  ✗ ERREUR: {e}")
                
    except Exception as e:
        print(f"✗ ERREUR lors de l'import: {e}")


def test_treasury_service_reconciliation():
    """Test des corrections dans le service de trésorerie"""
    print("\n=== Test du service de trésorerie ===")
    
    try:
        from app.utils.decimal_utils import safe_decimal_operation, validate_amount
        
        # Simuler les données de réconciliation
        class MockCashRegister:
            def __init__(self, current_balance):
                self.current_balance = current_balance
        
        test_scenarios = [
            {
                'name': 'Solde Decimal, montant compté float',
                'register_balance': Decimal("1500.50"),
                'counted_amount': 1600.75
            },
            {
                'name': 'Solde float, montant compté Decimal',
                'register_balance': 1200.25,
                'counted_amount': Decimal("1250.00")
            },
            {
                'name': 'Solde string, montant compté int',
                'register_balance': "800.50",
                'counted_amount': 850
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n--- {scenario['name']} ---")
            
            try:
                register = MockCashRegister(scenario['register_balance'])
                counted_amount = scenario['counted_amount']
                
                # Test de l'opération corrigée (ligne 398-400)
                difference = safe_decimal_operation(counted_amount, '-', register.current_balance)
                previous_balance = validate_amount(register.current_balance)
                
                print(f"  Solde actuel: {register.current_balance} (type: {type(register.current_balance)})")
                print(f"  Montant compté: {counted_amount} (type: {type(counted_amount)})")
                print(f"  Différence: {difference} (type: {type(difference)})")
                print(f"  Solde précédent: {previous_balance} (type: {type(previous_balance)})")
                
                # Test de mise à jour du solde (ligne 417)
                new_balance = validate_amount(counted_amount)
                print(f"  Nouveau solde: {new_balance} (type: {type(new_balance)})")
                
                print("  ✓ PASS - Opérations réussies")
                
            except Exception as e:
                print(f"  ✗ ERREUR: {e}")
                
    except Exception as e:
        print(f"✗ ERREUR lors de l'import: {e}")


def test_payment_dialog_corrections():
    """Test des corrections du dialogue de paiement"""
    print("\n=== Test du dialogue de paiement ===")
    
    # Simuler les données de réparation
    class MockRepair:
        def __init__(self, final_amount, total_paid):
            self.final_amount = final_amount
            self.total_paid = total_paid
    
    test_repairs = [
        MockRepair(Decimal("1500.00"), Decimal("500.00")),
        MockRepair(1200.50, 300.25),  # float
        MockRepair("800.75", "200.50"),  # string
        MockRepair(None, None),  # None values
    ]
    
    for i, repair in enumerate(test_repairs):
        print(f"\n--- Réparation {i+1} ---")
        
        try:
            # Code corrigé du dialogue de paiement (lignes 183-186)
            final_amount = Decimal(str(getattr(repair, 'final_amount', 0.0) or 0.0))
            total_paid = Decimal(str(getattr(repair, 'total_paid', 0.0) or 0.0))
            balance_due = final_amount - total_paid
            
            # Création du résumé financier (lignes 188-192)
            financial_summary = {
                'final_amount': float(final_amount),
                'total_paid': float(total_paid),
                'balance_due': float(balance_due)
            }
            
            print(f"  Final amount: {repair.final_amount} -> {final_amount}")
            print(f"  Total paid: {repair.total_paid} -> {total_paid}")
            print(f"  Balance due: {balance_due}")
            print(f"  Résumé: {financial_summary}")
            print("  ✓ PASS - Calculs réussis")
            
        except Exception as e:
            print(f"  ✗ ERREUR: {e}")
            import traceback
            traceback.print_exc()


def test_error_scenarios():
    """Test des scénarios d'erreur"""
    print("\n=== Test des scénarios d'erreur ===")
    
    try:
        from app.utils.decimal_utils import safe_decimal_operation, validate_amount
        
        error_scenarios = [
            {
                'name': 'Division par zéro',
                'operation': lambda: safe_decimal_operation(100, '/', 0),
                'should_fail': True
            },
            {
                'name': 'Opérateur invalide',
                'operation': lambda: safe_decimal_operation(100, '%', 50),
                'should_fail': True
            },
            {
                'name': 'Valeur invalide',
                'operation': lambda: validate_amount("abc"),
                'should_fail': True
            },
            {
                'name': 'Valeur None avec opération',
                'operation': lambda: safe_decimal_operation(None, '+', None),
                'should_fail': False
            }
        ]
        
        for scenario in error_scenarios:
            print(f"\n--- {scenario['name']} ---")
            
            try:
                result = scenario['operation']()
                if scenario['should_fail']:
                    print(f"  ✗ FAIL - Devrait échouer mais a réussi: {result}")
                else:
                    print(f"  ✓ PASS - Réussi comme attendu: {result}")
            except Exception as e:
                if scenario['should_fail']:
                    print(f"  ✓ PASS - Échec attendu: {e}")
                else:
                    print(f"  ✗ FAIL - Échec inattendu: {e}")
                    
    except Exception as e:
        print(f"✗ ERREUR lors du test: {e}")


def main():
    """Fonction principale"""
    print("Test des corrections des erreurs Decimal/float\n")
    
    test_reconciliation_dialog_fix()
    test_safe_decimal_operation()
    test_treasury_service_reconciliation()
    test_payment_dialog_corrections()
    test_error_scenarios()
    
    print("\n=== Résumé des corrections ===")
    print("✅ Dialogue de réconciliation - Conversions Decimal/float sûres")
    print("✅ Fonction safe_decimal_operation - Opérations arithmétiques robustes")
    print("✅ Service de trésorerie - Réconciliation avec types mixtes")
    print("✅ Dialogue de paiement - Calculs financiers stables")
    print("✅ Gestion d'erreurs - Scénarios d'échec appropriés")
    
    print("\n=== Erreurs corrigées ===")
    print("✅ TypeError: unsupported operand type(s) for -: 'float' and 'decimal.Decimal'")
    print("✅ Erreurs de calcul dans les dialogues de réconciliation")
    print("✅ Problèmes de types dans les services de trésorerie")
    print("✅ Incohérences de types dans les calculs financiers")
    
    print("\n=== Tests terminés ===")
    print("Toutes les opérations Decimal/float devraient maintenant fonctionner sans erreur !")


if __name__ == "__main__":
    main()
