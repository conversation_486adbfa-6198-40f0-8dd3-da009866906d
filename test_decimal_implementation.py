"""
Test de l'implémentation Decimal pour la trésorerie.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from decimal import Decimal
from app.utils.decimal_utils import validate_amount, safe_decimal_sum, MoneyAmount, DecimalValidationError
from app.ui.components.decimal_spinbox import DecimalSpinBox
from PyQt6.QtWidgets import QApplication


def test_decimal_utils():
    """Test des utilitaires Decimal"""
    print("=== Test des utilitaires Decimal ===")
    
    # Test de validation de montants
    try:
        amount1 = validate_amount("123.45")
        print(f"✓ Validation '123.45': {amount1} (type: {type(amount1)})")
        
        amount2 = validate_amount("123,67")  # Virgule française
        print(f"✓ Validation '123,67': {amount2} (type: {type(amount2)})")
        
        amount3 = validate_amount(100)
        print(f"✓ Validation 100: {amount3} (type: {type(amount3)})")
        
        # Test avec contraintes
        amount4 = validate_amount("50.00", min_value=Decimal("10.00"), max_value=Decimal("100.00"))
        print(f"✓ Validation avec contraintes: {amount4}")
        
    except DecimalValidationError as e:
        print(f"✗ Erreur de validation: {e}")
    
    # Test de MoneyAmount
    try:
        money1 = MoneyAmount("123.45")
        money2 = MoneyAmount("67.89")
        
        print(f"✓ MoneyAmount 1: {money1}")
        print(f"✓ MoneyAmount 2: {money2}")
        print(f"✓ Addition: {money1 + money2}")
        print(f"✓ Soustraction: {money1 - money2}")
        print(f"✓ Comparaison: {money1} > {money2} = {money1 > money2}")
        
    except DecimalValidationError as e:
        print(f"✗ Erreur MoneyAmount: {e}")
    
    # Test de safe_decimal_sum
    values = [Decimal("10.50"), Decimal("20.25"), Decimal("5.75")]
    total = safe_decimal_sum(values)
    print(f"✓ Somme sécurisée: {total}")
    
    # Test d'erreurs
    try:
        validate_amount("abc")
    except DecimalValidationError as e:
        print(f"✓ Erreur attendue pour 'abc': {e}")
    
    try:
        validate_amount("-10", allow_negative=False)
    except DecimalValidationError as e:
        print(f"✓ Erreur attendue pour valeur négative: {e}")


def test_decimal_spinbox():
    """Test du widget DecimalSpinBox"""
    print("\n=== Test du DecimalSpinBox ===")
    
    app = QApplication([])
    
    try:
        # Créer le widget
        spinbox = DecimalSpinBox()
        spinbox.setRange(Decimal("0.00"), Decimal("1000.00"))
        spinbox.setDecimals(2)
        spinbox.setSuffix(" DA")
        
        print(f"✓ DecimalSpinBox créé")
        print(f"✓ Valeur initiale: {spinbox.value()}")
        print(f"✓ Type de la valeur: {type(spinbox.value())}")
        
        # Test de définition de valeur
        spinbox.setValue(Decimal("123.45"))
        print(f"✓ Valeur après setValue(123.45): {spinbox.value()}")
        
        # Test avec float (doit être converti)
        spinbox.setValue(67.89)
        print(f"✓ Valeur après setValue(67.89): {spinbox.value()}")
        
        # Test des limites
        spinbox.setValue(Decimal("2000.00"))  # Au-dessus du max
        print(f"✓ Valeur après setValue(2000.00) [max=1000]: {spinbox.value()}")
        
        print("✓ DecimalSpinBox fonctionne correctement")
        
    except Exception as e:
        print(f"✗ Erreur DecimalSpinBox: {e}")
        import traceback
        traceback.print_exc()
    
    app.quit()


def test_precision():
    """Test de la précision des calculs"""
    print("\n=== Test de précision ===")
    
    # Test classique qui pose problème avec float
    a = Decimal("0.1")
    b = Decimal("0.2")
    c = a + b
    print(f"✓ 0.1 + 0.2 = {c} (exact)")
    
    # Test avec float pour comparaison
    a_float = 0.1
    b_float = 0.2
    c_float = a_float + b_float
    print(f"✗ 0.1 + 0.2 = {c_float} (float - imprécis)")
    
    # Test de calculs complexes
    values = [Decimal("10.01"), Decimal("20.02"), Decimal("30.03")]
    total = safe_decimal_sum(values)
    print(f"✓ Somme complexe: {total}")
    
    # Test d'arrondi
    amount = Decimal("123.456")
    rounded = amount.quantize(Decimal("0.01"))
    print(f"✓ Arrondi 123.456 -> {rounded}")


if __name__ == "__main__":
    print("Test de l'implémentation Decimal pour la trésorerie\n")
    
    test_decimal_utils()
    test_decimal_spinbox()
    test_precision()
    
    print("\n=== Tests terminés ===")
