#!/usr/bin/env python3
"""
Test pour vérifier que le problème de double création de produits est corrigé
"""
import sys
import os

def test_sku_generator_consistency():
    """Teste la cohérence du générateur de SKU"""
    try:
        print("Testing SKU generator consistency...")
        
        from app.utils.sku_generator import SKUGenerator
        
        # Simuler une catégorie avec code
        class MockCategory:
            def __init__(self, id, name, code):
                self.id = id
                self.name = name
                self.code = code
        
        categories = [
            MockCategory(1, "LCD Screens", "LCD"),
            MockCategory(2, "Pièces détachées", "PAR")
        ]
        
        # Test 1: Génération avec ID de catégorie
        sku1 = SKUGenerator.generate_sku(
            category_id=1,
            category_name="LCD Screens",
            categories=categories
        )
        
        # Test 2: Génération avec le même ID (devrait utiliser le même préfixe)
        sku2 = SKUGenerator.generate_sku(
            category_id=1,
            category_name="LCD Screens",
            categories=categories
        )
        
        # Vérifier que les deux SKU utilisent le même préfixe
        prefix1 = sku1.split('-')[0]
        prefix2 = sku2.split('-')[0]
        
        if prefix1 == prefix2 == "LCD":
            print(f"SUCCESS: Consistent SKU prefix: {prefix1}")
            print(f"SKU 1: {sku1}")
            print(f"SKU 2: {sku2}")
            return True
        else:
            print(f"ERROR: Inconsistent SKU prefixes: {prefix1} vs {prefix2}")
            return False
        
    except Exception as e:
        print(f"ERROR: Error in SKU generator test: {e}")
        return False

def test_duplicate_detection():
    """Teste la détection de doublons"""
    try:
        print("Testing duplicate detection...")
        
        # Simuler un service d'inventaire
        class MockInventoryService:
            def __init__(self):
                self.products = []
            
            async def get_by_sku(self, sku):
                for product in self.products:
                    if product.get('sku') == sku:
                        return product
                return None
            
            async def get_by_name(self, name):
                for product in self.products:
                    if product.get('name') == name:
                        return product
                return None
            
            async def create(self, data):
                product = {
                    'id': len(self.products) + 1,
                    'sku': data['sku'],
                    'name': data['name']
                }
                self.products.append(product)
                return product
        
        service = MockInventoryService()
        
        # Ajouter un produit existant
        existing_product = {
            'id': 1,
            'sku': 'LCD-241201-123AB',
            'name': 'Écran LCD 24 pouces'
        }
        service.products.append(existing_product)
        
        # Test 1: Détection par SKU
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        found_by_sku = loop.run_until_complete(service.get_by_sku('LCD-241201-123AB'))
        if found_by_sku:
            print("SUCCESS: Duplicate detection by SKU works")
        else:
            print("ERROR: Duplicate detection by SKU failed")
            return False
        
        # Test 2: Détection par nom
        found_by_name = loop.run_until_complete(service.get_by_name('Écran LCD 24 pouces'))
        if found_by_name:
            print("SUCCESS: Duplicate detection by name works")
        else:
            print("ERROR: Duplicate detection by name failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error in duplicate detection test: {e}")
        return False

def test_product_creation_protection():
    """Teste la protection contre la création multiple"""
    try:
        print("Testing product creation protection...")
        
        # Simuler la classe OrderItemDialog avec protection
        class MockOrderItemDialog:
            def __init__(self):
                self._creating_product = False
                self.creation_count = 0
            
            def _create_new_product(self):
                """Version protégée de _create_new_product"""
                # Protection contre les créations multiples
                if hasattr(self, '_creating_product') and self._creating_product:
                    print("Protection: product creation already in progress")
                    return False
                
                self._creating_product = True
                try:
                    self.creation_count += 1
                    print(f"Creating product #{self.creation_count}")
                    return True
                finally:
                    self._creating_product = False
        
        dialog = MockOrderItemDialog()
        
        # Test 1: Création normale
        success1 = dialog._create_new_product()
        
        # Test 2: Tentative de création multiple
        dialog._creating_product = True  # Simuler une création en cours
        success2 = dialog._create_new_product()
        
        if success1 and not success2 and dialog.creation_count == 1:
            print("SUCCESS: Product creation protection works correctly")
            return True
        else:
            print("ERROR: Product creation protection failed")
            return False
        
    except Exception as e:
        print(f"ERROR: Error in product creation protection test: {e}")
        return False

def test_sku_validation():
    """Teste la validation des SKU"""
    try:
        print("Testing SKU validation...")
        
        from app.utils.sku_generator import SKUGenerator
        
        # Test de SKU valides
        valid_skus = [
            "LCD-241201-123AB",
            "PAR-241201-456CD",
            "GEN-241201-789EF"
        ]
        
        for sku in valid_skus:
            if not SKUGenerator.validate_sku(sku):
                print(f"ERROR: Valid SKU rejected: {sku}")
                return False
        
        # Test de SKU invalides
        invalid_skus = [
            "LCD-24120-123AB",  # Date trop courte
            "L-241201-123AB",   # Préfixe trop court
            "LCD-241201-123",   # Suffixe trop court
            "LCD_241201_123AB", # Mauvais séparateur
            ""                  # SKU vide
        ]
        
        for sku in invalid_skus:
            if SKUGenerator.validate_sku(sku):
                print(f"ERROR: Invalid SKU accepted: {sku}")
                return False
        
        print("SUCCESS: SKU validation works correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in SKU validation test: {e}")
        return False

def test_category_prefix_extraction():
    """Teste l'extraction du préfixe de catégorie"""
    try:
        print("Testing category prefix extraction...")
        
        from app.utils.sku_generator import SKUGenerator
        
        test_cases = [
            ("LCD-241201-123AB", "LCD"),
            ("PAR-241201-456CD", "PAR"),
            ("GEN-241201-789EF", "GEN")
        ]
        
        for sku, expected_prefix in test_cases:
            actual_prefix = SKUGenerator.extract_category_from_sku(sku)
            if actual_prefix != expected_prefix:
                print(f"ERROR: Wrong prefix extracted from {sku}: {actual_prefix} != {expected_prefix}")
                return False
        
        print("SUCCESS: Category prefix extraction works correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in category prefix extraction test: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("Test de correction du problème de double création de produits")
    print("=" * 65)
    
    success = True
    
    # Test de cohérence du générateur de SKU
    if not test_sku_generator_consistency():
        success = False
    
    # Test de détection de doublons
    if not test_duplicate_detection():
        success = False
    
    # Test de protection de création
    if not test_product_creation_protection():
        success = False
    
    # Test de validation des SKU
    if not test_sku_validation():
        success = False
    
    # Test d'extraction de préfixe
    if not test_category_prefix_extraction():
        success = False
    
    if success:
        print("\n🎉 SUCCESS: Tous les tests de correction de double création sont passés!")
        print("✅ Générateur de SKU centralisé et cohérent")
        print("✅ Détection de doublons par SKU et nom")
        print("✅ Protection contre les créations multiples")
        print("✅ Validation des SKU")
        print("✅ Plus de produits avec des SKU différents (LCD-XXXX vs PAR-XXXX)")
        print("\nLe problème de double création devrait être corrigé!")
    else:
        print("\n❌ ERROR: Certains tests ont échoué")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
