#!/usr/bin/env python3
"""
Test pour identifier et corriger le problème de duplication d'articles
"""
import sys
import os

def test_add_item_protection():
    """Teste la protection contre les doubles ajouts"""
    try:
        print("Testing add_item protection...")
        
        # Simuler la classe PurchaseOrderDialog avec protection
        class MockPurchaseOrderDialog:
            def __init__(self):
                self.order_items = []
                self._adding_item = False
            
            def add_item(self):
                """Version protégée de add_item"""
                # Protection contre les doubles clics
                if hasattr(self, '_adding_item') and self._adding_item:
                    print("Protection activated: add_item already in progress")
                    return
                
                self._adding_item = True
                try:
                    # Simuler l'ajout d'un article
                    item_data = {
                        "product_id": 1,
                        "quantity": 3,
                        "purchase_unit_price": 25.50
                    }
                    
                    # Simuler la création d'un article
                    class MockItem:
                        def __init__(self, **kwargs):
                            for key, value in kwargs.items():
                                setattr(self, key, value)
                    
                    item = MockItem(**item_data)
                    self.order_items.append(item)
                    print(f"Item added. Total items: {len(self.order_items)}")
                    
                finally:
                    self._adding_item = False
        
        # Test 1: Ajout normal
        dialog = MockPurchaseOrderDialog()
        dialog.add_item()
        
        if len(dialog.order_items) == 1:
            print("SUCCESS: Normal add_item works correctly")
        else:
            print("ERROR: Normal add_item failed")
            return False
        
        # Test 2: Tentative de double ajout
        dialog._adding_item = True  # Simuler un ajout en cours
        dialog.add_item()  # Devrait être bloqué
        
        if len(dialog.order_items) == 1:
            print("SUCCESS: Double add_item protection works correctly")
        else:
            print("ERROR: Double add_item protection failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error in add_item protection test: {e}")
        return False

def test_validation_protection():
    """Teste la protection contre les validations multiples"""
    try:
        print("Testing validation protection...")
        
        # Simuler la classe OrderItemDialog avec protection
        class MockOrderItemDialog:
            def __init__(self):
                self._validating = False
                self._accepted = False
            
            def validate_and_accept(self):
                """Version protégée de validate_and_accept"""
                # Protection contre les validations multiples
                if hasattr(self, '_validating') and self._validating:
                    print("Protection activated: validation already in progress")
                    return
                
                self._validating = True
                try:
                    # Simuler la validation
                    print("Validating...")
                    self._accepted = True
                    print("Dialog accepted")
                finally:
                    self._validating = False
        
        # Test 1: Validation normale
        dialog = MockOrderItemDialog()
        dialog.validate_and_accept()
        
        if dialog._accepted:
            print("SUCCESS: Normal validation works correctly")
        else:
            print("ERROR: Normal validation failed")
            return False
        
        # Test 2: Tentative de double validation
        dialog._validating = True  # Simuler une validation en cours
        dialog._accepted = False   # Reset
        dialog.validate_and_accept()  # Devrait être bloqué
        
        if not dialog._accepted:
            print("SUCCESS: Double validation protection works correctly")
        else:
            print("ERROR: Double validation protection failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error in validation protection test: {e}")
        return False

def test_schema_alias_removal():
    """Teste que les alias problématiques sont supprimés"""
    try:
        print("Testing schema alias removal...")
        
        # Simuler un schéma Pydantic sans alias
        class MockPurchaseOrderItemBase:
            def __init__(self):
                self.purchase_unit_price = 25.50
                # Plus d'alias "unit_price"
        
        item = MockPurchaseOrderItemBase()
        
        # Vérifier que purchase_unit_price existe
        if hasattr(item, 'purchase_unit_price'):
            print("SUCCESS: purchase_unit_price field exists")
        else:
            print("ERROR: purchase_unit_price field missing")
            return False
        
        # Vérifier qu'il n'y a pas de confusion avec unit_price
        if not hasattr(item, 'unit_price'):
            print("SUCCESS: No unit_price alias confusion")
        else:
            print("WARNING: unit_price still exists (might be intentional)")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error in schema alias test: {e}")
        return False

def test_threading_safety():
    """Teste la sécurité des threads"""
    try:
        print("Testing threading safety...")
        
        import asyncio
        
        # Simuler la logique de gestion d'event loop corrigée
        def safe_get_event_loop():
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
                return loop
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                return loop
        
        # Test 1: Obtenir un event loop
        loop1 = safe_get_event_loop()
        print(f"SUCCESS: Got event loop: {type(loop1)}")
        
        # Test 2: Fermer et rouvrir
        loop1.close()
        loop2 = safe_get_event_loop()
        print(f"SUCCESS: Got new event loop after closing: {type(loop2)}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error in threading safety test: {e}")
        return False

def test_item_uniqueness():
    """Teste l'unicité des articles ajoutés"""
    try:
        print("Testing item uniqueness...")
        
        # Simuler une liste d'articles avec vérification d'unicité
        class MockOrderItems:
            def __init__(self):
                self.items = []
            
            def add_item(self, item_data):
                # Vérifier si l'article existe déjà
                for existing_item in self.items:
                    if (existing_item.get('product_id') == item_data.get('product_id') and
                        existing_item.get('purchase_unit_price') == item_data.get('purchase_unit_price')):
                        print("WARNING: Duplicate item detected, not adding")
                        return False
                
                self.items.append(item_data)
                print(f"Item added. Total items: {len(self.items)}")
                return True
        
        order_items = MockOrderItems()
        
        # Test 1: Ajouter un article
        item1 = {"product_id": 1, "quantity": 3, "purchase_unit_price": 25.50}
        success1 = order_items.add_item(item1)
        
        # Test 2: Tenter d'ajouter le même article
        item2 = {"product_id": 1, "quantity": 3, "purchase_unit_price": 25.50}
        success2 = order_items.add_item(item2)
        
        if success1 and not success2 and len(order_items.items) == 1:
            print("SUCCESS: Item uniqueness protection works correctly")
            return True
        else:
            print("ERROR: Item uniqueness protection failed")
            return False
        
    except Exception as e:
        print(f"ERROR: Error in item uniqueness test: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("Test de correction du problème de duplication d'articles")
    print("=" * 60)
    
    success = True
    
    # Test de protection add_item
    if not test_add_item_protection():
        success = False
    
    # Test de protection validation
    if not test_validation_protection():
        success = False
    
    # Test de suppression d'alias
    if not test_schema_alias_removal():
        success = False
    
    # Test de sécurité threading
    if not test_threading_safety():
        success = False
    
    # Test d'unicité des articles
    if not test_item_uniqueness():
        success = False
    
    if success:
        print("\nSUCCESS: Tous les tests de correction de duplication sont passés!")
        print("Les problèmes de duplication d'articles devraient être corrigés")
    else:
        print("\nERROR: Certains tests ont échoué")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
