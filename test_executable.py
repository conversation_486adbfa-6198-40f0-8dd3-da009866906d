import os
import sys
import subprocess
import logging
import platform
import shutil
import time
from datetime import datetime
import traceback

# Configuration du logging
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = os.path.join(log_dir, f'test_executable_{timestamp}.log')

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("test_executable")

def find_executable():
    """Trouve l'exécutable compilé"""
    logger.info("Recherche de l'exécutable compilé...")
    
    # Chemins possibles pour l'exécutable
    possible_paths = [
        "dist/main.dist/main.exe",
        "dist/main_patched.dist/main_patched.exe",
        "dist/main_debug.dist/main_debug.exe"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"Exécutable trouvé: {path}")
            return path
    
    logger.error("Aucun exécutable trouvé")
    return None

def check_system_info():
    """Vérifie les informations système"""
    logger.info("=== Informations système ===")
    logger.info(f"Système d'exploitation: {platform.system()} {platform.release()} {platform.version()}")
    logger.info(f"Architecture: {platform.machine()}")
    logger.info(f"Processeur: {platform.processor()}")
    logger.info(f"Python: {sys.version}")
    
    # Vérifier les variables d'environnement importantes
    env_vars = ['PATH', 'PYTHONPATH', 'TEMP', 'TMP', 'APPDATA', 'LOCALAPPDATA']
    logger.info("Variables d'environnement importantes:")
    for var in env_vars:
        value = os.environ.get(var, 'Non défini')
        # Tronquer les valeurs très longues
        if len(value) > 100:
            value = value[:97] + '...'
        logger.info(f"  {var}: {value}")

def check_dependencies():
    """Vérifie les dépendances de l'application"""
    logger.info("=== Vérification des dépendances ===")
    
    # Liste des dépendances critiques
    dependencies = [
        "PyQt6",
        "sqlalchemy",
        "apscheduler",
        "passlib",
        "toml"
    ]
    
    for dep in dependencies:
        try:
            module = __import__(dep)
            version = getattr(module, '__version__', 'Inconnue')
            logger.info(f"✓ {dep}: Installé (version: {version})")
        except ImportError:
            logger.error(f"✗ {dep}: Non installé")
        except Exception as e:
            logger.error(f"✗ {dep}: Erreur - {str(e)}")

def check_executable_environment(executable_path):
    """Vérifie l'environnement de l'exécutable"""
    logger.info("=== Vérification de l'environnement de l'exécutable ===")
    
    # Vérifier si l'exécutable existe
    if not os.path.exists(executable_path):
        logger.error(f"L'exécutable {executable_path} n'existe pas")
        return False
    
    # Vérifier la taille de l'exécutable
    size_mb = os.path.getsize(executable_path) / (1024 * 1024)
    logger.info(f"Taille de l'exécutable: {size_mb:.2f} MB")
    
    # Vérifier le répertoire de l'exécutable
    exe_dir = os.path.dirname(executable_path)
    logger.info(f"Répertoire de l'exécutable: {exe_dir}")
    
    # Vérifier les fichiers critiques dans le répertoire de l'exécutable
    critical_paths = [
        "config",
        "config/settings.toml",
        "data",
        "PyQt6",
        "app/ui/resources"
    ]
    
    for path in critical_paths:
        full_path = os.path.join(exe_dir, path)
        exists = os.path.exists(full_path)
        type_str = "Répertoire" if os.path.isdir(full_path) else "Fichier" if os.path.isfile(full_path) else "Inconnu"
        logger.info(f"  {path}: {'✓ Existe' if exists else '✗ N\'existe pas'} ({type_str})")
    
    return True

def run_executable_test(executable_path):
    """Exécute l'exécutable en mode test"""
    logger.info("=== Test d'exécution en mode test ===")
    
    try:
        # Exécuter avec l'option de test
        logger.info(f"Exécution de {executable_path} en mode test...")
        result = subprocess.run(
            [executable_path, "--test-mode"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=10  # Timeout de 10 secondes
        )
        
        logger.info(f"Code de sortie: {result.returncode}")
        logger.info(f"Sortie standard:\n{result.stdout}")
        
        if result.stderr:
            logger.warning(f"Erreur standard:\n{result.stderr}")
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        logger.error("Timeout lors du test de l'exécutable")
        return False
    except Exception as e:
        logger.error(f"Erreur lors du test de l'exécutable: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def run_executable_debug(executable_path):
    """Exécute l'exécutable en mode debug"""
    logger.info("=== Test d'exécution en mode debug ===")
    
    try:
        # Exécuter avec l'option de debug
        logger.info(f"Exécution de {executable_path} en mode debug...")
        result = subprocess.run(
            [executable_path, "--debug"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=10  # Timeout de 10 secondes
        )
        
        logger.info(f"Code de sortie: {result.returncode}")
        logger.info(f"Sortie standard:\n{result.stdout}")
        
        if result.stderr:
            logger.warning(f"Erreur standard:\n{result.stderr}")
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        logger.error("Timeout lors du test de l'exécutable en mode debug")
        return False
    except Exception as e:
        logger.error(f"Erreur lors du test de l'exécutable en mode debug: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def check_dll_dependencies(executable_path):
    """Vérifie les dépendances DLL de l'exécutable"""
    logger.info("=== Vérification des dépendances DLL ===")
    
    # Vérifier si le script check_missing_dlls.py existe
    if os.path.exists("check_missing_dlls.py"):
        try:
            logger.info("Exécution du script check_missing_dlls.py...")
            result = subprocess.run(
                [sys.executable, "check_missing_dlls.py", os.path.dirname(executable_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            logger.info(f"Résultat de la vérification des DLL:\n{result.stdout}")
            
            if result.stderr:
                logger.warning(f"Erreurs lors de la vérification des DLL:\n{result.stderr}")
            
            # Analyser la sortie pour détecter les DLL manquantes
            if "DLL manquantes" in result.stdout:
                logger.error("Des DLL sont manquantes")
                return False
            else:
                logger.info("Toutes les DLL requises sont présentes")
                return True
        except Exception as e:
            logger.error(f"Erreur lors de la vérification des DLL: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    else:
        logger.warning("Le script check_missing_dlls.py n'existe pas")
        return None

def generate_report(executable_path, test_result, debug_result, dll_check_result):
    """Génère un rapport de diagnostic"""
    logger.info("=== Génération du rapport de diagnostic ===")
    
    report_file = os.path.join(log_dir, f'rapport_diagnostic_{timestamp}.txt')
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== RAPPORT DE DIAGNOSTIC DE L'EXÉCUTABLE ===\n\n")
        f.write(f"Date et heure: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Exécutable testé: {executable_path}\n")
        f.write(f"Système: {platform.system()} {platform.release()} {platform.version()}\n")
        f.write(f"Architecture: {platform.machine()}\n\n")
        
        f.write("=== RÉSULTATS DES TESTS ===\n")
        f.write(f"Test en mode test: {'✓ Réussi' if test_result else '✗ Échoué'}\n")
        f.write(f"Test en mode debug: {'✓ Réussi' if debug_result else '✗ Échoué'}\n")
        
        if dll_check_result is not None:
            f.write(f"Vérification des DLL: {'✓ Toutes présentes' if dll_check_result else '✗ DLL manquantes'}\n\n")
        else:
            f.write("Vérification des DLL: Non effectuée\n\n")
        
        f.write("=== DIAGNOSTIC ===\n")
        if test_result and debug_result and (dll_check_result is None or dll_check_result):
            f.write("L'exécutable semble fonctionner correctement en mode test et debug.\n")
            f.write("Si l'application ne démarre pas normalement, le problème pourrait être lié à:\n")
            f.write("1. Des ressources manquantes ou mal placées\n")
            f.write("2. Des problèmes de configuration\n")
            f.write("3. Des erreurs lors de l'initialisation de l'interface graphique\n")
        else:
            f.write("L'exécutable présente des problèmes:\n")
            
            if not test_result:
                f.write("- Échec du test en mode test\n")
            
            if not debug_result:
                f.write("- Échec du test en mode debug\n")
            
            if dll_check_result is not None and not dll_check_result:
                f.write("- DLL manquantes\n")
            
            f.write("\nSuggestions de résolution:\n")
            f.write("1. Vérifiez que toutes les dépendances sont correctement installées\n")
            f.write("2. Assurez-vous que les fichiers de configuration sont présents et valides\n")
            f.write("3. Installez les Visual C++ Redistributable les plus récents\n")
            f.write("4. Recompilez l'application avec les options --enable-plugin=pyqt6 et --include-package=PyQt6\n")
            f.write("5. Consultez les logs détaillés pour plus d'informations\n")
        
        f.write("\n=== FICHIERS DE LOG ===\n")
        f.write(f"Log de test: {log_file}\n")
        f.write(f"Log de compilation: build.log et build_debug.log\n")
        f.write(f"Log de l'application: logs/app_*.log\n")
    
    logger.info(f"Rapport de diagnostic généré: {report_file}")
    return report_file

def main():
    logger.info("=== DÉBUT DU TEST DE L'EXÉCUTABLE ===")
    
    # Vérifier les informations système
    check_system_info()
    
    # Vérifier les dépendances
    check_dependencies()
    
    # Trouver l'exécutable
    executable_path = find_executable()
    if not executable_path:
        logger.error("Impossible de trouver l'exécutable compilé")
        print("\nERREUR: Impossible de trouver l'exécutable compilé.")
        print("Veuillez d'abord compiler l'application avec build_with_nuitka.py ou build_with_nuitka_debug.py.")
        return 1
    
    # Vérifier l'environnement de l'exécutable
    if not check_executable_environment(executable_path):
        logger.error("L'environnement de l'exécutable est incorrect")
        print("\nERREUR: L'environnement de l'exécutable est incorrect.")
        return 1
    
    # Tester l'exécutable en mode test
    test_result = run_executable_test(executable_path)
    
    # Tester l'exécutable en mode debug
    debug_result = run_executable_debug(executable_path)
    
    # Vérifier les dépendances DLL
    dll_check_result = check_dll_dependencies(executable_path)
    
    # Générer le rapport
    report_file = generate_report(executable_path, test_result, debug_result, dll_check_result)
    
    # Afficher le résultat
    print("\n=== RÉSULTAT DU TEST DE L'EXÉCUTABLE ===")
    print(f"Exécutable testé: {executable_path}")
    print(f"Test en mode test: {'✓ Réussi' if test_result else '✗ Échoué'}")
    print(f"Test en mode debug: {'✓ Réussi' if debug_result else '✗ Échoué'}")
    
    if dll_check_result is not None:
        print(f"Vérification des DLL: {'✓ Toutes présentes' if dll_check_result else '✗ DLL manquantes'}")
    
    print(f"\nRapport de diagnostic généré: {report_file}")
    print("Consultez ce fichier pour plus de détails sur les problèmes détectés.")
    
    if not test_result or not debug_result or (dll_check_result is not None and not dll_check_result):
        print("\nDes problèmes ont été détectés avec l'exécutable. Veuillez consulter le rapport de diagnostic.")
        return 1
    else:
        print("\nL'exécutable semble fonctionner correctement en mode test et debug.")
        print("Si l'application ne démarre pas normalement, consultez le rapport de diagnostic pour plus d'informations.")
        return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        logger.info(f"=== FIN DU TEST DE L'EXÉCUTABLE (code de sortie: {exit_code}) ===")
        sys.exit(exit_code)
    except Exception as e:
        logger.critical(f"Erreur fatale: {str(e)}")
        logger.critical(traceback.format_exc())
        print(f"\nERREUR FATALE: {str(e)}")
        print("Consultez le fichier de log pour plus de détails.")
        sys.exit(1)