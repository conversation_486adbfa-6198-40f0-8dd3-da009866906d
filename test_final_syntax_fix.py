#!/usr/bin/env python3
"""
Test final pour vérifier que tous les problèmes de syntaxe sont corrigés
"""
import sys
import os

def test_all_imports():
    """Teste tous les imports critiques"""
    try:
        print("Testing all critical imports...")
        
        # Liste des imports critiques à tester
        critical_imports = [
            "app.app_manager",
            "app.ui.window", 
            "app.ui.views.purchasing.purchasing_view",
            "app.ui.views.purchasing.dialogs.purchase_order_dialog",
            "app.ui.views.purchasing.dialogs.order_item_dialog",
            "app.ui.views.inventory.dialogs.item_dialog",
            "app.core.services.purchasing_service",
            "app.core.services.inventory_service",
            "app.core.schemas.purchasing",
            "app.utils.sku_generator"
        ]
        
        for module_name in critical_imports:
            try:
                __import__(module_name)
                print(f"✅ {module_name}")
            except Exception as e:
                print(f"❌ {module_name}: {e}")
                return False
        
        print("SUCCESS: All critical imports work correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in imports test: {e}")
        return False

def test_syntax_validation():
    """Teste la validation de syntaxe des fichiers modifiés"""
    try:
        print("Testing syntax validation...")
        
        files_to_check = [
            "app/ui/views/purchasing/dialogs/order_item_dialog.py",
            "app/ui/views/purchasing/dialogs/purchase_order_dialog.py",
            "app/ui/views/inventory/dialogs/item_dialog.py",
            "app/utils/sku_generator.py"
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Compiler le code pour vérifier la syntaxe
                    compile(content, file_path, 'exec')
                    print(f"✅ {file_path}")
                except SyntaxError as e:
                    print(f"❌ {file_path}: Syntax error at line {e.lineno}: {e.msg}")
                    return False
                except Exception as e:
                    print(f"❌ {file_path}: {e}")
                    return False
            else:
                print(f"⚠️  {file_path}: File not found")
        
        print("SUCCESS: All syntax validation passed")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in syntax validation: {e}")
        return False

def test_class_instantiation():
    """Teste l'instanciation des classes principales"""
    try:
        print("Testing class instantiation...")
        
        # Test du générateur de SKU
        from app.utils.sku_generator import SKUGenerator
        sku = SKUGenerator.generate_sku(category_name="Test")
        if sku and len(sku.split('-')) == 3:
            print(f"✅ SKUGenerator: {sku}")
        else:
            print(f"❌ SKUGenerator: Invalid SKU generated: {sku}")
            return False
        
        # Test du service d'inventaire
        from app.core.services.inventory_service import InventoryService
        from app.utils.database import SessionLocal
        
        db = SessionLocal()
        service = InventoryService(db)
        
        # Vérifier que les méthodes existent
        required_methods = ['get_by_sku', 'get_by_barcode', 'get_by_name']
        for method in required_methods:
            if hasattr(service, method):
                print(f"✅ InventoryService.{method}")
            else:
                print(f"❌ InventoryService.{method}: Method not found")
                db.close()
                return False
        
        db.close()
        
        print("SUCCESS: Class instantiation works correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in class instantiation test: {e}")
        return False

def test_application_startup():
    """Teste le démarrage de l'application (import seulement)"""
    try:
        print("Testing application startup (imports only)...")
        
        # Tester l'import du gestionnaire d'application
        from app.app_manager import AppManager
        print("✅ AppManager import successful")
        
        # Tester l'import de la fenêtre principale
        from app.ui.window import MainWindow
        print("✅ MainWindow import successful")
        
        # Tester l'import des vues principales
        from app.ui.views.purchasing.purchasing_view import PurchasingView
        print("✅ PurchasingView import successful")
        
        print("SUCCESS: Application startup imports work correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in application startup test: {e}")
        return False

def test_schema_consistency():
    """Teste la cohérence des schémas"""
    try:
        print("Testing schema consistency...")
        
        from app.core.schemas.purchasing import PurchaseOrderItemBase
        
        # Créer une instance de test
        item = PurchaseOrderItemBase(
            product_id=1,
            quantity=3,
            purchase_unit_price=25.50
        )
        
        # Vérifier les propriétés
        if (hasattr(item, 'product_id') and 
            hasattr(item, 'quantity') and 
            hasattr(item, 'purchase_unit_price')):
            print("✅ Schema properties correct")
        else:
            print("❌ Schema properties missing")
            return False
        
        # Vérifier les valeurs
        if (item.product_id == 1 and 
            item.quantity == 3 and 
            item.purchase_unit_price == 25.50):
            print("✅ Schema values correct")
        else:
            print("❌ Schema values incorrect")
            return False
        
        print("SUCCESS: Schema consistency works correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in schema consistency test: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🔍 TEST FINAL DE CORRECTION DE SYNTAXE")
    print("=" * 50)
    
    all_tests = [
        ("All Imports", test_all_imports),
        ("Syntax Validation", test_syntax_validation),
        ("Class Instantiation", test_class_instantiation),
        ("Application Startup", test_application_startup),
        ("Schema Consistency", test_schema_consistency)
    ]
    
    passed = 0
    total = len(all_tests)
    
    for test_name, test_func in all_tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"   ❌ {test_name} FAILED")
    
    print(f"\n{'='*50}")
    print(f"📊 RÉSULTATS: {passed}/{total} tests passés")
    
    if passed == total:
        print("\n🎉 SUCCÈS COMPLET! Tous les problèmes de syntaxe sont corrigés:")
        print("   ✅ Tous les imports fonctionnent")
        print("   ✅ Aucune erreur de syntaxe")
        print("   ✅ Classes instanciables")
        print("   ✅ Application démarrable")
        print("   ✅ Schémas cohérents")
        print("\n🚀 L'application est maintenant prête à être utilisée!")
        print("\n💡 Instructions de test:")
        print("   1. Lancez: python main.py")
        print("   2. Testez: Gestion des achats → Nouvelle commande → Ajouter un article")
        print("   3. Vérifiez: Aucune erreur de threading, unit_price ou duplication")
        return True
    else:
        print(f"\n❌ ÉCHEC: {total - passed} test(s) ont échoué")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
