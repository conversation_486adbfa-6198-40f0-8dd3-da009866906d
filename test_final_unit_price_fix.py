#!/usr/bin/env python3
"""
Test final pour vérifier que toutes les erreurs 'unit_price is not defined' sont corrigées
"""
import sys
import os

def test_purchasing_dialog_flow():
    """Teste le flux complet du dialogue d'achat"""
    try:
        print("Testing purchasing dialog flow...")
        
        # Simuler le flux complet comme dans l'application
        from app.ui.views.purchasing.dialogs.order_item_dialog import OrderItemDialog
        
        # Simuler get_item_data()
        item_data = {
            "product_id": 1,
            "product": None,
            "quantity": 5,
            "purchase_unit_price": 25.50,  # Clé correcte
            "delivery_date": None,
            "specifications": {},
            "received_quantity": 0
        }
        
        # Simuler edit_item() - vérifier que toutes les clés existent
        required_keys = ["product_id", "product", "quantity", "purchase_unit_price", "delivery_date"]
        
        for key in required_keys:
            if key not in item_data:
                print(f"ERROR: Missing key '{key}' in item_data")
                return False
        
        # Simuler la création d'un PurchaseOrderItem
        from app.core.models.purchasing import PurchaseOrderItem
        
        item = PurchaseOrderItem(
            product_id=item_data["product_id"],
            quantity=item_data["quantity"],
            purchase_unit_price=item_data["purchase_unit_price"],  # Doit utiliser cette clé
            delivery_date=item_data["delivery_date"],
            specifications=item_data["specifications"],
            received_quantity=item_data["received_quantity"]
        )
        
        print("SUCCESS: Purchasing dialog flow works correctly")
        return True
        
    except NameError as e:
        if "unit_price" in str(e):
            print(f"ERROR: unit_price variable still referenced: {e}")
            return False
        else:
            print(f"ERROR: Other NameError: {e}")
            return False
    except Exception as e:
        print(f"ERROR: Error in purchasing dialog flow: {e}")
        return False

def test_repair_service_methods():
    """Teste les méthodes du service de réparation"""
    try:
        print("Testing repair service methods...")
        
        # Simuler add_repair_part avec le nouveau paramètre
        def mock_add_repair_part(repair_id, product_id, quantity, purchase_unit_price):
            # Cette fonction simule la signature corrigée
            total_cost = quantity * purchase_unit_price
            return total_cost
        
        # Tester avec les nouveaux paramètres
        result = mock_add_repair_part(1, 1, 3, 25.50)
        
        if result == 76.50:
            print("SUCCESS: Repair service methods use correct parameters")
            return True
        else:
            print("ERROR: Repair service calculation incorrect")
            return False
            
    except Exception as e:
        print(f"ERROR: Error in repair service methods: {e}")
        return False

def test_event_system():
    """Teste que le système d'événements utilise les bonnes clés"""
    try:
        print("Testing event system...")
        
        # Simuler un événement avec purchase_unit_price
        event_data = {
            "item_id": 1,
            "purchase_unit_price": 25.50,
            "quantity": 3
        }
        
        # Simuler la logique de traitement d'événement
        item_id = event_data.get('item_id')
        purchase_unit_price = event_data.get('purchase_unit_price', event_data.get('unit_price'))
        
        if item_id and purchase_unit_price:
            print(f"SUCCESS: Event processed with purchase_unit_price: {purchase_unit_price}")
            return True
        else:
            print("ERROR: Event processing failed")
            return False
            
    except Exception as e:
        print(f"ERROR: Error in event system: {e}")
        return False

def test_used_parts_dialog():
    """Teste le dialogue des pièces utilisées"""
    try:
        print("Testing used parts dialog...")
        
        # Simuler la logique du dialogue des pièces utilisées
        quantity = 2
        purchase_unit_price = 15.75  # Variable correcte
        
        # Simuler la création d'une UsedPart
        from app.core.models.repair import UsedPart
        
        used_part = UsedPart(
            part_id=1,
            repair_order_id=1,
            quantity=quantity,
            purchase_unit_price=purchase_unit_price,
            total_price=quantity * purchase_unit_price
        )
        
        if used_part.total_price == 31.50:
            print("SUCCESS: Used parts dialog works correctly")
            return True
        else:
            print("ERROR: Used parts calculation incorrect")
            return False
            
    except NameError as e:
        if "unit_price" in str(e):
            print(f"ERROR: unit_price variable still referenced in used parts: {e}")
            return False
        else:
            print(f"ERROR: Other NameError in used parts: {e}")
            return False
    except Exception as e:
        print(f"ERROR: Error in used parts dialog: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("Test final de correction - unit_price is not defined")
    print("=" * 60)
    
    success = True
    
    # Test du flux de dialogue d'achat
    if not test_purchasing_dialog_flow():
        success = False
    
    # Test des méthodes de service de réparation
    if not test_repair_service_methods():
        success = False
    
    # Test du système d'événements
    if not test_event_system():
        success = False
    
    # Test du dialogue des pièces utilisées
    if not test_used_parts_dialog():
        success = False
    
    if success:
        print("\nSUCCESS: Tous les tests finaux sont passes!")
        print("L'erreur 'unit_price is not defined' devrait etre completement corrigee")
    else:
        print("\nERROR: Certains tests ont echoue")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
