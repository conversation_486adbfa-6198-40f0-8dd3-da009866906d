#!/usr/bin/env python3
"""
Script de test pour vérifier l'intégration du FinanceService
"""

import asyncio
import sys
import os
from datetime import datetime, timezone
from decimal import Decimal

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.finance_service import FinanceService
from app.core.services.sale_service import SaleService
from app.core.services.repair_service import RepairService
from app.core.models.sale import Sale, PaymentMethod as SalePaymentMethod
from app.core.models.repair import RepairOrder, PaymentMethod as RepairPaymentMethod
from app.core.models.treasury import CashRegister, CashRegisterType


async def test_finance_service_integration():
    """Test l'intégration du FinanceService"""
    print("🧪 Test de l'intégration du FinanceService")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        finance_service = FinanceService(db)
        sale_service = SaleService(db)
        repair_service = RepairService(db)
        
        # Test 1: Vérifier qu'une caisse existe
        print("\n1. Vérification des caisses...")
        cash_registers = db.query(CashRegister).filter(CashRegister.is_active == True).all()
        if not cash_registers:
            print("❌ Aucune caisse active trouvée. Création d'une caisse de test...")
            test_register = CashRegister(
                name="Caisse Test",
                type=CashRegisterType.MAIN,
                is_active=True,
                balance=1000.0
            )
            db.add(test_register)
            db.commit()
            print("✅ Caisse de test créée")
        else:
            print(f"✅ {len(cash_registers)} caisse(s) active(s) trouvée(s)")
        
        # Test 2: Créer une vente de test
        print("\n2. Création d'une vente de test...")
        test_sale = Sale(
            number="TEST-001",
            customer_id=None,
            subtotal=100.0,
            tax_amount=19.0,
            discount_amount=0.0,
            final_amount=119.0,
            total_paid=0.0,
            payment_status="pending",
            status="completed",
            user_id=1
        )
        db.add(test_sale)
        db.commit()
        db.refresh(test_sale)
        print(f"✅ Vente créée avec ID: {test_sale.id}")
        
        # Test 3: Tester le paiement via FinanceService
        print("\n3. Test de paiement via FinanceService...")
        try:
            payment = await finance_service.pay_sale(
                sale_id=test_sale.id,
                amount=119.0,
                method=SalePaymentMethod.cash,
                processed_by=1,
                reference="TEST-PAY-001"
            )
            print(f"✅ Paiement créé avec ID: {payment.id}")
            
            # Vérifier que la vente a été mise à jour
            db.refresh(test_sale)
            print(f"✅ Statut de paiement de la vente: {test_sale.payment_status}")
            print(f"✅ Montant payé: {test_sale.total_paid}")
            
        except Exception as e:
            print(f"❌ Erreur lors du paiement: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 4: Créer une réparation de test
        print("\n4. Création d'une réparation de test...")
        test_repair = RepairOrder(
            number="REP-TEST-001",
            customer_id=None,
            customer_name="Test Customer",
            brand="Test",
            model="Test Model",
            issue_description="Test issue",
            estimated_cost=50.0,
            final_cost=50.0,
            total_paid=0.0,
            payment_status="pending",
            status="completed",
            user_id=1
        )
        db.add(test_repair)
        db.commit()
        db.refresh(test_repair)
        print(f"✅ Réparation créée avec ID: {test_repair.id}")
        
        # Test 5: Tester le paiement de réparation via FinanceService
        print("\n5. Test de paiement de réparation via FinanceService...")
        try:
            repair_payment = await finance_service.pay_repair(
                repair_id=test_repair.id,
                amount=50.0,
                method=RepairPaymentMethod.cash,
                processed_by=1,
                reference_number="TEST-REP-PAY-001"
            )
            print(f"✅ Paiement de réparation créé avec ID: {repair_payment.id}")
            
            # Vérifier que la réparation a été mise à jour
            db.refresh(test_repair)
            print(f"✅ Statut de paiement de la réparation: {test_repair.payment_status}")
            print(f"✅ Montant payé: {test_repair.total_paid}")
            
        except Exception as e:
            print(f"❌ Erreur lors du paiement de réparation: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 50)
        print("✅ Tests d'intégration terminés avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def test_payment_consistency():
    """Test la cohérence des paiements"""
    print("\n🔍 Test de cohérence des paiements")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # Vérifier que tous les paiements récents ont des écritures de trésorerie correspondantes
        from app.core.models.treasury import CashTransaction
        from app.core.models.sale import Payment
        from app.core.models.repair import RepairPayment
        
        # Compter les paiements de vente récents
        recent_sale_payments = db.query(Payment).filter(
            Payment.payment_date >= datetime.now(timezone.utc).replace(hour=0, minute=0, second=0)
        ).count()
        
        # Compter les paiements de réparation récents
        recent_repair_payments = db.query(RepairPayment).filter(
            RepairPayment.payment_date >= datetime.now(timezone.utc).replace(hour=0, minute=0, second=0)
        ).count()
        
        # Compter les transactions de trésorerie récentes
        recent_treasury_transactions = db.query(CashTransaction).filter(
            CashTransaction.transaction_date >= datetime.now(timezone.utc).replace(hour=0, minute=0, second=0)
        ).count()
        
        print(f"📊 Paiements de vente récents: {recent_sale_payments}")
        print(f"📊 Paiements de réparation récents: {recent_repair_payments}")
        print(f"📊 Transactions de trésorerie récentes: {recent_treasury_transactions}")
        
        if recent_treasury_transactions >= (recent_sale_payments + recent_repair_payments):
            print("✅ Cohérence des écritures de trésorerie vérifiée")
        else:
            print("⚠️  Possible incohérence dans les écritures de trésorerie")
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de cohérence: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 Démarrage des tests d'intégration FinanceService")
    
    # Exécuter les tests
    asyncio.run(test_finance_service_integration())
    asyncio.run(test_payment_consistency())
    
    print("\n🎉 Tous les tests sont terminés!")
