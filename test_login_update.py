import asyncio
import sys
sys.path.append('.')

from app.core.services.auth_service import AuthService
from app.core.services.user_service import UserService
from app.core.services.permission_service import RoleService
from app.core.models.user import UserCreate
from app.utils.database import Session<PERSON><PERSON>al

async def test_login_update():
    """Test de mise à jour de la date de dernière connexion lors de l'authentification"""
    
    print("=== Test de mise à jour de last_login ===")
    
    # Créer une session de base de données
    db = SessionLocal()
    
    try:
        # Créer les services
        auth_service = AuthService(db)
        user_service = UserService(db)
        role_service = RoleService(db)
        
        # Récupérer un rôle existant
        roles = await role_service.get_all()
        if not roles:
            print("❌ Aucun rôle trouvé")
            return
        
        test_role = roles[0]
        print(f"✅ Rôle trouvé: {test_role.name} (ID: {test_role.id})")
        
        # Créer un utilisateur de test
        test_email = "<EMAIL>"
        test_password = "password123"
        
        # Supprimer l'utilisateur s'il existe déjà
        existing_user = await user_service.get_user_by_email(test_email)
        if existing_user:
            print(f"🗑️  Suppression de l'utilisateur existant: {test_email}")
            await user_service.delete_user(existing_user.id, deleted_by_id=1)
        
        # Créer un nouvel utilisateur
        user_data = UserCreate(
            email=test_email,
            password=test_password,
            full_name="Test Login User",
            phone="0123456789",
            position="Testeur",
            department="IT",
            role_ids=[test_role.id]
        )
        
        print(f"📝 Création de l'utilisateur: {test_email}")
        new_user = await user_service.create_user(user_data, created_by_id=1)
        
        print(f"✅ Utilisateur créé:")
        print(f"  ID: {new_user.id}")
        print(f"  Email: {new_user.email}")
        print(f"  Dernière connexion AVANT authentification: {new_user.last_login}")
        
        # Test d'authentification
        print(f"\n=== Test d'authentification ===")
        print(f"Tentative de connexion avec {test_email} / {test_password}")
        
        try:
            success, token, user_info = await auth_service.authenticate_user(test_email, test_password)

            if success:
                print(f"✅ Authentification réussie")
                print(f"  Token: {token[:50] if token else 'N/A'}...")
                print(f"  User info: {user_info}")
                
                # Vérifier la mise à jour de last_login
                print(f"\n=== Vérification de la mise à jour ===")
                
                # Récupérer l'utilisateur mis à jour
                updated_user = await user_service.get_user_by_email(test_email)
                print(f"Dernière connexion APRÈS authentification: {updated_user.last_login}")
                
                if updated_user.last_login:
                    print("✅ SUCCÈS: La date de dernière connexion a été mise à jour")
                    
                    # Vérifier le type de la date
                    print(f"  Type de last_login: {type(updated_user.last_login)}")
                    print(f"  Valeur: {updated_user.last_login}")
                else:
                    print("❌ PROBLÈME: La date de dernière connexion n'a pas été mise à jour")
                
                # Vérifier aussi directement dans la base de données
                print(f"\n=== Vérification directe en base ===")
                fresh_user = db.query(user_service.model).filter(
                    user_service.model.email == test_email
                ).first()
                
                if fresh_user:
                    print(f"Dernière connexion en base: {fresh_user.last_login}")
                    print(f"Type en base: {type(fresh_user.last_login)}")
                else:
                    print("❌ Utilisateur non trouvé en base")
                
            else:
                print("❌ Authentification échouée")
                print(f"  Erreur: {user_info}")
                
        except Exception as e:
            print(f"❌ Erreur lors de l'authentification: {e}")
            import traceback
            traceback.print_exc()
        
        # Test avec un mauvais mot de passe
        print(f"\n=== Test avec mauvais mot de passe ===")
        try:
            bad_success, bad_token, bad_info = await auth_service.authenticate_user(test_email, "mauvais_password")
            if bad_success:
                print("❌ PROBLÈME: Authentification réussie avec mauvais mot de passe")
            else:
                print("✅ Authentification échouée comme attendu avec mauvais mot de passe")
                print(f"  Erreur: {bad_info}")
        except Exception as e:
            print(f"Erreur attendue avec mauvais mot de passe: {e}")
        
        # Nettoyer
        print(f"\n=== Nettoyage ===")
        await user_service.delete_user(new_user.id, deleted_by_id=1)
        print(f"🗑️  Utilisateur de test supprimé")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
        print("✅ Session de base de données fermée")

if __name__ == "__main__":
    asyncio.run(test_login_update())
