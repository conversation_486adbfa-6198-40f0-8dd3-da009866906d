import asyncio
import sys
sys.path.append('.')

from app.core.services.user_service import UserService
from app.core.services.permission_service import RoleService
from app.core.models.user import UserCreate
from app.utils.database import SessionLocal
from app.ui.views.user.user_table_model import UserTableModel

async def test_new_user_creation():
    """Test de création d'un nouvel utilisateur et vérification de l'affichage"""
    
    print("=== Test de création d'un nouvel utilisateur ===")
    
    # Créer une session de base de données
    db = SessionLocal()
    
    try:
        # Créer les services
        user_service = UserService(db)
        role_service = RoleService(db)
        
        # Récupérer un rôle existant (admin)
        roles = await role_service.get_all()
        admin_role = None
        for role in roles:
            if role.name.lower() == 'admin':
                admin_role = role
                break
        
        if not admin_role:
            print("❌ Aucun rôle admin trouvé")
            return
        
        print(f"✅ Rôle admin trouvé: {admin_role.name} (ID: {admin_role.id})")
        
        # Créer un nouvel utilisateur de test
        test_email = "<EMAIL>"
        
        # Vérifier si l'utilisateur existe déjà et le supprimer si nécessaire
        existing_user = await user_service.get_user_by_email(test_email)
        if existing_user:
            print(f"🗑️  Suppression de l'utilisateur existant: {test_email}")
            await user_service.delete_user(existing_user.id, deleted_by_id=1)
        
        # Créer les données du nouvel utilisateur
        user_data = UserCreate(
            email=test_email,
            password="password123",
            full_name="Utilisateur Test Nouveau",
            phone="0123456789",
            position="Testeur",
            department="IT",
            role_ids=[admin_role.id]
        )
        
        print(f"📝 Création de l'utilisateur: {user_data.email}")
        
        # Créer l'utilisateur
        new_user = await user_service.create_user(user_data, created_by_id=1)
        
        print(f"✅ Utilisateur créé avec succès:")
        print(f"  ID: {new_user.id}")
        print(f"  Email: {new_user.email}")
        print(f"  Nom: {new_user.full_name}")
        print(f"  Statut: {new_user.status}")
        print(f"  Actif: {new_user.is_active}")
        print(f"  Dernière connexion: {new_user.last_login}")
        print(f"  Type last_login: {type(new_user.last_login)}")
        
        # Tester l'affichage avec UserTableModel
        print(f"\n=== Test de l'affichage dans UserTableModel ===")
        
        # Créer le modèle de table
        table_model = UserTableModel()
        
        # Charger les utilisateurs
        await table_model.load_data()
        
        # Trouver notre utilisateur dans la liste
        user_found = False
        for i in range(table_model.rowCount()):
            user = table_model.users[i]
            if user.email == test_email:
                user_found = True
                print(f"✅ Utilisateur trouvé dans la table à la ligne {i}")
                
                # Tester l'affichage de chaque colonne
                from PyQt6.QtCore import Qt
                for col in range(table_model.columnCount()):
                    index = table_model.index(i, col)
                    value = table_model.data(index, Qt.ItemDataRole.DisplayRole)
                    header = table_model.headerData(col, Qt.Orientation.Horizontal, Qt.ItemDataRole.DisplayRole)
                    print(f"  Colonne {col} ({header}): {value}")
                
                break
        
        if not user_found:
            print("❌ Utilisateur non trouvé dans la table")
        
        # Tester spécifiquement le formatage de la date
        print(f"\n=== Test du formatage de date ===")
        formatted_date = table_model._format_datetime(new_user.last_login)
        print(f"Date formatée: {formatted_date}")
        
        # Vérifier que c'est bien "Jamais connecté"
        if formatted_date == "Jamais connecté":
            print("✅ Affichage correct pour un nouvel utilisateur")
        else:
            print(f"❌ Affichage inattendu: {formatted_date}")
        
        print(f"\n=== Nettoyage ===")
        # Supprimer l'utilisateur de test
        await user_service.delete_user(new_user.id, deleted_by_id=1)
        print(f"🗑️  Utilisateur de test supprimé")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
        print("✅ Session de base de données fermée")

if __name__ == "__main__":
    asyncio.run(test_new_user_creation())
