#!/usr/bin/env python3
"""
Test spécifique pour reproduire l'erreur dans le dialogue d'ajout d'article
"""
import sys
import os

def test_order_item_dialog_creation():
    """Teste la création du dialogue d'ajout d'article"""
    try:
        print("Testing OrderItemDialog creation...")
        
        # Importer les modules nécessaires
        from app.core.models.inventory import InventoryItem
        
        # Créer une liste de produits de test
        products = []
        
        # Essayer d'importer et créer le dialogue
        from app.ui.views.purchasing.dialogs.order_item_dialog import OrderItemDialog
        
        print("SUCCESS: OrderItemDialog imported successfully")
        
        # Note: On ne peut pas créer le dialogue sans QApplication
        # mais on peut tester les méthodes statiques
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error creating OrderItemDialog: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_get_item_data_method():
    """Teste la méthode get_item_data"""
    try:
        print("Testing get_item_data method...")
        
        # Simuler un produit sélectionné
        class MockProduct:
            def __init__(self):
                self.id = 1
                self.name = "Produit Test"
                self.purchase_price = 25.50
        
        selected_product = MockProduct()
        
        # Simuler les valeurs des widgets
        quantity = 3
        unit_price_value = 25.50
        
        # Simuler la méthode get_item_data
        item_data = {
            "product_id": selected_product.id,
            "product": selected_product,
            "quantity": quantity,
            "purchase_unit_price": unit_price_value,  # Clé correcte
            "delivery_date": None,
            "specifications": {},
            "received_quantity": 0
        }
        
        print(f"SUCCESS: get_item_data returns: {item_data}")
        
        # Vérifier que toutes les clés nécessaires sont présentes
        required_keys = ["product_id", "product", "quantity", "purchase_unit_price"]
        for key in required_keys:
            if key not in item_data:
                print(f"ERROR: Missing key '{key}' in item_data")
                return False
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error in get_item_data method: {e}")
        return False

def test_purchase_order_dialog_add_item():
    """Teste la méthode add_item du dialogue de commande"""
    try:
        print("Testing PurchaseOrderDialog add_item...")
        
        # Simuler item_data comme retourné par get_item_data
        item_data = {
            "product_id": 1,
            "product": None,
            "quantity": 3,
            "purchase_unit_price": 25.50,  # Clé correcte
            "delivery_date": None,
            "specifications": {},
            "received_quantity": 0
        }
        
        # Simuler la création d'un PurchaseOrderItem
        from app.core.models.purchasing import PurchaseOrderItem
        
        item = PurchaseOrderItem(
            product_id=item_data["product_id"],
            quantity=item_data["quantity"],
            purchase_unit_price=item_data["purchase_unit_price"],  # Utiliser la bonne clé
            delivery_date=item_data["delivery_date"],
            specifications=item_data["specifications"],
            received_quantity=item_data["received_quantity"]
        )
        
        print(f"SUCCESS: PurchaseOrderItem created with purchase_unit_price: {item.purchase_unit_price}")
        
        return True
        
    except KeyError as e:
        print(f"ERROR: KeyError in add_item - missing key: {e}")
        print("This indicates that the code is trying to access a key that doesn't exist")
        return False
    except Exception as e:
        print(f"ERROR: Error in add_item: {e}")
        return False

def test_edit_item_method():
    """Teste la méthode edit_item"""
    try:
        print("Testing edit_item method...")
        
        # Créer un item existant
        from app.core.models.purchasing import PurchaseOrderItem
        
        item = PurchaseOrderItem(
            product_id=1,
            quantity=2,
            purchase_unit_price=20.0
        )
        
        # Simuler les nouvelles données
        item_data = {
            "product_id": 2,
            "product": None,
            "quantity": 5,
            "purchase_unit_price": 25.50,  # Clé correcte
            "delivery_date": None,
            "specifications": {},
            "received_quantity": 0
        }
        
        # Simuler la mise à jour comme dans edit_item
        item.product_id = item_data["product_id"]
        item.product = item_data["product"]
        item.quantity = item_data["quantity"]
        item.purchase_unit_price = item_data["purchase_unit_price"]  # Utiliser la bonne clé
        item.delivery_date = item_data["delivery_date"]
        
        print(f"SUCCESS: Item updated with purchase_unit_price: {item.purchase_unit_price}")
        
        return True
        
    except KeyError as e:
        print(f"ERROR: KeyError in edit_item - missing key: {e}")
        return False
    except Exception as e:
        print(f"ERROR: Error in edit_item: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("Test du dialogue d'ajout d'article - Recherche de l'erreur unit_price")
    print("=" * 70)
    
    success = True
    
    # Test de création du dialogue
    if not test_order_item_dialog_creation():
        success = False
    
    # Test de get_item_data
    if not test_get_item_data_method():
        success = False
    
    # Test de add_item
    if not test_purchase_order_dialog_add_item():
        success = False
    
    # Test de edit_item
    if not test_edit_item_method():
        success = False
    
    if success:
        print("\nSUCCESS: Tous les tests sont passes!")
        print("Le probleme pourrait venir d'un autre endroit ou d'une interaction specifique")
    else:
        print("\nERROR: Certains tests ont echoue")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
