"""
Test pour vérifier les corrections du dialogue de paiement.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from decimal import Decimal


def test_decimal_operations():
    """Test des opérations avec Decimal"""
    print("=== Test des opérations Decimal ===")
    
    # Simuler les données comme dans le dialogue de paiement
    test_cases = [
        {
            'name': 'Montants normaux',
            'final_amount': Decimal('1000.50'),
            'total_paid': Decimal('250.25'),
            'expected_balance': Decimal('750.25')
        },
        {
            'name': 'Montants avec float',
            'final_amount': 1000.50,  # float
            'total_paid': 250.25,     # float
            'expected_balance': Decimal('750.25')
        },
        {
            'name': 'Montants mixtes',
            'final_amount': Decimal('500.00'),  # Decimal
            'total_paid': 150.75,               # float
            'expected_balance': Decimal('349.25')
        },
        {
            'name': 'Montants zéro',
            'final_amount': Decimal('0.00'),
            'total_paid': Decimal('0.00'),
            'expected_balance': Decimal('0.00')
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- Test: {test_case['name']} ---")
        
        try:
            # Simuler la conversion comme dans le code corrigé
            final_amount = Decimal(str(test_case['final_amount'] or 0.0))
            total_paid = Decimal(str(test_case['total_paid'] or 0.0))
            balance_due = final_amount - total_paid
            
            print(f"  Final amount: {final_amount} (type: {type(final_amount)})")
            print(f"  Total paid: {total_paid} (type: {type(total_paid)})")
            print(f"  Balance due: {balance_due} (type: {type(balance_due)})")
            print(f"  Expected: {test_case['expected_balance']}")
            
            # Vérifier le résultat
            if abs(balance_due - test_case['expected_balance']) < Decimal('0.01'):
                print("  ✓ PASS - Calcul correct")
            else:
                print("  ✗ FAIL - Calcul incorrect")
                
        except Exception as e:
            print(f"  ✗ ERREUR: {e}")


def test_financial_summary_creation():
    """Test de la création du résumé financier"""
    print("\n=== Test de création du résumé financier ===")
    
    # Simuler une réparation avec différents types de données
    class MockRepair:
        def __init__(self, final_amount, total_paid):
            self.final_amount = final_amount
            self.total_paid = total_paid
    
    test_repairs = [
        MockRepair(Decimal('1500.00'), Decimal('500.00')),
        MockRepair(1200.50, 300.25),  # float
        MockRepair(None, None),       # None values
        MockRepair(Decimal('0.00'), Decimal('0.00')),
    ]
    
    for i, repair in enumerate(test_repairs):
        print(f"\n--- Réparation {i+1} ---")
        
        try:
            # Code corrigé du dialogue de paiement
            final_amount = Decimal(str(getattr(repair, 'final_amount', 0.0) or 0.0))
            total_paid = Decimal(str(getattr(repair, 'total_paid', 0.0) or 0.0))
            balance_due = final_amount - total_paid
            
            financial_summary = {
                'final_amount': float(final_amount),
                'total_paid': float(total_paid),
                'balance_due': float(balance_due)
            }
            
            print(f"  Résumé financier: {financial_summary}")
            print("  ✓ PASS - Résumé créé sans erreur")
            
        except Exception as e:
            print(f"  ✗ ERREUR: {e}")
            import traceback
            traceback.print_exc()


def test_event_loop_handling():
    """Test de la gestion des event loops"""
    print("\n=== Test de gestion des event loops ===")
    
    import asyncio
    
    # Test 1: Pas d'event loop en cours
    print("\n--- Test 1: Pas d'event loop ---")
    try:
        loop = asyncio.get_running_loop()
        print("  ⚠️  Un event loop est déjà en cours")
    except RuntimeError:
        print("  ✓ Aucun event loop en cours - OK pour en créer un nouveau")
    
    # Test 2: Création d'un nouvel event loop
    print("\n--- Test 2: Création d'un nouvel event loop ---")
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        async def test_coroutine():
            return "Test réussi"
        
        result = loop.run_until_complete(test_coroutine())
        print(f"  ✓ Event loop créé et exécuté: {result}")
        
        loop.close()
        print("  ✓ Event loop fermé proprement")
        
    except Exception as e:
        print(f"  ✗ ERREUR: {e}")
    
    # Test 3: Gestion avec thread
    print("\n--- Test 3: Gestion avec thread ---")
    try:
        from PyQt6.QtCore import QThread
        import threading
        
        class TestThread(QThread):
            def run(self):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    async def test_async():
                        return "Thread async réussi"
                    
                    result = loop.run_until_complete(test_async())
                    print(f"    ✓ Thread async exécuté: {result}")
                    
                    loop.close()
                except Exception as e:
                    print(f"    ✗ Erreur dans le thread: {e}")
        
        thread = TestThread()
        thread.start()
        thread.wait()  # Attendre la fin du thread
        
        print("  ✓ Thread terminé")
        
    except Exception as e:
        print(f"  ✗ ERREUR: {e}")


def test_payment_validation():
    """Test de validation des paiements"""
    print("\n=== Test de validation des paiements ===")
    
    validation_cases = [
        {
            'name': 'Montant valide',
            'amount_text': '150.50',
            'balance_due': 200.00,
            'should_pass': True
        },
        {
            'name': 'Montant avec virgule',
            'amount_text': '150,50',
            'balance_due': 200.00,
            'should_pass': True
        },
        {
            'name': 'Montant vide',
            'amount_text': '',
            'balance_due': 200.00,
            'should_pass': False
        },
        {
            'name': 'Montant négatif',
            'amount_text': '-50.00',
            'balance_due': 200.00,
            'should_pass': False
        },
        {
            'name': 'Montant supérieur au solde dû',
            'amount_text': '250.00',
            'balance_due': 200.00,
            'should_pass': False
        },
        {
            'name': 'Montant invalide',
            'amount_text': 'abc',
            'balance_due': 200.00,
            'should_pass': False
        }
    ]
    
    for test_case in validation_cases:
        print(f"\n--- Test: {test_case['name']} ---")
        
        try:
            # Simuler la validation du dialogue de paiement
            amount_text = test_case['amount_text'].strip()
            
            # Vérifier si le montant est vide
            if not amount_text:
                if not test_case['should_pass']:
                    print("  ✓ PASS - Montant vide détecté")
                else:
                    print("  ✗ FAIL - Montant vide non détecté")
                continue
            
            # Remplacer la virgule par un point
            amount_text = amount_text.replace(',', '.')
            
            # Convertir en float
            try:
                amount = float(amount_text)
            except ValueError:
                if not test_case['should_pass']:
                    print("  ✓ PASS - Montant invalide détecté")
                else:
                    print("  ✗ FAIL - Montant invalide non détecté")
                continue
            
            # Vérifier si le montant est positif
            if amount <= 0:
                if not test_case['should_pass']:
                    print("  ✓ PASS - Montant négatif/zéro détecté")
                else:
                    print("  ✗ FAIL - Montant négatif/zéro non détecté")
                continue
            
            # Vérifier si le montant dépasse le solde dû
            if amount > test_case['balance_due']:
                if not test_case['should_pass']:
                    print("  ✓ PASS - Montant supérieur au solde dû détecté")
                else:
                    print("  ✗ FAIL - Montant supérieur au solde dû non détecté")
                continue
            
            # Si on arrive ici, la validation a réussi
            if test_case['should_pass']:
                print(f"  ✓ PASS - Montant valide: {amount}")
            else:
                print(f"  ✗ FAIL - Montant devrait être invalide: {amount}")
                
        except Exception as e:
            print(f"  ✗ ERREUR: {e}")


def main():
    """Fonction principale"""
    print("Test des corrections du dialogue de paiement\n")
    
    test_decimal_operations()
    test_financial_summary_creation()
    test_event_loop_handling()
    test_payment_validation()
    
    print("\n=== Résumé des corrections ===")
    print("✅ Correction des opérations Decimal/float")
    print("✅ Gestion robuste des event loops")
    print("✅ Validation améliorée des montants")
    print("✅ Gestion d'erreurs renforcée")
    
    print("\n=== Tests terminés ===")
    print("Les corrections devraient résoudre les erreurs:")
    print("- TypeError: unsupported operand type(s) for -: 'decimal.Decimal' and 'float'")
    print("- RuntimeError: Cannot run the event loop while another loop is running")


if __name__ == "__main__":
    main()
