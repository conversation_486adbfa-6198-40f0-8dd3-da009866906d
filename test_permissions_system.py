import asyncio
import sys
sys.path.append('.')

from app.core.services.user_service import UserService
from app.core.services.permission_service import RoleService, PermissionService
from app.core.models.user import UserCreate
from app.utils.database import SessionLocal

async def test_permissions_system():
    """Test du système de permissions"""
    
    print("=== Test du système de permissions ===")
    
    # Créer une session de base de données
    db = SessionLocal()
    
    try:
        # Créer les services
        user_service = UserService(db)
        role_service = RoleService(db)
        permission_service = PermissionService(db)
        
        # 1. Lister tous les rôles disponibles
        print("\n=== 1. Rôles disponibles ===")
        roles = await role_service.get_all()
        for role in roles:
            print(f"  - {role.name} (ID: {role.id})")
            
            # Récupérer les permissions de ce rôle
            role_with_perms = await role_service.get_role_with_permissions(role.id)
            if role_with_perms and role_with_perms.permissions:
                role_permissions = role_with_perms.permissions
                print(f"    Permissions ({len(role_permissions)}):")
                for perm in role_permissions[:5]:  # Afficher seulement les 5 premières
                    print(f"      • {perm.name}")
                if len(role_permissions) > 5:
                    print(f"      ... et {len(role_permissions) - 5} autres")
            else:
                print(f"    Aucune permission trouvée")
        
        # 2. Trouver un rôle "vendeur" ou similaire
        print("\n=== 2. Recherche du rôle vendeur ===")
        vendeur_role = None
        for role in roles:
            if "vendeur" in role.name.lower() or "vente" in role.name.lower() or "sales" in role.name.lower():
                vendeur_role = role
                break
        
        if not vendeur_role:
            # Créer un rôle vendeur pour le test
            print("Aucun rôle vendeur trouvé, création d'un rôle de test...")
            
            # Récupérer quelques permissions limitées
            all_permissions = await permission_service.get_all()
            limited_permissions = []
            for perm in all_permissions:
                if any(keyword in perm.name.lower() for keyword in ['customer', 'inventory.view', 'repair.view']):
                    limited_permissions.append(perm.id)
                if len(limited_permissions) >= 5:  # Limiter à 5 permissions
                    break
            
            # Créer le rôle vendeur
            from app.core.services.permission_service import RoleCreate
            vendeur_data = RoleCreate(
                name="Vendeur Test",
                description="Rôle de vendeur avec permissions limitées",
                permission_ids=limited_permissions
            )
            vendeur_role = await role_service.create_role(vendeur_data, created_by_id=1)
            print(f"✅ Rôle vendeur créé: {vendeur_role.name} (ID: {vendeur_role.id})")
        else:
            print(f"✅ Rôle vendeur trouvé: {vendeur_role.name} (ID: {vendeur_role.id})")
        
        # Afficher les permissions du rôle vendeur
        vendeur_with_perms = await role_service.get_role_with_permissions(vendeur_role.id)
        if vendeur_with_perms and vendeur_with_perms.permissions:
            vendeur_permissions = vendeur_with_perms.permissions
            print(f"Permissions du vendeur ({len(vendeur_permissions)}):")
            for perm in vendeur_permissions:
                print(f"  • {perm.name}")
        else:
            print(f"Aucune permission trouvée pour le vendeur")
        
        # 3. Créer un utilisateur avec le rôle vendeur
        print(f"\n=== 3. Création d'un utilisateur vendeur ===")
        test_email = "<EMAIL>"
        
        # Supprimer l'utilisateur s'il existe déjà
        existing_user = await user_service.get_user_by_email(test_email)
        if existing_user:
            print(f"🗑️  Suppression de l'utilisateur existant: {test_email}")
            await user_service.delete_user(existing_user.id, deleted_by_id=1)
        
        # Créer le nouvel utilisateur vendeur
        user_data = UserCreate(
            email=test_email,
            password="password123",
            full_name="Vendeur Test",
            phone="0123456789",
            position="Vendeur",
            department="Ventes",
            role_ids=[vendeur_role.id]
        )
        
        vendeur_user = await user_service.create_user(user_data, created_by_id=1)
        print(f"✅ Utilisateur vendeur créé:")
        print(f"  ID: {vendeur_user.id}")
        print(f"  Email: {vendeur_user.email}")

        # Récupérer les rôles de l'utilisateur
        user_roles = await user_service.get_user_roles(vendeur_user.id)
        print(f"  Rôles: {[role.name for role in user_roles]}")
        
        # 4. Tester les permissions de l'utilisateur
        print(f"\n=== 4. Test des permissions de l'utilisateur ===")
        user_permissions = await user_service.get_user_permissions(vendeur_user.id)
        print(f"Permissions de l'utilisateur ({len(user_permissions)}):")
        for perm in user_permissions:
            print(f"  • {perm}")
        
        # 5. Tester des permissions spécifiques
        print(f"\n=== 5. Test de permissions spécifiques ===")
        test_permissions = [
            "user.create",
            "user.delete", 
            "customer.view",
            "customer.create",
            "inventory.view",
            "inventory.create",
            "inventory.delete",
            "system.settings",
            "user.manage_roles"
        ]
        
        for perm in test_permissions:
            has_permission = await user_service.has_permission(vendeur_user.id, perm)
            status = "✅ AUTORISÉ" if has_permission else "❌ REFUSÉ"
            print(f"  {perm}: {status}")
        
        # 6. Comparer avec un admin
        print(f"\n=== 6. Comparaison avec un administrateur ===")
        admin_users = []
        all_users = await user_service.get_all()
        for user in all_users:
            user_roles = [role.name.lower() for role in user.roles]
            if any("admin" in role for role in user_roles):
                admin_users.append(user)
        
        if admin_users:
            admin_user = admin_users[0]
            print(f"Utilisateur admin trouvé: {admin_user.email}")
            admin_permissions = await user_service.get_user_permissions(admin_user.id)
            print(f"Permissions admin ({len(admin_permissions)}):")
            
            print(f"\nComparaison des permissions:")
            print(f"  Vendeur: {len(user_permissions)} permissions")
            print(f"  Admin: {len(admin_permissions)} permissions")
            
            # Tester les mêmes permissions pour l'admin
            print(f"\nTest des mêmes permissions pour l'admin:")
            for perm in test_permissions:
                has_permission = await user_service.has_permission(admin_user.id, perm)
                status = "✅ AUTORISÉ" if has_permission else "❌ REFUSÉ"
                print(f"  {perm}: {status}")
        
        # 7. Vérifier la logique de vérification des permissions
        print(f"\n=== 7. Vérification de la logique de permissions ===")
        
        # Regarder comment la méthode has_permission fonctionne
        print("Méthode has_permission:")
        import inspect
        source = inspect.getsource(user_service.has_permission)
        print("Code source (extrait):")
        lines = source.split('\n')[:10]  # Premières lignes
        for i, line in enumerate(lines):
            print(f"  {i+1}: {line}")
        
        # Nettoyer
        print(f"\n=== Nettoyage ===")
        await user_service.delete_user(vendeur_user.id, deleted_by_id=1)
        print(f"🗑️  Utilisateur vendeur supprimé")
        
        # Supprimer le rôle de test s'il a été créé
        if vendeur_role.name == "Vendeur Test":
            await role_service.delete_role(vendeur_role.id, deleted_by_id=1)
            print(f"🗑️  Rôle vendeur de test supprimé")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
        print("✅ Session de base de données fermée")

if __name__ == "__main__":
    asyncio.run(test_permissions_system())
