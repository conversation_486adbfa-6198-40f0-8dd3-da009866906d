#!/usr/bin/env python3
"""
Test spécifique pour vérifier que le module de nouvelle commande utilise purchase_unit_price
"""
import sys
import os

def test_purchase_order_imports():
    """Teste que les modules de commande d'achat peuvent être importés"""
    try:
        print("Testing purchase order imports...")
        
        # Test des dialogues
        from app.ui.views.purchasing.dialogs.order_item_dialog import OrderItemDialog
        from app.ui.views.purchasing.dialogs.purchase_order_dialog import PurchaseOrderDialog
        from app.ui.views.purchasing.dialogs.supplier_quote_dialog import SupplierQuoteDialog
        
        print("SUCCESS: Purchase order dialogs imported successfully")
        
        # Test des widgets
        from app.ui.views.purchasing.widgets.order_items_widget import OrderItemsWidget
        from app.ui.views.purchasing.purchase_order_table_model import PurchaseOrderTableModel
        from app.ui.views.purchasing.order_items_table_model import OrderItemsTableModel
        
        print("SUCCESS: Purchase order widgets imported successfully")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error importing purchase order modules: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_purchase_order_item_creation():
    """Teste la création d'un article de commande avec purchase_unit_price"""
    try:
        from app.core.models.purchasing import PurchaseOrderItem
        
        # Créer un article de commande avec purchase_unit_price
        item = PurchaseOrderItem(
            product_id=1,
            quantity=5.0,
            purchase_unit_price=25.50
        )
        
        print(f"SUCCESS: PurchaseOrderItem created with purchase_unit_price: {item.purchase_unit_price}")

        # Vérifier que l'attribut existe
        if hasattr(item, 'purchase_unit_price'):
            print("SUCCESS: PurchaseOrderItem has purchase_unit_price attribute")
        else:
            print("ERROR: PurchaseOrderItem missing purchase_unit_price attribute")
            return False
            
        return True
        
    except Exception as e:
        print(f"ERROR: Error creating PurchaseOrderItem: {e}")
        return False

def test_purchase_order_schema():
    """Teste les schémas Pydantic de commande d'achat"""
    try:
        from app.core.schemas.purchasing import PurchaseOrderItemBase, PurchaseOrderItemCreate
        
        # Créer un schéma avec purchase_unit_price
        item_data = {
            "product_id": 1,
            "quantity": 3.0,
            "purchase_unit_price": 15.75
        }
        
        item_schema = PurchaseOrderItemBase(**item_data)
        print(f"SUCCESS: PurchaseOrderItemBase created with purchase_unit_price: {item_schema.purchase_unit_price}")

        # Test avec alias unit_price pour rétrocompatibilité
        item_data_alias = {
            "product_id": 1,
            "quantity": 3.0,
            "unit_price": 15.75  # Utiliser l'alias
        }

        item_schema_alias = PurchaseOrderItemBase(**item_data_alias)
        print(f"SUCCESS: PurchaseOrderItemBase created with unit_price alias: {item_schema_alias.purchase_unit_price}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Error with purchase order schemas: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("Test du module de nouvelle commande - unit_price -> purchase_unit_price")
    print("=" * 70)
    
    success = True
    
    # Test des imports
    if not test_purchase_order_imports():
        success = False
    
    # Test de création d'article
    if not test_purchase_order_item_creation():
        success = False
    
    # Test des schémas
    if not test_purchase_order_schema():
        success = False
    
    if success:
        print("\nSUCCESS: Tous les tests du module de nouvelle commande sont passes!")
        print("Le module utilise correctement purchase_unit_price")
    else:
        print("\nERROR: Certains tests ont echoue")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
