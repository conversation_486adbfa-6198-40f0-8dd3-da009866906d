import os
import sys
import traceback
import logging
from datetime import datetime

# Configuration du logging
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = os.path.join(log_dir, f'pyqt_test_{timestamp}.log')

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("pyqt_test")

def test_pyqt_basic_import():
    """Teste l'importation de base de PyQt6"""
    logger.info("Test d'importation de base de PyQt6")
    try:
        import PyQt6
        logger.info(f"✓ PyQt6 importé avec succès (version: {getattr(PyQt6, '__version__', 'Inconnue')})")
        return True
    except ImportError as e:
        logger.error(f"✗ Erreur d'importation de PyQt6: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"✗ Erreur inattendue lors de l'importation de PyQt6: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_pyqt_modules():
    """Teste l'importation des modules principaux de PyQt6"""
    logger.info("Test d'importation des modules principaux de PyQt6")
    modules = [
        "PyQt6.QtCore",
        "PyQt6.QtGui",
        "PyQt6.QtWidgets",
        "PyQt6.QtSql",
        "PyQt6.QtCharts"
    ]
    
    all_success = True
    for module in modules:
        try:
            __import__(module)
            logger.info(f"✓ {module} importé avec succès")
        except ImportError as e:
            logger.error(f"✗ Erreur d'importation de {module}: {str(e)}")
            all_success = False
        except Exception as e:
            logger.error(f"✗ Erreur inattendue lors de l'importation de {module}: {str(e)}")
            logger.error(traceback.format_exc())
            all_success = False
    
    return all_success

def test_qapplication_creation():
    """Teste la création d'une QApplication"""
    logger.info("Test de création d'une QApplication")
    try:
        from PyQt6.QtWidgets import QApplication
        app = QApplication([])
        logger.info("✓ QApplication créée avec succès")
        
        # Vérifier les styles disponibles
        from PyQt6.QtWidgets import QStyleFactory
        styles = QStyleFactory.keys()
        logger.info(f"✓ Styles disponibles: {', '.join(styles)}")
        
        app.quit()
        return True
    except Exception as e:
        logger.error(f"✗ Erreur lors de la création de QApplication: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_basic_widgets():
    """Teste la création de widgets de base"""
    logger.info("Test de création de widgets de base")
    try:
        from PyQt6.QtWidgets import QApplication, QLabel, QPushButton, QVBoxLayout, QWidget
        
        app = QApplication([])
        
        # Créer un widget simple
        widget = QWidget()
        widget.setWindowTitle("Test PyQt6")
        
        # Créer un layout
        layout = QVBoxLayout(widget)
        
        # Ajouter un label
        label = QLabel("Test de PyQt6")
        layout.addWidget(label)
        logger.info("✓ QLabel créé avec succès")
        
        # Ajouter un bouton
        button = QPushButton("Bouton de test")
        layout.addWidget(button)
        logger.info("✓ QPushButton créé avec succès")
        
        # Finaliser le widget
        widget.setLayout(layout)
        logger.info("✓ Layout appliqué avec succès")
        
        # Ne pas afficher le widget, juste tester sa création
        # widget.show()
        
        app.quit()
        return True
    except Exception as e:
        logger.error(f"✗ Erreur lors de la création des widgets: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_pyqt_plugins():
    """Teste les plugins PyQt6"""
    logger.info("Test des plugins PyQt6")
    try:
        from PyQt6.QtCore import QLibraryInfo, QCoreApplication
        
        # Créer une application
        app = QCoreApplication([])
        
        # Vérifier les chemins des plugins
        plugin_path = QLibraryInfo.path(QLibraryInfo.LibraryPath.PluginsPath)
        logger.info(f"Chemin des plugins: {plugin_path}")
        
        # Vérifier si le répertoire des plugins existe
        if os.path.exists(plugin_path):
            logger.info(f"✓ Répertoire des plugins trouvé: {plugin_path}")
            
            # Lister les plugins disponibles
            plugins = os.listdir(plugin_path)
            logger.info(f"Plugins disponibles ({len(plugins)}): {', '.join(plugins)}")
        else:
            logger.warning(f"✗ Répertoire des plugins non trouvé: {plugin_path}")
        
        app.quit()
        return True
    except Exception as e:
        logger.error(f"✗ Erreur lors de la vérification des plugins: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def check_qt_environment():
    """Vérifie les variables d'environnement liées à Qt"""
    logger.info("Vérification des variables d'environnement liées à Qt")
    
    qt_env_vars = [
        "QT_PLUGIN_PATH",
        "QT_QPA_PLATFORM_PLUGIN_PATH",
        "QT_DEBUG_PLUGINS"
    ]
    
    for var in qt_env_vars:
        value = os.environ.get(var)
        if value:
            logger.info(f"✓ {var} = {value}")
        else:
            logger.info(f"✗ {var} non défini")
    
    # Définir QT_DEBUG_PLUGINS pour obtenir plus d'informations sur les problèmes de plugins
    os.environ["QT_DEBUG_PLUGINS"] = "1"
    logger.info("Variable QT_DEBUG_PLUGINS définie à 1 pour le débogage")

def test_qtcharts_module():
    """Teste spécifiquement le module PyQt6.QtCharts qui est nécessaire pour l'application"""
    logger.info("Test spécifique du module PyQt6.QtCharts")
    try:
        from PyQt6.QtCharts import QChart, QChartView
        logger.info("✓ Module PyQt6.QtCharts importé avec succès")
        
        # Tester la création d'un objet QChart
        from PyQt6.QtWidgets import QApplication
        app = QApplication([])
        
        chart = QChart()
        chart.setTitle("Test Chart")
        chart_view = QChartView(chart)
        
        logger.info("✓ Objets QChart et QChartView créés avec succès")
        app.quit()
        return True
    except ImportError as e:
        logger.error(f"✗ Erreur d'importation de PyQt6.QtCharts: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"✗ Erreur lors de l'utilisation de PyQt6.QtCharts: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def main():
    logger.info("=== DÉBUT DU TEST D'INITIALISATION DE PYQT6 ===")
    
    # Vérifier les variables d'environnement Qt
    check_qt_environment()
    
    # Test d'importation de base
    if not test_pyqt_basic_import():
        logger.error("Échec de l'importation de base de PyQt6. Les tests suivants ne seront pas exécutés.")
        return 1
    
    # Test d'importation des modules
    test_pyqt_modules()
    
    # Test spécifique du module QtCharts
    test_qtcharts_module()
    
    # Test de création d'une QApplication
    if not test_qapplication_creation():
        logger.error("Échec de la création de QApplication. Les tests suivants ne seront pas exécutés.")
        return 1
    
    # Test des plugins
    test_pyqt_plugins()
    
    # Test de création de widgets de base
    test_basic_widgets()
    
    logger.info("=== FIN DU TEST D'INITIALISATION DE PYQT6 ===")
    logger.info(f"Log complet disponible dans: {log_file}")
    
    print(f"\nTest d'initialisation de PyQt6 terminé.")
    print(f"Log complet disponible dans: {log_file}")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        logger.critical(f"Erreur fatale: {str(e)}")
        logger.critical(traceback.format_exc())
        print(f"\nERREUR FATALE: {str(e)}")
        print(f"Log disponible dans: {log_file}")
        sys.exit(1)