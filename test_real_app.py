#!/usr/bin/env python3
"""
Script pour tester l'application réelle avec les corrections de permissions
"""
import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from app.ui.login_window import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_real_app():
    """Lance l'application réelle pour tester les permissions"""
    
    print("=== TEST DE L'APPLICATION RÉELLE ===")
    print("Lancement de l'application...")
    print("Connectez-vous avec un utilisateur admin ou vendeur pour tester les permissions.")
    print("Vérifiez que les boutons 'Nouvel Article', 'Nouvelle Réparation' et 'Nouveau Client' sont actifs selon les permissions.")
    
    # Créer l'application Qt
    app = QApplication(sys.argv)
    
    # Créer et afficher la fenêtre de connexion
    login_window = LoginWindow()
    login_window.show()
    
    # Lancer l'application
    sys.exit(app.exec())

if __name__ == "__main__":
    test_real_app()
