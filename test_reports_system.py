"""
Test du système de rapports et d'exports de trésorerie.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from decimal import Decimal
from app.core.services.treasury_report_service import TreasuryReportService, ReportFilter
from app.core.services.export_service import ExportService


def test_report_service():
    """Test du service de rapports"""
    print("=== Test du service de rapports ===")
    
    # Simuler une session de base de données
    class MockTransaction:
        def __init__(self, id, amount, date, category, description=""):
            self.id = id
            self.amount = amount
            self.transaction_date = date
            self.category = category
            self.description = description
            self.reference_number = f"REF-{id}"
            self.cash_register = MockCashRegister(1, "Caisse principale")
    
    class MockCashRegister:
        def __init__(self, id, name):
            self.id = id
            self.name = name
            self.current_balance = Decimal("1000.00")
            self.initial_balance = Decimal("500.00")
            self.is_active = True
    
    class MockQuery:
        def __init__(self, data):
            self.data = data
            self._filters = []
        
        def join(self, *args):
            return self
        
        def filter(self, *args):
            return self
        
        def order_by(self, *args):
            return self
        
        def offset(self, value):
            return self
        
        def limit(self, value):
            return self
        
        def count(self):
            return len(self.data)
        
        def all(self):
            return self.data
    
    class MockSession:
        def __init__(self):
            # Données de test
            self.transactions = [
                MockTransaction(1, Decimal("100.00"), datetime.now(), "SALE"),
                MockTransaction(2, Decimal("-50.00"), datetime.now(), "EXPENSE"),
                MockTransaction(3, Decimal("200.00"), datetime.now() - timedelta(days=1), "SALE"),
            ]
            
            self.cash_registers = [
                MockCashRegister(1, "Caisse principale"),
                MockCashRegister(2, "Caisse vente"),
            ]
        
        def query(self, model):
            if hasattr(model, '__name__'):
                if 'Transaction' in model.__name__:
                    return MockQuery(self.transactions)
                elif 'Register' in model.__name__:
                    return MockQuery(self.cash_registers)
            return MockQuery([])
    
    # Test du service
    session = MockSession()
    report_service = TreasuryReportService(session)
    
    # Test 1: Rapport des transactions
    print("\n--- Test rapport des transactions ---")
    filters = ReportFilter(
        start_date=datetime.now() - timedelta(days=7),
        end_date=datetime.now()
    )
    
    try:
        report = report_service.get_transactions_report(filters, page=1, page_size=10)
        print(f"✓ Rapport généré: {report.title}")
        print(f"✓ Nombre de transactions: {len(report.data)}")
        print(f"✓ Pagination: page {report.pagination.page}/{report.pagination.total_pages}")
        print(f"✓ Résumé: {len(report.summary)} éléments")
    except Exception as e:
        print(f"✗ Erreur lors de la génération du rapport: {e}")
    
    # Test 2: Rapport des caisses
    print("\n--- Test rapport des caisses ---")
    try:
        report = report_service.get_cash_registers_report(filters)
        print(f"✓ Rapport généré: {report.title}")
        print(f"✓ Nombre de caisses: {len(report.data)}")
        print(f"✓ Résumé: {len(report.summary)} éléments")
    except Exception as e:
        print(f"✗ Erreur lors de la génération du rapport: {e}")


def test_export_service():
    """Test du service d'export"""
    print("\n=== Test du service d'export ===")
    
    from app.core.services.treasury_report_service import ReportData, PaginationInfo
    
    # Créer des données de test
    test_data = [
        {
            'id': 1,
            'date': datetime.now(),
            'amount': 100.50,
            'description': 'Test transaction 1',
            'cash_register': 'Caisse principale'
        },
        {
            'id': 2,
            'date': datetime.now() - timedelta(days=1),
            'amount': -25.75,
            'description': 'Test transaction 2',
            'cash_register': 'Caisse vente'
        }
    ]
    
    summary = {
        'total_transactions': 2,
        'total_in': 100.50,
        'total_out': 25.75,
        'net_change': 74.75
    }
    
    pagination = PaginationInfo(
        page=1,
        page_size=10,
        total_items=2,
        total_pages=1
    )
    
    report_data = ReportData(
        title="Rapport de Test",
        subtitle="Données de test pour l'export",
        generated_at=datetime.now(),
        filters=None,
        pagination=pagination,
        summary=summary,
        data=test_data
    )
    
    # Test du service d'export
    export_service = ExportService("test_exports")
    
    # Test des formats disponibles
    formats = export_service.get_available_formats()
    print(f"✓ Formats disponibles: {', '.join(formats)}")
    
    # Test export CSV (toujours disponible)
    print("\n--- Test export CSV ---")
    try:
        csv_file = export_service.export_to_csv(report_data, "test_report.csv")
        print(f"✓ Export CSV réussi: {csv_file}")
        
        # Vérifier que le fichier existe
        if os.path.exists(csv_file):
            print(f"✓ Fichier créé: {os.path.getsize(csv_file)} octets")
        else:
            print("✗ Fichier non créé")
            
    except Exception as e:
        print(f"✗ Erreur export CSV: {e}")
    
    # Test export PDF (si disponible)
    if "PDF" in formats:
        print("\n--- Test export PDF ---")
        try:
            pdf_file = export_service.export_to_pdf(report_data, "test_report.pdf")
            print(f"✓ Export PDF réussi: {pdf_file}")
            
            if os.path.exists(pdf_file):
                print(f"✓ Fichier créé: {os.path.getsize(pdf_file)} octets")
            else:
                print("✗ Fichier non créé")
                
        except Exception as e:
            print(f"✗ Erreur export PDF: {e}")
    else:
        print("\n--- Export PDF non disponible (reportlab non installé) ---")
    
    # Test export Excel (si disponible)
    if "Excel" in formats:
        print("\n--- Test export Excel ---")
        try:
            excel_file = export_service.export_to_excel(report_data, "test_report.xlsx")
            print(f"✓ Export Excel réussi: {excel_file}")
            
            if os.path.exists(excel_file):
                print(f"✓ Fichier créé: {os.path.getsize(excel_file)} octets")
            else:
                print("✗ Fichier non créé")
                
        except Exception as e:
            print(f"✗ Erreur export Excel: {e}")
    else:
        print("\n--- Export Excel non disponible (openpyxl non installé) ---")


def test_filters():
    """Test des filtres de rapports"""
    print("\n=== Test des filtres ===")
    
    # Test de création de filtres
    filters = ReportFilter(
        start_date=datetime.now() - timedelta(days=30),
        end_date=datetime.now(),
        cash_register_ids=[1, 2],
        min_amount=Decimal("10.00"),
        max_amount=Decimal("1000.00"),
        search_text="test"
    )
    
    print(f"✓ Filtre créé:")
    print(f"  - Période: {filters.start_date.strftime('%d/%m/%Y')} - {filters.end_date.strftime('%d/%m/%Y')}")
    print(f"  - Caisses: {filters.cash_register_ids}")
    print(f"  - Montants: {filters.min_amount} - {filters.max_amount}")
    print(f"  - Recherche: '{filters.search_text}'")


def test_pagination():
    """Test de la pagination"""
    print("\n=== Test de la pagination ===")
    
    from app.core.services.treasury_report_service import PaginationInfo
    
    # Test avec différents scénarios
    scenarios = [
        (1, 10, 25),  # Page 1, 10 par page, 25 total
        (3, 10, 25),  # Page 3, 10 par page, 25 total
        (1, 50, 25),  # Page 1, 50 par page, 25 total
    ]
    
    for page, page_size, total_items in scenarios:
        total_pages = (total_items + page_size - 1) // page_size
        
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total_items=total_items,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_previous=page > 1
        )
        
        print(f"✓ Pagination {page}/{total_pages}:")
        print(f"  - {total_items} éléments, {page_size} par page")
        print(f"  - Précédent: {pagination.has_previous}, Suivant: {pagination.has_next}")


if __name__ == "__main__":
    print("Test du système de rapports et d'exports\n")
    
    test_report_service()
    test_export_service()
    test_filters()
    test_pagination()
    
    print("\n=== Tests terminés ===")
    print("\nPour tester l'interface graphique, lancez l'application principale")
    print("et cliquez sur le bouton 'Rapports' dans la vue de trésorerie.")
