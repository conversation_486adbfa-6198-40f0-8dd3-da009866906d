#!/usr/bin/env python3
"""
Script de test pour le service des rôles et permissions
"""

import sys
import os
sys.path.append('.')

import asyncio
from datetime import datetime

def test_roles_permissions():
    """
    Tester le service des rôles et permissions
    """
    print("=== Test du service des rôles et permissions ===")
    
    try:
        from app.utils.database import get_db, SessionLocal
        from app.core.services.user_service import UserService
        from app.core.services.auth_service import AuthService
        from app.core.models.user import User

        # Créer une session de base de données
        db = SessionLocal()
        
        # Créer les services
        user_service = UserService(db)
        auth_service = AuthService(db)
        
        print("✅ Services créés avec succès")
        
        # Test 1: Récupérer l'utilisateur
        print("\n--- Test 1: Récupération de l'utilisateur ---")

        # Vérifier tous les utilisateurs d'abord
        all_users = db.query(User).all()
        print(f"Nombre total d'utilisateurs dans la base: {len(all_users)}")
        for u in all_users:
            print(f"  ID: {u.id}, Email: {u.email}, Nom: {u.full_name}")

        # Récupérer l'utilisateur spécifique
        user = db.query(User).filter(User.email == '<EMAIL>').first()
        if user:
            print(f"✅ Utilisateur trouvé: {user.email}")
            print(f"  ID: {user.id}")
            print(f"  Nom: {user.full_name}")
            print(f"  Statut: {user.status}")
            print(f"  Dernière connexion: {user.last_login}")
        else:
            print("❌ Utilisateur non trouvé")
            return
        
        # Test 2: Récupérer les permissions de l'utilisateur
        print("\n--- Test 2: Récupération des permissions ---")
        async def test_permissions():
            try:
                permissions = await user_service.get_user_permissions(user.id)
                print(f"✅ Permissions récupérées: {len(permissions)}")
                for perm in permissions:
                    print(f"  - {perm}")
                return permissions
            except Exception as e:
                print(f"❌ Erreur lors de la récupération des permissions: {e}")
                import traceback
                traceback.print_exc()
                return []
        
        # Exécuter le test asynchrone
        permissions = asyncio.run(test_permissions())
        
        # Test 3: Vérifier des permissions spécifiques
        print("\n--- Test 3: Vérification de permissions spécifiques ---")
        async def test_specific_permissions():
            test_permissions_list = ['user.view', 'user.create', 'system.admin', 'nonexistent.permission']
            
            for perm in test_permissions_list:
                try:
                    has_perm = await user_service.has_permission(user.id, perm)
                    status = "✅" if has_perm else "❌"
                    print(f"  {status} {perm}: {has_perm}")
                except Exception as e:
                    print(f"  ❌ Erreur pour {perm}: {e}")
        
        asyncio.run(test_specific_permissions())
        
        # Test 4: Test d'authentification
        print("\n--- Test 4: Test d'authentification ---")
        async def test_authentication():
            try:
                # Tenter une authentification
                result = await auth_service.authenticate_user(
                    email='<EMAIL>',
                    password='password123',
                    ip_address='127.0.0.1',
                    user_agent='Test Script'
                )
                
                if result:
                    print("✅ Authentification réussie")
                    print(f"  Token généré: {result[:50]}..." if len(result) > 50 else f"  Token: {result}")
                    
                    # Vérifier que la date de dernière connexion a été mise à jour
                    db.refresh(user)
                    print(f"  Nouvelle date de dernière connexion: {user.last_login}")
                else:
                    print("❌ Authentification échouée")
                    
            except Exception as e:
                print(f"❌ Erreur lors de l'authentification: {e}")
                import traceback
                traceback.print_exc()
        
        asyncio.run(test_authentication())
        
        # Test 5: Vérifier l'affichage de la date
        print("\n--- Test 5: Test du formatage de date ---")
        try:
            from app.ui.views.user.user_table_model import UserTableModel
            
            # Créer une instance du modèle (sans interface graphique)
            model = UserTableModel()
            model.db = db  # Assigner la session de base de données
            
            # Tester le formatage de la date
            formatted_date = model._format_datetime(user.last_login)
            print(f"✅ Date formatée: {formatted_date}")
            
        except Exception as e:
            print(f"❌ Erreur lors du test de formatage: {e}")
            # Test manuel du formatage
            if user.last_login:
                if isinstance(user.last_login, datetime):
                    formatted = user.last_login.strftime("%d/%m/%Y %H:%M")
                    print(f"✅ Formatage manuel réussi: {formatted}")
                else:
                    print(f"⚠️  Type de date inattendu: {type(user.last_login)}")
            else:
                print("⚠️  Pas de date de dernière connexion")
        
        db.close()
        print("\n✅ Tous les tests terminés")
        
    except Exception as e:
        print(f"❌ Erreur générale lors des tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_roles_permissions()
