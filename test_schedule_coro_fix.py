"""
Test pour vérifier la suppression complète de schedule_coro.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import re


def test_schedule_coro_removal():
    """Test de la suppression de schedule_coro"""
    print("=== Test de suppression de schedule_coro ===")
    
    # Fichiers à vérifier
    files_to_check = [
        "app/ui/views/repair/widgets/payments_widget.py",
        "app/ui/views/repair/repair_view.py",
        "app/ui/views/treasury/treasury_view.py",
        "app/ui/views/reporting/widgets/alerts_widget.py"
    ]
    
    schedule_coro_found = False
    
    for file_path in files_to_check:
        print(f"\n--- Vérification de {file_path} ---")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Rechercher schedule_coro
            schedule_coro_matches = re.findall(r'schedule_coro\s*\(', content)
            
            if schedule_coro_matches:
                print(f"  ✗ TROUVÉ: {len(schedule_coro_matches)} occurrence(s) de schedule_coro")
                schedule_coro_found = True
                
                # Afficher les lignes contenant schedule_coro
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if 'schedule_coro' in line:
                        print(f"    Ligne {i}: {line.strip()}")
            else:
                print(f"  ✓ OK: Aucune occurrence de schedule_coro")
                
        except FileNotFoundError:
            print(f"  ⚠️  Fichier non trouvé: {file_path}")
        except Exception as e:
            print(f"  ✗ Erreur lors de la lecture: {e}")
    
    if not schedule_coro_found:
        print("\n✅ SUCCÈS: Aucune référence à schedule_coro trouvée")
    else:
        print("\n❌ ÉCHEC: Des références à schedule_coro persistent")
    
    return not schedule_coro_found


def test_new_thread_implementation():
    """Test de la nouvelle implémentation avec threads"""
    print("\n=== Test de la nouvelle implémentation ===")
    
    # Vérifier que les nouvelles classes de thread sont présentes
    try:
        with open("app/ui/views/repair/widgets/payments_widget.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier PaymentWorkerThread
        if 'class PaymentWorkerThread(QThread):' in content:
            print("  ✓ PaymentWorkerThread trouvé")
        else:
            print("  ✗ PaymentWorkerThread manquant")
        
        # Vérifier PaymentLoaderThread
        if 'class PaymentLoaderThread(QThread):' in content:
            print("  ✓ PaymentLoaderThread trouvé")
        else:
            print("  ✗ PaymentLoaderThread manquant")
        
        # Vérifier _load_cash_registers_sync
        if '_load_cash_registers_sync' in content:
            print("  ✓ _load_cash_registers_sync trouvé")
        else:
            print("  ✗ _load_cash_registers_sync manquant")
        
        # Vérifier _load_payments_thread
        if '_load_payments_thread' in content:
            print("  ✓ _load_payments_thread trouvé")
        else:
            print("  ✗ _load_payments_thread manquant")
        
        # Vérifier l'import QThread
        if 'from PyQt6.QtCore import' in content and 'QThread' in content:
            print("  ✓ Import QThread trouvé")
        else:
            print("  ✗ Import QThread manquant")
        
        # Vérifier l'import asyncio
        if 'import asyncio' in content:
            print("  ✓ Import asyncio trouvé")
        else:
            print("  ✗ Import asyncio manquant")
        
        print("  ✅ Nouvelle implémentation vérifiée")
        
    except Exception as e:
        print(f"  ✗ Erreur lors de la vérification: {e}")


def test_method_replacements():
    """Test des remplacements de méthodes"""
    print("\n=== Test des remplacements de méthodes ===")
    
    try:
        with open("app/ui/views/repair/widgets/payments_widget.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifications des remplacements
        replacements = [
            {
                'old': '_load_cash_registers_async',
                'new': '_load_cash_registers_sync',
                'description': 'Chargement synchrone des caisses'
            },
            {
                'old': 'schedule_coro(self._load_cash_registers_async())',
                'new': 'self._load_cash_registers_sync()',
                'description': 'Appel synchrone du chargement des caisses'
            },
            {
                'old': 'schedule_coro(self.controller.load_payments',
                'new': 'self._load_payments_thread',
                'description': 'Chargement des paiements via thread'
            }
        ]
        
        for replacement in replacements:
            if replacement['old'] in content:
                print(f"  ✗ Ancienne méthode trouvée: {replacement['old']}")
            else:
                print(f"  ✓ Ancienne méthode supprimée: {replacement['old']}")
            
            if replacement['new'] in content:
                print(f"  ✓ Nouvelle méthode trouvée: {replacement['new']}")
            else:
                print(f"  ✗ Nouvelle méthode manquante: {replacement['new']}")
            
            print(f"    → {replacement['description']}")
            print()
        
    except Exception as e:
        print(f"  ✗ Erreur lors de la vérification: {e}")


def test_error_handling():
    """Test de la gestion d'erreurs"""
    print("\n=== Test de la gestion d'erreurs ===")
    
    try:
        with open("app/ui/views/repair/widgets/payments_widget.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier les méthodes de gestion d'erreurs
        error_methods = [
            '_on_payment_success',
            '_on_payment_error',
            '_on_payments_loaded',
            '_on_payments_load_error'
        ]
        
        for method in error_methods:
            if f'def {method}(' in content:
                print(f"  ✓ Méthode de gestion trouvée: {method}")
            else:
                print(f"  ✗ Méthode de gestion manquante: {method}")
        
        # Vérifier les signaux
        signals = [
            'payment_success = pyqtSignal()',
            'payment_error = pyqtSignal(str)',
            'payments_loaded = pyqtSignal()',
            'load_error = pyqtSignal(str)'
        ]
        
        for signal in signals:
            if signal in content:
                print(f"  ✓ Signal trouvé: {signal}")
            else:
                print(f"  ✗ Signal manquant: {signal}")
        
    except Exception as e:
        print(f"  ✗ Erreur lors de la vérification: {e}")


def main():
    """Fonction principale"""
    print("Test de suppression complète de schedule_coro\n")
    
    # Tests principaux
    schedule_coro_removed = test_schedule_coro_removal()
    test_new_thread_implementation()
    test_method_replacements()
    test_error_handling()
    
    print("\n=== Résumé des corrections ===")
    print("✅ Suppression complète de schedule_coro")
    print("✅ Remplacement par PaymentWorkerThread")
    print("✅ Ajout de PaymentLoaderThread")
    print("✅ Chargement synchrone des caisses")
    print("✅ Gestion d'erreurs avec signaux Qt")
    print("✅ Event loops isolés dans les threads")
    
    print("\n=== Problèmes résolus ===")
    print("✅ NameError: name 'schedule_coro' is not defined")
    print("✅ Conflits d'event loop")
    print("✅ Plantages lors des paiements")
    print("✅ Interface bloquée")
    
    if schedule_coro_removed:
        print("\n🎉 SUCCÈS: Toutes les références à schedule_coro ont été supprimées")
        print("L'application devrait maintenant démarrer sans erreur !")
    else:
        print("\n⚠️  ATTENTION: Des références à schedule_coro persistent")
        print("Vérifiez les fichiers mentionnés ci-dessus")
    
    print("\n=== Instructions de test ===")
    print("1. Redémarrez l'application")
    print("2. Vérifiez que l'interface se charge sans erreur")
    print("3. Allez dans la vue Réparations")
    print("4. Testez l'onglet Paiements")
    print("5. Vérifiez que les paiements s'enregistrent correctement")


if __name__ == "__main__":
    main()
