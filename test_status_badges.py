#!/usr/bin/env python3
"""
Script de test pour vérifier l'affichage des badges de statut dans l'inventaire
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QA<PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QTableView
from PyQt6.QtCore import Qt
from app.ui.views.inventory.inventory_table_model import InventoryTableModel
from app.ui.views.inventory.widgets.status_badge_widget import StatusBadgeDelegate

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test des Badges de Statut - Inventaire")
        self.setGeometry(100, 100, 1200, 600)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        layout = QVBoxLayout(central_widget)
        
        # C<PERSON>er le modèle de table
        self.model = InventoryTableModel()
        
        # Créer le tableau
        self.table_view = QTableView()
        self.table_view.setModel(self.model)
        
        # Appliquer les délégués personnalisés
        self.table_view.setItemDelegateForColumn(8, StatusBadgeDelegate(self.table_view))  # Statut
        
        # Configuration du tableau
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSortingEnabled(True)
        
        # Style du tableau
        self.table_view.setStyleSheet("""
            QTableView {
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                gridline-color: #e0e0e0;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
            QTableView::item {
                padding: 8px;
                border: none;
            }
            QHeaderView::section {
                background-color: #f5f7fa;
                color: #1976d2;
                padding: 12px 8px;
                border: none;
                border-bottom: 2px solid #e0e0e0;
                font-weight: 600;
                font-size: 13px;
            }
        """)
        
        # Définir les largeurs de colonnes
        column_widths = {
            0: 100,  # SKU
            1: 200,  # Nom
            2: 120,  # Catégorie
            3: 80,   # Quantité
            4: 120,  # Emplacement
            5: 100,  # Prix d'achat
            6: 100,  # Prix de vente
            7: 120,  # Marge bénéficiaire
            8: 120,  # Statut
        }
        
        for col, width in column_widths.items():
            self.table_view.setColumnWidth(col, width)
        
        layout.addWidget(self.table_view)
        
        # Charger les données
        self.load_data()
    
    async def load_data(self):
        """Charge les données de test"""
        try:
            await self.model.load_data()
            print("Données chargées avec succès!")
            print(f"Nombre d'articles: {len(self.model.items)}")
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()

def main():
    app = QApplication(sys.argv)
    
    # Créer la fenêtre de test
    window = TestWindow()
    window.show()
    
    # Lancer l'application
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 