#!/usr/bin/env python3
"""
Test pour vérifier que tous les problèmes de syntaxe et d'imports sont corrigés
"""
import sys
import os

def test_critical_imports():
    """Teste les imports critiques de l'application"""
    try:
        print("Testing critical imports...")
        
        # Test des imports principaux
        imports_to_test = [
            ("app.app_manager", "AppManager"),
            ("app.ui.window", "MainWindow"),
            ("app.ui.views.purchasing.purchasing_view", "PurchasingView"),
            ("app.ui.views.purchasing.dialogs.purchase_order_dialog", "PurchaseOrderDialog"),
            ("app.ui.views.purchasing.dialogs.order_item_dialog", "OrderItemDialog"),
            ("app.core.services.purchasing_service", "PurchasingService"),
            ("app.core.services.inventory_service", "InventoryService"),
            ("app.utils.sku_generator", "SKUGenerator"),
        ]
        
        for module_name, class_name in imports_to_test:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                print(f"✅ {module_name}.{class_name}")
            except Exception as e:
                print(f"❌ {module_name}.{class_name}: {e}")
                return False
        
        print("SUCCESS: All critical imports work correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in critical imports test: {e}")
        return False

def test_syntax_validation():
    """Teste la validation de la syntaxe des fichiers modifiés"""
    try:
        print("Testing syntax validation...")
        
        files_to_check = [
            "app/ui/views/purchasing/dialogs/order_item_dialog.py",
            "app/ui/views/purchasing/dialogs/purchase_order_dialog.py",
            "app/ui/views/inventory/dialogs/item_dialog.py",
            "app/core/services/purchasing_service.py",
            "app/core/services/inventory_service.py",
            "app/core/schemas/purchasing.py",
            "app/utils/sku_generator.py"
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Compiler le code pour vérifier la syntaxe
                    compile(content, file_path, 'exec')
                    print(f"✅ {file_path}")
                except SyntaxError as e:
                    print(f"❌ {file_path}: Syntax error at line {e.lineno}: {e.msg}")
                    return False
                except Exception as e:
                    print(f"❌ {file_path}: {e}")
                    return False
            else:
                print(f"⚠️  {file_path}: File not found")
        
        print("SUCCESS: All syntax validation passed")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in syntax validation test: {e}")
        return False

def test_sku_generator_functionality():
    """Teste la fonctionnalité du générateur de SKU"""
    try:
        print("Testing SKU generator functionality...")
        
        from app.utils.sku_generator import SKUGenerator
        
        # Test de génération de SKU
        sku = SKUGenerator.generate_sku(category_name="LCD Screens")
        if not sku or len(sku.split('-')) != 3:
            print(f"ERROR: Invalid SKU generated: {sku}")
            return False
        
        # Test de validation de SKU
        if not SKUGenerator.validate_sku(sku):
            print(f"ERROR: Generated SKU failed validation: {sku}")
            return False
        
        # Test d'extraction de préfixe
        prefix = SKUGenerator.extract_category_from_sku(sku)
        if not prefix:
            print(f"ERROR: Could not extract prefix from SKU: {sku}")
            return False
        
        print(f"✅ SKU Generator: {sku} (prefix: {prefix})")
        print("SUCCESS: SKU generator functionality works correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in SKU generator test: {e}")
        return False

def test_service_methods():
    """Teste les méthodes des services"""
    try:
        print("Testing service methods...")
        
        from app.core.services.inventory_service import InventoryService
        from app.utils.database import SessionLocal
        
        # Créer une session de test
        db = SessionLocal()
        service = InventoryService(db)
        
        # Vérifier que les nouvelles méthodes existent
        methods_to_check = ['get_by_sku', 'get_by_barcode', 'get_by_name']
        
        for method_name in methods_to_check:
            if hasattr(service, method_name):
                print(f"✅ InventoryService.{method_name}")
            else:
                print(f"❌ InventoryService.{method_name}: Method not found")
                db.close()
                return False
        
        db.close()
        print("SUCCESS: Service methods work correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in service methods test: {e}")
        return False

def test_schema_consistency():
    """Teste la cohérence des schémas"""
    try:
        print("Testing schema consistency...")
        
        from app.core.schemas.purchasing import PurchaseOrderItemBase
        
        # Vérifier que le schéma utilise purchase_unit_price
        schema_instance = PurchaseOrderItemBase(
            product_id=1,
            quantity=3,
            purchase_unit_price=25.50
        )
        
        if hasattr(schema_instance, 'purchase_unit_price') and schema_instance.purchase_unit_price == 25.50:
            print("✅ Schema uses purchase_unit_price correctly")
        else:
            print("❌ Schema does not use purchase_unit_price correctly")
            return False
        
        print("SUCCESS: Schema consistency works correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Error in schema consistency test: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🔍 TEST DE SYNTAXE ET D'IMPORTS - Vérification finale")
    print("=" * 60)
    
    all_tests = [
        ("Critical Imports", test_critical_imports),
        ("Syntax Validation", test_syntax_validation),
        ("SKU Generator", test_sku_generator_functionality),
        ("Service Methods", test_service_methods),
        ("Schema Consistency", test_schema_consistency)
    ]
    
    passed = 0
    total = len(all_tests)
    
    for test_name, test_func in all_tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"   ❌ {test_name} FAILED")
    
    print(f"\n{'='*60}")
    print(f"📊 RÉSULTATS: {passed}/{total} tests passés")
    
    if passed == total:
        print("\n🎉 SUCCÈS COMPLET! L'application est prête:")
        print("   ✅ Tous les imports fonctionnent")
        print("   ✅ Aucune erreur de syntaxe")
        print("   ✅ Générateur de SKU opérationnel")
        print("   ✅ Services d'inventaire mis à jour")
        print("   ✅ Schémas cohérents")
        print("\n🚀 L'application peut maintenant être lancée sans erreur!")
        print("\n💡 Pour tester l'application:")
        print("   1. Lancez: python main.py")
        print("   2. Allez dans: Gestion des achats → Nouvelle commande")
        print("   3. Cliquez sur: Ajouter un article")
        print("   4. Testez la création de produits")
        return True
    else:
        print(f"\n❌ ÉCHEC: {total - passed} test(s) ont échoué")
        print("   Des problèmes persistent encore")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
