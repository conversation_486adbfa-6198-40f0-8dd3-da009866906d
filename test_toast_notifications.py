"""
Test du système de notifications toast basé sur l'event bus.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt6.QtCore import QTimer
from app.ui.components.toast_notification import <PERSON>astManager, ToastType, get_toast_manager
from app.utils.event_bus import event_bus


class TestWindow(QMainWindow):
    """Fenêtre de test pour les notifications toast"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test des notifications toast")
        self.setGeometry(100, 100, 800, 600)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QVBoxLayout(central_widget)
        
        # Boutons de test
        success_btn = QPushButton("Notification de succès")
        success_btn.clicked.connect(self.show_success)
        layout.addWidget(success_btn)
        
        info_btn = QPushButton("Notification d'information")
        info_btn.clicked.connect(self.show_info)
        layout.addWidget(info_btn)
        
        warning_btn = QPushButton("Notification d'avertissement")
        warning_btn.clicked.connect(self.show_warning)
        layout.addWidget(warning_btn)
        
        error_btn = QPushButton("Notification d'erreur")
        error_btn.clicked.connect(self.show_error)
        layout.addWidget(error_btn)
        
        multiple_btn = QPushButton("Notifications multiples")
        multiple_btn.clicked.connect(self.show_multiple)
        layout.addWidget(multiple_btn)
        
        event_bus_btn = QPushButton("Test via Event Bus")
        event_bus_btn.clicked.connect(self.test_event_bus)
        layout.addWidget(event_bus_btn)
        
        # Initialiser le gestionnaire de toast
        self.toast_manager = get_toast_manager(self)
        
        # Connecter aux signaux de l'event bus
        self.connect_event_bus()
    
    def connect_event_bus(self):
        """Connecte aux signaux de l'event bus"""
        event_bus.show_success_notification.connect(self.on_success_notification)
        event_bus.show_info_notification.connect(self.on_info_notification)
        event_bus.show_warning_notification.connect(self.on_warning_notification)
        event_bus.show_error_notification.connect(self.on_error_notification)
    
    def on_success_notification(self, message: str):
        """Gère les notifications de succès via l'event bus"""
        if self.toast_manager:
            self.toast_manager.show_toast(message, ToastType.SUCCESS)
    
    def on_info_notification(self, message: str):
        """Gère les notifications d'info via l'event bus"""
        if self.toast_manager:
            self.toast_manager.show_toast(message, ToastType.INFO)
    
    def on_warning_notification(self, message: str):
        """Gère les notifications d'avertissement via l'event bus"""
        if self.toast_manager:
            self.toast_manager.show_toast(message, ToastType.WARNING)
    
    def on_error_notification(self, message: str):
        """Gère les notifications d'erreur via l'event bus"""
        if self.toast_manager:
            self.toast_manager.show_toast(message, ToastType.ERROR)
    
    def show_success(self):
        """Affiche une notification de succès"""
        if self.toast_manager:
            self.toast_manager.show_toast(
                "Opération réussie avec succès !",
                ToastType.SUCCESS
            )
    
    def show_info(self):
        """Affiche une notification d'information"""
        if self.toast_manager:
            self.toast_manager.show_toast(
                "Voici une information importante à retenir.",
                ToastType.INFO
            )
    
    def show_warning(self):
        """Affiche une notification d'avertissement"""
        if self.toast_manager:
            self.toast_manager.show_toast(
                "Attention : cette action nécessite votre vigilance.",
                ToastType.WARNING,
                duration=4000
            )
    
    def show_error(self):
        """Affiche une notification d'erreur"""
        if self.toast_manager:
            self.toast_manager.show_toast(
                "Erreur : impossible de terminer l'opération.",
                ToastType.ERROR,
                duration=5000
            )
    
    def show_multiple(self):
        """Affiche plusieurs notifications en séquence"""
        if not self.toast_manager:
            return
        
        messages = [
            ("Première notification", ToastType.INFO),
            ("Deuxième notification", ToastType.SUCCESS),
            ("Troisième notification", ToastType.WARNING),
            ("Quatrième notification", ToastType.ERROR),
            ("Cinquième notification", ToastType.INFO)
        ]
        
        for i, (message, toast_type) in enumerate(messages):
            QTimer.singleShot(i * 500, lambda m=message, t=toast_type: 
                             self.toast_manager.show_toast(m, t))
    
    def test_event_bus(self):
        """Test des notifications via l'event bus"""
        # Simuler des événements de trésorerie
        event_bus.show_success("Transaction de 150.00 DA enregistrée avec succès")
        
        QTimer.singleShot(1000, lambda: 
                         event_bus.show_info("Mise à jour de la trésorerie en cours..."))
        
        QTimer.singleShot(2000, lambda: 
                         event_bus.show_warning("Solde faible dans la caisse principale"))
        
        QTimer.singleShot(3000, lambda: 
                         event_bus.show_error("Erreur de connexion à la base de données"))


def test_toast_system():
    """Test du système de notifications toast"""
    print("=== Test du système de notifications toast ===")
    
    app = QApplication(sys.argv)
    
    # Créer la fenêtre de test
    window = TestWindow()
    window.show()
    
    print("Fenêtre de test affichée")
    print("Cliquez sur les boutons pour tester les notifications")
    print("Fermez la fenêtre pour terminer le test")
    
    # Afficher une notification de bienvenue
    QTimer.singleShot(1000, lambda: 
                     event_bus.show_info("Système de notifications toast initialisé"))
    
    # Lancer l'application
    sys.exit(app.exec())


if __name__ == "__main__":
    test_toast_system()
