#!/usr/bin/env python3
import sys
import os

# Test des imports d'interface utilisateur
try:
    # Test des dialogues
    print("Testing UI imports...")
    
    # Ces imports ne devraient pas échouer même si on ne peut pas créer les widgets
    from app.ui.views.repair.dialogs.used_parts_dialog import UsedPartsDialog
    from app.ui.views.purchasing.dialogs.order_item_dialog import OrderItemDialog
    from app.ui.views.purchasing.dialogs.supplier_quote_dialog import SupplierQuoteDialog
    
    print("SUCCESS: UI dialogs imported")
    
    # Test des widgets
    from app.ui.views.repair.widgets.used_parts_widget import UsedPartsWidget
    from app.ui.views.repair.widgets.repair_parts_widget import RepairPartsWidget
    
    print("SUCCESS: UI widgets imported")
    
    # Test des services
    from app.core.services.sale_service import SaleService
    from app.core.services.purchasing_service import PurchasingService
    
    print("SUCCESS: Services imported")
    
    print("All UI components imported successfully!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
