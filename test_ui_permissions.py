import sys
import asyncio
sys.path.append('.')

from PyQt6.QtWidgets import QApplication
from app.ui.window import MainWindow

def test_ui_permissions():
    """Test des permissions dans l'interface utilisateur"""
    
    print("=== Test des permissions dans l'interface utilisateur ===")
    
    # Créer l'application Qt
    app = QApplication(sys.argv)
    
    # Test 1: Utilisateur avec permissions limitées (vendeur)
    print("\n=== Test 1: Utilisateur vendeur (permissions limitées) ===")
    vendeur_info = {
        'id': 1,
        'email': '<EMAIL>',
        'full_name': 'Vendeur Test',
        'permissions': [
            'customer.view',
            'customer.create', 
            'customer.edit',
            'inventory.view',
            'repair.view',
            'repair.create'
        ]
    }
    
    # Créer la fenêtre principale avec les infos du vendeur
    window_vendeur = MainWindow(user_info=vendeur_info)
    
    # Vérifier les permissions pour chaque section
    sections_to_test = [
        'dashboard',
        'inventory', 
        'repair',
        'customer',
        'supplier',
        'purchasing',
        'user',
        'settings',
        'reporting'
    ]
    
    print("Permissions du vendeur:")
    for section in sections_to_test:
        has_permission = window_vendeur._check_section_permission(section)
        button_enabled = (section in window_vendeur.nav_buttons and 
                         window_vendeur.nav_buttons[section].isEnabled())
        
        status = "✅ AUTORISÉ" if has_permission else "❌ REFUSÉ"
        button_status = "✅ ACTIVÉ" if button_enabled else "❌ DÉSACTIVÉ"
        
        print(f"  {section}: {status} | Bouton: {button_status}")
        
        # Vérifier la cohérence
        if has_permission != button_enabled and section in window_vendeur.nav_buttons:
            print(f"    ⚠️  INCOHÉRENCE: Permission={has_permission}, Bouton={button_enabled}")
    
    # Test 2: Utilisateur administrateur
    print("\n=== Test 2: Utilisateur administrateur (accès complet) ===")
    admin_info = {
        'id': 2,
        'email': '<EMAIL>',
        'full_name': 'Admin Test',
        'permissions': [
            'user.create',
            'user.delete',
            'user.manage_roles',
            'system.settings',
            'customer.view',
            'customer.create',
            'customer.edit',
            'customer.delete',
            'supplier.view',
            'supplier.create',
            'supplier.edit',
            'supplier.delete',
            'inventory.view',
            'inventory.create',
            'inventory.edit',
            'inventory.delete',
            'repair.view',
            'repair.create',
            'repair.edit',
            'repair.delete',
            'purchasing.view',
            'purchasing.create',
            'purchasing.edit',
            'purchasing.delete',
            'reporting.view',
            'reporting.export'
        ]
    }
    
    # Créer la fenêtre principale avec les infos de l'admin
    window_admin = MainWindow(user_info=admin_info)
    
    print("Permissions de l'administrateur:")
    for section in sections_to_test:
        has_permission = window_admin._check_section_permission(section)
        button_enabled = (section in window_admin.nav_buttons and 
                         window_admin.nav_buttons[section].isEnabled())
        
        status = "✅ AUTORISÉ" if has_permission else "❌ REFUSÉ"
        button_status = "✅ ACTIVÉ" if button_enabled else "❌ DÉSACTIVÉ"
        
        print(f"  {section}: {status} | Bouton: {button_status}")
        
        # Vérifier la cohérence
        if has_permission != button_enabled and section in window_admin.nav_buttons:
            print(f"    ⚠️  INCOHÉRENCE: Permission={has_permission}, Bouton={button_enabled}")
    
    # Test 3: Test de navigation avec restrictions
    print("\n=== Test 3: Test de navigation avec restrictions ===")
    
    # Tester la navigation vers une section interdite pour le vendeur
    print("Test navigation vendeur vers section 'user' (interdite):")
    try:
        # Simuler un clic sur le bouton utilisateurs
        result = window_vendeur._check_section_permission('user')
        print(f"  Vérification permission: {result}")
        
        if not result:
            print("  ✅ Navigation bloquée comme attendu")
        else:
            print("  ❌ Navigation autorisée alors qu'elle devrait être bloquée")
    except Exception as e:
        print(f"  Erreur lors du test: {e}")
    
    # Test 4: Vérifier les tooltips et styles
    print("\n=== Test 4: Vérification des tooltips et styles ===")
    
    print("Boutons désactivés pour le vendeur:")
    for section_id, button in window_vendeur.nav_buttons.items():
        if not button.isEnabled():
            tooltip = button.toolTip()
            print(f"  {section_id}: {tooltip}")
    
    print("Boutons activés pour l'admin:")
    enabled_count = sum(1 for button in window_admin.nav_buttons.values() if button.isEnabled())
    total_count = len(window_admin.nav_buttons)
    print(f"  {enabled_count}/{total_count} boutons activés")
    
    # Test 5: Vérifier le message de la barre de statut
    print("\n=== Test 5: Messages de la barre de statut ===")
    
    vendeur_status = window_vendeur.statusBar().currentMessage()
    admin_status = window_admin.statusBar().currentMessage()
    
    print(f"Message vendeur: '{vendeur_status}'")
    print(f"Message admin: '{admin_status}'")
    
    # Résumé
    print("\n=== Résumé du test ===")
    
    # Compter les sections autorisées
    vendeur_allowed = sum(1 for section in sections_to_test 
                         if window_vendeur._check_section_permission(section))
    admin_allowed = sum(1 for section in sections_to_test 
                       if window_admin._check_section_permission(section))
    
    print(f"Vendeur: {vendeur_allowed}/{len(sections_to_test)} sections autorisées")
    print(f"Admin: {admin_allowed}/{len(sections_to_test)} sections autorisées")
    
    if vendeur_allowed < admin_allowed:
        print("✅ SUCCÈS: Le système de permissions fonctionne - le vendeur a moins d'accès que l'admin")
    else:
        print("❌ PROBLÈME: Le vendeur a autant d'accès que l'admin")
    
    # Nettoyer
    window_vendeur.close()
    window_admin.close()
    app.quit()
    
    print("✅ Test terminé")

if __name__ == "__main__":
    test_ui_permissions()
