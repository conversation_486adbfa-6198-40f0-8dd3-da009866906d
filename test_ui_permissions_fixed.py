#!/usr/bin/env python3
"""
Script pour tester les permissions dans l'interface utilisateur après les corrections
"""
import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from app.ui.window import MainWindow

def test_ui_permissions():
    """Teste les permissions dans l'interface utilisateur"""
    
    print("=== TEST DES PERMISSIONS DANS L'INTERFACE UTILISATEUR ===\n")
    
    # Créer l'application Qt
    app = QApplication(sys.argv)
    
    try:
        # Test avec un utilisateur admin
        print("1. Test avec utilisateur admin:")
        admin_user_info = {
            'id': 1,
            'email': '<EMAIL>',
            'full_name': 'Administrateur',
            'permissions': [
                'inventory.create', 'repair.create', 'customer.create',
                'user.create', 'system.settings', 'admin'
            ],
            'roles': ['Administrateur'],
            'is_admin': True
        }
        
        window_admin = MainWindow(admin_user_info)
        
        # Vérifier les boutons dans les vues
        print("   Boutons dans InventoryView:")
        if hasattr(window_admin.inventory, 'add_button'):
            enabled = window_admin.inventory.add_button.isEnabled()
            print(f"     - Nouvel Article: {'✅ ACTIVÉ' if enabled else '❌ DÉSACTIVÉ'}")
        
        print("   Boutons dans RepairView:")
        if hasattr(window_admin.repair, 'add_button'):
            enabled = window_admin.repair.add_button.isEnabled()
            print(f"     - Nouvelle Réparation: {'✅ ACTIVÉ' if enabled else '❌ DÉSACTIVÉ'}")
        
        print("   Boutons dans CustomerView:")
        if hasattr(window_admin.customer, 'add_button'):
            enabled = window_admin.customer.add_button.isEnabled()
            print(f"     - Nouveau Client: {'✅ ACTIVÉ' if enabled else '❌ DÉSACTIVÉ'}")
        
        # Test avec un utilisateur vendeur
        print(f"\n2. Test avec utilisateur vendeur:")
        vendeur_user_info = {
            'id': 3,
            'email': '<EMAIL>',
            'full_name': 'Vendeur',
            'permissions': [
                'customer.create', 'repair.view', 'repair.create',
                'customer.edit', 'inventory.view'
            ],
            'roles': ['Vendeur']
        }
        
        window_vendeur = MainWindow(vendeur_user_info)
        
        # Vérifier les boutons dans les vues
        print("   Boutons dans InventoryView:")
        if hasattr(window_vendeur.inventory, 'add_button'):
            enabled = window_vendeur.inventory.add_button.isEnabled()
            print(f"     - Nouvel Article: {'✅ ACTIVÉ' if enabled else '❌ DÉSACTIVÉ'}")
        
        print("   Boutons dans RepairView:")
        if hasattr(window_vendeur.repair, 'add_button'):
            enabled = window_vendeur.repair.add_button.isEnabled()
            print(f"     - Nouvelle Réparation: {'✅ ACTIVÉ' if enabled else '❌ DÉSACTIVÉ'}")
        
        print("   Boutons dans CustomerView:")
        if hasattr(window_vendeur.customer, 'add_button'):
            enabled = window_vendeur.customer.add_button.isEnabled()
            print(f"     - Nouveau Client: {'✅ ACTIVÉ' if enabled else '❌ DÉSACTIVÉ'}")
        
        # Test de mise à jour des permissions
        print(f"\n3. Test de mise à jour des permissions:")
        print("   Mise à jour des informations utilisateur vendeur avec permissions admin...")
        
        # Mettre à jour les informations utilisateur
        vendeur_user_info_updated = {
            'id': 3,
            'email': '<EMAIL>',
            'full_name': 'Vendeur Promu',
            'permissions': [
                'inventory.create', 'repair.create', 'customer.create',
                'user.create', 'system.settings'
            ],
            'roles': ['Gestionnaire'],
            'is_admin': False
        }
        
        window_vendeur.user_info = vendeur_user_info_updated
        window_vendeur.update_user_interface()
        
        print("   Boutons après mise à jour:")
        print("   Boutons dans InventoryView:")
        if hasattr(window_vendeur.inventory, 'add_button'):
            enabled = window_vendeur.inventory.add_button.isEnabled()
            print(f"     - Nouvel Article: {'✅ ACTIVÉ' if enabled else '❌ DÉSACTIVÉ'}")
        
        print("   Boutons dans RepairView:")
        if hasattr(window_vendeur.repair, 'add_button'):
            enabled = window_vendeur.repair.add_button.isEnabled()
            print(f"     - Nouvelle Réparation: {'✅ ACTIVÉ' if enabled else '❌ DÉSACTIVÉ'}")
        
        print("   Boutons dans CustomerView:")
        if hasattr(window_vendeur.customer, 'add_button'):
            enabled = window_vendeur.customer.add_button.isEnabled()
            print(f"     - Nouveau Client: {'✅ ACTIVÉ' if enabled else '❌ DÉSACTIVÉ'}")
        
        print(f"\n✅ Test terminé avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

if __name__ == "__main__":
    test_ui_permissions()
