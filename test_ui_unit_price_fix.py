#!/usr/bin/env python3
"""
Test pour vérifier que les corrections UI pour unit_price sont appliquées
"""
import sys
import os

def test_order_items_table_model_headers():
    """Teste que les en-têtes du modèle de table utilisent 'Prix d'achat'"""
    try:
        print("🔍 Testing OrderItemsTableModel headers...")
        
        from app.ui.views.purchasing.order_items_table_model import OrderItemsTableModel
        
        # Créer une instance du modèle
        model = OrderItemsTableModel()
        
        # Vérifier les en-têtes
        headers = model.headers
        print(f"Headers found: {headers}")
        
        # Vérifier que "Prix d'achat" est utilisé au lieu de "Prix unitaire"
        if "Prix d'achat" in headers:
            print("✅ 'Prix d'achat' found in headers")
        else:
            print("❌ 'Prix d'achat' not found in headers")
            return False
        
        if "Prix unitaire" in headers:
            print("❌ 'Prix unitaire' still found in headers")
            return False
        else:
            print("✅ 'Prix unitaire' not found in headers")
        
        print("🎉 SUCCESS: OrderItemsTableModel headers are correct")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: OrderItemsTableModel headers test failed: {e}")
        return False

def test_order_item_dialog_data_structure():
    """Teste que OrderItemDialog utilise purchase_unit_price"""
    try:
        print("🔍 Testing OrderItemDialog data structure...")
        
        from app.ui.views.purchasing.dialogs.order_item_dialog import OrderItemDialog
        
        # Vérifier que la méthode get_item_data existe
        if hasattr(OrderItemDialog, 'get_item_data'):
            print("✅ get_item_data method exists")
        else:
            print("❌ get_item_data method missing")
            return False
        
        # Nous ne pouvons pas vraiment instancier le dialogue sans QApplication
        # mais nous pouvons vérifier le code source
        import inspect
        source = inspect.getsource(OrderItemDialog.get_item_data)
        
        if 'purchase_unit_price' in source:
            print("✅ 'purchase_unit_price' found in get_item_data method")
        else:
            print("❌ 'purchase_unit_price' not found in get_item_data method")
            return False
        
        print("🎉 SUCCESS: OrderItemDialog data structure is correct")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: OrderItemDialog data structure test failed: {e}")
        return False

def test_purchase_order_dialog_compatibility():
    """Teste que PurchaseOrderDialog gère purchase_unit_price"""
    try:
        print("🔍 Testing PurchaseOrderDialog compatibility...")
        
        from app.ui.views.purchasing.dialogs.purchase_order_dialog import PurchaseOrderDialog
        
        # Vérifier que les méthodes critiques existent
        critical_methods = ['_update_items_table', 'add_item', 'get_order_data']
        
        for method_name in critical_methods:
            if hasattr(PurchaseOrderDialog, method_name):
                print(f"✅ {method_name} method exists")
            else:
                print(f"❌ {method_name} method missing")
                return False
        
        # Vérifier le code source pour purchase_unit_price
        import inspect
        try:
            source = inspect.getsource(PurchaseOrderDialog._update_items_table)
            if 'purchase_unit_price' in source:
                print("✅ 'purchase_unit_price' found in _update_items_table method")
            else:
                print("❌ 'purchase_unit_price' not found in _update_items_table method")
                return False
        except:
            print("⚠️  Could not inspect _update_items_table source")
        
        print("🎉 SUCCESS: PurchaseOrderDialog compatibility is correct")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: PurchaseOrderDialog compatibility test failed: {e}")
        return False

def test_inventory_table_model_headers():
    """Teste que InventoryTableModel utilise 'Prix d'achat'"""
    try:
        print("🔍 Testing InventoryTableModel headers...")
        
        from app.ui.views.inventory.inventory_table_model import InventoryTableModel
        
        # Créer une instance du modèle
        model = InventoryTableModel()
        
        # Vérifier les en-têtes
        headers = model.headers
        print(f"Inventory headers found: {headers}")
        
        # Vérifier que "Prix d'achat" est utilisé
        if "Prix d'achat" in headers:
            print("✅ 'Prix d'achat' found in inventory headers")
        else:
            print("❌ 'Prix d'achat' not found in inventory headers")
            return False
        
        print("🎉 SUCCESS: InventoryTableModel headers are correct")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: InventoryTableModel headers test failed: {e}")
        return False

def test_schemas_consistency():
    """Teste que les schémas utilisent purchase_unit_price"""
    try:
        print("🔍 Testing schemas consistency...")
        
        from app.core.schemas.purchasing import PurchaseOrderItemBase
        
        # Créer une instance de test
        item_data = {
            "product_id": 1,
            "quantity": 3,
            "purchase_unit_price": 25.50
        }
        
        item = PurchaseOrderItemBase(**item_data)
        
        # Vérifier que purchase_unit_price est accessible
        if hasattr(item, 'purchase_unit_price') and item.purchase_unit_price == 25.50:
            print("✅ purchase_unit_price accessible in schema")
        else:
            print("❌ purchase_unit_price not accessible in schema")
            return False
        
        print("🎉 SUCCESS: Schemas consistency is correct")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Schemas consistency test failed: {e}")
        return False

def test_complete_ui_chain():
    """Teste la chaîne complète UI pour les achats"""
    try:
        print("🔍 Testing complete UI chain...")
        
        # Test de la chaîne d'imports UI
        from app.ui.views.purchasing.purchasing_view import PurchasingView
        from app.ui.views.purchasing.dialogs.purchase_order_dialog import PurchaseOrderDialog
        from app.ui.views.purchasing.dialogs.order_item_dialog import OrderItemDialog
        from app.ui.views.purchasing.order_items_table_model import OrderItemsTableModel
        from app.ui.components.product_search_widget import ProductSearchWidget
        
        print("✅ All UI components imported successfully")
        
        print("🎉 SUCCESS: Complete UI chain works")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Complete UI chain test failed: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🎯 TEST DES CORRECTIONS UI POUR unit_price")
    print("=" * 55)
    print("Vérification des corrections:")
    print("• En-têtes de colonnes: 'Prix unitaire' → 'Prix d'achat'")
    print("• Structures de données: unit_price → purchase_unit_price")
    print("• Compatibilité des dialogues et modèles")
    print("=" * 55)
    
    all_tests = [
        ("OrderItemsTableModel Headers", test_order_items_table_model_headers),
        ("OrderItemDialog Data Structure", test_order_item_dialog_data_structure),
        ("PurchaseOrderDialog Compatibility", test_purchase_order_dialog_compatibility),
        ("InventoryTableModel Headers", test_inventory_table_model_headers),
        ("Schemas Consistency", test_schemas_consistency),
        ("Complete UI Chain", test_complete_ui_chain)
    ]
    
    passed = 0
    total = len(all_tests)
    
    for test_name, test_func in all_tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"   ❌ {test_name} FAILED")
    
    print(f"\n{'='*55}")
    print(f"📊 RÉSULTATS FINAUX: {passed}/{total} tests passés")
    
    if passed == total:
        print("\n🎉🎉🎉 SUCCÈS COMPLET! 🎉🎉🎉")
        print("\n✅ TOUTES LES CORRECTIONS UI SONT APPLIQUÉES:")
        print("   ✅ En-têtes de colonnes corrigés: 'Prix d'achat'")
        print("   ✅ Structures de données cohérentes: purchase_unit_price")
        print("   ✅ Dialogues compatibles avec les nouveaux schémas")
        print("   ✅ Modèles de table mis à jour")
        print("   ✅ Schémas Pydantic cohérents")
        print("   ✅ Chaîne UI complète fonctionnelle")
        print("\n🚀 L'INTERFACE UTILISATEUR EST MAINTENANT COHÉRENTE!")
        print("\n💡 INSTRUCTIONS FINALES:")
        print("   1. Lancez: python main.py")
        print("   2. Testez: Gestion des achats → Nouvelle commande")
        print("   3. Vérifiez: Les en-têtes affichent 'Prix d'achat'")
        print("   4. Testez: Ajouter un article → Aucune erreur unit_price")
        print("\n🎯 TOUTES LES CORRECTIONS UI SONT APPLIQUÉES!")
        return True
    else:
        print(f"\n❌ ÉCHEC: {total - passed} test(s) ont échoué")
        print("   Des problèmes UI persistent encore")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
