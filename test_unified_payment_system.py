#!/usr/bin/env python3
"""
Test complet du système unifié de paiements avec références et synchronisation automatique
"""

import sys
import os
import asyncio
from datetime import datetime, timezone
from decimal import Decimal

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.finance_service import FinanceService
from app.core.services.reference_service import ReferenceService, ReferenceType
from app.core.models.customer import Customer
from app.core.models.repair import RepairOrder, PaymentMethod as RepairPaymentMethod
from app.core.models.sale import Sale, PaymentMethod as SalePaymentMethod


async def test_unified_reference_system():
    """Test du système de références unifiées"""
    print("🔗 Test du système de références unifiées")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        reference_service = ReferenceService(db)
        
        # Test 1: Génération de références
        print("\n1. Test de génération de références...")
        
        repair_ref = reference_service.generate_payment_reference(
            ReferenceType.REPAIR_PAYMENT, entity_id=123, user_id=1
        )
        print(f"   Référence réparation: {repair_ref}")
        
        sale_ref = reference_service.generate_payment_reference(
            ReferenceType.SALE_PAYMENT, entity_id=456, user_id=1
        )
        print(f"   Référence vente: {sale_ref}")
        
        customer_ref = reference_service.generate_payment_reference(
            ReferenceType.CUSTOMER_TRANSACTION, entity_id=789, user_id=1
        )
        print(f"   Référence client: {customer_ref}")
        
        # Test 2: Vérification d'unicité
        print("\n2. Test de vérification d'unicité...")
        
        # Générer la même référence deux fois
        ref1 = reference_service.generate_payment_reference(
            ReferenceType.REPAIR_PAYMENT, entity_id=999, user_id=1, custom_suffix="TEST"
        )
        ref2 = reference_service.generate_payment_reference(
            ReferenceType.REPAIR_PAYMENT, entity_id=999, user_id=1, custom_suffix="TEST"
        )
        
        print(f"   Première génération: {ref1}")
        print(f"   Deuxième génération: {ref2}")
        print(f"   Références différentes: {'✅' if ref1 != ref2 else '❌'}")
        
        # Test 3: Recherche de références
        print("\n3. Test de recherche de références...")
        
        # Créer un paiement test pour la recherche
        existing_ref = "REP-20250910-120000-TEST-SEARCH"
        exists = reference_service.reference_exists(existing_ref)
        print(f"   Référence {existing_ref} existe: {'✅' if not exists else '❌'}")
        
        print("✅ Tests de références unifiées terminés")
        
    except Exception as e:
        print(f"❌ Erreur lors du test de références: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def test_payment_with_unified_references():
    """Test des paiements avec références unifiées"""
    print("\n💰 Test des paiements avec références unifiées")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        finance_service = FinanceService(db)
        
        # Trouver un client et une réparation pour les tests
        customer = db.query(Customer).first()
        repair = db.query(RepairOrder).first()
        sale = db.query(Sale).first()
        
        if not customer:
            print("❌ Aucun client trouvé pour les tests")
            return
        
        print(f"📊 Test avec client ID: {customer.id} ({customer.name})")
        
        # Test 1: Paiement de réparation avec référence automatique
        if repair:
            print(f"\n1. Test paiement réparation ID: {repair.id}")
            
            # Statut avant paiement
            print(f"   Statut avant: {repair.payment_status}")
            print(f"   Total payé avant: {repair.total_paid or 0:.2f} DA")
            
            try:
                payment = await finance_service.pay_repair(
                    repair_id=repair.id,
                    amount=50.0,
                    method=RepairPaymentMethod.cash,
                    processed_by=1,
                    reference_number=None,  # Génération automatique
                    auto_generate_reference=True
                )
                
                print(f"   ✅ Paiement créé avec référence: {payment.reference_number}")
                
                # Vérifier la synchronisation du statut
                db.refresh(repair)
                print(f"   Statut après: {repair.payment_status}")
                print(f"   Total payé après: {repair.total_paid or 0:.2f} DA")
                
                # Vérifier la traçabilité
                related_payments = finance_service.get_related_payments(payment.reference_number)
                print(f"   Paiements liés trouvés: {len(related_payments)}")
                
            except Exception as e:
                print(f"   ❌ Erreur paiement réparation: {e}")
        
        # Test 2: Paiement de vente avec référence automatique
        if sale:
            print(f"\n2. Test paiement vente ID: {sale.id}")
            
            # Statut avant paiement
            print(f"   Statut avant: {sale.payment_status}")
            print(f"   Total payé avant: {sale.total_paid or 0:.2f} DA")
            
            try:
                payment = await finance_service.pay_sale(
                    sale_id=sale.id,
                    amount=75.0,
                    method=SalePaymentMethod.cash,
                    processed_by=1,
                    reference=None,  # Génération automatique
                    auto_generate_reference=True
                )
                
                print(f"   ✅ Paiement créé avec référence: {payment.reference}")
                
                # Vérifier la synchronisation du statut
                db.refresh(sale)
                print(f"   Statut après: {sale.payment_status}")
                print(f"   Total payé après: {sale.total_paid or 0:.2f} DA")
                
            except Exception as e:
                print(f"   ❌ Erreur paiement vente: {e}")
        
        # Test 3: Transaction client avec référence automatique
        print(f"\n3. Test transaction client ID: {customer.id}")
        
        # Solde avant transaction
        print(f"   Solde avant: {customer.current_balance:.2f} DA")
        
        try:
            transaction = await finance_service.record_customer_transaction(
                customer_id=customer.id,
                amount=100.0,
                description="Test transaction avec référence unifiée",
                transaction_type="payment",
                reference_number=None,  # Génération automatique
                processed_by=1,
                auto_generate_reference=True
            )
            
            print(f"   ✅ Transaction créée avec référence: {transaction.reference_number}")
            
            # Vérifier la mise à jour du solde
            db.refresh(customer)
            print(f"   Solde après: {customer.current_balance:.2f} DA")
            
        except Exception as e:
            print(f"   ❌ Erreur transaction client: {e}")
        
        print("✅ Tests de paiements avec références unifiées terminés")
        
    except Exception as e:
        print(f"❌ Erreur lors du test de paiements: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def test_duplicate_prevention():
    """Test de prévention des doublons"""
    print("\n🚫 Test de prévention des doublons")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        finance_service = FinanceService(db)
        
        # Trouver une réparation pour le test
        repair = db.query(RepairOrder).first()
        if not repair:
            print("❌ Aucune réparation trouvée pour le test")
            return
        
        print(f"📊 Test avec réparation ID: {repair.id}")
        
        # Créer une référence unique pour le test
        test_reference = f"TEST-DUPLICATE-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        print(f"\n1. Premier paiement avec référence: {test_reference}")
        
        try:
            payment1 = await finance_service.pay_repair(
                repair_id=repair.id,
                amount=25.0,
                method=RepairPaymentMethod.cash,
                processed_by=1,
                reference_number=test_reference,
                auto_generate_reference=False  # Utiliser la référence fournie
            )
            
            print(f"   ✅ Premier paiement créé: ID {payment1.id}")
            
        except Exception as e:
            print(f"   ❌ Erreur premier paiement: {e}")
            return
        
        print(f"\n2. Tentative de doublon avec même référence: {test_reference}")
        
        try:
            payment2 = await finance_service.pay_repair(
                repair_id=repair.id,
                amount=30.0,
                method=RepairPaymentMethod.cash,
                processed_by=1,
                reference_number=test_reference,
                auto_generate_reference=False
            )
            
            if payment2.id == payment1.id:
                print("   ✅ Doublon détecté et prévenu - même paiement retourné")
            else:
                print("   ❌ Doublon non détecté - nouveau paiement créé")
            
        except Exception as e:
            print(f"   ⚠️  Exception lors du test de doublon: {e}")
        
        print("✅ Test de prévention des doublons terminé")
        
    except Exception as e:
        print(f"❌ Erreur lors du test de doublons: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def test_payment_status_sync():
    """Test de synchronisation des statuts de paiement"""
    print("\n🔄 Test de synchronisation des statuts de paiement")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        finance_service = FinanceService(db)
        
        # Trouver des entités pour les tests
        repair = db.query(RepairOrder).first()
        sale = db.query(Sale).first()
        
        if repair:
            print(f"\n1. Test synchronisation réparation ID: {repair.id}")
            
            # Forcer une incohérence (simulation)
            original_status = repair.payment_status
            print(f"   Statut actuel: {original_status}")
            
            # Synchroniser
            success = await finance_service.sync_payment_status('repair', repair.id)
            print(f"   Synchronisation: {'✅' if success else '❌'}")
            
            db.refresh(repair)
            print(f"   Statut après sync: {repair.payment_status}")
        
        if sale:
            print(f"\n2. Test synchronisation vente ID: {sale.id}")
            
            # Forcer une incohérence (simulation)
            original_status = sale.payment_status
            print(f"   Statut actuel: {original_status}")
            
            # Synchroniser
            success = await finance_service.sync_payment_status('sale', sale.id)
            print(f"   Synchronisation: {'✅' if success else '❌'}")
            
            db.refresh(sale)
            print(f"   Statut après sync: {sale.payment_status}")
        
        print("✅ Test de synchronisation des statuts terminé")
        
    except Exception as e:
        print(f"❌ Erreur lors du test de synchronisation: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def main():
    """Fonction principale de test"""
    print("🚀 Test complet du système unifié de paiements")
    print("=" * 80)
    
    # Exécuter tous les tests
    await test_unified_reference_system()
    await test_payment_with_unified_references()
    await test_duplicate_prevention()
    await test_payment_status_sync()
    
    print("\n" + "=" * 80)
    print("🎉 Tests du système unifié terminés !")
    print("\n📋 Fonctionnalités testées:")
    print("   ✅ Génération de références unifiées")
    print("   ✅ Prévention des doublons")
    print("   ✅ Synchronisation automatique des statuts")
    print("   ✅ Traçabilité des paiements")
    print("   ✅ Cohérence entre interfaces")


if __name__ == "__main__":
    asyncio.run(main())
