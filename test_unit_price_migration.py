#!/usr/bin/env python3
"""
Script de test pour vérifier la migration de unit_price vers purchase_unit_price
"""
import os
import sys
import sqlite3
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

def test_database_schema():
    """Teste que les colonnes de la base de données sont correctes"""
    db_path = os.path.join('data', 'app.db')
    
    if not os.path.exists(db_path):
        print("❌ Base de données non trouvée")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Tables à vérifier
    tables_to_check = {
        'purchase_order_items': 'purchase_unit_price',
        'supplier_quotes': 'purchase_unit_price', 
        'sale_items': 'purchase_unit_price',
        'quote_items': 'purchase_unit_price',
        'used_parts': 'purchase_unit_price'
    }
    
    all_good = True
    
    for table, expected_column in tables_to_check.items():
        try:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [row[1] for row in cursor.fetchall()]
            
            if expected_column in columns:
                print(f"✅ {table}: {expected_column} trouvée")
            else:
                print(f"❌ {table}: {expected_column} manquante")
                all_good = False
                
            # Vérifier que unit_price n'existe plus
            if 'unit_price' in columns:
                print(f"⚠️  {table}: unit_price existe encore")
                all_good = False
                
        except Exception as e:
            print(f"❌ Erreur lors de la vérification de {table}: {e}")
            all_good = False
    
    conn.close()
    return all_good

def test_model_imports():
    """Teste que les modèles peuvent être importés sans erreur"""
    try:
        from app.core.models.sale import SaleItem, QuoteItem
        from app.core.models.repair import UsedPart
        from app.core.models.purchasing import PurchaseOrderItem, SupplierQuote
        
        print("✅ Tous les modèles importés avec succès")
        
        # Vérifier que les modèles ont les bonnes colonnes
        if hasattr(SaleItem, 'purchase_unit_price'):
            print("✅ SaleItem.purchase_unit_price existe")
        else:
            print("❌ SaleItem.purchase_unit_price manquante")
            return False
            
        if hasattr(UsedPart, 'purchase_unit_price'):
            print("✅ UsedPart.purchase_unit_price existe")
        else:
            print("❌ UsedPart.purchase_unit_price manquante")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'import des modèles: {e}")
        return False

def test_schema_imports():
    """Teste que les schémas Pydantic peuvent être importés"""
    try:
        from app.core.schemas.purchasing import PurchaseOrderItemBase, SupplierQuoteBase
        from app.core.models.sale import SaleItemPydantic, QuoteItemPydantic
        
        print("✅ Tous les schémas importés avec succès")
        
        # Vérifier que les schémas ont les bons champs
        schema = PurchaseOrderItemBase(product_id=1, quantity=1, purchase_unit_price=10.0)
        print("✅ PurchaseOrderItemBase.purchase_unit_price fonctionne")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'import des schémas: {e}")
        return False

def run_migrations():
    """Exécute les migrations pour s'assurer qu'elles fonctionnent"""
    try:
        print("🔄 Exécution des migrations...")
        
        # Migration des achats
        from migrations.rename_unit_price_to_purchase_unit_price_in_purchasing import migrate as migrate_purchasing
        migrate_purchasing()
        print("✅ Migration des achats terminée")
        
        # Migration des ventes et réparations
        from migrations.rename_unit_price_to_purchase_unit_price_sales_repairs import migrate as migrate_sales
        migrate_sales()
        print("✅ Migration des ventes et réparations terminée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des migrations: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 Test de la migration unit_price -> purchase_unit_price")
    print("=" * 60)
    
    # Exécuter les migrations
    if not run_migrations():
        print("\n❌ Les migrations ont échoué")
        return False
    
    # Tester le schéma de la base de données
    if not test_database_schema():
        print("\n❌ Le schéma de la base de données n'est pas correct")
        return False
    
    # Tester les imports de modèles
    if not test_model_imports():
        print("\n❌ Les modèles ne peuvent pas être importés")
        return False
    
    # Tester les imports de schémas
    if not test_schema_imports():
        print("\n❌ Les schémas ne peuvent pas être importés")
        return False
    
    print("\n✅ Tous les tests sont passés avec succès!")
    print("🎉 La migration unit_price -> purchase_unit_price est terminée")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
