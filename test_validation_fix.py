#!/usr/bin/env python3
"""
Test spécifique pour vérifier que l'erreur de validation "unit_price is not defined" est corrigée
"""
import sys
import os

def test_item_data_consistency():
    """Teste que get_item_data() et edit_item() utilisent les mêmes clés"""
    try:
        print("Testing item data consistency...")
        
        # Simuler les données retournées par get_item_data()
        item_data = {
            "product_id": 1,
            "product": None,
            "quantity": 5,
            "purchase_unit_price": 25.50,  # Clé utilisée par get_item_data()
            "delivery_date": None,
            "specifications": {},
            "received_quantity": 0
        }
        
        # Vérifier que toutes les clés nécessaires sont présentes
        required_keys = ["product_id", "product", "quantity", "purchase_unit_price", "delivery_date"]
        
        for key in required_keys:
            if key not in item_data:
                print(f"ERROR: Missing key '{key}' in item_data")
                return False
        
        # Vérifier que la clé deprecated n'est pas présente
        if "unit_price" in item_data:
            print("WARNING: Deprecated key 'unit_price' found in item_data")
        
        print("SUCCESS: Item data structure is consistent")
        return True
        
    except Exception as e:
        print(f"ERROR: Error testing item data consistency: {e}")
        return False

def test_purchase_order_item_update():
    """Teste la mise à jour d'un PurchaseOrderItem"""
    try:
        print("Testing PurchaseOrderItem update...")
        
        from app.core.models.purchasing import PurchaseOrderItem
        
        # Créer un article de test
        item = PurchaseOrderItem(
            product_id=1,
            quantity=3,
            purchase_unit_price=20.0
        )
        
        # Simuler les données de mise à jour (comme retournées par get_item_data())
        item_data = {
            "product_id": 2,
            "product": None,
            "quantity": 5,
            "purchase_unit_price": 25.50,  # Doit utiliser cette clé
            "delivery_date": None,
            "specifications": {},
            "received_quantity": 0
        }
        
        # Simuler la mise à jour comme dans edit_item()
        item.product_id = item_data["product_id"]
        item.product = item_data["product"]
        item.quantity = item_data["quantity"]
        item.purchase_unit_price = item_data["purchase_unit_price"]  # Doit utiliser cette clé
        item.delivery_date = item_data["delivery_date"]
        
        print(f"SUCCESS: Item updated with purchase_unit_price: {item.purchase_unit_price}")
        
        if item.purchase_unit_price == 25.50:
            print("SUCCESS: Purchase unit price correctly updated")
        else:
            print("ERROR: Purchase unit price not correctly updated")
            return False
            
        return True
        
    except KeyError as e:
        print(f"ERROR: KeyError during item update: {e}")
        print("This indicates that the code is trying to access a key that doesn't exist")
        return False
    except Exception as e:
        print(f"ERROR: Error updating PurchaseOrderItem: {e}")
        return False

def test_service_calculations():
    """Teste que les services utilisent les bonnes propriétés pour les calculs"""
    try:
        print("Testing service calculations...")
        
        # Simuler un article avec purchase_unit_price
        class MockItem:
            def __init__(self):
                self.purchase_unit_price = 25.50
                self.quantity = 3
                # Pas de unit_price pour tester le fallback
        
        item = MockItem()
        
        # Tester la logique de calcul comme dans le service
        price = getattr(item, 'purchase_unit_price', getattr(item, 'unit_price', 0))
        total = price * item.quantity
        
        print(f"SUCCESS: Calculation uses purchase_unit_price: {price}")
        print(f"SUCCESS: Total calculated: {total}")
        
        if price == 25.50 and total == 76.50:
            print("SUCCESS: Service calculations work correctly")
        else:
            print("ERROR: Service calculations incorrect")
            return False
            
        return True
        
    except Exception as e:
        print(f"ERROR: Error testing service calculations: {e}")
        return False

def test_fallback_logic():
    """Teste la logique de fallback pour la compatibilité"""
    try:
        print("Testing fallback logic...")
        
        # Simuler un ancien article avec unit_price seulement
        class MockOldItem:
            def __init__(self):
                self.unit_price = 20.0
                self.quantity = 2
                # Pas de purchase_unit_price
        
        old_item = MockOldItem()
        
        # Tester la logique de fallback
        price = getattr(old_item, 'purchase_unit_price', getattr(old_item, 'unit_price', 0))
        
        print(f"SUCCESS: Fallback to unit_price: {price}")
        
        if price == 20.0:
            print("SUCCESS: Fallback logic works correctly")
        else:
            print("ERROR: Fallback logic failed")
            return False
            
        return True
        
    except Exception as e:
        print(f"ERROR: Error testing fallback logic: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("Test de correction de l'erreur de validation - unit_price is not defined")
    print("=" * 75)
    
    success = True
    
    # Test de cohérence des données
    if not test_item_data_consistency():
        success = False
    
    # Test de mise à jour d'article
    if not test_purchase_order_item_update():
        success = False
    
    # Test des calculs de service
    if not test_service_calculations():
        success = False
    
    # Test de la logique de fallback
    if not test_fallback_logic():
        success = False
    
    if success:
        print("\nSUCCESS: Tous les tests de correction sont passes!")
        print("L'erreur 'unit_price is not defined' devrait etre corrigee")
    else:
        print("\nERROR: Certains tests ont echoue")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
