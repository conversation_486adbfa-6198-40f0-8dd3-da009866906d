class TestDBManager:
    def __init__(self):
        self.test_users = {}
        self.test_roles = {}

    async def get_user(self, user_id):
        return self.test_users.get(user_id)

    async def get_user_by_email(self, email):
        return next(
            (user for user in self.test_users.values() if user.email == email),
            None
        )

    async def get_user_roles(self, user_id):
        return self.test_roles.get(user_id, [])