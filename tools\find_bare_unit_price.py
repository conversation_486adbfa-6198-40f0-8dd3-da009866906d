#!/usr/bin/env python3
import os
import ast

ROOT = '.'

def find_bare_names(root):
    results = []
    for dirpath, dirnames, filenames in os.walk(root):
        # skip virtual envs
        if '.venv' in dirpath or '__pycache__' in dirpath:
            continue
        for fn in filenames:
            if fn.endswith('.py'):
                path = os.path.join(dirpath, fn)
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        src = f.read()
                    tree = ast.parse(src, filename=path)
                    for node in ast.walk(tree):
                        if isinstance(node, ast.Name) and node.id in ('unit_price', 'init_price'):
                            results.append((path, node.lineno, node.col_offset, node.id))
                except Exception:
                    pass
    return results

if __name__ == '__main__':
    hits = find_bare_names(ROOT)
    if not hits:
        print('No bare unit_price/init_price found.')
    else:
        for path, line, col, name in hits:
            print(f'{path}:{line}:{col} -> {name}')

