from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
import logging
import os
import sys
from app.utils.toml_parser import load_from_file

# Déterminer le chemin du fichier de configuration
if getattr(sys, 'frozen', False):
    # Si l'application est compilée avec PyInstaller
    base_dir = sys._MEIPASS
    config_path = os.path.join(base_dir, 'config', 'settings.toml')
else:
    # Si l'application est exécutée normalement
    config_path = "config/settings.toml"

# Chargement de la configuration
config = load_from_file(config_path)

DATABASE_URL = config["database"]["url"]
engine = create_engine(
    DATABASE_URL,
    pool_size=config["database"].get("pool_size", 5),
    max_overflow=config["database"].get("max_overflow", 10)
)

SessionLocal = sessionmaker(bind=engine)
Base = declarative_base()
logger = logging.getLogger(__name__)

@contextmanager
def get_db():
    """Gestionnaire de contexte pour les sessions de base de données"""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        logger.error(f"Erreur de base de données: {str(e)}")
        raise
    finally:
        db.close()

