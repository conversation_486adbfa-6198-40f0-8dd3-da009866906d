from abc import ABC, abstractmethod
from typing import List, Dict, Any

class BaseExporter(ABC):
    """Classe de base pour tous les exporteurs"""
    
    @abstractmethod
    def export(self, data: List[Dict[str, Any]], output_path: str) -> None:
        """Exporte les données vers un fichier"""
        pass

    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """Retourne les formats supportés par l'exporteur"""
        pass