from typing import Callable, Dict, List
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class EventManager:
    """Gestionnaire d'événements système"""
    
    def __init__(self):
        self._listeners: Dict[str, List[Callable]] = {}
        
    def subscribe(self, event_type: str, callback: Callable) -> None:
        """Ajoute un listener pour un type d'événement"""
        if event_type not in self._listeners:
            self._listeners[event_type] = []
        self._listeners[event_type].append(callback)
        
    def unsubscribe(self, event_type: str, callback: Callable) -> None:
        """Retire un listener"""
        if event_type in self._listeners:
            self._listeners[event_type].remove(callback)
            
    def emit(self, event_type: str, **kwargs) -> None:
        """Émet un événement à tous les listeners concernés"""
        if event_type in self._listeners:
            timestamp = datetime.now()
            for callback in self._listeners[event_type]:
                try:
                    callback(timestamp=timestamp, **kwargs)
                except Exception as e:
                    logger.error(f"Erreur dans le listener: {str(e)}")