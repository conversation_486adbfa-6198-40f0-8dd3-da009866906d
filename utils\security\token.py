import base64
import secrets
from typing import Set

def generate_api_key() -> str:
    """Génère une clé API sécurisée"""
    return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8')

def generate_random_token(length: int = 32) -> str:
    """
    Génère un token aléatoire
    
    Args:
        length: Longueur du token en bytes
    """
    return secrets.token_urlsafe(length)

class TokenBlacklist:
    """Gestionnaire de liste noire des tokens"""
    _blacklist: Set[str] = set()

    @classmethod
    def add(cls, token: str) -> None:
        """Ajoute un token à la liste noire"""
        cls._blacklist.add(token)

    @classmethod
    def is_blacklisted(cls, token: str) -> bool:
        """Vérifie si un token est dans la liste noire"""
        return token in cls._blacklist

    @classmethod
    def clear(cls) -> None:
        """Vide la liste noire"""
        cls._blacklist.clear()