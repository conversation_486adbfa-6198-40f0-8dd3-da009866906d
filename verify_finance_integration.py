#!/usr/bin/env python3
"""
Script de vérification pour s'assurer que tous les appels de paiement 
passent par FinanceService et qu'il n'y a pas de création directe de paiements
"""

import os
import re
import sys
from pathlib import Path


def find_direct_payment_creation():
    """Trouve les créations directes de paiements dans le code"""
    print("🔍 Recherche de créations directes de paiements...")
    print("=" * 60)
    
    # Patterns à rechercher
    patterns = [
        r'Payment\s*\(',  # Création directe de Payment
        r'RepairPayment\s*\(',  # Création directe de RepairPayment
        r'SupplierPayment\s*\(',  # Création directe de SupplierPayment
        r'\.add\(payment\)',  # Ajout direct en base
        r'db\.add\(.*payment.*\)',  # Ajout direct en base avec payment
    ]
    
    # Fichiers à exclure (services autorisés)
    excluded_files = [
        'finance_service.py',
        'repair_payment_service.py',
        'supplier_finance_service.py',
        'purchasing_service.py',  # Utilise SupplierFinanceService
        'test_finance_integration.py',
        'verify_finance_integration.py',
        'models/',  # Les modèles peuvent définir les classes
    ]
    
    # Répertoires à scanner
    scan_dirs = ['app/', 'controllers/']
    
    issues_found = []
    
    for scan_dir in scan_dirs:
        if not os.path.exists(scan_dir):
            continue
            
        for root, dirs, files in os.walk(scan_dir):
            for file in files:
                if not file.endswith('.py'):
                    continue
                    
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path)
                
                # Vérifier si le fichier doit être exclu
                should_exclude = False
                for excluded in excluded_files:
                    if excluded in relative_path:
                        should_exclude = True
                        break
                
                if should_exclude:
                    continue
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        
                        for i, line in enumerate(lines, 1):
                            for pattern in patterns:
                                if re.search(pattern, line):
                                    # Vérifier si c'est dans un commentaire
                                    if line.strip().startswith('#'):
                                        continue

                                    # Vérifier si c'est dans une docstring
                                    if '"""' in line or "'''" in line:
                                        continue

                                    # Vérifier si c'est une définition de classe
                                    if line.strip().startswith('class ') and ('Payment' in line or 'RepairPayment' in line or 'SupplierPayment' in line):
                                        continue
                                    
                                    issues_found.append({
                                        'file': relative_path,
                                        'line': i,
                                        'content': line.strip(),
                                        'pattern': pattern
                                    })
                
                except Exception as e:
                    print(f"⚠️  Erreur lors de la lecture de {file_path}: {e}")
    
    return issues_found


def check_finance_service_usage():
    """Vérifie que FinanceService est bien utilisé dans les contrôleurs"""
    print("\n🔍 Vérification de l'utilisation de FinanceService...")
    print("=" * 60)
    
    controllers_to_check = [
        'app/controllers/payments_controller.py',
        'app/ui/views/sale/dialogs/payment_dialog.py',
        'app/ui/views/customer/dialogs/financial_dialog.py',
        'app/ui/views/customer/widgets/customer_payments_widget.py',
    ]
    
    results = []
    
    for controller_path in controllers_to_check:
        if not os.path.exists(controller_path):
            results.append({
                'file': controller_path,
                'status': 'NOT_FOUND',
                'message': 'Fichier non trouvé'
            })
            continue
        
        try:
            with open(controller_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                has_finance_import = 'FinanceService' in content
                has_finance_usage = 'finance_service' in content.lower()
                
                if has_finance_import and has_finance_usage:
                    status = 'OK'
                    message = 'FinanceService correctement importé et utilisé'
                elif has_finance_import:
                    status = 'WARNING'
                    message = 'FinanceService importé mais pas utilisé'
                else:
                    status = 'ERROR'
                    message = 'FinanceService non importé'
                
                results.append({
                    'file': controller_path,
                    'status': status,
                    'message': message
                })
        
        except Exception as e:
            results.append({
                'file': controller_path,
                'status': 'ERROR',
                'message': f'Erreur lors de la lecture: {e}'
            })
    
    return results


def check_deprecated_methods():
    """Vérifie les méthodes dépréciées qui devraient utiliser FinanceService"""
    print("\n🔍 Vérification des méthodes dépréciées...")
    print("=" * 60)
    
    deprecated_patterns = [
        r'service\.record_payment\(',  # SaleService.record_payment direct
        r'RepairService.*record_payment',  # RepairService.record_payment
        r'service\.record_transaction\(',  # CustomerService.record_transaction direct
        r'TreasuryService.*add_transaction',  # Écriture directe en trésorerie
    ]
    
    # Fichiers à vérifier (hors services)
    check_dirs = ['app/controllers/', 'app/ui/', 'app/api/']

    # Fichiers à exclure pour les méthodes dépréciées
    excluded_deprecated = [
        'supplier_payment_dialog.py',  # Utilise SupplierFinanceService
        'payments_widget.py',  # Utilise le controller qui a été modifié
        'test_customer_finance_integration.py',  # Script de test
    ]
    
    issues = []
    
    for check_dir in check_dirs:
        if not os.path.exists(check_dir):
            continue
            
        for root, dirs, files in os.walk(check_dir):
            for file in files:
                if not file.endswith('.py'):
                    continue
                    
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path)

                # Vérifier si le fichier doit être exclu pour les méthodes dépréciées
                should_exclude_deprecated = False
                for excluded in excluded_deprecated:
                    if excluded in relative_path:
                        should_exclude_deprecated = True
                        break

                if should_exclude_deprecated:
                    continue

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        
                        for i, line in enumerate(lines, 1):
                            for pattern in deprecated_patterns:
                                if re.search(pattern, line):
                                    if line.strip().startswith('#'):
                                        continue
                                    
                                    issues.append({
                                        'file': relative_path,
                                        'line': i,
                                        'content': line.strip(),
                                        'suggestion': 'Utiliser FinanceService.pay_sale() ou FinanceService.pay_repair()'
                                    })
                
                except Exception as e:
                    print(f"⚠️  Erreur lors de la lecture de {file_path}: {e}")
    
    return issues


def main():
    """Fonction principale"""
    print("🚀 Vérification de l'intégration FinanceService")
    print("=" * 60)
    
    # 1. Rechercher les créations directes de paiements
    direct_payments = find_direct_payment_creation()
    
    if direct_payments:
        print(f"\n❌ {len(direct_payments)} création(s) directe(s) de paiement trouvée(s):")
        for issue in direct_payments:
            print(f"  📁 {issue['file']}:{issue['line']}")
            print(f"     {issue['content']}")
            print(f"     Pattern: {issue['pattern']}")
            print()
    else:
        print("\n✅ Aucune création directe de paiement trouvée")
    
    # 2. Vérifier l'utilisation de FinanceService
    finance_usage = check_finance_service_usage()
    
    print("\n📊 Utilisation de FinanceService:")
    for result in finance_usage:
        status_icon = {
            'OK': '✅',
            'WARNING': '⚠️',
            'ERROR': '❌',
            'NOT_FOUND': '❓'
        }.get(result['status'], '❓')
        
        print(f"  {status_icon} {result['file']}: {result['message']}")
    
    # 3. Vérifier les méthodes dépréciées
    deprecated_usage = check_deprecated_methods()
    
    if deprecated_usage:
        print(f"\n⚠️  {len(deprecated_usage)} utilisation(s) de méthode(s) dépréciée(s):")
        for issue in deprecated_usage:
            print(f"  📁 {issue['file']}:{issue['line']}")
            print(f"     {issue['content']}")
            print(f"     💡 {issue['suggestion']}")
            print()
    else:
        print("\n✅ Aucune utilisation de méthode dépréciée trouvée")
    
    # Résumé
    print("\n" + "=" * 60)
    total_issues = len(direct_payments) + len(deprecated_usage)
    error_count = len([r for r in finance_usage if r['status'] == 'ERROR'])
    
    if total_issues == 0 and error_count == 0:
        print("🎉 Intégration FinanceService: SUCCÈS!")
        print("   Tous les paiements passent par FinanceService")
        return 0
    else:
        print(f"⚠️  Intégration FinanceService: {total_issues + error_count} problème(s) trouvé(s)")
        print("   Veuillez corriger les problèmes identifiés")
        return 1


if __name__ == "__main__":
    sys.exit(main())
