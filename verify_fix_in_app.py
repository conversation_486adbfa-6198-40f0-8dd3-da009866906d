import sys
sys.path.append('.')

from app.ui.views.user.user_table_model import UserTableModel

def verify_fix():
    """Vérifier que notre correction est bien en place"""
    
    print("=== Vérification de la correction ===")
    
    # Créer une instance du modèle
    model = UserTableModel()
    
    # Tester la méthode _format_datetime avec None
    result = model._format_datetime(None)
    
    print(f"Résultat de _format_datetime(None): '{result}'")
    
    if result == "Jamais connecté":
        print("✅ CORRECTION CONFIRMÉE: La méthode retourne 'Jamais connecté'")
    elif "Aucune date de dernière connexion trouvée" in result:
        print("❌ PROBLÈME: L'ancienne version est encore utilisée")
    else:
        print(f"⚠️  INATTENDU: Résultat '{result}'")
    
    # Vérifier le code source de la méthode
    import inspect
    source = inspect.getsource(model._format_datetime)
    print(f"\n=== Code source de _format_datetime ===")
    print(source)
    
    # Vérifier si l'ancien message est présent dans le code
    if "Aucune date de dernière connexion trouvée" in source:
        print("❌ L'ancien message est encore dans le code source")
    elif "Jamais connecté" in source:
        print("✅ Le nouveau message est dans le code source")
    else:
        print("⚠️  Aucun des messages trouvé dans le code source")

if __name__ == "__main__":
    verify_fix()
